// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserStatAddress = "x_user_stat_address"

// XUserStatAddress 渠道用户转账地址
type XUserStatAddress struct {
	ChannelID      int32     `gorm:"column:ChannelId;primaryKey;comment:渠道id" json:"ChannelId"`                           // 渠道id
	FromAddressCrc int64     `gorm:"column:FromAddressCrc;primaryKey;comment:玩家地址crc" json:"FromAddressCrc"`              // 玩家地址crc
	FromAddress    string    `gorm:"column:FromAddress;primaryKey;comment:玩家地址" json:"FromAddress"`                       // 玩家地址
	SellerID       int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	UseCount       int32     `gorm:"column:UseCount;comment:使用次数" json:"UseCount"`                                        // 使用次数
	Memo           string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XUserStatAddress's table name
func (*XUserStatAddress) TableName() string {
	return TableNameXUserStatAddress
}
