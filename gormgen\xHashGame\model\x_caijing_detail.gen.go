// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXCaijingDetail = "x_caijing_detail"

// XCaijingDetail mapped from table <x_caijing_detail>
type XCaijingDetail struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID     int32     `gorm:"column:UserId" json:"UserId"`
	SType      string    `gorm:"column:SType" json:"SType"`
	Symbol     string    `gorm:"column:Symbol" json:"Symbol"`
	Amount     float64   `gorm:"column:Amount" json:"Amount"`
	MinLiuShui float64   `gorm:"column:MinLiuShui;default:0.000000;comment:提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比" json:"MinLiuShui"` // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	CSGroup    string    `gorm:"column:CSGroup" json:"CSGroup"`
	CSID       string    `gorm:"column:CSId" json:"CSId"`
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	TopAgentID int32     `gorm:"column:TopAgentId" json:"TopAgentId"`
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`       // 备注
	Account    string    `gorm:"column:Account;comment:账号" json:"Account"` // 账号
}

// TableName XCaijingDetail's table name
func (*XCaijingDetail) TableName() string {
	return TableNameXCaijingDetail
}
