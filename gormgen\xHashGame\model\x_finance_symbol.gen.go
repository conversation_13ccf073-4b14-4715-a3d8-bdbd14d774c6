// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXFinanceSymbol = "x_finance_symbol"

// XFinanceSymbol 充提币种
type XFinanceSymbol struct {
	ID                int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID          int32     `gorm:"column:SellerId;not null;default:1;comment:运营商Id" json:"SellerId"` // 运营商Id
	PSymbol           string    `gorm:"column:PSymbol;comment:平台币" json:"PSymbol"`                        // 平台币
	Symbol            string    `gorm:"column:Symbol;comment:币种" json:"Symbol"`                           // 币种
	Country           string    `gorm:"column:Country" json:"Country"`
	FType             int32     `gorm:"column:FType;default:1;comment:类型 1法币 2加密货币" json:"FType"`                 // 类型 1法币 2加密货币
	RateSource        int32     `gorm:"column:RateSource;comment:汇率来源 1币安" json:"RateSource"`                     // 汇率来源 1币安
	RechargeRate      float64   `gorm:"column:RechargeRate;comment:自动时时充值汇率" json:"RechargeRate"`                 // 自动时时充值汇率
	RechargeRateEx    float64   `gorm:"column:RechargeRateEx;comment:手动设置充值汇率" json:"RechargeRateEx"`             // 手动设置充值汇率
	RechargeFixType   int32     `gorm:"column:RechargeFixType;comment:充值汇率偏差类型 1固定值,2百分比" json:"RechargeFixType"` // 充值汇率偏差类型 1固定值,2百分比
	RechargeFix       float64   `gorm:"column:RechargeFix;comment:充值汇率偏差" json:"RechargeFix"`                     // 充值汇率偏差
	WithwardRate      float64   `gorm:"column:WithwardRate;comment:自动时时提现汇率" json:"WithwardRate"`                 // 自动时时提现汇率
	WithwardRateEx    float64   `gorm:"column:WithwardRateEx;comment:手动设置提现汇率" json:"WithwardRateEx"`             // 手动设置提现汇率
	WithwardFixType   int32     `gorm:"column:WithwardFixType;comment:提现汇率偏差类型 1固定值,2百分比" json:"WithwardFixType"` // 提现汇率偏差类型 1固定值,2百分比
	WithwardFix       float64   `gorm:"column:WithwardFix;comment:提现汇率偏差" json:"WithwardFix"`                     // 提现汇率偏差
	State             int32     `gorm:"column:State;comment:状态,1启用,2禁用" json:"State"`                             // 状态,1启用,2禁用
	UpdateTime        time.Time `gorm:"column:UpdateTime;comment:修改时间" json:"UpdateTime"`                         // 修改时间
	UpdateAccount     string    `gorm:"column:UpdateAccount;comment:修改人" json:"UpdateAccount"`                    // 修改人
	RateUpdateTime    time.Time `gorm:"column:RateUpdateTime" json:"RateUpdateTime"`
	RateUpdateAccount string    `gorm:"column:RateUpdateAccount" json:"RateUpdateAccount"`
	AutoState         int32     `gorm:"column:AutoState;default:2;comment:是否开启自动同步 1开启,2关闭" json:"AutoState"` // 是否开启自动同步 1开启,2关闭
	Icon              string    `gorm:"column:Icon" json:"Icon"`
	BuyPrice          float64   `gorm:"column:BuyPrice;default:0.000000;comment:充值汇率" json:"BuyPrice"`          // 充值汇率
	SellPrice         float64   `gorm:"column:SellPrice;default:0.000000;comment:提款汇率" json:"SellPrice"`        // 提款汇率
	Sort              int32     `gorm:"column:Sort;comment:排序,数字越大越靠前" json:"Sort"`                             // 排序,数字越大越靠前
	RechargeMin       float64   `gorm:"column:RechargeMin;default:0.000000;comment:充值最小金额" json:"RechargeMin"`  // 充值最小金额
	RechargeMax       float64   `gorm:"column:RechargeMax;default:0.000000;comment:充值最大金额" json:"RechargeMax"`  // 充值最大金额
	WithwardMin       float64   `gorm:"column:WithwardMin;default:0.000000;comment:提现最小金额" json:"WithwardMin"`  // 提现最小金额
	WithwardMax       float64   `gorm:"column:WithwardMax;default:0.000000;comment:提现最大金额" json:"WithwardMax"`  // 提现最大金额
	ShowIndex         int32     `gorm:"column:ShowIndex;default:2;comment:是否显示在首页 1:显示 2:不显示" json:"ShowIndex"` // 是否显示在首页 1:显示 2:不显示
	NetJSON           string    `gorm:"column:NetJson;comment:网络协议" json:"NetJson"`                             // 网络协议
	ColdWallet        string    `gorm:"column:ColdWallet;comment:冷钱包" json:"ColdWallet"`                        // 冷钱包
}

// TableName XFinanceSymbol's table name
func (*XFinanceSymbol) TableName() string {
	return TableNameXFinanceSymbol
}
