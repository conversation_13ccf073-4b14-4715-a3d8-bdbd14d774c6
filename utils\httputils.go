package utils

import (
	"github.com/imroc/req"
	//"github.com/fatih/structs"
	//"io"
)

func HttpPost(url string, body interface{}, headers map[string]string, resBody interface{}) error {
	resp, err := req.Post(url, req.BodyJSON(body), req.<PERSON>er(headers))
	if err != nil {
		return err
	}
	err = resp.ToJSON(resBody)
	return err
}

func HttpGet(url string, body map[string]interface{}, headers map[string]string, resBody interface{}) error {
	resp, err := req.Get(url, req.QueryParam(body), req.<PERSON>er(headers))
	if err != nil {
		return err
	}
	err = resp.ToJSON(resBody)
	return err
}
