// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserRechargeWithardDate(db *gorm.DB, opts ...gen.DOOption) xUserRechargeWithardDate {
	_xUserRechargeWithardDate := xUserRechargeWithardDate{}

	_xUserRechargeWithardDate.xUserRechargeWithardDateDo.UseDB(db, opts...)
	_xUserRechargeWithardDate.xUserRechargeWithardDateDo.UseModel(&model.XUserRechargeWithardDate{})

	tableName := _xUserRechargeWithardDate.xUserRechargeWithardDateDo.TableName()
	_xUserRechargeWithardDate.ALL = field.NewAsterisk(tableName)
	_xUserRechargeWithardDate.RecordDate = field.NewTime(tableName, "RecordDate")
	_xUserRechargeWithardDate.UserID = field.NewInt32(tableName, "UserId")
	_xUserRechargeWithardDate.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserRechargeWithardDate.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserRechargeWithardDate.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xUserRechargeWithardDate.RechargeCount = field.NewInt32(tableName, "RechargeCount")
	_xUserRechargeWithardDate.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xUserRechargeWithardDate.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xUserRechargeWithardDate.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xUserRechargeWithardDate.WithdrawFee = field.NewFloat64(tableName, "WithdrawFee")
	_xUserRechargeWithardDate.FirstRechargeAmount = field.NewFloat64(tableName, "FirstRechargeAmount")
	_xUserRechargeWithardDate.FirstRechargeTime = field.NewTime(tableName, "FirstRechargeTime")
	_xUserRechargeWithardDate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserRechargeWithardDate.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserRechargeWithardDate.fillFieldMap()

	return _xUserRechargeWithardDate
}

type xUserRechargeWithardDate struct {
	xUserRechargeWithardDateDo xUserRechargeWithardDateDo

	ALL                 field.Asterisk
	RecordDate          field.Time
	UserID              field.Int32
	SellerID            field.Int32
	ChannelID           field.Int32
	TopAgentID          field.Int32
	RechargeCount       field.Int32   // 充值笔数
	RechargeAmount      field.Float64 // 充值金额
	WithdrawCount       field.Int32   // 体现笔数
	WithdrawAmount      field.Float64 // 提现金额
	WithdrawFee         field.Float64 // 提现手续费
	FirstRechargeAmount field.Float64
	FirstRechargeTime   field.Time
	CreateTime          field.Time // 创建时间
	UpdateTime          field.Time // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserRechargeWithardDate) Table(newTableName string) *xUserRechargeWithardDate {
	x.xUserRechargeWithardDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserRechargeWithardDate) As(alias string) *xUserRechargeWithardDate {
	x.xUserRechargeWithardDateDo.DO = *(x.xUserRechargeWithardDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserRechargeWithardDate) updateTableName(table string) *xUserRechargeWithardDate {
	x.ALL = field.NewAsterisk(table)
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.RechargeCount = field.NewInt32(table, "RechargeCount")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.WithdrawFee = field.NewFloat64(table, "WithdrawFee")
	x.FirstRechargeAmount = field.NewFloat64(table, "FirstRechargeAmount")
	x.FirstRechargeTime = field.NewTime(table, "FirstRechargeTime")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserRechargeWithardDate) WithContext(ctx context.Context) *xUserRechargeWithardDateDo {
	return x.xUserRechargeWithardDateDo.WithContext(ctx)
}

func (x xUserRechargeWithardDate) TableName() string { return x.xUserRechargeWithardDateDo.TableName() }

func (x xUserRechargeWithardDate) Alias() string { return x.xUserRechargeWithardDateDo.Alias() }

func (x xUserRechargeWithardDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserRechargeWithardDateDo.Columns(cols...)
}

func (x *xUserRechargeWithardDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserRechargeWithardDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["RechargeCount"] = x.RechargeCount
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["WithdrawFee"] = x.WithdrawFee
	x.fieldMap["FirstRechargeAmount"] = x.FirstRechargeAmount
	x.fieldMap["FirstRechargeTime"] = x.FirstRechargeTime
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserRechargeWithardDate) clone(db *gorm.DB) xUserRechargeWithardDate {
	x.xUserRechargeWithardDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserRechargeWithardDate) replaceDB(db *gorm.DB) xUserRechargeWithardDate {
	x.xUserRechargeWithardDateDo.ReplaceDB(db)
	return x
}

type xUserRechargeWithardDateDo struct{ gen.DO }

func (x xUserRechargeWithardDateDo) Debug() *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserRechargeWithardDateDo) WithContext(ctx context.Context) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserRechargeWithardDateDo) ReadDB() *xUserRechargeWithardDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserRechargeWithardDateDo) WriteDB() *xUserRechargeWithardDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserRechargeWithardDateDo) Session(config *gorm.Session) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserRechargeWithardDateDo) Clauses(conds ...clause.Expression) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserRechargeWithardDateDo) Returning(value interface{}, columns ...string) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserRechargeWithardDateDo) Not(conds ...gen.Condition) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserRechargeWithardDateDo) Or(conds ...gen.Condition) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserRechargeWithardDateDo) Select(conds ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserRechargeWithardDateDo) Where(conds ...gen.Condition) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserRechargeWithardDateDo) Order(conds ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserRechargeWithardDateDo) Distinct(cols ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserRechargeWithardDateDo) Omit(cols ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserRechargeWithardDateDo) Join(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserRechargeWithardDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserRechargeWithardDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserRechargeWithardDateDo) Group(cols ...field.Expr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserRechargeWithardDateDo) Having(conds ...gen.Condition) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserRechargeWithardDateDo) Limit(limit int) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserRechargeWithardDateDo) Offset(offset int) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserRechargeWithardDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserRechargeWithardDateDo) Unscoped() *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserRechargeWithardDateDo) Create(values ...*model.XUserRechargeWithardDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserRechargeWithardDateDo) CreateInBatches(values []*model.XUserRechargeWithardDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserRechargeWithardDateDo) Save(values ...*model.XUserRechargeWithardDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserRechargeWithardDateDo) First() (*model.XUserRechargeWithardDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithardDate), nil
	}
}

func (x xUserRechargeWithardDateDo) Take() (*model.XUserRechargeWithardDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithardDate), nil
	}
}

func (x xUserRechargeWithardDateDo) Last() (*model.XUserRechargeWithardDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithardDate), nil
	}
}

func (x xUserRechargeWithardDateDo) Find() ([]*model.XUserRechargeWithardDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserRechargeWithardDate), err
}

func (x xUserRechargeWithardDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserRechargeWithardDate, err error) {
	buf := make([]*model.XUserRechargeWithardDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserRechargeWithardDateDo) FindInBatches(result *[]*model.XUserRechargeWithardDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserRechargeWithardDateDo) Attrs(attrs ...field.AssignExpr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserRechargeWithardDateDo) Assign(attrs ...field.AssignExpr) *xUserRechargeWithardDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserRechargeWithardDateDo) Joins(fields ...field.RelationField) *xUserRechargeWithardDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserRechargeWithardDateDo) Preload(fields ...field.RelationField) *xUserRechargeWithardDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserRechargeWithardDateDo) FirstOrInit() (*model.XUserRechargeWithardDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithardDate), nil
	}
}

func (x xUserRechargeWithardDateDo) FirstOrCreate() (*model.XUserRechargeWithardDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithardDate), nil
	}
}

func (x xUserRechargeWithardDateDo) FindByPage(offset int, limit int) (result []*model.XUserRechargeWithardDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserRechargeWithardDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserRechargeWithardDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserRechargeWithardDateDo) Delete(models ...*model.XUserRechargeWithardDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserRechargeWithardDateDo) withDO(do gen.Dao) *xUserRechargeWithardDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
