package single

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// OFAAPIResponse 表示标准API响应结构
type OFAAPIResponse struct {
	Code      string      `json:"code"`                // 响应代码
	Message   string      `json:"message"`             // 响应消息
	Timestamp int64       `json:"timestamp"`           // 时间戳
	Data      interface{} `json:"data"`                // 响应数据
	Dev       interface{} `json:"dev,omitempty"`       // 开发者信息（可选）
	Signature string      `json:"signature,omitempty"` // 签名（可选）
}

// sendRequest 向OFA API发送请求
func (c *OFALiveSingleService) sendRequest(endpoint string, params interface{}) (OFAAPIResponse, error) {
	var response OFAAPIResponse

	// 将参数序列化为JSON
	jsonData, err := json.Marshal(params)
	if err != nil {
		return response, fmt.Errorf("序列化参数错误: %v", err)
	}

	// 加密消息
	encryptedMsg, err := c.DESEncrypt(string(jsonData))
	if err != nil {
		return response, fmt.Errorf("加密消息错误: %v", err)
	}

	// 获取时间戳 (毫秒)
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)

	// 创建签名
	signature := c.CreateSignature(timestamp, encryptedMsg)

	// 构建请求URL
	baseURL := c.apiUrl
	if strings.HasSuffix(baseURL, "/") {
		baseURL = baseURL[:len(baseURL)-1]
	}

	// 确保endpoint不以/开头
	if strings.HasPrefix(endpoint, "/") {
		endpoint = endpoint[1:]
	}

	// 根据新的OFA API文档，API路径格式为Player/XXX或Report/XXX
	requestURL := fmt.Sprintf("%s/%s", baseURL, endpoint)

	// 构建请求参数
	formData := url.Values{}
	formData.Set("msg", encryptedMsg)

	// 调试模式下输出请求信息
	if c.Debug {
		logs.Info("=== 发送API请求 ===")
		logs.Info("请求端点: %s", endpoint)
		logs.Info("请求JSON参数: %s", string(jsonData))
		logs.Info("加密后消息: %s", encryptedMsg)
		logs.Info("时间戳: %s", timestamp)
		logs.Info("签名: %s", signature)
		logs.Info("请求URL: %s", requestURL)
		logs.Info("请求头: APICI=%s, APISI=%s, APITS=%s", c.clientId, signature, timestamp)
		logs.Info("请求体: %s", formData.Encode())
	}

	// 创建请求
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return response, fmt.Errorf("创建请求错误: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("APICI", c.clientId)
	req.Header.Set("APISI", signature)
	req.Header.Set("APITS", timestamp)
	// Send the request
	client := &http.Client{}
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return response, fmt.Errorf("发送请求错误: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return response, fmt.Errorf("读取响应错误: %v", err)
	}

	// 获取响应头中的签名和时间戳
	respSignature := resp.Header.Get("APISI")
	respTimestamp := resp.Header.Get("APITS")

	// 调试模式下输出响应信息
	if c.Debug {
		logs.Info("=== 收到API响应 ===")
		logs.Info("响应体: %s", string(body))

		if respSignature != "" && respTimestamp != "" {
			logs.Info("响应签名: %s", respSignature)
			logs.Info("响应时间戳: %s", respTimestamp)
		}
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return response, fmt.Errorf("HTTP错误: %s, 响应体: %s", resp.Status, string(body))
	}

	// 检查响应内容是否为空
	if len(body) == 0 {
		return response, fmt.Errorf("空响应")
	}

	// 如果响应头中包含签名和时间戳，则验证签名
	if respSignature != "" && respTimestamp != "" {
		// 获取响应消息体（可能是加密的）
		respMsg := string(body)

		// 验证签名
		if !c.VerifySignature(respTimestamp, respSignature, respMsg) {
			logs.Warning("响应签名验证失败！可能存在安全风险")
			if c.Debug {
				logs.Info("响应签名验证失败详情:")
				logs.Info("  接收到的签名: %s", respSignature)
				logs.Info("  接收到的时间戳: %s", respTimestamp)
				logs.Info("  响应消息体: %s", respMsg)
			}
		} else if c.Debug {
			logs.Info("响应签名验证成功")
		}
	}

	// 尝试解析响应为JSON
	if err := json.Unmarshal(body, &response); err != nil {
		// 如果无法解析为标准响应格式，则返回原始响应内容作为错误
		return response, fmt.Errorf("解析响应错误: %v, 原始响应: %s", err, string(body))
	}

	// 检查响应代码
	if response.Code != "0" && response.Code != OFALive_Code_Success {
		//errorMsg := OFAGetStatusMessage(response.Code)
		errorMsg := ""
		return response, fmt.Errorf("API错误: %s - %s", response.Code, errorMsg)
	}

	return response, nil
}
