package single

import (
	"crypto/aes"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/goccy/go-json"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"unicode"
	"xserver/abugo"
	"xserver/model"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"
)

type FcSingleService struct {
	apiDomain             string
	agentCode             string         //商户代码
	agentKey              string         //商户key
	currency              string         // 币种
	brandName             string         // 厂商标识
	timeArea              *time.Location // 时区
	RefreshUserAmountFunc func(int) error
}

func NewFcSingleService(params map[string]string, fc func(int) error) *FcSingleService {
	return &FcSingleService{
		apiDomain:             params["api_domain"],
		agentCode:             params["agent_code"],
		agentKey:              params["agent_key"],
		currency:              params["currency"],
		brandName:             "fc",
		timeArea:              time.FixedZone("utc-4", -4*3600), // 美东时区utc-4
		RefreshUserAmountFunc: fc,
	}
}

const FcCacheTaExpireTime = 10 // 超时时间（秒）

const (
	Success_Code            = 0    // 成功
	Err_Not_Enough_Balance  = 203  // 余额不足
	Err_Third_Id_Repeat     = 205  // 交易对应单号重复
	Err_Third_Id_Nil        = 210  // 交易对应单号为空
	Err_Params_Not_Is_Json  = 217  // 传入资料不是JSON格式
	Err_Third_Id_Not_Exist  = 221  // 交易单号不存在
	Err_Game_Type_Error     = 222  // 游戏类型错误
	Err_Params_Decrypt_Fail = 303  // 资料解密失败
	Err_Game_Not_Exist      = 405  // 游戏不存在
	Err_Not_Allowed_Ip      = 410  // 不允许的ip
	Err_Account_Not_Exist   = 500  // 账号不存在
	Err_Third_Id_Add_Fail   = 702  // 交易单写入失败
	Err_999                 = 999  // 无法预期的错误
	Err_Params_Fail         = 1099 // 参数错误
)

var fcUTC8 = time.FixedZone("utc+8", 8*3600)

type ResponseSuccess struct {
	Result     int     `json:"Result"`
	MainPoints float64 `json:"MainPoints"`
}

type ResponseError struct {
	Result    int    `json:"Result"`
	ErrorText string `json:"ErrorText"`
}

func GetKeyResult(paramSource string, l *FcSingleService) []byte {
	fcTestUrl := l.apiDomain + "/Key"
	method := "POST"

	encodedString := url.QueryEscape(paramSource)
	paramTemp := "Params=" + encodedString + "&AgentKey=" + l.agentKey
	logs.Info("param encoded转换后字符串：", paramTemp)
	// "AgentKey=4nxDknkhGrjXUXdU&Params=%7B%7D"
	payload := strings.NewReader(paramTemp)

	client := &http.Client{}
	req, err := http.NewRequest(method, fcTestUrl, payload)

	if err != nil {
		fmt.Println(err)
		return nil
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	fmt.Println("key返回结果：", string(body))
	return body
}

func GetHttpKeyResult(paramSource string, l *FcSingleService) []byte {
	fcTestUrl := l.apiDomain + "/Key"
	method := "POST"

	encodedString := url.QueryEscape(paramSource)
	paramTemp := "Params=" + encodedString + "&AgentKey=" + l.agentKey
	logs.Info("paramTemp:", paramTemp)
	// 替换
	//paramTemp2 := strings.ReplaceAll(paramTemp, "https", "https%3A%2F%")
	//logs.Info("param encoded转换后字符串：", paramTemp2)
	// "AgentKey=4nxDknkhGrjXUXdU&Params=%7B%7D"
	payload := strings.NewReader(paramTemp)

	client := &http.Client{}
	req, err := http.NewRequest(method, fcTestUrl, payload)

	if err != nil {
		fmt.Println(err)
		return nil
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	fmt.Println("key返回结果：", string(body))
	return body
}

func GameList(param string) []byte {

	url := "https://api.fcg666.net/GetGameIconList"
	method := "POST"

	//payload := strings.NewReader(param)
	payload := strings.NewReader("AgentCode=66888&Currency=HKD&Params=r%2FFSv49sX%2B1EGk3KTWXghg%3D%3D&Sign=99914b932bd37a50b983c5e7c90ae93b")

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return nil
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	//fmt.Println(string(body))
	return body
}

// 初始化游戏列表
func (l *FcSingleService) InitFcGameList(ctx *abugo.AbuHttpContent) {
	logs.Info("开始初始化fc游戏列表")
	type RequestData struct {
		//AgentCode string `json:"AgentCode" validate:"required"` //FC代理服务代码
		//AgentKey  string `json:"AgentKey" validate:"required"`  //FC代理服务key
		//Currency  string `json:"Currency" validate:"required"`  // FC支持的币种
		Params string `json:"Params" validate:"required"` // 加密转换前的原始参数
	}

	data := GetKeyResult("{}", l)
	var keyResultMap map[string]string
	keyErr := json.Unmarshal(data, &keyResultMap)
	if keyErr != nil {
		fmt.Println("key结果转换map出错:", keyErr)
		return
	}

	paramsStr := keyResultMap["Params"]
	singStr := keyResultMap["Sign"]
	gamListParam := "AgentCode=" + l.agentCode + "&Currency=" + l.currency + "&Params=" + paramsStr + "&Sign=" + singStr
	logs.Info("gameList参数：", gamListParam)
	gameData := GameList(gamListParam)
	if gameData != nil {
		// 将byte类型的游戏列表转换成map，便于操作
		var result map[string]any
		err := json.Unmarshal(gameData, &result)
		if err != nil {
			fmt.Println("游戏列表转换map出错:", err)
			return
		}
		gameList := result["GetGameIconList"]
		var count int64
		addErr := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			for _, mapv := range gameList.(map[string]any) {
				// 获取捕鱼、街机等层级
				for gameId, value := range mapv.(map[string]interface{}) {
					// 动态处理不同类型的值
					var cnName string
					var enName string
					for key, val := range value.(map[string]interface{}) {
						switch key {
						case "gameNameOfChinese":
							cnName = val.(string)
						case "gameNameOfEnglish":
							enName = val.(string)
						default:

						}
					}
					// 检查游戏是否存在
					betTran := thirdGameModel.GameList{}
					err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).First(&betTran).Error
					if err != daogorm.ErrRecordNotFound {
						continue
					}
					//写入数据库中
					gameInfo := xgo.H{
						"Brand":     l.brandName,
						"GameId":    gameId,
						"Name":      cnName,
						"EName":     enName,
						"GameType":  1, // 1电子 2棋牌 3趣味 4彩票 5真人 6体育
						"HubType":   0, // 0非聚合类型 1hub88类型
						"State":     2, // 状态 1启用,2禁用;默认禁用，测试完成后才放开
						"OpenState": 2, // 状态 1启用,2禁用，可在管理后台开启
					}
					logs.Info("key:", gameId, " gameInfo:", gameInfo)
					row, inErr := server.Db().Table("x_game_list").Insert(gameInfo)
					if inErr == nil {
						count += row
					}
				}
			}
			return nil
		})
		if addErr != nil {
			logs.Info("fc新增游戏列表失败：", addErr)
		} else {
			logs.Info("fc新增游戏列表成功，新增行数：", count)
		}
	}
	return
}

const Language = "1,英文; 2,简体中文 (Lucky 9 不支援);3,越南文 (Lucky 9 不支援);4,泰文 (Super Color Game & Lucky 9 不支援);5,印尼文 (Super Color Game & Lucky 9 不支援);6,缅甸文 (Super Color Game & Lucky 9 不支援);7,日文 (Super Color Game & Lucky 9 不支援);8,韩文 (Super Color Game & Lucky 9 不支援);9,葡萄牙文 (Super Color Game & Lucky 9 不支援);10,西班牙文 (Super Color Game & Lucky 9 不支援)"

var FcLanguageList = []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"}

func (l *FcSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MemberAccount string `json:"MemberAccount" validate:"required"` //玩家账号
		GameID        string `json:"GameId" validate:"required"`        // 游戏ID
		LanguageID    string `json:"LanguageID" validate:"required"`    // 语言
		HomeUrl       string `json:"HomeUrl"`                           // 返回商户地址
	}
	responseError := ResponseError{}
	// 登陆参数校验
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("fc-Login 登录游戏 请求参数错误req=", reqdata, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		responseError.Result = 1099
		responseError.ErrorText = "请求参数转换错误：" + err.Error()
		ctx.RespJson(responseError)
		return
	}

	// 语言环境校验(如果不在三方游戏提供的语言列表时，默认为中文)
	loginLang := "2"
	if reqdata.LanguageID != "" {
		for _, v := range FcLanguageList {
			if v == reqdata.LanguageID {
				loginLang = reqdata.LanguageID
				break
			}
		}
	}
	// map在转字符串时会乱序，使用字符拼接
	paramInput := "{\"MemberAccount\":\"" + reqdata.MemberAccount + "\",\"GameID\":" + reqdata.GameID + ",\"LanguageID\":" + loginLang + ",\"HomeUrl\":\"" + reqdata.HomeUrl + "\",\"JackpotStatus\":true,\"LoginGameHall\":true,\"GameHallGameType\":[1,2,7,8],\"CloseFeatureBuy\":true}"
	//paramString := fmt.Sprintf("调用key传入的参数：%v", paramData)
	//jsonString, err := json.Marshal(paramInput)
	logs.Info("fc-Login 调用key传入的参数：", paramInput)

	// 调用key接口获取转换后的param和sign
	//data := GetKeyResult(paramInput)
	data := GetHttpKeyResult(paramInput, l)
	var keyResultMap map[string]string
	keyErr := json.Unmarshal(data, &keyResultMap)
	if keyErr != nil {
		fmt.Println("fc-Login key结果转换map出错:", keyErr)
		responseError.Result = 1099
		responseError.ErrorText = "登陆参数key接口转换错误：" + err.Error()
		ctx.RespJson(responseError)
		return
	}

	// key接口ECB模式AES加密结果
	paramsStr := keyResultMap["Params"]
	//paramsStr := "KS8zP2up/7C+kjO0w5s157mrcWMne0sIdyECK/bsDpxVW0sXKSM/gVeJt/tivkleAKAw5g51dEL8AMX2jGMphlQs27rDFlNPbNhkthrDzgAKD16EE53WXv05z1efRGkQRVCXygfkymLaAvXk9+OGmHmrB6wdoB96QBbkNWnrqk2PIufN/q0MhXYjMk50PtQIPVstaEfq/nh/Y90ALSHOdm9WKEph/Md/gWs2KkV79pfQtL8Xoi9Yr7SyxP3bxw5+"
	paramsStr = url.QueryEscape(paramsStr)
	// Key接口MD5加密结果
	singStr := keyResultMap["Sign"]

	url := l.apiDomain + "/Login"
	method := "POST"

	paramLoadStr := "AgentCode=" + l.agentCode + "&Currency=" + l.currency + "&Params=" + paramsStr + "&Sign=" + singStr
	payload := strings.NewReader(paramLoadStr)
	logs.Info("登陆参数：", paramLoadStr)
	logs.Info("payload参数：", payload)
	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println("fc-Login 登陆请求创建失败：", err)
		responseError.Result = 1099
		responseError.ErrorText = "登陆请求创建失败：" + err.Error()
		ctx.RespJson(responseError)
		return
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println("fc-Login 登陆失败：", err)
		responseError.Result = 1099
		responseError.ErrorText = "登陆失败：" + err.Error()
		ctx.RespJson(responseError)
		return
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		responseError.Result = 1099
		responseError.ErrorText = "fc-Login body读取失败：" + err.Error()
		ctx.RespJson(responseError)
		return
	}
	fmt.Println(string(body))
	ctx.RespOK(string(body))
}

func (l *FcSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AgentCode string `json:"AgentCode" validate:"required"`
		Currency  string `json:"Currency" validate:"required"`
		Params    string `json:"Params" validate:"required"`
		Sign      string `json:"Sign" validate:"required"`
	}
	// 参数接收
	type Params struct {
		MemberAccount string `json:"MemberAccount" validate:"required"` // 玩家账号
		Currency      string `json:"Currency" validate:"required"`      // 币种（币别）
		GameID        string `json:"GameID" validate:"required"`        // 游戏id
		Ts            int64  `json:"Ts" validate:"required"`            // 时间戳（毫秒）
	}
	responseError := ResponseError{}
	responseSuccess := ResponseSuccess{}
	ginReq := RequestData{}
	reqData := Params{}
	errCode := 221

	// x-www-form-urlencoded参数类型时，必须使用以下解析，确保参数正常
	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-GetBalance 商户代码AgentCode错误, code:", ginReq.AgentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误：code:" + ginReq.AgentCode
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-GetBalance 获取用户余额 币别校验错误 err=")
		ctx.RespErrString(true, &errCode, "币别错误")
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + reqData.Currency
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-GetBalance 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &reqData)
	if decErr != nil {
		logs.Info("fc-GetBalance 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", reqData)
		return
	}

	// 获取用户余额
	user := thirdGameModel.UserBalance{}

	err := server.Db().GormDao().Table("x_user").Select("Amount").Where("Account = ?", reqData.MemberAccount).First(&user).Error
	if err != nil {
		logs.Info("fc-GetBalance 获取用户余额失败 用户账号=", reqData.MemberAccount, " err=", err.Error())
		responseError.Result = Err_Not_Enough_Balance
		responseError.ErrorText = "查询用户余额信息出错：" + err.Error()
		ctx.RespJson(responseError)
		return
	}
	logs.Info(user)
	responseSuccess.Result = 0
	responseSuccess.MainPoints = user.Amount
	ctx.RespJson(responseSuccess)
}

// 下注信息及游戏结果
func (l *FcSingleService) BetNInfo(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RecordID      string  `json:"RecordID" validate:"required"`      // 游戏记录编号(唯一码)，长度 24 码
		BankID        string  `json:"BankID" validate:"required"`        // 交易单号(唯一码)，长度24码
		MemberAccount string  `json:"MemberAccount" validate:"required"` // 玩家账号
		Currency      string  `json:"Currency" validate:"required"`      // 币别
		GameID        int     `json:"GameID" validate:"required"`        // 游戏编号
		GameType      int     `json:"GameType" validate:"required"`      // 游戏类型(游戏类型)
		IsBuyFeature  bool    `json:"isBuyFeature"`                      // 老虎机游戏是否有使用购买免费游戏功能（bool字段不设置必要值，如果设置则只能是true）
		Bet           float64 `json:"Bet" validate:"required"`           // 押注金额
		Win           float64 `json:"Win"`                               // 游戏赢分，可能是0值，当注解是彩金注单时，该值为0
		JPBet         float64 `json:"JPBet"`                             // 彩金抽水(支持到小数第六位)
		JPPrize       float64 `json:"JPPrize"`                           // 彩金赢分
		NetWin        float64 `json:"NetWin" validate:"required"`        // 总输赢
		RequireAmt    float64 `json:"RequireAmt" validate:"required"`    // 实际押注金额 (仅推币机与捕鱼机)
		GameDate      string  `json:"GameDate" validate:"required"`      // 游戏时间(年-月-日 时:分:秒)
		CreateDate    string  `json:"CreateDate" validate:"required"`    // 交易建立时间(年-月-日 时:分:秒)
		Ts            int64   `json:"Ts" validate:"required"`            // 发送请求当下的时间戳（毫秒）
	}

	// 重复请求校验结构
	type CacheRequest struct {
		Req RequestData `json:"req"`
		//Res ResponseData `json:"res"`
	}
	responseError := ResponseError{}
	responseSuccess := ResponseSuccess{}

	requestData := RequestData{}
	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-BetNInfo 商户代码AgentCode错误, code:", agentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误：code:" + agentCode
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-BetNInfo 获取用户余额 币别校验错误 err=")
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-BetNInfo 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Info("fc-BetNInfo 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		return
	}
	logs.Info("fc-BetNInfo 解析后参数：", requestData)
	body, bodyErr := json.Marshal(requestData)
	if bodyErr != nil {
		logs.Error("fc-BetNInfo 转换参数json失败")
		responseError.Result = Err_Params_Not_Is_Json
		responseError.ErrorText = "参数不是json字符串，转换参数json失败"
		ctx.RespJson(responseError)
		return
	}
	bodyStr := string(body)

	// 重复请求校验
	// bet transaction缓存
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:fc:betninfo:ta:%v:%v", server.Project(), server.Module(), requestData.MemberAccount, requestData.BankID)
	logs.Info("fc-BetNInfo 重复请求缓存key：", cacheKeyTa)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.BankID == requestData.BankID && cacheRes.Req.RecordID == requestData.RecordID && cacheRes.Req.MemberAccount == requestData.MemberAccount {
				responseError.Result = Err_Third_Id_Repeat
				responseError.ErrorText = "重复的单号，重复下注"
				logs.Error("fc-BetNInfo 重复请求下注:  bet ta = ", requestData.BankID, responseError)
				ctx.RespJson(responseError)
				return
			}
			logs.Error("fc-BetNInfo 下注事务相同 内容不容:  bet ta = ", requestData.BankID, requestData, cacheRes)
		} else {
			logs.Error("fc-BetNInfo 重复下注事务:  bet ta = ", requestData.BankID, "反序列化返回结果错误", e)
		}

		responseError.Result = Err_Third_Id_Repeat
		responseError.ErrorText = "重复请求下注"
		ctx.RespJson(responseError)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: requestData}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, FcCacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("fc-BetNInfo 下注请求 设置重复请求缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()

	logs.Info("fc-BetNInfo 重复请求校验结束")

	// 前置用户、余额、游戏等校验
	user := thirdGameModel.UserBalance{}
	err := server.Db().GormDao().Table("x_user").Select("UserId, SellerId, ChannelId, Amount").Where("Account = ?", requestData.MemberAccount).First(&user).Error
	if err != nil {
		logs.Info("fc-BetNInfo  获取用户余额失败 用户账号=", requestData.MemberAccount, " err=", err.Error())
		responseError.Result = Err_Not_Enough_Balance
		responseError.ErrorText = "用户余额不足，或查询用户余额信息出错：" + err.Error()
		ctx.RespJson(responseError)
		return
	}
	amount := user.Amount

	// 优先校验捕鱼,推币游戏RequireAmt下注额与余额
	tempGameType := []int{1, 2}
	if containsBoth(tempGameType, requestData.GameType) {
		// RequireAmt校验
		if user.Amount < requestData.RequireAmt {
			// 返回余额不足
			logs.Info("fc-BetNInfo  用户余额不足，下注失败, ", requestData.MemberAccount, "\n余额：", amount, " 下注额：", requestData.Bet)
			responseError.Result = Err_Not_Enough_Balance
			responseError.ErrorText = "用户余额不足：" + fmt.Sprintf("%f", user.Amount)
			ctx.RespJson(responseError)
			return
		}
	} else if amount <= 0 || amount < requestData.Bet {
		// 除捕鱼和推币外，其他游戏校验，用户余额为0或者小于投注金额
		logs.Info("fc-BetNInfo  用户余额不足，下注失败, ", requestData.MemberAccount, "\n余额：", amount, " 下注额：", requestData.Bet)
		responseError.Result = Err_Not_Enough_Balance
		responseError.ErrorText = "用户余额不足，下注失败：" + fmt.Sprintf("%f", user.Amount)
		ctx.RespJson(responseError)
		return
	}

	logs.Info("fc-BetNInfo 用户余额校验结束")

	// 使用gameId查询game_list获取游戏对应的gameType
	//根据游戏获取分类
	gameId := fmt.Sprintf("%d", requestData.GameID)
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("fc-BetNInfo  获取游戏名称失败 thirdId=", requestData.BankID, " reqdata.Game=", gameList.GameId, " err=", err.Error())
		responseError.Result = Err_Game_Not_Exist
		responseError.ErrorText = "游戏不存在或未添加"
		ctx.RespJson(responseError)
		return
	}

	logs.Info("fc-BetNInfo 游戏列表game_list校验结束")

	// 校验三方游戏订单表判断
	table := "x_third_dianzhi"
	tablepre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		table = "x_third_dianzhi"
		tablepre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		table = "x_third_qipai"
		tablepre = "x_third_qipai_pre_order"
	} else {
		logs.Error("fc-BetNInfo 游戏类型配置错误 thirdId=", requestData.BankID, " gameList=", gameList)
		responseError.Result = Err_Game_Type_Error
		responseError.ErrorText = "游戏类型配置错误"
		ctx.RespJson(responseError)
		return
	}
	logs.Info("fc-BetNInfo 开始开启事务")

	// 开启事务，操作用户金额与账变
	errTran := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 分两种情况：
		// 1，普通注单：使用总输赢NetWin（净输赢：下注额-(中奖金额)）进行用户余额操作. 用户变动后余额=用户余额+NetWin
		// 2，彩金注单：只有彩金没有下注额的注单，计算方式：用户变动后余额=用户余额+JPPrize彩金赢分，不需要对彩金抽水进行处理
		var tempAmount = user.Amount
		var tempType string
		var tempWin float64
		if requestData.JPPrize > 0 {
			tempAmount = user.Amount + requestData.JPPrize
			tempType = " 注单类型：彩金赢分"
			tempWin = requestData.JPPrize
		} else {
			// NetWin 用户总赢时为正，总输时为负值
			tempAmount = user.Amount + requestData.NetWin
			tempType = " 注单类型：游戏下注"
			tempWin = requestData.NetWin
		}

		// 订单校验，暂时使用三方传入的BankID作为三方订单号
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablepre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", requestData.BankID, l.brandName).First(&order).Error
		if e == nil {
			logs.Error("fc-BetNInfo 注单已存在 thirdId=", requestData.BankID, " order=", order)
			responseError.Result = Err_Third_Id_Repeat
			responseError.ErrorText = "交易单号已存在:" + requestData.BankID
			ctx.RespJson(responseError)
			return e
		}
		//if e != daogorm.ErrRecordNotFound {
		//	logs.Error("fc-BetNInfo  查询注单失败 thirdId=", requestData.BankID, " err=", e.Error())
		//	responseError.Result = Err_Third_Id_Not_Exist
		//	responseError.ErrorText = "交易单号不存在，查询注单失败"
		//	ctx.RespJson(responseError)
		//	return e
		//}

		// 修改用户金额
		resultTmp := tx.Table("x_user").Where("Account = ?", requestData.MemberAccount).Update("Amount", tempAmount)
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && requestData.NetWin != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("fc-BetNInfo 用户总输赢金额变更失败 gameId=", requestData.GameID,
				" Account=", requestData.MemberAccount, " NetWin(总输赢)=", requestData.NetWin, " err=", e.Error())
			responseError.Result = Err_999
			responseError.ErrorText = "扣款失败"
			ctx.RespJson(responseError)
			return e
		}

		logs.Info("fc-BetNInfo 事务-变更用户金额结束")

		// 创建下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       user.UserId,
			BeforeAmount: user.Amount,
			Amount:       tempWin, //金额变动值：可能是注单输赢，也可能是彩金赢分
			AfterAmount:  tempAmount,
			Reason:       utils.BalanceCReasonFCWin, // 发财下注与派奖是同一请求，因此下注即结算
			Memo:         l.brandName + " betNInfo,GameId:" + fmt.Sprintf("%d", requestData.GameID) + ", thirdId:" + requestData.BankID + tempType,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   requestData.CreateDate, //该时间使用的是三方游戏的时间
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("fc-BetNInfo  ", tempType, " 账变记录交易单写入失败 gameId=", requestData.GameID,
				" amountLogBet=", amountLogBet, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "交易单写入失败,创建下注账变记录失败"
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-BetNInfo 事务-创建账变记录结束")
		// 创建注单记录（老虎机是一局一局下注，捕鱼与推币则是多局合在一起推送注单），(当前FC游戏是否都是电子游戏？)
		order = thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			BetChannelId: user.ChannelId, // 下注渠道id，待确认
			UserId:       user.UserId,
			Brand:        l.brandName,
			ThirdId:      requestData.BankID,
			GameId:       fmt.Sprintf("%d", requestData.GameID),
			GameName:     gameList.Name, //使用游戏中文名
			BetAmount:    requestData.Bet,
			WinAmount:    tempWin, // 输赢金额：如果是下注，则展示的是下注金额；如果是彩金，则记录的是彩金输赢金额
			ValidBet:     requestData.RequireAmt,
			ThirdTime:    requestData.CreateDate,
			Currency:     l.currency,
			RawData:      bodyStr,
			State:        1,
			Fee:          0,
			DataState:    1, // 开奖状态. (-2:, -1:, 1:已开奖, 2:, 3:)
			CreateTime:   requestData.CreateDate,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("fc-BetNInfo 创建预设注单失败 thirdId=", requestData.BankID, " orderPre=", order, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "交易单写入失败,创建注单失败"
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-BetNInfo 事务-创建预设注单记录结束")

		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("fc-BetNInfo 创建正式注单失败 thirdId=", requestData.BankID, " order=", order, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "交易单写入失败,创建正式注单失败"
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-BetNInfo 事务-创建正式注单记录结束")
		responseSuccess.Result = Success_Code
		responseSuccess.MainPoints = tempAmount
		ctx.RespJson(responseSuccess)
		return nil
	})
	if errTran != nil {
		responseError.Result = Err_999
		responseError.ErrorText = "fc-BetNInfo 注单事务，用户金额变更、账变或注单记录失败"
		ctx.RespJson(responseError)
		return
	}
}

// 取消下注与结果
func (l *FcSingleService) CancelBetNInfo(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		BankID        string `json:"BankID" validate:"required"`        // 交易单号(唯一值)
		Currency      string `json:"currency" validate:"required"`      // 币别
		MemberAccount string `json:"MemberAccount" validate:"required"` // 玩家账号
		GameId        int64  `json:"gameId" validate:"required"`        // 游戏id
		TS            int64  `json:"ts" validate:"required"`            // 发送请求当下的时间戳（毫秒）
	}
	// 重复请求校验结构
	type CacheRequest struct {
		Req RequestData `json:"req"`
		//Res ResponseData `json:"res"`
	}
	responseError := ResponseError{}
	responseSuccess := ResponseSuccess{}
	requestData := RequestData{}
	// 重复请求校验
	// bet transaction缓存
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:fc:CancelBetNInfo:ta:%v:%v", server.Project(), server.Module(), requestData.MemberAccount, requestData.BankID)
	logs.Info("fc-CancelBetNInfo 重复请求缓存key：", cacheKeyTa)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.BankID == requestData.BankID && cacheRes.Req.MemberAccount == requestData.MemberAccount {
				responseError.Result = Err_Third_Id_Repeat
				responseError.ErrorText = "重复的单号，重复取消下注"
				logs.Error("fc-CancelBetNInfo 重复取消下注:  bet ta = ", requestData.BankID, responseError)
				ctx.RespJson(responseError)
				return
			}
			logs.Error("fc-CancelBetNInfo 取消下注事务相同 内容不同:  bet ta = ", requestData.BankID, requestData, cacheRes)
		} else {
			logs.Error("fc-CancelBetNInfo 重复取消下注:  bet ta = ", requestData.BankID, "反序列化返回结果错误", e)
		}

		responseError.Result = Err_Third_Id_Repeat
		responseError.ErrorText = "重复取消下注"
		ctx.RespJson(responseError)
		return
	}

	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-CancelBetNInfo 商户代码AgentCode错误, code:", agentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误：code:" + agentCode
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-CancelBetNInfo 获取用户余额 币别校验错误 err=")
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-CancelBetNInfo 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Info("fc-CancelBetNInfo 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-CancelBetNInfo 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	logs.Info("fc-CancelBetNInfo 解析后参数：", requestData)
	//根据游戏获取分类
	gameId := fmt.Sprintf("%d", requestData.GameId)
	gameList := thirdGameModel.GameList{}
	err := server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("fc-CancelBetNInfo 投注中奖 获取游戏名称失败 thirdId=", requestData.BankID, " reqdata.Game=", gameList.GameId, " err=", err.Error())
		responseError.Result = Err_Game_Not_Exist
		responseError.ErrorText = "游戏未添加"
		ctx.RespJson(responseError)
		return
	}

	logs.Info("fc-CancelBetNInfo 游戏列表game_list校验结束，bankID:", requestData.BankID)

	// 校验三方游戏订单表判断
	table := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		table = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		table = "x_third_qipai_pre_order"
	} else {
		logs.Error("fc-CancelBetNInfo 游戏类型配置错误 thirdId=", requestData.BankID, " gameId=", requestData.GameId)
		responseError.Result = Err_Game_Type_Error
		responseError.ErrorText = "游戏类型配置错误"
		ctx.RespJson(responseError)
		return
	}

	// 交易单号与交易状态校验
	order := model.ThirdDianZi{}
	err = server.Db().GormDao().Table(table).Select("DataState").Where("ThirdId = ? and Brand = ?", requestData.BankID, l.brandName).First(&order).Error
	if err != nil {
		logs.Error("fc-CancelBetNInfo 获取注单记录失败 thirdId=", requestData.BankID, " reqdata.Game=", gameList.GameId, " err=", err.Error())
		responseError.Result = Err_Third_Id_Not_Exist
		responseError.ErrorText = "交易单号不存在"
		ctx.RespJson(responseError)
		return
	}

	if order.DataState == -1 {
		// 只有在下注状态的单子才能撤单
		// 修改注单状态、退回用户金额、增加账变记录
		logs.Info("fc-CancelBetNInfo 取消注单事务开始")
		server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			resultTmp := tx.Table(table).Where("ThirdId=? and Brand=?", requestData.BankID, l.brandName).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": time.Now().In(fcUTC8).Format("2006-01-02 15:04:05"),
			})
			uErr := resultTmp.Error
			if uErr == nil && resultTmp.RowsAffected <= 0 {
				uErr = errors.New("fc-settle更新条数0")
			}
			if uErr != nil {
				logs.Error("fc-CancelBetNInfo 撤单事务更新注单状态失败 thirdId=", requestData.BankID, " orderPre=", order, " error=", uErr.Error())
				responseError.Result = 999
				responseError.ErrorText = "取消下注更新注单失败"
				ctx.RespJson(responseError)
				return uErr
			}
			logs.Info("fc-CancelBetNInfo 撤单事务修改订单状态结束")
			// 前置用户、余额、游戏等校验
			user := thirdGameModel.UserBalance{}
			userSelErr := server.Db().GormDao().Table("x_user").Select("UserId, SellerId, ChannelId, Amount").Where("Account = ?", requestData.MemberAccount).First(&user).Error
			if userSelErr != nil {
				logs.Info("fc-CancelBetNInfo  获取用户余额失败 用户账号=", requestData.MemberAccount, " err=", err.Error())
				responseError.Result = Err_Not_Enough_Balance
				responseError.ErrorText = "用户余额不足，或查询用户余额信息出错：" + err.Error()
				ctx.RespJson(responseError)
				return userSelErr
			}
			beforeAmount := user.Amount
			orderAmount, _ := order.BetAmount.Float64()
			endAmount := beforeAmount + orderAmount
			userResult := tx.Exec("update x_user set Amount = Amount + ? where UserId = ?", orderAmount, order.UserId)
			userErr := userResult.Error
			if userErr == nil && userResult.RowsAffected <= 0 {
				userErr = errors.New("fc-CancelBetNInfo 更新条数0")
			}
			if userErr != nil {
				logs.Error("fc-CancelBetNInfo 撤单事务更新用户金额失败 thirdId=", requestData.BankID, " orderPre=", order, " error=", uErr.Error())
				responseError.Result = 999
				responseError.ErrorText = "取消注单更新用户金额失败"
				ctx.RespJson(responseError)
				return userErr
			}
			logs.Info("fc-CancelBetNInfo 撤单事务回退用户下注额结束")
			amountLogBet := thirdGameModel.AmountChangeLog{
				UserId:       order.UserId,
				BeforeAmount: beforeAmount,
				Amount:       orderAmount, //金额变动值：即下注额
				AfterAmount:  endAmount,
				Reason:       utils.BalanceCReasonFCCancel,
				Memo:         l.brandName + " cancelBetNInfo thirdId:" + requestData.BankID + " GameId:" + fmt.Sprintf("%d", requestData.GameId) + " cancelBet:" + fmt.Sprintf("%d", order.BetAmount),
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				CreateTime:   time.Now().In(fcUTC8).Format("2006-01-02 15:04:05"), //该时间使用的是三方游戏的时间
			}
			cancelError := tx.Table("x_amount_change_log").Create(&amountLogBet).Error
			if cancelError != nil {
				logs.Error("fc-CancelBetNInfo  撤单账变记录失败 gameId=", requestData.GameId,
					" amountLogBet=", amountLogBet, " error=", cancelError.Error())
				responseError.Result = 999
				responseError.ErrorText = "创建下注账变记录失败"
				ctx.RespJson(responseError)
				return cancelError
			}
			responseSuccess.Result = 0
			responseSuccess.MainPoints = endAmount
			ctx.RespJson(responseSuccess)
			logs.Info("fc-Bet 事务-创建账变记录结束")
			return nil
		})
	} else {
		logs.Info("fc-CancelBetNInfo 注单记录已结算或已撤销，无法执行撤销操作 thirdId=", requestData.BankID, " reqdata.Game=", gameList.GameId, " dataState:", order.DataState)
		responseError.Result = Err_999
		responseError.ErrorText = "该注单已结算或已撤销，无法撤销注单"
		ctx.RespJson(responseError)
		return
	}
	logs.Info("fc-CancelBetNInfo 结束")
}

func (l *FcSingleService) Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RecordID      string  `json:"recordId" validate:"required"`
		BetID         string  `json:"betId" validate:"required"`
		MemberAccount string  `json:"MemberAccount" validate:"required"`
		Currency      string  `json:"currency" validate:"required"`
		GameID        int64   `json:"gameId" validate:"required"`
		GameType      int     `json:"gameType" validate:"required"`
		Bet           float64 `json:"bet" validate:"required"`
		CreateDate    string  `json:"createDate" validate:"required"`
		Ts            float64 `json:"ts" validate:"required"`
	}

	// 重复请求校验结构
	type CacheRequest struct {
		Req RequestData `json:"req"`
	}

	requestData := RequestData{}
	responseSuccess := ResponseSuccess{}
	responseError := ResponseError{}
	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-Bet 商户代码AgentCode错误, code:", agentCode, " l:", l.agentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误：code:" + agentCode
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-Bet 获取用户余额 币别校验错误 curr:", curr, " l:", l.currency)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-Bet 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Info("fc-Bet 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		return
	}
	logs.Info("fc-Bet 解析后参数：", requestData)
	body, bodyErr := json.Marshal(requestData)
	if bodyErr != nil {
		logs.Error("fc-Bet指定游戏转换参数json失败")
		responseError.Result = Err_Params_Not_Is_Json
		responseError.ErrorText = "传入的参数不是json格式，转换参数json失败"
		ctx.RespJson(responseError)
		return
	}
	bodyStr := string(body)

	// 重复请求校验
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:fc:bet:ta:%v:%v", server.Project(), server.Module(), requestData.MemberAccount, requestData.BetID)
	logs.Info("fc-Bet 重复请求缓存key：", cacheKeyTa)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.BetID == requestData.BetID && cacheRes.Req.RecordID == requestData.RecordID && cacheRes.Req.MemberAccount == requestData.MemberAccount {
				responseError.Result = Err_999
				responseError.ErrorText = "Bet 重复请求下注"
				logs.Error("fc-Bet 重复请求下注:  BetID = ", requestData.BetID)
				ctx.RespJson(responseError)
				return
			}
			logs.Error("fc-Bet 下注事务相同 内容不容:  bet ta = ", requestData.BetID, requestData, cacheRes)
		} else {
			logs.Error("fc-Bet 重复下注事务:  bet ta = ", requestData.BetID, "反序列化返回结果错误", e)
		}

		responseError.Result = Err_999
		responseError.ErrorText = "重复请求下注"
		ctx.RespJson(responseError)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: requestData}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, FcCacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("fc-Bet 下注请求 设置重复请求缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()
	logs.Info("fc-Bet 重复请求校验结束")

	// 游戏类型校验
	boolGame := CheckGame(requestData.GameID)
	if !boolGame {
		logs.Info("fc-Bet 当前接口不支持该游戏(仅支持多彩骰宝(27007)、Lucky9、向钱冲（27008）、扫雷高手（27009）、爬塔高手（27010）) gameId=", requestData.GameID)
		responseError.Result = Err_999
		responseError.ErrorText = "fc-Bet 接口不支持该游戏，gameId: " + fmt.Sprintf("%v", requestData.GameID)
		ctx.RespJson(responseError)
		return
	}

	//根据游戏获取分类
	gameId := fmt.Sprintf("%d", requestData.GameID)
	gameList := thirdGameModel.GameList{}
	err := server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("fc-Bet 投注中奖 获取游戏失败 BetID=", requestData.BetID, " reqdata.Game=", gameList.GameId, " err=", err.Error())
		responseError.Result = Err_Game_Not_Exist
		responseError.ErrorText = "游戏不存在"
		ctx.RespJson(responseError)
		return
	}

	// 用户余额校验
	user := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId, SellerId, ChannelId, Amount").Where("Account = ?", requestData.MemberAccount).First(&user).Error
	if err != nil {
		logs.Info("fc-Bet  获取用户余额失败 用户账号=", requestData.MemberAccount, " err=", err.Error())
		responseError.Result = Err_999
		responseError.ErrorText = "查询用户余额信息出错"
		ctx.RespJson(responseError)
		return
	}
	amount := user.Amount
	if amount < requestData.Bet {
		logs.Info("fc-Bet  用户余额不足 用户账号=", requestData.MemberAccount, " amount:", amount, " err=", err.Error())
		responseError.Result = Err_Not_Enough_Balance
		responseError.ErrorText = "用户余额不足"
		ctx.RespJson(responseError)
		return
	}

	// 校验三方游戏订单表判断
	tablepre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		tablepre = "x_third_qipai_pre_order"
	} else {
		logs.Error("fc-Bet 游戏类型配置错误 thirdId=", requestData.BetID, " gameList=", gameList)
		responseError.Result = Err_Game_Type_Error
		responseError.ErrorText = "游戏类型配置错误"
		ctx.RespJson(responseError)
		return
	}

	// 开启事务,扣除下注额,生成账变记录,生成注单记录(此接口只注单，不结算，因此订单只加入到预设表中，结算完成后加入正式表)
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		//
		var tempAmount = user.Amount
		// 用户剩余金额 = 用户原有金额-下注额
		tempAmount = tempAmount - requestData.Bet

		// 订单校验，使用三方传入的BankID作为三方订单号
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablepre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", requestData.BetID, l.brandName).First(&order).Error
		if e == nil {
			logs.Error("fc-Bet 注单已存在 thirdId=", requestData.BetID, " order=", order)
			responseError.Result = Err_Third_Id_Repeat
			responseError.ErrorText = "注单已存在"
			ctx.RespJson(responseError)
			return e
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("fc-Bet  查询注单失败 thirdId=", requestData.BetID, " err=", e.Error())
			responseError.Result = Err_999
			responseError.ErrorText = "查询注单失败"
			ctx.RespJson(responseError)
			return e
		}

		// 修改用户金额
		resultTmp := tx.Table("x_user").Where("Account = ?", requestData.MemberAccount).Update("Amount", tempAmount)
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && requestData.Bet == 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("fc-Bet 用户总输赢金额变更失败 gameId=", requestData.GameID,
				" Account=", requestData.MemberAccount, " Bet(下注额)=", requestData.Bet, " err=", e.Error())
			responseError.Result = Err_999
			responseError.ErrorText = "扣款失败"
			ctx.RespJson(responseError)
			return e
		}

		logs.Info("fc-Bet 事务-变更用户金额结束")

		// 创建下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       user.UserId,
			BeforeAmount: user.Amount,
			Amount:       requestData.Bet, //金额变动值：即下注额
			AfterAmount:  tempAmount,
			Reason:       utils.BalanceCReasonFCSpBet,
			Memo:         l.brandName + " thirdId:" + requestData.BetID + " GameId:" + fmt.Sprintf("%d", requestData.GameID) + " Bet:" + fmt.Sprintf("%d", requestData.Bet),
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   requestData.CreateDate, //该时间使用的是三方游戏的时间
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("fc-Bet  账变记录失败 gameId=", requestData.GameID,
				" amountLogBet=", amountLogBet, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "创建下注账变记录失败"
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-Bet 事务-创建账变记录结束")

		// 创建注单记录（老虎机是一局一局下注，捕鱼与推币则是多局合在一起推送注单），(当前FC游戏是否都是电子游戏？)
		order = thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			BetChannelId: user.ChannelId, // 下注渠道id，待确认
			UserId:       user.UserId,
			Brand:        l.brandName,
			ThirdId:      requestData.BetID,
			GameId:       fmt.Sprintf("%d", requestData.GameID),
			GameName:     gameList.Name, //使用游戏中文名
			BetAmount:    requestData.Bet,
			//WinAmount:    tempWin, // 此处暂未派奖结算，因此没有该金额
			//ValidBet:     requestData.RequireAmt,  // 有效投注需要派奖结束后才能计算，有效投注 = 下注额 - 中奖金额
			ThirdTime:  requestData.CreateDate,
			Currency:   l.currency,
			RawData:    bodyStr,
			State:      1,
			Fee:        0,
			DataState:  -1, // 开奖状态：(-2:取消订单, -1:下注订单, 1:结算后的订单, 2:已统计返佣的订单, 3:开元棋牌特殊状态)
			CreateTime: requestData.CreateDate,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("fc-Bet 创建预设注单失败 thirdId=", requestData.BetID, " orderPre=", order, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "创建注单失败"
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-Bet 事务-创建预设注单记录结束")

		responseSuccess.Result = 0
		responseSuccess.MainPoints = tempAmount
		ctx.RespJson(responseSuccess)
		return nil
	})
}

func CheckGame(reqGameId int64) bool {
	// 当前接口只允许以下5个游戏调用：多彩骰宝（27007）、Lucky9（暂无）、向钱冲（27008）、扫雷高手（27009）、爬塔高手（27010）
	var gameIds = []int64{27007, 27008, 27009, 27010}
	boolGame := false
	for _, gameId := range gameIds {
		if gameId == reqGameId {
			boolGame = true
		}
	}
	return boolGame
}

// （扫雷、爬塔高手等）派彩
func (l *FcSingleService) Settle(ctx *abugo.AbuHttpContent) {
	type BetTemp struct {
		BetID    string  `json:"betId"`    // 投注编号
		Bet      float64 `json:"bet"`      // 投注金额
		ValidBet float64 `json:"validBet"` // 有效投注
		Win      float64 `json:"win"`      // 派彩金额
		WinLose  float64 `json:"winLose"`  // 净输赢金额
	}
	type RequestData struct {
		RecordId      string    `json:"recordId"`
		BankID        string    `json:"bankId"`
		MemberAccount string    `json:"memberAccount"`
		Currency      string    `json:"currency"`
		GameID        int64     `json:"gameId"`
		GameType      int       `json:"gameType"`
		Bet           float64   `json:"bet"`
		ValidBet      float64   `json:"validBet"`
		Win           float64   `json:"win"`
		Commission    float64   `json:"commission"`
		NetWin        float64   `json:"netWin"`
		GameDate      string    `json:"gameDate"`
		CreateDate    string    `json:"createDate"`
		Refund        float64   `json:"refund"`       // 要返还给玩家的金额（永远为正数）
		SettleBetIDs  []BetTemp `json:"settleBetIds"` // 此次结算了哪些下注；对应至下注的BetID
		Ts            int64     `json:"ts"`           // 发送请求当下的时间
	}

	// 重复请求校验结构
	type CacheRequest struct {
		Req RequestData `json:"req"`
	}

	requestData := RequestData{}
	responseError := ResponseError{}
	responseSuccess := ResponseSuccess{}

	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-Settle 商户代码AgentCode错误, code:", agentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误"
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-Settle 币别校验错误 err=")
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误"
		logs.Error("fc-Settle 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Info("fc-Settle 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		responseError.Result = Err_Params_Not_Is_Json
		responseError.ErrorText = "转换参数失败"
		ctx.RespJson(responseError)
		return
	}
	logs.Info("fc-Settle 解析后参数：", requestData)

	body, bodyErr := json.Marshal(requestData)
	if bodyErr != nil {
		logs.Error("fc-Settle 转换json参数json失败")
		responseError.Result = Err_Params_Not_Is_Json
		responseError.ErrorText = "转换参数失败"
		ctx.RespJson(responseError)
		return
	}
	bodyStr := string(body)

	// 游戏类型校验
	boolGame := CheckGame(requestData.GameID)
	if !boolGame {
		logs.Info("fc-Settle 当前接口不支持该游戏(仅支持多彩骰宝(27007)、Lucky9（暂无）、向钱冲（27008）、扫雷高手（27009）、爬塔高手（27010）) gameId=", requestData.GameID)
		responseError.Result = Err_999
		responseError.ErrorText = "fc-Settle 接口不支持该游戏，gameId: " + fmt.Sprintf("%v", requestData.GameID)
		ctx.RespJson(responseError)
		return
	}

	// 重复请求校验
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:fc:settle:ta:%v:%v", server.Project(), server.Module(), requestData.MemberAccount, requestData.BankID)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.BankID == requestData.BankID && cacheRes.Req.MemberAccount == requestData.MemberAccount && cacheRes.Req.RecordId == requestData.RecordId {
				responseError.Result = Err_999
				responseError.ErrorText = "重复的结算请求"
				logs.Debug("fc-Settle 重复的结算请求:  rollback ta = ", requestData.BankID)
				ctx.RespJson(responseError)
				return
			}
		} else {
			logs.Error("fc-Settle 重复回滚事务:  rollback ta = ", requestData.BankID, "反序列化返回结果错误", e)
		}

		responseError.Result = 999
		responseError.ErrorText = "重复请求回滚"
		logs.Debug("fc-Settle 重复的结算请求:  rollback ta = ", requestData.BankID)
		ctx.RespJson(responseError)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: requestData}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, FcCacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("fc-Settle 设置响应缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()

	// 用户信息校验
	user := thirdGameModel.UserBalance{}
	userErr := server.Db().GormDao().Table("x_user").Select("UserId, SellerId, ChannelId, Account, Amount").Where("Account = ?", requestData.MemberAccount).First(&user).Error
	if userErr != nil {
		logs.Error("fc-Settle 用户信息查询错误:  account=", requestData.MemberAccount, userErr.Error())
		responseError.Result = Err_999
		responseError.ErrorText = "用户信息查询错误"
		ctx.RespJson(responseError)
		return
	}
	game := thirdGameModel.GameList{}
	gameErr := server.Db().GormDao().Table("x_game_list").Select("Brand, GameId, Name, GameType").Where("GameId = ?", requestData.GameID).First(&game).Error
	if gameErr != nil {
		logs.Error("fc-Settle 游戏信息查询错误：GameId:" + strconv.FormatInt(requestData.GameID, 10) + gameErr.Error())
		responseError.Result = Err_999
		responseError.ErrorText = "游戏信息查询错误"
		ctx.RespJson(responseError)
		return
	}

	// 校验三方游戏订单表判断
	tablepre := "x_third_dianzhi_pre_order"
	table := "x_third_dianzhi"
	if game.GameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	} else if game.GameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
	} else {
		logs.Error("fc-settle 游戏类型配置错误 BankID=", requestData.BankID, " gameList=", game)
		responseError.Result = Err_Game_Type_Error
		responseError.ErrorText = "游戏类型配置错误"
		ctx.RespJson(responseError)
		return
	}

	for _, tempBet := range requestData.SettleBetIDs {
		// 开始派奖、账变等
		server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

			// 订单校验，暂时使用三方传入的BankID作为三方订单号
			order := thirdGameModel.ThirdOrder{}
			e := tx.Table(tablepre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", tempBet.BetID, l.brandName).First(&order).Error
			if e != nil {
				logs.Error("fc-settle 注单查询错误 betID=", tempBet.BetID, " order=", order)
				responseError.Result = Err_999
				responseError.ErrorText = "注单查询错误"
				ctx.RespJson(responseError)
				return e
			}

			tempAmount := user.Amount + tempBet.Win
			// 修改用户金额
			resultTmp := tx.Table("x_user").Where("Account = ?", requestData.MemberAccount).Update("Amount", tempAmount)
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && requestData.NetWin != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("fc-settle 派奖修改用户金额失败 BetId:"+tempBet.BetID+" gameId=", requestData.GameID,
					" Account=", requestData.MemberAccount, " NetWin(总输赢)=", requestData.NetWin, " err=", e.Error())
				responseError.Result = 999
				responseError.ErrorText = responseError.ErrorText + " 派奖失败, betID:" + tempBet.BetID
				ctx.RespJson(responseError)
				return e
			}

			logs.Info("fc-settle 事务-变更用户金额结束")

			// 创建派奖账变记录
			amountLogBet := thirdGameModel.AmountChangeLog{
				UserId:       user.UserId,
				BeforeAmount: user.Amount,
				Amount:       tempBet.Win, //当前注单游戏赢分
				AfterAmount:  tempAmount,
				Reason:       utils.BalanceCReasonFCSpWin,
				Memo:         l.brandName + " NetWin,GameId:" + fmt.Sprintf("%d", requestData.GameID) + " BetId:" + tempBet.BetID,
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				CreateTime:   requestData.CreateDate, //该时间使用的是三方游戏的时间
			}
			e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
			if e != nil {
				logs.Error("fc-settle 账变记录失败 gameId=", requestData.GameID,
					" amountLogBet=", amountLogBet, " error=", e.Error())
				responseError.Result = Err_Third_Id_Add_Fail
				responseError.ErrorText = responseError.ErrorText + "创建派奖账变记录失败, betID:" + tempBet.BetID + "\n"
				ctx.RespJson(responseError)
				return e
			}
			logs.Info("fc-settle 事务-创建账变记录结束")

			// 创建正式注单记录
			order = thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				BetChannelId: user.ChannelId, // 下注渠道id，待确认
				UserId:       user.UserId,
				Brand:        l.brandName,
				ThirdId:      tempBet.BetID,
				GameId:       fmt.Sprintf("%d", requestData.GameID),
				GameName:     game.Name, //使用游戏中文名
				BetAmount:    tempBet.Bet,
				WinAmount:    tempBet.Win, // 输赢金额：如果是下注，则展示的是下注金额；如果是彩金，则记录的是彩金输赢金额
				ValidBet:     tempBet.ValidBet,
				ThirdTime:    requestData.CreateDate,
				Currency:     l.currency,
				RawData:      bodyStr,
				State:        1,
				Fee:          0,
				DataState:    1, // 开奖状态. (-2:, -1:, 1:已开奖, 2:, 3:)
				CreateTime:   requestData.CreateDate,
			}
			// 修改预设注单状态
			resultTmp = tx.Table(tablepre).Where("ThirdId=? and Brand=?", tempBet.BetID, l.brandName).Updates(map[string]interface{}{
				"DataState": 1,
				"ThirdTime": requestData.CreateDate,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("fc-settle更新条数0")
			}
			if e != nil {
				logs.Error("fc-settle 派奖更新注单失败 thirdId=", tempBet.BetID, " orderPre=", order, " error=", e.Error())
				responseError.Result = Err_Third_Id_Add_Fail
				responseError.ErrorText = responseError.ErrorText + " 派奖更新注单失败, betID:" + tempBet.BetID + "\n"
				return e
			}

			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("fc-settle 派奖创建正式注单失败 thirdId=", requestData.BankID, " orderPre=", order, " error=", e.Error())
				responseError.Result = Err_Third_Id_Add_Fail
				responseError.ErrorText = responseError.ErrorText + "派奖创建注单失败, betID:" + tempBet.BetID + "\n"
				ctx.RespJson(responseError)
				return e
			}
			logs.Info("fc-settle 事务-创建派奖记录结束, betID:", tempBet.BetID)
			responseSuccess.Result = 0
			responseSuccess.MainPoints = tempAmount
			ctx.RespJson(responseSuccess)
			return nil
		})
	}
}

// （扫雷、爬塔高手等）取消下注
func (l *FcSingleService) CancelBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		BetID         string  `json:"betId"`                             // 下注编号（唯一码）
		MemberAccount string  `json:"MemberAccount" validate:"required"` // 玩家账号
		Currency      string  `json:"currency" validate:"required"`      // 币别
		GameId        int64   `json:"gameId" validate:"required"`        // 游戏id
		Bet           float64 `json:"bet" validate:"required"`           // 下注额
		RecordID      string  `json:"recordId" validate:"required"`      // 注单编号（唯一码）
		TS            int64   `json:"ts" validate:"required"`            // 发送请求当下的时间戳（毫秒）
	}
	responseError := ResponseError{}
	responseSuccess := ResponseSuccess{}
	requestData := RequestData{}
	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-CancelBet 商户代码AgentCode错误, code:", agentCode, " l:", l.agentCode)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "商户代码AgentCode错误"
		ctx.RespJson(responseError)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-CancelBet 币别错误,支持的比别:HKD，传入的币别:", curr, " l:", l.currency)
		responseError.Result = Err_Params_Fail
		responseError.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseError)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseError.Result = Err_Params_Decrypt_Fail
		responseError.ErrorText = "参数解密错误"
		logs.Error("fc-CancelBet 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseError)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Info("fc-CancelBet 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		responseError.Result = Err_Params_Not_Is_Json
		responseError.ErrorText = "参数转换错误"
		ctx.RespJson(responseError)
		return
	}
	logs.Info("fc-CancelBet 解析后参数：", requestData)
	//游戏类型校验
	boolGame := CheckGame(requestData.GameId)
	if !boolGame {
		logs.Info("fc-CancelBet 当前接口不支持该游戏(仅支持多彩骰宝（27007）、Lucky9（暂无）、向钱冲（27008）、扫雷高手（27009）、爬塔高手（27010）) gameId=", requestData.GameId)
		responseError.Result = Err_999
		responseError.ErrorText = "接口不支持该游戏"
		ctx.RespJson(responseError)
		return
	}
	game := thirdGameModel.GameList{}
	gameErr := server.Db().GormDao().Table("x_game_list").Select("Brand, GameId, Name, GameType").Where("GameId = ?", requestData.GameId).First(&game).Error
	if gameErr != nil {
		logs.Error("fc-CancelBet 游戏信息查询错误" + strconv.FormatInt(requestData.GameId, 10) + gameErr.Error())
		responseError.Result = Err_999
		responseError.ErrorText = "游戏信息查询错误"
		ctx.RespJson(responseError)
		return
	}
	// 校验三方游戏订单表判断
	tablepre := "x_third_dianzhi_pre_order"
	if game.GameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
	} else if game.GameType == 2 {
		tablepre = "x_third_qipai_pre_order"
	} else {
		logs.Error("fc-CancelBet 游戏类型配置错误 thirdId=", requestData.BetID, " gameList=", game)
		responseError.Result = Err_Game_Type_Error
		responseError.ErrorText = "游戏类型配置错误"
		ctx.RespJson(responseError)
		return
	}
	// 结算状态校验（已结算不能取消）、游戏id校验
	order := thirdGameModel.ThirdOrder{}
	err := server.Db().GormDao().Table(tablepre).Where("Brand = ? and ThirdId = ? and GameId = ? and DataState = 1", l.brandName, requestData.BetID, requestData.GameId).First(&order).Error
	if err == nil {
		logs.Error("fc-CancelBet 当前订单已结算，不能取消注单 thirdId=", requestData.BetID, " gameID=", requestData.GameId, " err=", err.Error())
		responseError.Result = Err_999
		responseError.ErrorText = "当前订单已结算，不能取消注单"
		ctx.RespJson(responseError)
		return
	}

	// 用户校验
	user := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("Account = ?", requestData.MemberAccount).First(&user).Error
	if err != nil {
		logs.Error("fc-CancelBet 获取玩家余额 失败 Account=", requestData.MemberAccount, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			responseError.Result = Err_Account_Not_Exist
			responseError.ErrorText = "账号不存在, account:" + requestData.MemberAccount
		} else {
			responseError.Result = Err_999
			responseError.ErrorText = "查询会员余额失败, account:" + requestData.MemberAccount + "\n"
		}
		ctx.RespJson(responseError)
		return
	}
	cancelAmount := user.Amount + requestData.Bet
	// 修改注单状态、返回用户金额、增加账变记录
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		resultTemp := tx.Table(tablepre).Where("Brand = ? and ThirdId = ? and GameId = ? and DataState = 1", l.brandName, requestData.BetID, requestData.GameId).Updates(map[string]interface{}{
			"DataState": -2,
		})
		e := resultTemp.Error
		if e != nil && resultTemp.RowsAffected <= 0 {
			logs.Error("fc-CancelBet 取消注单修改记录条数：0 thirdId:", requestData.BetID, " err=", e.Error())
			responseError.Result = Err_Third_Id_Not_Exist
			responseError.ErrorText = "取消注单修改记录条数：0"
			ctx.RespJson(responseError)
			return nil
		}

		resultTemp = tx.Table("x_user").Where("Account = ?", requestData.MemberAccount).Updates(map[string]interface{}{
			//"Amount": daogorm.Expr("Amount + ?", requestData.Bet),
			"Amount": cancelAmount,
		})
		e = resultTemp.Error
		if e != nil && resultTemp.RowsAffected <= 0 {
			logs.Error("fc_CancelBet 退回用户下注金额失败，更新数据记录：0")
			responseError.Result = Err_999
			responseError.ErrorText = "退回用户下注金额失败，更新数据记录：0"
			ctx.RespJson(responseError)
			return nil
		}

		t := time.Unix(0, requestData.TS*int64(time.Millisecond))
		// 创建回滚下注账变记录
		cancelBet := thirdGameModel.AmountChangeLog{
			UserId:       user.UserId,
			BeforeAmount: user.Amount,
			Amount:       requestData.Bet,
			AfterAmount:  user.Amount + requestData.Bet,
			Reason:       utils.BalanceCReasonFCSpCancel,
			Memo:         l.brandName + " cancel,thirdId:" + requestData.BetID,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   t.Format("2006-01-02 15:04:05"), // 使用三方调用时间TS
		}
		e = tx.Table("x_amount_change_log").Create(&cancelBet).Error
		if e != nil {
			logs.Error("fc-CancelBet 创建回滚下注账变记录失败 thirdId=", requestData.BetID, " cancelBet=", cancelBet, " error=", e.Error())
			responseError.Result = Err_Third_Id_Add_Fail
			responseError.ErrorText = "创建回滚下注账变记录失败, BetID:" + requestData.BetID
			ctx.RespJson(responseError)
			return e
		}
		logs.Info("fc-CancelBet 撤单事务结束 thirdId:", requestData.BetID, " cancelBet=", cancelBet)
		responseSuccess.Result = 0
		responseSuccess.MainPoints = cancelAmount
		ctx.RespJson(responseSuccess)
		return nil
	})
}

// 活动派彩(同时对多个用户派彩)
func (l *FcSingleService) EventSettle(ctx *abugo.AbuHttpContent) {
	type Event struct {
		EventID       string  `json:"eventId"`
		MemberAccount string  `json:"memberAccount"`
		GameID        int64   `json:"gameId"`
		BankID        string  `json:"bankId"`     // 交易编号
		TrsID         string  `json:"trsId"`      // 奖励编号
		Points        float64 `json:"points"`     // 派彩金额
		CreateTime    string  `json:"createTime"` // 创建时间
	}

	type RequestData struct {
		EventData []Event `json:"eventData"`
	}

	type ResponseData struct {
		Result     int64   `json:"result"`
		ErrorText  string  `json:"ErrorText"`
		MainPoints float64 `json:"mainPoints"`
	}

	responseData := ResponseData{}
	requestData := RequestData{}
	//err := ctx.RequestData(&requestData)
	//if err != nil {
	//	responseData.Result = 999
	//	responseData.ErrorText = "请求参数转换出错" + err.Error()
	//	logs.Info("fc_EventSettle 请求参数转换出错" + err.Error())
	//	return
	//}

	ctx.Gin().Request.ParseForm()
	agentCode := ctx.Gin().Request.PostFormValue("AgentCode")
	curr, _ := ctx.Gin().GetPostForm("Currency")
	paramsAes := ctx.Gin().PostForm("Params")
	//signTemp := ctx.Gin().PostForm("Sign")
	if agentCode != l.agentCode {
		logs.Error("fc-EventSettle 商户代码AgentCode错误, code:", agentCode)
		responseData.Result = 1099
		responseData.ErrorText = "商户代码AgentCode错误：code:" + agentCode
		ctx.RespJson(responseData)
		return
	}

	// 币别是否正确（目前FC只支持HKD）
	if curr != l.currency {
		logs.Info("fc-EventSettle 获取用户余额 币别校验错误 err=")
		responseData.Result = 1099
		responseData.ErrorText = "币别错误,支持的比别:HKD，传入的币别:" + curr
		ctx.RespJson(responseData)
		return
	}
	paramsStr, decErr := Decrypt(l.agentKey, paramsAes)
	if decErr != nil {
		responseData.Result = 303
		responseData.ErrorText = "参数解密错误：" + decErr.Error()
		logs.Error("fc-EventSettle 参数解密错误：", decErr.Error(), " paramsStr:", paramsStr, " paramsAes:", paramsAes)
		ctx.RespJson(responseData)
		return
	}
	// 清理不可见无效字符
	cleanParamsStr := cleanString(paramsStr)
	// 解析 JSON 字符串到 参数requestData(map格式)
	decErr = json.Unmarshal([]byte(cleanParamsStr), &requestData)
	if decErr != nil {
		logs.Error("fc-EventSettle 参数解析到map错误：", decErr.Error(), " paramsStr:", paramsStr, " reqData:", requestData)
		return
	}
	logs.Info("fc-EventSettle 解析后参数：", requestData)

	for _, event := range requestData.EventData {
		// 用户校验、游戏校验、是否已经存在派彩
		user := thirdGameModel.UserBalance{}
		err := server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("Account = ?", event.MemberAccount).First(&user).Error
		if err != nil {
			responseData.Result = 999
			responseData.ErrorText = "用户查询出错, account:" + event.MemberAccount
			ctx.RespJson(responseData)
			return
		}
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand, GameId, Name, GameType").Where("Brand = ? and GameId = ?", l.brandName, event.GameID).First(&gameList).Error
		if err != nil {
			responseData.Result = 999
			responseData.ErrorText = "游戏查询出错, GameId:" + fmt.Sprintf("%d", event.GameID)
			ctx.RespJson(responseData)
			return
		}

		// 校验三方游戏订单表判断
		tablepre := "x_third_dianzhi_pre_order"
		table := "x_third_dianzhi"
		if gameList.GameType == 1 {
			tablepre = "x_third_dianzhi_pre_order"
			table = "x_third_dianzhi"
		} else if gameList.GameType == 2 {
			tablepre = "x_third_qipai_pre_order"
			table = "x_third_qipai"
		} else {
			logs.Error("fc-EventSettle 游戏类型配置错误 thirdId=", event.BankID, " gameList=", gameList)
			responseData.Result = 999
			responseData.ErrorText = "游戏类型配置错误"
			ctx.RespJson(responseData)
			return
		}

		dianzhi := thirdGameModel.ThirdOrder{}
		err = server.Db().GormDao().Table(table).Select("thirdId").Where("Brand = ? and thirdId = ?", l.brandName, event.BankID).First(&dianzhi).Error
		if err == nil {
			logs.Error("fc-EventSettle 当前派奖已经存在结算记录, BankId:" + event.BankID)
			responseData.Result = 999
			responseData.ErrorText = "当前派奖已经存在结算记录, BankId:" + event.BankID
			ctx.RespJson(responseData)
			return
		}

		// 修改用户余额、创建预设和正式结算、账变记录
		tranErr := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			cancelAmount := user.Amount + event.Points
			resultTemp := server.Db().GormDao().Table("x_user").Updates(map[string]interface{}{
				"Amount": cancelAmount,
			})
			err = resultTemp.Error
			if err != nil && resultTemp.RowsAffected <= 0 {
				logs.Error("fc_EventSettle 活动派彩修改用户金额失败，更新数据记录：0")
				responseData.Result = 999
				responseData.ErrorText = "活动派彩修改用户金额失败，更新数据记录：0"
				ctx.RespJson(responseData)
				return nil
			}
			// 创建正式注单记录
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				BetChannelId: user.ChannelId, // 下注渠道id，待确认
				UserId:       user.UserId,
				Brand:        l.brandName,
				ThirdId:      event.BankID,
				GameId:       fmt.Sprintf("%d", event.GameID),
				GameName:     gameList.Name, //使用游戏中文名
				BetAmount:    0,
				WinAmount:    event.Points, // 输赢金额：如果是下注，则展示的是下注金额；如果是彩金，则记录的是彩金输赢金额
				ValidBet:     event.Points,
				ThirdTime:    event.CreateTime,
				Currency:     l.currency,
				RawData:      fmt.Sprintf("%v", event),
				State:        1,
				Fee:          0,
				DataState:    1, // 开奖状态. (-2:, -1:, 1:已开奖, 2:, 3:)
				CreateTime:   time.Now().In(fcUTC8).Format("2006-01-02 15:04:05"),
			}
			e := tx.Table(tablepre).Create(&order).Error
			if e != nil {
				logs.Error("fc-EventSettle 创建活动派彩预设注单失败 thirdId=", event.BankID, " orderPre=", order, " error=", e.Error())
				responseData.Result = 999
				responseData.ErrorText = "创建活动派彩注单失败, BankID:" + event.BankID
				ctx.RespJson(responseData)
				return e
			}
			e = server.Db().GormDao().Table(table).Create(&order).Error
			if e != nil {
				logs.Error("fc-EventSettle 创建活动派彩正式注单失败 thirdId=", event.BankID, " orderPre=", order, " error=", e.Error())
				responseData.Result = 999
				responseData.ErrorText = "创建活动派彩注单失败, BankID:" + event.BankID
				ctx.RespJson(responseData)
				return e
			}

			// 账变
			// 创建回滚下注账变记录
			cancelBet := thirdGameModel.AmountChangeLog{
				UserId:       user.UserId,
				BeforeAmount: user.Amount,
				Amount:       event.Points,
				AfterAmount:  user.Amount + event.Points,
				Reason:       utils.BalanceCReasonJiLiCancel,
				Memo:         l.brandName + " cancel,thirdId:" + event.BankID,
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				CreateTime:   event.CreateTime, // 使用三方时间
			}
			e = tx.Table("x_amount_change_log").Create(&cancelBet).Error
			if e != nil {
				logs.Error("fc_EventSettle 创建活动派彩账变记录失败 thirdId=", event.BankID, " error=", e.Error())
				responseData.Result = 999
				responseData.ErrorText = "创建活动派彩账变记录失败, BankID:" + event.BankID
				ctx.RespJson(responseData)
				return e
			}
			return nil
		})

		if tranErr != nil {
			ctx.RespJson(responseData)
		}

		responseData.Result = 0
		responseData.ErrorText = ""
		ctx.RespJson(responseData)
		return
	}

}

// 后端返回hub88体育betsy体育下注框样式（备用）
func (l *FcSingleService) SportsBookCss(ctx *abugo.AbuHttpContent) {
	ctx.Gin().Header("Content-Type", "text/css")
	//ctx.RespFile()
	//ctx.Gin().Header("Content-Disposition", fmt.Sprintf("attachment;filename=test.css"))
	ctx.Gin().String(200,
		`.mobile-menu__head-pro {
  background: #3d4451 !important;
  color: #fff !important;
}`)
	return
}

// 只要包含任意数字，则返回true
func containsBoth(nums []int, a int) bool {
	for _, num := range nums {
		if num == a {
			return true
		}
	}
	return false
}

// 参数解密
func Decrypt(keyStr string, ciphertext string) (string, error) {
	key := []byte(keyStr)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext := make([]byte, len(cipherBytes))
	// 对于ECB模式，直接使用cipher.NewCBCDecrypter即可
	for start := 0; start < len(cipherBytes); start += aes.BlockSize {
		block.Decrypt(plaintext[start:start+aes.BlockSize], cipherBytes[start:start+aes.BlockSize])
	}
	return string(plaintext), nil
}

// 去除不可见字符的辅助函数
func cleanString(s string) string {
	return strings.Map(func(r rune) rune {
		if unicode.IsPrint(r) {
			return r
		}
		return -1
	}, s)
}
