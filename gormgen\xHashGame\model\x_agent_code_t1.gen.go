// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCodeT1 = "x_agent_code_t1"

// XAgentCodeT1 mapped from table <x_agent_code_t1>
type XAgentCodeT1 struct {
	ID            int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`               // id
	UserID        int32     `gorm:"column:UserId;not null;comment:玩家id" json:"UserId"`                          // 玩家id
	AgentCode     string    `gorm:"column:AgentCode;not null;comment:推广码" json:"AgentCode"`                     // 推广码
	FenCheng      string    `gorm:"column:FenCheng;comment:分成比例" json:"FenCheng"`                               // 分成比例
	RegisterCount int32     `gorm:"column:RegisterCount;comment:注册人数" json:"RegisterCount"`                     // 注册人数
	BetCount      int32     `gorm:"column:BetCount;comment:投注人数" json:"BetCount"`                               // 投注人数
	CreateTime    time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	Memo          string    `gorm:"column:Memo" json:"Memo"`
}

// TableName XAgentCodeT1's table name
func (*XAgentCodeT1) TableName() string {
	return TableNameXAgentCodeT1
}
