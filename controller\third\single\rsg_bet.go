package single

import (
	"encoding/json"
	"errors"
	"fmt"
	daogormclause "gorm.io/gorm/clause"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/controller/third/single/base"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	"xserver/abugo"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
)

// Bet 下注 API URL Bet
func (l *RSGSingleService) Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string  `json:"SystemCode"`    // 系统代码，必填
		WebId         string  `json:"WebId"`         // 站台代码，必填
		UserId        string  `json:"UserId"`        // 会员惟一识别码，必填
		TransactionID string  `json:"TransactionID"` // 交易惟一识别码，必填
		Currency      string  `json:"Currency"`      // 币别代码，必填
		GameId        int     `json:"GameId"`        // 游戏代码，必填
		SubGameType   int     `json:"SubGameType"`   // 子游戏代码，必填
		SequenNumber  int64   `json:"SequenNumber"`  // 游戏纪录惟一编号，必填
		Amount        float64 `json:"Amount"`        // 下注金额，必填
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 下注")
	if err != nil {
		logs.Error("RSG_single 下注 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 下注 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 下注 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 下注 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 下注 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 检查订单是否已存在
	tablePre := "x_third_dianzhi_pre_order"
	thirdId := strconv.Itoa(int(reqdata.SequenNumber))
	// 开始下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询订单是否存在
		orderPre, exists, err := base.GetOrderForUpdate(tx, thirdId, l.brandName, tablePre)
		// 处理查询错误
		if err != nil {
			// 查询出错
			logs.Error("RSG_single 下注 查询订单失败 thirdId=", thirdId, " err=", err.Error(), " order=", orderPre)
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
			return err
		}

		// 判断订单是否存在
		if exists {
			logs.Error("RSG_single 下注 订单已存在 thirdId=", thirdId, " order=", orderPre)
			respdata.ErrorCode = RSG_Code_Duplicate_Seq
			respdata.ErrorMessage = "订单号重复"
			l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
			return errors.New(fmt.Sprintf("订单号重复，thirdId= %s", thirdId))
		}

		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 下注 获取用户余额失败 userId=", userId, " err=", e.Error())
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		} else {
			respdata.Data.Balance = userBalance.Amount
		}

		// 获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)

		// 检查余额是否足够
		if reqdata.Amount > userBalance.Amount {
			logs.Error("RSG_single 下注 余额不足 userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdata.Amount=", reqdata.Amount)
			respdata.ErrorCode = RSG_Code_Balance_Not_Enough
			respdata.ErrorMessage = "余额不足"
			return fmt.Errorf("余额不足")
		}

		// 获取游戏名称
		gameName := ""
		if name, ok := l.games[reqdata.GameId]; ok {
			gameName = name
		} else {
			gameList := thirdGameModel.GameList{}
			e = tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
			if e == nil {
				gameName = gameList.Name
			} else {
				gameName = fmt.Sprintf("%s_%d", l.brandName, reqdata.GameId)
			}
		}

		// 创建预注单
		rawData, _ := json.Marshal(reqdata)
		order := thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       strconv.Itoa(reqdata.GameId),
			GameName:     gameName,
			BetAmount:    reqdata.Amount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    utils.GetCurrentTime(),
			Currency:     l.currency,
			RawData:      string(rawData),
			State:        1,
			DataState:    -1, // 下注状态
			CreateTime:   utils.GetCurrentTime(),
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("RSG_single 下注 创建预注单失败 userId=", userId, " thirdId=", thirdId, " order=", order, " err=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		// 扣除用户余额
		if reqdata.Amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, reqdata.Amount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", reqdata.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
				e = fmt.Errorf("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 下注 扣除用户余额失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       -reqdata.Amount,
				AfterAmount:  userBalance.Amount - reqdata.Amount,
				Reason:       utils.BalanceCReasonRSGBet,
				Memo:         "RSG bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   utils.GetCurrentTime(),
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("RSG_single 下注 创建账变记录失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}

		userBalance.Amount -= reqdata.Amount

		balance := math.Floor(userBalance.Amount*100) / 100
		respdata.Data.Balance = balance
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 下注 事务处理失败 userId=", userId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
	if err != nil {
		logs.Error("RSG_single 下注 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 下注")
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}
