package abugo

/*
	go get github.com/beego/beego/logs
	go get github.com/spf13/viper
	go get github.com/gin-gonic/gin
	go get github.com/go-redis/redis
	go get github.com/garyburd/redigo/redis
	go get github.com/go-sql-driver/mysql
	go get github.com/satori/go.uuid
	go get github.com/gorilla/websocket
	go get github.com/jinzhu/gorm
	go get github.com/imroc/req
	go get github.com/go-playground/validator/v10
	go get github.com/go-playground/universal-translator
	go get code.google.com/p/mahonia
	go get github.com/360EntSecGroup-Skylar/excelize
*/
import (
	"bytes"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/des"
	"crypto/hmac"
	"crypto/md5"
	crand "crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"database/sql"
	"encoding/asn1"
	"encoding/base32"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"mime/multipart"
	"net/http"
	"os"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"

	mrand "math/rand"

	"github.com/beego/beego/logs"
	"github.com/garyburd/redigo/redis"
	"github.com/gin-gonic/gin"
	val "github.com/go-playground/validator/v10"
	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-module/carbon/v2"
	"github.com/gorilla/websocket"
	"github.com/jinzhu/gorm"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/viper"
	"github.com/zhms/xgo/xgo"
	daomysql "gorm.io/driver/mysql"
	daogorm "gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var TimeLayout string = "2006-01-02 15:04:05"
var DateLayout string = "2006-01-02"

func get_config_int64(key string, invalval int64) int64 {
	val := viper.GetInt64(key)
	if val == invalval {
		err := fmt.Sprint("read config error:", key)
		logs.Error(err)
		panic(err)
	}
	return val
}

func get_config_int(key string, invalval int) int {
	val := viper.GetInt(key)
	if val == invalval {
		err := fmt.Sprint("read config error:", key)
		logs.Error(err)
		panic(err)
	}
	return val
}

func get_config_string(key string, invalval string) string {
	val := viper.GetString(key)
	if val == invalval {
		err := fmt.Sprint("read config error:", key)
		logs.Error(err)
		panic(err)
	}
	return val
}

// ////////////////////////////////////////////////////////////////////////////////
// 分布式id生成
// ///////////////////////////////////////////////////////////////////////////////
const (
	snow_nodeBits  uint8 = 10
	snow_stepBits  uint8 = 12
	snow_nodeMax   int64 = -1 ^ (-1 << snow_nodeBits)
	snow_stepMax   int64 = -1 ^ (-1 << snow_stepBits)
	snow_timeShift uint8 = snow_nodeBits + snow_stepBits
	snow_nodeShift uint8 = snow_stepBits
)

var snow_epoch int64 = 1514764800000

type snowflake struct {
	mu        sync.Mutex
	timestamp int64
	node      int64
	step      int64
}

func (n *snowflake) GetId() int64 {
	n.mu.Lock()
	defer n.mu.Unlock()
	now := time.Now().UnixNano() / 1e6
	if n.timestamp == now {
		n.step++
		if n.step > snow_stepMax {
			for now <= n.timestamp {
				now = time.Now().UnixNano() / 1e6
			}
		}
	} else {
		n.step = 0
	}
	n.timestamp = now
	result := (now-snow_epoch)<<snow_timeShift | (n.node << snow_nodeShift) | (n.step)
	return result
}

type IdWorker interface {
	GetId() int64
}

var idworker IdWorker

func NewIdWorker(node int64) {
	if node < 0 || node > snow_nodeMax {
		panic(fmt.Sprintf("snowflake节点必须在0-%d之间", node))
	}
	snowflakeIns := &snowflake{
		timestamp: 0,
		node:      node,
		step:      0,
	}
	idworker = snowflakeIns
}

func GetId() int64 {
	return idworker.GetId()
}
func GetUuid() string {
	id, _ := uuid.NewV4()
	return id.String()
}
func Run() {
	for i := range abuwsmsgqueue {
		if i.MsgData == nil {
			i.Ws.dispatch(i.MsgType, i.Id, abumsgdata{}, i.callback)
		} else {
			i.Ws.dispatch(i.MsgType, i.Id, *i.MsgData, i.callback)
		}
	}
}

// ////////////////////////////////////////////////////////////////////////////////
// abugo初始化
// ///////////////////////////////////////////////////////////////////////////////
func Init() {
	mrand.NewSource(time.Now().UnixNano())
	initCarbonTime()
	gin.SetMode(gin.ReleaseMode)
	logs.EnableFuncCallDepth(true)
	logs.SetLogFuncCallDepth(3)
	logs.SetLogger(logs.AdapterFile, `{"filename":"_log/logfile.log","maxsize":10485760}`)
	logs.SetLogger(logs.AdapterConsole, `{"color":true}`)
	viper.AddConfigPath("./")
	viper.AddConfigPath("./config")
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	err := viper.ReadInConfig()
	if err != nil {
		logs.Error(err)
		return
	}
	snowflakenode := get_config_int64("server.snowflakenode", 0)
	if snowflakenode != 0 {
		NewIdWorker(snowflakenode)
	}
}

type AbuDbError struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

func GetDbResult(rows *sql.Rows, ref interface{}) *AbuDbError {
	fields, _ := rows.Columns()
	scans := make([]interface{}, len(fields))
	for i := range scans {
		scans[i] = &scans[i]
	}
	errscan := rows.Scan(scans...)
	if errscan != nil {
		return &AbuDbError{1, errscan.Error()}
	}
	data := make(map[string]interface{})
	ct, _ := rows.ColumnTypes()
	for i := range fields {
		if scans[i] != nil {
			typename := ct[i].DatabaseTypeName()
			if typename == "INT" || typename == "TINYINT" || typename == "SMALLINT" || typename == "BIGINT" ||
				typename == "UNSIGNED INT" || typename == "UNSIGNED TINYINT" || typename == "UNSIGNED SMALLINT" || typename == "UNSIGNED BIGINT" ||
				typename == "UNSIGNED" {
				if reflect.TypeOf(scans[i]).Name() == "" {
					v, _ := strconv.ParseInt(string(scans[i].([]uint8)), 10, 64)
					data[fields[i]] = v
				} else {
					data[fields[i]] = scans[i]
				}
			} else if typename == "DOUBLE" || typename == "DECIMAL" {
				if reflect.TypeOf(scans[i]).Name() == "" {
					v, _ := strconv.ParseFloat(string(scans[i].([]uint8)), 64)
					data[fields[i]] = v
				} else {
					data[fields[i]] = scans[i]
				}
			} else {
				data[fields[i]] = string(scans[i].([]uint8))
			}
		} else {
			data[fields[i]] = nil
		}
	}
	jdata, _ := json.Marshal(&data)
	abuerr := AbuDbError{}
	err := json.Unmarshal(jdata, &abuerr)
	if err != nil {
		logs.Error(err)
		return &AbuDbError{2, err.Error()}
	}
	if abuerr.ErrCode != 0 && len(abuerr.ErrMsg) > 0 {
		return &abuerr
	}
	err = json.Unmarshal(jdata, ref)
	if err != nil {
		logs.Error(err)
		return &AbuDbError{3, err.Error()}
	}
	return nil
}

func GetDbResultNoAbuError(rows *sql.Rows, ref interface{}) error {
	fields, _ := rows.Columns()
	if len(fields) == 0 {
		return errors.New("no fields")
	}
	scans := make([]interface{}, len(fields))
	for i := range scans {
		scans[i] = &scans[i]
	}
	rows.Scan(scans...)
	data := make(map[string]interface{})
	for i := range fields {
		if reflect.TypeOf(scans[i]).Name() == "" {
			data[fields[i]] = string(scans[i].([]uint8))
		} else {
			data[fields[i]] = scans[i]
		}
	}
	jdata, _ := json.Marshal(&data)
	err := json.Unmarshal(jdata, ref)
	return err
}

// ////////////////////////////////////////////////////////////////////////////////
// Http
// ///////////////////////////////////////////////////////////////////////////////
const (
	HTTP_SAVE_DATA_KEY                = "http_save_api_data_key"
	HTTP_RESPONSE_CODE_OK             = 200
	HTTP_RESPONSE_CODE_OK_MESSAGE     = "success"
	HTTP_RESPONSE_CODE_ERROR          = 100
	HTTP_RESPONSE_CODE_ERROR_MESSAGE  = "fail"
	HTTP_RESPONSE_CODE_NOAUTH         = 300
	HTTP_RESPONSE_CODE_NOAUTH_MESSAGE = "noauth"
)

type AbuHttpContent struct {
	gin       *gin.Context
	TokenData string
	Token     string
}

func abuhttpcors() gin.HandlerFunc {
	return func(context *gin.Context) {
		method := context.Request.Method
		context.Header("Access-Control-Allow-Origin", "*")
		context.Header("Access-Control-Allow-Headers", "Content-Type, x-token, Content-Length, X-Requested-With")
		context.Header("Access-Control-Allow-Methods", "GET,POST")
		context.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		context.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		context.Header("Access-Control-Allow-Credentials", "true")
		if method == "OPTIONS" {
			context.AbortWithStatus(http.StatusNoContent)
		}
		context.Next()
	}
}

func (c *AbuHttpContent) RequestData(obj interface{}) error {
	err := c.gin.ShouldBindJSON(obj)
	if err != nil {
		return err
	}
	validator := val.New()
	err = validator.Struct(obj)
	return err
}

func (c *AbuHttpContent) Query(key string) string {
	return c.gin.Query(key)
}

func (c *AbuHttpContent) GetIp() string {
	return c.gin.ClientIP()
}

func (c *AbuHttpContent) Gin() *gin.Context {
	return c.gin
}

func (c *AbuHttpContent) Host() string {
	return c.gin.Request.Host
}

type AbuHttpHandler func(*AbuHttpContent)

type HttpResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

type AbuHttp struct {
	gin           *gin.Engine
	token         *AbuRedis
	tokenrefix    string
	tokenlifetime int
}

type AbuHttpGropu struct {
	http *AbuHttp
	name string
}

func (c *AbuHttp) Static(relativePaths string, root string) {
	c.gin.Static(relativePaths, root)
}

func (c *AbuHttp) NewGroup(path string) *AbuHttpGropu {
	return &AbuHttpGropu{c, path}
}
func (c *AbuHttpGropu) Get(path string, handlers ...AbuHttpHandler) {
	secureHandlers := applySQLInjectionMiddleware(handlers...)
	c.http.Get(fmt.Sprint(c.name, path), secureHandlers...)
}

func (c *AbuHttpGropu) GetNoAuth(path string, handlers ...AbuHttpHandler) {
	secureHandlers := applySQLInjectionMiddleware(handlers...)
	c.http.GetNoAuth(fmt.Sprint(c.name, path), secureHandlers...)
}

func (c *AbuHttpGropu) Post(path string, handlers ...AbuHttpHandler) {
	// 应用SQL注入防护中间件
	secureHandlers := applySQLInjectionMiddleware(handlers...)
	c.http.Post(fmt.Sprint(c.name, path), secureHandlers...)
}

func (c *AbuHttpGropu) PostNoAuth(path string, handlers ...AbuHttpHandler) {
	// 应用SQL注入防护中间件
	secureHandlers := applySQLInjectionMiddleware(handlers...)
	c.http.PostNoAuth(fmt.Sprint(c.name, path), secureHandlers...)
}

func (c *AbuHttpGropu) PostByNoAuthMayUserToken(path string, handlers ...AbuHttpHandler) {
	// 应用SQL注入防护中间件
	secureHandlers := applySQLInjectionMiddleware(handlers...)
	c.http.PostByNoAuthMayUserToken(fmt.Sprint(c.name, path), secureHandlers...)
}

func (ctx *AbuHttpContent) Put(key string, value interface{}) {
	if ctx.gin.Keys == nil {
		ctx.gin.Keys = make(map[string]interface{})
	}
	if ctx.gin.Keys[HTTP_SAVE_DATA_KEY] == nil {
		ctx.gin.Keys[HTTP_SAVE_DATA_KEY] = make(map[string]interface{})
	}
	if len(key) <= 0 || key == "" {
		ctx.gin.Keys[HTTP_SAVE_DATA_KEY] = value
		return
	}
	ctx.gin.Keys[HTTP_SAVE_DATA_KEY].(map[string]interface{})[key] = value
}

func (ctx *AbuHttpContent) RespOK(objects ...interface{}) {
	resp := new(HttpResponse)
	resp.Code = HTTP_RESPONSE_CODE_OK
	resp.Msg = HTTP_RESPONSE_CODE_OK_MESSAGE
	if len(objects) > 0 {
		ctx.Put("", objects[0])
	}
	resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
	if resp.Data == nil {
		resp.Data = make(map[string]interface{})
	}
	ctx.gin.JSON(http.StatusOK, resp)
}

func (ctx *AbuHttpContent) RespJson(objects interface{}) {
	ctx.gin.JSON(http.StatusOK, objects)
}

func (ctx *AbuHttpContent) RespFile(savename string, filepath string) {
	ctx.gin.Header("Content-Disposition", fmt.Sprintf("attachment;filename=%s.xls", savename))
	ctx.gin.File(filepath)
}

func (ctx *AbuHttpContent) RespErr(err error, errcode *int) bool {
	(*errcode)--
	if err != nil {
		resp := new(HttpResponse)
		ctx.Put("errcode", errcode)
		ctx.Put("errmsg", err.Error())
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
	}
	return err != nil
}

func (ctx *AbuHttpContent) RespProcedureErr(err *map[string]interface{}) bool {
	if err != nil && (*err)["errcode"] != nil {
		resp := new(HttpResponse)
		ctx.Put("errcode", GetInt64FromInterface((*err)["errcode"]))
		ctx.Put("errmsg", GetStringFromInterface((*err)["errmsg"]))
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
		return true
	}
	return false
}

func (ctx *AbuHttpContent) RespDbErr(dberr *AbuDbError) bool {
	if dberr != nil && dberr.ErrCode > 0 && len(dberr.ErrMsg) > 0 {
		resp := new(HttpResponse)
		ctx.Put("errcode", dberr.ErrCode)
		ctx.Put("errmsg", dberr.ErrMsg)
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
		return true
	}
	return false
}

func (ctx *AbuHttpContent) RespErrString(err bool, errcode *int, errmsg string) bool {
	(*errcode)--
	if err {
		resp := new(HttpResponse)
		ctx.Put("errcode", errcode)
		ctx.Put("errmsg", errmsg)
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
	}
	return err
}

func (ctx *AbuHttpContent) RespErrCode(err error, errcode int) bool {
	if err != nil {
		resp := new(HttpResponse)
		ctx.Put("errcode", errcode)
		ctx.Put("errmsg", err.Error())
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
	}
	return err != nil
}

func (ctx *AbuHttpContent) RespErrCodeString(err bool, errcode int, errmsg string) bool {
	if err {
		resp := new(HttpResponse)
		ctx.Put("errcode", errcode)
		ctx.Put("errmsg", errmsg)
		resp.Code = HTTP_RESPONSE_CODE_ERROR
		resp.Msg = HTTP_RESPONSE_CODE_ERROR_MESSAGE
		resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
		ctx.gin.JSON(http.StatusOK, resp)
	}
	return err
}

func (ctx *AbuHttpContent) RespNoAuth(errcode int, errmsg string) {
	resp := new(HttpResponse)
	ctx.Put("errcode", errcode)
	ctx.Put("errmsg", errmsg)
	resp.Code = HTTP_RESPONSE_CODE_NOAUTH
	resp.Msg = HTTP_RESPONSE_CODE_NOAUTH_MESSAGE
	resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
	ctx.gin.JSON(http.StatusOK, resp)
}

func (ctx *AbuHttpContent) FromFile(name string) (multipart.File, *multipart.FileHeader, error) {
	return ctx.gin.Request.FormFile(name)
}

func (ctx *AbuHttpContent) SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	return ctx.gin.SaveUploadedFile(file, dst)
}

// GetRawBody 获取原始请求体
func (ctx *AbuHttpContent) GetRawBody() []byte {
	// 获取请求体
	var bodyBytes []byte
	if ctx.gin.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(ctx.gin.Request.Body)
		// 重新设置请求体，因为读取后会消耗
		ctx.gin.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}
	return bodyBytes
}

// SetRawBody 设置原始请求体
func (ctx *AbuHttpContent) SetRawBody(body []byte) {
	ctx.gin.Request.Body = io.NopCloser(bytes.NewBuffer(body))
}

// RespJsonWithCode 返回自定义状态码的JSON响应
func (ctx *AbuHttpContent) RespJsonWithCode(statusCode int, message string, data interface{}) {
	resp := new(HttpResponse)
	resp.Code = HTTP_RESPONSE_CODE_ERROR
	resp.Msg = message
	if data != nil {
		ctx.Put("", data)
	}
	resp.Data = ctx.gin.Keys[HTTP_SAVE_DATA_KEY]
	ctx.gin.JSON(statusCode, resp)
}

func (c *AbuHttp) Init(cfgkey string) {
	port := get_config_int(cfgkey, 0)
	c.gin = gin.New()
	//去除307情况
	c.gin.RedirectTrailingSlash = false
	c.gin.Use(abuhttpcors(), gin.Recovery())
	tokenhost := viper.GetString("server.token.host")
	if len(tokenhost) > 0 {
		c.tokenrefix = fmt.Sprint(get_config_string("server.project", ""), ":", get_config_string("server.module", ""), ":token")
		c.token = new(AbuRedis)
		c.tokenlifetime = get_config_int("server.token.lifetime", 0)
		c.token.Init("server.token")
	}
	go func() {
		bind := fmt.Sprint("0.0.0.0:", port)
		c.gin.Run(bind)
	}()
	logs.Debug("http listen:", port)
}

func (c *AbuHttp) Get(path string, handlers ...AbuHttpHandler) {
	c.gin.GET(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		if c.token == nil {
			ctx.RespNoAuth(-1, "未配置token redis")
			return
		}
		tokenstr := gc.GetHeader("x-token")
		if len(tokenstr) == 0 {
			ctx.RespNoAuth(1, "请在header填写:x-token")
			return
		}
		rediskey := fmt.Sprint(c.tokenrefix, ":", tokenstr)
		tokendata := c.token.Get(rediskey)
		if tokendata == nil {
			ctx.RespNoAuth(2, "未登录或登录已过期")
			return
		}
		c.token.Expire(rediskey, c.tokenlifetime)
		ctx.TokenData = string(tokendata.([]uint8))
		ctx.Token = tokenstr
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) GetNoAuth(path string, handlers ...AbuHttpHandler) {
	c.gin.GET(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) Post(path string, handlers ...AbuHttpHandler) {
	c.gin.POST(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		if c.token == nil {
			ctx.RespNoAuth(-1, "未配置token redis")
			return
		}
		tokenstr := gc.GetHeader("x-token")

		rediskey := fmt.Sprint(c.tokenrefix, ":", tokenstr)
		tokendata := c.token.Get(rediskey)
		if len(tokenstr) == 0 || tokendata == nil {
			if ctx.Gin().Request.URL.Path == "/api/third/og_login" {

				// 如果是/api/third/og_login 且游戏id是
				// GameId=29 测试服https://h5.tradingcs1.com       正式服https://h5.trading658.com
				// GameId=35 测试服https://h5.updowncs1.com        正式服https://h5.updown658.com
				bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
				errcode := 0
				if err != nil {
					ctx.RespErrString(true, &errcode, "进入失败")
					return
				}
				// 保存请求体
				ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				type RequestData struct {
					Lang   string
					GameId int64
				}
				reqdata := RequestData{}
				json.Unmarshal(bodyBytes, &reqdata)
				if reqdata.Lang == "zh_cn" {
					reqdata.Lang = "zh"
				} else {
					reqdata.Lang = "en"
				}
				if reqdata.GameId == 29 {
					if xgo.Env() == "prd" {
						ctx.Put("url", fmt.Sprintf("https://h5.trading658.com?language=%s", reqdata.Lang))
					} else {
						ctx.Put("url", fmt.Sprintf("https://h5.tradingcs1.com?language=%s", reqdata.Lang))
					}
					ctx.RespOK()
					return
				}
				if reqdata.GameId == 35 {
					if xgo.Env() == "prd" {
						ctx.Put("url", fmt.Sprintf("https://h5.updown658.com?language=%s", reqdata.Lang))
					} else {
						ctx.Put("url", fmt.Sprintf("https://h5.updowncs1.com?language=%s", reqdata.Lang))
					}
					ctx.RespOK()
					return
				}
			}
			ctx.RespNoAuth(2, "未登录或登录已过期")
			return
		}
		c.token.Expire(rediskey, c.tokenlifetime)
		ctx.TokenData = string(tokendata.([]uint8))
		ctx.Token = tokenstr
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) PostByNoAuth(path string, handlers ...AbuHttpHandler) {
	c.gin.POST(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		//if c.token == nil {
		//	ctx.RespNoAuth(-1, "未配置token redis")
		//	return
		//}
		//tokenstr := gc.GetHeader("x-token")
		//if len(tokenstr) == 0 {
		//	ctx.RespNoAuth(1, "请在header填写:x-token")
		//	return
		//}
		//rediskey := fmt.Sprint(c.tokenrefix, ":", tokenstr)
		//tokendata := c.token.Get(rediskey)
		//if tokendata == nil {
		//	ctx.RespNoAuth(2, "未登录或登录已过期")
		//	return
		//}
		//c.token.Expire(rediskey, c.tokenlifetime)
		//ctx.TokenData = string(tokendata.([]uint8))
		//ctx.Token = tokenstr
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

// / PostByNoAuthMayUserToken 登陆则有登陆信息，没有则无
func (c *AbuHttp) PostByNoAuthMayUserToken(path string, handlers ...AbuHttpHandler) {
	c.gin.POST(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		if c.token != nil {
			tokenstr := gc.GetHeader("x-token")
			if len(tokenstr) != 0 {
				rediskey := fmt.Sprint(c.tokenrefix, ":", tokenstr)
				tokendata := c.token.Get(rediskey)
				if tokendata != nil {
					c.token.Expire(rediskey, c.tokenlifetime)
					ctx.TokenData = string(tokendata.([]uint8))
					ctx.Token = tokenstr
				}
			}
		}
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) PostNoAuth(path string, handlers ...AbuHttpHandler) {
	c.gin.POST(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) PutNoAuth(path string, handlers ...AbuHttpHandler) {
	c.gin.PUT(path, func(gc *gin.Context) {
		ctx := &AbuHttpContent{gc, "", ""}
		for i := range handlers {
			handlers[i](ctx)
		}
	})
}

func (c *AbuHttp) SetToken(key string, data interface{}) {
	if c.token == nil {
		return
	}
	c.token.SetEx(fmt.Sprint(c.tokenrefix, ":", key), c.tokenlifetime, data)
}

func (c *AbuHttp) DelToken(key string) {
	if c.token == nil {
		return
	}
	c.token.Del(fmt.Sprint(c.tokenrefix, ":", key))
}

func (c *AbuHttp) RenewToken(key string) {
	if c.token == nil {
		return
	}
	c.token.Expire(fmt.Sprint(c.tokenrefix, ":", key), c.tokenlifetime)
}

func (c *AbuHttp) GetToken(key string) interface{} {
	if c.token == nil {
		return nil
	}
	return c.token.Get(fmt.Sprint(c.tokenrefix, ":", key))
}

func (c *AbuHttp) Gin() *gin.Engine {
	return c.gin
}

// ////////////////////////////////////////////////////////////////////////////////
// Redis
// ///////////////////////////////////////////////////////////////////////////////
type AbuRedisSubCallback func(string)
type AbuRedis struct {
	redispool          *redis.Pool
	pubconnection      *redis.PubSubConn
	host               string
	port               int
	db                 int
	password           string
	recving            bool
	subscribecallbacks map[string]AbuRedisSubCallback
	mu                 *sync.RWMutex
}

func (c *AbuRedis) Init(prefix string) {
	if c.redispool != nil {
		return
	}
	host := get_config_string(fmt.Sprint(prefix, ".host"), "")
	port := get_config_int(fmt.Sprint(prefix, ".port"), 0)
	db := get_config_int(fmt.Sprint(prefix, ".db"), -1)
	password := get_config_string(fmt.Sprint(prefix, ".password"), "")
	maxidle := get_config_int(fmt.Sprint(prefix, ".maxidle"), 0)
	maxactive := get_config_int(fmt.Sprint(prefix, ".maxactive"), 0)
	idletimeout := get_config_int(fmt.Sprint(prefix, ".idletimeout"), 0)
	c.redispool = &redis.Pool{
		MaxIdle:     maxidle,
		MaxActive:   maxactive,
		IdleTimeout: time.Duration(idletimeout) * time.Second,
		Wait:        true,
		Dial: func() (redis.Conn, error) {
			con, err := redis.Dial("tcp", fmt.Sprint(host, ":", port),
				redis.DialPassword(password),
				redis.DialDatabase(db),
			)
			if err != nil {
				logs.Error(err)
				panic(err)
			}
			return con, nil
		},
	}
	conn, err := redis.Dial("tcp", fmt.Sprint(host, ":", port),
		redis.DialPassword(password),
		redis.DialDatabase(db),
	)
	if err != nil {
		logs.Error(err)
		panic(err)
	}
	c.pubconnection = new(redis.PubSubConn)
	c.pubconnection.Conn = conn
	c.recving = false
	c.subscribecallbacks = make(map[string]AbuRedisSubCallback)
	c.mu = new(sync.RWMutex)
	logs.Debug("连接redis 成功:", host, port, db)
}

func (c *AbuRedis) getcallback(channel string) AbuRedisSubCallback {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.subscribecallbacks[channel]
}

func (c *AbuRedis) subscribe(channels ...string) {
	c.pubconnection.Subscribe(redis.Args{}.AddFlat(channels)...)
	if !c.recving {
		go func() {
			for {
				imsg := c.pubconnection.Receive()
				msgtype := reflect.TypeOf(imsg).Name()
				if msgtype == "Message" {
					msg := imsg.(redis.Message)
					callback := c.getcallback(msg.Channel)
					if callback != nil {
						callback(string(msg.Data))
					}
				}
			}
		}()
	}
}

func (c *AbuRedis) Subscribe(channel string, callback AbuRedisSubCallback) {
	c.mu.Lock()
	c.subscribecallbacks[channel] = callback
	c.mu.Unlock()
	c.subscribe(channel)
}

func (c *AbuRedis) Publish(k, v interface{}) error {
	conn := c.redispool.Get()
	defer conn.Close()
	output, _ := json.Marshal(&v)
	_, err := conn.Do("publish", k, output)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) Get(k string) interface{} {
	conn := c.redispool.Get()
	defer conn.Close()
	ret, err := conn.Do("get", k)
	if err != nil {
		logs.Error(err.Error())
		return nil
	}
	return ret
}

func (c *AbuRedis) Set(k string, v interface{}) error {
	conn := c.redispool.Get()
	defer conn.Close()
	output, _ := json.Marshal(&v)
	_, err := conn.Do("set", k, output)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) SetString(k string, v string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("set", k, v)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) SetEx(k string, to int, v interface{}) error {
	conn := c.redispool.Get()
	defer conn.Close()
	output, _ := json.Marshal(&v)
	_, err := conn.Do("setex", k, to, string(output))
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) SetStringEx(k string, to int, v string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("setex", k, to, v)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) Del(k string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("del", k)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) Expire(k string, to int) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("expire", k, to)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) Ttl(k string) int64 {
	conn := c.redispool.Get()
	defer conn.Close()
	result, err := conn.Do("ttl", k)
	if err != nil {
		logs.Error(err.Error())
		return -3
	}
	return result.(int64)
}

func (c *AbuRedis) HSet(k string, f string, v interface{}) error {
	conn := c.redispool.Get()
	defer conn.Close()
	output, _ := json.Marshal(&v)
	_, err := conn.Do("hset", k, f, string(output))
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) HMSet(key string, fs map[string]interface{}) bool {
	conn := c.redispool.Get()
	defer conn.Close()
	s := make([]interface{}, len(fs)*2+1)
	s[0] = key
	i := 0
	for key, value := range fs {
		s[1+i*2] = key
		s[1+i*2+1] = value
		i++
	}
	reply, err := conn.Do("hmset", s...)
	if err != nil {
		return false
	}
	return replyOK(reply)
}

func (c *AbuRedis) HSetString(k string, f string, v string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("hset", k, f, v)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) HGet(k string, f string) interface{} {
	conn := c.redispool.Get()
	defer conn.Close()
	ret, err := conn.Do("hget", k, f)
	if err != nil {
		logs.Error(err.Error())
		return nil
	}
	return ret
}

// HGetAll 获取全部hash值
func (c *AbuRedis) HGetAll(hashKey string) (map[string]string, error) {
	conn := c.redispool.Get()
	defer conn.Close()
	ret, err := conn.Do("HGetAll", hashKey)
	if err != nil {
		logs.Error(err.Error())
		return nil, err
	}
	return convMap(ret), nil
}

func (c *AbuRedis) RPushString(key string, value string) error {
	if key == "" || value == "" {
		return errors.New("key or value is empty")
	}
	conn := c.redispool.Get()
	defer conn.Close()
	data := make([]interface{}, 0, 2)
	data = append(data, key)
	data = append(data, value)
	_, err := conn.Do("rpush", data...)
	if err != nil {
		logs.Error("rpush data=", data, " err=", err.Error())
		return err
	}
	return nil
}

func convInt(reply interface{}) int64 {
	i, ok := reply.(int64)
	if ok {
		return i
	}
	return 0
}

func convMap(reply interface{}) map[string]string {
	mp := make(map[string]string)
	arrs := convArr(reply)

	var key string
	for index, item := range arrs {

		if index%2 == 0 {
			key = item

		} else {
			mp[key] = item
			key = ""
		}
	}
	return mp
}

func convArr(reply interface{}) []string {
	rs := []string{}
	arrs, ok := reply.([]interface{})
	if ok {
		for _, item := range arrs {
			s, ok := item.([]byte)
			if ok {
				rs = append(rs, string(s))
			} else {
				rs = append(rs, "")
			}
		}
	}
	return rs
}

func (c *AbuRedis) HDel(k string, f string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("hdel", k, f)
	if err != nil {
		logs.Error(err.Error())
		return nil
	}
	return nil
}

func (c *AbuRedis) HKeys(k string) []string {
	conn := c.redispool.Get()
	defer conn.Close()
	keys, err := conn.Do("hkeys", k)
	ikeys := keys.([]interface{})
	strkeys := []string{}
	if err != nil {
		logs.Error(err.Error())
		return strkeys
	}
	for i := 0; i < len(ikeys); i++ {
		strkeys = append(strkeys, string(ikeys[i].([]byte)))
	}
	return strkeys
}

func (c *AbuRedis) SAdd(k string, f string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("sadd", k, f)
	if err != nil {
		logs.Error(err.Error())
		return nil
	}
	return nil
}

func (c *AbuRedis) SAdds(k string, f ...string) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("sadd", k, f)
	if err != nil {
		logs.Error(err.Error())
		return nil
	}
	return nil
}

func (c *AbuRedis) SetNxString(k string, v string, e int) error {
	conn := c.redispool.Get()
	defer conn.Close()
	data, err := conn.Do("SETNX", k, v)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	idata := data.(int64)
	if idata == 0 {
		return errors.New("key已经存在")
	}
	if e > 0 {
		c.Expire(k, e)
	}
	return nil
}

// SCard 获取集合总数
func (c *AbuRedis) SCard(key string) int64 {
	conn := c.redispool.Get()
	defer conn.Close()
	reply, err := conn.Do("scard", key)
	if err != nil {
		return 0
	}
	return convInt(reply)
}

func (c *AbuRedis) SIsMember(key, member string) bool {
	conn := c.redispool.Get()
	defer conn.Close()

	reply, err := conn.Do("sismember", key, member)
	if err != nil {
		return false
	}
	return replyOK(reply)
}

func (c *AbuRedis) Redispool() *redis.Pool {
	return c.redispool
}

func (c *AbuRedis) HIncrBy(key, field string, incr int64) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("hincrby", key, field, incr)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) HIncrByFloat(key, field string, incr float64) error {
	conn := c.redispool.Get()
	defer conn.Close()
	_, err := conn.Do("hincrbyfloat", key, field, incr)
	if err != nil {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func (c *AbuRedis) Exists(key string) bool {
	conn := c.redispool.Get()
	defer conn.Close()
	reply, err := conn.Do("EXISTS", key)
	if err != nil {
		logs.Error(err.Error())
		return false
	}
	return replyOK(reply)
}

func replyOK(reply interface{}) bool {
	i, ok := reply.(int64)
	if ok {
		return i >= 1
	}
	s, ok := reply.(string)
	if ok {
		return strings.ToUpper(s) == "OK"
	}
	return false
}

// ////////////////////////////////////////////////////////////////////////////////
// db
// ///////////////////////////////////////////////////////////////////////////////
type AbuDb struct {
	user            string
	password        string
	host            string
	port            int
	connmaxlifetime int
	database        string
	db              *gorm.DB
	daodb           *daogorm.DB
	connmaxidletime int
	connmaxidle     int
	connmaxopen     int
	logmode         bool
}

func (c *AbuDb) Init(prefix string) {
	c.user = get_config_string(fmt.Sprint(prefix, ".user"), "")
	c.password = get_config_string(fmt.Sprint(prefix, ".password"), "")
	c.host = get_config_string(fmt.Sprint(prefix, ".host"), "")
	c.database = get_config_string(fmt.Sprint(prefix, ".database"), "")
	c.port = get_config_int(fmt.Sprint(prefix, ".port"), 0)
	c.connmaxlifetime = get_config_int(fmt.Sprint(prefix, ".connmaxlifetime"), 0)
	c.connmaxidletime = get_config_int(fmt.Sprint(prefix, ".connmaxidletime"), 0)
	c.connmaxidle = get_config_int(fmt.Sprint(prefix, ".connmaxidle"), 0)
	c.connmaxopen = get_config_int(fmt.Sprint(prefix, ".connmaxopen"), 0)
	str := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s", c.user, c.password, c.host, c.port, c.database)
	daostr := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&loc=%s", c.user, c.password, c.host, c.port, c.database, "Asia%2FShanghai")
	db, err := gorm.Open("mysql", str)
	if err != nil {
		logs.Error(err)
		panic(err)
	}
	db.DB().SetMaxIdleConns(c.connmaxidle)
	db.DB().SetMaxOpenConns(c.connmaxopen)
	db.DB().SetConnMaxIdleTime(time.Second * time.Duration(c.connmaxidletime))
	db.DB().SetConnMaxLifetime(time.Second * time.Duration(c.connmaxlifetime))
	c.db = db
	c.daodb, _ = daogorm.Open(daomysql.Open(daostr))
	rdaodb, _ := c.daodb.DB()
	rdaodb.SetMaxIdleConns(c.connmaxidle)
	rdaodb.SetMaxOpenConns(c.connmaxopen)
	rdaodb.SetConnMaxIdleTime(time.Second * time.Duration(c.connmaxidletime))
	rdaodb.SetConnMaxLifetime(time.Second * time.Duration(c.connmaxlifetime))
	c.logmode = viper.GetBool(fmt.Sprint(prefix, ".logmode"))
	db.LogMode(c.logmode)
	if c.logmode {
		logger.Default.LogMode(logger.Info)
	} else {
		logger.Default.LogMode(logger.Silent)
	}

	logs.Debug("连接数据库成功:", c.host, c.port, c.database)
}

func (c *AbuDb) Conn() *sql.DB {
	return c.db.DB()
}

func (c *AbuDb) Gorm() *gorm.DB {
	return c.db
}

func (c *AbuDb) GormDao() *daogorm.DB {
	return c.daodb
}

func (c *AbuDb) QueryNoResult(sqlstr string, args ...interface{}) error {
	result, err := c.db.DB().Query(sqlstr, args...)
	if err != nil {
		logs.Error(err)
	} else {
		result.Close()
	}
	return err
}

func (c *AbuDb) QueryScan(sqlstr string, params []interface{}, args ...interface{}) (error, bool) {
	result, err := c.db.DB().Query(sqlstr, params...)
	if err != nil {
		logs.Error(err)
		return err, false
	}
	if !result.Next() {
		return nil, false
	}
	result.Scan(args...)
	result.Close()
	return nil, true
}

func (c *AbuDb) AddWhere(sql *string, params *[]interface{}, operator string, field string, fieldoperator string, value interface{}, invalid_value interface{}) {
	if value == invalid_value {
		return
	}
	if len(*sql) > 0 {
		(*sql) += operator
	}
	(*sql) += " "
	(*sql) += field
	(*sql) += " "
	(*sql) += fieldoperator
	(*sql) += " ? "
	*params = append(*params, value)
}

func (c *AbuDb) Table(tablename string) *AbuDbTable {
	dbtable := AbuDbTable{tablename: tablename, selectstr: "*", db: c}
	return &dbtable
}

func (c *AbuDb) CallProcedure(procname string, args ...interface{}) (*map[string]interface{}, error) {
	sql := ""
	for i := 0; i < len(args); i++ {
		sql += "?,"
	}
	if len(sql) > 0 {
		sql = strings.TrimRight(sql, ",")
	}
	sql = fmt.Sprintf("call %s(%s)", procname, sql)

	dbresult, err := c.db.DB().Query(sql, args...)
	if err != nil {
		return nil, err
	}
	if dbresult.Next() {
		data := make(map[string]interface{})
		fields, _ := dbresult.Columns()
		scans := make([]interface{}, len(fields))
		for i := range scans {
			scans[i] = &scans[i]
		}
		err := dbresult.Scan(scans...)
		if err != nil {
			return nil, err
		}
		ct, _ := dbresult.ColumnTypes()
		for i := range fields {
			if scans[i] != nil {
				typename := ct[i].DatabaseTypeName()
				if typename == "INT" || typename == "TINYINT" || typename == "SMALLINT" || typename == "BIGINT" ||
					typename == "UNSIGNED INT" || typename == "UNSIGNED TINYINT" || typename == "UNSIGNED SMALLINT" || typename == "UNSIGNED BIGINT" ||
					typename == "UNSIGNED" {
					if reflect.TypeOf(scans[i]).Name() == "" {
						v, _ := strconv.ParseInt(string(scans[i].([]uint8)), 10, 64)
						data[fields[i]] = v
					} else {
						data[fields[i]] = scans[i]
					}
				} else if typename == "DOUBLE" || typename == "DECIMAL" {
					if reflect.TypeOf(scans[i]).Name() == "" {
						v, _ := strconv.ParseFloat(string(scans[i].([]uint8)), 64)
						data[fields[i]] = v
					} else {
						data[fields[i]] = scans[i]
					}
				} else {
					data[fields[i]] = string(scans[i].([]uint8))
				}
			} else {
				data[fields[i]] = nil
			}
		}
		dbresult.Close()
		return &data, nil
	}
	dbresult.Close()
	return nil, nil
}

func (c *AbuDb) Query(sql string, params []interface{}) (*[]map[string]interface{}, error) {
	dbresult, err := c.db.DB().Query(sql, params...)
	if err != nil {
		logs.Error(sql, params, err)
		return nil, err
	}
	abutable := AbuDbTable{}
	data := []map[string]interface{}{}
	for dbresult.Next() {
		data = append(data, *abutable.getone(dbresult))
	}
	dbresult.Close()
	return &data, nil
}

type AbuDbWhere struct {
	Data   map[string]interface{}
	length int
}

func (c *AbuDbWhere) Add(opt string, field string, operator string, val interface{}, invalidval interface{}) *AbuDbWhere {
	if c.Data == nil {
		c.Data = make(map[string]interface{})
	}
	if val != invalidval {
		if c.length == 0 {
			opt = ""
		}
		c.Data[fmt.Sprintf("%s %s@%d@%s", opt, field, c.length, operator)] = val
		c.length++
	}
	return c
}

func (c *AbuDbWhere) Sql() (string, []interface{}) {
	wstr := ""
	wv := []interface{}{}
	type FieldValue struct {
		Sort  int64
		Field string
		Value interface{}
		Opt   string
	}
	order := []FieldValue{}
	for k, v := range c.Data {
		ks := strings.Split(k, "@")
		opt := "="
		if len(ks) == 3 {
			opt = ks[2]
		}
		if len(ks) == 2 || len(ks) == 3 {
			sort, _ := strconv.ParseInt(ks[1], 10, 32)
			order = append(order, FieldValue{Sort: sort, Field: ks[0], Value: v, Opt: opt})
		} else if len(ks) == 1 {
			order = append(order, FieldValue{Sort: 1000000, Field: k, Value: nil, Opt: opt})
		}
	}
	sort.Slice(order, func(i, j int) bool {
		return order[i].Sort < order[j].Sort
	})
	for _, v := range order {
		if v.Value != nil {
			if strings.ToLower(v.Opt) != "in" {
				wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
				wv = append(wv, v.Value)
			} else {
				wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
			}
		} else {
			wstr += v.Field
		}
	}
	return wstr, wv
}

func (c *AbuDbWhere) End(field string) *AbuDbWhere {
	if c.Data == nil {
		c.Data = make(map[string]interface{})
	}
	c.Data[field] = 0
	return c
}

type AbuDbTable struct {
	db        *AbuDb
	dbconn    *sql.DB
	opttype   int
	tablename string
	where     map[string]interface{}
	selectstr string
	orderby   string
	limit     int
	join      string
	update    map[string]interface{}
	insert    map[string]interface{}
	pagekey   string
	pageorder string
}

func (c *AbuDbTable) Conn(db *sql.DB) *AbuDbTable {
	c.dbconn = db
	return c
}

func (c *AbuDbTable) TableName(TableName string) *AbuDbTable {
	c.tablename = TableName
	return c
}

func (c *AbuDbTable) Select(SelectStr string) *AbuDbTable {
	c.selectstr = SelectStr
	return c
}

func (c *AbuDbTable) Where(where AbuDbWhere) *AbuDbTable {
	c.where = where.Data
	return c
}

func (c *AbuDbTable) WhereRaw(sql string) *AbuDbTable {
	// sql := fmt.Sprintf("JSON_CONTAINS(%s, '%d')", field, value)
	c.where = map[string]interface{}{sql: ""}
	return c
}

func (c *AbuDbTable) OrderBy(orderby string) *AbuDbTable {
	c.orderby = orderby
	return c
}

func (c *AbuDbTable) Limit(limit int) *AbuDbTable {
	c.limit = limit
	return c
}

func (c *AbuDbTable) Join(join string) *AbuDbTable {
	c.join = join
	return c
}

func (c *AbuDbTable) getone(rows *sql.Rows) *map[string]interface{} {
	data := make(map[string]interface{})
	fields, _ := rows.Columns()
	scans := make([]interface{}, len(fields))
	for i := range scans {
		scans[i] = &scans[i]
	}
	err := rows.Scan(scans...)
	if err != nil {
		logs.Error(err)
		return nil
	}
	ct, _ := rows.ColumnTypes()
	for i := range fields {
		if scans[i] != nil {
			typename := ct[i].DatabaseTypeName()
			if typename == "INT" || typename == "TINYINT" || typename == "SMALLINT" || typename == "BIGINT" ||
				typename == "UNSIGNED INT" || typename == "UNSIGNED TINYINT" || typename == "UNSIGNED SMALLINT" || typename == "UNSIGNED BIGINT" ||
				typename == "UNSIGNED" {
				if reflect.TypeOf(scans[i]).Name() == "" {
					v, _ := strconv.ParseInt(string(scans[i].([]uint8)), 10, 64)
					data[fields[i]] = v
				} else {
					data[fields[i]] = scans[i]
				}
			} else if typename == "DOUBLE" || typename == "DECIMAL" {
				if reflect.TypeOf(scans[i]).Name() == "" {
					v, _ := strconv.ParseFloat(string(scans[i].([]uint8)), 64)
					data[fields[i]] = v
				} else {
					data[fields[i]] = scans[i]
				}
			} else {
				data[fields[i]] = string(scans[i].([]uint8))
			}
		} else {
			data[fields[i]] = nil
		}
	}
	return &data
}

func (c *AbuDbTable) get_select_sql() (string, []interface{}) {
	sql := ""
	wstr := ""
	wv := []interface{}{}
	type FieldValue struct {
		Sort  int64
		Field string
		Value interface{}
		Opt   string
	}
	order := []FieldValue{}
	for k, v := range c.where {
		ks := strings.Split(k, "@")
		opt := "="
		if len(ks) == 3 {
			opt = ks[2]
		}
		if len(ks) == 2 || len(ks) == 3 {
			sort, _ := strconv.ParseInt(ks[1], 10, 32)
			order = append(order, FieldValue{Sort: sort, Field: ks[0], Value: v, Opt: opt})
		} else if len(ks) == 1 {
			order = append(order, FieldValue{Sort: 1000000, Field: k, Value: nil, Opt: opt})
		}
	}
	sort.Slice(order, func(i, j int) bool {
		return order[i].Sort < order[j].Sort
	})
	for _, v := range order {
		if v.Value != nil {
			if strings.ToLower(v.Opt) != "in" {
				wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
				wv = append(wv, v.Value)
			} else {
				wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
			}
		} else {
			wstr += v.Field
		}
	}
	if len(wstr) > 0 {
		sql = fmt.Sprintf("SELECT %s FROM %s %s WHERE %s ", c.selectstr, c.tablename, c.join, wstr)
	} else {
		sql = fmt.Sprintf("SELECT %s FROM %s %s", c.selectstr, c.tablename, c.join)
	}
	if len(c.orderby) > 0 {
		sql += "order by "
		sql += c.orderby
		sql += " "
	}
	if c.limit > 0 {
		sql += fmt.Sprintf("limit %d ", c.limit)
	}
	return sql, wv
}

func (c *AbuDbTable) GetOne() (*map[string]interface{}, error) {
	sql, wv := c.get_select_sql()
	sql += " limit 1"
	conn := c.dbconn
	if conn == nil {
		conn = c.db.Conn()
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := conn.Query(sql, wv...)
	if err != nil {
		logs.Error(sql, wv, err)
		return nil, err
	}
	if dbresult.Next() {
		one := c.getone(dbresult)
		dbresult.Close()
		return one, nil
	}
	dbresult.Close()
	return nil, nil
}

func (c *AbuDbTable) GetList() (*[]map[string]interface{}, error) {
	sql, wv := c.get_select_sql()
	conn := c.dbconn
	if conn == nil {
		conn = c.db.Conn()
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := conn.Query(sql, wv...)
	if err != nil {
		logs.Error(sql, wv, err)
		return nil, err
	}
	data := []map[string]interface{}{}
	for dbresult.Next() {
		data = append(data, *c.getone(dbresult))
	}
	dbresult.Close()
	return &data, nil
}

func (c *AbuDbTable) get_update_sql() (string, []interface{}) {
	sql := ""
	ustr := ""
	uv := []interface{}{}
	for k, v := range c.update {
		ustr += fmt.Sprintf(" %s = ?,", k)
		uv = append(uv, v)
	}
	if len(ustr) > 0 {
		ustr = strings.TrimRight(ustr, ",")
	}
	ustr += " "
	wstr := ""
	type FieldValue struct {
		Sort  int64
		Field string
		Value interface{}
		Opt   string
	}
	order := []FieldValue{}
	for k, v := range c.where {
		ks := strings.Split(k, "@")
		opt := "="
		if len(ks) == 3 {
			opt = ks[2]
		}
		if len(ks) == 2 || len(ks) == 3 {
			sort, _ := strconv.ParseInt(ks[1], 10, 32)
			order = append(order, FieldValue{Sort: sort, Field: ks[0], Value: v, Opt: opt})
		} else if len(ks) == 1 {
			order = append(order, FieldValue{Sort: 1000000, Field: k, Value: nil, Opt: opt})
		}
	}
	sort.Slice(order, func(i, j int) bool {
		return order[i].Sort < order[j].Sort
	})
	for _, v := range order {
		if v.Value != nil {
			if strings.ToLower(v.Opt) != "in" {
				wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
				uv = append(uv, v.Value)
			} else {
				wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
			}
		} else {
			wstr += v.Field
		}
	}
	if len(wstr) > 0 {
		sql = fmt.Sprintf("UPDATE %s SET%s WHERE %s ", c.tablename, ustr, wstr)
	} else {
		sql = fmt.Sprintf("UPDATE %s SET%s  ", c.tablename, ustr)
	}
	return sql, uv
}

func (c *AbuDbTable) Update(update map[string]interface{}) (int64, error) {
	c.update = update
	sql, wv := c.get_update_sql()
	conn := c.dbconn
	if conn == nil {
		conn = c.db.Conn()
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := conn.Exec(sql, wv...)
	if err != nil {
		logs.Error(sql, wv, err)
		return 0, err
	}
	return dbresult.RowsAffected()
}

func (c *AbuDbTable) get_insert_sql() (string, []interface{}) {
	sql := ""
	istr := ""
	ivstr := ""
	iv := []interface{}{}
	for k, v := range c.insert {
		istr += fmt.Sprintf("%s,", k)
		ivstr += "?,"
		iv = append(iv, v)
	}
	if len(istr) > 0 {
		istr = strings.TrimRight(istr, ",")
		ivstr = strings.TrimRight(ivstr, ",")
	}
	sql = fmt.Sprintf("INSERT INTO %s(%s) VALUES(%s)", c.tablename, istr, ivstr)
	return sql, iv
}

func (c *AbuDbTable) Insert(insert map[string]interface{}) (int64, error) {
	c.insert = insert
	sql, wv := c.get_insert_sql()
	conn := c.dbconn
	if conn == nil {
		conn = c.db.Conn()
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := conn.Exec(sql, wv...)
	if err != nil {
		logs.Error(sql, wv, err)
		return 0, err
	}
	return dbresult.LastInsertId()
}

func (c *AbuDbTable) get_replace_sql() (string, []interface{}) {
	sql := ""
	istr := ""
	ivstr := ""
	iv := []interface{}{}
	for k, v := range c.insert {
		istr += fmt.Sprintf("%s,", k)
		ivstr += "?,"
		iv = append(iv, v)
	}
	if len(istr) > 0 {
		istr = strings.TrimRight(istr, ",")
		ivstr = strings.TrimRight(ivstr, ",")
	}
	sql = fmt.Sprintf("REPLACE INTO %s(%s) VALUES(%s)", c.tablename, istr, ivstr)
	return sql, iv
}

func (c *AbuDbTable) Replace(insert map[string]interface{}) (int64, error) {
	c.insert = insert
	sql, wv := c.get_replace_sql()
	conn := c.dbconn
	if conn == nil {
		conn = c.db.Conn()
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := conn.Exec(sql, wv...)
	if err != nil {
		logs.Error(sql, wv, err)
		return 0, err
	}
	return dbresult.LastInsertId()
}

func (c *AbuDbTable) PageData(Page int, PageSize int) (int64, *[]map[string]interface{}) {
	if Page <= 0 {
		Page = 1
	}
	if PageSize <= 0 {
		PageSize = 20
	}
	sql := ""
	wstr := ""
	wv := []interface{}{}
	type FieldValue struct {
		Sort  int64
		Field string
		Value interface{}
		Opt   string
	}
	order := []FieldValue{}
	for k, v := range c.where {
		ks := strings.Split(k, "@")
		opt := "="
		if len(ks) == 3 {
			opt = ks[2]
		}
		if len(ks) == 2 || len(ks) == 3 {
			sort, _ := strconv.ParseInt(ks[1], 10, 32)
			order = append(order, FieldValue{Sort: sort, Field: ks[0], Value: v, Opt: opt})
		} else if len(ks) == 1 {
			order = append(order, FieldValue{Sort: 1000000, Field: k, Value: nil, Opt: opt})
		}
	}
	sort.Slice(order, func(i, j int) bool {
		return order[i].Sort < order[j].Sort
	})
	for _, v := range order {
		if v.Value != nil {
			if strings.ToLower(v.Opt) != "in" {
				wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
				wv = append(wv, v.Value)
			} else {
				wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
			}
		} else {
			wstr += v.Field
		}
	}
	c.orderby = strings.Trim(c.orderby, " ")
	orderbysplit := strings.Split(c.orderby, " ")
	//orderfield := strings.Trim(orderbysplit[0], " ")
	orderby := strings.Trim(orderbysplit[len(orderbysplit)-1], " ")
	orderby = strings.ToLower(orderby)
	if len(wstr) > 0 {
		sql = fmt.Sprintf("SELECT COUNT(*) AS Total FROM %s %s where %s", c.tablename, c.join, wstr)
	} else {
		sql = fmt.Sprintf("SELECT COUNT(*) AS Total FROM %s %s %s", c.tablename, c.join, wstr)
	}
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err := c.db.Conn().Query(sql, wv...)
	if err != nil {
		logs.Error(sql, err)
		return 0, &[]map[string]interface{}{}
	}
	dbresult.Next()
	var total int
	dbresult.Scan(&total)
	dbresult.Close()
	if total == 0 {
		return 0, &[]map[string]interface{}{}
	}
	//if len(wstr) > 0 {
	//	sql = fmt.Sprintf("SELECT %s AS MinValue FROM %s %s where %s order by %s limit %d,1", orderfield, c.tablename, c.join, wstr, c.orderby, (Page-1)*PageSize)
	//} else {
	//	sql = fmt.Sprintf("SELECT %s AS MinValue FROM %s %s %s order by %s limit %d,1", orderfield, c.tablename, c.join, wstr, c.orderby, (Page-1)*PageSize)
	//}
	//if c.db.logmode {
	//	logs.Debug(sql, wv...)
	//}
	//dbresult, err = c.db.Conn().Query(sql, wv...)
	//if err != nil {
	//	logs.Error(sql, err)
	//	return 0, &[]map[string]interface{}{}
	//}
	//if !dbresult.Next() {
	//	return int64(total), &[]map[string]interface{}{}
	//}
	//var minvalue int
	//dbresult.Scan(&minvalue)
	//dbresult.Close()
	//opt := ""
	//if orderby == "asc" {
	//	opt = ">="
	//}
	//if orderby == "desc" {
	//	opt = "<="
	//}
	if c.where == nil {
		c.where = make(map[string]interface{})
	}
	//如果orderby已经添加了表名称，则不需要再拼接表名称, 包含字符.则视为已经添加表名称
	//if strings.Contains(orderfield, ".") {
	//	c.where[fmt.Sprintf("%s@-1@%s", orderfield, opt)] = minvalue
	//} else {
	//	c.where[fmt.Sprintf("%s.%s@-1@%s", c.tablename, orderfield, opt)] = minvalue
	//}
	wstr = ""
	wv = []interface{}{}
	order = []FieldValue{}
	for k, v := range c.where {
		ks := strings.Split(k, "@")
		opt := "="
		if len(ks) == 3 {
			opt = ks[2]
		}
		if len(ks) == 2 || len(ks) == 3 {
			sort, _ := strconv.ParseInt(ks[1], 10, 32)
			order = append(order, FieldValue{Sort: sort, Field: ks[0], Value: v, Opt: opt})
		} else if len(ks) == 1 {
			order = append(order, FieldValue{Sort: 1000000, Field: k, Value: nil, Opt: opt})
		}
	}
	sort.Slice(order, func(i, j int) bool {
		return order[i].Sort < order[j].Sort
	})
	for _, v := range order {
		if v.Value != nil {
			if strings.ToLower(v.Opt) != "in" {
				wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
				wv = append(wv, v.Value)
			} else {
				wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
			}
		} else {
			wstr += v.Field
		}
		//if k == 1 {
		//	if v.Value != nil {
		//		if strings.ToLower(v.Opt) != "in" {
		//			wstr += fmt.Sprintf(" and %s %s ? ", v.Field, v.Opt)
		//			wv = append(wv, v.Value)
		//		} else {
		//			wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
		//		}
		//	} else {
		//		wstr += v.Field
		//	}
		//} else {
		//	if v.Value != nil {
		//		if strings.ToLower(v.Opt) != "in" {
		//			wstr += fmt.Sprintf(" %s %s ? ", v.Field, v.Opt)
		//			wv = append(wv, v.Value)
		//		} else {
		//			wstr += fmt.Sprintf(" %v %s %v ", v.Field, v.Opt, v.Value)
		//		}
		//	} else {
		//		wstr += v.Field
		//	}
		//}
	}
	if len(wstr) > 0 {
		sql = fmt.Sprintf("SELECT %s FROM %s %s WHERE %s  ", c.selectstr, c.tablename, c.join, wstr)
	} else {
		sql = fmt.Sprintf("SELECT %s FROM %s %s ", c.selectstr, c.tablename, c.join)
	}
	if len(c.orderby) > 0 {
		sql += fmt.Sprintf("order by %s", c.orderby)
		sql += " "
	}
	sql += fmt.Sprintf("limit %d,%d", (Page-1)*PageSize, PageSize)
	if c.db.logmode {
		logs.Debug(sql, wv...)
	}
	dbresult, err = c.db.Conn().Query(sql, wv...)
	if err != nil {
		logs.Error(sql, err)
		return 0, &[]map[string]interface{}{}
	}
	datas := []map[string]interface{}{}
	for dbresult.Next() {
		datas = append(datas, *c.getone(dbresult))
	}
	dbresult.Close()
	return int64(total), &datas
}

//////////////////////////////////////////////////////////////////////////////////
//websocket
/////////////////////////////////////////////////////////////////////////////////

type abumsgqueuestruct struct {
	MsgType  int //1链接进入 2链接关闭 3消息
	Id       int64
	Ws       *AbuWebsocket
	MsgData  *abumsgdata
	callback AbuWsCallback
}

var abuwsmsgqueue = make(chan abumsgqueuestruct, 10000)

type AbuWsCallback func(int64)
type AbuWsMsgCallback func(int64, string)
type AbuWebsocket struct {
	upgrader         websocket.Upgrader
	idx_conn         sync.Map
	conn_idx         sync.Map
	connect_callback AbuWsCallback
	close_callback   AbuWsCallback
	msgtype          sync.Map
	msg_callback     sync.Map
}

func (c *AbuWebsocket) Init(prefix string) {
	port := get_config_int(fmt.Sprint(prefix, ".port"), 0)
	c.upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	go func() {
		http.HandleFunc("/", c.home)
		bind := fmt.Sprint("0.0.0.0:", port)
		http.ListenAndServe(bind, nil)
	}()
	logs.Debug("websocket listen:", port)
}

type abumsgdata struct {
	MsgId string      `json:"msgid"`
	Data  interface{} `json:"data"`
}

func (c *AbuWebsocket) home(w http.ResponseWriter, r *http.Request) {
	conn, err := c.upgrader.Upgrade(w, r, nil)
	if err != nil {
		logs.Error(err)
		return
	}
	defer conn.Close()
	id := GetId()
	c.idx_conn.Store(id, conn)
	c.conn_idx.Store(conn, id)
	{
		mds := abumsgqueuestruct{1, id, c, nil, nil}
		abuwsmsgqueue <- mds
	}
	for {
		mt, message, err := conn.ReadMessage()
		c.msgtype.Store(id, mt)
		if err != nil {
			break
		}
		md := abumsgdata{}
		err = json.Unmarshal(message, &md)
		if err == nil {
			mds := abumsgqueuestruct{3, id, c, &md, nil}
			abuwsmsgqueue <- mds
		}
	}
	_, ccerr := c.idx_conn.Load(id)
	if ccerr {
		c.idx_conn.Delete(id)
		c.conn_idx.Delete(conn)
		{
			mds := abumsgqueuestruct{2, id, c, nil, nil}
			abuwsmsgqueue <- mds
		}
	}
}

func (c *AbuWebsocket) AddConnectCallback(callback AbuWsCallback) {
	c.connect_callback = callback
}

func (c *AbuWebsocket) AddMsgCallback(msgid string, callback AbuWsMsgCallback) {
	c.msg_callback.Store(msgid, callback)
}

func (c *AbuWebsocket) AddCloseCallback(callback AbuWsCallback) {
	c.close_callback = callback
}

func (c *AbuWebsocket) dispatch(msgtype int, id int64, data abumsgdata, ccb AbuWsCallback) {
	switch msgtype {
	case 3:
		callback, cbok := c.msg_callback.Load(data.MsgId)
		if cbok {
			cb := callback.(AbuWsMsgCallback)
			jdata, err := json.Marshal(data.Data)
			if err == nil {
				cb(id, string(jdata))
			}
		}
	case 1:
		if c.connect_callback == nil {
			return
		}
		c.connect_callback(id)
	case 2:
		if c.close_callback == nil {
			return
		}
		c.close_callback(id)
	case 4:
		ccb(id)
	}
}

func (c *AbuWebsocket) SendMsg(id int64, msgid string, data interface{}) {
	iconn, connok := c.idx_conn.Load(id)
	imt, mtok := c.msgtype.Load(id)
	if mtok && connok {
		conn := iconn.(*websocket.Conn)
		mt := imt.(int)
		msg := abumsgdata{msgid, data}
		msgbyte, jerr := json.Marshal(msg)
		if jerr == nil {
			werr := conn.WriteMessage(mt, msgbyte)
			if werr != nil {
			}
		}
	}
}

func (c *AbuWebsocket) Close(id int64) {
	iconn, connok := c.idx_conn.Load(id)
	if connok {
		conn := iconn.(*websocket.Conn)
		c.conn_idx.Delete(conn)
		c.idx_conn.Delete(id)
		c.msgtype.Delete(id)
		conn.Close()
	}
}

func (c *AbuWebsocket) Connect(host string, callback AbuWsCallback) {
	go func() {
		conn, _, err := websocket.DefaultDialer.Dial(host, nil)
		if err != nil {
			mds := abumsgqueuestruct{4, 0, c, nil, callback}
			abuwsmsgqueue <- mds
			return
		}
		defer conn.Close()
		id := GetId()
		c.idx_conn.Store(id, conn)
		c.conn_idx.Store(conn, id)
		{
			mds := abumsgqueuestruct{4, id, c, nil, callback}
			abuwsmsgqueue <- mds
		}
		for {
			mt, message, err := conn.ReadMessage()
			c.msgtype.Store(id, mt)
			if err != nil {
				break
			}
			md := abumsgdata{}
			err = json.Unmarshal(message, &md)
			if err == nil {
				mds := abumsgqueuestruct{3, id, c, &md, nil}
				abuwsmsgqueue <- mds
			}
		}
		_, ccerr := c.idx_conn.Load(id)
		if ccerr {
			c.idx_conn.Delete(id)
			c.conn_idx.Delete(conn)
			{
				mds := abumsgqueuestruct{2, id, c, nil, nil}
				abuwsmsgqueue <- mds
			}
		}
	}()
}

func RsaSign(data interface{}, privatekey string) string {
	privatekey = strings.Replace(privatekey, "-----BEGIN PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----END PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----BEGIN RSA PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----END RSA PRIVATE KEY-----", "", -1)
	t := reflect.TypeOf(data)
	v := reflect.ValueOf(data)
	keys := []string{}
	for i := 0; i < t.NumField(); i++ {
		fn := strings.ToLower(t.Field(i).Name)
		if fn != "sign" {
			keys = append(keys, t.Field(i).Name)
		}
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	var sb strings.Builder
	for i := 0; i < len(keys); i++ {
		switch sv := v.FieldByName(keys[i]).Interface().(type) {
		case string:
			sb.WriteString(sv)
		case int:
			sb.WriteString(fmt.Sprint(sv))
		case int8:
			sb.WriteString(fmt.Sprint(sv))
		case int16:
			sb.WriteString(fmt.Sprint(sv))
		case int32:
			sb.WriteString(fmt.Sprint(sv))
		case int64:
			sb.WriteString(fmt.Sprint(sv))
		case float32:
			sb.WriteString(fmt.Sprint(sv))
		case float64:
			sb.WriteString(fmt.Sprint(sv))
		}
	}
	privatekeybase64, errb := base64.StdEncoding.DecodeString(privatekey)
	if errb != nil {
		logs.Error(errb)
		return ""
	}
	privatekeyx509, errc := x509.ParsePKCS8PrivateKey([]byte(privatekeybase64))
	if errc != nil {
		logs.Error(errc)
		return ""
	}
	hashmd5 := md5.Sum([]byte(sb.String()))
	hashed := hashmd5[:]
	sign, errd := rsa.SignPKCS1v15(crand.Reader, privatekeyx509.(*rsa.PrivateKey), crypto.MD5, hashed)
	if errd != nil {
		logs.Error(errd)
		return ""
	}
	return base64.StdEncoding.EncodeToString(sign)
}

func RsaVerify(data interface{}, publickey string) bool {
	publickey = strings.Replace(publickey, "-----BEGIN PUBLIC KEY-----", "", -1)
	publickey = strings.Replace(publickey, "-----END PUBLIC KEY-----", "", -1)
	publickey = strings.Replace(publickey, "-----BEGIN RSA PUBLIC KEY-----", "", -1)
	publickey = strings.Replace(publickey, "-----END RSA PUBLIC KEY-----", "", -1)
	t := reflect.TypeOf(data)
	v := reflect.ValueOf(data)
	keys := []string{}
	for i := 0; i < t.NumField(); i++ {
		fn := strings.ToLower(t.Field(i).Name)
		if fn != "sign" {
			keys = append(keys, t.Field(i).Name)
		}
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	var sb strings.Builder
	for i := 0; i < len(keys); i++ {
		switch sv := v.FieldByName(keys[i]).Interface().(type) {
		case string:
			sb.WriteString(sv)
		case int:
			sb.WriteString(fmt.Sprint(sv))
		case int8:
			sb.WriteString(fmt.Sprint(sv))
		case int16:
			sb.WriteString(fmt.Sprint(sv))
		case int32:
			sb.WriteString(fmt.Sprint(sv))
		case int64:
			sb.WriteString(fmt.Sprint(sv))
		case float32:
			sb.WriteString(fmt.Sprint(sv))
		case float64:
			sb.WriteString(fmt.Sprint(sv))
		}
	}
	signedstr := fmt.Sprint(v.FieldByName("Sign"))
	publickeybase64, errb := base64.StdEncoding.DecodeString(publickey)
	if errb != nil {
		logs.Error(errb)
		return false
	}
	publickeyx509, errc := x509.ParsePKIXPublicKey([]byte(publickeybase64))
	if errc != nil {
		logs.Error(errc)
		return false
	}
	hash := md5.New()
	hash.Write([]byte(sb.String()))
	signdata, _ := base64.StdEncoding.DecodeString(signedstr)
	errd := rsa.VerifyPKCS1v15(publickeyx509.(*rsa.PublicKey), crypto.MD5, hash.Sum(nil), signdata)
	return errd == nil
}

func aesPKCS7Padding(ciphertext []byte, blocksize int) []byte {
	padding := blocksize - len(ciphertext)%blocksize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func aesPKCS7UnPadding(origData []byte) []byte {
	if len(origData) == 0 {
		return nil
	}
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func AesEncrypt(orig string, key string) string {
	origData := []byte(orig)
	k := []byte(key)
	block, erra := aes.NewCipher(k)
	if erra != nil {
		logs.Error(erra)
		return ""
	}
	blockSize := block.BlockSize()
	origData = aesPKCS7Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, k[:blockSize])
	cryted := make([]byte, len(origData))
	blockMode.CryptBlocks(cryted, origData)
	return base64.StdEncoding.EncodeToString(cryted)
}

func AesDecrypt(cryted string, key string) string {
	if len(cryted) == 0 {
		return ""
	}
	crytedByte, _ := base64.StdEncoding.DecodeString(cryted)
	k := []byte(key)
	block, err := aes.NewCipher(k)
	if err != nil {
		return ""
	}
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, k[:blockSize])
	orig := make([]byte, len(crytedByte))
	blockMode.CryptBlocks(orig, crytedByte)
	orig = aesPKCS7UnPadding(orig)
	if orig == nil {
		return ""
	}
	return string(orig)
}

type AbuRsaKey struct {
	Public  string
	Private string
}

type abuPKCS8Key struct {
	Version             int
	PrivateKeyAlgorithm []asn1.ObjectIdentifier
	PrivateKey          []byte
}

func abuMarshalPKCS8PrivateKey(key *rsa.PrivateKey) ([]byte, error) {
	var pkey abuPKCS8Key
	pkey.Version = 0
	pkey.PrivateKeyAlgorithm = make([]asn1.ObjectIdentifier, 1)
	pkey.PrivateKeyAlgorithm[0] = asn1.ObjectIdentifier{1, 2, 840, 113549, 1, 1, 1}
	pkey.PrivateKey = x509.MarshalPKCS1PrivateKey(key)
	return asn1.Marshal(pkey)
}

func NewRsaKey() *AbuRsaKey {
	key := &AbuRsaKey{}
	privateKey, _ := rsa.GenerateKey(crand.Reader, 2048)
	bytes, _ := abuMarshalPKCS8PrivateKey(privateKey)
	privateblock := &pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: bytes,
	}
	key.Private = string(pem.EncodeToMemory(privateblock))
	PublicKey := &privateKey.PublicKey
	pkixPublicKey, _ := x509.MarshalPKIXPublicKey(PublicKey)
	publicblock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pkixPublicKey,
	}
	key.Public = string(pem.EncodeToMemory(publicblock))
	return key
}

func abuGoogleRandStr(strSize int) string {
	dictionary := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var bytes = make([]byte, strSize)
	_, _ = crand.Read(bytes)
	for k, v := range bytes {
		bytes[k] = dictionary[v%byte(len(dictionary))]
	}
	return string(bytes)
}

func GetGoogleSecret() string {
	return strings.ToUpper(abuGoogleRandStr(32))
}

func abuOneTimePassword(key []byte, value []byte) uint32 {
	hmacSha1 := hmac.New(sha1.New, key)
	hmacSha1.Write(value)
	hash := hmacSha1.Sum(nil)
	offset := hash[len(hash)-1] & 0x0F
	hashParts := hash[offset : offset+4]
	hashParts[0] = hashParts[0] & 0x7F
	number := abuToUint32(hashParts)
	pwd := number % 1000000
	return pwd
}

func abuToUint32(bytes []byte) uint32 {
	return (uint32(bytes[0]) << 24) + (uint32(bytes[1]) << 16) +
		(uint32(bytes[2]) << 8) + uint32(bytes[3])
}

func abuToBytes(value int64) []byte {
	var result []byte
	mask := int64(0xFF)
	shifts := [8]uint16{56, 48, 40, 32, 24, 16, 8, 0}
	for _, shift := range shifts {
		result = append(result, byte((value>>shift)&mask))
	}
	return result
}

func GetGoogleCode(secret string) int32 {
	key, err := base32.StdEncoding.DecodeString(secret)
	if err != nil {
		logs.Error(err)
		return 0
	}
	epochSeconds := time.Now().Unix() + 0
	return int32(abuOneTimePassword(key, abuToBytes(epochSeconds/30)))
}

func VerifyGoogleCode(secret string, code string) bool {
	nowcode := GetGoogleCode(secret)
	if fmt.Sprint(nowcode) == code {
		return true
	}
	return false
}

func ReadAllText(path string) string {
	file, err := os.Open(path)
	if err != nil {
		logs.Error(err)
		return ""
	}
	bytes, err := ioutil.ReadAll(file)
	if err != nil {
		logs.Error(err)
		return ""
	}
	return string(bytes)
}

func UtcToLocalTime(timestr string) string {
	if len(timestr) == 0 {
		return ""
	}
	t, err := time.Parse(time.RFC3339, timestr)
	if err != nil {
		return ""
	}
	localTime := t.Local()
	return localTime.In(time.Local).Format("2006-01-02 15:04:05")
}

func LocalTimeToUtc(timestr string) string {
	if len(timestr) == 0 {
		return timestr
	}
	if len(timestr) == 10 {
		timestr = timestr + " 00:00:00"
	}
	t, _ := time.ParseInLocation(TimeLayout, timestr, time.Local)
	r := t.UTC().Format("2006-01-02T15:04:05Z")
	return r
}

func TimeStampToLocalTime(tvalue int64) string {
	if tvalue == 0 {
		return ""
	}
	tm := time.Unix(tvalue/1000, 0)
	tstr := tm.Format(TimeLayout)
	return tstr
}

// applySQLInjectionMiddleware  将 SQL注入防护中间件应用到所有处理器
func applySQLInjectionMiddleware(handlers ...AbuHttpHandler) []AbuHttpHandler {
	secureHandlers := make([]AbuHttpHandler, len(handlers))
	for i, handler := range handlers {
		secureHandlers[i] = SQLInjectionMiddleware(handler)
	}
	return secureHandlers
}

func TimeStampToLocalDate(tvalue int64) string {
	if tvalue == 0 {
		return ""
	}
	tm := time.Unix(tvalue/1000, 0)
	tstr := tm.Format(TimeLayout)
	return strings.Split(tstr, " ")[0]
}

func GetLocalTime() string {
	tm := time.Now()
	return tm.In(time.Local).Format(TimeLayout)
}

func GetLocalDate() string {
	tm := time.Now()
	return tm.In(time.Local).Format(DateLayout)
}

func LocalTimeToTimeStamp(timestr string) int64 {
	if timestr == "" {
		return 0
	}
	t, _ := time.ParseInLocation(TimeLayout, timestr, time.Local)
	return t.Local().Unix()
}

func LocalDateToTimeStamp(timestr string) int64 {
	t, _ := time.ParseInLocation(DateLayout, timestr, time.Local)
	return t.Local().Unix()
}

func GetStringFromInterface(v interface{}) string {
	if v == nil {
		return ""
	}
	switch v.(type) {
	case string:
		return v.(string)
	case int:
		return fmt.Sprint(v.(int))
	case int32:
		return fmt.Sprint(v.(int32))
	case int64:
		return fmt.Sprint(v.(int64))
	case float32:
		return fmt.Sprint(v.(float32))
	case float64:
		return fmt.Sprint(v.(float64))
	case []byte:
		return string(v.([]byte))
	}
	return ""
}

func GetInt64FromInterface(v interface{}) int64 {
	if v == nil {
		return 0
	}
	switch v.(type) {
	case string:
		i, err := strconv.ParseInt(v.(string), 10, 64)
		if err != nil {
			return 0
		}
		return i
	case int:
		return int64(v.(int))
	case int32:
		return int64(v.(int32))
	case int64:
		return v.(int64)
	case float32:
		return int64(v.(float32))
	case float64:
		return int64(v.(float64))
	}
	return 0
}

func GetFloat64FromInterface(v interface{}) float64 {
	if v == nil {
		return 0
	}
	switch v.(type) {
	case string:
		i, err := strconv.ParseFloat(v.(string), 64)
		if err != nil {
			return 0
		}
		return i
	case int:
		return float64(v.(int))
	case int32:
		return float64(v.(int32))
	case int64:
		return float64(v.(int64))
	case float32:
		return float64(v.(float32))
	case float64:
		return v.(float64)
	}
	return 0
}

func GetStartOfMonth(timestr string) time.Time {
	timestr = strings.Trim(timestr, " ")
	if len(timestr) == 10 {
		timestr = timestr + " 00:00:00"
	}
	loc, _ := time.LoadLocation("Local")
	d, _ := time.ParseInLocation(TimeLayout, timestr, loc)
	d = d.AddDate(0, 0, -d.Day()+1)
	return GetZeroTimeOfDay(d)
}

func GetEndOfMonth(timestr string) time.Time {
	return GetStartOfMonth(timestr).AddDate(0, 1, -1)
}

func GetZeroTimeOfDay(d time.Time) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
}

func GetWeekOfYear(timestr string) int {
	timestr = strings.Trim(timestr, " ")
	if len(timestr) == 10 {
		timestr = timestr + " 00:00:00"
	}
	loc, _ := time.LoadLocation("Local")
	tx, _ := time.ParseInLocation(TimeLayout, timestr, loc)
	tm1 := time.Date(tx.Year(), tx.Month(), tx.Day()+3-(int(tx.Weekday())+6)%7, 0, 0, 0, 0, time.UTC)
	week1 := time.Date(tm1.Year(), 1, 4, 0, 0, 0, 0, time.UTC)
	abs := 2 + math.Round((float64((tm1.Unix()-week1.Unix())/86400)-float64(3)+float64((int(week1.Weekday())+6)%7))/7)
	return int(abs)
}

func GetStartOfWeek(timestr string) time.Time {
	timestr = strings.Trim(timestr, " ")
	if len(timestr) == 10 {
		timestr = timestr + " 00:00:00"
	}
	loc, _ := time.LoadLocation("Local")
	now, _ := time.ParseInLocation(TimeLayout, timestr, loc)
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}
	weekStartDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
	return weekStartDate
}

func GetEndOfWeek(timestr string) time.Time {
	TimeMonday := GetStartOfWeek(timestr)
	lastWeekMonday := TimeMonday.AddDate(0, 0, 6)
	return lastWeekMonday
}

func pcks5padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padText...)
}

func DesCbcEncrypt(data, key []byte, iv []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}
	bs := block.BlockSize()
	data = pcks5padding(data, bs) //填充字符
	blockMode := cipher.NewCBCEncrypter(block, iv)
	out := make([]byte, len(data))
	blockMode.CryptBlocks(out, data)
	return out, nil
}

func Base64Encode(src []byte) []byte {
	return []byte(base64.StdEncoding.EncodeToString(src))
}

func InterfaceToInt(v interface{}) int32 {
	if v == nil {
		return 0
	}
	switch v.(type) {
	case string:
		i, err := strconv.ParseInt(v.(string), 10, 64)
		if err != nil {
			return 0
		}
		return int32(i)
	case int:
		return int32(v.(int))
	case int32:
		return int32(v.(int32))
	case int64:
		return int32(v.(int64))
	case float32:
		return int32(v.(float32))
	case float64:
		return int32(v.(float64))
	}
	return 0
}

type DBError struct {
	ErrCode int
	ErrMsg  string
}

func GetDbError(data *map[string]interface{}) *DBError {
	err := DBError{}
	code, codeok := (*data)["errcode"]
	if !codeok {
		return &err
	}
	err.ErrCode = int(InterfaceToInt(code))
	msg, msgok := (*data)["errmsg"]
	if msgok {
		err.ErrMsg = InterfaceToString(msg)
	}
	return &err
}

func InterfaceToString(v interface{}) string {
	if v == nil {
		return ""
	}
	switch v.(type) {
	case string:
		return v.(string)
	case int:
		return fmt.Sprint(v.(int))
	case int32:
		return fmt.Sprint(v.(int32))
	case int64:
		return fmt.Sprint(v.(int64))
	case float32:
		return fmt.Sprint(v.(float32))
	case float64:
		return fmt.Sprint(v.(float64))
	}
	return ""
}

func ObjectToMap(obj interface{}) *map[string]interface{} {
	bytes, err := json.Marshal(obj)
	if err != nil {
		logs.Error("ObjectToMap:", err)
		return nil
	}
	data := map[string]interface{}{}
	json.Unmarshal(bytes, &data)
	return &data
}

func InterfaceToFloat64(v interface{}) float64 {
	if v == nil {
		return 0
	}
	switch v.(type) {
	case string:
		i, err := strconv.ParseFloat(v.(string), 64)
		if err != nil {
			return 0
		}
		return i
	case int:
		return float64(v.(int))
	case int32:
		return float64(v.(int32))
	case int64:
		return float64(v.(int64))
	case float32:
		return float64(v.(float32))
	case float64:
		return v.(float64)
	}
	return 0
}

func ContainsLower(str string) bool {
	for _, char := range str {
		if unicode.IsLower(char) {
			return true
		}
	}
	return false
}

func ContainsUpper(str string) bool {
	for _, char := range str {
		if unicode.IsUpper(char) {
			return true
		}
	}
	return false
}

func ContainsDigit(str string) bool {
	for _, char := range str {
		if unicode.IsDigit(char) {
			return true
		}
	}
	return false
}

func LogInfo(str string, data interface{}) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		logs.Debug(str, "Error marshalling data to JSON: %v", err)
	}
	logs.Debug(str, string(jsonData))
}

func initCarbonTime() {
	carbon.SetDefault(carbon.Default{
		Layout:       carbon.DateTimeLayout,
		Timezone:     carbon.Shanghai, // 同数据库
		WeekStartsAt: carbon.Monday,
		Locale:       "zh-CN", // i18n
	})
}
