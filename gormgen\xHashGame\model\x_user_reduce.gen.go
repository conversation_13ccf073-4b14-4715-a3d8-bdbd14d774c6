// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserReduce = "x_user_reduce"

// XUserReduce 玩家扣减流水
type XUserReduce struct {
	UserID            int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	ReduceLiuShui     float64   `gorm:"column:ReduceLiuShui;default:0.000000;comment:需要扣减流水" json:"ReduceLiuShui"`           // 需要扣减流水
	RealReduceLiuShui float64   `gorm:"column:RealReduceLiuShui;default:0.000000;comment:真实扣减流水" json:"RealReduceLiuShui"`   // 真实扣减流水
	CreateTime        time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime        time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XUserReduce's table name
func (*XUserReduce) TableName() string {
	return TableNameXUserReduce
}
