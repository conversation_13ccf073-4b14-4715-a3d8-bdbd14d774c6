// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentIndependenceFenchengHistory = "x_agent_independence_fencheng_history"

// XAgentIndependenceFenchengHistory mapped from table <x_agent_independence_fencheng_history>
type XAgentIndependenceFenchengHistory struct {
	ID           int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID       int32     `gorm:"column:UserId" json:"UserId"`
	AgentID      int32     `gorm:"column:AgentId" json:"AgentId"`
	Reason       int32     `gorm:"column:Reason;comment:1注册2上级设置3顶级方案调整" json:"Reason"` // 1注册2上级设置3顶级方案调整
	ChangeBefore string    `gorm:"column:ChangeBefore" json:"ChangeBefore"`
	ChangeAfter  string    `gorm:"column:ChangeAfter" json:"ChangeAfter"`
	Systime      time.Time `gorm:"column:Systime;default:CURRENT_TIMESTAMP" json:"Systime"`
}

// TableName XAgentIndependenceFenchengHistory's table name
func (*XAgentIndependenceFenchengHistory) TableName() string {
	return TableNameXAgentIndependenceFenchengHistory
}
