package model

type PasswordTokenReq struct {
	ResetType       int    `json:"ResetType" form:"ResetType" validate:"required"`
	Account         string `json:"Account" form:"Account" validate:"required"` //账号
	Email           string `json:"Email" form:"Email"`                         //邮箱号
	Phone           string `json:"Phone" form:"Phone"`                         //手机号
	TransferAddress string `json:"TransferAddress" form:"TransferAddress"`     //转账地址
	TransferMoney   string `json:"TransferMoney" form:"TransferMoney"`         //转账数值
}

type PasswordTokenRes struct {
	Token string `json:"Token" form:"Token"` //如果是转账地址获取token
}

type ResetPasswordReq struct {
	Token          string `json:"Token"  form:"Token" validate:"required"`
	Account        string `json:"Account" form:"Account" validate:"required"`
	Password       string `json:"Password" form:"Password" validate:"required"`
	RepeatPassword string `json:"RepeatPassword" form:"RepeatPassword" validate:"required"`
}

type ResetPasswordRes struct {
}

// 通过账号获取转账地址与余额
type ResetMoneyReq struct {
	ResetType int    `json:"ResetType" form:"ResetType" validate:"required"`
	Account   string `json:"Account" form:"Account" validate:"required"`
}

type ResetMoneyRes struct {
	ResetType       int     `json:"ResetType" form:"ResetType" validate:"required"`
	TransferMoney   float64 `json:"TransferMoney" validate:"required"`
	TransferAddress string  `json:"TransferAddress" validate:"required"`
}
