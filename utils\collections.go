package utils

type AnyMap struct {
	anyMap map[string]interface{}
	strMap map[string]string
}

func NewAnyMap() *AnyMap {
	return &AnyMap{
		anyMap: make(map[string]interface{}),
	}
}

func (m *AnyMap) AppendKv(k string, v interface{}) *AnyMap {
	m.anyMap[k] = v
	return m
}
func (m *AnyMap) AppendKStr(k string, v string) *AnyMap {
	m.strMap[k] = v
	return m
}

func (m *AnyMap) GetMap() map[string]interface{} {
	return m.anyMap
}

func (m *AnyMap) GetStrMap() map[string]string {
	return m.strMap
}
