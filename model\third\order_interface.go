package third

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"xserver/abugo"
	thirdGameModel "xserver/model/third_game"
	"xserver/utils"
)

// IBetOrder 统一的下注接口
type IBetOrder interface {
	Bet(ctx *abugo.AbuHttpContent)
}

// ISettleOrder 统一的派奖接口
type ISettleOrder interface {
	Settle(ctx *abugo.AbuHttpContent)
}

// IReSettleOrder 统一的重新结算接口
type IReSettleOrder interface {
	ReSettle(ctx *abugo.AbuHttpContent)
}

// BaseOrder 基础订单结构
type BaseOrder struct {
	BrandName string
	TableName string // 表名前缀
}

// BetOrderHelper 下注辅助函数
func (b *BaseOrder) CreateBetOrder(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance) error {
	// 扣除金额
	if err := b.deductAmount(tx, order, userBalance); err != nil {
		return err
	}

	// 创建订单
	if err := tx.Table(b.TableName).Create(order).Error; err != nil {
		return fmt.Errorf("创建注单失败: %v", err)
	}

	return nil
}

// SettleOrderHelper 派奖辅助函数
func (b *BaseOrder) SettleOrder(tx *gorm.DB, thirdId string, userId int32, winAmount float64, thirdTime string) error {
	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	if err := tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).
		Select("UserId,SellerId,ChannelId,Amount,BonusAmount,Token").
		Where("UserId = ?", userId).First(&userBalance).Error; err != nil {
		return err
	}

	// 查询原始注单
	order := thirdGameModel.ThirdOrder{}
	if err := tx.Table(b.TableName).Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("ThirdId=? and Brand=?", thirdId, b.BrandName).First(&order).Error; err != nil {
		return err
	}

	// 更新注单状态
	updates := map[string]interface{}{
		"WinAmount":  winAmount,
		"DataState":  1,
		"ThirdTime":  thirdTime,
		"SettleTime": thirdTime,
	}
	if err := tx.Table(b.TableName).Where("Id=?", order.Id).Updates(updates).Error; err != nil {
		return err
	}

	// 派发奖金
	return b.awardPrize(tx, &order, &userBalance, winAmount, thirdTime)
}

// 扣除金额处理
func (b *BaseOrder) deductAmount(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance) error {
	realAmount := order.BetAmount - order.BonusBetAmount
	// 扣除真金
	if realAmount > 0 {
		if err := b.deductRealMoney(tx, order, userBalance); err != nil {
			return err
		}
	}

	// 扣除Bonus币
	if order.BonusBetAmount > 0 {
		if err := b.deductBonus(tx, order, userBalance); err != nil {
			return err
		}
	}

	return nil
}

// 派发奖金处理
func (b *BaseOrder) awardPrize(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance, winAmount float64, thirdTime string) error {
	// 计算真金和Bonus币的中奖比例
	realAmount := order.BetAmount - order.BonusBetAmount
	totalBet := realAmount + order.BonusBetAmount
	if totalBet <= 0 {
		return nil
	}

	realMoneyWin := winAmount * (realAmount / totalBet)
	bonusWin := winAmount * (order.BonusBetAmount / totalBet)

	// 派发真金奖金
	if realMoneyWin > 0 {
		if err := b.awardRealMoney(tx, order, userBalance, realMoneyWin, thirdTime); err != nil {
			return err
		}
	}

	// 派发Bonus币奖金
	if bonusWin > 0 {
		if err := b.awardBonus(tx, order, userBalance, bonusWin, thirdTime); err != nil {
			return err
		}
	}

	return nil
}

// 扣除真金
func (b *BaseOrder) deductRealMoney(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance) error {
	realAmount := order.BetAmount - order.BonusBetAmount
	result := tx.Table("x_user").Where("UserId = ?", order.UserId).
		Updates(map[string]interface{}{
			"Amount": gorm.Expr("Amount - ?", realAmount),
		})
	if result.Error != nil || result.RowsAffected <= 0 {
		return errors.New("扣除真金失败")
	}

	// 创建账变记录
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userBalance.UserId,
		BeforeAmount: userBalance.Amount,
		Amount:       -realAmount,
		AfterAmount:  userBalance.Amount - realAmount,
		Reason:       utils.BalanceCReasonBet,
		Memo:         fmt.Sprintf("%s bet real money,thirdId:%s", b.BrandName, order.ThirdId),
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   order.CreateTime,
		AmountType:   1, // 真金类型
	}
	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}

// 扣除Bonus币
func (b *BaseOrder) deductBonus(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance) error {
	result := tx.Table("x_user").Where("UserId = ?", order.UserId).
		Updates(map[string]interface{}{
			"BonusAmount": gorm.Expr("BonusAmount - ?", order.BonusBetAmount),
		})
	if result.Error != nil || result.RowsAffected <= 0 {
		return errors.New("扣除Bonus币失败")
	}

	// 创建账变记录
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userBalance.UserId,
		BeforeAmount: userBalance.BonusAmount,
		Amount:       -order.BonusBetAmount,
		AfterAmount:  userBalance.BonusAmount - order.BonusBetAmount,
		Reason:       utils.BalanceCReasonBet,
		Memo:         fmt.Sprintf("%s bet bonus,thirdId:%s", b.BrandName, order.ThirdId),
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   order.CreateTime,
		AmountType:   2, // Bonus币类型
	}
	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}

// 派发真金奖金
func (b *BaseOrder) awardRealMoney(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance, winAmount float64, thirdTime string) error {
	result := tx.Table("x_user").Where("UserId = ?", order.UserId).
		Updates(map[string]interface{}{
			"Amount": gorm.Expr("Amount + ?", winAmount),
		})
	if result.Error != nil || result.RowsAffected <= 0 {
		return errors.New("派发真金奖金失败")
	}

	// 创建账变记录
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userBalance.UserId,
		BeforeAmount: userBalance.Amount,
		Amount:       winAmount,
		AfterAmount:  userBalance.Amount + winAmount,
		Reason:       utils.BalanceCReasonWin,
		Memo:         fmt.Sprintf("%s win real money,thirdId:%s", b.BrandName, order.ThirdId),
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   thirdTime,
		AmountType:   1, // 真金类型
	}
	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}

// 派发Bonus币奖金
func (b *BaseOrder) awardBonus(tx *gorm.DB, order *thirdGameModel.ThirdOrder, userBalance *thirdGameModel.UserBalance, winAmount float64, thirdTime string) error {
	result := tx.Table("x_user").Where("UserId = ?", order.UserId).
		Updates(map[string]interface{}{
			"BonusAmount": gorm.Expr("BonusAmount + ?", winAmount),
		})
	if result.Error != nil || result.RowsAffected <= 0 {
		return errors.New("派发Bonus币奖金失败")
	}

	// 创建账变记录
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userBalance.UserId,
		BeforeAmount: userBalance.BonusAmount,
		Amount:       winAmount,
		AfterAmount:  userBalance.BonusAmount + winAmount,
		Reason:       utils.BalanceCReasonWin,
		Memo:         fmt.Sprintf("%s win bonus,thirdId:%s", b.BrandName, order.ThirdId),
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   thirdTime,
		AmountType:   2, // Bonus币类型
	}
	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}
