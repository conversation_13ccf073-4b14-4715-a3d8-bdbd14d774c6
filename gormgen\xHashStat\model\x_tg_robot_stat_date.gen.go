// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRobotStatDate = "x_tg_robot_stat_date"

// XTgRobotStatDate 机器人日统计
type XTgRobotStatDate struct {
	RecordDate     time.Time `gorm:"column:RecordDate;primaryKey;comment:日期" json:"RecordDate"`                           // 日期
	RobotID        int64     `gorm:"column:RobotId;primaryKey;comment:机器人Id" json:"RobotId"`                              // 机器人Id
	TgChatID       int64     `gorm:"column:TgChatId;primaryKey;comment: tg用户Id" json:"TgChatId"`                          //  tg用户Id
	ActionType     int32     `gorm:"column:ActionType;primaryKey;comment:行为分类" json:"ActionType"`                         // 行为分类
	SellerID       int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	ChannelID      int32     `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`                                      // 渠道id
	ActionCount    int32     `gorm:"column:ActionCount;not null;comment:行为次数" json:"ActionCount"`                         // 行为次数
	IsInResourceDb int32     `gorm:"column:IsInResourceDb;not null;comment:是否在库" json:"IsInResourceDb"`                   // 是否在库
	RobotUsername  string    `gorm:"column:RobotUsername;comment:机器人Username" json:"RobotUsername"`                       // 机器人Username
	SellerName     string    `gorm:"column:SellerName;comment:运营商名称" json:"SellerName"`                                   // 运营商名称
	ChannelName    string    `gorm:"column:ChannelName;comment:渠道名称" json:"ChannelName"`                                  // 渠道名称
	Memo           string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTgRobotStatDate's table name
func (*XTgRobotStatDate) TableName() string {
	return TableNameXTgRobotStatDate
}
