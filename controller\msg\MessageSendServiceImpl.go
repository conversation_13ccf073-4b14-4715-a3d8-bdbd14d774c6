package msg

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/msg/model"
	"xserver/server"
)

// messageSendServiceImpl 消息发送服务实现
type messageSendServiceImpl struct {
	db *abugo.AbuDb
}

// SendBatchMessage 根据模板ID批量发送消息
// @param templateId 模板ID
// @param userFilter 用户筛选条件，用于确定消息接收者
// @param variables 消息变量，用于替换模板中的变量
// @return int 成功发送的消息数量
// @return error 发送失败时返回错误
func (s *messageSendServiceImpl) SendBatchMessage(templateId int64, userFilter UserFilter, variables map[string]interface{}) (int, error) {
	// 1. 获取模板信息
	var template model.StationMessageTemplate
	result := server.Db().GormDao().Where("Id = ?", templateId).First(&template)
	if result.Error != nil {
		return 0, fmt.Errorf("模板不存在: %w", result.Error)
	}
	if template.Status == 0 {
		logs.Error("站内信模板已经停用，跳过本次消息发送", " Id=", templateId, " Type=", template.Type)
		return 0, nil
	}

	// 2. 获取模板内容
	var contents []model.TemplateContent
	result = server.Db().GormDao().Where("TemplateId = ?", templateId).Find(&contents)
	if result.Error != nil {
		return 0, fmt.Errorf("获取模板内容失败: %w", result.Error)
	}

	// 3. 获取目标用户
	users, err := s.getTargetUsers(userFilter.SellerType, userFilter.SellerIds, userFilter.ChannelIds, userFilter.VipLevels, userFilter.UserIds, userFilter.UserLabels, userFilter.TopAgentIds)
	if err != nil {
		return 0, fmt.Errorf("获取目标用户失败: %w", err)
	}

	if len(*users) == 0 {
		return 0, fmt.Errorf("没有符合条件的目标用户")
	}

	// 4. 准备多语言内容
	// 将所有语言版本的标题和内容整理成JSON格式
	titleLangMap := make(map[string]string)
	contentLangMap := make(map[string]string)

	// 处理每种语言的内容
	for _, content := range contents {
		lang := content.Lang
		title := content.Title
		contentText := content.Content

		// 应用变量替换
		if variables != nil {
			title = s.replaceTemplateVariables(title, variables)
			contentText = s.replaceTemplateVariables(contentText, variables)
		}

		titleLangMap[lang] = title
		contentLangMap[lang] = contentText
	}

	// 特殊处理：如果是活动参与提醒（Type=1）且提供了活动标题 则用户收到的消息取活动标题
	if template.Type == "1" && variables != nil {
		// 检查是否有活动标题的多语言版本
		if titleLang, ok := variables["_titleLang"].(string); ok && titleLang != "" {
			// 尝试解析多语言标题JSON
			var activityTitleLang map[string]string
			err := json.Unmarshal([]byte(titleLang), &activityTitleLang)
			if err == nil && len(activityTitleLang) > 0 {
				// 使用活动的多语言标题替换模板标题
				for lang, title := range activityTitleLang {
					if title != "" {
						if _, exists := titleLangMap[lang]; exists {
							titleLangMap[lang] = title
						}
					}
				}
			}
		} else if title, ok := variables["_title"].(string); ok && title != "" {
			// 如果没有多语言标题但有普通标题，则为所有语言版本设置相同的标题
			for lang := range titleLangMap {
				titleLangMap[lang] = title
			}
		}
	}

	// 转换为JSON
	titleLangJSON, _ := json.Marshal(titleLangMap)
	contentLangJSON, _ := json.Marshal(contentLangMap)

	// 5. 去重用户ID
	userIdMap := make(map[int64]bool)
	uniqueUsers := make([]map[string]interface{}, 0, len(*users)) // 预分配容量

	for _, user := range *users {
		userId := abugo.GetInt64FromInterface(user["UserId"])
		if _, exists := userIdMap[userId]; !exists {
			userIdMap[userId] = true
			uniqueUsers = append(uniqueUsers, user)
		}
	}

	// 6. 批量构建消息记录
	// 预分配切片容量，避免频繁扩容
	messages := make([]model.MessageRecord, 0, len(uniqueUsers))

	// 使用相同的时间戳，减少时间函数调用
	now := time.Now()

	// 默认语言ID
	langId := "1"

	// 获取默认标题和内容
	defaultTitle := titleLangMap[langId]
	defaultContent := contentLangMap[langId]

	// 如果默认语言没有内容，使用第一个可用的语言
	if defaultTitle == "" || defaultContent == "" {
		for _, title := range titleLangMap {
			defaultTitle = title
			break
		}
		for _, content := range contentLangMap {
			defaultContent = content
			break
		}
	}

	titleLang := string(titleLangJSON)
	contentLang := string(contentLangJSON)

	for _, user := range uniqueUsers {
		userId := abugo.GetInt64FromInterface(user["UserId"])
		sellerId := abugo.GetInt64FromInterface(user["SellerId"])
		channelId := abugo.GetInt64FromInterface(user["ChannelId"])

		title := defaultTitle
		content := defaultContent
		titleLangCopy := titleLang
		contentLangCopy := contentLang

		// 替换用户名变量
		nickName := abugo.GetStringFromInterface(user["NickName"])
		if nickName != "" {
			title = strings.ReplaceAll(title, "{用户名}", nickName)
			content = strings.ReplaceAll(content, "{用户名}", nickName)
			titleLangCopy = strings.ReplaceAll(titleLangCopy, "{用户名}", nickName)
			contentLangCopy = strings.ReplaceAll(contentLangCopy, "{用户名}", nickName)
		}

		// 创建消息记录
		message := model.MessageRecord{
			TemplateID:  templateId,
			UserID:      userId,
			SellerID:    sellerId,
			ChannelID:   channelId,
			Type:        template.Type,
			Title:       title,
			Content:     content,
			TitleLang:   titleLangCopy,
			ContentLang: contentLangCopy,
			IsRead:      0,   // 0表示未读
			SentAt:      now, // 使用相同的时间戳
		}

		// 添加到批量消息列表
		messages = append(messages, message)
	}

	// 7. 批量插入记录
	// 设置合理的批次大小（200条），平衡内存使用和数据库负载
	batchSize := 200
	if len(messages) > 0 {
		result := server.Db().GormDao().CreateInBatches(messages, batchSize)
		if result.Error != nil {
			return 0, fmt.Errorf("批量插入消息记录失败: %w", result.Error)
		}

		logs.Info("成功批量发送消息，模板ID: %d, 发送数量: %d", templateId, len(messages))
		return len(messages), nil
	}

	return 0, nil
}

// SendMessage 根据模板ID发送消息给单个用户
// @param templateId 模板ID
// @param userId 用户ID
// @param variables 消息变量，用于替换模板中的变量
// @return error 发送失败时返回错误
func (s *messageSendServiceImpl) SendMessage(templateId int64, userId int, variables map[string]interface{}) error {
	// 创建只包含单个用户ID的用户筛选条件
	userFilter := UserFilter{
		UserIds: strconv.Itoa(userId),
	}

	// 调用批量发送方法
	count, err := s.SendBatchMessage(templateId, userFilter, variables)
	if err != nil {
		return err
	}

	if count == 0 {
		return fmt.Errorf("发送消息失败，没有成功发送的消息")
	}

	return nil
}

// SendMessageByType 根据消息类型发送消息（内部查询模板ID）
// @param messageType 消息类型，对应模板类型
// @param userId 用户ID
// @param variables 消息变量，用于替换模板中的变量
// @return error 发送失败时返回错误
func (s *messageSendServiceImpl) SendMessageByType(messageType string, userId int, variables map[string]interface{}) error {
	// 根据消息类型获取模板ID
	templateId, err := s.getTemplateIdByType(messageType)
	if err != nil {
		return fmt.Errorf("获取模板失败: %w", err)
	}

	// 调用SendMessage方法发送消息
	return s.SendMessage(templateId, userId, variables)
}

// SendTimedMessage 发送定时消息
func (s *messageSendServiceImpl) SendTimedMessage(templateId int64) (int, error) {
	// 1. 获取模板信息
	var template model.StationMessageTemplate
	result := server.Db().GormDao().Where("Id = ?", templateId).First(&template)
	if result.Error != nil {
		return 0, fmt.Errorf("获取模板失败: %w", result.Error)
	}

	if template.Status == 0 {
		return 0, fmt.Errorf("模板已停用，无法发送消息")
	}

	if template.IsTimed == 0 || template.TimedAt == nil {
		return 0, fmt.Errorf("该模板不是定时发送模板或定时发送时间未设置")
	}

	// 检查定时发送时间是否已到
	now := time.Now()
	if now.Before(*template.TimedAt) {
		return 0, fmt.Errorf("定时发送时间未到，当前时间: %v, 定时时间: %v", now, *template.TimedAt)
	}

	// 创建用户筛选条件
	userFilter := UserFilter{
		SellerType:  template.SellerType,
		SellerIds:   template.SellerIds,
		ChannelIds:  template.ChannelIds,
		VipLevels:   template.VipLevels,
		UserIds:     template.UserIds,
		UserLabels:  template.UserLabels,
		TopAgentIds: template.TopAgentIds,
	}

	// 发送消息
	return s.SendBatchMessage(templateId, userFilter, nil)
}

// AsyncSendMessage 异步发送消息
func (s *messageSendServiceImpl) AsyncSendMessage(templateId int64, userFilter UserFilter, variables map[string]interface{}) (string, error) {
	// 生成任务ID - 使用时间戳+随机数
	taskId := fmt.Sprintf("task_%d_%d", time.Now().UnixNano(), rand.Intn(10000))

	// 记录任务开始
	logs.Info("异步发送消息任务开始: taskId=%s, templateId=%d", taskId, templateId)

	// 将任务加入队列
	go func() {
		// 记录开始时间
		startTime := time.Now()

		// 执行发送
		count, err := s.SendBatchMessage(templateId, userFilter, variables)

		// 计算耗时
		duration := time.Since(startTime)

		// 记录任务结果
		if err != nil {
			logs.Error("异步发送消息任务失败: taskId=%s, templateId=%d, error=%v, 耗时=%v",
				taskId, templateId, err, duration)
		} else {
			logs.Info("异步发送消息任务完成: taskId=%s, templateId=%d, 发送数量=%d, 耗时=%v",
				taskId, templateId, count, duration)
		}

		// 这里可以将结果保存到数据库中，简化示例中仅记录日志
		// saveTaskResult(taskId, count, err)
	}()

	return taskId, nil
}

// getTargetUsers 获取目标用户
func (s *messageSendServiceImpl) getTargetUsers(sellerType int, sellerIds, channelIds, vipLevels, userIds string, userLabels string, topAgentIds string) (*[]map[string]interface{}, error) {
	// 构建基本查询条件
	where := abugo.AbuDbWhere{}
	hasCondition := false

	// 处理运营商条件或顶级代理条件（根据sellerType决定）
	if sellerType == 1 {
		// sellerType=1表示使用运营商ID筛选
		if sellerIds != "" {
			sellerIdList := strings.Split(sellerIds, ",")
			where.Add("and", "x_user.SellerId", "in", "("+strings.Join(sellerIdList, ",")+")", nil)
			hasCondition = true
		}
		// 处理渠道条件
		if channelIds != "" {
			channelIdList := strings.Split(channelIds, ",")
			where.Add("and", "x_user.ChannelId", "in", "("+strings.Join(channelIdList, ",")+")", nil)
			hasCondition = true
		}
	} else if sellerType == 2 {
		// sellerType=2表示使用顶级代理ID筛选
		if topAgentIds != "" {
			topAgentIdList := strings.Split(topAgentIds, ",")
			where.Add("and", "x_user.TopAgentId", "in", "("+strings.Join(topAgentIdList, ",")+")", nil)
			hasCondition = true
		}
	}

	if vipLevels != "" || userLabels != "" {
		if sellerType != 1 && sellerType != 2 {
			return nil, fmt.Errorf("获取VIP用户失败:  sellerType,当指定了VIP等级或用户标签时 sellerType必须为1或2")
		}
	}

	// 处理用户ID条件
	if userIds != "" {
		userIdList := strings.Split(userIds, ",")
		where.Add("and", "x_user.UserId", "in", "("+strings.Join(userIdList, ",")+")", nil)
		hasCondition = true
	}

	// 执行基本查询，获取基本用户列表
	var baseUserIds []int
	if hasCondition {
		baseUsers, err := server.Db().Table("x_user").Select("UserId").Where(where).GetList()
		if err != nil {
			return nil, fmt.Errorf("获取基本条件用户失败: %w", err)
		}

		// 提取用户ID
		baseUserIds = make([]int, 0, len(*baseUsers))
		for _, user := range *baseUsers {
			userId := int(abugo.GetInt64FromInterface(user["UserId"]))
			if userId > 0 {
				baseUserIds = append(baseUserIds, userId)
			}
		}

		// 如果没有找到用户，返回空结果
		if len(baseUserIds) == 0 {
			return &[]map[string]interface{}{}, nil
		}
	}

	// 处理VIP等级条件
	var vipUserIds []int
	if vipLevels != "" {
		vipLevelList := strings.Split(vipLevels, ",")
		var err error
		vipUserIds, err = s.getUserIdsByVipLevels(vipLevelList)
		if err != nil {
			return nil, fmt.Errorf("获取VIP用户失败: %w", err)
		}

		// 如果没有找到VIP用户，返回空结果
		if len(vipUserIds) == 0 && hasCondition {
			return &[]map[string]interface{}{}, nil
		}
	}

	// 处理用户标签
	var labelUserIds []int
	if userLabels != "" {
		userLabelList := strings.Split(userLabels, ",")
		var err error
		labelUserIds, err = s.getUserIdsByLabels(userLabelList)
		if err != nil {
			return nil, fmt.Errorf("获取标签用户失败: %w", err)
		}

		// 如果没有找到标签用户，返回空结果
		if len(labelUserIds) == 0 && hasCondition {
			return &[]map[string]interface{}{}, nil
		}
	}

	// 根据筛选条件计算最终用户ID
	var finalUserIds []int

	// 如果没有任何条件，则不筛选用户
	if !hasCondition && vipLevels == "" && userLabels == "" {
		//// 获取所有用户
		//allUsers, err := server.Db().Table("x_user").Select("UserId").GetList()
		//if err != nil {
		//	return nil, fmt.Errorf("获取所有用户失败: %w", err)
		//}
		//
		//finalUserIds = make([]int, 0, len(*allUsers))
		//for _, user := range *allUsers {
		//	userId := int(abugo.GetInt64FromInterface(user["UserId"]))
		//	if userId > 0 {
		//		finalUserIds = append(finalUserIds, userId)
		//	}
		//}
	} else {
		// 有条件时，需要进行条件组合
		// 初始化最终用户ID集合
		userIdSet := make(map[int]bool)

		// 如果有基本条件（运营商、渠道、用户ID、顶级代理ID），则使用这些用户作为基础
		if hasCondition {
			for _, id := range baseUserIds {
				userIdSet[id] = true
			}
		} else {
			// 如果没有基本条件，但有VIP或标签条件，则使用第一个非空条件作为基础
			if len(vipUserIds) > 0 {
				for _, id := range vipUserIds {
					userIdSet[id] = true
				}
				// 标记VIP条件已处理
				vipUserIds = nil
			} else if len(labelUserIds) > 0 {
				for _, id := range labelUserIds {
					userIdSet[id] = true
				}
				// 标记标签条件已处理
				labelUserIds = nil
			}
		}

		// 如果还有VIP条件未处理，则进行交集处理（VIP内部是或关系）
		if len(vipUserIds) > 0 {
			// 创建一个临时集合，存储同时满足现有条件和VIP条件的用户ID
			tempSet := make(map[int]bool)

			// 遍历现有用户ID集合
			for id := range userIdSet {
				// 检查该用户是否也在VIP用户ID列表中
				for _, vipId := range vipUserIds {
					if id == vipId {
						tempSet[id] = true
						break
					}
				}
			}

			// 更新用户ID集合
			userIdSet = tempSet
		}

		// 如果还有标签条件未处理，则进行交集处理
		if len(labelUserIds) > 0 {
			// 创建一个临时集合，存储同时满足现有条件和标签条件的用户ID
			tempSet := make(map[int]bool)

			// 遍历现有用户ID集合
			for id := range userIdSet {
				// 检查该用户是否也在标签用户ID列表中
				for _, labelId := range labelUserIds {
					if id == labelId {
						tempSet[id] = true
						break
					}
				}
			}

			// 更新用户ID集合
			userIdSet = tempSet
		}

		// 将最终的用户ID集合转换为切片
		finalUserIds = make([]int, 0, len(userIdSet))
		for id := range userIdSet {
			finalUserIds = append(finalUserIds, id)
		}
	}

	// 如果没有找到用户，返回空结果
	if len(finalUserIds) == 0 {
		return &[]map[string]interface{}{}, nil
	}

	// 一次性查询所有用户信息
	return s.getUserInfos(finalUserIds)
}

// getUserIdsByVipLevels 根据VIP等级获取用户ID
func (s *messageSendServiceImpl) getUserIdsByVipLevels(vipLevels []string) ([]int, error) {
	if len(vipLevels) == 0 {
		return []int{}, nil
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	vipLevelList := make([]string, len(vipLevels))
	for i, level := range vipLevels {
		vipLevelList[i] = "'" + level + "'"
	}
	where.Add("and", "VipLevel", "in", "("+strings.Join(vipLevelList, ",")+")", nil)

	// 执行查询
	vipInfos, err := server.Db().Table("x_vip_info").Select("UserId").Where(where).GetList()
	if err != nil {
		return nil, err
	}

	// 提取用户ID
	userIds := make([]int, 0, len(*vipInfos))
	for _, info := range *vipInfos {
		userId := int(abugo.GetInt64FromInterface(info["UserId"]))
		if userId > 0 {
			userIds = append(userIds, userId)
		}
	}

	return userIds, nil
}

// getUserInfos 获取用户信息
func (s *messageSendServiceImpl) getUserInfos(userIds []int) (*[]map[string]interface{}, error) {
	if len(userIds) == 0 {
		return &[]map[string]interface{}{}, nil
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	userIdStrList := make([]string, len(userIds))
	for i, id := range userIds {
		userIdStrList[i] = strconv.Itoa(id)
	}
	where.Add("and", "x_user.UserId", "in", "("+strings.Join(userIdStrList, ",")+")", nil)

	// 执行查询 - 使用与 getTargetUsers 相同的表关联
	selectFields := "x_user.UserId, x_user.SellerId, x_user.ChannelId, x_user.LoginLang, x_user.NickName"

	// 执行查询
	users, err := server.Db().Table("x_user").Select(selectFields).Where(where).GetList()
	if err != nil {
		return nil, err
	}

	return users, nil
}

// getUserIdsByLabels 根据用户标签获取用户ID
func (s *messageSendServiceImpl) getUserIdsByLabels(userLabels []string) ([]int, error) {
	if len(userLabels) == 0 {
		return []int{}, nil
	}

	// 构建查询条件
	where := abugo.AbuDbWhere{}
	labelList := make([]string, len(userLabels))
	for i, label := range userLabels {
		labelList[i] = "'" + label + "'"
	}
	where.Add("and", "Label", "in", "("+strings.Join(labelList, ",")+")", nil)

	// 执行查询
	userLabelsData, err := server.Db().Table("x_user_label").Select("UserId").Where(where).GetList()
	if err != nil {
		return nil, err
	}

	// 提取用户ID
	userIds := make([]int, 0, len(*userLabelsData))
	for _, data := range *userLabelsData {
		userId := int(abugo.GetInt64FromInterface(data["UserId"]))
		if userId > 0 {
			userIds = append(userIds, userId)
		}
	}

	return userIds, nil
}

// getTemplateIdByType 根据消息类型获取模板ID
// @param messageType 消息类型，对应模板类型
// @return int64 模板ID
// @return error 查询失败时返回错误
func (s *messageSendServiceImpl) getTemplateIdByType(messageType string) (int64, error) {
	// 查询数据库获取指定类型的模板
	var template model.StationMessageTemplate
	result := server.Db().GormDao().Model(&model.StationMessageTemplate{}).
		Where("Type = ? AND PushType = ?", messageType, 0). // 只按类型和推送类型过滤
		Order("Id DESC").                                   // 获取最新的模板
		Select("Id, Status").                               // 只选择需要的字段
		First(&template)

	if result.Error != nil {
		logs.Error("未找到类型为 %s 的模板: %v", messageType, result.Error)
		return 0, fmt.Errorf("未找到类型为 %s 的模板: %w", messageType, result.Error)
	}

	// 根据模板状态判断
	if template.Status == 0 {
		logs.Error("类型为 %s 的模板已被禁用", messageType)
		return 0, fmt.Errorf("类型为 %s 的模板已被禁用", messageType)
	} else if template.Status == 1 {
		// 模板状态为启用
		return template.ID, nil
	} else {
		// 其他未知状态
		logs.Error("类型为 %s 的模板状态异常: %d", messageType, template.Status)
		return 0, fmt.Errorf("类型为 %s 的模板状态异常: %d", messageType, template.Status)
	}
}

// 替换变量的兼容处理函数
func (s *messageSendServiceImpl) replaceTemplateVariables(text string, vars map[string]interface{}) string {
	result := text

	// 先处理传入的变量
	for key, value := range vars {
		strValue := fmt.Sprint(value)

		// 处理不带花括号的键
		plainKey := key
		if strings.HasPrefix(key, "{") && strings.HasSuffix(key, "}") {
			// 如果键已经有花括号，去掉它们
			plainKey = key[1 : len(key)-1]
		}

		// 替换 {key} 格式
		result = strings.ReplaceAll(result, "{"+plainKey+"}", strValue)

		// 如果键本身已经带有花括号，也直接替换
		if key != plainKey {
			result = strings.ReplaceAll(result, key, strValue)
		}
	}

	// 查找并替换剩余的所有 {变量} 为空字符串
	re := regexp.MustCompile(`\{[^{}]+\}`)
	result = re.ReplaceAllString(result, "")

	return result
}

// NewMessageSendServiceImpl 创建消息发送服务实现实例
func NewMessageSendServiceImpl() MessageSendService {
	return &messageSendServiceImpl{
		db: server.Db(),
	}
}
