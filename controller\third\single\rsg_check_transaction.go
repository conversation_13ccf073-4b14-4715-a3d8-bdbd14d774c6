package single

import (
	"encoding/json"
	"math"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
)

// CheckTransaction 检查交易状态 API URL CheckTransaction
func (l *RSGSingleService) CheckTransaction(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string `json:"SystemCode"`    // 系统代码，必填，2~20位英数
		WebId         string `json:"WebId"`         // 站台代码，必填，3~20位英数
		UserId        string `json:"UserId"`        // 会员惟一识别码，必填，3~20位英数
		GameId        int    `json:"GameId"`        // 游戏代码，必填，只限3001和3002
		Currency      string `json:"Currency"`      // 币别代码，必填，2~5位
		TransactionId string `json:"TransactionId"` // 交易惟一识别码，必填，8~30位英数、@
	}

	type TransactionData struct {
		TransactionId   string  `json:"TransactionId"`   // 交易惟一识别码
		TransactionTime string  `json:"TransactionTime"` // 交易时间，格式：yyyy-MM-dd HH:mm:ss
		WebId           string  `json:"WebId"`           // 站台代码
		UserId          string  `json:"UserId"`          // 会员惟一识别码
		GameId          int     `json:"GameId"`          // 游戏代码
		Currency        string  `json:"Currency"`        // 币别代码
		Action          int     `json:"Action"`          // 操作类型：1=预付，2=退款
		Amount          float64 `json:"Amount"`          // 交易金额（小数点两位）
		AfterBalance    float64 `json:"AfterBalance"`    // 交易后余额（小数点两位）
	}

	type ResponseData struct {
		ErrorCode    int             `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string          `json:"ErrorMessage"` // 错误信息
		Timestamp    int64           `json:"Timestamp"`    // 时间戳
		Data         TransactionData `json:"Data"`         // 交易数据
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 检查交易状态")
	if err != nil {
		logs.Error("RSG_single 检查交易状态 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 检查交易状态 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 检查交易状态 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 检查交易状态 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 检查交易状态 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	// 检查交易是否存在
	thirdId := reqdata.TransactionId

	// 查询账变记录，确定订单号
	thirdAmountLog := thirdGameModel.ThirdAmountLog{}
	err = server.Db().GormDao().Table("x_third_amount_log").
		Where("UserId = ? and ThirdId = ? and Brand = ? ", userId, thirdId, l.brandName).
		First(&thirdAmountLog).Error
	if err == nil {
		// 交易存在，返回交易详情
		logs.Info("RSG_single 检查交易状态 交易存在 thirdId=", thirdId)

		// 交易时间已经是字符串格式
		transactionTime := thirdAmountLog.CreateTime

		// 确定操作类型（1=预付，2=退款）
		action := 1
		if thirdAmountLog.Amount > 0 {
			action = 2 // 金额为正，表示退款
		}

		// 保留两位小数
		amount := math.Abs(thirdAmountLog.Amount)
		amount = math.Floor(amount*100) / 100

		afterBalance := math.Floor(thirdAmountLog.AfterAmount*100) / 100

		// 填充响应数据
		respdata.Data = TransactionData{
			TransactionId:   thirdId,
			TransactionTime: transactionTime,
			WebId:           reqdata.WebId,
			UserId:          reqdata.UserId,
			GameId:          reqdata.GameId,
			Currency:        reqdata.Currency,
			Action:          action,
			Amount:          amount,
			AfterBalance:    afterBalance,
		}

		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	} else if err != daogorm.ErrRecordNotFound {
		// 查询出错
		logs.Error("RSG_single 检查交易状态 查询交易失败 thirdId=", thirdId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
		return
	}

	// 交易不存在
	logs.Info("RSG_single 检查交易状态 交易不存在 thirdId=", thirdId)
	respdata.ErrorCode = RSG_Code_Transaction_Not_Found
	respdata.ErrorMessage = "找不到交易结果"
	l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 检查交易状态")
}
