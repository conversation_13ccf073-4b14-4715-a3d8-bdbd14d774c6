// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserWallet(db *gorm.DB, opts ...gen.DOOption) xUserWallet {
	_xUserWallet := xUserWallet{}

	_xUserWallet.xUserWalletDo.UseDB(db, opts...)
	_xUserWallet.xUserWalletDo.UseModel(&model.XUserWallet{})

	tableName := _xUserWallet.xUserWalletDo.TableName()
	_xUserWallet.ALL = field.NewAsterisk(tableName)
	_xUserWallet.ID = field.NewInt32(tableName, "Id")
	_xUserWallet.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserWallet.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserWallet.UserID = field.NewInt32(tableName, "UserId")
	_xUserWallet.Address = field.NewString(tableName, "Address")
	_xUserWallet.State = field.NewInt32(tableName, "State")
	_xUserWallet.VerifyAmount = field.NewInt32(tableName, "VerifyAmount")
	_xUserWallet.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserWallet.IsTgSend = field.NewInt32(tableName, "IsTgSend")
	_xUserWallet.IsStat = field.NewInt32(tableName, "IsStat")
	_xUserWallet.Rank = field.NewInt32(tableName, "Rank")
	_xUserWallet.Net = field.NewString(tableName, "Net")

	_xUserWallet.fillFieldMap()

	return _xUserWallet
}

type xUserWallet struct {
	xUserWalletDo xUserWalletDo

	ALL          field.Asterisk
	ID           field.Int32
	SellerID     field.Int32
	ChannelID    field.Int32
	UserID       field.Int32  // 玩家
	Address      field.String // 地址
	State        field.Int32  // 状态 1已验证 2未验证
	VerifyAmount field.Int32  // 验证金额
	CreateTime   field.Time
	IsTgSend     field.Int32 // 是否发送已TG绑定成功消息(0:否 1:是)
	IsStat       field.Int32 // 是否统计(0:否 1:是)
	Rank         field.Int32
	Net          field.String // 网络

	fieldMap map[string]field.Expr
}

func (x xUserWallet) Table(newTableName string) *xUserWallet {
	x.xUserWalletDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserWallet) As(alias string) *xUserWallet {
	x.xUserWalletDo.DO = *(x.xUserWalletDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserWallet) updateTableName(table string) *xUserWallet {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Address = field.NewString(table, "Address")
	x.State = field.NewInt32(table, "State")
	x.VerifyAmount = field.NewInt32(table, "VerifyAmount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.IsTgSend = field.NewInt32(table, "IsTgSend")
	x.IsStat = field.NewInt32(table, "IsStat")
	x.Rank = field.NewInt32(table, "Rank")
	x.Net = field.NewString(table, "Net")

	x.fillFieldMap()

	return x
}

func (x *xUserWallet) WithContext(ctx context.Context) *xUserWalletDo {
	return x.xUserWalletDo.WithContext(ctx)
}

func (x xUserWallet) TableName() string { return x.xUserWalletDo.TableName() }

func (x xUserWallet) Alias() string { return x.xUserWalletDo.Alias() }

func (x xUserWallet) Columns(cols ...field.Expr) gen.Columns { return x.xUserWalletDo.Columns(cols...) }

func (x *xUserWallet) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserWallet) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["State"] = x.State
	x.fieldMap["VerifyAmount"] = x.VerifyAmount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["IsTgSend"] = x.IsTgSend
	x.fieldMap["IsStat"] = x.IsStat
	x.fieldMap["Rank"] = x.Rank
	x.fieldMap["Net"] = x.Net
}

func (x xUserWallet) clone(db *gorm.DB) xUserWallet {
	x.xUserWalletDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserWallet) replaceDB(db *gorm.DB) xUserWallet {
	x.xUserWalletDo.ReplaceDB(db)
	return x
}

type xUserWalletDo struct{ gen.DO }

func (x xUserWalletDo) Debug() *xUserWalletDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserWalletDo) WithContext(ctx context.Context) *xUserWalletDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserWalletDo) ReadDB() *xUserWalletDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserWalletDo) WriteDB() *xUserWalletDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserWalletDo) Session(config *gorm.Session) *xUserWalletDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserWalletDo) Clauses(conds ...clause.Expression) *xUserWalletDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserWalletDo) Returning(value interface{}, columns ...string) *xUserWalletDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserWalletDo) Not(conds ...gen.Condition) *xUserWalletDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserWalletDo) Or(conds ...gen.Condition) *xUserWalletDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserWalletDo) Select(conds ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserWalletDo) Where(conds ...gen.Condition) *xUserWalletDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserWalletDo) Order(conds ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserWalletDo) Distinct(cols ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserWalletDo) Omit(cols ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserWalletDo) Join(table schema.Tabler, on ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserWalletDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserWalletDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserWalletDo) Group(cols ...field.Expr) *xUserWalletDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserWalletDo) Having(conds ...gen.Condition) *xUserWalletDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserWalletDo) Limit(limit int) *xUserWalletDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserWalletDo) Offset(offset int) *xUserWalletDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserWalletDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserWalletDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserWalletDo) Unscoped() *xUserWalletDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserWalletDo) Create(values ...*model.XUserWallet) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserWalletDo) CreateInBatches(values []*model.XUserWallet, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserWalletDo) Save(values ...*model.XUserWallet) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserWalletDo) First() (*model.XUserWallet, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserWallet), nil
	}
}

func (x xUserWalletDo) Take() (*model.XUserWallet, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserWallet), nil
	}
}

func (x xUserWalletDo) Last() (*model.XUserWallet, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserWallet), nil
	}
}

func (x xUserWalletDo) Find() ([]*model.XUserWallet, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserWallet), err
}

func (x xUserWalletDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserWallet, err error) {
	buf := make([]*model.XUserWallet, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserWalletDo) FindInBatches(result *[]*model.XUserWallet, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserWalletDo) Attrs(attrs ...field.AssignExpr) *xUserWalletDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserWalletDo) Assign(attrs ...field.AssignExpr) *xUserWalletDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserWalletDo) Joins(fields ...field.RelationField) *xUserWalletDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserWalletDo) Preload(fields ...field.RelationField) *xUserWalletDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserWalletDo) FirstOrInit() (*model.XUserWallet, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserWallet), nil
	}
}

func (x xUserWalletDo) FirstOrCreate() (*model.XUserWallet, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserWallet), nil
	}
}

func (x xUserWalletDo) FindByPage(offset int, limit int) (result []*model.XUserWallet, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserWalletDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserWalletDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserWalletDo) Delete(models ...*model.XUserWallet) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserWalletDo) withDO(do gen.Dao) *xUserWalletDo {
	x.DO = *do.(*gen.DO)
	return x
}
