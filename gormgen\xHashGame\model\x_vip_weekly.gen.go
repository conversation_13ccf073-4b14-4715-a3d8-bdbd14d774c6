// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXVipWeekly = "x_vip_weekly"

// XVipWeekly mapped from table <x_vip_weekly>
type XVipWeekly struct {
	ID              int32     `gorm:"column:Id;not null" json:"Id"`
	RecordDate      time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	RecordStartDate time.Time `gorm:"column:RecordStartDate;comment:记录开始日期" json:"RecordStartDate"` // 记录开始日期
	SellerID        int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID       int32     `gorm:"column:ChannelId" json:"ChannelId"`
	UserID          int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	VipLevel        int32     `gorm:"column:VipLevel;comment:vip等级" json:"VipLevel"`             // vip等级
	State           int32     `gorm:"column:State;default:1;comment:状态 1 未领取 2已领取" json:"State"` // 状态 1 未领取 2已领取
	RewardAmount    float64   `gorm:"column:RewardAmount;comment:领取金额" json:"RewardAmount"`      // 领取金额
	GetTime         time.Time `gorm:"column:GetTime;comment:领取时间" json:"GetTime"`                // 领取时间
}

// TableName XVipWeekly's table name
func (*XVipWeekly) TableName() string {
	return TableNameXVipWeekly
}
