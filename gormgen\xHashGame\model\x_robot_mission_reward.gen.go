// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotMissionReward = "x_robot_mission_reward"

// XRobotMissionReward 活动货币领取表记录
type XRobotMissionReward struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                 // pk
	UserID     int64     `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                          // 用户ID
	Currency   string    `gorm:"column:currency;not null;comment:货币" json:"currency"`                          // 货币
	Amount     int32     `gorm:"column:amount;comment:数量" json:"amount"`                                       // 数量
	Stat       int32     `gorm:"column:stat;comment:活动奖励领取状态0：未知，1已发放" json:"stat"`                            // 活动奖励领取状态0：未知，1已发放
	Remark     string    `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
	IsDel      int32     `gorm:"column:is_del;comment:软删除" json:"is_del"`                                      // 软删除
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新日期" json:"update_time"` // 更新日期
}

// TableName XRobotMissionReward's table name
func (*XRobotMissionReward) TableName() string {
	return TableNameXRobotMissionReward
}
