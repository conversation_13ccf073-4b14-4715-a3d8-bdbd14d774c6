package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// CancelBet 取消下注 API URL CancelBet
func (l *RSGSingleService) CancelBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string `json:"SystemCode"`    // 系统代码，必填
		WebId         string `json:"WebId"`         // 站台代码，必填
		UserId        string `json:"UserId"`        // 会员惟一识别码，必填
		TransactionID string `json:"TransactionID"` // 交易惟一识别码，必填
		GameId        int    `json:"GameId"`        // 游戏代码，必填
		SequenNumber  int64  `json:"SequenNumber"`  // 游戏纪录惟一编号，必填
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 取消下注")
	if err != nil {
		logs.Error("RSG_single 取消下注 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 取消下注 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 取消下注 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 取消下注 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	thirdId := strconv.Itoa(int(reqdata.SequenNumber))
	tablePre := "x_third_dianzhi_pre_order"
	// 开始取消下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		// 查询订单是否存在
		order, exists, err := base.GetOrderForUpdate(tx, thirdId, l.brandName, tablePre)
		// 处理查询错误
		if err != nil {
			// 查询出错
			logs.Error("RSG_single 取消下注 查询预下注订单失败 thirdId=", thirdId, " err=", err.Error(), " order=", order)
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return err
		}

		// 判断订单是否存在
		if !exists {
			logs.Error("RSG_single 取消下注 预下注订单不存在，可能是先收到结算请求 thirdId=", thirdId, " order=", order)
			respdata.ErrorCode = RSG_Code_Seq_Not_Exist
			respdata.ErrorMessage = "订单不存在"
			return errors.New(fmt.Sprintf("订单不存在，thirdId= %s", thirdId))
		}

		if order.DataState != -1 {
			err = errors.New("不能取消已结算订单")
			if order.DataState == 1 {
				err = errors.New("订单已结算")
				logs.Error("RSG_single 取消下注 订单已结算  thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = RSG_Code_Seq_Settled
				respdata.ErrorMessage = "订单已结算"
			} else if order.DataState == -2 {
				logs.Error("RSG_single 取消下注 注单已取消 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = RSG_Code_Seq_Cancelled
				respdata.ErrorMessage = "注单已取消"
				return err
			} else {
				logs.Error("RSG_single 取消下注 注单状态异常 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "注单状态异常"
				err = errors.New("注单状态异常")
				return err
			}
		}

		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 取消下注 获取用户余额失败 userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		}

		rawData, _ := json.Marshal(reqdata)
		// 更新注单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  -2,
			"ThirdTime":  utils.GetCurrentTime(),
			"ValidBet":   0,
			"WinAmount":  0,
			"BetCtx":     string(rawData),
			"GameRst":    string(rawData),
			"BetCtxType": 3,
			"RawData":    string(decryptedBytes),
		}).Error
		if e != nil {
			logs.Error("RSG_single 取消下注 创建取消订单失败 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		amount := order.BetAmount
		if amount != 0 { //金额不=0才需要更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 取消下注 返还用户余额失败 userId=", userId, " amount=", amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       utils.BalanceCReasonRSGCancelBet,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   utils.GetCurrentTime(),
		}

		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("RSG_single 取消下注 创建账变记录失败 userId=", userId, " amount=", amount, " err=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		// 设置响应数据
		respdata.Data.Balance = userBalance.Amount + amount
		balance := math.Floor(respdata.Data.Balance*100) / 100
		respdata.Data.Balance = balance
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 取消下注 事务处理失败  userId=", userId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
	if err != nil {
		logs.Error("RSG_single 取消下注 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 取消下注")
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 取消下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 取消下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}
