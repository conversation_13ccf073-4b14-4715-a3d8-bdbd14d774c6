package tronapi

import (
	"github.com/imroc/req"
	"time"
)

const (
	baseUrl  = "https://apilist.tronscanapi.com/api"
	apikey   = "4ce9dd2f-1981-495a-86cc-77f42dc475df"
	interval = time.Millisecond * 200
)

var (
	//keys   = [3]string{"1ce6e82d-d251-437d-b039-04cd698373ad", "beec7111-3912-4dc1-82a1-29c1747887f2", "8bdc9c2a-2c3b-4816-a688-61c43e0d9e77"}
	client *TronScanApi
)

type TronScanApi struct {
	baseUrl         string
	apikey          string
	lastRequestTime time.Time
	//keys      [3]string
	//keysIndex int
	Account
	Transaction
}

func Client() *TronScanApi {
	if client == nil {
		client = &TronScanApi{
			baseUrl: baseUrl,
			apikey:  apikey,
			//keys:    keys,
		}
	}
	return client
}

func (c *TronScanApi) HttpGet(url string, v ...interface{}) (*req.Resp, error) {
	header := req.Header{
		//"TRON-PRO-API-KEY": keys[c.keysIndex%len(c.keys)],
		"TRON-PRO-API-KEY": apikey,
	}
	now := time.Now()
	if now.Sub(c.lastRequestTime) < interval {
		time.Sleep(interval - now.Sub(c.lastRequestTime))
	}
	c.lastRequestTime = time.Now()
	//c.keysIndex++
	var v1 []any
	v1 = append(v1, header)
	v1 = append(v1, v...)
	return req.Get(url, v1...)
}

func (c *TronScanApi) HttpPost(url string, v ...interface{}) (*req.Resp, error) {
	header := req.Header{
		//"TRON-PRO-API-KEY": keys[c.keysIndex%len(c.keys)],
		"TRON-PRO-API-KEY": apikey,
	}
	now := time.Now()
	if now.Sub(c.lastRequestTime) < interval {
		time.Sleep(interval - now.Sub(c.lastRequestTime))
	}
	c.lastRequestTime = time.Now()
	//c.keysIndex++
	var v1 []any
	v1 = append(v1, header)
	v1 = append(v1, v...)
	return req.Post(url, v1...)
}
