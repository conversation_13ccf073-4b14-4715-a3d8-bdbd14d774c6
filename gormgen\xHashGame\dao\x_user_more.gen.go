// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserMore(db *gorm.DB, opts ...gen.DOOption) xUserMore {
	_xUserMore := xUserMore{}

	_xUserMore.xUserMoreDo.UseDB(db, opts...)
	_xUserMore.xUserMoreDo.UseModel(&model.XUserMore{})

	tableName := _xUserMore.xUserMoreDo.TableName()
	_xUserMore.ALL = field.NewAsterisk(tableName)
	_xUserMore.UserID = field.NewInt32(tableName, "UserId")
	_xUserMore.Fbp = field.NewString(tableName, "Fbp")
	_xUserMore.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserMore.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserMore.fillFieldMap()

	return _xUserMore
}

type xUserMore struct {
	xUserMoreDo xUserMoreDo

	ALL        field.Asterisk
	UserID     field.Int32  // 玩家
	Fbp        field.String // fbp
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserMore) Table(newTableName string) *xUserMore {
	x.xUserMoreDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserMore) As(alias string) *xUserMore {
	x.xUserMoreDo.DO = *(x.xUserMoreDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserMore) updateTableName(table string) *xUserMore {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.Fbp = field.NewString(table, "Fbp")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserMore) WithContext(ctx context.Context) *xUserMoreDo {
	return x.xUserMoreDo.WithContext(ctx)
}

func (x xUserMore) TableName() string { return x.xUserMoreDo.TableName() }

func (x xUserMore) Alias() string { return x.xUserMoreDo.Alias() }

func (x xUserMore) Columns(cols ...field.Expr) gen.Columns { return x.xUserMoreDo.Columns(cols...) }

func (x *xUserMore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserMore) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 4)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Fbp"] = x.Fbp
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserMore) clone(db *gorm.DB) xUserMore {
	x.xUserMoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserMore) replaceDB(db *gorm.DB) xUserMore {
	x.xUserMoreDo.ReplaceDB(db)
	return x
}

type xUserMoreDo struct{ gen.DO }

func (x xUserMoreDo) Debug() *xUserMoreDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserMoreDo) WithContext(ctx context.Context) *xUserMoreDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserMoreDo) ReadDB() *xUserMoreDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserMoreDo) WriteDB() *xUserMoreDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserMoreDo) Session(config *gorm.Session) *xUserMoreDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserMoreDo) Clauses(conds ...clause.Expression) *xUserMoreDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserMoreDo) Returning(value interface{}, columns ...string) *xUserMoreDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserMoreDo) Not(conds ...gen.Condition) *xUserMoreDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserMoreDo) Or(conds ...gen.Condition) *xUserMoreDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserMoreDo) Select(conds ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserMoreDo) Where(conds ...gen.Condition) *xUserMoreDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserMoreDo) Order(conds ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserMoreDo) Distinct(cols ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserMoreDo) Omit(cols ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserMoreDo) Join(table schema.Tabler, on ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserMoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserMoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserMoreDo) Group(cols ...field.Expr) *xUserMoreDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserMoreDo) Having(conds ...gen.Condition) *xUserMoreDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserMoreDo) Limit(limit int) *xUserMoreDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserMoreDo) Offset(offset int) *xUserMoreDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserMoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserMoreDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserMoreDo) Unscoped() *xUserMoreDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserMoreDo) Create(values ...*model.XUserMore) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserMoreDo) CreateInBatches(values []*model.XUserMore, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserMoreDo) Save(values ...*model.XUserMore) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserMoreDo) First() (*model.XUserMore, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserMore), nil
	}
}

func (x xUserMoreDo) Take() (*model.XUserMore, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserMore), nil
	}
}

func (x xUserMoreDo) Last() (*model.XUserMore, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserMore), nil
	}
}

func (x xUserMoreDo) Find() ([]*model.XUserMore, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserMore), err
}

func (x xUserMoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserMore, err error) {
	buf := make([]*model.XUserMore, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserMoreDo) FindInBatches(result *[]*model.XUserMore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserMoreDo) Attrs(attrs ...field.AssignExpr) *xUserMoreDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserMoreDo) Assign(attrs ...field.AssignExpr) *xUserMoreDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserMoreDo) Joins(fields ...field.RelationField) *xUserMoreDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserMoreDo) Preload(fields ...field.RelationField) *xUserMoreDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserMoreDo) FirstOrInit() (*model.XUserMore, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserMore), nil
	}
}

func (x xUserMoreDo) FirstOrCreate() (*model.XUserMore, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserMore), nil
	}
}

func (x xUserMoreDo) FindByPage(offset int, limit int) (result []*model.XUserMore, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserMoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserMoreDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserMoreDo) Delete(models ...*model.XUserMore) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserMoreDo) withDO(do gen.Dao) *xUserMoreDo {
	x.DO = *do.(*gen.DO)
	return x
}
