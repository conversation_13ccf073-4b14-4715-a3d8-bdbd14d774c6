package third_game

// x_user
type UserBalance struct {
	UserId      int     `json:"UserId" gorm:"column:UserId"`
	SellerId    int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId   int     `json:"ChannelId" gorm:"column:ChannelId"`
	Amount      float64 `json:"Amount" gorm:"column:Amount"`
	IsTest      int     `json:"IsTest" gorm:"column:IsTest"` //`IsTest` int DEFAULT '2' COMMENT '是否是测试账号,1是,2不是'
	Token       string  `json:"Token" gorm:"column:Token"`
	State       int     `json:"State" gorm:"column:State"`              //1 启用 2禁用
	BonusAmount float64 `json:"BonusBalance" gorm:"column:BonusAmount"` // Bonus币余额
}
