package controller

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"

	daoModel "xserver/gormgen/xHashGame/model"
	"xserver/model"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/zeromicro/go-zero/core/threading"

	daogorm "gorm.io/gorm"
)

type UtOrderController struct {
}

// Init ...
func (this *UtOrderController) Init() {
	// 限制访问次数,和签名验证
	//server.Http().Gin().Use(utils.LimitHandler(100), utils.CheckSign())
	server.Http().Gin().GET("/api/ut/recharge/order", utils.CheckSign(), utils.LimitHandler(100), this.GetRechargeOrder)
	server.Http().Gin().GET("/api/ut/withdraw/order", utils.CheckSign(), utils.LimitHandler(100), this.GetWithdrawOrder)
	server.Http().Gin().GET("/api/ut/order/list", utils.CheckSign(), utils.LimitHandler(100), this.GetOrderList)
	server.Http().Gin().POST("/api/ut/tranfer_game_record", utils.CheckSignBody(), this.tranfer_game_record)
	server.Http().Gin().POST("/api/ut/update_user_email", utils.CheckSignBody(), this.update_user_email)
	server.Http().Gin().POST("/api/ut/check_user_email", utils.CheckSignBody(), this.check_user_email)
	server.Http().Gin().POST("/api/ut/get_user_agent_code", this.get_user_agent_code)
	server.Http().Gin().POST("/api/ut/agent/order", utils.CheckSignBody(), this.GetAgentOrder)
	server.Http().Gin().POST("/api/ut/recharge_withdraw_report", utils.CheckSignBody(), this.GetRechargeWithdrawReport)
	// server.Http().Gin().POST("/api/ut/tranfer_game_record", this.tranfer_game_record)
	//gropu := server.Http().NewGroup("/api/ut/")
	//{
	//	gropu.GetNoAuth("/recharge/order", utils.CheckSign(), utils.LimitHandler(100), this.GetRechargeOrder)
	//	gropu.GetNoAuth("/withdraw/order", utils.CheckSign(), utils.LimitHandler(100), this.GetWithdrawOrder)
	//}

	// threading.GoSafe(func() {
	// 	time.Sleep(10 * time.Second)
	// 	data := map[string]string{
	// 		"UserId":      "12345",
	// 		"TxId":        "adgaegrafagdf234523aerhg4",
	// 		"FromAddress": "123",
	// 		"ToAddress":   "1231",
	// 		"Amount":      "1232",
	// 		"Symbol":      "d",
	// 		"ReqTime":     "1233",
	// 	}
	// 	sign := utils.Sign(data)
	// 	data["Sign"] = sign
	// 	client := resty.New()
	// 	client.SetDebug(true)
	// 	resp, err := client.R().SetBody(data).Post("http://127.0.0.1:4533/api/ut/tranfer_game_record")
	// 	if err != nil {
	// 		logs.Error("ddddd ", err)
	// 	}
	// 	logs.Info(resp)
	// })

	// threading.GoSafe(func() {
	// 	time.Sleep(10 * time.Second)
	// 	data := map[string]string{
	// 		"Account": "440895_jjohn10",
	// 		"Email":   "<EMAIL>",
	// 	}
	// 	sign := utils.Sign(data)
	// 	data["Sign"] = sign
	// 	client := resty.New()
	// 	client.SetDebug(true)
	// 	resp, err := client.R().SetBody(data).Post("http://127.0.0.1:4533/api/ut/update_user_email")
	// 	if err != nil {
	// 		logs.Error("ddddd ", err)
	// 	}
	// 	logs.Info(resp)
	// })

	// threading.GoSafe(func() {
	// 	time.Sleep(10 * time.Second)
	// 	data := map[string]string{
	// 		"Email": "<EMAIL>",
	// 	}
	// 	sign := utils.Sign(data)
	// 	data["Sign"] = sign
	// 	client := resty.New()
	// 	client.SetDebug(true)
	// 	resp, err := client.R().SetBody(data).Post("http://127.0.0.1:4533/api/ut/check_user_email")
	// 	if err != nil {
	// 		logs.Error("ddddd ", err)
	// 	}
	// 	logs.Info(resp)
	// })

	// threading.GoSafe(func() {
	// 	time.Sleep(10 * time.Second)
	// 	data := map[string]string{
	// 		"Account": "qwe1122",
	// 	}
	// 	sign := utils.Sign(data)
	// 	data["Sign"] = sign
	// 	client := resty.New()
	// 	client.SetDebug(true)
	// 	resp, err := client.R().SetBody(data).Post("http://127.0.0.1:4533/api/ut/get_user_agent_code")
	// 	if err != nil {
	// 		logs.Error("ddddd ", err)
	// 	}
	// 	logs.Info(resp)
	// })
}

func (this *UtOrderController) GetRechargeOrder(ctx *gin.Context) {
	page := ctx.DefaultQuery("page", "1")
	pagesize := ctx.DefaultQuery("pagesize", "30")
	userId := ctx.DefaultQuery("userId", "")
	startTimeStr := ctx.DefaultQuery("start_time", "")
	endTimeStr := ctx.DefaultQuery("end_time", "")
	state := ctx.DefaultQuery("state", "")
	sellerId := ctx.DefaultQuery("sellerId", "")

	if pagesize >= "200" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "每次最多查询200条数据",
		})
		return
	}

	// 判断开始时间和结束时间是否为空
	if startTimeStr == "" || endTimeStr == "" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "开始时间和结束时间不能为空",
		})
		return
	}

	// 开始时间和结束时间跨度不能超过30天
	// 获取开始时间时间戳
	startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	if err != nil {
		logs.Info("Error parsing start time:", err)
		return
	}

	endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	if err != nil {
		logs.Info("Error parsing end time:", err)
		return
	}

	// 计算时间跨度
	duration := endTime.Sub(startTime)
	// 检查时间跨度是否超过30天
	if duration.Hours() > 62*24 {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "时间跨度不能超过两个月",
		})
		return
	}

	pageFormat, _ := strconv.Atoi(page)
	pagesizeFormat, _ := strconv.Atoi(pagesize)

	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", startTime, "")
	where.Add("and", "CreateTime", "<=", endTime, "")
	where.Add("and", "State", "=", state, "")
	where.Add("and", "SellerId", "=", sellerId, "")
	if userId != "" {
		where.Add("and", "UserId", "=", userId, "")
	} else {
		where.Add("and", "UserId", ">", 0, "")
	}

	total, result := server.Db().Table("x_recharge").Select("Id, UserId, SellerId, Symbol, Amount, RealAmount, Net, TxId, CreateTime, State").Where(where).OrderBy("id desc").PageData(pageFormat, pagesizeFormat)
	//ctx.Put("total", total)
	//ctx.Put("data", *result)
	//ctx.RespOK()
	ctx.JSON(200, gin.H{
		"code": 200,
		"data": map[string]interface{}{
			"total": total,
			"list":  *result,
		},
	})
}

func (this *UtOrderController) GetWithdrawOrder(ctx *gin.Context) {
	page := ctx.DefaultQuery("page", "1")
	pagesize := ctx.DefaultQuery("pagesize", "30")
	userId := ctx.DefaultQuery("userId", "")
	startTimeStr := ctx.DefaultQuery("start_time", "")
	endTimeStr := ctx.DefaultQuery("end_time", "")
	state := ctx.DefaultQuery("state", "")
	sellerId := ctx.DefaultQuery("sellerId", "")

	if pagesize >= "200" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "每次最多查询200条数据",
		})
		return
	}

	// 判断开始时间和结束时间是否为空
	if startTimeStr == "" || endTimeStr == "" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "开始时间和结束时间不能为空",
		})
		return
	}

	// 开始时间和结束时间跨度不能超过30天
	// 获取开始时间时间戳
	startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
	if err != nil {
		logs.Info("Error parsing start time:", err)
		return
	}

	endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
	if err != nil {
		logs.Info("Error parsing end time:", err)
		return
	}

	// 计算时间跨度
	duration := endTime.Sub(startTime)
	// 检查时间跨度是否超过30天
	if duration.Hours() > 62*24 {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "时间跨度不能超过两个月",
		})
		return
	}

	pageFormat, _ := strconv.Atoi(page)
	pagesizeFormat, _ := strconv.Atoi(pagesize)

	where := abugo.AbuDbWhere{}
	where.Add("and", "CreateTime", ">=", startTime, "")
	where.Add("and", "CreateTime", "<=", endTime, "")
	where.Add("and", "State", "=", state, "")
	where.Add("and", "SellerId", "=", sellerId, "")
	if userId != "" {
		where.Add("and", "UserId", "=", userId, "")
	} else {
		where.Add("and", "UserId", ">", 0, "")
	}

	total, result := server.Db().Table("x_withdraw").Select("Id, UserId, SellerId, Symbol, Amount, RealAmount, Net, TxId, CreateTime, State").Where(where).OrderBy("id desc").PageData(pageFormat, pagesizeFormat)
	ctx.JSON(200, gin.H{
		"code": 200,
		"data": map[string]interface{}{
			"total": total,
			"list":  *result,
		},
	})
}

func (this *UtOrderController) GetOrderList(ctx *gin.Context) {
	userId := ctx.DefaultQuery("userId", "")
	period := ctx.DefaultQuery("period", "")
	if userId == "" || period == "" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "用户id和期数不能为空",
		})
		return
	}
	userIdInt, err := strconv.ParseInt(userId, 10, 64)
	if err != nil || userIdInt == 0 {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "用户id参数错误",
		})
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.Amount).
		Where(userTb.UserID.Eq(int32(userIdInt))).First()
	if err != nil {
		logs.Error("GetOrderList user err", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "系统错误,请稍后再试",
		})
		return
	}
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())
	orderList, err := orderDb.Select(orderTb.ID, orderTb.GameID, orderTb.Memo,
		orderTb.RewardAmount, orderTb.CreateTime, orderTb.BlockHash, orderTb.BlockNum,
		orderTb.Symbol, orderTb.Amount, orderTb.IsWin, orderTb.State, orderTb.Period,
		orderTb.NextBlockHash, orderTb.BetArea, orderTb.TxID, orderTb.OpenArea).
		Where(orderTb.UserID.Eq(int32(userIdInt))).
		Where(orderTb.Period.Eq(period)).Find()
	if err != nil {
		logs.Error("GetOrderList orderList err", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "系统错误,请稍后再试",
		})
		return
	}
	var orderUtList = make([]model.UtOrderList, 0)
	if len(orderList) > 0 {
		for _, v := range orderList {
			var data model.UtOrderList
			data.Id = v.ID
			data.GameId = v.GameID
			data.Memo = v.Memo
			data.RewardAmount = v.RewardAmount
			data.CreateTime = v.CreateTime.Format(utils.TimeFormatStr)
			data.BlockHash = v.BlockHash
			data.BlockNum = v.BlockNum
			data.Symbol = v.Symbol
			data.Amount = v.Amount
			data.IsWin = v.IsWin
			data.State = v.State
			data.Period = v.Period
			data.NextBlockHash = v.NextBlockHash
			data.BetArea = v.BetArea
			data.TxId = v.TxID
			data.OpenArea = v.OpenArea
			data.UserAmount = user.Amount
			orderUtList = append(orderUtList, data)
		}
	}
	ctx.JSON(200, gin.H{
		"code": 200,
		"data": map[string]interface{}{
			"list": orderUtList,
		},
	})
}

func (s *UtOrderController) tranfer_game_record(ctx *gin.Context) {
	defer recover()
	type RequestData struct {
		UserId      string
		TxId        string
		FromAddress string
		ToAddress   string
		Amount      string
		Symbol      string
		ReqTime     string // 请求北京时间
	}
	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("tranfer_game_record ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}
	data := daoModel.XTransationUt{
		UserID:      int32(abugo.GetInt64FromInterface(req.UserId)),
		TxID:        req.TxId,
		FromAddress: req.FromAddress,
		ToAddress:   req.ToAddress,
		Amount:      abugo.GetFloat64FromInterface(req.Amount),
		Symbol:      req.Symbol,
		ReqTime:     carbon.Parse(req.ReqTime).StdTime(),
	}
	result := server.Db().GormDao().Create(&data)
	if result.Error != nil {
		logs.Error("tranfer_game_record create error ", result.Error)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "insert error",
		})
		return
	}
	if data.ID%1000 == 0 {
		threading.GoSafe(func() {
			limit := 100000
			minId := 0
			result := server.Db().GormDao().Raw(`
				WITH t AS (
					SELECT id FROM x_transation_ut ORDER BY id DESC LIMIT ?
				), l AS (
					SELECT COUNT(*) AS num FROM x_transation_ut
				), r AS (
					SELECT MIN(id) AS minId FROM t
				)
				SELECT minId FROM r JOIN l WHERE num > ?
			`, limit, limit).Scan(&minId)
			if result.Error != nil {
				logs.Error("tranfer_game_record minid", result.Error)
			}
			if minId > 0 {
				err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
					r := tx.Exec("INSERT INTO x_transation_ut_backup SELECT * FROM x_transation_ut WHERE id < ?", minId)
					if r.Error != nil {
						return r.Error
					}
					d := tx.Exec("delete from x_transation_ut where id < ?", minId)
					if d.Error != nil {
						return d.Error
					}
					if r.RowsAffected != d.RowsAffected {
						logs.Error("tranfer_game_record backup error %d %d ", r.RowsAffected, d.RowsAffected)
						return errors.New("r.RowsAffected != d.RowsAffected")
					}
					return nil
				})
				if err != nil {
					logs.Error("tranfer_game_record Transaction", err)
				}
			}
		})
	}
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}

func (s *UtOrderController) update_user_email(ctx *gin.Context) {
	defer recover()
	type RequestData struct {
		Account string `validate:"required"`
		Email   string `validate:"required"`
	}
	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("update_user_email ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}
	result := server.Db().GormDao().Table("x_user").Where("Account = ?", req.Account).UpdateColumn("Email", req.Email)
	if result.Error != nil {
		logs.Error("update_user_email update err ", result.Error)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "update error",
		})
		return
	}
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}

func (s *UtOrderController) check_user_email(ctx *gin.Context) {
	defer recover()
	type RequestData struct {
		Email string `validate:"required"`
	}
	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil || len(req.Email) < 4 {
		logs.Error("check_user_email ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}
	tb := server.DaoxHashGame().XUser
	data, err := tb.WithContext(context.Background()).Where(tb.Email.Eq(req.Email)).Limit(1).Find()
	if err != nil {
		logs.Error("check_user_email update err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "find error",
		})
		return
	}

	logs.Info("fffff %+v", data)

	exist := 2
	if len(data) == 0 {
		exist = 1
	}
	ctx.JSON(200, gin.H{
		"code":  200,
		"msg":   "success",
		"exist": exist, // 1 不存在 2 存在
	})
}

func (s *UtOrderController) get_user_agent_code(ctx *gin.Context) {
	defer recover()
	type RequestData struct {
		Account string `validate:"required"`
		Host    string
	}
	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil || len(req.Account) < 2 {
		logs.Error("get_user_agent_code ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}

	host := ctx.Request.Host
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if req.Host != "" {
		host = req.Host
	}

	xChannelHost := server.DaoxHashGame().XChannelHost
	xChannel := server.DaoxHashGame().XChannel
	accountArr := strings.Split(req.Account, ",")

	type hostInfoData struct {
		SellerId int
	}
	var hostInfo hostInfoData
	err = xChannelHost.WithContext(nil).Select(xChannel.SellerID).Where(xChannelHost.Host.Eq(host)).LeftJoin(xChannel, xChannel.ChannelID.EqCol(xChannelHost.ChannelID)).Scan(&hostInfo)
	if err != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "get seller error",
		})
		return
	}

	xUserTb := server.DaoxHashGame().XUser
	xUserDb := xUserTb.WithContext(context.Background())
	query := xUserDb.Where(xUserTb.SellerID.Eq(int32(hostInfo.SellerId)))
	if len(accountArr) == 1 {
		user, err := query.Where(xUserTb.Account.Eq(req.Account)).First()
		if err != nil {
			logs.Error("logs agent_generate_lose_code user ", req, err)
			ctx.JSON(200, gin.H{
				"code": 0,
				"msg":  "user not existed",
			})
			return
		}
		r := struct {
			AgentCode int
		}{}
		server.Db().GormDao().Raw(`call x_api_agent_get_info(?, "usdt")`, user.UserID).Scan(&r)
		if r.AgentCode == 0 {
			logs.Error("logs agent_generate_lose_code x_api_agent_get_info ", req, r)
			ctx.JSON(200, gin.H{
				"code": 0,
				"msg":  "generate agent code error",
			})
			return
		}
		ctx.JSON(200, gin.H{
			"code":      200,
			"msg":       "success",
			"agentCode": r.AgentCode, // 1 不存在 2 存在
			"userId":    user.UserID,
		})
	}

	if len(accountArr) > 1 {
		//userAccounts := strings.Join(accountArr, ",")
		users, err := query.Where(xUserTb.Account.In(accountArr...)).Find()
		if err != nil {
			logs.Error("logs agent_generate_lose_code user ", req, err)
			ctx.JSON(200, gin.H{
				"code": 0,
				"msg":  "user not existed",
			})
			return
		}
		type result struct {
			AgentCode int    `json:"agentCode"`
			UserId    int32  `json:"userId"`
			Account   string `json:"account"`
		}

		var results []result
		for _, user := range users {
			r := struct {
				AgentCode int
			}{}
			server.Db().GormDao().Raw(`call x_api_agent_get_info(?, "usdt")`, user.UserID).Scan(&r)
			if r.AgentCode == 0 {
				continue
			}
			results = append(results, result{AgentCode: r.AgentCode, UserId: user.UserID, Account: user.Account})
		}
		ctx.JSON(200, gin.H{
			"code": 200,
			"msg":  "success",
			"data": results,
		})
	}

	//user, err := xUserDb.Where(xUserTb.Account.Eq(req.Account)).Where(xUserTb.SellerID.Eq(int32(hostInfo.SellerId))).First()
	//if err != nil {
	//	logs.Error("logs agent_generate_lose_code user ", req, err)
	//	ctx.JSON(200, gin.H{
	//		"code": 0,
	//		"msg":  "user not existed",
	//	})
	//	return
	//}
	//r := struct {
	//	AgentCode int
	//}{}
	//server.Db().GormDao().Raw(`call x_api_agent_get_info(?, "usdt")`, user.UserID).Scan(&r)
	//if r.AgentCode == 0 {
	//	logs.Error("logs agent_generate_lose_code x_api_agent_get_info ", req, r)
	//	ctx.JSON(200, gin.H{
	//		"code": 0,
	//		"msg":  "generate agent code error",
	//	})
	//	return
	//}
	//ctx.JSON(200, gin.H{
	//	"code":      200,
	//	"msg":       "success",
	//	"agentCode": r.AgentCode, // 1 不存在 2 存在
	//	"userId":    user.UserID,
	//})
}

func (s *UtOrderController) GetAgentOrder(ctx *gin.Context) {
	defer recover()
	type RequestData struct {
		TopAgentId int    `validate:"required"`
		StartTime  string `validate:"required"`
		EndTime    string `validate:"required"`
	}
	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}

	var result struct {
		RechargeUsers int     `gorm:"column:recharge_users"`
		TotalRecharge float64 `gorm:"column:total_recharge"`
		WithdrawUsers int     `gorm:"column:withdraw_users"`
		TotalWithdraw float64 `gorm:"column:total_withdraw"`
	}

	query := `
        SELECT
          COUNT(DISTINCT CASE WHEN RechargeAmount > 0 THEN UserId END) AS recharge_users,
          SUM(RechargeAmount) AS total_recharge,
          COUNT(DISTINCT CASE WHEN WithdrawAmount > 0 THEN UserId END) AS withdraw_users,
          SUM(WithdrawAmount) AS total_withdraw
        FROM (
          SELECT
            UserId, RechargeAmount, WithdrawAmount
          FROM x_user_recharge_withard_date
          WHERE TopAgentId = ? AND RecordDate >= ? AND RecordDate < ?
          UNION ALL
          SELECT
            UserId, RechargeAmount, WithdrawAmount
          FROM x_user_recharge_withard_date
          WHERE UserId = ? AND RecordDate >= ? AND RecordDate < ?
        ) AS A`

	//params := make([]interface{}, 0)
	//params = append(params, req.TopAgentId, req.StartTime, req.EndTime, req.TopAgentId, req.StartTime, req.EndTime)
	//logs.Info(query, req.TopAgentId, req.StartTime, req.EndTime, req.TopAgentId, req.StartTime, req.EndTime)
	err = server.Db().GormDao().Raw(query, req.TopAgentId, req.StartTime, req.EndTime, req.TopAgentId, req.StartTime, req.EndTime).Scan(&result).Error
	if err != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(200, result)
}

func (s *UtOrderController) GetRechargeWithdrawReport(ctx *gin.Context) {
	type RequestData struct {
		UserIds   []int32 `validate:"required"`
		StartTime string  `validate:"required"`
		EndTime   string  `validate:"required"`
	}

	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}

	if req.StartTime == "" || req.EndTime == "" {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "开始时间和结束时间不能为空",
		})
		return
	}

	startTime := carbon.Parse(req.StartTime)
	endTime := carbon.Parse(req.EndTime)

	type Result struct {
		UserId        int32   `json:"user_id"`
		RechargeTotal float64 `json:"recharge_total"`
		WithdrawTotal float64 `json:"withdraw_total"`
	}

	results := make([]Result, 0)

	xRecharge := server.DaoxHashGame().XRecharge
	xWithdraw := server.DaoxHashGame().XWithdraw

	for _, userId := range req.UserIds {
		var rechargeResult struct {
			RechargeTotal float64 `gorm:"column:RechargeTotal"`
		}

		err = xRecharge.WithContext(nil).
			Select(xRecharge.RealAmount.Sum().As("RechargeTotal")).
			Where(xRecharge.State.Eq(5)).
			Where(xRecharge.CreateTime.Between(startTime.StdTime(), endTime.StdTime())).
			Where(xRecharge.UserID.Eq(userId)).
			Scan(&rechargeResult)
		if err != nil {
			ctx.JSON(200, gin.H{
				"code": 0,
				"msg":  "mysql err",
			})
			return
		}

		var withdrawResult struct {
			WithdrawTotal float64 `gorm:"column:WithdrawTotal"`
		}

		err = xWithdraw.WithContext(nil).
			Select(xWithdraw.RealAmount.Sum().As("WithdrawTotal")).
			Where(xWithdraw.State.Eq(6)).
			Where(xWithdraw.CreateTime.Between(startTime.StdTime(), endTime.StdTime())).
			Where(xWithdraw.UserID.Eq(userId)).
			Scan(&withdrawResult)
		if err != nil {
			ctx.JSON(200, gin.H{
				"code": 0,
				"msg":  "mysql err",
			})
			return
		}

		results = append(results, Result{
			UserId:        userId,
			RechargeTotal: rechargeResult.RechargeTotal,
			WithdrawTotal: withdrawResult.WithdrawTotal,
		})
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
		"data": results,
	})
}
