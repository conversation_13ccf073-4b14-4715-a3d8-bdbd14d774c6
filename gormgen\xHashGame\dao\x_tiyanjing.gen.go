// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTiyanjing(db *gorm.DB, opts ...gen.DOOption) xTiyanjing {
	_xTiyanjing := xTiyanjing{}

	_xTiyanjing.xTiyanjingDo.UseDB(db, opts...)
	_xTiyanjing.xTiyanjingDo.UseModel(&model.XTiyanjing{})

	tableName := _xTiyanjing.xTiyanjingDo.TableName()
	_xTiyanjing.ALL = field.NewAsterisk(tableName)
	_xTiyanjing.ID = field.NewInt32(tableName, "Id")
	_xTiyanjing.SellerID = field.NewInt32(tableName, "SellerId")
	_xTiyanjing.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTiyanjing.UserID = field.NewInt32(tableName, "UserId")
	_xTiyanjing.Address = field.NewString(tableName, "Address")
	_xTiyanjing.CSGroup = field.NewString(tableName, "CSGroup")
	_xTiyanjing.CSID = field.NewString(tableName, "CSId")
	_xTiyanjing.TgName = field.NewString(tableName, "TgName")
	_xTiyanjing.IPCount = field.NewInt32(tableName, "IpCount")
	_xTiyanjing.LoginIPCount = field.NewInt32(tableName, "LoginIpCount")
	_xTiyanjing.PwdCount = field.NewInt32(tableName, "PwdCount")
	_xTiyanjing.AddressCount = field.NewInt32(tableName, "AddressCount")
	_xTiyanjing.Symbol = field.NewString(tableName, "Symbol")
	_xTiyanjing.Amount = field.NewFloat64(tableName, "Amount")
	_xTiyanjing.Games = field.NewString(tableName, "Games")
	_xTiyanjing.State = field.NewInt32(tableName, "State")
	_xTiyanjing.Memo = field.NewString(tableName, "Memo")
	_xTiyanjing.KeFuTgName = field.NewString(tableName, "KeFuTgName")
	_xTiyanjing.BetCountTransfer = field.NewString(tableName, "BetCountTransfer")
	_xTiyanjing.BetAmountTransfer = field.NewString(tableName, "BetAmountTransfer")
	_xTiyanjing.RewardAmountTransfer = field.NewString(tableName, "RewardAmountTransfer")
	_xTiyanjing.BetCountYue = field.NewInt32(tableName, "BetCountYue")
	_xTiyanjing.BetAmountYue = field.NewFloat64(tableName, "BetAmountYue")
	_xTiyanjing.RewardAmountYue = field.NewFloat64(tableName, "RewardAmountYue")
	_xTiyanjing.BetCountThird = field.NewInt32(tableName, "BetCountThird")
	_xTiyanjing.BetAmountThird = field.NewFloat64(tableName, "BetAmountThird")
	_xTiyanjing.RewardAmountThird = field.NewFloat64(tableName, "RewardAmountThird")
	_xTiyanjing.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTiyanjing.HbcOrder = field.NewString(tableName, "HbcOrder")
	_xTiyanjing.HbcState = field.NewInt32(tableName, "HbcState")
	_xTiyanjing.LoginTime = field.NewTime(tableName, "LoginTime")
	_xTiyanjing.BetTime = field.NewTime(tableName, "BetTime")
	_xTiyanjing.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xTiyanjing.AuditTime = field.NewTime(tableName, "AuditTime")
	_xTiyanjing.SendAccount = field.NewString(tableName, "SendAccount")
	_xTiyanjing.SendTime = field.NewTime(tableName, "SendTime")
	_xTiyanjing.Account = field.NewString(tableName, "Account")
	_xTiyanjing.RegisterTime = field.NewTime(tableName, "RegisterTime")
	_xTiyanjing.TxID = field.NewString(tableName, "TxId")
	_xTiyanjing.ApplyType = field.NewInt32(tableName, "ApplyType")
	_xTiyanjing.OperUserAccount = field.NewString(tableName, "OperUserAccount")
	_xTiyanjing.OperUserID = field.NewInt32(tableName, "OperUserId")
	_xTiyanjing.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTiyanjing.fillFieldMap()

	return _xTiyanjing
}

type xTiyanjing struct {
	xTiyanjingDo xTiyanjingDo

	ALL                  field.Asterisk
	ID                   field.Int32 // 自增Id
	SellerID             field.Int32 // 运营商
	ChannelID            field.Int32 // 渠道
	UserID               field.Int32
	Address              field.String  // 地址
	CSGroup              field.String  // 客服团队
	CSID                 field.String  // 客服工号
	TgName               field.String  // 玩家tg
	IPCount              field.Int32   // 同ip注册人数
	LoginIPCount         field.Int32   // 同ip登录人数
	PwdCount             field.Int32   // 同密码注册人数
	AddressCount         field.Int32   // 钱包地址相同人数
	Symbol               field.String  // 币种
	Amount               field.Float64 // 金额
	Games                field.String  // 玩过哪些游戏
	State                field.Int32   // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
	Memo                 field.String  // 备注
	KeFuTgName           field.String  // 客服tg
	BetCountTransfer     field.String  // 转账下注次数
	BetAmountTransfer    field.String  // 转账下注金额
	RewardAmountTransfer field.String  // 转账返奖金额
	BetCountYue          field.Int32   // 余额下注次数
	BetAmountYue         field.Float64 // 余额下注金额
	RewardAmountYue      field.Float64 // 余额返奖金额
	BetCountThird        field.Int32   // 三方下注次数
	BetAmountThird       field.Float64 // 三方下注金额
	RewardAmountThird    field.Float64 // 三方返奖金额
	CreateTime           field.Time    // 申请时间
	HbcOrder             field.String  // hbc订单
	HbcState             field.Int32   // hbc状态
	LoginTime            field.Time    // 最后登录时间
	BetTime              field.Time    // 最后投注时间
	AuditAccount         field.String  // 审核人
	AuditTime            field.Time    // 审核时间
	SendAccount          field.String  // 发放人
	SendTime             field.Time    // 发放时间
	Account              field.String  // 玩家账号
	RegisterTime         field.Time    // 玩家注册时间
	TxID                 field.String  // 交易id
	ApplyType            field.Int32   // 申请分类 1用户申请 2客服申请
	OperUserAccount      field.String  // 操作员账号
	OperUserID           field.Int32   // 操作员账号Id
	UpdateTime           field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTiyanjing) Table(newTableName string) *xTiyanjing {
	x.xTiyanjingDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTiyanjing) As(alias string) *xTiyanjing {
	x.xTiyanjingDo.DO = *(x.xTiyanjingDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTiyanjing) updateTableName(table string) *xTiyanjing {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Address = field.NewString(table, "Address")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TgName = field.NewString(table, "TgName")
	x.IPCount = field.NewInt32(table, "IpCount")
	x.LoginIPCount = field.NewInt32(table, "LoginIpCount")
	x.PwdCount = field.NewInt32(table, "PwdCount")
	x.AddressCount = field.NewInt32(table, "AddressCount")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.Games = field.NewString(table, "Games")
	x.State = field.NewInt32(table, "State")
	x.Memo = field.NewString(table, "Memo")
	x.KeFuTgName = field.NewString(table, "KeFuTgName")
	x.BetCountTransfer = field.NewString(table, "BetCountTransfer")
	x.BetAmountTransfer = field.NewString(table, "BetAmountTransfer")
	x.RewardAmountTransfer = field.NewString(table, "RewardAmountTransfer")
	x.BetCountYue = field.NewInt32(table, "BetCountYue")
	x.BetAmountYue = field.NewFloat64(table, "BetAmountYue")
	x.RewardAmountYue = field.NewFloat64(table, "RewardAmountYue")
	x.BetCountThird = field.NewInt32(table, "BetCountThird")
	x.BetAmountThird = field.NewFloat64(table, "BetAmountThird")
	x.RewardAmountThird = field.NewFloat64(table, "RewardAmountThird")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.HbcOrder = field.NewString(table, "HbcOrder")
	x.HbcState = field.NewInt32(table, "HbcState")
	x.LoginTime = field.NewTime(table, "LoginTime")
	x.BetTime = field.NewTime(table, "BetTime")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AuditTime = field.NewTime(table, "AuditTime")
	x.SendAccount = field.NewString(table, "SendAccount")
	x.SendTime = field.NewTime(table, "SendTime")
	x.Account = field.NewString(table, "Account")
	x.RegisterTime = field.NewTime(table, "RegisterTime")
	x.TxID = field.NewString(table, "TxId")
	x.ApplyType = field.NewInt32(table, "ApplyType")
	x.OperUserAccount = field.NewString(table, "OperUserAccount")
	x.OperUserID = field.NewInt32(table, "OperUserId")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTiyanjing) WithContext(ctx context.Context) *xTiyanjingDo {
	return x.xTiyanjingDo.WithContext(ctx)
}

func (x xTiyanjing) TableName() string { return x.xTiyanjingDo.TableName() }

func (x xTiyanjing) Alias() string { return x.xTiyanjingDo.Alias() }

func (x xTiyanjing) Columns(cols ...field.Expr) gen.Columns { return x.xTiyanjingDo.Columns(cols...) }

func (x *xTiyanjing) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTiyanjing) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 43)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TgName"] = x.TgName
	x.fieldMap["IpCount"] = x.IPCount
	x.fieldMap["LoginIpCount"] = x.LoginIPCount
	x.fieldMap["PwdCount"] = x.PwdCount
	x.fieldMap["AddressCount"] = x.AddressCount
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["Games"] = x.Games
	x.fieldMap["State"] = x.State
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["KeFuTgName"] = x.KeFuTgName
	x.fieldMap["BetCountTransfer"] = x.BetCountTransfer
	x.fieldMap["BetAmountTransfer"] = x.BetAmountTransfer
	x.fieldMap["RewardAmountTransfer"] = x.RewardAmountTransfer
	x.fieldMap["BetCountYue"] = x.BetCountYue
	x.fieldMap["BetAmountYue"] = x.BetAmountYue
	x.fieldMap["RewardAmountYue"] = x.RewardAmountYue
	x.fieldMap["BetCountThird"] = x.BetCountThird
	x.fieldMap["BetAmountThird"] = x.BetAmountThird
	x.fieldMap["RewardAmountThird"] = x.RewardAmountThird
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["HbcOrder"] = x.HbcOrder
	x.fieldMap["HbcState"] = x.HbcState
	x.fieldMap["LoginTime"] = x.LoginTime
	x.fieldMap["BetTime"] = x.BetTime
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AuditTime"] = x.AuditTime
	x.fieldMap["SendAccount"] = x.SendAccount
	x.fieldMap["SendTime"] = x.SendTime
	x.fieldMap["Account"] = x.Account
	x.fieldMap["RegisterTime"] = x.RegisterTime
	x.fieldMap["TxId"] = x.TxID
	x.fieldMap["ApplyType"] = x.ApplyType
	x.fieldMap["OperUserAccount"] = x.OperUserAccount
	x.fieldMap["OperUserId"] = x.OperUserID
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTiyanjing) clone(db *gorm.DB) xTiyanjing {
	x.xTiyanjingDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTiyanjing) replaceDB(db *gorm.DB) xTiyanjing {
	x.xTiyanjingDo.ReplaceDB(db)
	return x
}

type xTiyanjingDo struct{ gen.DO }

func (x xTiyanjingDo) Debug() *xTiyanjingDo {
	return x.withDO(x.DO.Debug())
}

func (x xTiyanjingDo) WithContext(ctx context.Context) *xTiyanjingDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTiyanjingDo) ReadDB() *xTiyanjingDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTiyanjingDo) WriteDB() *xTiyanjingDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTiyanjingDo) Session(config *gorm.Session) *xTiyanjingDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTiyanjingDo) Clauses(conds ...clause.Expression) *xTiyanjingDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTiyanjingDo) Returning(value interface{}, columns ...string) *xTiyanjingDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTiyanjingDo) Not(conds ...gen.Condition) *xTiyanjingDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTiyanjingDo) Or(conds ...gen.Condition) *xTiyanjingDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTiyanjingDo) Select(conds ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTiyanjingDo) Where(conds ...gen.Condition) *xTiyanjingDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTiyanjingDo) Order(conds ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTiyanjingDo) Distinct(cols ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTiyanjingDo) Omit(cols ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTiyanjingDo) Join(table schema.Tabler, on ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTiyanjingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTiyanjingDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTiyanjingDo) Group(cols ...field.Expr) *xTiyanjingDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTiyanjingDo) Having(conds ...gen.Condition) *xTiyanjingDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTiyanjingDo) Limit(limit int) *xTiyanjingDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTiyanjingDo) Offset(offset int) *xTiyanjingDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTiyanjingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTiyanjingDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTiyanjingDo) Unscoped() *xTiyanjingDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTiyanjingDo) Create(values ...*model.XTiyanjing) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTiyanjingDo) CreateInBatches(values []*model.XTiyanjing, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTiyanjingDo) Save(values ...*model.XTiyanjing) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTiyanjingDo) First() (*model.XTiyanjing, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjing), nil
	}
}

func (x xTiyanjingDo) Take() (*model.XTiyanjing, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjing), nil
	}
}

func (x xTiyanjingDo) Last() (*model.XTiyanjing, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjing), nil
	}
}

func (x xTiyanjingDo) Find() ([]*model.XTiyanjing, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTiyanjing), err
}

func (x xTiyanjingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTiyanjing, err error) {
	buf := make([]*model.XTiyanjing, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTiyanjingDo) FindInBatches(result *[]*model.XTiyanjing, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTiyanjingDo) Attrs(attrs ...field.AssignExpr) *xTiyanjingDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTiyanjingDo) Assign(attrs ...field.AssignExpr) *xTiyanjingDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTiyanjingDo) Joins(fields ...field.RelationField) *xTiyanjingDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTiyanjingDo) Preload(fields ...field.RelationField) *xTiyanjingDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTiyanjingDo) FirstOrInit() (*model.XTiyanjing, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjing), nil
	}
}

func (x xTiyanjingDo) FirstOrCreate() (*model.XTiyanjing, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjing), nil
	}
}

func (x xTiyanjingDo) FindByPage(offset int, limit int) (result []*model.XTiyanjing, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTiyanjingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTiyanjingDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTiyanjingDo) Delete(models ...*model.XTiyanjing) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTiyanjingDo) withDO(do gen.Dao) *xTiyanjingDo {
	x.DO = *do.(*gen.DO)
	return x
}
