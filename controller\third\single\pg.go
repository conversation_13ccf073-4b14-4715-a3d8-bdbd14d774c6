package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/shopspring/decimal"
	daogorm "gorm.io/gorm"
)

type PGConfig struct {
	url                   string
	gameUrl               string
	operatorToken         string
	secretKey             string //Secret Key
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
	baseThirdService      *base.BaseThirdService
}

func NewPGLogic(params map[string]string, fc func(int) error) *PGConfig {

	return &PGConfig{
		url:                   params["url"],
		gameUrl:               params["gameurl"],
		operatorToken:         params["operator_token"],
		secretKey:             params["secret_key"],
		currency:              params["currency"],
		brandName:             "pg",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
		baseThirdService:      base.NewBaseThirdService("pg", params["currency"], base.GameTypeDianzi),
	}
}

const cacheKeyPG = "cacheKeyPG:"

// Login
func (l *PGConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId string `validate:"required"`
		Lang   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyPG, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	//sessionId
	sessionId := base.UserId2token(cacheKeyPG, token.UserId)
	ea := fmt.Sprintf("btt=1&ops=%v&l=%v", sessionId, reqdata.Lang)
	querydata := map[string]any{
		"operator_token": l.operatorToken,
		"secret_key":     l.secretKey,
		"currency":       l.currency,
		"path":           fmt.Sprintf("/%v/index.html", reqdata.GameId),
		"extra_args":     ea,
		"url_type":       "game-entry",
		"client_ip":      ctx.GetIp(),
	}
	urlreq := fmt.Sprintf("%s/external-game-launcher/api/v1/GetLaunchURLHTML?trace_id=%s", l.url, abugo.GetUuid())
	reqBytes, _ := json.Marshal(querydata)
	header := map[string]string{
		"Cache-Control": "no-cache, no-store, must-revalidate",
	}
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		RespType:   1,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if data != nil {
		//fmt.Println(data["data"])
		ctx.Put("html", data["data"])
	} else {
		ctx.Put("html", "")
	}
	ctx.RespOK()
}

type verifySessionReq struct {
	OperatorToken         string `form:"operator_token" json:"operator_token"`
	SecretKey             string `form:"secret_key" json:"secret_key"`
	OperatorPlayerSession string `form:"operator_player_session" json:"operator_player_session"`
	GameId                *int   `form:"game_id" json:"game_id"`
	Ip                    string `form:"ip" json:"ip"`
	CustomParameter       string `form:"custom_parameter" json:"custom_parameter"`
	BetType               *int   `form:"bet_type" json:"bet_type"`
}

type resp struct {
	Data  any `json:"data"`
	Error any `json:"error"`
}

type errorResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// VerifySession
func (l *PGConfig) VerifySession(ctx *abugo.AbuHttpContent) {
	authResp := resp{}
	req := verifySessionReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方VerifySession req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Error(l.brandName, "三方VerifySession req  data |  traceId ", string(reqBytes), traceId)

	//获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey || userId == -1 {
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	_, _, err = base.GetUserBalance(userId, cacheKeyPG)

	if err != nil {
		errorResps := errorResp{}
		errorResps.Code = 1200
		errorResps.Message = "内部服务器错误"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//成功返回
	type respData struct {
		PlayerName string `json:"player_name"`
		NickName   string `json:"nickname"`
		Currency   string `json:"currency"`
	}

	respdatas := respData{
		PlayerName: fmt.Sprint(userId),
		NickName:   fmt.Sprint(userId),
		Currency:   l.currency,
	}
	authResp.Data = respdatas
	ctx.RespJson(authResp)
	return
}

type cashReq struct {
	OperatorToken         string `form:"operator_token" json:"operator_token"`
	SecretKey             string `form:"secret_key" json:"secret_key"`
	PlayerName            string `form:"player_name" json:"player_name"`
	OperatorPlayerSession string `form:"operator_player_session" json:"operator_player_session"`
	GameId                *int   `form:"game_id" json:"game_id"`
}

// Cash
func (l *PGConfig) Cash(ctx *abugo.AbuHttpContent) {
	authResp := resp{}
	req := cashReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方Cash req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方Cash req  data |  traceId ", string(reqBytes), traceId)

	//获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey ||
		userId == -1 || req.PlayerName != fmt.Sprint(userId) {
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 使用 BaseThirdService 获取用户余额（真金+Bonus币）
	userBalance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		logs.Error(l.brandName, "获取用户余额失败 userId=", userId, " err=", err.Error())
		errorResps := errorResp{}
		errorResps.Code = 3005
		errorResps.Message = "玩家钱包不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 计算总余额（真金+Bonus币）并保留两位小数
	totalBalance := userBalance.Amount + userBalance.BonusAmount
	totalBalance = float64(int(totalBalance*100)) / 100

	//成功返回
	type respData struct {
		CurrencyCode  string  `json:"currency_code"`
		BalanceAmount float64 `json:"balance_amount"`
		UpdatedTime   int64   `json:"updated_time"`
	}

	respdatas := respData{
		CurrencyCode:  l.currency,
		BalanceAmount: totalBalance,
		UpdatedTime:   time.Now().UnixMilli(),
	}
	authResp.Data = respdatas
	ctx.RespJson(authResp)
	return
}

type transferInOutReq struct {
	OperatorToken         string   `form:"operator_token" json:"operator_token"`
	SecretKey             string   `form:"secret_key" json:"secret_key"`
	OperatorPlayerSession string   `form:"operator_player_session" json:"operator_player_session"`
	PlayerName            string   `form:"player_name" json:"player_name"`
	GameId                *int     `form:"game_id" json:"game_id"`
	ParentBetId           string   `form:"parent_bet_id" json:"parent_bet_id"`
	BetId                 string   `form:"bet_id" json:"bet_id"`
	CurrencyCode          string   `form:"currency_code" json:"currency_code"`
	BetAmount             *float64 `form:"bet_amount" json:"bet_amount"`
	WinAmount             *float64 `form:"win_amount" json:"win_amount"`
	TransferAmount        *float64 `form:"transfer_amount" json:"transfer_amount"`
	TransactionId         string   `form:"transaction_id" json:"transaction_id"`
	WalletType            string   `form:"wallet_type" json:"wallet_type"`
	BetType               *int     `form:"bet_type" json:"bet_type"`
	Platform              string   `form:"platform" json:"platform"`
	CreateTime            int      `form:"create_time" json:"create_time"`
	UpdatedTime           int64    `form:"updated_time" json:"updated_time"`
	IsValidateBet         bool     `form:"is_validate_bet" json:"is_validate_bet"`
	IsAdjustment          bool     `form:"is_adjustment" json:"is_adjustment"`
	IsParentZeroStake     bool     `form:"is_parent_zero_stake" json:"is_parent_zero_stake"`
	IsFeature             bool     `form:"is_feature" json:"is_feature"`
	IsFeatureBuy          bool     `form:"is_feature_buy" json:"is_feature_buy"`
	Iswager               bool     `form:"is_wager" json:"is_wager"`
	IsEndRound            bool     `form:"is_end_round" json:"is_end_round"`
	FreeGameTransactionId string   `form:"free_game_transaction_id" json:"free_game_transaction_id"`
	FreeGameName          string   `form:"free_game_name" json:"free_game_name"`
	FreeGameId            *int     `form:"free_game_id" json:"free_game_id"`
	IsMinusCount          bool     `form:"is_minus_count" json:"is_minus_count"`
	BonusTransactionId    string   `form:"bonus_transaction_id" json:"bonus_transaction_id"`
	BonusName             string   `form:"bonus_name" json:"bonus_name"`
	BonusId               *int     `form:"bonus_id" json:"bonus_id"`
	BonusBalanceAmount    *float64 `form:"bonus_balance_amount" json:"bonus_balance_amount"`
	BonusRatioAmount      *float64 `form:"bonus_ratio_amount" json:"bonus_ratio_amount"`
}

// 3004 玩家不存在
// 3005 玩家钱包不存在
// 3021 投注不存在
// 3033 投注失败
// 3202 玩家余额不足
// TransferInOut 处理 PG 游戏的下注和派彩，支持混合投注系统
func (l *PGConfig) TransferInOut(ctx *abugo.AbuHttpContent) {
	authResp := resp{}
	req := transferInOutReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方TransferInOut req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "InvalidRequest"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方TransferInOut req  data |  traceId ", string(reqBytes), traceId)

	// 响应数据结构
	type respData struct {
		CurrencyCode  string  `json:"currency_code"`
		BalanceAmount float64 `json:"balance_amount"`
		UpdatedTime   int64   `json:"updated_time"`
	}
	respDatas := respData{}
	respDatas.CurrencyCode = l.currency

	// 获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)
	if req.IsValidateBet || req.IsAdjustment {
		userId, _ = strconv.Atoi(req.PlayerName)
	}

	// 验证请求参数
	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey ||
		userId == -1 || req.PlayerName != fmt.Sprint(userId) {
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "InvalidRequest"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 检查此订单是否已经成功请求过了
	key := fmt.Sprintf("cacheKeyPG:trace_id:%s", req.TransactionId)
	before := server.Redis().Get(key)
	if before != nil {
		// 之前成功请求了，直接返回
		befores := string(before.([]byte)[:])
		err = base.MyJson.Unmarshal([]byte(befores), &authResp)
		v, _ := authResp.Data.(map[string]interface{})
		v["updated_time"] = req.UpdatedTime
		authResp.Data = v
		logs.Error("PG beforebytes Unmarshal", err)
		ctx.RespJson(authResp)
		return
	}

	// 准备请求数据
	thirdId := req.BetId
	gameId := ""
	gameName := ""
	if req.GameId != nil {
		gameId = fmt.Sprint(*req.GameId)
		gameName = fmt.Sprintf("PG游戏 %s", gameId)
	}

	// 验证转账金额
	k, _ := decimal.NewFromFloat(*req.WinAmount).Sub(decimal.NewFromFloat(*req.BetAmount)).Float64()
	if *req.TransferAmount != k {
		errorResps := errorResp{}
		errorResps.Code = 3073
		errorResps.Message = "BetFailedException"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 准备下注和派彩金额
	betAmount := *req.BetAmount
	winAmount := *req.WinAmount

	// 使用事务处理下注和派彩
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额并加锁
		userBalance, err := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if err != nil {
			logs.Error(l.brandName, "获取用户余额失败 userId=", userId, " err=", err.Error())
			return err
		}

		// 检查余额是否足够
		totalBalance := userBalance.Amount + userBalance.BonusAmount
		if totalBalance < betAmount {
			return errors.New("玩家余额不足")
		}

		// 查询订单是否存在
		var exists bool
		existingOrder, exists, err := l.baseThirdService.GetOrderForUpdate(tx, thirdId)
		if err != nil {
			logs.Error("PG 游戏 查询订单失败:", err.Error())
			return err
		}

		// 如果订单已存在，返回成功
		if exists && existingOrder != nil {
			logs.Info("PG 游戏 订单已存在:", thirdId)
			return nil
		}

		// 准备参数
		betMemo := fmt.Sprintf("PG游戏下注 thirdId:%s", thirdId)
		winMemo := fmt.Sprintf("PG游戏派彩 thirdId:%s", thirdId)
		currentTime := time.Now().Format("2006-01-02 15:04:05")
		rawData := string(reqBytes)

		// 使用 ProcessBetAndWin 处理下注和派彩
		_, err = l.baseThirdService.ProcessBetAndWin(
			tx,
			userBalance,
			betAmount,
			winAmount,
			utils.BalanceCReasonPGBet,
			utils.BalanceCReasonPGSettle,
			thirdId,
			gameId,
			gameName,
			betMemo,
			winMemo,
			currentTime,
			rawData,
			userBalance.ChannelId,
		)

		if err != nil {
			logs.Error(l.brandName, "处理下注和派彩失败 thirdId=", thirdId, " err=", err.Error())
			return err
		}

		// 更新订单的游戏记录链接
		betCtx := fmt.Sprintf("https://public.pg-staging.com/history/redirect.html?trace_id=%s&t=%%s&psid=%s&sid=%s&lang=zh&type=operator", abugo.GetUuid(), req.ParentBetId, req.BetId)

		err = tx.Table(l.baseThirdService.TableName).
			Where("ThirdId = ? AND Brand = ?", req.BetId, l.baseThirdService.BrandName).
			Updates(map[string]interface{}{
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 2,
			}).Error

		if err != nil {
			logs.Error(l.brandName, "更新订单游戏记录链接失败 thirdId=", req.BetId, " err=", err.Error())
			return err
		}

		// 推送游戏事件通知
		if l.thirdGamePush != nil {
			// 使用 gameId 作为游戏标识
			game := fmt.Sprintf("PG_%s", gameId)
			l.thirdGamePush.PushBetEvent(userId, game, l.brandName, betAmount, l.currency)
		}
		return nil
	})
	// 如果事务处理出错，返回错误信息
	if err != nil {
		logs.Error(l.brandName, "事务处理失败 err=", err.Error())
		errorResps := errorResp{}
		errorResps.Code = 3202
		errorResps.Message = "发生错误"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 获取最新的用户余额
	userBalance, _ := l.baseThirdService.GetUserBalance(userId)
	balanceAmount := userBalance.Amount + userBalance.BonusAmount
	balanceAmount = float64(int(balanceAmount*100)) / 100 // 保留两位小数

	// 返回成功响应
	respDatas.UpdatedTime = req.UpdatedTime
	respDatas.BalanceAmount = balanceAmount
	authResp.Data = respDatas

	// 将成功的响应缓存起来
	authRespstr, _ := json.Marshal(authResp)
	err = server.Redis().SetString(key, string(authRespstr))
	if err != nil {
		logs.Debug("成功的响应缓存失败", err)
	}

	ctx.RespJson(authResp)

	// 推送奖励事件通知
	if l.thirdGamePush != nil {
		// 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PGConfig] TransferInOut 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PGConfig] TransferInOut 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type adjustmentPGReq struct {
	OperatorToken           string   `form:"operator_token" json:"operator_token"`
	SecretKey               string   `form:"secret_key" json:"secret_key"`
	PlayerName              string   `form:"player_name" json:"player_name"`
	CurrencyCode            string   `form:"currency_code" json:"currency_code"`
	TransferAmount          *float64 `form:"transfer_amount" json:"transfer_amount"`
	AdjustmentId            string   `form:"adjustment_id" json:"adjustment_id"`
	AdjustmentTransactionId string   `form:"adjustment_transaction_id" json:"adjustment_transaction_id"`
	AdjustmentTime          *int64   `form:"adjustment_time" json:"adjustment_time"`
	TransactionType         string   `form:"transaction_type" json:"transaction_type"`
	BetType                 *int     `form:"bet_type" json:"bet_type"`
	PromotionId             *int64   `form:"promotion_id" json:"promotion_id"`
	PromotionType           *int64   `form:"promotion_type" json:"promotion_type"`
	Remark                  string   `form:"remark" json:"remark"`
}

// Adjustment  此段代码未使用  未验证
func (l *PGConfig) Adjustment(ctx *abugo.AbuHttpContent) {

	authResp := resp{}
	req := adjustmentPGReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方Adjustment req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Error(l.brandName, "三方Adjustment req  data |  traceId ", string(reqBytes), traceId)

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey {
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	userId, _ := strconv.Atoi(req.PlayerName)

	// 使用 BaseThirdService 获取用户余额（真金+Bonus币）
	userBalance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		logs.Error(l.brandName, "获取用户余额失败 userId=", userId, " err=", err.Error())
		errorResps := errorResp{}
		errorResps.Code = 3005
		errorResps.Message = "玩家钱包不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 计算总余额（真金+Bonus币）并保留两位小数
	totalBalance := userBalance.Amount + userBalance.BonusAmount
	totalBalance = float64(int(totalBalance*100)) / 100

	// 检查余额是否足够
	if totalBalance+(*req.TransferAmount) < 0 {
		errorResps := errorResp{}
		errorResps.Code = 3202
		errorResps.Message = "玩家余额不足"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	type respData struct {
		AdjustAmount  float64 `json:"adjust_amount"`
		BalanceBefore float64 `json:"balance_before"`
		BalanceAfter  float64 `json:"balance_after"`
		UpdatedTime   int64   `json:"updated_time"`
	}
	respDatas := respData{}
	//防止重复请求
	key := cacheKeyPG + req.AdjustmentTransactionId
	if server.Redis().Get(key) != nil && server.Redis().Get(key).(string) == "1" {
		respDatas.AdjustAmount = *req.TransferAmount
		respDatas.BalanceBefore = totalBalance
		respDatas.BalanceAfter = totalBalance
		respDatas.UpdatedTime = time.Now().UnixMilli()
		authResp.Data = respDatas
		ctx.RespJson(authResp)
		return
	}
	// 开启事务
	var (
		amount  = *req.TransferAmount
		thirdId = req.AdjustmentTransactionId
	)

	// 使用 BaseThirdService 处理调整
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额并加锁
		userBalance, err := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if err != nil {
			logs.Error(l.brandName, "获取用户余额失败 userId=", userId, " err=", err.Error())
			return err
		}

		// 准备参数
		memo := fmt.Sprintf("PG Adjustment %s", thirdId)
		currentTime := time.Now().Format("2006-01-02 15:04:05")

		// 处理调整
		if amount > 0 {
			// 如果是正数，则视为派彩
			// 使用 AddAmountWithLogs 处理派彩
			// 按比例全部加到真金上
			err = l.baseThirdService.AddAmountWithLogs(
				tx,
				userBalance,
				amount, // 真金增加金额
				0,      // Bonus币增加金额
				utils.BalanceCReasonPGAdjustment,
				memo,
				currentTime,
			)
		} else {
			// 如果是负数，则视为扣款
			// 使用 DeductAmountWithLogs 处理扣款
			absAmount := -amount // 取绝对值

			// 计算扣除分配
			realDeductAmount, bonusDeductAmount, _, err := l.baseThirdService.CalculateBetDistribution(userBalance, absAmount)
			if err != nil {
				logs.Error(l.brandName, "计算扣除分配失败 err=", err.Error())
				return err
			}

			// 按照混合投注规则扣除真金和Bonus币
			err = l.baseThirdService.DeductAmountWithLogs(
				tx,
				userBalance,
				realDeductAmount,
				bonusDeductAmount,
				utils.BalanceCReasonPGAdjustment,
				memo,
				currentTime,
			)
		}

		if err != nil {
			logs.Error(l.brandName, "处理调整失败 thirdId=", thirdId, " err=", err.Error())
			return err
		}

		return nil
	})

	// 如果事务处理出错，返回错误信息
	if err != nil {
		logs.Error(l.brandName, "事务处理失败 err=", err.Error())
		errorResps := errorResp{}
		errorResps.Code = 3062
		errorResps.Message = "事务回滚异常"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 获取最新的用户余额
	updatedBalance, _ := l.baseThirdService.GetUserBalance(userId)
	updatedTotalBalance := updatedBalance.Amount + updatedBalance.BonusAmount
	updatedTotalBalance = float64(int(updatedTotalBalance*100)) / 100 // 保留两位小数

	// 成功响应
	server.Redis().SetStringEx(key, 60*60, "1")
	respDatas.AdjustAmount = *req.TransferAmount
	respDatas.BalanceBefore = totalBalance
	respDatas.BalanceAfter = updatedTotalBalance
	respDatas.UpdatedTime = time.Now().UnixMilli()
	authResp.Data = respDatas
	ctx.RespJson(authResp)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PGConfig] Adjustment 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PGConfig] Adjustment 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// UpdateBetDetail 暂时不做
func (l *PGConfig) UpdateBetDetail(ctx *abugo.AbuHttpContent) {
	ctx.RespJson(1)
}

// pg获取运营商令牌
func (l *PGConfig) getPgOperatorSessionForGameRst() (t string) {
	// 获取pg令牌缓存
	cacheKeyOpSession := fmt.Sprintf("%v:%v:cache:pg:opsession", server.Project(), server.Module())
	if cacheValue := server.Redis().Get(cacheKeyOpSession); cacheValue != nil {
		t = string(cacheValue.([]byte))
		logs.Info("pg 获取运营商session缓存成功:  cacheKeyOpSession = ", cacheKeyOpSession, " t = ", t)
		return
	}

	defer func() {
		if t != "" {
			// 设置pg令牌缓存 30分钟过期 我们取15分钟短 测试先用缓存60秒 上线用缓存900秒
			if e := server.Redis().SetStringEx(cacheKeyOpSession, 60, t); e != nil {
				logs.Error("pg 设置opsession错误:  cacheKeyOpSession = ", cacheKeyOpSession, e)
			}
		}
	}()

	type ResponseData struct {
		Data struct {
			OperatorSession string `json:"operator_session"`
		} `json:"data"`
		Error string `json:"error"`
	}
	rsp := ResponseData{}

	urlreq := fmt.Sprintf("%s/external/Login/v1/LoginProxy?trace_id=%s", l.url, abugo.GetUuid())
	postData := url.Values{}
	postData.Set("operator_token", l.operatorToken)
	postData.Set("secret_key", l.secretKey)
	reqBytes := []byte(postData.Encode())

	client := &http.Client{}
	payload := strings.NewReader(string(reqBytes))
	req, _ := http.NewRequest(http.MethodPost, urlreq, payload)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("PG 获取运营商session错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("PG 获取运营商session错误2 err=", err.Error())
		return
	}
	logs.Info("PG 获取运营商session成功 respBytes=", string(respBytes))
	err = json.Unmarshal(respBytes, &rsp)
	if err != nil {
		logs.Error("PG 获取运营商session错误3 err=", err.Error())
		return
	}
	if rsp.Error != "" {
		logs.Error("PG 获取运营商session错误4 err=", rsp.Error)
		return
	}
	if rsp.Data.OperatorSession != "" {
		t = rsp.Data.OperatorSession
		logs.Info("PG 获取运营商session成功 OperatorSession=", rsp.Data.OperatorSession)
	} else {
		logs.Error("PG 获取运营商session错误5 OperatorSession=", rsp.Data.OperatorSession)
	}
	return
}
