// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTbWinlostConfig(db *gorm.DB, opts ...gen.DOOption) xTbWinlostConfig {
	_xTbWinlostConfig := xTbWinlostConfig{}

	_xTbWinlostConfig.xTbWinlostConfigDo.UseDB(db, opts...)
	_xTbWinlostConfig.xTbWinlostConfigDo.UseModel(&model.XTbWinlostConfig{})

	tableName := _xTbWinlostConfig.xTbWinlostConfigDo.TableName()
	_xTbWinlostConfig.ALL = field.NewAsterisk(tableName)
	_xTbWinlostConfig.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTbWinlostConfig.SellerID = field.NewInt32(tableName, "SellerId")
	_xTbWinlostConfig.GameType = field.NewString(tableName, "GameType")
	_xTbWinlostConfig.IntervalTime = field.NewInt32(tableName, "IntervalTime")
	_xTbWinlostConfig.DisplayRows = field.NewInt32(tableName, "DisplayRows")
	_xTbWinlostConfig.IsUserDistinct = field.NewInt32(tableName, "IsUserDistinct")
	_xTbWinlostConfig.MinRewardAmount = field.NewFloat64(tableName, "MinRewardAmount")
	_xTbWinlostConfig.MinOnlineUsers = field.NewInt32(tableName, "MinOnlineUsers")
	_xTbWinlostConfig.Memo = field.NewString(tableName, "Memo")
	_xTbWinlostConfig.Status = field.NewInt32(tableName, "Status")
	_xTbWinlostConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTbWinlostConfig.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTbWinlostConfig.fillFieldMap()

	return _xTbWinlostConfig
}

// xTbWinlostConfig 最近盈利配置
type xTbWinlostConfig struct {
	xTbWinlostConfigDo xTbWinlostConfigDo

	ALL             field.Asterisk
	ChannelID       field.Int32   // 渠道Id
	SellerID        field.Int32   // 运营商Id
	GameType        field.String  // 游戏分类
	IntervalTime    field.Int32   // 计算时间(单位：秒)
	DisplayRows     field.Int32   // 显示行数
	IsUserDistinct  field.Int32   // 是否玩家去重 1是 2否
	MinRewardAmount field.Float64 // 限制最低盈利金额 0忽略
	MinOnlineUsers  field.Int32   // 最低在线用户数 0忽略
	Memo            field.String  // 描述
	Status          field.Int32   // 1有效 2无效
	CreateTime      field.Time    // 创建时间
	UpdateTime      field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTbWinlostConfig) Table(newTableName string) *xTbWinlostConfig {
	x.xTbWinlostConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTbWinlostConfig) As(alias string) *xTbWinlostConfig {
	x.xTbWinlostConfigDo.DO = *(x.xTbWinlostConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTbWinlostConfig) updateTableName(table string) *xTbWinlostConfig {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.GameType = field.NewString(table, "GameType")
	x.IntervalTime = field.NewInt32(table, "IntervalTime")
	x.DisplayRows = field.NewInt32(table, "DisplayRows")
	x.IsUserDistinct = field.NewInt32(table, "IsUserDistinct")
	x.MinRewardAmount = field.NewFloat64(table, "MinRewardAmount")
	x.MinOnlineUsers = field.NewInt32(table, "MinOnlineUsers")
	x.Memo = field.NewString(table, "Memo")
	x.Status = field.NewInt32(table, "Status")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTbWinlostConfig) WithContext(ctx context.Context) *xTbWinlostConfigDo {
	return x.xTbWinlostConfigDo.WithContext(ctx)
}

func (x xTbWinlostConfig) TableName() string { return x.xTbWinlostConfigDo.TableName() }

func (x xTbWinlostConfig) Alias() string { return x.xTbWinlostConfigDo.Alias() }

func (x xTbWinlostConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xTbWinlostConfigDo.Columns(cols...)
}

func (x *xTbWinlostConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTbWinlostConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["IntervalTime"] = x.IntervalTime
	x.fieldMap["DisplayRows"] = x.DisplayRows
	x.fieldMap["IsUserDistinct"] = x.IsUserDistinct
	x.fieldMap["MinRewardAmount"] = x.MinRewardAmount
	x.fieldMap["MinOnlineUsers"] = x.MinOnlineUsers
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTbWinlostConfig) clone(db *gorm.DB) xTbWinlostConfig {
	x.xTbWinlostConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTbWinlostConfig) replaceDB(db *gorm.DB) xTbWinlostConfig {
	x.xTbWinlostConfigDo.ReplaceDB(db)
	return x
}

type xTbWinlostConfigDo struct{ gen.DO }

func (x xTbWinlostConfigDo) Debug() *xTbWinlostConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xTbWinlostConfigDo) WithContext(ctx context.Context) *xTbWinlostConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTbWinlostConfigDo) ReadDB() *xTbWinlostConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTbWinlostConfigDo) WriteDB() *xTbWinlostConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTbWinlostConfigDo) Session(config *gorm.Session) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTbWinlostConfigDo) Clauses(conds ...clause.Expression) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTbWinlostConfigDo) Returning(value interface{}, columns ...string) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTbWinlostConfigDo) Not(conds ...gen.Condition) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTbWinlostConfigDo) Or(conds ...gen.Condition) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTbWinlostConfigDo) Select(conds ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTbWinlostConfigDo) Where(conds ...gen.Condition) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTbWinlostConfigDo) Order(conds ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTbWinlostConfigDo) Distinct(cols ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTbWinlostConfigDo) Omit(cols ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTbWinlostConfigDo) Join(table schema.Tabler, on ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTbWinlostConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTbWinlostConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTbWinlostConfigDo) Group(cols ...field.Expr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTbWinlostConfigDo) Having(conds ...gen.Condition) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTbWinlostConfigDo) Limit(limit int) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTbWinlostConfigDo) Offset(offset int) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTbWinlostConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTbWinlostConfigDo) Unscoped() *xTbWinlostConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTbWinlostConfigDo) Create(values ...*model.XTbWinlostConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTbWinlostConfigDo) CreateInBatches(values []*model.XTbWinlostConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTbWinlostConfigDo) Save(values ...*model.XTbWinlostConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTbWinlostConfigDo) First() (*model.XTbWinlostConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbWinlostConfig), nil
	}
}

func (x xTbWinlostConfigDo) Take() (*model.XTbWinlostConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbWinlostConfig), nil
	}
}

func (x xTbWinlostConfigDo) Last() (*model.XTbWinlostConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbWinlostConfig), nil
	}
}

func (x xTbWinlostConfigDo) Find() ([]*model.XTbWinlostConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTbWinlostConfig), err
}

func (x xTbWinlostConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTbWinlostConfig, err error) {
	buf := make([]*model.XTbWinlostConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTbWinlostConfigDo) FindInBatches(result *[]*model.XTbWinlostConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTbWinlostConfigDo) Attrs(attrs ...field.AssignExpr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTbWinlostConfigDo) Assign(attrs ...field.AssignExpr) *xTbWinlostConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTbWinlostConfigDo) Joins(fields ...field.RelationField) *xTbWinlostConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTbWinlostConfigDo) Preload(fields ...field.RelationField) *xTbWinlostConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTbWinlostConfigDo) FirstOrInit() (*model.XTbWinlostConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbWinlostConfig), nil
	}
}

func (x xTbWinlostConfigDo) FirstOrCreate() (*model.XTbWinlostConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbWinlostConfig), nil
	}
}

func (x xTbWinlostConfigDo) FindByPage(offset int, limit int) (result []*model.XTbWinlostConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTbWinlostConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTbWinlostConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTbWinlostConfigDo) Delete(models ...*model.XTbWinlostConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTbWinlostConfigDo) withDO(do gen.Dao) *xTbWinlostConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
