package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

var Paymentiq = new(paymentiq)

type paymentiq struct {
	Base
}

type RechargeReq struct {
	Symbol   string
	MethodId int
}

type VerifyUserReq struct {
	SessionID string `json:"sessionId"`
	UserID    string `json:"userId"`
}

type AuthorizeReq struct {
	UserID     string `json:"userId"`
	TxAmount   string `json:"txAmount"`
	TxAmountCy string `json:"txAmountCy"`
	TxID       string `json:"txId"`
	TxTypeID   int    `json:"txTypeId"`
	TxName     string `json:"txName"`
	Provider   string `json:"provider"`
}

type TransferReq struct {
	UserID        string `json:"userId"`
	TxAmount      string `json:"txAmount"`
	TxAmountCy    string `json:"txAmountCy"`
	TxPspAmount   string `json:"txPspAmount"`
	TxPspAmountCy string `json:"txPspAmountCy"`
	Fee           string `json:"fee"`
	FeeCy         string `json:"feeCy"`
	TxID          string `json:"txId"`
	TxTypeID      string `json:"txTypeId"`
	TxName        string `json:"txName"`
	Provider      string `json:"provider"`
	TxRefID       string `json:"txRefId"`
	AuthCode      string `json:"authCode"`
}

type PaymentIqCfg struct {
	MerchantId string `json:"merchantId"`
	Env        string `json:"env"`
}

func (c *paymentiq) Init() {
	server.Http().Post("/api/paymentiq/recharge", c.Recharge)
	server.Http().PostNoAuth("/api/paymentiq/verifyuser", c.verifyuser)
	server.Http().PostNoAuth("/api/paymentiq/authorize", c.authorize)
	server.Http().PostNoAuth("/api/paymentiq/transfer", c.transfer)
}

func (c *paymentiq) Recharge(ctx *abugo.AbuHttpContent) {
	req := RechargeReq{}
	if err := ctx.Gin().BindJSON(&req); err != nil {
		ctx.Gin().JSON(200, gin.H{
			"errCode": 1,
			"errMsg":  "verify failed",
		})
		return
	}

	token := server.GetToken(ctx)
	errcode := 0

	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	cfg := PaymentIqCfg{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 验证用户是否存在
	dao := server.DaoxHashGame().XUser
	user, err := dao.WithContext(ctx.Gin()).Where(dao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	sessionId := c.generateNum()
	// 写入redis
	cacheKey := fmt.Sprintf("paymentiq:%v:sessionId", user.UserID)
	err = server.XRedis().Set(cacheKey, sessionId, 3600)

	var predefinedAmounts []string
	json.Unmarshal([]byte(payMethod.RechargeAmountOptions), &predefinedAmounts)

	ctx.RespOK(gin.H{
		"userId":            user.UserID,
		"sessionId":         sessionId,
		"merchantId":        cfg.MerchantId,
		"environment":       cfg.Env,
		"predefinedAmounts": strings.Join(predefinedAmounts, ","),
	})
}

func (c *paymentiq) verifyuser(ctx *abugo.AbuHttpContent) {
	req := VerifyUserReq{}
	if err := ctx.Gin().BindJSON(&req); err != nil {
		ctx.Gin().JSON(200, gin.H{
			"userId":  "",
			"success": false,
			"errCode": 1,
			"errMsg":  "verify failed",
		})
		return
	}
	body, _ := json.Marshal(req)
	logs.Info("Paymentiq verifyuser 回调数据", string(body))

	// ip白名单验证
	reqIp := ctx.GetIp()
	if !c.checkIp(reqIp) {
		logs.Info("Paymentiq Ip", reqIp)
		ctx.Gin().JSON(200, gin.H{
			"userId":  "",
			"success": false,
			"errCode": 1,
			"errMsg":  "ip deny",
		})
		return
	}

	userId, _ := strconv.Atoi(req.UserID)
	// 验证用户是否存在
	dao := server.DaoxHashGame().XUser
	user, err := dao.WithContext(ctx.Gin()).Where(dao.UserID.Eq(int32(userId))).First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"userId":  user.UserID,
			"success": false,
			"errCode": 1,
			"errMsg":  "user verify failed",
		})
		return
	}

	// 验证sessionid
	cacheKey := fmt.Sprintf("paymentiq:%v:sessionId", user.UserID)
	cacheSessionId, err := server.XRedis().Get(cacheKey)
	if err != nil {
		return
	}
	if string(cacheSessionId) != req.SessionID {
		ctx.Gin().JSON(200, gin.H{
			"userId":  user.UserID,
			"success": false,
			"errCode": 1,
			"errMsg":  "sessionId verify failed",
		})
		return
	}

	ctx.Gin().JSON(200, gin.H{
		"userId":    user.UserID,
		"success":   true,
		"firstName": user.Account,
		"lastName":  user.RealName,
		"street":    "street",
		"country":   user.IsoCountry,
		"email":     user.Email,
		"dob":       user.Birthday.Format("2006-01-02"),
		"mobile":    user.PhoneNum,
		"balance":   user.Amount,
		"balanceCy": "USD",
	})

}

func (c *paymentiq) authorize(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("Paymentiq authorize 回调数据:", string(body))
	req := AuthorizeReq{}
	json.Unmarshal(body, &req)

	userId, _ := strconv.Atoi(req.UserID)
	// 验证用户是否存在
	dao := server.DaoxHashGame().XUser
	user, err := dao.WithContext(ctx.Gin()).Where(dao.UserID.Eq(int32(userId))).First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"userId":  req.UserID,
			"success": false,
			"errCode": 1,
			"errMsg":  "verify failed",
		})
		return
	}

	// ip白名单验证
	reqIp := ctx.GetIp()
	if !c.checkIp(reqIp) {
		logs.Info("Paymentiq Ip", reqIp)
		ctx.Gin().JSON(200, gin.H{
			"userId":  "",
			"success": false,
			"errCode": 1,
			"errMsg":  "ip deny",
		})
		return
	}

	// 创建一个authCode
	authCode := uuid.New().String()
	// 写入redis
	cacheKey := fmt.Sprintf("paymentiq:%v:authcode", user.UserID)
	err = server.XRedis().Set(cacheKey, authCode, 3600)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"userId":  user.UserID,
			"success": false,
			"errCode": 1,
			"errMsg":  "system error",
		})
		return
	}

	ctx.Gin().JSON(200, gin.H{
		"userId":   user.UserID,
		"success":  true,
		"authCode": authCode,
	})

}

func (c *paymentiq) transfer(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("Paymentiq transfer 回调数据:", string(body))
	req := TransferReq{}
	json.Unmarshal(body, &req)

	userId, _ := strconv.Atoi(req.UserID)
	// 验证用户是否存在
	dao := server.DaoxHashGame().XUser
	user, err := dao.WithContext(ctx.Gin()).Where(dao.UserID.Eq(int32(userId))).First()
	if err != nil {
		logs.Info("user verify failed")
		ctx.Gin().JSON(200, gin.H{
			"errCode": 1,
			"errMsg":  "user verify failed",
		})
		return
	}

	// ip白名单验证
	reqIp := ctx.GetIp()
	if !c.checkIp(reqIp) {
		logs.Info("Paymentiq Ip", reqIp)
		ctx.Gin().JSON(200, gin.H{
			"userId":  "",
			"success": false,
			"errCode": 1,
			"errMsg":  "ip deny",
		})
		return
	}

	agentDao := server.DaoxHashGame().XAgentIndependence
	agent, err := agentDao.WithContext(ctx.Gin()).Where(agentDao.UserID.Eq(int32(userId))).First()
	specialAgent := 2
	if agent != nil {
		specialAgent = 1
	}

	// 验证订单是否存在
	rechargeDao := server.DaoxHashGame().XRecharge
	rechargeDb := rechargeDao.WithContext(ctx.Gin())

	order, err := rechargeDb.Where(rechargeDao.ThirdID.Eq(req.TxID)).First()
	if order != nil {
		logs.Info("order exist")
		ctx.Gin().JSON(200, gin.H{
			"errCode": 1,
			"errMsg":  "order exist",
		})
		return
	}

	// 验证authCode
	cacheKey := fmt.Sprintf("paymentiq:%v:authcode", req.UserID)
	cacheAuthCode, _ := server.XRedis().Get(cacheKey)
	if req.AuthCode == "" || req.AuthCode != string(cacheAuthCode) {
		logs.Info("authCode verify failed")
		ctx.Gin().JSON(200, gin.H{
			"errCode": 1,
			"errMsg":  "authCode verify failed",
		})
		return
	}
	txAmount, _ := strconv.ParseFloat(req.TxAmount, 64)
	var rate float64 // 汇率和真实金额
	errcode := 0
	if user.SellerID == 26 {
		rate = 0 // 运营商ID为26时，汇率设为0
	} else {
		rate, err = c.getWithdrawRate(req.TxAmountCy) // 使用TxAmountCy作为货币符号
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode)
			return
		}
		txAmount = txAmount / rate
	}
	// 建立订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:  user.SellerID,
		ChannelID: user.ChannelID,
		UserID:    user.UserID,
		Symbol:    req.TxAmountCy,
		//PayID:        int32(req.MethodId),
		ThirdID:      req.TxID,
		PayType:      12,
		Amount:       txAmount,
		RealAmount:   txAmount,
		TransferRate: 1,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	// payMethod, err := c.getPayMethod(int(rechargeOrder.PayID))
	if err != nil {
		tx.Rollback()
		logs.Info("create order failed")
		ctx.Gin().JSON(200, gin.H{
			"errCode": 1,
			"errMsg":  "create order failed",
		})
		return
	}

	logs.Info("success")
	tx.Commit()

	c.rechargeCallbackHandel(int(user.UserID), int(rechargeOrder.ID), 5)

	ctx.Gin().JSON(200, gin.H{
		"userId":       user.UserID,
		"success":      true,
		"txId":         req.TxID,
		"merchantTxId": rechargeOrder.ID,
	})
}

func (c *paymentiq) checkIp(ip string) bool {
	if ip != "***********" && ip != "*************" && ip != "**************" && ip != "*************" {
		return false
	}

	return true
}
