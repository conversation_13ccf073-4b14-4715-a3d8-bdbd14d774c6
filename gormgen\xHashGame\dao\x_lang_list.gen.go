// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXLangList(db *gorm.DB, opts ...gen.DOOption) xLangList {
	_xLangList := xLangList{}

	_xLangList.xLangListDo.UseDB(db, opts...)
	_xLangList.xLangListDo.UseModel(&model.XLangList{})

	tableName := _xLangList.xLangListDo.TableName()
	_xLangList.ALL = field.NewAsterisk(tableName)
	_xLangList.ID = field.NewInt32(tableName, "Id")
	_xLangList.GameSort = field.NewString(tableName, "GameSort")
	_xLangList.GameSortEx = field.NewString(tableName, "GameSortEx")
	_xLangList.LangName = field.NewString(tableName, "LangName")
	_xLangList.LangAlisa = field.NewString(tableName, "LangAlisa")
	_xLangList.State = field.NewInt32(tableName, "State")
	_xLangList.CreateTime = field.NewTime(tableName, "CreateTime")
	_xLangList.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xLangList.SportTarget = field.NewString(tableName, "SportTarget")
	_xLangList.LoginRegisterType = field.NewString(tableName, "LoginRegisterType")

	_xLangList.fillFieldMap()

	return _xLangList
}

type xLangList struct {
	xLangListDo xLangListDo

	ALL               field.Asterisk
	ID                field.Int32  // 前端语言Id
	GameSort          field.String // 游戏大类排序
	GameSortEx        field.String // 游戏小类排序
	LangName          field.String // 语言名称
	LangAlisa         field.String // 语言简写
	State             field.Int32  // 1:开启 2:关闭
	CreateTime        field.Time
	UpdateTime        field.Time
	SportTarget       field.String // 体育跳转
	LoginRegisterType field.String // 登录注册类型

	fieldMap map[string]field.Expr
}

func (x xLangList) Table(newTableName string) *xLangList {
	x.xLangListDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xLangList) As(alias string) *xLangList {
	x.xLangListDo.DO = *(x.xLangListDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xLangList) updateTableName(table string) *xLangList {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.GameSort = field.NewString(table, "GameSort")
	x.GameSortEx = field.NewString(table, "GameSortEx")
	x.LangName = field.NewString(table, "LangName")
	x.LangAlisa = field.NewString(table, "LangAlisa")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.SportTarget = field.NewString(table, "SportTarget")
	x.LoginRegisterType = field.NewString(table, "LoginRegisterType")

	x.fillFieldMap()

	return x
}

func (x *xLangList) WithContext(ctx context.Context) *xLangListDo {
	return x.xLangListDo.WithContext(ctx)
}

func (x xLangList) TableName() string { return x.xLangListDo.TableName() }

func (x xLangList) Alias() string { return x.xLangListDo.Alias() }

func (x xLangList) Columns(cols ...field.Expr) gen.Columns { return x.xLangListDo.Columns(cols...) }

func (x *xLangList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xLangList) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["GameSort"] = x.GameSort
	x.fieldMap["GameSortEx"] = x.GameSortEx
	x.fieldMap["LangName"] = x.LangName
	x.fieldMap["LangAlisa"] = x.LangAlisa
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["SportTarget"] = x.SportTarget
	x.fieldMap["LoginRegisterType"] = x.LoginRegisterType
}

func (x xLangList) clone(db *gorm.DB) xLangList {
	x.xLangListDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xLangList) replaceDB(db *gorm.DB) xLangList {
	x.xLangListDo.ReplaceDB(db)
	return x
}

type xLangListDo struct{ gen.DO }

func (x xLangListDo) Debug() *xLangListDo {
	return x.withDO(x.DO.Debug())
}

func (x xLangListDo) WithContext(ctx context.Context) *xLangListDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xLangListDo) ReadDB() *xLangListDo {
	return x.Clauses(dbresolver.Read)
}

func (x xLangListDo) WriteDB() *xLangListDo {
	return x.Clauses(dbresolver.Write)
}

func (x xLangListDo) Session(config *gorm.Session) *xLangListDo {
	return x.withDO(x.DO.Session(config))
}

func (x xLangListDo) Clauses(conds ...clause.Expression) *xLangListDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xLangListDo) Returning(value interface{}, columns ...string) *xLangListDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xLangListDo) Not(conds ...gen.Condition) *xLangListDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xLangListDo) Or(conds ...gen.Condition) *xLangListDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xLangListDo) Select(conds ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xLangListDo) Where(conds ...gen.Condition) *xLangListDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xLangListDo) Order(conds ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xLangListDo) Distinct(cols ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xLangListDo) Omit(cols ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xLangListDo) Join(table schema.Tabler, on ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xLangListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xLangListDo) RightJoin(table schema.Tabler, on ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xLangListDo) Group(cols ...field.Expr) *xLangListDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xLangListDo) Having(conds ...gen.Condition) *xLangListDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xLangListDo) Limit(limit int) *xLangListDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xLangListDo) Offset(offset int) *xLangListDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xLangListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xLangListDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xLangListDo) Unscoped() *xLangListDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xLangListDo) Create(values ...*model.XLangList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xLangListDo) CreateInBatches(values []*model.XLangList, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xLangListDo) Save(values ...*model.XLangList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xLangListDo) First() (*model.XLangList, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangList), nil
	}
}

func (x xLangListDo) Take() (*model.XLangList, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangList), nil
	}
}

func (x xLangListDo) Last() (*model.XLangList, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangList), nil
	}
}

func (x xLangListDo) Find() ([]*model.XLangList, error) {
	result, err := x.DO.Find()
	return result.([]*model.XLangList), err
}

func (x xLangListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XLangList, err error) {
	buf := make([]*model.XLangList, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xLangListDo) FindInBatches(result *[]*model.XLangList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xLangListDo) Attrs(attrs ...field.AssignExpr) *xLangListDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xLangListDo) Assign(attrs ...field.AssignExpr) *xLangListDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xLangListDo) Joins(fields ...field.RelationField) *xLangListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xLangListDo) Preload(fields ...field.RelationField) *xLangListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xLangListDo) FirstOrInit() (*model.XLangList, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangList), nil
	}
}

func (x xLangListDo) FirstOrCreate() (*model.XLangList, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangList), nil
	}
}

func (x xLangListDo) FindByPage(offset int, limit int) (result []*model.XLangList, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xLangListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xLangListDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xLangListDo) Delete(models ...*model.XLangList) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xLangListDo) withDO(do gen.Dao) *xLangListDo {
	x.DO = *do.(*gen.DO)
	return x
}
