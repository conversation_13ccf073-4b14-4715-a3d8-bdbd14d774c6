package third

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/spf13/viper"
	"strings"
	"time"
	"xserver/utils"
)

type BukaBaseConf struct {
	ApiKey string `json:"apiKey"`
	ApiPwd string `json:"apiPwd"`
}

type BukaBaseResult struct {
	//Status  string `json:"status"`
	Status  int    `json:"status"`
	Reason  string `json:"reason"`
	Success string `json:"success"`
	Fail    string `json:"fail"`
}

type BukaSmsConf struct {
	SenderId string `json:"senderId"`
	SmsUrl   string `json:"smsUrl"`
}

type BukaEmailConf struct {
	FromEmailAddress  string `json:"fromEmailAddress"`
	CheckEmailAddress bool   `json:"checkEmailAddress"`
	EmailUrl          string `json:"emailUrl"`
	Language          string `json:"Language"` //默认语言
	AdFlag            int    `json:"adFlag"`
	TempCodeId        int    `json:"tempCodeId"` //发送验证码的temp id
}

type BuKaConf struct {
	BukaBaseConf  *BukaBaseConf  `json:"base" mapstructure:"base"`
	BukaSmsConf   *BukaSmsConf   `json:"sms" mapstructure:"sms"`
	BukaEmailConf *BukaEmailConf `json:"email" mapstructure:"email"`
	AppId         string         `json:"appId"`
}

type BuKa struct {
	BuKaConf
}

type BuKaSmsBody struct {
	Numbers  string `json:"numbers"`
	Content  string `json:"content"`
	SenderId string `json:"senderId"`
	AppId    string `json:"appId"`
}

type BuKaSmsResult struct {
	*BukaBaseResult
}

type BuKaEmailBody struct {
	AppId             string `json:"appId"`
	FromEmailAddress  string `json:"fromEmailAddress"`
	ToAddress         string `json:"toAddress"`
	Subject           string `json:"subject"`
	TemplateID        int    `json:"templateID"`
	TemplateData      string `json:"templateData"`
	Language          string `json:"language"`
	AdFlag            int    `json:"adFlag"`
	CheckEmailAddress bool   `json:"checkEmailAddress"`
}

type BuKaCodeTemp struct {
	Platform string `json:"platform""`
	Code     string `json:"code"`
	Minutes  string `json:"minutes"`
}

type BuKaEmailResult struct {
	*BukaBaseResult
}

func (t *BuKa) InitConfig() {
	viper.SetConfigName("third")
	viper.AddConfigPath("./config")
	err := viper.ReadInConfig() // 查找并读取配置文件
	if err != nil {             // 处理读取配置文件的错误
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	err = viper.Unmarshal(&t.BuKaConf)
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	fmt.Println(t.BuKaConf)
}

func (t *BuKa) BuKaSendSms(numbers []string, content string) {
	if &t.BukaSmsConf == nil {
		return
	}

	now := time.Now()
	second := now.Unix()
	sign := t.BuKaSignByMd5(second)
	result := BuKaSmsResult{}
	err := utils.HttpPost(t.BukaSmsConf.SmsUrl,
		BuKaSmsBody{
			AppId:    t.AppId,
			Numbers:  strings.Join(numbers, ","),
			Content:  content,
			SenderId: t.BukaSmsConf.SenderId,
		},
		utils.NewAnyMap().AppendKStr("Sign", sign).
			AppendKStr("Timestamp", fmt.Sprintf("%d", second)).
			AppendKStr("Api-Key", t.BukaBaseConf.ApiKey).GetStrMap(),
		&result)
	if err != nil {
		logs.Error("post error", err)
	}
	if result.Status != 0 {
		return
	}
	return
}

func (t *BuKa) BuKaSendCodeEmail(toAddress []string, code int, minutes int) {
	if &t.BukaSmsConf == nil {
		return
	}

	t.BuKaSendEmail("subject", t.BukaEmailConf.TempCodeId,
		utils.NewAnyMap().AppendKStr("platform", "ok hash").
			AppendKStr("code", fmt.Sprintf("%d", code)).
			AppendKStr("minutes", fmt.Sprintf("%d", minutes)).GetStrMap(),
		t.BukaEmailConf.Language, toAddress)
	return
}

func (t *BuKa) BuKaSendEmail(subject string, templateID int, templateData interface{},
	language string, toAddress []string) {

	//platform code minutes
	if &t.BukaEmailConf == nil {
		return
	}

	now := time.Now()
	second := now.Unix()
	sign := t.BuKaSignByMd5(second)

	result := BuKaEmailResult{}

	data, _ := json.Marshal(templateData)

	err := utils.HttpPost(t.BukaEmailConf.EmailUrl,
		BuKaEmailBody{
			AppId:             t.AppId,
			FromEmailAddress:  t.BukaEmailConf.FromEmailAddress,
			ToAddress:         strings.Join(toAddress, ","),
			Subject:           subject,
			TemplateID:        templateID,
			TemplateData:      string(data),
			Language:          t.BukaEmailConf.Language,
			AdFlag:            t.BukaEmailConf.AdFlag,
			CheckEmailAddress: t.BukaEmailConf.CheckEmailAddress,
		},
		utils.NewAnyMap().AppendKv("Sign", sign).AppendKv("Timestamp", second).AppendKv("Api-Key", t.BukaBaseConf.ApiKey).GetStrMap(),
		&result)

	if err != nil {
		logs.Error("post error", err)
	}
	if result.Status != 0 {
		return
	}
	return
}

func (t *BuKa) BuKaSignByMd5(second int64) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s%s%d", t.BukaBaseConf.ApiKey, t.BukaBaseConf.ApiPwd, second))))
}
