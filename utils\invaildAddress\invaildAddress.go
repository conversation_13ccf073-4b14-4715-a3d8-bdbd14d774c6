package invaildAddress

import "regexp"

func InvalidTronAddress(address string) bool {
	tronRegex := `^(T[a-zA-Z0-9]{33}|t[a-zA-Z0-9]{33})$`
	re := regexp.MustCompile(tronRegex)
	return re.MatchString(address)
}

func InvalidBTCAddress(address string) bool {
	// 比特币地址的正则表达式
	// 支持 P2PKH (以 1 开头)，P2SH (以 3 开头)，Bech32 (以 bc1 开头)
	btcRegex := `^(1[a-km-zA-HJ-NP-Z1-9]{25,34}|3[a-km-zA-HJ-NP-Z1-9]{25,34}|bc1[ac-hj-np-z02-9]{39,59})$`

	// 编译正则表达式
	re := regexp.MustCompile(btcRegex)

	// 匹配地址格式
	return re.MatchString(address)
}

// InvalidBSCAddress BSCAddress IsValidBSCAddress 校验 BSC 地址（基于以太坊地址格式）
func InvalidBSCAddress(address string) bool {
	// BSC 地址的正则表达式（42 个字符，以 0x 开头，包含十六进制字符）
	bscRegex := `^0x[0-9a-fA-F]{40}$`
	re := regexp.MustCompile(bscRegex)
	return re.MatchString(address)
}

// InvalidSolanaAddress SolanaAddress 校验 Solana 地址
func InvalidSolanaAddress(address string) bool {
	// Solana 地址的正则表达式（基于 Base58，通常 43-44 个字符）
	solanaRegex := `^[1-9A-HJ-NP-Za-km-z]{32,44}$`
	re := regexp.MustCompile(solanaRegex)
	return re.MatchString(address)
}

// InvalidTONAddress TONAddress 校验 TON 地址
func InvalidTONAddress(address string) bool {
	// TON 地址的正则表达式（基于 Base64 和工作链 ID）
	tonRegex := `^(kf|EQ|Ef)[0-9a-zA-Z_-]{48}$`
	re := regexp.MustCompile(tonRegex)
	return re.MatchString(address)
}
