package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/imroc/req"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
)

// CBK 板球api 接口配置
type CbkConfig struct {
	url                   string
	key                   string
	token                 string
	currency              string
	walletToken           string
	homeUrl               string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
	baseThirdService      *base.BaseThirdService // 添加 BaseThirdService 支持混合投注
}

var tzUTC82 = time.FixedZone("utc+8", 8*3600)

// NewCbkLogic 初始化CBK api 接口配置
func NewCbkLogic(temp map[string]string, fc func(int) error) *CbkConfig {
	// 创建 BaseThirdService 实例，使用体育游戏类型
	baseService := base.NewBaseThirdService("cbk", temp["currency"], base.GameTypeSport)

	return &CbkConfig{
		url:                   temp["url"],
		key:                   temp["key"],
		token:                 temp["token"],
		walletToken:           temp["wallet_token"],
		homeUrl:               temp["home_url"],
		currency:              temp["currency"],
		brandName:             "cbk",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
		baseThirdService:      baseService, // 初始化 BaseThirdService
	}
}

const cacheKeyCbk = "cacheKeyCbk:"

// Login 登录
func (l *CbkConfig) Login(ctx *abugo.AbuHttpContent) {
	type cbkRequesData struct {
		Lang    string
		TableId string
	}

	errcode := 0
	reqdata := cbkRequesData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("cbk 解析数据失败 ", " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)

	if err, errcode = base.IsLoginByUserId(cacheKeyCbk, token.UserId); err != nil {
		logs.Error("cbk 登录失败 userId=", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	//ip
	strip := ctx.GetIp()
	//if net.ParseIP(ctx.GetIp()) == nil {
	//	strip = "************"
	//}

	//国家
	//country := ctx.Gin().Request.Header.Get("country")

	//eur := "AT,BE,CY,EE,FI,FR,DE,GR,IE,IT,LV,LT,LU,MT,NL,PT,SK,SI,ES"
	//if country != "" && strings.Contains(eur, country) {
	//	country = "EU"
	//}
	//if country == "" {
	//	ctx.RespErrString(true, &errcode, "您的区域暂不支持")
	//	return
	//}

	//姓名
	strid := fmt.Sprintf("%d", token.UserId)

	//语言
	if reqdata.Lang == "" {
		reqdata.Lang = "en"
	}

	//sessionId
	sessionId := base.UserId2token(cacheKeyCbk, token.UserId)

	//本地测试 暂时将国家写死
	//country = "CN"
	//la, ok := l[country]
	//if !ok {
	//	ctx.RespErrString(true, &errcode, "您的区域暂不支持")
	//	return
	//}
	method := "login"
	uuId := abugo.GetUuid()
	signStr := fmt.Sprintf("%s%s%d%s", method, l.token, token.UserId, uuId)
	logs.Debug("参数=", signStr)
	appSign := base.MD5(signStr)
	querydata := fmt.Sprintf(`{
        "uuid": "%s",
		"appKey": "%s",
		"method": "login",
        "player": {
            "id": "%d",
            "nickname": "%s",
            "currency": "%s",
            "session": {
                "id": "%s",
                "ip": "%s"
            }
        },
        "appSign": "%s"
    }`, uuId, l.key, token.UserId, strid, l.currency, sessionId, strip, appSign)
	url := fmt.Sprintf("%s", l.url)
	httpClient := httpc.DoRequest{
		UrlPath:    url,
		Param:      []byte(querydata),
		PostMethod: httpc.JSON_DATA,
	}
	logs.Debug("[cbk] login request:%s, ", querydata, l.url)
	data, err := httpClient.DoPost()
	logs.Debug("[cbk] login receive==>", data)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if _, ok := data["errors"]; ok {
		logs.Error("Cbk_http_post body error:", data["errors"])
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	if data != nil {
		var theme = "&theme=gold" //皮肤
		homeUrl := fmt.Sprintf("%s%s%s", l.homeUrl, data["entry"], theme)
		ctx.Put("url", homeUrl)
	}
	ctx.RespOK()

}

// GetTopEvents 获取热门赛事
func (l *CbkConfig) GetHotEvents(ctx *abugo.AbuHttpContent) {
	type EventItem struct {
		ID             string  `json:"id"`             // 赛事id
		EventName      string  `json:"eventName"`      // 赛事名称，如 Zimbabwe vs India
		EventTime      int64   `json:"eventTime"`      // 赛事时间戳
		Running        int     `json:"running"`        // 是否进行中：0否，1是
		HomeName       string  `json:"homeName"`       // 主队名称
		HomeIcon       string  `json:"homeIcon"`       // 主队图标URL
		HomeScore      string  `json:"homeScore"`      // 主队得分
		HomeStakePrice float64 `json:"homeStakePrice"` // 主队赔率
		AwayName       string  `json:"awayName"`       // 客队名称
		AwayIcon       string  `json:"awayIcon"`       // 客队图标URL
		AwayScore      string  `json:"awayScore"`      // 客队得分
	}

	type ResponseData struct {
		Status string      `json:"status"` // 响应状态，成功为"success"
		List   []EventItem `json:"list"`   // 赛事列表
	}

	type RequestData struct {
		UUID    string `json:"uuid"`    // 唯一标识
		AppKey  string `json:"appKey"`  // 应用密钥
		Method  string `json:"method"`  // 方法名
		AppSign string `json:"appSign"` // 签名
	}

	rspdata := ResponseData{
		Status: "success",
	}

	// 生成请求参数
	method := "topEvent"
	uuId := abugo.GetUuid()
	signStr := fmt.Sprintf("%s%s%s", method, l.token, uuId)
	logs.Debug("[DEBUG][cbk] GetTopEvents signStr=", signStr)
	appSign := base.MD5(signStr)

	reqdata := RequestData{
		UUID:    uuId,
		AppKey:  l.key,
		Method:  method,
		AppSign: appSign,
	}

	// 缓存key
	rKey := fmt.Sprintf("%s:%s:cbk:topevents", server.Project(), server.Module())
	rValueInterface := server.Redis().Get(rKey)
	rValue := abugo.GetStringFromInterface(rValueInterface)
	if rValue != "" {
		json.Unmarshal([]byte(rValue), &rspdata)
		ctx.RespOK(rspdata)
		return
	}

	errcode := 0
	header := req.Header{
		"Content-Type": "application/json;charset=UTF-8",
	}

	// 序列化请求数据
	reqbytes, err := json.Marshal(&reqdata)
	if err != nil {
		logs.Error("[ERROR][cbk] GetTopEvents Marshal request data err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	resp, err := req.Post(l.url, header, string(reqbytes))
	if err != nil {
		logs.Error("[ERROR][cbk] GetTopEvents url=", l.url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	if resp.Response().StatusCode != http.StatusOK {
		err = fmt.Errorf("http error, status code: %d", resp.Response().StatusCode)
		logs.Error("[ERROR][cbk] GetTopEvents url=", l.url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("[ERROR][cbk] GetTopEvents url=", l.url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}
	logs.Info("响应参数=", string(body))
	err = json.Unmarshal(body, &rspdata)
	if err != nil {
		logs.Error("[ERROR][cbk] GetTopEvents url=", l.url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	// 设置缓存，有效期30秒
	defer func() {
		if e := server.Redis().SetStringEx(rKey, 30, string(body)); e != nil {
			logs.Error("[ERROR][cbk] GetTopEvents Redis SetStringEx err=", e.Error())
		}
	}()

	ctx.RespOK(rspdata)
	return
}

// 验证token
func (l *CbkConfig) CheckToken(authToken string) bool {
	if l.walletToken == authToken {
		return true
	}

	return false
}

//OK  成功
//TEMPORARY_ERROR  可重试的错误  游戏服务器暂时出现问题。
//INVALID_TOKEN_ID  致命错误    赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//INVALID_SID  致命错误         赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//ACCOUNT_LOCKED               赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//FATAL_ERROR_CLOSE_USER_SESSION  赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//UNKNOWN_ERROR  	请联系客户支持寻求帮助。
//INVALID_PARAMETER 	请联系客户支持寻求帮助。
//BET_DOES_NOT_EXIST 	请联系客户支持寻求帮助。
//BET_ALREADY_EXIST     投注已存在于第三方系统中。
//BET_ALREADY_SETTLED 	投注已在第三方系统中结算。
//INSUFFICIENT_FUNDS 	您没有足够的资金来进行此投注。。

type reqCheck struct {
	UserId  string `json:"userId"`
	Sid     string `json:"sid"`
	Channel struct {
		Type string `json:"type"`
	} `json:"channel"`
	Uuid string `json:"uuid"`
}

// Check 登录成功后三方反查请求
func (l *CbkConfig) Check(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status string `json:"status"`
		Sid    string `json:"sid"`
		Uuid   string `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	if !l.CheckToken(authToken) { //验证token
		logs.Error("Cbk Check 失败 token验证不通过,authToken=", authToken)
		return
	}
	req := reqCheck{}
	_ = ctx.Gin().ShouldBindJSON(&req)
	id := req.Sid
	logs.Debug("Check", ctx.Gin().Request.URL, authToken, id, req)
	userId := base.Token2UserId(cacheKeyCbk, id)
	if userId == -1 { //账号被禁用
		res.Status = "INVALID_SID"
		res.Sid = abugo.GetUuid()
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Check 账号不存在,userId=", userId)
		return
	}
	res.Status = "OK"
	res.Sid = base.UserId2token(cacheKeyCbk, userId)
	res.Uuid = abugo.GetUuid()
	ctx.RespJson(res)
	logs.Error("Cbk Check 反查用户状态成功,userId=", userId)
	return
}

// Balance 余额请求
func (l *CbkConfig) Balance(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status  string  `json:"status"`
		Balance float64 `json:"balance"`
		Uuid    string  `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Balance 失败 token验证不通过,authToken=", authToken)
		return
	}
	logs.Debug("Cbk Balance", ctx.Gin().Request.URL, authToken)
	req := reqCheck{}
	_ = ctx.Gin().ShouldBindJSON(&req)
	id := req.Sid
	logs.Info("Cbk Balance", ctx.Gin().Request.URL, authToken, id, req)
	userId := base.Token2UserId(cacheKeyCbk, id)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Balance 查询用余额,账号被禁用,userId=", userId, "res=", res)
		return
	}

	// 使用 BaseThirdService 获取用户余额（真金+Bonus币）
	userBalance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Balance 查询用余额,发生错误,userId=", userId, "err=", err.Error())
		return
	}

	// 计算总余额（真金+Bonus币）并保留两位小数
	totalBalance := userBalance.Amount + userBalance.BonusAmount
	totalBalance = float64(int(totalBalance*100)) / 100

	res.Status = "OK"
	res.Balance = totalBalance
	res.Uuid = abugo.GetUuid()
	ctx.RespJson(res)
	logs.Error("Cbk Balance 查询用余额,响应成功,userId=", userId, "res=", res)
	return
}

/**
 *投注内容中文解释
 */
func (e *CbkConfig) getBetCtxFromRowData(_rawData string) (_betCtx string) {
	// 拼接下注内容 开始
	_betCtx = _rawData
	_betCtx = strings.ReplaceAll(_betCtx, "\"id\"", "\"id订单ID\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"sportId\"", "\"sportId运动类型\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"sportName\"", "\"sportName运动名称\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"leagueName\"", "\"match_date赛事日期\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"eventId\"", "\"eventId比赛Id\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"eventName\"", "\"eventName比赛名称\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"stakeNumber\"", "\"stakeNumber下注项\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"stakeName\"", "\"stakeName下注项名称\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"oddsType\"", "\"oddsType注单风格\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"price\"", "\"price下注赔率\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"currency\"", "\"currency会员货币\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"size\"", "\"size下注数量\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"settledTime\"", "\"settledTime结算日期\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"payoff\"", "\"payoff输赢金额\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"currencyProfit\"", "\"currencyProfit用户货币单位利润\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"profit\"", "\"profit利润\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"placeTime\"", "\"placeTime下单时间\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"updateTime\"", "\"updateTime更新时间\"")
	// "status"`  //注单状态 待付款 1下单成功2(交易所)部分匹配 3(交易所)匹配完成4(交易所)取消 5结算 6下单失敗/被拒绝 7VOID
	_betCtx = strings.ReplaceAll(_betCtx, "\"status\"", "\"status注单状态1成功4取消5结算6失败\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"result\"", "\"result比赛结果\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"currencySize\"", "\"currencySize下注数量 用户货币单位\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"teamB_name\"", "\"teamB_name队伍B名称\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"fxRate\"", "\"fxRate用户下注单位\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"cashOutStatus\"", "\"cashOutStatus是否可提前套现\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"userId\"", "\"userId平台用户Id\"")
	_betCtx = strings.ReplaceAll(_betCtx, "\"cancelledTime\"", "\"cancelledTime取消时间\"")
	return
}

// 订单结构体
type Order struct {
	Id                  json.Number `json:"id"`         //订单ID
	SportId             interface{} `json:"sportId"`    //运动类型 4板球
	SportName           string      `json:"sportName"`  //运动名
	LeagueName          string      `json:"leagueName"` //联赛名称
	EventID             string      `json:"eventId"`    //比赛ID
	EventName           string      `json:"eventName"`  //比赛名称
	Type                interface{} `json:"type"`       //注单类型 1赔2交易所 3采大注
	OddsType            interface{} `json:"oddsType"`   //注单风格 2fixed odds 4fancy
	MarketID            string      `json:"marketId"`   //支点的betType
	MarketName          string      `json:"marketName"` //
	BetTypeName         string      `json:"betTypeName"`
	SelectionId         interface{} `json:"selectionId"`     //支点的盘口
	PlatBetId           string      `json:"platBetId"`       //支点 必发的注单ID
	Handicap            string      `json:"handicap"`        //让球盘 或line
	Side                string      `json:"side"`            //仅仅交易所
	StakeNumber         interface{} `json:"stakeNumber"`     //下注项
	StakeName           string      `json:"stakeName"`       //下注项名称
	Price               string      `json:"price"`           //下注赔率
	PriceMatched        string      `json:"priceMatched"`    //仅交易所 实际赔率
	Size                string      `json:"size"`            //下注数量
	SizeMatched         string      `json:"sizeMatched"`     //仅仅交易所 实际匹配注数
	SizeCancelled       string      `json:"sizeCancelled"`   //
	CurrencyProfit      string      `json:"currencyProfit"`  //利润 lose为负数 win为正 用户货币单位 使用该字段
	Profit              string      `json:"profit"`          //利润lose为负数 win为正
	PlaceTime           interface{} `json:"placeTime"`       //下单时间
	SettledTime         interface{} `json:"settledTime"`     //结算时间
	LastMatchedTime     interface{} `json:"lastMatchedTime"` //仅交易所 最新匹配时间
	CancelledTime       interface{} `json:"cancelledTime"`   //仅交易所 取消时间
	UpdateTime          int64       `json:"updateTime"`
	Status              int         `json:"status"`              //注单状态 待付款 1下单成功2(交易所)部分匹配 3(交易所)匹配完成4(交易所)取消 5结算 6下单失敗/被拒绝 7VOID
	Currency            string      `json:"currency"`            //用户货币单位
	CurrencySize        string      `json:"currencySize"`        //下注数量 用户货币单位
	CurrencyMatchedSize string      `json:"currencyMatchedSize"` //仅交易所实际匹配注数
	FxRate              string      `json:"fxRate"`              //用户下注时的单位
	CompanyPt           string      `json:"companyPT"`           //用户下注时的CompanyPt百分比
	MultiBetId          string      `json:"multiBetId"`          //仅乘大单 值为对应的交易所ID
	Result              string      `json:"result"`              //赛果
	ResultType          string      `json:"resultType"`
	PersistenceType     string      `json:"persistenceType"`
	CurrencyRefund      string      `json:"currencyRefund"`
	CurrencyAmount      string      `json:"currencyAmount"`
	Cli                 string      `json:"cli"`
	CashOutStatus       interface{} `json:"cashOutStatus"` //提前套现状态不可 可提前套现 2套现完成
	EventTime           int64       `json:"eventTime"`
	Level               interface{} `json:"level"`
	Multiplier          string      `json:"multiplier"` //仅交易所用户下注注数
	PriceDecimal        string      `json:"price_decimal"`
	LevelName           string      `json:"levelName"`
	ReportType          interface{} `json:"reportType"`
	UserId              string      `json:"userId"` //平台用户ID
}

// Transaction 交易信息
type Transaction struct {
	Id     json.Number `json:"id"`     //交易的唯一标识符 订单ID
	Amount float64     `json:"amount"` //交易金额
}

// Data 三方发起的请求数据体
type Data struct {
	Uuid  string `json:"uuid"`  //唯一的请求 ID
	Order Order  `json:"order"` //订单数据
}

// 第三方发起请求结构体
// Sid 只有balance、debit，这种需要客户在线或者操作的才会有sid值
type cbkReq struct {
	UserId      string      `json:"userId"`      //平台在 UserAuthentication 调用中发送的玩家 ID (player.id)
	Sid         string      `json:"sid"`         //玩家的session ID，由平台在 UserAuthentication 调用 (session.id) 中发送。
	Uuid        string      `json:"uuid"`        //唯一的请求 ID，用于标识 CheckUserRequest
	Transaction Transaction `json:"transaction"` //包含交易对象
	Data        Data        `json:"data"`        //订单数据
}

// 响应给第三方结构体
type cbkResp struct {
	Status  string  `json:"status"`
	Balance float64 `json:"balance"`
	Uuid    string  `json:"uuid"`
	Message string  `json:"message"`
}

// Debit 用于从帐户中扣除（下注）
func (l *CbkConfig) Debit(ctx *abugo.AbuHttpContent) {
	res := cbkResp{}
	// 当三方返回的同字段数据格式不一致时解析int和字符串不能自动转换
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqCtx := string(reqDataByte) //原始数据
	logs.Info("Cbk Debit api receive:%s, ", string(reqDataByte))

	req := cbkReq{}
	err := json.Unmarshal(reqDataByte, &req)
	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Debit 下注失败 解析数据失败 ", err.Error())
		return
	}

	authToken := ctx.Query("authToken")
	logs.Debug("Debit", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Debit 下注 失败 token验证不通过,authToken=", authToken)
		return
	}

	//注单详情
	reqDataByte, _ = json.Marshal(req.Data.Order)
	betCtx := string(reqDataByte)
	betCtxZh := l.getBetCtxFromRowData(betCtx)
	userId := base.Token2UserId(cacheKeyCbk, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Debit 下注失败 userId 被禁用:userId=", userId)
		return
	}

	//三方来源的数据整理
	var (
		betAmount = req.Transaction.Amount
		thirdId   = req.Transaction.Id.String()
		gameCode  = l.brandName //l.brandName
		gameName  = "板球"
		thirdTime = utils.GetCurrentTime()
	)

	user, _, err := base.GetUserById(userId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("Cbk Debit 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
		}
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Debit 下注失败 查询用余额,发生错误,thirdId=", thirdId, "userId=", userId, "err=", err.Error())
		return
	}

	//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
	//获取投注渠道
	ChannelId := base.GetUserChannelId(ctx, user)
	//开启事务
	tablepre := "x_third_sport_pre_order"
	err = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 使用 BaseThirdService 获取用户余额并加锁
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			if errors.Is(e, gorm.ErrRecordNotFound) {
				logs.Error("Cbk Debit 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			logs.Error("Cbk Debit 下注失败 查询用余额,发生错误,thirdId=", thirdId, "userId=", userId, "err=", e.Error())
			return e
		}

		// 计算总余额（真金+Bonus币）
		totalBalance := userBalance.Amount + userBalance.BonusAmount

		// 判断用户余额是否足够
		if totalBalance < 0 || totalBalance < betAmount {
			res.Status = "INSUFFICIENT_FUNDS"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			logs.Error("Cbk Debit 下注失败 用户余额不足,thirdId=", thirdId, "userId=", userId, "betAmount=", betAmount, "totalBalance=", totalBalance)
			e = errors.New("玩家余额不足")
			return e
		}

		// 计算下注金额分配（真金和Bonus币）
		realBetAmount, bonusBetAmount, _, e := l.baseThirdService.CalculateBetDistribution(userBalance, betAmount)
		if e != nil {
			logs.Error("Cbk Debit 计算下注金额分配失败,thirdId=", thirdId, "userId=", userId, "err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 使用 DeductAmountWithLogs 扣除下注金额
		e = l.baseThirdService.DeductAmountWithLogs(
			tx,
			userBalance,
			realBetAmount,
			bonusBetAmount,
			utils.BalanceCReasonCBKBet,
			l.brandName+" bet,thirdId:"+thirdId,
			thirdTime,
		)
		if e != nil {
			logs.Error("Cbk Debit 扣除下注金额失败,thirdId=", thirdId, "userId=", userId, "err=", e.Error())
			res.Status = "INSUFFICIENT_FUNDS"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 查询注单是否存在
		order := thirdGameModel.ThirdSportOrder{}
		e = tx.Table(tablepre).Clauses(clause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e == nil {
			res.Status = "BET_ALREADY_EXIST"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			logs.Error("Cbk Debit 下注失败 订单号重复,thirdId=", thirdId, "betAmount=", betAmount, "totalBalance=", totalBalance)
			return e
		}
		if e != nil && e != gorm.ErrRecordNotFound {
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			logs.Error("Cbk Debit 下注失败 查询注单失败 thirdId=", thirdId, " err=", e.Error())
			return e
		}

		// 不需要手动更新用户余额，已经使用 DeductAmountWithLogs 完成

		// 确定下注类型
		betType := 1 // 默认为纯真金
		if bonusBetAmount > 0 {
			if realBetAmount > 0 {
				betType = 2 // 混合下注
			} else {
				betType = 3 // 纯彩金
			}
		}

		order = thirdGameModel.ThirdSportOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gameCode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			BetTime:      getTimestampUTC(req.Data.Order.PlaceTime),
			BetLocalTime: getTimestampLocal(req.Data.Order.PlaceTime),
			Currency:     req.Data.Order.Currency,
			BetCtx:       betCtxZh,
			RawData:      reqCtx,
			State:        1,
			DataState:    -1,
			BetCtxType:   3,
			CreateTime:   thirdTime,
			// 添加混合投注相关字段
			BetType:        betType,
			BonusBetAmount: bonusBetAmount,
			BonusWinAmount: 0,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("Cbk Debit下注 创建订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}
		// 不需要手动创建账变记录，已经在 DeductAmountWithLogs 中完成

		// 获取最新的用户余额
		updatedBalance, err := l.baseThirdService.GetUserBalance(userId)
		if err != nil {
			logs.Error("Cbk Debit 获取最新用户余额失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance - betAmount // 估算下注后的余额
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}

		// 计算总余额（真金+Bonus币）
		updatedTotalBalance := updatedBalance.Amount + updatedBalance.BonusAmount
		updatedTotalBalance = float64(int(updatedTotalBalance*100)) / 100

		res.Status = "OK"
		res.Balance = updatedTotalBalance
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Info("[fb] Cbk Debit下注成功 thirdId=", thirdId)
		return nil
	})

	// 推送下注事件通知
	if l.thirdGamePush != nil {
		l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		v := l
		if v == nil || v.RefreshUserAmountFunc == nil {
			return
		}
		if v.RefreshUserAmountFunc != nil {
			tmpErr := v.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][CbkT] Debit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			} else {
				return
			}
		}

	}(userId)
}

// Credit 结算
/**
* credit 接口amount 不会出现负数
* 比如下注100，赔率1.9
* debit接口： 扣除100
* credit接口：
* 赢：加190，
* 输：不加不减amount=0，
* 平或取消：加100
* 两种输赢判断方法：
* 钱包金额加减操作以amount为准；输赢判断以profit 来
* 1、注单的profit信息
* 2、钱包一笔交易的debit与credit（一个注单的交易ID相同），但需要考虑重结,所以最简单还是注单profit信息
 */
func (l CbkConfig) Credit(ctx *abugo.AbuHttpContent) {
	authToken := ctx.Query("authToken")
	logs.Debug("Credit", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Credit 结算 失败 token验证不通过,authToken=", authToken)
		return
	}

	// 当三方返回的同字段数据格式不一致时解析int和字符串不能自动转换
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqCtx := string(reqDataByte)
	logs.Info("Cbk Credit api receive:%s, ", string(reqDataByte))

	req := cbkReq{}
	res := cbkResp{}
	err := json.Unmarshal(reqDataByte, &req)
	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Credit 结算参数解析失败", err.Error())
		return
	}

	userId, _ := strconv.Atoi(req.UserId) //base.Token2UserId(cacheKeyCbk, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Credit 结算失败，账号不存在，userId= ", userId)
		return
	}

	//三方来源的数据整理
	var (
		amount    = req.Transaction.Amount
		winAmount = amount
		thirdId   = req.Transaction.Id.String()
		thirdTime = utils.GetCurrentTime()
	)

	//保存注单详情
	reqDataByte, _ = json.Marshal(req.Data.Order)
	betCtx := string(reqDataByte)
	betCtxZh := l.getBetCtxFromRowData(betCtx)

	//有效投注金额取profit的绝对值，或者stake值
	//profit_ := req.Data.Order.CurrencyProfit //用户盈利
	//profit, _ := strconv.ParseFloat(profit_, 64)
	//validBet := math.Abs(amount - profit)
	//currencySize_ := req.Data.Order.CurrencySize //用户下注金额
	//currencySize, _ := strconv.ParseFloat(currencySize_, 64)
	//validBet := currencySize //有效流水取用户下注金额

	// 开始结算事务
	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	err = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 获取用户余额（包含真金和Bonus币）
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("Cbk 结算 查询用户余额失败 thirdId=", thirdId, "userId=", userId, "err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 计算总余额（真金+Bonus币）
		totalBalance := userBalance.Amount + userBalance.BonusAmount
		totalBalance = float64(int(totalBalance*100)) / 100
		res.Balance = totalBalance
		// 查询注单是否存在
		order := thirdGameModel.ThirdSportOrder{}
		e = tx.Table(tablePre).Clauses(clause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			if errors.Is(e, gorm.ErrRecordNotFound) {
				logs.Error("Cbk 结算 查询注单失败 thirdId=", thirdId, " err=", e.Error())
			}
			// 没有注单
			logs.Error("Cbk 结算 注单不存在 thirdId=", thirdId, " err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}
		if order.DataState != -1 {
			if order.DataState == 1 {
				logs.Error("Cbk 结算 注单已结算 thirdId=", thirdId, " order=", order)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = 0
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				e = errors.New("注单状态异常")
				return e
			} else if order.DataState == -2 {
				logs.Error("Cbk 结算 注单已取消 thirdId=", thirdId, " order=", order)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = 0
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return e
			} else {
				logs.Error("Cbk 结算 注单状态异常 thirdId=", thirdId, " order=", order)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = 0
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				e = errors.New("注单状态异常")
				return e
			}
		}

		// 计算派彩金额分配
		realWinAmount, bonusWinAmount := l.baseThirdService.CalculateWinDistribution(order.BetAmount, winAmount, order.BonusBetAmount)
		realBetAmount := order.BetAmount - order.BonusBetAmount
		validBet := math.Abs(realWinAmount - realBetAmount)
		if validBet > math.Abs(realBetAmount) {
			validBet = math.Abs(realBetAmount)
		}
		//validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值

		upData := map[string]interface{}{
			"WinAmount":      winAmount,
			"BonusWinAmount": bonusWinAmount,
			"ValidBet":       validBet,
			"RawData":        reqCtx,
			"ThirdTime":      thirdTime,
			"DataState":      1,
			"GameRst":        betCtxZh,
			"SettleTime":     getTimestampUTC(req.Data.Order.SettledTime),
		}
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(upData)
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("Cbk 结算 更新注单失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e

		}
		order.WinAmount = winAmount
		order.BonusWinAmount = bonusWinAmount
		order.ValidBet = validBet
		order.RawData = reqCtx
		order.DataState = 1
		order.ThirdTime = thirdTime
		order.BetCtxType = 3
		order.CreateTime = thirdTime
		order.SettleTime = getTimestampUTC(req.Data.Order.SettledTime)
		order.Id = 0

		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("Cbk 结算 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 使用 AddAmountWithLogs 增加用户余额
		e = l.baseThirdService.AddAmountWithLogs(
			tx,
			userBalance,
			realWinAmount,
			bonusWinAmount,
			utils.BalanceCReasonCBKSettle,
			l.brandName+" settle,thirdId:"+thirdId,
			thirdTime,
		)
		if e != nil {
			logs.Error("Cbk 结算 增加用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 获取最新的用户余额
		updatedBalance, err := l.baseThirdService.GetUserBalance(userId)
		if err != nil {
			logs.Error("Cbk Credit 获取最新用户余额失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance + winAmount // 估算结算后的余额
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}

		// 计算总余额（真金+Bonus币）
		updatedTotalBalance := updatedBalance.Amount + updatedBalance.BonusAmount
		updatedTotalBalance = float64(int(updatedTotalBalance*100)) / 100
		logs.Info("Cbk Credit :", "結算成功,thirdId=", thirdId)

		res.Status = "OK"
		res.Balance = updatedTotalBalance
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return nil
	})

	// 推送奖励事件通知
	if l.thirdGamePush != nil && winAmount > 0 {
		//l.thirdGamePush.PushRewardEvent(userId, req.Data.Order.EventName, l.brandName, order.BetAmount, winAmount, l.currency)
		//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		l.thirdGamePush.PushRewardEvent(6, l.brandName, thirdId)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		v := l
		if v.RefreshUserAmountFunc != nil {
			tmpErr := v.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][CbkT] Credit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			} else {

			}
		}
	}(userId)
}

// Cancel 取消请求
func (l CbkConfig) Cancel(ctx *abugo.AbuHttpContent) {
	authToken := ctx.Query("authToken")
	logs.Debug("Cancel", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Cancel 取消在注单 失败 token验证不通过,authToken=", authToken)
		return
	}
	// 当三方返回的同字段数据格式不一致时解析int和字符串不能自动转换
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("Cbk Cancel api receive:%s, ", string(reqDataByte))

	req := cbkReq{}
	res := cbkResp{}
	err := json.Unmarshal(reqDataByte, &req)
	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Cancel 取消在注单,参数解析失败", err.Error())
		return
	}

	//保存注单详情
	reqDataByte, _ = json.Marshal(req.Data.Order)

	// 三方返回的userId
	userId, _ := strconv.Atoi(req.UserId) // base.Token2UserId(cacheKeyCbk, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Cancel 取消注单 用户id不存在 userId = ", userId)
		return
	}

	//三方来源的数据整理
	var (
		// 三方cancel接口问题 未返回数据 暂时取orde的数值
		amount    = req.Transaction.Amount
		thirdId   = req.Data.Order.Id.String()
		thirdTime = utils.GetCurrentTime()
	)

	// 取消注单
	tablePre := "x_third_sport_pre_order"
	err = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 获取用户余额（包含真金和Bonus币）
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("Cbk Cancel 取消注单 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 计算总余额（真金+Bonus币）
		totalBalance := userBalance.Amount + userBalance.BonusAmount
		totalBalance = float64(int(totalBalance*100)) / 100
		res.Balance = totalBalance

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(clause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			if errors.Is(e, gorm.ErrRecordNotFound) {
				logs.Error("Cbk Cancel 取消注单失败，订单不存在 thirdId=", thirdId, " err=", e.Error())
			}
			logs.Error("Cbk Cancel 取消注单失败 thirdId=", thirdId, " err=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		if order.DataState != -1 {
			if order.DataState == -2 {
				logs.Error("Cbk Cancel 取消注单 注单已取消 thirdId=", thirdId, " order=", order)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = 0
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return e
			} else {
				logs.Error("Cbk Cancel 取消注单 注单状态异常 thirdId=", thirdId, " order=", order)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = 0
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				e = errors.New("注单状态异常")
				return e
			}
		}

		// 设置预设表注单状态为已取消
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"DataState": -2,
			"ThirdTime": thirdTime,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("Cbk Cancel 取消注单 更新注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		betAmount := amount

		// 获取原始下注订单的真金和Bonus币下注金额
		realBetAmount := order.BetAmount - order.BonusBetAmount
		bonusBetAmount := order.BonusBetAmount

		// 按原始下注比例计算退款金额
		var realAmount, bonusAmount float64

		if order.BetAmount > 0 {
			// 如果有原始下注金额，按比例计算
			realAmount = betAmount * (realBetAmount / order.BetAmount)
			bonusAmount = betAmount * (bonusBetAmount / order.BetAmount)
		} else {
			// 如果原始下注金额为0，全部返还到真金账户
			realAmount = betAmount
			bonusAmount = 0
		}

		// 保留两位小数
		realAmount = float64(int(realAmount*100)) / 100

		// 使用 AddAmountWithLogs 增加用户余额
		e = l.baseThirdService.AddAmountWithLogs(
			tx,
			userBalance,
			realAmount,
			bonusAmount,
			utils.BalanceCReasonCBKCancel,
			l.brandName+" cancel,thirdId:"+thirdId,
			thirdTime,
		)
		if e != nil {
			logs.Error("Cbk Cancel 取消注单 增加用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 获取最新的用户余额
		updatedBalance, err := l.baseThirdService.GetUserBalance(userId)
		if err != nil {
			logs.Error("Cbk Cancel 获取最新用户余额失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = totalBalance + betAmount // 估算取消后的余额
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}

		// 计算总余额（真金+Bonus币）
		updatedTotalBalance := updatedBalance.Amount + updatedBalance.BonusAmount
		updatedTotalBalance = float64(int(updatedTotalBalance*100)) / 100
		logs.Info("Cbk 注单取消成功了:  thirdId = ", thirdId, err)
		res.Status = "OK"
		res.Balance = updatedTotalBalance
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		v := l
		if v.RefreshUserAmountFunc != nil {
			tmpErr := v.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][CbkT] Cancel 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			} else {

			}
		}
	}(userId)
}

// Resettle 重新结算
/*
1、重结一般出现在类似乌龙球、或裁判赛后重判等情况
2、resettle接口需要支持把客户扣成负数，或扣为0但保留记录，具体后续可由平台决定（接口amount，正数加钱，负数扣钱）
3、resettle可能会有多次，包括胜负互换，或reVoid注单无效
4、resettle一般发生在首次结算后48小时内
如果重结，赛果变化，但胜负未变的情况，会通过data上报而不调用resettle。
如下注项 得分大于100，首次结算得分为110，胜
重结得分为120，依然为胜，仅update赛果result，无钱包调用
因为有错误重试的机制，取消、结算、重结都需要做一下重复验证
取消、结算都是一次性操作，而重结可能有多次，需要结合重结次数来验证
涉及的注单参数，有重结时会多2个参数：
reSettled = 1  （表示是重结过的注单）
reSettleTimes = 1 （重结的次数）
用户把钱提走了 还重新结算？重新结算业务待讨论
钱包金额加减操作以amount为准；输赢判断以profit来
* resettle接口：
* 赢：加190，
* 输：不加不减amount=0，
* 平或取消：加100
resettle是基于上一次结算的profit 重新算。正数+余额；负数减少余额
重结，金额正数增加，负数扣除。
重新结算过的不能在次重新结算 这里需要加状态判断
重新结算amount 会出现负数
*/
// 板球三方重新结算
func (l CbkConfig) Resettle(ctx *abugo.AbuHttpContent) {
	authToken := ctx.Query("authToken")
	logs.Debug("Resettle", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Resettle 重新结算 失败 token验证不通过,authToken=", authToken)
		return
	}

	// ctx.Gin().ShouldBindJSON(&req)
	// 当三方返回的同字段数据格式不一致时解析int和字符串不能自动转换
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("Cbk Resettle api receive:%s, ", string(reqDataByte))

	req := cbkReq{}
	res := cbkResp{}
	err := json.Unmarshal(reqDataByte, &req)
	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Resettle 重新结算失败,参数解析失败", err.Error())
		return
	}

	//保存注单详情
	reqDataByte, _ = json.Marshal(req.Data.Order)
	//三方来源的数据整理
	var (
		// 基于第一次结算的结果 正数增加余额 负数减少余额
		amount    = req.Transaction.Amount
		thirdId   = req.Transaction.Id.String()
		thirdTime = utils.GetCurrentTime()
	)

	userId, _ := strconv.Atoi(req.UserId) //base.Token2UserId(cacheKeyCbk, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Resettle 重新结算失败,用户id不存在,thirdId=", thirdId, ",userId=", userId)
		return
	}

	betCtx := string(reqDataByte)
	betCtxZh := l.getBetCtxFromRowData(betCtx)

	//重新结算金额不能是0
	if amount == 0 {
		res.Status = "INVALID_AMOUNT"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Resettle 重新结算金额错误", err.Error())
	}
	//有效投注金额取profit的绝对值，或者stake值
	profit := req.Data.Order.CurrencyProfit //用户利润
	profit_, err := strconv.ParseFloat(profit, 64)
	currencySize_ := req.Data.Order.CurrencySize //用户下注金额
	currencySize, _ := strconv.ParseFloat(currencySize_, 64)
	validBet := currencySize //有效流水取用户下注金额

	// 钱包金额加减操作以amount为准；输赢判断以profit来
	var winAmount = 0.0
	if profit_ < 0.0 { //如果用户输钱 派奖金额为0
		winAmount = 0.0
	} else { //如果用户赢钱 派奖金额是用户盈利+用户投注金额
		winAmount = currencySize + profit_
	}

	logs.Info("Cbk 重新结算金额 :", "重新結算", winAmount, validBet, thirdId)

	//开启事务
	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	err = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, gorm.ErrRecordNotFound) {
				logs.Error("Cbk Resettle 重新结算 会员不存在 thirdId=", thirdId, "userId=", userId)
			}
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			logs.Error("Cbk Resettle 重新结算 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			return e
		}
		res.Balance = userBalance.Amount

		order := thirdGameModel.ThirdSportOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(clause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("Cbk Resettle 订单不存在 thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}
		if order.DataState != 1 {
			logs.Error("Cbk Resettle 订单不是已结算状态，不能重新结算 thirdId=", thirdId, " state=", order.DataState, " order=", order)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return errors.New("订单已完成")
		}
		userId := order.UserId
		validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值

		//更新订单表
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":      winAmount,
			"ValidBet":       validBet,
			"DataState":      1,
			"ThirdTime":      thirdTime,
			"GameRst":        betCtxZh,
			"ResettleState":  1,
			"ResettleNumber": gorm.Expr("ResettleNumber+?", 1),
			"ResettleTime":   thirdTime,
			"SettleTime":     getTimestampUTC(req.Data.Order.SettledTime),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("Cbk Resettle 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		//更新统计表
		//问题：重新结算后用户提款了怎么办？是否修改账变？统计报表是实时结算即DataState = 1就会返佣
		resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
			"WinAmount":  winAmount,
			"ValidBet":   validBet,
			"ThirdTime":  thirdTime,
			"GameRst":    betCtxZh,
			"SettleTime": getTimestampUTC(req.Data.Order.SettledTime),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("Cbk Resettle 更新统计表失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 获取原始下注订单的真金和Bonus币下注金额
		realBetAmount := order.BetAmount - order.BonusBetAmount
		bonusBetAmount := order.BonusBetAmount

		// 按原始下注比例计算重新结算金额
		var realAmount, bonusAmount float64

		if order.BetAmount > 0 {
			// 如果有原始下注金额，按比例计算
			realAmount = amount * (realBetAmount / order.BetAmount)
			bonusAmount = amount * (bonusBetAmount / order.BetAmount)
		} else {
			// 如果原始下注金额为0，全部分配到真金账户
			realAmount = amount
			bonusAmount = 0
		}

		// 获取最新的用户余额对象
		updatedUserBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("Cbk 结算 获取最新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}

		// 使用 AddAmountWithLogs 增加用户余额
		e = l.baseThirdService.AddAmountWithLogs(
			tx,
			updatedUserBalance,
			realAmount,
			bonusAmount,
			utils.BalanceCReasonCBKBet, // 使用现有的原因代码
			l.brandName+" win,thirdId:"+thirdId,
			thirdTime,
		)
		if e != nil {
			logs.Error("Cbk 结算 增加用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}
		//重新结算增加记录日志
		state := 0 //状态 0-重新结算1-撤销结算
		resettleTime := thirdTime
		changeType := 0 // 0增加 1减少
		if amount <= 0 {
			changeType = 1
		}
		memo := "重新结算"
		e = base.AddSportResettleLog(tx, order, thirdTime, betCtx, amount, changeType, state, thirdId, resettleTime, "", memo)
		if e != nil {
			logs.Error("Cbk Resettle 增加重新结算记录失败 userId=", order.UserId, " thirdId=", order.ThirdId, " e=", e.Error())
			res.Status = "TEMPORARY_ERROR"
			res.Balance = 0
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return e
		}
		res.Status = "OK"
		res.Balance = userBalance.Amount
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Info("[Cbk] Resettle 重新结算成功 thirdId=", thirdId)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		v := l
		if v.RefreshUserAmountFunc != nil {
			tmpErr := v.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][CbkT] Resettle 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			} else {

			}
		}
	}(userId)
}

type cbkDataReq struct {
	Uuid  string `json:"uuid"`
	Order Order  `json:"order"` //订单数据
}

// 板球三方数据上报
func (l CbkConfig) Data(ctx *abugo.AbuHttpContent) {
	authToken := ctx.Query("authToken")
	logs.Debug("Cbk Data", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		logs.Error("Cbk Data 板球三方数据上报 失败 token验证不通过,authToken=", authToken)
		return
	}

	req := cbkDataReq{}
	res := cbkResp{}
	// 当三方返回的同字段数据格式不一致时解析int和字符串不能自动转换
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("Cbk Data api receive:%s, ", string(reqDataByte))
	err := json.Unmarshal(reqDataByte, &req)
	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk Data,参数解析失败", err.Error())
		return
	}

	userId, _ := strconv.Atoi(req.Order.UserId) // base.Token2UserId(cacheKeyCbk, req.Sid)
	//三方来源的数据整理
	var (
		thirdId = req.Order.Id.String()
	)

	logs.Info("Cbk 数据上报,  userId = ", userId, thirdId)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk 数据上报 用户Id不存在,thirdId=", thirdId, "userId=", userId)
		return
	}

	_, balance, err := base.GetUserBalance(userId, cacheKeyCbk)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		logs.Error("Cbk 数据上报 取消注单,查询用余额失败,thirdId=", thirdId, "userId=", userId, err.Error())
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", l.brandName, nil)
	betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
	if betTran == nil {
		//订单不存在
		logs.Error("Cbk 订单不存在:  id = ", thirdId)
		res.Status = "BET_DOES_NOT_EXIST"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	betCtx := string(reqDataByte)
	betCtxZh := l.getBetCtxFromRowData(betCtx)
	var tablepre = "x_third_sport_pre_order"
	//更新订单下注内容
	e := server.Db().GormDao().Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
		"BetCtx": betCtxZh,
	}).Error
	if e != nil {
		logs.Error("cbk 单一钱包 data 上报下注内容错误 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
	} else {
		logs.Info("cbk 单一钱包 data 上报下注内容成功 userId=", userId, " thirdId=", thirdId)
	}

	res.Status = "OK"
	res.Balance = balance2
	res.Uuid = abugo.GetUuid()
	ctx.RespJson(res)
	logs.Info("Cbk 数据上报 成功,thirdId=", thirdId, "res=", res)
}

func getTimestampUTC(placeTime interface{}) *string {
	var timestamp int64
	switch v := placeTime.(type) {
	case string:
		t, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil
		}
		timestamp = t
	case float64:
		timestamp = int64(v)
	case int64:
		timestamp = v
	case json.Number:
		t, err := v.Int64()
		if err != nil {
			return nil
		}
		timestamp = t
	default:
		return nil
	}
	return utils.TimestampToUTC(timestamp)
}

func getTimestampLocal(placeTime interface{}) *string {
	var timestamp int64
	switch v := placeTime.(type) {
	case string:
		t, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil
		}
		timestamp = t
	case float64:
		timestamp = int64(v)
	case int64:
		timestamp = v
	case json.Number:
		t, err := v.Int64()
		if err != nil {
			return nil
		}
		timestamp = t
	default:
		return nil
	}
	return utils.TimestampIntToLocal(timestamp)
}
