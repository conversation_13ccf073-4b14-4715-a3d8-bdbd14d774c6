// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserWallet = "x_user_wallet"

// XUserWallet mapped from table <x_user_wallet>
type XUserWallet struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID     int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID    int32     `gorm:"column:ChannelId" json:"ChannelId"`
	UserID       int32     `gorm:"column:UserId;comment:玩家" json:"UserId"`                   // 玩家
	Address      string    `gorm:"column:Address;comment:地址" json:"Address"`                 // 地址
	State        int32     `gorm:"column:State;default:2;comment:状态 1已验证 2未验证" json:"State"` // 状态 1已验证 2未验证
	VerifyAmount int32     `gorm:"column:VerifyAmount;comment:验证金额" json:"VerifyAmount"`     // 验证金额
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	IsTgSend     int32     `gorm:"column:IsTgSend;comment:是否发送已TG绑定成功消息(0:否 1:是)" json:"IsTgSend"` // 是否发送已TG绑定成功消息(0:否 1:是)
	IsStat       int32     `gorm:"column:IsStat;comment:是否统计(0:否 1:是)" json:"IsStat"`              // 是否统计(0:否 1:是)
	Rank         int32     `gorm:"column:Rank;default:1" json:"Rank"`
	Net          string    `gorm:"column:Net;default:tron;comment:网络" json:"Net"` // 网络
}

// TableName XUserWallet's table name
func (*XUserWallet) TableName() string {
	return TableNameXUserWallet
}
