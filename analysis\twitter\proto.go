package twitter

import "time"

const (
	EventName_CompleteRegistration = "tw-onila-onilq" // 完成注册
	EventName_Purchase             = "tw-onila-onilf" // 完成购买或结账流程
	EventName_AddToCart            = "tw-onila-onild" // 添加到购物车
	EventName_FirstRecharge        = "tw-onila-onilm" // 首次充值
)

type PostData struct {
	Conversions []Conversion `json:"conversions"`
}

type Conversion struct {
	ConversionTime time.Time    `json:"conversion_time"`
	EventID        string       `json:"event_id"`
	Identifiers    []Identifier `json:"identifiers"`
	Value          float64      `json:"value"`
	NumberItems    int          `json:"number_items"`
	ConversionID   string       `json:"conversion_id"`
	Description    string       `json:"description"`
	Contents       []Content    `json:"contents"`
}

type Identifier struct {
	Twclid      string `json:"twclid,omitempty"`
	HashedEmail string `json:"hashed_email,omitempty"`
}

type Content struct {
	ContentID    string  `json:"content_id"`
	ContentPrice float64 `json:"content_price"`
	NumItems     int     `json:"num_items"`
}
