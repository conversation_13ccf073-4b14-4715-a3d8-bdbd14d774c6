package controller

import (
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/server"
)

type PandaController struct {
}

func (c *PandaController) Init() {
	server.Http().PostNoAuth("/api/panda/login", c.login)
	server.Http().PostNoAuth("/api/panda/win", c.win)
	server.Http().PostNoAuth("/api/panda/child_detail", c.child_detail)
	server.Http().PostNoAuth("/api/panda/withdraw_info", c.withdraw_info)
	server.Http().PostNoAuth("/api/panda/withdraw", c.withdraw)
	server.Http().PostNoAuth("/api/panda/order", c.order)
	server.Http().PostNoAuth("/api/panda/order_update", c.order_update)
}

func (c *PandaController) login(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		Address string `validate:"required"`
		Agent   string
	}{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Address == reqdata.Agent {
		reqdata.Agent = ""
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Address", "=", reqdata.Address, nil)
	for {
		user, _ := server.Db().Table("x_panda").Where(where).GetOne()
		if user != nil {
			(*user)["RechargeAddress"] = server.GetConfigString(1, 0, "LHRechargeAddress")
			(*user)["UpgradeAddress"] = server.GetConfigString(1, 0, "LHAgentBuyAddress")
			childcount, _ := server.Db().Query("select count(id) as count from x_panda where Agent = ?", []interface{}{reqdata.Address})
			d1, _ := server.Db().Query("SELECT IFNULL(sum(Amount),0) as RechargeTrx FROM x_panda_recharge WHERE Symbol = 'trx' and  Address in (SELECT Address FROM x_panda WHERE Agent = ?)", []interface{}{reqdata.Address})
			d2, _ := server.Db().Query("SELECT IFNULL(sum(Amount),0) as RechargeUsdt FROM x_panda_recharge WHERE   Symbol = 'usdt' and Address in (SELECT Address FROM x_panda WHERE Agent = ?)", []interface{}{reqdata.Address})

			(*user)["RechargeTrx"] = (*d1)[0]["RechargeTrx"]
			(*user)["RechargeUsdt"] = (*d2)[0]["RechargeUsdt"]
			(*user)["ChildCount"] = (*childcount)[0]["count"]
			agent := abugo.GetStringFromInterface((*user)["Agent"])
			if len(agent) > 0 {
				agent = "yes"
			}
			(*user)["Agent"] = agent
			server.Db().Conn().Exec("update x_panda set LastLoginTime = now() where Address = ?", reqdata.Address)
			ctx.RespOK(user)
			break
		} else {
			server.Db().Conn().Exec("insert into x_panda(Address,Agent,IsAgent,AmountTrx,AmountUsdt,GiftTrx,GiftUsdt)values(?,?,2,100,0,100,0)", reqdata.Address, reqdata.Agent)
			server.Db().Conn().Exec("update x_panda set ChildCount = ChildCount + 1 where Address = ?", reqdata.Agent)
		}
	}
}

func (c *PandaController) win(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address string  `validate:"required"`
		Symbol  string  `validate:"required"`
		Amount  float64 `validate:"min=0"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Amount <= 0 {
		ctx.RespErrString(true, &errcode, "失败")
		return
	}
	result, _ := server.Db().CallProcedure("x_panda_win", reqdata.Address, reqdata.Symbol, reqdata.Amount)
	amount := abugo.GetFloat64FromInterface((*result)["Amount"])
	ctx.Put("Amount", amount)
	ctx.RespOK()
}

func (c *PandaController) child_detail(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address string `validate:"required"`
		Days    int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Days > 31 {
		ctx.RespErrString(true, &errcode, "查询失败,查询时间太长")
		return
	}
	StartTime := int64(0)
	EndTime := int64(0)
	if reqdata.Days == 0 {
		StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate()) * 1000
		EndTime = StartTime + 86400000
	}
	if reqdata.Days == 1 {
		StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate())*1000 - 86400000
		EndTime = StartTime + 86400000
	}
	if reqdata.Days >= 3 {
		StartTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate())*1000 - 86400000*int64(reqdata.Days)
		EndTime = abugo.LocalDateToTimeStamp(abugo.GetLocalDate())*1000 + 86400000
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Agent", "=", reqdata.Address, nil)
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalDate(StartTime), "")
	where.Add("and", "CreateTime", "<", abugo.TimeStampToLocalDate(EndTime), "")
	data, _ := server.Db().Table("x_panda").Select("Address,ChildCount,RewardTrx,RewardUsdt,AmountTrx,AmountUsdt,UsedAmountTrx,UsedAmountUsdt,AgentTrx,AgentUsdt").Where(where).GetList()
	for i := 0; i < len(*data); i++ {
		d1, _ := server.Db().Query("select IFNULL(sum(Amount),0) as RechargeTrx from x_panda_recharge where Symbol = 'trx' and Address = ?", []interface{}{(*data)[i]["Address"]})
		d2, _ := server.Db().Query("select IFNULL(sum(Amount),0) as	RechargeUsdt from x_panda_recharge where Symbol = 'usdt' and Address = ?", []interface{}{(*data)[i]["Address"]})
		(*data)[i]["RechargeTrx"] = (*d1)[0]["RechargeTrx"]
		(*data)[i]["RechargeUsdt"] = (*d2)[0]["RechargeUsdt"]
	}
	ctx.RespOK(data)
}

func (c *PandaController) withdraw_info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address   string `validate:"required"`
		Symbol    string
		StartTime string
		EndTime   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Address", "=", reqdata.Address, nil)
	data, _ := server.Db().Table("x_panda").Select("RewardTrx,RewardUsdt,RewardValidTrx,RewardValidUsdt").Where(where).GetOne()
	where = abugo.AbuDbWhere{}
	where.Add("and", "Symbol", "=", reqdata.Symbol, nil)
	where.Add("and", "Address", "=", reqdata.Address, nil)
	where.Add("and", "CreateTime", ">=", reqdata.StartTime, "")
	where.Add("and", "CreateTime", "<", reqdata.EndTime, "")
	history, _ := server.Db().Table("x_panda_withdraw").Select("Id,Symbol,Amount,State,CreateTime").OrderBy("id desc").Where(where).GetList()
	(*data)["History"] = history
	ctx.RespOK(data)
}

func (c *PandaController) withdraw(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Address string  `validate:"required"`
		Symbol  string  `validate:"required"`
		Amount  float64 `validate:"min=0"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if strings.ToLower(reqdata.Symbol) == "trx" && reqdata.Amount < 100 {
		ctx.RespErrString(true, &errcode, "提现金额最低为: 100")
		return
	}
	if strings.ToLower(reqdata.Symbol) == "usdt" && reqdata.Amount < 10 {
		ctx.RespErrString(true, &errcode, "提现金额最低为: 10")
		return
	}
	result, _ := server.Db().CallProcedure("x_panda_withdraw", reqdata.Address, reqdata.Symbol, reqdata.Amount)
	if ctx.RespProcedureErr(result) {
		return
	}
	ctx.RespOK()
}

func (c *PandaController) order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address  string  `validate:"required"`
		Symbol   string  `validate:"required"`
		Amount   float64 `validate:"min=0"`
		GameId   int     `validate:"required"`
		BlockNum int     `validate:"required"`
		TxId     string  `validate:"required"`
		PeiLv    float32 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	data := abugo.ObjectToMap(reqdata)
	server.Db().Table("x_panda_order").Insert(*data)
	server.Db().Conn().Exec("update x_panda set FirstBetTime = now() where Address = ? and FirstBetTime = '2000-01-01 00:00:00'", reqdata.Address)
	if reqdata.Symbol == "trx" {
		server.Db().Conn().Exec("update x_panda set FirstBetTimeTrx = now() where Address = ? and FirstBetTimeTrx  = '2000-01-01 00:00:00'", reqdata.Address)
	}
	if reqdata.Symbol == "usdt" {
		server.Db().Conn().Exec("update x_panda set FirstBetTimeUsdt = now() where Address = ? and FirstBetTimeUsdt  = '2000-01-01 00:00:00'", reqdata.Address)
	}
	ctx.RespOK()
}

func (c *PandaController) order_update(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		TxId  string `validate:"required"`
		IsWin int    `validate:"required"` // 1赢,2输
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.IsWin != 1 {
		reqdata.IsWin = 2
	}
	if reqdata.IsWin == 1 {
		_, err = server.Db().Conn().Exec("update x_panda_order set IsWin = ?,State = 1,RewardAmount = Amount * PeiLv where TxId = ? ", reqdata.IsWin, reqdata.TxId)
		if err != nil {
			fmt.Println(err)
		}
	} else {
		_, err = server.Db().Conn().Exec("update x_panda_order set IsWin = ?,State = 1 where TxId = ? ", reqdata.IsWin, reqdata.TxId)
		if err != nil {
			fmt.Println(err)
		}
	}
	ctx.RespOK()
}
