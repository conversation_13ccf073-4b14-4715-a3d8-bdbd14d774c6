package third

import (
	"encoding/json"
	"fmt"
	"testing"
	"xserver/abugo"

	"github.com/beego/beego/logs"
)

func TestEasybet(t *testing.T) {
	srv := NewEasybetSrvice("", "", "", "", "", "", nil)
	userId := 123456
	ebToken := srv.userId2token(userId)
	fmt.Println(ebToken)
	if ebToken == "" {
		t.Error()
	}
	userId2 := srv.token2UserId(ebToken)
	fmt.Println(userId2)
	if userId != userId2 {
		t.Error()
	}
}

func TestEasybetParseRes(t *testing.T) {
	body := []byte(`
	{
		"status": 0,
		"msg": "success",
		"extend": {
		  "date": "2023-11-28 19:11:56",
		  "microtime": 1701169916.164597,
		  "unique": "6565cafb77fa01284933644",
		  "version": 0
		},
		"data": {
		  "url": "https://qa8.sport.nqsf9emow.com/skin001.html?t=73368b8f2175625880009eb0363bc19c4ac08302340fdbeaba048cb643a922a1f7113ec1d72c0375d95b834eaa417bc9ec0575529771ae071b0a518517869211&a=6&displayname=&timezone_change=1&lang=zh-TW&daynight_type=1"
		}
	  }
	`)
	jdata := map[string]interface{}{}
	err := json.Unmarshal(body, &jdata)
	if err != nil {
		t.Error("easybet_http_post json error:", err)
	}

	var status int64
	if statusInf, ok := jdata["status"]; ok {
		status = abugo.GetInt64FromInterface(statusInf)
	} else {
		fmt.Println("easybet_http_post 解析失败")
		t.FailNow()
	}

	if status != 0 {
		msg := "response msg 異常"
		if rmsg, ok := jdata["msg"].(string); ok {
			msg = rmsg
		}
		fmt.Println(msg)
		t.FailNow()
	}

	fmt.Printf("%v\n", jdata)
	r := (jdata)["data"].(map[string]interface{})
	fmt.Println(r["url"])
}

func TestEasybetGetGameName(t *testing.T) {
	type ResponseLog struct {
		Status int    `json:"status"`
		Msg    string `json:"msg"`
		Data   struct {
			List []struct {
				SportID      int    `json:"sport_id"`      // The field we're interested in
				PartnerOrder string `json:"partner_order"` // The field we're interested in
			} `json:"list"`
		} `json:"data"`
	}

	// Marshal the map into a JSON string
	jsonString := `{"status":0,"msg":"success","extend":{"date":"2024-03-26 02:53:21","microtime":1711392801.112919,"unique":"6601c821188db378788568","version":0,"runtime":"12.48 ms"},"data":{"list":[{"order_no":"32MUL5617","status":1,"is_settle":0,"win":"0.000","market_type":"","content":"","order_type":0,"stake":"10.00","currency":"HKD","fx_rate":"1.000000000000","create_time":"2024-03-25 21:42:59","odds":"6.45","wid":0,"maybe_payout":"15300","event_time":"1970-01-01 08:00:00","handicap":"","sid":"","sn":"","combo_name":"","score":"","settle_score":"","user_id":300116686,"partner_order":"6792393029316325376","combo_list":[{"id":18330,"partner_order":"6792393029316325376","market_id":"4caab5b2-5a2b-4767-a1d7-738b3bfb1afd","market_type":"TOTAL_GOALS_HALF_TIME","content":"Under 0.5 goals","order_type":0,"odds":"2.53","final_odds":"2.53","event_time":"2024-03-26 00:00:00","is_settle":1,"handicap":"0.50","sid":"1","sn":"Under 0.5 goals","event_name":"Azerbaijan v Bulgaria","home_name":"Azerbaijan","away_name":"Bulgaria","score":"0-0","cpn":"79668","ors":0,"event_id":250241,"settle_time":1711385495,"settle_score":"HT(0-0)","side":"Back","selection_id":"1","sport_id":1,"vendor_id":2,"mark":"","leg_id":"leg1","type":0,"s_type":"Unders","win_type":1,"state":"[]","settle_state":"{\"id\":94933,\"match_id\":\"FBL-1204587\",\"match_version\":370,\"match_phase\":\"FT\",\"phase_minute\":-1,\"update_time\":1711389107416,\"goals_ft\":\"1-1\",\"goals_ht\":\"0-0\",\"goals_otht\":\"0-0\",\"goals_ot\":\"0-0\",\"goals_pk\":\"0-0\",\"corners_ht\":\"6-5\",\"corners_ft\":\"9-8\",\"corners_otht\":\"0-0\",\"corners_ot\":\"0-0\",\"yellow_cards_ht\":\"3-1\",\"yellow_cards_ft\":\"3-3\",\"yellow_cards_otht\":\"0-0\",\"yellow_cards_ot\":\"0-0\",\"red_cards_ht\":\"0-0\",\"red_cards_ft\":\"0-0\",\"red_cards_otht\":\"0-0\",\"red_cards_ot\":\"0-0\",\"last_time\":1711389145,\"checkpoint\":\"1711389107417\"}","price_index":0,"resettle_version":0,"sort":0,"combo_type":"2X1*1","cpn_name":"International Friendly Games","market_type_text":"Total Goals - Half Time","translate_type":{"market":7,"tip":8}},{"id":18329,"partner_order":"6792393029316325376","market_id":"4449cde0-9694-44d3-ba28-9bb7531b09d7","market_type":"TEAM_A_EXACT_GOALS_HALF_TIME","content":"One","order_type":0,"odds":"2.55","final_odds":"2.55","event_time":"2024-03-26 02:00:00","is_settle":1,"handicap":"","sid":"1","sn":"One","event_name":"Sweden v Albania","home_name":"Sweden","away_name":"Albania","score":"0-0","cpn":"79668","ors":0,"event_id":250217,"settle_time":1711392799,"settle_score":"HT(0-0)","side":"Back","selection_id":"1","sport_id":1,"vendor_id":2,"mark":"","leg_id":"leg0","type":0,"s_type":"One","win_type":4,"state":"[]","settle_state":"{\"id\":94909,\"match_id\":\"FBL-1207502\",\"match_version\":194,\"match_phase\":\"HT\",\"phase_minute\":-1,\"update_time\":1711392497315,\"goals_ft\":\"0-0\",\"goals_ht\":\"0-0\",\"goals_otht\":\"0-0\",\"goals_ot\":\"0-0\",\"goals_pk\":\"0-0\",\"corners_ht\":\"2-1\",\"corners_ft\":\"2-1\",\"corners_otht\":\"0-0\",\"corners_ot\":\"0-0\",\"yellow_cards_ht\":\"0-0\",\"yellow_cards_ft\":\"0-0\",\"yellow_cards_otht\":\"0-0\",\"yellow_cards_ot\":\"0-0\",\"red_cards_ht\":\"0-0\",\"red_cards_ft\":\"0-0\",\"red_cards_otht\":\"0-0\",\"red_cards_ot\":\"0-0\",\"last_time\":1711392543,\"checkpoint\":\"1711392540374\"}","price_index":0,"resettle_version":0,"sort":0,"combo_type":"2X1*1","cpn_name":"International Friendly Games","market_type_text":"Home Team Exact Goals - Half Time","translate_type":{"market":7,"tip":8}}],"cpn":"","ors":0,"event_id":0,"settle_time":"1970-01-01 08:00:00","size_matched":0,"side":"BACK","selection_id":"","sport_id":0,"event_name":"","home_name":"","away_name":"","s_type":"","win_type":0,"state":[],"mark":"","resettle_version":0,"bet_type":"multi","cash_out_status":0,"cash_out_amount":"0.000","cash_out_detail":"","data_update_version":2,"is_sp":0,"event_time_timestamp":0,"create_time_timestamp":1711374179,"settle_time_timestamp":0,"expected_win":"54.500","username":"74328839","market_type_text":"_NAME","translate_type":[],"home_score":0,"away_score":0,"real_time_state":[],"bet_home_score":0,"bet_away_score":0,"c_type":"","number_of_session":null,"is_race":0,"user_level":"58-easybet-external9","valid_stake":0}],"pageInfo":{"pageCurrent":1,"pageCount":1,"pageSize":40,"pageTotal":1}}}`

	var response ResponseLog

	// Unmarshal the JSON string into the response struct
	if err2 := json.Unmarshal([]byte(jsonString), &response); err2 != nil {
		logs.Debug("Error parsing JSON:", err2)
	}

	order_id := "6792393029316325376"

	for i := range response.Data.List {
		SportID := response.Data.List[i].SportID
		PartnerOrder := response.Data.List[i].PartnerOrder
		logs.Debug("PartnerOrder", PartnerOrder, "order_id", order_id)
		if PartnerOrder == order_id {

			sportsMapping := map[int]string{
				0: "串关",
				1: "足球",
				4: "篮球",
				5: "美式足球",
				7: "赛马",
				8: "赛狗",
				9: "马车赛",
			}
			GameName := ""
			if chineseName, ok := sportsMapping[SportID]; ok {
				GameName = chineseName
			}
			logs.Debug("PartnerOrder", PartnerOrder, "SportID", SportID, "GameName", GameName)
		}
	}
}
