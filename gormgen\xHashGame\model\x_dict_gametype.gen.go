// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXDictGametype = "x_dict_gametype"

// XDictGametype 游戏场馆分类
type XDictGametype struct {
	GameType       int32     `gorm:"column:GameType;primaryKey;comment:游戏分类" json:"GameType"`                             // 游戏分类
	GameTypeName   string    `gorm:"column:GameTypeName;comment:游戏分类名" json:"GameTypeName"`                               // 游戏分类名
	ParentType     int32     `gorm:"column:ParentType;not null;comment:上级分类" json:"ParentType"`                           // 上级分类
	ParentTypeName string    `gorm:"column:ParentTypeName;comment:上级分类名" json:"ParentTypeName"`                           // 上级分类名
	Memo           string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                  // 描述
	Status         int32     `gorm:"column:Status;not null;default:1;comment:1有效 2无效" json:"Status"`                      // 1有效 2无效
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XDictGametype's table name
func (*XDictGametype) TableName() string {
	return TableNameXDictGametype
}
