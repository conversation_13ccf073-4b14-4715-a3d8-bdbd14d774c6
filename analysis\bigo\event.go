package bigo

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"time"
)

func (c *Client) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	logs.Info("Bigo 请求参数", string(data))
	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, err
	}

	logs.Info("Bigo 返回：", resp.String())

	return resp, nil
}

func (c *Client) PushEvent(clickId string, eventName string, amount float64) (err error) {

	data := &PostData{
		Bbg:         clickId,
		PixelId:     c.pixelId,
		TimestampMs: time.Now().UnixMicro(),
		Event: Event{
			EventId:      eventName,
			Currency:     "USD",
			Monetary:     amount,
			OrigMonetary: amount,
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = c.post(c.api, map[string]string{
		"Content-Type": "application/json",
	}, jsonData)

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) AddToCart(clickId string) (err error) {
	return c.PushEvent(clickId, EventName_AddToCart, 0)
}

func (c *Client) CompleteRegistration(clickId string) (err error) {
	return c.PushEvent(clickId, EventName_CompleteRegistration, 0)
}

func (c *Client) Purchase(clickId string, amount float64) (err error) {
	return c.PushEvent(clickId, EventName_Purchase, amount)
}

func (c *Client) FirstRecharge(clickId string, amount float64) (err error) {
	return c.PushEvent(clickId, EventName_FirstRecharge, amount)
}
