package model

import (
	"time"

	"github.com/shopspring/decimal"
)

type SpinConfig struct {
	Round []struct {
		InviteCount int   `json:"invitecount"`
		Recharge    int   `json:"recharge"`
		RandRate    []int `json:"randrate"`
		ExpireTime  int   `json:"expiretime"`
	} `json:"round"`
	SpinData []struct {
		Id   int    `json:"id"`
		Name string `json:"name"`
		Type int    `json:"type"`
		Data []int  `json:"data"`
	} `json:"spindata"`
}

type FirstDepositGiftBaseConfig struct {
	IsBindEmail       bool            `json:"IsBindEmail"`       // 是否需要绑定邮箱
	IsDuringReg       bool            `json:"IsDuringReg" `      // 是否活动期间内注册的账号
	IsValidWallet     bool            `json:"IsValidWallet"`     // 是否有效的钱包地址
	IsDeviceLimit     bool            `json:"IsDeviceLimit"`     // 同设备号是否可参与
	MaxIPAttempts     int             `json:"MaxIPAttempts"`     // 同一IP最大领取次数，0表示不限制
	MaxIDAttempts     int             `json:"MaxIDAttempts"`     // 同ID最大领取次数，0表示不限制
	BlockedIPList     string          `json:"BlockedIPList"`     // 被限制参与的IP，多个IP用逗号分隔
	RegisterDay       int32           `json:"RegisterDay"`       // 注册天数限制
	ReceiveDay        int32           `json:"ReceiveDay"`        // 领取天数限制
	RechargeCount     int32           `json:"RechargeCount"`     // 充值次数要求
	ActivityMethod    int32           `json:"ActivityMethod"`    // 活动方式：1=前置（先领奖励后打流水），2=后置（先打流水再领奖励)
	MinBetAmount      decimal.Decimal `json:"MinBetAmount"`      // 最小投注金额限制
	MaxBetAmount      decimal.Decimal `json:"MaxBetAmount"`      // 最大投注金额限制
	BetType           int32           `json:"BetType"`           // 打码条件：1=真金，2=彩金,3=彩金+真金
	IsCalcActiveWager bool            `json:"IsCalcActiveWager"` // 玩家参与此活动所产生的流水是否纳入会员返水及代理返佣统计，由前端传递
	RewardWalletType  int32           `json:"RewardWalletType"`  // 奖励账户类型，0=真金账户，1=彩金账户
	TotalRewardLimit  decimal.Decimal `json:"TotalRewardLimit"`  // 总派发金额上限
	DailyRewardLimit  decimal.Decimal `json:"DailyRewardLimit"`  // 每日派发金额上限
}

type FirstDepositGiftConfig struct {
	ID                   int32           `json:"Id"`                   // 档位ID，从1开始递增
	FirstChargeUstdLimit decimal.Decimal `json:"FirstChargeUstdLimit"` // 首次单笔最低充值(U)
	LiushuiMultiple      decimal.Decimal `json:"LiushuiMultiple"`      // 真金流水倍数
	BonusMultiple        decimal.Decimal `json:"BonusMultiple"`        // 彩金流水倍数
	GiveProportion       decimal.Decimal `json:"GiveProportion"`       // 赠送比例
	GiveLimit            decimal.Decimal `json:"GiveLimit"`            // 赠送上限(U)
}

type FirstDepositGiftLiuSui struct {
	LiuSuiUsdt      decimal.Decimal
	LiuSuiLottery   decimal.Decimal
	LiuSuiQiPai     decimal.Decimal
	LiuSuiDianZhi   decimal.Decimal
	LiuSuiXiaoYouXi decimal.Decimal
	LiuSuiLive      decimal.Decimal
	LiuSuiSport     decimal.Decimal
	LiuSuiTexas     decimal.Decimal
}

type CumulativeWeeklyRechargeConfig struct {
	ID             int32           `json:"Id"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
	GiveAmount     decimal.Decimal `json:"GiveAmount"`
}

type CumulativeWeeklyRechargeParam struct {
	ID             int32           `json:"Id"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
	GiveAmount     decimal.Decimal `json:"GiveAmount"`
	State          int8            `json:"State"`
}

type CumulativeWeeklyRechargeRes struct {
	TotalRecharge float64                         `json:"TotalRecharge"`
	List          []CumulativeWeeklyRechargeParam `json:"List"`
	Seconds       float64                         `json:"Seconds"`
}

type RealAmountLiuSui struct {
	RealAmount decimal.Decimal
}

type SignRewardBaseConfig struct {
	MixBetLimit decimal.Decimal `json:"MixBetLimit"`
	TrxPrice    decimal.Decimal `json:"TrxPrice"`
	RemakeDay   int8            `json:"RemakeDay"`
}

type SignRewardConfig struct {
	ID               int32           `json:"Id"`
	SignDay          int32           `json:"SignDay"`
	Award            decimal.Decimal `json:"Award"`
	AdditionalReward decimal.Decimal `json:"AdditionalReward"`
}

type SignRewardRes struct {
	FirstSignTime string          `json:"FirstSignTime"`
	LastSignTime  string          `json:"LastSignTime"`
	Days          int64           `json:"Days"`
	RewardAmount  decimal.Decimal `json:"RewardAmount"`
	IsTodaySign   uint8           `json:"IsTodaySign"` // 今天是否签到
}

type SignRewardSum struct {
	RecordID     int64           `json:"RecordID" gorm:"column:RecordId"`
	RewardAmount decimal.Decimal `json:"RewardAmount"`
}

type SignRewardLiuSui struct {
	LiuSuiUsdt      decimal.Decimal
	LiuSuiLottery   decimal.Decimal
	LiuSuiQiPai     decimal.Decimal
	LiuSuiDianZhi   decimal.Decimal
	LiuSuiXiaoYouXi decimal.Decimal
	LiuSuiLive      decimal.Decimal
	LiuSuiSport     decimal.Decimal
	LiuSuiTexas     decimal.Decimal
	LiuSuiTrx       decimal.Decimal
}

type CustomUsdtLiuSui struct {
	LiuSuiUsdt decimal.Decimal
}
type CustomTrxLiuSui struct {
	LiuSuiTrx decimal.Decimal
}

type SaveActiveDataInfo struct {
	AuditType         int
	SellerID          int32
	ChannelID         int32
	ActiveId          int
	Level             int
	RealAmount        float64
	TotalLiushui      float64
	WithdrawLiuSuiAdd float64
	ActiveName        string
	ActiveMemo        string
	BalanceCReason    int
	ConfigStr         []byte
	BastConfigStr     []byte
	FirstRecharge     float64
	TotalRecharge     float64
	FirstSignTime     *time.Time
	LastSignTime      *time.Time
	InviteRewardType  int32 // 邀请好友奖励分类 0其他 1单人奖励 2额外奖励
	ChildUserID       int32
	InviteRewardUsers int32 // 邀请好友额外奖励用户数量
	OrderId           int32
	GameType          string // 游戏分类
	MinLiuShui        decimal.Decimal
	UserIP            string // 用户IP
	DeviceID          string // 设备ID
}

// 推荐好友多重奖励
type RecommendFriendRewardBaseConfig struct {
	RegisterDay          int32           `json:"RegisterDay"`          // 注册天数
	FirstChargeUstdLimit decimal.Decimal `json:"FirstChargeUstdLimit"` // 首次存款
	Level                int32           `json:"Level"`                // vip等级
	Award                decimal.Decimal `json:"Award"`                // 单次奖励
}

type RecommendFriendRewardConfig struct {
	ID               int32           `json:"Id"`
	TotalMin         int32           `json:"TotalMin"`         // 积累邀请人数最小
	TotalMax         int32           `json:"TotalMax"`         // 积累邀请人数最大
	AdditionalReward decimal.Decimal `json:"AdditionalReward"` // 额外奖励
}

type RecommendFriendRewardRes struct {
	Num          int32           `json:"Num"`
	RewardAmount decimal.Decimal `json:"RewardAmount"`
	AgentCode    string          `json:"AgentCode"`
}

type RecommendFriendRewardSum struct {
	Amount decimal.Decimal `json:"Amount"`
}

type BreakThroughConfig struct {
	ID          int32           `json:"Id"`
	LimitValue  decimal.Decimal `json:"LimitValue"`
	RewardValue decimal.Decimal `json:"RewardValue"`
}
type BreakThroughBaseConfig struct {
	TrxPrice       float32         `json:"TrxPrice"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
}

type BreakThroughLiuShui struct {
	LiuSui decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`
}

type WeekendBreakThroughParam struct {
	ID          int32           `json:"Id"`
	LimitValue  decimal.Decimal `json:"LimitValue"`
	RewardValue decimal.Decimal `json:"RewardValue"`
	State       int8            `json:"State"`
}

type WeekendBreakThroughRes struct {
	List   []WeekendBreakThroughParam `json:"List"`
	LiuSui decimal.Decimal            `json:"LiuSui" gorm:"column:LiuSui"`
}

type TodayBreakThroughRes struct {
	LiuSui decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`
}

type NewFirstDepositConfig struct {
	ID              int32           `json:"Id"`
	GiveProportion  decimal.Decimal `json:"GiveProportion"`
	GiveLimit       decimal.Decimal `json:"GiveLimit"`
	LiushuiMultiple decimal.Decimal `json:"LiushuiMultiple"`
}
type XRecharge struct {
	ID         int32
	RealAmount float64
}

type XRecharges []XRecharge

func (s XRecharges) Len() int {
	return len(s)
}
func (s XRecharges) Less(i, j int) bool {
	return s[i].ID < s[j].ID
}
func (s XRecharges) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

type NewFirstData struct {
	RealAmount        float64
	WithdrawLiuSuiAdd float64
	Level             int32
	TotalRecharge     float64
	MinLiuShui        decimal.Decimal
}
