// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXHomeCarouselV2(db *gorm.DB, opts ...gen.DOOption) xHomeCarouselV2 {
	_xHomeCarouselV2 := xHomeCarouselV2{}

	_xHomeCarouselV2.xHomeCarouselV2Do.UseDB(db, opts...)
	_xHomeCarouselV2.xHomeCarouselV2Do.UseModel(&model.XHomeCarouselV2{})

	tableName := _xHomeCarouselV2.xHomeCarouselV2Do.TableName()
	_xHomeCarouselV2.ALL = field.NewAsterisk(tableName)
	_xHomeCarouselV2.SellerID = field.NewInt32(tableName, "SellerId")
	_xHomeCarouselV2.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xHomeCarouselV2.Lang = field.NewInt32(tableName, "Lang")
	_xHomeCarouselV2.ID = field.NewInt32(tableName, "Id")
	_xHomeCarouselV2.Name = field.NewString(tableName, "Name")
	_xHomeCarouselV2.Sort = field.NewInt32(tableName, "Sort")
	_xHomeCarouselV2.ExpiredTime = field.NewInt64(tableName, "ExpiredTime")
	_xHomeCarouselV2.State = field.NewInt32(tableName, "State")
	_xHomeCarouselV2.URL = field.NewString(tableName, "Url")
	_xHomeCarouselV2.PcURL = field.NewString(tableName, "PcUrl")
	_xHomeCarouselV2.ELink = field.NewString(tableName, "ELink")
	_xHomeCarouselV2.PcELink = field.NewString(tableName, "PcELink")

	_xHomeCarouselV2.fillFieldMap()

	return _xHomeCarouselV2
}

type xHomeCarouselV2 struct {
	xHomeCarouselV2Do xHomeCarouselV2Do

	ALL         field.Asterisk
	SellerID    field.Int32
	ChannelID   field.Int32
	Lang        field.Int32
	ID          field.Int32
	Name        field.String // 轮播图名称
	Sort        field.Int32  // 排序
	ExpiredTime field.Int64  // 过期时间
	State       field.Int32  // 状态 1启用 2禁用
	URL         field.String // h5图片路径
	PcURL       field.String // pc图片路径
	ELink       field.String
	PcELink     field.String

	fieldMap map[string]field.Expr
}

func (x xHomeCarouselV2) Table(newTableName string) *xHomeCarouselV2 {
	x.xHomeCarouselV2Do.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xHomeCarouselV2) As(alias string) *xHomeCarouselV2 {
	x.xHomeCarouselV2Do.DO = *(x.xHomeCarouselV2Do.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xHomeCarouselV2) updateTableName(table string) *xHomeCarouselV2 {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Lang = field.NewInt32(table, "Lang")
	x.ID = field.NewInt32(table, "Id")
	x.Name = field.NewString(table, "Name")
	x.Sort = field.NewInt32(table, "Sort")
	x.ExpiredTime = field.NewInt64(table, "ExpiredTime")
	x.State = field.NewInt32(table, "State")
	x.URL = field.NewString(table, "Url")
	x.PcURL = field.NewString(table, "PcUrl")
	x.ELink = field.NewString(table, "ELink")
	x.PcELink = field.NewString(table, "PcELink")

	x.fillFieldMap()

	return x
}

func (x *xHomeCarouselV2) WithContext(ctx context.Context) *xHomeCarouselV2Do {
	return x.xHomeCarouselV2Do.WithContext(ctx)
}

func (x xHomeCarouselV2) TableName() string { return x.xHomeCarouselV2Do.TableName() }

func (x xHomeCarouselV2) Alias() string { return x.xHomeCarouselV2Do.Alias() }

func (x xHomeCarouselV2) Columns(cols ...field.Expr) gen.Columns {
	return x.xHomeCarouselV2Do.Columns(cols...)
}

func (x *xHomeCarouselV2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xHomeCarouselV2) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Name"] = x.Name
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["ExpiredTime"] = x.ExpiredTime
	x.fieldMap["State"] = x.State
	x.fieldMap["Url"] = x.URL
	x.fieldMap["PcUrl"] = x.PcURL
	x.fieldMap["ELink"] = x.ELink
	x.fieldMap["PcELink"] = x.PcELink
}

func (x xHomeCarouselV2) clone(db *gorm.DB) xHomeCarouselV2 {
	x.xHomeCarouselV2Do.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xHomeCarouselV2) replaceDB(db *gorm.DB) xHomeCarouselV2 {
	x.xHomeCarouselV2Do.ReplaceDB(db)
	return x
}

type xHomeCarouselV2Do struct{ gen.DO }

func (x xHomeCarouselV2Do) Debug() *xHomeCarouselV2Do {
	return x.withDO(x.DO.Debug())
}

func (x xHomeCarouselV2Do) WithContext(ctx context.Context) *xHomeCarouselV2Do {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xHomeCarouselV2Do) ReadDB() *xHomeCarouselV2Do {
	return x.Clauses(dbresolver.Read)
}

func (x xHomeCarouselV2Do) WriteDB() *xHomeCarouselV2Do {
	return x.Clauses(dbresolver.Write)
}

func (x xHomeCarouselV2Do) Session(config *gorm.Session) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Session(config))
}

func (x xHomeCarouselV2Do) Clauses(conds ...clause.Expression) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xHomeCarouselV2Do) Returning(value interface{}, columns ...string) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xHomeCarouselV2Do) Not(conds ...gen.Condition) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Not(conds...))
}

func (x xHomeCarouselV2Do) Or(conds ...gen.Condition) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Or(conds...))
}

func (x xHomeCarouselV2Do) Select(conds ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Select(conds...))
}

func (x xHomeCarouselV2Do) Where(conds ...gen.Condition) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Where(conds...))
}

func (x xHomeCarouselV2Do) Order(conds ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Order(conds...))
}

func (x xHomeCarouselV2Do) Distinct(cols ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xHomeCarouselV2Do) Omit(cols ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xHomeCarouselV2Do) Join(table schema.Tabler, on ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xHomeCarouselV2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xHomeCarouselV2Do) RightJoin(table schema.Tabler, on ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xHomeCarouselV2Do) Group(cols ...field.Expr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Group(cols...))
}

func (x xHomeCarouselV2Do) Having(conds ...gen.Condition) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Having(conds...))
}

func (x xHomeCarouselV2Do) Limit(limit int) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Limit(limit))
}

func (x xHomeCarouselV2Do) Offset(offset int) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Offset(offset))
}

func (x xHomeCarouselV2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xHomeCarouselV2Do) Unscoped() *xHomeCarouselV2Do {
	return x.withDO(x.DO.Unscoped())
}

func (x xHomeCarouselV2Do) Create(values ...*model.XHomeCarouselV2) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xHomeCarouselV2Do) CreateInBatches(values []*model.XHomeCarouselV2, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xHomeCarouselV2Do) Save(values ...*model.XHomeCarouselV2) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xHomeCarouselV2Do) First() (*model.XHomeCarouselV2, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHomeCarouselV2), nil
	}
}

func (x xHomeCarouselV2Do) Take() (*model.XHomeCarouselV2, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHomeCarouselV2), nil
	}
}

func (x xHomeCarouselV2Do) Last() (*model.XHomeCarouselV2, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHomeCarouselV2), nil
	}
}

func (x xHomeCarouselV2Do) Find() ([]*model.XHomeCarouselV2, error) {
	result, err := x.DO.Find()
	return result.([]*model.XHomeCarouselV2), err
}

func (x xHomeCarouselV2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XHomeCarouselV2, err error) {
	buf := make([]*model.XHomeCarouselV2, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xHomeCarouselV2Do) FindInBatches(result *[]*model.XHomeCarouselV2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xHomeCarouselV2Do) Attrs(attrs ...field.AssignExpr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xHomeCarouselV2Do) Assign(attrs ...field.AssignExpr) *xHomeCarouselV2Do {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xHomeCarouselV2Do) Joins(fields ...field.RelationField) *xHomeCarouselV2Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xHomeCarouselV2Do) Preload(fields ...field.RelationField) *xHomeCarouselV2Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xHomeCarouselV2Do) FirstOrInit() (*model.XHomeCarouselV2, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHomeCarouselV2), nil
	}
}

func (x xHomeCarouselV2Do) FirstOrCreate() (*model.XHomeCarouselV2, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XHomeCarouselV2), nil
	}
}

func (x xHomeCarouselV2Do) FindByPage(offset int, limit int) (result []*model.XHomeCarouselV2, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xHomeCarouselV2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xHomeCarouselV2Do) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xHomeCarouselV2Do) Delete(models ...*model.XHomeCarouselV2) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xHomeCarouselV2Do) withDO(do gen.Dao) *xHomeCarouselV2Do {
	x.DO = *do.(*gen.DO)
	return x
}
