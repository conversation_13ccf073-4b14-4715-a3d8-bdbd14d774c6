package single

import (
	"encoding/json"
	"errors"
	"fmt"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"io"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
)

// CancelBet  取消下注
func (l *SabaService) CancelBet(ctx *abugo.AbuHttpContent) {

	// 交易详情
	type Txns struct {
		UserId         string  `json:"userId"`                // (string) 用户 id.
		RefId          string  `json:"refId"`                 // (string) 唯一 id.
		TxId           string  `json:"txId"`                  // (long) 沙巴系统交易 id
		UpdateTime     string  `json:"updateTime"`            // 更新时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
		WinlostDate    string  `json:"winlostDate"`           // 决胜时间(仅显示日期) (yyyy-MM-dd 00:00:00.000) GMT-4
		Status         string  `json:"status"`                // (string) 交易结果 half won/half lose/ won/ lose/ draw/ void/ refund/ reject
		Payout         float64 `json:"payout"`                // (decimal) 订单赢回的金额
		CreditAmount   float64 `json:"creditAmount"`          // (decimal) 需增加在玩家的金额。
		DebitAmount    float64 `json:"debitAmount"`           // (decimal) 需从玩家扣除的金额。
		ExtraStatus    string  `json:"extraStatus,omitempty"` // (string) 在以下的情况会有值。
		SettlementTime string  `json:"settlementTime"`        // 此張注單結算的時間。
		Odds           float64 `json:"odds,omitempty"`        // (decimal) 仅当 bettype=468,469 时才会显示此参数。
	}
	type Message struct {
		Action      string `json:"action"`      // (string) Settle
		OperationId string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		Txns        []Txns `json:"txns"`        // Json 格式: 请参阅下方说明
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba cancelBet 数据解压失败:", err)
	}
	logs.Debug("saba cancelBet api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba cancelBet 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	userAmount := 0.00
	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	var success []string //成功注单
	var retry []string   //需要重试的注单 数据库操作失败的需要三方重试
	logs.Info("[saba] cancelBet 开始取消注单，注单总数：", len(trxResults))
	for _, trxResult := range trxResults { //批量订单处理

		//creditAmount有值，就加錢 debitAmount有值就減錢
		//取消注单时需要给用户加钱
		amount := trxResult.CreditAmount
		//取消投注/下注时保存的是refId ,因为三方还没生成txtId，撤销注单用refId
		thirdId := trxResult.RefId
		thirdTime := utils.GetCurrentTime()
		// 下注内容 开奖结果 json
		_GameRst := fmt.Sprintf("输赢：%s,赔率:%f", trxResult.Status, trxResult.Odds)
		logs.Info(_GameRst) //打印开奖结果

		//保存订单详情
		_resultCtx, _ := json.Marshal(trxResult)
		resultCtx := l.convertLongToStr(string(_resultCtx))
		//开启事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

			//查询订单是否存在 订单不存在或者订单已完成 直接返回成功信息给三方 避免三方重复调用接口
			order := thirdGameModel.ThirdSportOrder{}
			e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound {
					logs.Error("saba cancelBet 订单不存在 thirdId=", thirdId, " e=", e.Error())
				} else {
					logs.Error("saba cancelBet 查询订单错误 thirdId=", thirdId, " e=", e.Error())
				}
				return e
			}
			if order.DataState != -1 {
				logs.Error("saba cancelBet 订单已处理 thirdId=", thirdId, " order=", order)
				//订单已经处理过 跳过
				return e
			}

			userId := order.UserId
			//修改订单状态为取消状态 下注金额设置为0
			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
				"GameRst":   resultCtx,
				"BetAmount": 0,
				"DataState": -2,
			})
			e = resultTmp.Error
			if e != nil {
				logs.Error("saba cancelBet 更新订单状态失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				retry = append(retry, thirdId)
				return e
			}

			if amount > 0 { //只会发生加款操作
				userBalance := thirdGameModel.UserBalance{}
				e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
				if e != nil {
					logs.Error("saba cancelBet  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					retry = append(retry, thirdId)
					return e
				}

				resultTmp = tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount+?", amount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("saba cancelBet 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					retry = append(retry, thirdId)
					return e
				}

				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       amount,
					AfterAmount:  userBalance.Amount + amount,
					Reason:       utils.BalanceCReasonSABACancel,
					Memo:         l.brandName + " cancelBet,thirdId:" + thirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("saba cancelBet 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					retry = append(retry, thirdId)
					return e
				}
				userAmount = userBalance.Amount + amount //记录用户最后一次余额
				success = append(success, thirdId)

				logs.Info("[saba] cancelBet 取消订单成功 thirdId=", thirdId)

				// 发送余额变动通知
				go func(notifyUserId int) {
					if l.RefreshUserAmountFunc != nil {
						tmpErr := l.RefreshUserAmountFunc(notifyUserId)
						if tmpErr != nil {
							logs.Error("saba cancelBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
						}
					} else {
						logs.Error("saba cancelBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					}
				}(userId)
			}
			return nil
		})
	}

	/*
	 批量取消时，失败的订单需要通知三方重试
	*/
	if len(retry) > 0 { //返回失败 三方会发起重试
		ctx.RespJson(map[string]interface{}{
			"status": "901",
			"msg":    "",
		})
		logs.Info("[saba] Settle 存在失败的注单，需要重试")
	} else { //返回成功
		ctx.RespJson(map[string]interface{}{
			"status":  "0",
			"balance": userAmount,
		})
	}
}

// Settle 结算处理
/**
 * 重新結算有二種情境
 * 1. settle -> resettle
 * 2. settle -> unsettle -> settle
 * 会出现多次结算情况
 * 需要考虑幂等性
 *waiting(等待中):
	我们交易员因为可能因为赔率的转换等因素，还未接受这张注单。
	running(进行中):此注单还没有结算。（注单还没有结算的状态有可能是这场比赛还没有结算之类的情形.）
	void(作废):在注单为 running 的状态下，玩家下注注金返回。原因可能为我们交易员对此场赛事有些疑虑。可与我们联系询问发生什么状况。
	refund(退款):在注单为 running 的状态下，玩家下注注金返回。原因有可能是赛事取消或发生什么意外。
	reject(已取消):在注单为 waiting 的状态下，玩家下注注金返回。可能状况很多。
	lose(输):此注单已结算且玩家输了此注单。
	won(赢):此注单已结算且玩家赢了此注单。
	draw(和局):此注单已结算且此注单为和局。
	half won(半赢):此注单已结算且玩家赢了下注额一半。
	half lose(半输):注单已结算且玩家输了下注额一半。
*/
func (l *SabaService) Settle(ctx *abugo.AbuHttpContent) {
	type Message struct {
		Action      string `json:"action"`      // (string) Settle
		OperationId string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		Txns        []Txns `json:"txns"`        // Json 格式: 请参阅下方说明
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	//判断是否需要解压
	contentEncoding := ctx.Gin().Request.Header.Get("Content-Encoding")
	if contentEncoding == "gzip" {
		reqDataByte, err = l.gunzip(reqDataByte) //解压请求
		if err != nil {
			logs.Error("saba Settle 数据解压失败:", err)
		}
	}

	bodyBytes := string(reqDataByte)
	logs.Debug("saba Settle api receive:%s, ", bodyBytes)
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba Settle 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	var failures = []map[string]interface{}{} //失败的注单
	var success []string                      //成功注单
	var retry []string                        //需要重试的注单 数据库操作失败的需要三方重试
	thirdTime := utils.GetCurrentTime()

	//这里判断是否是重复请求，如果是重复请求则拒绝 参考FB体育
	thirdRefId := reqData.Message.OperationId

	logs.Info("[saba] Settle 开始结算注单，注单总数：", len(trxResults))
	for v, trxResult := range trxResults {
		//creditAmount有值，就加錢 debitAmount有值就減錢 结算只会加钱
		winAmount := trxResult.CreditAmount
		thirdId := strconv.Itoa(trxResult.TxId)

		//保存订单详情
		bodyBytes_, _ := json.Marshal(trxResult)
		bodyBytes := l.convertLongToStr(string(bodyBytes_))
		status := trxResult.Status
		// 下注内容 开奖结果 json
		_GameRst := fmt.Sprintf("输赢：%s,赔率:%f", status, trxResult.Odds)
		gameRstZh := l.generateGameResult(trxResult)
		// 解析带时区的结算时间
		settleTime := utils.ParseTime(trxResult.SettlementTime)
		if settleTime == nil { //结算时间为空就取订单更新时间 状态为reject（结算后撤单）没有结算时间
			settleTime = utils.ParseTime(trxResult.UpdateTime)
		}
		logs.Info("开奖结果", _GameRst) //打印开奖结果
		//开启事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			order := thirdGameModel.ThirdSportOrder{}
			e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
			if e != nil {
				logs.Error("saba Settle 订单已不存在 thirdId=", thirdId, " e=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "结算失败，订单不存在",
					})
				} else {
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "结算失败，查询订单错误",
					})
				}
				return e
			}

			logs.Info(",第", v, "笔订单", "thirdId=", thirdId, "订单状态=", order.DataState, "结算状态=", order.ResettleState)
			if order.DataState == -2 { //订单已经撤单
				logs.Error("saba Settle 订单已经被撤销 thirdId=", thirdId, " order=", order)
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "结算失败，订单已经被撤销",
				})
				return errors.New("订单状态不正确")
			}

			if order.DataState != -1 {
				logs.Error("saba Settle 不是下注状态 thirdId=", thirdId, " order=", order)
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "结算失败，订单不是下注状态",
				})
				return errors.New("订单状态不正确")
			}

			userId := order.UserId
			//存在这种情况:2. settle sate=1 -> unsettle sate=-1 -> settle sate=1 存在结算-撤销结算-在次结算
			// 判断订单是否结算过 是否是二次结算 0:否 1：是重新结算
			resettleState := order.ResettleState
			if resettleState == 0 { //订单未结算过
				validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"WinAmount":  winAmount,
					"ValidBet":   validBet,
					"DataState":  1,
					"ThirdTime":  thirdTime,
					"SettleTime": settleTime,
					"GameRst":    gameRstZh,
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("saba Settle 更新订单状态失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "更新订单状态失败",
					})
					//记录需要三方重新结算的失败订单
					retry = append(retry, thirdId)
					return e
				}
				logs.Info("第一次结算，增加正式表数据", thirdId, "结算状态=", order.ResettleState)
				order.WinAmount = winAmount
				order.ValidBet = validBet
				order.DataState = 1
				order.ThirdTime = thirdTime
				order.GameRst = gameRstZh
				order.BetCtxType = 3
				order.SettleTime = settleTime
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("saba settle 结算发生错误 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "创建正式订单失败",
					})
					retry = append(retry, thirdId)
					return e
				}

			} else { //订单已经结算过撤销结算在重新结算
				logs.Info("saba  saba settle -> unsettle -> settle 二次结算 重新结算处理 userId=", userId, " thirdId=", thirdId)
				validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值
				resultTmp := tx.Table(tablePre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"WinAmount":  winAmount,
					"ValidBet":   validBet,
					"DataState":  1,
					"CreateTime": thirdTime, //更新为最新时间否则前端昨天下注今天开奖查询不到今天的注单
					"SettleTime": settleTime,
					"GameRst":    gameRstZh,
					//"RawData":    daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
					"ThirdTime": thirdTime,
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}

				if e != nil {
					logs.Error("saba settle -> unsettle -> settle 二次结算 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "二次结算修改订单失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"WinAmount":  winAmount,
					"CreateTime": thirdTime, //更新为最新时间否则前端昨天下注今天开奖查询不到今天的注单
					"SettleTime": settleTime,
					"GameRst":    gameRstZh,
					//"RawData":    daogorm.Expr("CONCAT(RawData, ?)", bodyBytes),
					"ThirdTime": thirdTime,
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("saba Settle 二次结算修改正式订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "二次结算修改正式订单失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				//重新结算增加记录日志
				state := 1 //状态 0-重新结算1-撤销结算
				resettleTime := thirdTime
				changeType := 0 //0 增加资金 1减少资金
				if winAmount <= 0 {
					changeType = 1
				}
				memo := "撤销结算在重新结算"
				e = base.AddSportResettleLog(tx, order, thirdTime, bodyBytes, winAmount, changeType, state, thirdRefId, resettleTime, "", memo)
				if e != nil {
					logs.Error("saba Resettle 增加重新结算记录失败 userId=", order.UserId, " thirdId=", order.ThirdId, " e=", e.Error())
					l.respFail(ctx, "901", "增加重新结算记录失败！")
					return e
				}
				logs.Info("saba  saba settle -> unsettle -> settle 二次结算 结束重新结算处理 userId=", userId, " thirdId=", thirdId)
			}

			//处理用户余额和账变
			if winAmount > 0 { //当派奖金额为0时是否要记录账变
				userBalance := thirdGameModel.UserBalance{}
				e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
				if e != nil {
					logs.Error("saba Settle  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "查询用户余额失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount+?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 { //当余额为0 RowsAffected<=0
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("saba Settle 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "更新用户余额失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       winAmount,
					AfterAmount:  userBalance.Amount + winAmount,
					Reason:       utils.BalanceCReasonSABASettle,
					Memo:         l.brandName + " settle," + status + ",thirdId:" + thirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("saba Settle 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "创建账变失败",
					})
					retry = append(retry, thirdId)
					return e
				}
			}

			// 发送余额变动通知
			go func(notifyUserId int) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(notifyUserId)
					if tmpErr != nil {
						logs.Error("saba Settle 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("saba Settle 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(userId)
			// 推送派奖事件到 CustomerIO
			if l.thirdGamePush != nil {
				//l.thirdGamePush.PushRewardEvent(userId, order.GameName, l.brandName, order.BetAmount, winAmount, l.currency)
				l.thirdGamePush.PushRewardEvent(6, l.brandName, thirdId)
			}
			logs.Info("[saba] Settle 结算成功 thirdId=", thirdId)
			success = append(success, thirdId)
			return nil
		})
	}

	logs.Info("[saba] Settle 结束结算注单，注单总数：", len(trxResults), "结算成功的注单数:", len(success), success, "结算失败的注单:", failures, "需要重试的注单:", retry)

	/*
		settle會使用數組的方式，一次發送多張結算注單資訊
		如果部分注單處理結算時失敗，回傳響應失敗，才會觸發重送機制
		三方不會區分你有哪些注單處理成功/失敗，會批量一次傳，此部分要过滤成功的订单
	*/
	if len(retry) > 0 { //返回失败 用于三方发送重试
		l.respFail(ctx, "901", "")
		logs.Info("[saba] Settle 存在结算失败的注单，需要重试")
	} else { //返回成功
		l.respSucces(ctx)
	}
}

// ConfirmBet   确认投注
/**
  只会出现单笔
  ConfirmBet只會有加錢，不會有扣錢的操作
*/
func (l *SabaService) ConfirmBet(ctx *abugo.AbuHttpContent) {

	// 交易详情
	type Txns struct {
		RefId         string  `json:"refId"`
		TxId          int     `json:"txId"`
		LicenseeTxId  string  `json:"licenseeTxId"`
		Odds          float64 `json:"odds"`
		OddsType      int     `json:"oddsType"`
		ActualAmount  float64 `json:"actualAmount"`
		IsOddsChanged bool    `json:"isOddsChanged"`
		CreditAmount  float64 `json:"creditAmount"`
		DebitAmount   float64 `json:"debitAmount"`
		WinLostDate   string  `json:"winlostDate"`
	}
	type Message struct {
		Action      string `json:"action"`      // (string) Settle
		OperationId string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		Txns        []Txns `json:"txns"`        // Json 格式: 请参阅下方说明
	}
	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba configBet 数据解压失败:", err)
	}
	logs.Debug("saba configBet api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba configBet 参数错误", err.Error())
		return
	}

	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	var failures = []map[string]interface{}{} //失败的注单
	var success []string                      //成功注单
	var retry []string                        //需要重试的注单 数据库操作失败的需要三方重试
	logs.Info("[saba] configBet 开始确认注单，注单总数：", len(trxResults))
	for _, trxResult := range trxResults { //批量订单处理

		//creditAmount有值，就加錢 debitAmount有值就減錢
		//确认订单注单时需要给用户加钱
		amount := trxResult.CreditAmount
		thirdId := trxResult.RefId
		thirdTime := utils.GetCurrentTime()
		//保存订单详情
		resultCtx_, _ := json.Marshal(trxResult)
		resultCtx := l.convertLongToStr(string(resultCtx_))
		logs.Debug("saba ConfirmBet 订单详情 thirdId=", thirdId, "注单详情=", resultCtx)
		//开启事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			//查询订单是否存在
			order := thirdGameModel.ThirdSportOrder{}
			e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound {
					logs.Error("saba configBet 订单不存在 thirdId=", thirdId, " e=", e.Error())
				} else {
					logs.Error("saba configBet 查询订单错误 thirdId=", thirdId, " e=", e.Error())
				}
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "失败，查询订单错误",
				})
				return e
			}
			userId := order.UserId
			//绑定最新的订单号
			if order.DataState == 1 {
				logs.Error("saba configBet 订单已处理 thirdId=", thirdId, " order=", order)
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "失败，订单已处理",
				})
				//订单已经处理过 跳过
				return nil
			} else {
				//绑定新的订单号
				//取消投注/下注时保存的是refId ,因为三方还没生成txtId，三方对账是txtId，所以这里需要修改为三方对账是txtId
				logs.Info("[saba] configBet 更新订单号 userId=", userId, " 原来thirdId=", trxResult.RefId, " 新的thirdId=", trxResult.TxId)
				thirdId = strconv.Itoa(trxResult.TxId)
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"ThirdId": thirdId,
				})
				e = resultTmp.Error
				if e != nil {
					logs.Error("saba configBet 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，更新订单失败",
					})
					retry = append(retry, thirdId)
					return e
				}
				//新订单号绑定到账变日志
				resultTmp = tx.Table("x_amount_change_log").Where("Memo=?", l.brandName+" bet,thirdId:"+trxResult.RefId).Updates(map[string]interface{}{
					"Memo": l.brandName + " bet,thirdId:" + thirdId,
				})
			}

			//处理结算 confirmable只會有加錢，不會有扣錢的操作>0才需要操作余额
			if amount > 0 {
				userBalance := thirdGameModel.UserBalance{}
				e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
				if e != nil {
					logs.Error("saba configBet  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，查询用户余额失败",
					})
					retry = append(retry, thirdId)
					return e
				}
				if amount != 0 { //是否需要更新用户余额
					resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount+?", amount),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 { //当余额为0 RowsAffected<=0
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("saba configBet 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
						failures = append(failures, map[string]interface{}{
							"refId": thirdId,
							"msg":   "失败，更新用户余额失败",
						})
						retry = append(retry, thirdId)
						return e
					}
				}
				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       amount,
					AfterAmount:  userBalance.Amount + amount,
					Reason:       utils.BalanceCReasonSABAConfirm,
					Memo:         l.brandName + " settle,old_thirdId:" + trxResult.RefId + " thirdId:" + thirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("saba configBet 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，创建账变失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				// 发送余额变动通知
				go func(notifyUserId int) {
					if l.RefreshUserAmountFunc != nil {
						tmpErr := l.RefreshUserAmountFunc(notifyUserId)
						if tmpErr != nil {
							logs.Error("saba configBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
						}
					} else {
						logs.Error("saba configBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					}
				}(userId)
			}
			// 交易成功时记录订单号
			success = append(success, trxResult.RefId)
			logs.Info("[saba] configBet 确认订单成功 thirdId=", thirdId)
			return nil
		})
	}

	l.respSucces(ctx)
	logs.Info("[saba] configBet 结束确认订单，注单总数：", len(trxResults), "成功的注单数:", len(success), success, "失败的注单:", failures, "需要重试的注单:", retry)
}

// Resettle 重新结算处理
/**
 * 需要考虑幂等性
 */
func (l *SabaService) Resettle(ctx *abugo.AbuHttpContent) {

	type Message struct {
		Action      string `json:"action"`      // (string) Resettle
		OperationId string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		Txns        []Txns `json:"txns"`        // Json 格式: 请参阅下方说明
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	//判断是否需要解压
	contentEncoding := ctx.Gin().Request.Header.Get("Content-Encoding")
	if contentEncoding == "gzip" {
		reqDataByte, err = l.gunzip(reqDataByte) //解压请求
		if err != nil {
			l.respFail(ctx, "203", "数据解压失败！")
			logs.Error("saba Bet 数据解压失败:", err)
			return
		}
	}

	logs.Debug("saba Resettle api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba Resettle 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	thirdTime := utils.GetCurrentTime()
	trxResult := trxResults[0] //重新结算只会有一笔订单
	thirdId := strconv.Itoa(trxResult.TxId)
	isOnlyWinlostDateChanged := trxResult.ExtraInfo.IsOnlyWinlostDateChanged
	if isOnlyWinlostDateChanged { //只是日期改变则不需要修改注单
		logs.Info("只是结算日期改变，不需要重新结算", thirdId) //打印开奖结果
		l.respSucces(ctx)
		return
	}

	if trxResult.CreditAmount == 0 && trxResult.DebitAmount == 0 {
		l.respFail(ctx, "501", "重新结算金额不能为0！")
		logs.Error("重新结算金额不能为0")
		return
	}

	//creditAmount有值，就加錢 debitAmount有值就減錢
	winAmount := trxResult.Payout   //用户赢的钱
	amount := 0.0                   //正数加钱 负数扣钱
	if trxResult.CreditAmount > 0 { //加钱
		amount = trxResult.CreditAmount
	}
	if trxResult.DebitAmount > 0 { //扣钱
		amount = -trxResult.DebitAmount
	}

	//保存订单详情
	bodyBytes_, _ := json.Marshal(trxResult)
	bodyBytes := l.convertLongToStr(string(bodyBytes_))

	//这里需要判断是否是重复请求，参考FB体育，如果是重复请求则拒绝
	thirdRefId := reqData.Message.OperationId

	logs.Info("[saba] Resettle 开始重新结算注单， thirdId=", thirdId)
	settleTime := utils.ParseTime(trxResult.SettlementTime)
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		order := thirdGameModel.ThirdSportOrder{}
		e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("saba Resettle 订单不存在 thirdId=", thirdId, " e=", e.Error())
			//订单不存在返回成功，否则三方系统会一直调用
			l.respSucces(ctx)
			return e
		}
		if order.DataState != 1 {
			//返回成功，否则三方系统会一直调用
			logs.Error("saba Resettle 订单不是已结算状态，不能重新结算 thirdId=", thirdId, " state=", order.DataState, " order=", order)
			l.respSucces(ctx)
			return errors.New("订单已完成")
		}
		userId := order.UserId
		validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值
		//更新订单表
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":      winAmount,
			"ValidBet":       validBet,
			"DataState":      1,
			"ThirdTime":      thirdTime,
			"GameRst":        bodyBytes,
			"ResettleState":  1,
			"ResettleNumber": daogorm.Expr("ResettleNumber+?", 1),
			"SettleTime":     settleTime,
			"ResettleTime":   thirdTime,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba Resettle 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新订单失败！")
			return e
		}

		//更新统计表
		//问题：重新结算后用户提款了怎么办？是否修改账变？统计报表是实时结算即DataState = 1就会返佣
		resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
			"WinAmount": winAmount,
			"ValidBet":  validBet,
			"ThirdTime": thirdTime,
			"GameRst":   bodyBytes,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba Resettle 更新统计表失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新统计表失败！")
			return e
		}

		userBalance := thirdGameModel.UserBalance{}
		e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("saba Resettle  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "查询用户余额失败！")
			return e
		}

		resultTmp = tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount+?", amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba Resettle 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新用户余额失败！")
			return e
		}

		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       utils.BalanceCReasonSABAResettle,
			Memo:         l.brandName + " resettle,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("saba Resettle 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "创建账变失败！")
			return e
		}
		//重新结算增加记录日志
		state := 0 //状态 0-重新结算1-撤销结算
		thirdTime := utils.GetCurrentTime()
		resettleTime := thirdTime
		changeType := 0 // 0增加 1减少
		if amount <= 0 {
			changeType = 1
		}
		memo := "重新结算"
		e = base.AddSportResettleLog(tx, order, thirdTime, bodyBytes, amount, changeType, state, thirdRefId, resettleTime, "", memo)
		if e != nil {
			logs.Error("saba Resettle 增加重新结算记录失败 userId=", order.UserId, " thirdId=", order.ThirdId, " e=", e.Error())
			l.respFail(ctx, "901", "增加重新结算记录失败！")
			return e
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("saba Resettle 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("saba Resettle 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
		l.respSucces(ctx)
		logs.Info("[saba] Resettle 重新结算成功 thirdId=", thirdId)
		return nil
	})
}

// Unsettle 撤销结算处理
/**
 * 需要考虑幂等性
 */
func (l *SabaService) Unsettle(ctx *abugo.AbuHttpContent) {

	type Message struct {
		Action      string `json:"action"`      // (string) Unsettle
		OperationId string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		Txns        []Txns `json:"txns"`        // Json 格式: 请参阅下方说明
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba Unsettle 数据解压失败:", err)
	}

	logs.Debug("saba Unsettle api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba Unsettle 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	thirdTime := utils.GetCurrentTime()
	trxResult := trxResults[0] //撤销结算只会有一笔订单
	thirdId := strconv.Itoa(trxResult.TxId)
	winAmount := 0 //用户赢的钱
	amount := 0.0
	if trxResult.CreditAmount > 0 { //加钱
		amount = trxResult.CreditAmount
	}
	if trxResult.DebitAmount > 0 { //扣钱
		amount = -trxResult.DebitAmount
	}
	logs.Info("[saba] Unsettle 开始撤销结算注单，thirdId=", thirdId, ",amount=", amount)

	gameRstZh := l.generateGameResult(trxResult)
	//保存订单详情
	bodyBytes_, _ := json.Marshal(trxResult)
	bodyBytes := l.convertLongToStr(string(bodyBytes_))

	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		order := thirdGameModel.ThirdSportOrder{}
		e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("saba Unsettle 订单不存在 thirdId=", thirdId, " e=", e.Error())
			l.respSucces(ctx) //订单不存在或者是不能操作返回成功 避免三方一直调用接口
			return e
		}
		if order.DataState != 1 {
			logs.Error("saba Unsettle 订单不是已结算状态，不能撤销结算 thirdId=", thirdId, " state=", order.DataState, " order=", order)
			l.respSucces(ctx)
			return errors.New("订单已完成")
		}

		userId := order.UserId
		validBet := 0 // 撤销结算不计有效流水
		//更新订单表
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":      winAmount,
			"ValidBet":       validBet,
			"DataState":      -1, //撤单
			"ThirdTime":      thirdTime,
			"GameRst":        gameRstZh,
			"ResettleState":  -1, //状态-1是撤销结算
			"ResettleNumber": daogorm.Expr("ResettleNumber+?", 1),
			"ResettleTime":   thirdTime,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba Unsettle 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新订单失败！")
			return e
		}

		//更新统计表
		//问题：重新结算后用户提款了怎么办？是否修改账变？统计报表是实时结算即DataState = 1就会返佣
		resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
			"WinAmount": winAmount,
			"ValidBet":  validBet,
			"DataState": -1,
			"ThirdTime": thirdTime,
			"GameRst":   gameRstZh,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba Unsettle 更新统计表失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新统计表失败！")
			return e
		}

		//撤销结算涉及扣款
		userBalance := thirdGameModel.UserBalance{}
		e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("saba Unsettle  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "查询用户余额失败！")
			return e
		}
		if amount != 0 { //是否需要更新用户余额
			resultTmp = tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 { //当余额为0 RowsAffected<=0
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("saba Unsettle 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				l.respFail(ctx, "901", "更新用户余额失败！")
				return e
			}
		}

		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       utils.BalanceCReasonSABAUNSettle,
			Memo:         l.brandName + " unsettle,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("saba Unsettle 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "创建账变失败！")
			return e
		}
		//重新结算增加记录日志
		state := 1      //状态 0-重新结算1-撤销结算
		changeType := 0 // 0减少资金 1增加资金
		if amount <= 0 {
			changeType = 1
		}
		memo := "撤销结算"
		e = base.AddSportResettleLog(tx, order, thirdTime, bodyBytes, amount, changeType, state, thirdId, thirdTime, "", memo)
		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("saba Unsettle 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("saba Unsettle 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
		logs.Info("[saba] Unsettle 撤销结算成功 thirdId=", thirdId)

		l.respSucces(ctx)
		return nil
	})
}

// AdjustBalance 沙巴体育钱包活动操作
/**
 * 需要考虑幂等性
 */
func (l *SabaService) AdjustBalance(ctx *abugo.AbuHttpContent) {
	// BalanceInfo 结构体表示余额信息
	type BalanceInfo struct {
		CreditAmount float64 `json:"creditAmount"` // 需增加在玩家的金额
		DebitAmount  float64 `json:"debitAmount"`  // 需从玩家扣除的金额
	}

	type Message struct {
		Action      string      `json:"action"`          // AdjustBalance
		Time        string      `json:"time"`            // 调用AdjustBalance的时间
		UserId      string      `json:"userId"`          // 用户id
		Currency    int         `json:"currency"`        // 沙巴体育货币币别
		TxId        int         `json:"txId"`            // 沙巴体育系统交易id
		RefNo       int         `json:"refNo,omitempty"` // 适用于bettype=17007的情况才会有此参数，使用风险券的注单号码
		RefId       string      `json:"refId"`           // 唯一id
		OperationId string      `json:"operationId"`     // 唯一识别号
		BetType     int         `json:"betType"`         // 不同的bet类型
		BetTypeName string      `json:"betTypeName"`     // 只提供英文语系
		WinlostDate string      `json:"winlostDate"`     // 决胜时间
		BalanceInfo BalanceInfo `json:"balanceInfo"`     // 余额信息
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba AdjustBalance 数据解压失败:", err)
	}

	logs.Debug("saba AdjustBalance api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba AdjustBalance 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	thirdTime := utils.GetCurrentTime()
	logs.Info("[saba] AdjustBalance 开始推广活动钱包操作")
	thirdId := strconv.Itoa(reqData.Message.TxId)

	creditAmount := reqData.Message.BalanceInfo.CreditAmount
	debitAmount := reqData.Message.BalanceInfo.DebitAmount
	if creditAmount == 0 && debitAmount == 0 {
		l.respFail(ctx, "501", "推广活动金额不能为0！")
		logs.Error("推广活动金额不能为0")
		return
	}

	amount := 0.0
	if creditAmount > 0 {
		amount = creditAmount
	}
	if debitAmount > 0 { //减少金额
		amount = -debitAmount
	}

	userId, err := strconv.Atoi(reqData.Message.UserId)
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		userBalance := thirdGameModel.UserBalance{}
		e := server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("saba AdjustBalance  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "查询用户余额失败！")
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount+?", amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("saba AdjustBalance 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "更新用户余额失败！")
			return e
		}

		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       utils.BalanceCReasonSABAadjustBalance,
			Memo:         l.brandName + " adjustBalance,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("saba AdjustBalance 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "创建账变失败！")
			return e
		}

		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("saba AdjustBalance 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("saba AdjustBalance 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
		logs.Info("[saba] AdjustBalance 钱包操作成功 thirdId=", thirdId)
		return nil
	})
}

// HealthCheck 心跳检测/*
func (l *SabaService) HealthCheck(ctx *abugo.AbuHttpContent) {

	type Message struct {
		Action string `json:"action"`      // (string) Settle
		Time   string `json:"operationId"` // (string) 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
	}
	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba healthCheck 数据解压失败:", err)
	}
	logs.Debug("saba healthCheck api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba healthCheck 参数错误", err.Error())
		return
	}

	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}
	l.respSucces(ctx)
	logs.Info("[saba] healthCheck 心跳检测成功")
}

//type Txns struct {
//	UserId       string  `json:"userId"`       // (string) 用户 id
//	TxId         int     `json:"txId"`         // (long) 沙巴系统交易 id
//	RefId        string  `json:"refId"`        // (string) 唯一 id.
//	UpdateTime   string  `json:"updateTime"`   // 更新时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
//	Status       string  `json:"status"`       // (string) 交易结果 half won/half lose/ won/ lose/ draw/ void/ refund/ reject
//	CreditAmount float64 `json:"creditAmount"` // (decimal) 需增加在玩家的金额。
//	DebitAmount  float64 `json:"debitAmount"`  // (decimal) 需从玩家扣除的金额。
//	ExtraStatus  string  `json:"extraStatus"`  // (string) 在以下的情况会有值。
//}

// ExtraInfo 结构体用于表示额外信息
type ExtraInfo struct {
	IsOnlyWinlostDateChanged bool `json:"isOnlyWinlostDateChanged"` // (boolean) 是否只有 WinlostDate 改变，其它参数例如: status、 creditAmount、debitAmount、payout 维持不变。 例如：true, false
}

type Txns struct {
	UserId         string    `json:"userId"`         // (string) 用户 id
	TxId           int       `json:"txId"`           // (long) 沙巴系统交易 id
	RefId          string    `json:"refId"`          // (string) 唯一 id.
	UpdateTime     string    `json:"updateTime"`     // 更新时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	WinlostDate    string    `json:"winlostDate"`    // 决胜时间(仅显示日期) (yyyy-MM-dd 00:00:00.000) GMT-4
	Status         string    `json:"status"`         // (string) 交易结果 half won/half lose/ won/ lose/ draw/ void/ refund/ reject
	Payout         float64   `json:"payout"`         // (decimal) 注单赢回的金额
	CreditAmount   float64   `json:"creditAmount"`   // (decimal) 需增加在玩家的金额。
	DebitAmount    float64   `json:"debitAmount"`    // (decimal) 需从玩家扣除的金额。
	ExtraStatus    string    `json:"extraStatus"`    // (string) 在以下的情况会有值。
	SettlementTime string    `json:"settlementTime"` // 此張注單結算的時間。
	Odds           float64   `json:"odds,omitempty"` // (decimal) 仅当 bettype=468,469 时才会显示此参数。
	ExtraInfo      ExtraInfo `json:"extraInfo"`      // 请参阅表 ExtraInfo
}

// generateGameResult 生成游戏结果描述
func (l *SabaService) generateGameResult(txn Txns) string {
	sb := strings.Builder{}
	sb.WriteString("{")

	// 添加基本信息
	sb.WriteString(fmt.Sprintf("\"用户ID\":\"%s\",", txn.UserId))
	sb.WriteString(fmt.Sprintf("\"唯一ID\":\"%s\",", txn.RefId))
	sb.WriteString(fmt.Sprintf("\"沙巴系统交易ID\":\"%v\",", txn.TxId))
	sb.WriteString(fmt.Sprintf("\"更新时间\":\"%s\",", txn.UpdateTime))
	sb.WriteString(fmt.Sprintf("\"结算时间\":\"%s\",", txn.SettlementTime))
	// 添加投注金额和赔付信息
	sb.WriteString(fmt.Sprintf("\"投注金额\":%.2f,", txn.DebitAmount))
	sb.WriteString(fmt.Sprintf("\"赔付金额\":%.2f,", txn.Payout))
	if txn.Odds > 0 {
		sb.WriteString(fmt.Sprintf("\"赔率\":%.2f,", txn.Odds))
	}

	// 添加输赢结果
	profitLoss := txn.CreditAmount - txn.DebitAmount
	if profitLoss > 0 {
		sb.WriteString(fmt.Sprintf("\"盈亏\":\"盈利 %.2f\",", profitLoss))
	} else if profitLoss < 0 {
		sb.WriteString(fmt.Sprintf("\"盈亏\":\"亏损 %.2f\",", -profitLoss))
	} else {
		sb.WriteString("\"盈亏\":\"平局\",")
	}

	// 添加注单状态
	status := ""
	switch txn.Status {
	case "waiting":
		status = "等待中"
	case "running":
		status = "进行中"
	case "void":
		status = "作废"
	case "refund":
		status = "退款"
	case "reject":
		status = "已取消"
	case "lose":
		status = "输"
	case "won":
		status = "赢"
	case "draw":
		status = "和局"
	case "half won":
		status = "半赢"
	case "half lose":
		status = "半输"
	default:
		status = txn.Status
	}
	sb.WriteString(fmt.Sprintf("\"状态\":\"%s\"", status))

	// 如果有额外状态，添加额外状态信息
	if txn.ExtraStatus != "" {
		sb.WriteString(fmt.Sprintf(",\"额外状态\":\"%s\"", txn.ExtraStatus))
	}

	sb.WriteString("}")
	return sb.String()
}
