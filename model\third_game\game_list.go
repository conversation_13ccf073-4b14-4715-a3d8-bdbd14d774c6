package third_game

// x_game_list
type GameList struct {
	Id            int    `json:"Id" gorm:"column:Id"`
	Brand         string `json:"Brand" gorm:"column:Brand"`
	GameId        string `json:"GameId" gorm:"column:GameId"`
	Name          string `json:"Name" gorm:"column:Name"`
	EName         string `json:"EName" gorm:"column:EName"`
	State         int    `json:"State" gorm:"column:State"`
	OpenState     int    `json:"OpenState" gorm:"column:OpenState"`
	GameType      int    `json:"GameType" gorm:"column:GameType"`
	HubType       int    `json:"HubType" gorm:"column:HubType"`
	ThirdGameType string `json:"ThirdGameType" gorm:"column:ThirdGameType"`
}
