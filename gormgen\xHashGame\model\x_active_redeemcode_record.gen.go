// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveRedeemcodeRecord = "x_active_redeemcode_record"

// XActiveRedeemcodeRecord 活动兑换码记录表
type XActiveRedeemcodeRecord struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:记录ID" json:"Id"`             // 记录ID
	SellerID     int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                       // 运营商
	ChannelID    int32     `gorm:"column:ChannelId;not null;comment:渠道" json:"ChannelId"`                      // 渠道
	UserID       int32     `gorm:"column:UserId;not null;comment:玩家ID" json:"UserId"`                          // 玩家ID
	RedeemCode   string    `gorm:"column:RedeemCode;not null;comment:兑换码" json:"RedeemCode"`                   // 兑换码
	UseCount     string    `gorm:"column:UseCount;comment:此码可兑次数" json:"UseCount"`                             // 此码可兑次数
	RewardAmount float64   `gorm:"column:RewardAmount;default:0.00;comment:获得奖励金额" json:"RewardAmount"`        // 获得奖励金额
	ActiveID     int32     `gorm:"column:ActiveId;comment:对应活动ID" json:"ActiveId"`                             // 对应活动ID
	ActiveName   string    `gorm:"column:ActiveName;comment:对应活动名称" json:"ActiveName"`                         // 对应活动名称
	RedeemTime   time.Time `gorm:"column:RedeemTime;comment:兑换时间" json:"RedeemTime"`                           // 兑换时间
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime   time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XActiveRedeemcodeRecord's table name
func (*XActiveRedeemcodeRecord) TableName() string {
	return TableNameXActiveRedeemcodeRecord
}
