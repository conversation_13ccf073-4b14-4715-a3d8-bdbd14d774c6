package base

import (
	"errors"
	"fmt"
	"runtime/debug"
	"strings"
	"time"
	"xserver/controller/customer"
	"xserver/gormgen/xHashGame/model"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	daogorm "gorm.io/gorm"

	"github.com/beego/beego/logs"
)

// BroadcastInterface 定义广播接口
type BroadcastInterface interface {
	WinBroadcast(orderID int, gameType string) error
}

// 全局广播处理器变量，可以被外部设置
var GlobalBroadcaster BroadcastInterface

// ThirdGamePush 三方游戏推送事件控制器
// 提供与CustomerIO集成的通用功能，用于三方游戏投注和派奖的事件追踪
type ThirdGamePush struct {
	thirdGamePush *customer.BaseController
	broadcaster   BroadcastInterface
}

// NewThirdGamePush 创建三方游戏推送事件控制器实例
func NewThirdGamePush() *ThirdGamePush {
	//logs.Info("开始创建 ThirdGamePush 实例...")

	thirdGamePush := customer.NewBaseController()
	if thirdGamePush == nil {
		logs.Error("ThirdGamePush 初始化失败: BaseController 创建失败")
		return nil
	}

	//logs.Info("ThirdGamePush 实例创建成功")
	return &ThirdGamePush{
		thirdGamePush: thirdGamePush,
		broadcaster:   GlobalBroadcaster, // 使用全局广播器
	}
}

// SetBroadcaster 设置广播器
func (c *ThirdGamePush) SetBroadcaster(broadcaster BroadcastInterface) {
	c.broadcaster = broadcaster
}

// PushBetEvent 推送三方游戏投注事件
// @param userId 用户ID
// @param gameName 游戏名称
// @param playType 游戏玩法
// @param betAmount 投注金额
// @param currency 币种
func (c *ThirdGamePush) PushBetEvent(userId int, gameName, playType string, betAmount float64, currency string) {
	if c == nil || c.thirdGamePush == nil {
		logs.Error("无法推送三方游戏投注事件: ThirdGamePush 或 BaseController 未初始化")
		return
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.Error("推送三方游戏投注事件失败:", err)
			}
		}()

		c.thirdGamePush.PushBetEvent(userId, gameName, playType, betAmount, currency)
	}()
}

// PushRewardEvent 推送三方游戏派奖事件
// @param gameType 游戏类型 1-电子 2-棋牌 3-小游戏 4-彩票 5-真人 6-体育 7-德州
// @param brand 游戏品牌
// @param thirdId 第三方订单ID
func (c *ThirdGamePush) PushRewardEvent(gameType int, brand string, thirdId string) {
	// 空指针检查
	if c == nil || c.thirdGamePush == nil {
		logs.Error("无法推送三方游戏派奖事件: ThirdGamePush 或 BaseController 未初始化")
		return
	}

	// 参数验证
	if thirdId == "" || brand == "" {
		logs.Error("推送三方游戏派奖事件参数无效: thirdId=", thirdId, " brand=", brand)
		return
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.Error("推送三方游戏派奖事件异常:", err, " thirdId=", thirdId, " brand=", brand)
				debug.PrintStack() // 打印堆栈信息，便于调试
			}
		}()

		// 延迟5秒推送，避免三方游戏界面还在玩但已经收到开奖结果
		time.Sleep(time.Second * 5)

		// 获取游戏类型信息
		table, gameTypeStr, err := getGameTypeInfo(gameType, brand)
		if err != nil {
			logs.Error(err.Error(), " thirdId=", thirdId, " gameType=", gameType)
			return
		}

		// 查询订单信息
		order, err := getOrderInfo(table, thirdId, brand)
		if err != nil {
			logs.Error(err.Error(), " thirdId=", thirdId, " brand=", brand)
			return
		}

		// 执行广播
		c.doBroadcast(order, gameTypeStr, thirdId, brand)

		// 推送派奖事件
		c.thirdGamePush.PushRewardEvent(order.UserId, order.GameName, brand, order.BetAmount, order.WinAmount, order.Currency)

		logs.Info("派奖事件推送完成 thirdId=", thirdId, " brand=", brand, " userId=", order.UserId, " win=", order.WinAmount)
	}()
}

// getGameTypeInfo 获取游戏类型信息
func getGameTypeInfo(gameType int, brand string) (table string, gameTypeStr string, err error) {
	//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
	switch gameType {
	case 1: //电子游戏
		table = "x_third_dianzhi"
		gameTypeStr = "Dianzi"
	case 2: //棋牌游戏
		table = "x_third_qipai"
		gameTypeStr = "Qipai"
	case 3: //quwei小游戏
		table = "x_third_quwei"
		gameTypeStr = "Qukuailian" // 默认使用区块链类型
	case 4: //lottery彩票
		table = "x_third_lottery"
		gameTypeStr = "Lottery"
	case 5: //live真人
		table = "x_third_live"
		gameTypeStr = "Zhenren"
	case 6: //体育
		table = "x_third_sport"
		gameTypeStr = "Sport"
	case 7: //德州
		table = "x_third_texas"
		gameTypeStr = "Texas"
	default:
		return "", "", fmt.Errorf("推送三方游戏派奖事件 游戏类型不正确 gameType=%d", gameType)
	}

	// 特殊品牌处理
	if strings.ToLower(strings.TrimSpace(brand)) == "og" {
		gameTypeStr = "Og"
	}

	return table, gameTypeStr, nil
}

// getOrderInfo 获取订单信息
func getOrderInfo(table, thirdId, brand string) (*thirdGameModel.ThirdOrder, error) {
	// 查询注单是否存在
	order := thirdGameModel.ThirdOrder{}
	e := server.Db().GormDao().Table(table).Where("ThirdId=? and Brand=?", thirdId, brand).First(&order).Error
	if e != nil {
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("推送三方游戏派奖事件 订单不存在 thirdId=%s brand=%s", thirdId, brand)
		}
		return nil, fmt.Errorf("推送三方游戏派奖事件 查询订单失败 error=%s", e.Error())
	}

	return &order, nil
}

// doBroadcast 执行广播
func (c *ThirdGamePush) doBroadcast(order *thirdGameModel.ThirdOrder, gameTypeStr, thirdId, brand string) {
	if c.broadcaster == nil {
		logs.Info("广播器未设置，跳过派奖播报 thirdId=", thirdId, " brand=", brand, " gameType=", gameTypeStr)
		return
	}

	err := c.broadcaster.WinBroadcast(int(order.Id), gameTypeStr)
	if err != nil {
		logs.Error("派奖播报失败 thirdId=", thirdId, " brand=", brand, " error=", err.Error())
	} else {
		logs.Info("派奖播报成功 thirdId=", thirdId, " brand=", brand, " gameType=", gameTypeStr, " win=", order.WinAmount)
	}
}

// GetUser 获取用户信息（通用方法）
func GetUser(userId int) (*model.XUser, error) {
	user, err := server.DaoxHashGame().XUser.WithContext(nil).
		Where(server.DaoxHashGame().XUser.UserID.Eq(int32(userId))).
		First()
	if err != nil {
		logs.Error("获取用户信息失败:", err)
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	return user, nil
}
