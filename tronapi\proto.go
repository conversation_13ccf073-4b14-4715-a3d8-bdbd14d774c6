package tronapi

import "github.com/shopspring/decimal"

type AccountV2Rsp struct {
	TransactionsOut       int   `json:"transactionsOut,omitempty"`
	TransactionsIn        int   `json:"transactionsIn,omitempty"`
	Transactions          int   `json:"transactions,omitempty"`
	TotalTransactionCount int   `json:"totalTransactionCount,omitempty"`
	RewardNum             int   `json:"rewardNum,omitempty"`
	Balance               int   `json:"balance,omitempty"`
	LatestOperationTime   int64 `json:"latest_operation_time"`
	DateCreated           int64 `json:"date_created"`
	WithPriceTokens       []struct {
		Amount           decimal.Decimal `json:"amount"`
		TokenId          string          `json:"tokenId,omitempty"`
		TokenType        string          `json:"tokenType,omitempty"`
		TokenName        string          `json:"tokenName,omitempty"`
		TokenAbbr        string          `json:"tokenAbbr,omitempty"`
		TokenPriceInTrx  decimal.Decimal `json:"tokenPriceInTrx"`
		TokenLogo        string          `json:"tokenLogo,omitempty"`
		Balance          string          `json:"balance,omitempty"`
		TokenDecimal     int             `json:"tokenDecimal,omitempty"`
		TokenCanShow     int             `json:"tokenCanShow,omitempty"`
		Vip              bool            `json:"vip,omitempty"`
		NrOfTokenHolders int             `json:"nrOfTokenHolders,omitempty"`
		TransferCount    int             `json:"transferCount,omitempty"`
	} `json:"withPriceTokens,omitempty"`
}

type TokensReq struct {
	Address   string `json:"address" form:"address"`     // Account address
	Start     int    `json:"start" form:"start"`         // Start number. Default: 0
	Limit     int    `json:"limit" form:"limit"`         // Number of items per page. Default: 20
	Hidden    int    `json:"hidden" form:"hidden"`       // Whether to hide tokens with small balance. 0: hide (default) 1: show
	Show      int    `json:"show" form:"show"`           // Token type. 1: TRC20 2: TRC721 3: ALL (default) 4: TRC1155
	SortBy    int    `json:"sortBy" form:"sortBy"`       // Sort field. 1: price 2: amount (default) 3: quantity
	SortType  int    `json:"sortType" form:"sortType"`   // Sort order. 0: descending order (default) 1: ascending order
	Token     string `json:"token" form:"token"`         // Specify token ID or token address
	AssetType int    `json:"assetType" form:"assetType"` // Asset type. 0: ALL 1: token only (default) 2: receipt token only
}

type TokensRsp struct {
	Total int `json:"total"`
	Data  []struct {
		Amount          decimal.Decimal `json:"amount"`
		Quantity        decimal.Decimal `json:"quantity"`
		TokenId         string          `json:"tokenId"`
		TokenPriceInUsd decimal.Decimal `json:"tokenPriceInUsd"`
		TokenName       string          `json:"tokenName"`
		TokenAbbr       string          `json:"tokenAbbr"`
		TokenCanShow    int             `json:"tokenCanShow"`
		TokenLogo       string          `json:"tokenLogo"`
		TokenPriceInTrx decimal.Decimal `json:"tokenPriceInTrx"`
		AmountInUsd     decimal.Decimal `json:"amountInUsd"`
		Balance         string          `json:"balance"`
		TokenDecimal    int             `json:"tokenDecimal"`
		TokenType       string          `json:"tokenType"`
		Vip             bool            `json:"vip"`
	} `json:"data"`
	ContractMap  map[string]bool `json:"contractMap"`
	ContractInfo ContractInfo    `json:"contractInfo,omitempty"`
}

type ContractInfo map[string]struct {
	IsToken     bool   `json:"isToken,omitempty"`
	Tag1        string `json:"tag1,omitempty"`
	Tag1Url     string `json:"tag1Url,omitempty"`
	Name        string `json:"name,omitempty"`
	Risk        bool   `json:"risk,omitempty"`
	Vip         bool   `json:"vip,omitempty"`
	OpenSource  bool   `json:"open_source,omitempty"`
	ProjectLogo string `json:"project_logo,omitempty"`
}

type TokenInfo struct {
	TokenId      string `json:"tokenId,omitempty"`
	TokenAbbr    string `json:"tokenAbbr,omitempty"`
	TokenName    string `json:"tokenName,omitempty"`
	TokenDecimal int32  `json:"tokenDecimal,omitempty"`
	TokenCanShow int    `json:"tokenCanShow,omitempty"`
	TokenType    string `json:"tokenType,omitempty"`
	TokenLogo    string `json:"tokenLogo,omitempty"`
	TokenLevel   int    `json:"tokenLevel,omitempty"`
	IssuerAddr   string `json:"issuerAddr,omitempty"`
	Vip          bool   `json:"vip,omitempty"`
}

type TransactionInfoRsp struct {
	Block         int    `json:"block,omitempty"`
	Hash          string `json:"hash,omitempty"`
	Timestamp     int64  `json:"timestamp,omitempty"`
	OwnerAddress  string `json:"ownerAddress,omitempty"`
	ContractType  int    `json:"contractType,omitempty"`
	ToAddress     string `json:"toAddress,omitempty"`
	Confirmations int    `json:"confirmations,omitempty"`
	Confirmed     bool   `json:"confirmed,omitempty"`
	Revert        bool   `json:"revert,omitempty"`
	ContractRet   string `json:"contractRet,omitempty"`
	ContractData  struct {
		Amount       int64     `json:"amount,omitempty"`
		AssetName    string    `json:"asset_name" json:"asset_name,omitempty"`
		OwnerAddress string    `json:"owner_address" json:"owner_address,omitempty"`
		ToAddress    string    `json:"to_address,omitempty"`
		TokenInfo    TokenInfo `json:"tokenInfo,omitempty"`
	} `json:"contractData,omitempty"`
}

type TransfersRsp struct {
	Total          int          `json:"total"`
	RangeTotal     int          `json:"rangeTotal"`
	ContractInfo   ContractInfo `json:"contractInfo,omitempty"`
	TokenTransfers []struct {
		TransactionId         string    `json:"transaction_id"`
		Status                int       `json:"status"`
		Block                 int       `json:"block"`
		BlockTs               int64     `json:"block_ts"`
		FromAddress           string    `json:"from_address"`
		ToAddress             string    `json:"to_address"`
		ContractAddress       string    `json:"contract_address"`
		Quant                 string    `json:"quant"`
		ApprovalAmount        string    `json:"approval_amount"`
		EventType             string    `json:"event_type"`
		Confirmed             bool      `json:"confirmed"`
		ContractRet           string    `json:"contractRet"`
		FinalResult           string    `json:"finalResult"`
		TokenInfo             TokenInfo `json:"TokenInfo"`
		Revert                bool      `json:"revert"`
		ContractType          string    `json:"contract_type"`
		FromAddressIsContract bool      `json:"fromAddressIsContract"`
		ToAddressIsContract   bool      `json:"toAddressIsContract"`
		RiskTransaction       bool      `json:"riskTransaction"`
	} `json:"token_transfers"`
	NormalAddressInfo map[string]struct {
		Risk bool `json:"risk"`
	} `json:"normalAddressInfo,omitempty"`
}
