package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// JiLi单一钱包类

type JiLiSingleService struct {
	apiUrl                string          // api基础接口
	agentId               string          // 代理ID
	agentKey              string          // 代理KEY
	currency              string          // 币种
	brandName             string          // 厂商标识
	locMd5                *time.Location  // MD5签名时区
	RefreshUserAmountFunc func(int) error // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

func NewJiLiSingleService(params map[string]string, fc func(int) error) *JiLiSingleService {
	return &JiLiSingleService{
		apiUrl:                params["api_url"],
		agentId:               params["agent_id"],
		agentKey:              params["agent_key"],
		currency:              params["currency"],
		brandName:             "jili",
		locMd5:                time.FixedZone("utc-4", -4*3600),
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyJiLi = "cacheKeyJiLi:"

const (
	JiLi_Code_Success = 0 // 0      成功
)

// 支持的语言 zh-CN=简体中文,en-US=英文,bn-IN=孟加拉文,da-DK=丹麦文,de-DE=德文,es-AR=西班牙文,fr-FR=法文,gr-GR=希腊文,hi-IN=印度文,id-ID=印度尼西亚文,it-IT=意大利文,ja-JP=日文,ko-KR=韩文,ms-MY=马来文,my-MM=缅甸文,nl-NL=荷兰文,pt-BR=葡萄牙文,ro-RO=罗马尼亚文,ru-RU=俄罗斯文,sv-SE=瑞典文,ta-IN=坦米尔文,th-TH=泰文,tr-TR=土耳其文,ur-IN=乌尔都文,vi-VN=越南文
var jiLiSupportLang = []string{"zh-CN", "en-US", "bn-IN", "da-DK", "de-DE", "es-AR", "fr-FR", "gr-GR", "hi-IN", "id-ID", "it-IT", "ja-JP", "ko-KR", "ms-MY", "my-MM", "nl-NL", "pt-BR", "ro-RO", "ru-RU", "sv-SE", "ta-IN", "th-TH", "tr-TR", "ur-IN", "vi-VN"}

// 登录 /singleWallet/LoginWithoutRedirect
// 错误码 2=Key验证失败 9=向营运商发送auth验证token失败；AgentId不存在；玩家账号与AgentId不匹配 104=营运商于auth回传错误或禁用的货币
func (l *JiLiSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("jili_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	loginLang := ""
	if reqdata.LangCode != "" {
		for _, v := range jiLiSupportLang {
			if v == reqdata.LangCode {
				loginLang = reqdata.LangCode
				break
			}
		}
		if loginLang == "" {
			loginLang = "zh-CN"
		}
	} else {
		loginLang = "zh-CN"
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("jili_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyDBLive, userId); err != nil {
		logs.Error("jili_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录1")
		return
	}

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("jili_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试2")
		return
	}
	// if gameList.State != 1 || gameList.OpenState != 1 {
	// 	logs.Error("jili_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
	// 	ctx.RespErrString(true, &errcode, "游戏未开放")
	// 	return
	// }

	loginGameId, err := strconv.ParseInt(reqdata.GameId, 10, 64)
	if err != nil {
		logs.Error("jili_single 登录游戏 GameId 转换错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试21")
		return
	}
	if reqdata.GameId != strconv.FormatInt(loginGameId, 10) {
		logs.Error("jili_single 登录游戏 GameId 转换错误 userId=", userId, " GameId=", reqdata.GameId, " loginGameId=", loginGameId)
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试22")
		return
	}

	loginHomeUrl := ""
	if reqdata.HomeUrl != "" {
		loginHomeUrl = regexp.MustCompile(`^((http://)|(https://))?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(/)`).FindString(reqdata.HomeUrl)
	}

	accessToken := l.generateToken()
	err = l.setToken(userId, token.Account, accessToken)
	if err != nil {
		logs.Error("jili_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试3")
		return
	}
	logs.Info("jili_single 登录游戏 开始 userId=", userId, " reqdata=", reqdata)

	reqLoginData := make(map[string]string)
	reqLoginData["Token"] = accessToken
	reqLoginData["GameId"] = reqdata.GameId
	reqLoginData["Lang"] = loginLang
	reqLoginData["AgentId"] = l.agentId

	urlParams := fmt.Sprintf("Token=%s&GameId=%s&Lang=%s&AgentId=%s", accessToken, reqdata.GameId, loginLang, l.agentId)
	sign := l.getSign(urlParams, time.Now().Unix())

	// 开始获取登陆url
	url := fmt.Sprintf("%s/singleWallet/LoginWithoutRedirect?%s&Key=%s", l.apiUrl, urlParams, sign)
	if loginHomeUrl != "" {
		url = url + "&HomeUrl=" + loginHomeUrl // HomeUrl,Platform,DisableFullScreen不参与签名
	}
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("jili_single 登录游戏 请求错误 userId=", userId, " reqUrl=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "网络错误,请稍后再试4")
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("jili_single 登录游戏 读取响应错误 userId=", userId, " reqUrl=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试5")
		return
	}
	logs.Info("jili_single 登录游戏 请求成功 userId=", userId, " reqUrl=", url, " respBytes=", string(respBytes))

	type ResponseGetUrlData struct {
		ErrorCode int    `json:"errorCode"` // 错误码
		Message   string `json:"message"`   // 错误信息
		Data      string `json:"data"`      // 登录地址
	}
	respDataUrl := ResponseGetUrlData{}
	err = json.Unmarshal(respBytes, &respDataUrl)
	if err != nil {
		logs.Error("jili_single 登录游戏 解析响应消息体错误 userId=", userId, " reqUrl=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试6")
		return
	}

	if respDataUrl.ErrorCode != JiLi_Code_Success {
		logs.Error("jili_single 登录游戏 登录失败 userId=", userId, " reqUrl=", url, " 错误码=", respDataUrl.ErrorCode, " Message=", respDataUrl.Message)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}

	ctx.RespOK(respDataUrl.Data)
	return
}

// 验证账号 余额查询接口
/*
三方接口说明：
*/
// 错误码 4=token过期 5=其他错误
func (l *JiLiSingleService) Auth(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ReqId string `json:"reqId"`
		Token string `json:"token"`
	}
	type ResponseData struct {
		ErrorCode int     `json:"errorCode"` // 错误码
		Message   string  `json:"message"`   // 错误信息
		Username  string  `json:"username"`  // 账号唯一识别名称
		Currency  string  `json:"currency"`  // 币种
		Balance   float64 `json:"balance"`   // 余额
		Token     string  `json:"token"`
	}
	respdata := ResponseData{
		ErrorCode: JiLi_Code_Success,
		Message:   "success",
		Currency:  l.currency,
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("jili_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("jili_single 获取玩家余额 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("jili_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	userId, _, err := l.authToken(reqdata.Token)
	if err != nil {
		logs.Error("jili_single 获取玩家余额 token验证错误 token=", reqdata.Token, " err=", err.Error())
		respdata.ErrorCode = 4
		respdata.Message = "token验证错误"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("jili_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.ErrorCode = 5
			respdata.Message = "会员不存在"
		} else {
			respdata.ErrorCode = 5
			respdata.Message = "查询会员余额失败"
		}
		ctx.RespJson(respdata)
		return
	}

	respdata.Username = l.getUserNameFromUserId(int64(userId))
	respdata.Balance = userBalance.Amount
	respdata.Token = reqdata.Token
	ctx.RespJson(respdata)
	logs.Info("jili_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

// bet 投注中奖接口
/*
三方接口说明：
传送一笔玩家注单给营运商。此注单之中包含了结算。
若因为网络或其他因素而未获得营运商的响应, 游戏商可以:
1. 取消该注单 (请见 §4.2.5);
2. 或重送该注单 (请见 §4.2.6)。
默认会发送取消。营运商可要求游戏商重送该注单，不过当达到重送次数上限之后，游戏
商仍会发送取消。营运商与游戏商应事先约定好要采取哪一种行动。
P.S. 部分游戏有离线后才开奖, 此种注单 isFreeRound 为 true, 并带 userId 识别玩家身
分, 详见 §4.2.4。
*/
// 错误码 1=该注单已承认 2=玩家余额不足 3=参数无效 4=Token expired 5=其他错误
func (l *JiLiSingleService) Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ReqId         string  `json:"reqId"`
		Token         string  `json:"token"`
		Currency      string  `json:"currency"`
		Game          int64   `json:"game"`          // 游戏代码
		Round         int64   `json:"round"`         // 注单唯一识别值
		WagersTime    int64   `json:"wagersTime"`    // 注单结账时间戳
		BetAmount     float64 `json:"betAmount"`     // 押注金额
		WinloseAmount float64 `json:"winloseAmount"` // 派彩金额
		UserId        string  `json:"userId"`        // 玩家账号唯一值
		IsFreeRound   bool    `json:"isFreeRound"`   // 为 true 时表示此注单为离线开奖
		TransactionId int64   `json:"transactionId"` // 1. 鱼机游戏大单号 (依营运商需求带入) 或 2. 离线开奖的触发局局号
		Platform      string  `json:"platform"`      // 玩家装置信息 (依营运商需求带入
		StatementType int64   `json:"statementType"` // 注单类型 (依营运商需求带入
		GameCategory  int64   `json:"gameCategory"`  // 游戏类型 (依营运商需求带入
		FreeSpinData  struct {
			ReferenceId string `json:"referenceId"` // 仅用于免费游戏 (free spin)
			Remain      int64  `json:"remain"`      // 免费局数序号
		} `json:"freeSpinData"` // 剩余免费局数
	}

	type ResponseData struct {
		ErrorCode int     `json:"ErrorCode"`
		Message   string  `json:"message"`
		Username  string  `json:"username"`
		Currency  string  `json:"currency"`
		Balance   float64 `json:"balance"`
		TxId      int64   `json:"txId"` // 营运商取消注单后提供的交易识别唯一值 (注单已取消也请附上)
		// Token     string  `json:"token"` // 营运商 api access token 若有更新需要以此字段回传
	}
	respdata := ResponseData{
		ErrorCode: JiLi_Code_Success,
		Message:   "success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("jili_single 投注中奖 读取请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("jili_single 投注中奖 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("jili_single 投注中奖 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.Round)

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.ReqId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.ErrorCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("jili_single 投注中奖 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if reqdata.BetAmount < 0 {
		logs.Error("jili_single 投注中奖 押注金额不能小于0 thirdId=", thirdId, " reqdata.BetAmount=", reqdata.BetAmount)
		respdata.ErrorCode = 3
		respdata.Message = "betAmount押注金额不能小于0"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.WinloseAmount < 0 {
		logs.Error("jili_single 投注中奖 派彩金额不能小于0 thirdId=", thirdId, " reqdata.WinloseAmount=", reqdata.WinloseAmount)
		respdata.ErrorCode = 3
		respdata.Message = "winloseAmount派彩金额不能小于0"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("jili_single 投注中奖 币种不正确 thirdId=", thirdId, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = 3
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}
	userId := int(0)
	if !reqdata.IsFreeRound { // 非离线开奖才验证token
		userId, _, err = l.authToken(reqdata.Token)
		if err != nil {
			logs.Error("jili_single 投注中奖 token验证错误 thirdId=", thirdId, " token=", reqdata.Token, " err=", err.Error())
			respdata.ErrorCode = 4
			respdata.Message = "token验证错误"
			ctx.RespJson(respdata)
			return
		}
	} else {
		userIdInt64, err := l.getUserIdFromUserName(reqdata.UserId)
		if err != nil {
			logs.Error("jili_single 投注中奖 会员账号错误 thirdId=", thirdId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
			respdata.ErrorCode = 3
			respdata.Message = "会员账号错误"
			ctx.RespJson(respdata)
			return
		}
		userId = int(userIdInt64)
	}

	gameId := fmt.Sprintf("%d", reqdata.Game)
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("jili_single 投注中奖 获取游戏名称失败 thirdId=", thirdId, " reqdata.Game=", reqdata.Game, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "游戏未添加"
		ctx.RespJson(respdata)
		return
	}
	gameName := gameList.Name

	ub := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&ub).Error
	if err != nil {
		logs.Error("jili_single 投注中奖 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.ErrorCode = 5
			respdata.Message = "会员不存在"
		} else {
			respdata.ErrorCode = 5
			respdata.Message = "查询用户信息失败"
		}
		ctx.RespJson(respdata)
		return
	}
	betChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(ub.Token).Host)

	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		table = "x_third_dianzhi"
		tablePre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		table = "x_third_qipai"
		tablePre = "x_third_qipai_pre_order"
	} else {
		logs.Error("jili_single 投注中奖 游戏类型配置错误 thirdId=", thirdId, " gameList=", gameList)
		respdata.ErrorCode = 5
		respdata.Message = "游戏类型配置错误"
		ctx.RespJson(respdata)
		return
	}

	// betTime := time.Unix(reqdata.WagersTime, 0).In(tzUTC8).Format("2006-01-02 15:04:05")
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betAmount := reqdata.BetAmount
	winAmount := reqdata.WinloseAmount

	// 所有电子的有效流水取不大于下注金额的输赢绝对值
	validBet := math.Abs(winAmount - betAmount)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}

	// 开始投注中奖事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("jili_single 投注中奖 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = 5
				respdata.Message = "会员不存在"
			} else {
				respdata.ErrorCode = 5
				respdata.Message = "查询用户信息失败"
			}
			return e
		}
		respdata.Balance = userBalance.Amount

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e == nil {
			logs.Error("jili_single 投注中奖 注单已存在 thirdId=", thirdId, " order=", order)
			respdata.ErrorCode = 1
			respdata.Message = "注单已存在"
			return e
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("jili_single 投注中奖 查询注单失败 thirdId=", thirdId, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "查询注单失败"
			return e
		}

		// 下注金额大于用户余额
		if userBalance.Amount < 0 || userBalance.Amount < betAmount {
			logs.Error("jili_single 投注中奖 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " betAmount=", betAmount)
			e = errors.New("玩家余额不足")
			respdata.ErrorCode = 2
			respdata.Message = "玩家余额不足"
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, betAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 投注中奖 扣款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "扣款失败"
			return e
		}
		// 创建下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -betAmount,
			AfterAmount:  userBalance.Amount - betAmount,
			Reason:       utils.BalanceCReasonJiLiBet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("jili_single 投注中奖 创建下注账变记录失败 thirdId=", thirdId, " amountLogBet=", amountLogBet, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建下注账变记录失败"
			return e
		}

		resultTmp = tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", winAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 投注中奖 加款失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "加款失败"
			return e
		}

		// 创建中奖账变记录
		amountLogWin := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount - betAmount,
			Amount:       winAmount,
			AfterAmount:  userBalance.Amount - betAmount + winAmount,
			Reason:       utils.BalanceCReasonJiLiWin,
			Memo:         l.brandName + " win,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogWin).Error
		if e != nil {
			logs.Error("jili_single 投注中奖 创建中奖账变记录失败 thirdId=", thirdId, " amountLogWin=", amountLogWin, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建中奖账变记录失败"
			return e
		}

		respdata.Balance = userBalance.Amount - betAmount + winAmount

		// 创建注单 投注中奖一起发送 所以只创建一次注单就好了
		order = thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: betChannelId,
			UserId:       userBalance.UserId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gameId,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    winAmount,
			ValidBet:     validBet,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			Fee:          0,
			DataState:    1, // 开奖状态
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("jili_single 投注中奖 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建注单失败"
			return e
		}
		respdata.TxId = order.Id
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("jili_single 投注中奖 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建正式注单失败"
			return e
		}

		return nil
	})

	if err != nil {
		logs.Error("jili_single 投注中奖 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][jili_single] 投注中奖 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][jili_single] 投注中奖 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))

		// 推送下注事件通知
		if l.thirdGamePush != nil && betAmount > 0 {
			l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency)
		}

		// 推送奖励事件通知
		if l.thirdGamePush != nil && winAmount > 0 {
			//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, betAmount, winAmount, l.currency)
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(gameList.GameType, l.brandName, thirdId)
		}

		// 获取结果详细信息链接
		go func() {
			time.Sleep(time.Second * 2)
			dUrl, err := l.getGameDetailUrl(reqdata.Round)
			if err != nil {
				logs.Error("jili_single 投注中奖 获取游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
				return
			}
			err = server.Db().GormDao().Table(tablePre).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"BetCtx":     dUrl,
				"GameRst":    dUrl,
				"BetCtxType": 2,
			}).Error
			if err != nil {
				logs.Error("jili_single 投注中奖 更新预设表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
			}
			err = server.Db().GormDao().Table(table).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"BetCtx":     dUrl,
				"GameRst":    dUrl,
				"BetCtxType": 2,
			}).Error
			if err != nil {
				logs.Error("jili_single 投注中奖 更新正式表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
			}
		}()
	}

	respdata.Username = reqdata.UserId
	respdata.Currency = l.currency
	ctx.RespJson(respdata)
	logs.Info("jili_single 投注中奖 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

// cancelBet 回滚投注中奖接口
/*
三方接口说明：
取消一笔注单。玩家的下注额应退回，而派奖则要扣除。
游戏商应持续发送直到获得营运商回应。此项请求可于玩家脱机时发送, token 可能已经失
效, 因此除了玩家最后一次使用的 token 之外还会带上 userId, 营运商可自行选择使用
userId 或 token 来识别玩家身分。另一种方式是使用离线模式。
*/
// 错误码 1=该注单已取消 2=注单无效 3=参数无效 5=其他错误 6=注单已成立而不可取消
func (l *JiLiSingleService) CancelBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ReqId         string  `json:"reqId"`
		Currency      string  `json:"currency"`
		Game          int64   `json:"game"`          // 游戏代码
		Round         int64   `json:"round"`         // 注单唯一识别值
		BetAmount     float64 `json:"betAmount"`     // 押注金额
		WinloseAmount float64 `json:"winloseAmount"` // 派彩金额
		UserId        string  `json:"userId"`        // 玩家账号唯一值
		Token         string  `json:"token"`         // 该注单发生时的 token
	}

	type ResponseData struct {
		ErrorCode int     `json:"ErrorCode"`
		Message   string  `json:"message"`
		Username  string  `json:"username"`
		Currency  string  `json:"currency"`
		Balance   float64 `json:"balance"`
		TxId      int64   `json:"txId"` //营运商取消注单后提供的交易识别唯一值 (注单已取消也请附上)
	}
	respdata := ResponseData{
		ErrorCode: JiLi_Code_Success,
		Message:   "success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("jili_single 回滚投注中奖 读取请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("jili_single 回滚投注中奖 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("jili_single 回滚投注中奖 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.Round)

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.ReqId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.ErrorCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("jili_single 回滚投注中奖 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if reqdata.BetAmount < 0 {
		logs.Error("jili_single 回滚投注中奖 押注金额不能小于0 thirdId=", thirdId, " reqdata.BetAmount=", reqdata.BetAmount)
		respdata.ErrorCode = 3
		respdata.Message = "betAmount押注金额不能小于0"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.WinloseAmount < 0 {
		logs.Error("jili_single 回滚投注中奖 派彩金额不能小于0 thirdId=", thirdId, " reqdata.WinloseAmount=", reqdata.WinloseAmount)
		respdata.ErrorCode = 3
		respdata.Message = "winloseAmount派彩金额不能小于0"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("jili_single 回滚投注中奖 币种不正确 thirdId=", thirdId, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = 3
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	// token可能失效，所以使用userId验证
	// userId, _, err := l.authToken(reqdata.Token)
	// if err != nil {
	// 	logs.Error("jili_single 回滚投注中奖 token验证错误 thirdId=", thirdId, " token=", reqdata.Token, " err=", err.Error())
	// 	respdata.ErrorCode = 4
	// 	respdata.Message = "token验证错误"
	// 	ctx.RespJson(respdata)
	// 	return
	// }
	userIdInt64, err := l.getUserIdFromUserName(reqdata.UserId)
	if err != nil {
		logs.Error("jili_single 回滚投注中奖 会员账号错误 thirdId=", thirdId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}
	userId := int(userIdInt64)

	gameId := fmt.Sprintf("%d", reqdata.Game)
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("jili_single 回滚投注中奖 获取游戏名称失败 thirdId=", thirdId, " reqdata.Game=", reqdata.Game, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "游戏未添加"
		ctx.RespJson(respdata)
		return
	}
	// gameName := gameList.Name

	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		table = "x_third_dianzhi"
		tablePre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		table = "x_third_qipai"
		tablePre = "x_third_qipai_pre_order"
	} else {
		logs.Error("jili_single 回滚投注中奖 游戏类型配置错误 thirdId=", thirdId, " gameList=", gameList)
		respdata.ErrorCode = 5
		respdata.Message = "游戏类型配置错误"
		ctx.RespJson(respdata)
		return
	}

	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betAmount := reqdata.BetAmount
	winAmount := reqdata.WinloseAmount

	// 开始回滚投注中奖事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = 5
				respdata.Message = "会员不存在"
			} else {
				respdata.ErrorCode = 5
				respdata.Message = "查询用户信息失败"
			}
			return e
		}
		respdata.Balance = userBalance.Amount

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			if e != daogorm.ErrRecordNotFound {
				logs.Error("jili_single 回滚投注中奖 查询注单失败 thirdId=", thirdId, " err=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "查询注单失败"
				return e
			} else { // 没有注单
				logs.Error("jili_single 回滚投注中奖 注单不存在 thirdId=", thirdId, " err=", e.Error())
				respdata.ErrorCode = 2
				respdata.Message = "注单不存在"
				return e
			}
		}

		if order.DataState != 1 {
			if order.DataState == -2 {
				logs.Error("jili_single 回滚投注中奖 注单已取消 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = 2
				respdata.Message = "注单已取消"
				return e
			} else {
				logs.Error("jili_single 回滚投注中奖 注单状态异常 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = 5
				respdata.Message = "注单状态异常"
				e = errors.New("注单状态异常")
				return e
			}
		}
		if order.UserId != userId {
			logs.Error("jili_single 回滚投注中奖 注单用户不匹配 thirdId=", thirdId, " order=", order, " userId=", userId)
			respdata.ErrorCode = 5
			respdata.Message = "注单用户不匹配"
			e = errors.New("注单用户不匹配")
			return e
		}
		if order.BetAmount != betAmount || order.WinAmount != winAmount {
			logs.Error("jili_single 回滚投注中奖 注单回滚金额不匹配 thirdId=", thirdId, " order=", order, " betAmount=", betAmount, " winAmount=", winAmount)
			respdata.ErrorCode = 5
			respdata.Message = "注单回滚金额不匹配"
			return e
		}
		// 根基订单实际金额回滚
		// betAmount = order.BetAmount
		// winAmount = order.WinAmount

		// 回滚金额大于用户余额
		if userBalance.Amount < 0 || userBalance.Amount < betAmount-winAmount {
			logs.Error("jili_single 回滚投注中奖 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " betAmount=", betAmount, " winAmount=", winAmount)
			e = errors.New("玩家余额不足")
			respdata.ErrorCode = 5
			respdata.Message = "玩家余额不足"
			return e
		}

		// 设置预设表注单状态为已取消
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"DataState": -2,
			"ThirdTime": thirdTime,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 更新注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "更新注单失败"
			return e
		}
		// 设置正式表注单状态为已取消
		resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
			"DataState": -2,
			"ThirdTime": thirdTime,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 更新正式注单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "更新正式注单失败"
			return e
		}

		// 回滚下注金额
		resultTmp = tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 加款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "加款失败"
			return e
		}
		// 创建回滚下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       betAmount,
			AfterAmount:  userBalance.Amount + betAmount,
			Reason:       utils.BalanceCReasonJiLiCancel,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 创建回滚下注账变记录失败 thirdId=", thirdId, " amountLogBet=", amountLogBet, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建回滚下注账变记录失败"
			return e
		}

		// 回滚中奖金额
		resultTmp = tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", winAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 扣款失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "扣款失败"
			return e
		}

		// 创建回滚中奖账变记录
		amountLogWin := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount + betAmount,
			Amount:       -winAmount,
			AfterAmount:  userBalance.Amount + betAmount - winAmount,
			Reason:       utils.BalanceCReasonJiLiCancel,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogWin).Error
		if e != nil {
			logs.Error("jili_single 回滚投注中奖 创建回滚中奖账变记录失败 thirdId=", thirdId, " amountLogWin=", amountLogWin, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建回滚中奖账变记录失败"
			return e
		}

		respdata.TxId = order.Id
		respdata.Balance = userBalance.Amount + betAmount - winAmount
		return nil
	})

	if err != nil {
		logs.Error("jili_single 回滚投注中奖 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][jili_single] 回滚投注中奖 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][jili_single] 回滚投注中奖 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Username = reqdata.UserId
	respdata.Currency = l.currency
	ctx.RespJson(respdata)
	logs.Info("jili_single 回滚投注中奖 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

// sessionBet 下注结算接口
/*
三方接口说明：
传送一笔牌局型玩家注单给营运商, 用于有开牌结算的游戏。分成下注及结算两类型。
若因为网络或其他因素而未获得营运商回应, 游戏商会采取以下行为:
1. 结算前，取消当笔下注并中止牌局，然后进行结算;
2. 结算后会将派彩发送给营运商，若不成功须持续发送；达到 n 次不成功 (预设
n=10) 时，游戏商应立即通知营运商进行处理。
取消牌局型注单请参考 §4.2.8。
P.S. 由于结算有可能发生在玩家离线之后，因此牌局型注单会有参数 userId, 营运商可自
行选择用 token 或 userId 识别玩家身分。另一种方式是使用离线模式。
*/
// 错误码 1=该注单已承认 2=玩家余额不足 3=参数无效 4=Token expired 5=其他错误
func (l *JiLiSingleService) SessionBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ReqId           string  `json:"reqId"`
		Token           string  `json:"token"`
		Currency        string  `json:"currency"`
		Game            int64   `json:"game"`            // 游戏代码
		Round           int64   `json:"round"`           // 下注/结算行为唯一识别值。等同于交易序号。
		Offline         bool    `json:"offline"`         // 是否离线模式 true=离线模式 false=在线模式
		WagersTime      int64   `json:"wagersTime"`      // 注单结账时间戳
		BetAmount       float64 `json:"betAmount"`       // 押注金额
		WinloseAmount   float64 `json:"winloseAmount"`   // 派彩金额
		SessionId       int64   `json:"sessionId"`       // 牌局唯一值 (所有下注及结算共享同一值, 等同 §3.1.3 WagersId)
		Type            int     `json:"type"`            // 1=下注 2=结算
		UserId          string  `json:"userId"`          // 玩家账号唯一值
		Turnover        float64 `json:"turnover"`        // 有效投注金额, 结算才会带入
		Preserve        float64 `json:"preserve"`        // 预扣金额 (仅用于有预先扣款机制的游戏，
		Platform        string  `json:"platform"`        // 玩家装置信息 (依营运商需求带入
		SessionTotalBet float64 `json:"sessionTotalBet"` // 牌局全部下注总和 (依营运商需求带入; 只在结算时带入)
		StatementType   int64   `json:"statementType"`   // 注单类型 (依营运商需求带入
		GameCategory    int64   `json:"gameCategory"`    // 游戏类型 (依营运商需求带入
		FreeSpinData    struct {
			ReferenceId string `json:"referenceId"` // 仅用于免费游戏 (free spin)
			Remain      int64  `json:"remain"`      // 免费局数序号
		} `json:"freeSpinData"` // 剩余免费局数
	}

	type ResponseData struct {
		ErrorCode int     `json:"ErrorCode"`
		Message   string  `json:"message"`
		Username  string  `json:"username"`
		Currency  string  `json:"currency"`
		Balance   float64 `json:"balance"`
		TxId      int64   `json:"txId"` // 营运商取消注单后提供的交易识别唯一值 (注单已取消也请附上)
		// Token     string  `json:"token"` // 营运商 api access token 若有更新需要以此字段回传
	}
	respdata := ResponseData{
		ErrorCode: JiLi_Code_Success,
		Message:   "success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("jili_single 下注结算 读取请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("jili_single 下注结算 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("jili_single 下注结算 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.SessionId)
	reqTypeStr := "下注结算"
	if reqdata.Type == 1 {
		reqTypeStr = "下注"
	} else if reqdata.Type == 2 {
		reqTypeStr = "结算"
	} else {
		logs.Error("jili_single 下注结算 type不正确 thirdId=", thirdId, " reqdata.Type=", reqdata.Type)
		respdata.ErrorCode = 3
		respdata.Message = "type类型不正确"
		ctx.RespJson(respdata)
		return
	}

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.ReqId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.ErrorCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("jili_single ", reqTypeStr, " 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if reqdata.Preserve < 0 {
		logs.Error("jili_single ", reqTypeStr, " 预扣金额不能小于0 thirdId=", thirdId, " reqdata.Preserve=", reqdata.Preserve)
		respdata.ErrorCode = 3
		respdata.Message = "preserve预扣金额不能小于0"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.BetAmount < 0 {
		logs.Error("jili_single ", reqTypeStr, " 押注金额不能小于0 thirdId=", thirdId, " reqdata.BetAmount=", reqdata.BetAmount)
		respdata.ErrorCode = 3
		respdata.Message = "betAmount押注金额不能小于0"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.WinloseAmount < 0 {
		logs.Error("jili_single ", reqTypeStr, " 派彩金额不能小于0 thirdId=", thirdId, " reqdata.WinloseAmount=", reqdata.WinloseAmount)
		respdata.ErrorCode = 3
		respdata.Message = "winloseAmount派彩金额不能小于0"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("jili_single ", reqTypeStr, " 币种不正确 thirdId=", thirdId, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = 3
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}
	userId := int(0)
	if !reqdata.Offline { // 非离线开奖才验证token
		userId, _, err = l.authToken(reqdata.Token)
		if err != nil {
			logs.Error("jili_single ", reqTypeStr, " token验证错误 thirdId=", thirdId, " token=", reqdata.Token, " err=", err.Error())
			respdata.ErrorCode = 4
			respdata.Message = "token验证错误"
			ctx.RespJson(respdata)
			return
		}
	} else {
		userIdInt64, err := l.getUserIdFromUserName(reqdata.UserId)
		if err != nil {
			logs.Error("jili_single ", reqTypeStr, " 会员账号错误 thirdId=", thirdId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
			respdata.ErrorCode = 3
			respdata.Message = "会员账号错误"
			ctx.RespJson(respdata)
			return
		}
		userId = int(userIdInt64)
	}

	gameId := fmt.Sprintf("%d", reqdata.Game)
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("jili_single ", reqTypeStr, " 获取游戏名称失败 thirdId=", thirdId, " reqdata.Game=", reqdata.Game, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "游戏未添加"
		ctx.RespJson(respdata)
		return
	}
	gameName := gameList.Name

	betChannelId := 0
	if reqdata.Type == 1 {
		ub := thirdGameModel.UserBalance{}
		err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&ub).Error
		if err != nil {
			logs.Error("jili_single ", reqTypeStr, " 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", err.Error())
			if err == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = 5
				respdata.Message = "会员不存在"
			} else {
				respdata.ErrorCode = 5
				respdata.Message = "查询用户信息失败"
			}
			ctx.RespJson(respdata)
			return
		}
		betChannelId, _ = server.GetChannel(ctx, server.GetTokenFromRedis(ub.Token).Host)
	}

	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		table = "x_third_dianzhi"
		tablePre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		table = "x_third_qipai"
		tablePre = "x_third_qipai_pre_order"
	} else {
		logs.Error("jili_single ", reqTypeStr, " 游戏类型配置错误 thirdId=", thirdId, " gameList=", gameList)
		respdata.ErrorCode = 5
		respdata.Message = "游戏类型配置错误"
		ctx.RespJson(respdata)
		return
	}

	// betTime := time.Unix(reqdata.WagersTime, 0).In(tzUTC8).Format("2006-01-02 15:04:05")
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betAmount := reqdata.BetAmount
	winAmount := reqdata.WinloseAmount

	//依照 preserve 的使用与否，游戏可分成两种类型：
	if reqdata.Preserve > 0 { // 有 preserve
		if reqdata.Type == 1 { // 下注
			if betAmount != 0 {
				logs.Error("jili_single ", reqTypeStr, " 下注 preserve参数错误 thirdId=", thirdId, " reqdata.Preserve=", reqdata.Preserve)
				respdata.ErrorCode = 3
				respdata.Message = "preserve参数错误"
				ctx.RespJson(respdata)
				return
			}
			betAmount = reqdata.Preserve
		} else if reqdata.Type == 2 { // 结算
			winAmount = reqdata.Preserve - betAmount + winAmount
		}
	}
	// } else { // 无 preserve 的不做特殊处理
	// 	if reqdata.Type == 1 { // 下注
	// 		betAmount = reqdata.BetAmount
	// 	} else if reqdata.Type == 2 { // 结算
	// 		winAmount = reqdata.WinloseAmount
	// 	}
	// }

	if reqdata.Type == 1 {
		// 开始下注事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.ErrorCode = 5
					respdata.Message = "会员不存在"
				} else {
					respdata.ErrorCode = 5
					respdata.Message = "查询用户信息失败"
				}
				return e
			}
			respdata.Balance = userBalance.Amount

			// 下注金额大于用户余额
			if userBalance.Amount < 0 || userBalance.Amount < betAmount {
				logs.Error("jili_single ", reqTypeStr, " 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " betAmount=", betAmount, " reqdata.BetAmount=", reqdata.BetAmount, " reqdata.Preserve=", reqdata.Preserve)
				e = errors.New("玩家余额不足")
				respdata.ErrorCode = 2
				respdata.Message = "玩家余额不足"
				return e
			}

			// 查询注单是否存在
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e != daogorm.ErrRecordNotFound {
					logs.Error("jili_single ", reqTypeStr, " 查询注单失败 thirdId=", thirdId, " err=", e.Error())
					respdata.ErrorCode = 5
					respdata.Message = "查询注单失败"
					return e
				} else { // 没有注单 创建注单
					order = thirdGameModel.ThirdOrder{
						// Id: 0,
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						BetChannelId: betChannelId,
						UserId:       userBalance.UserId,
						Brand:        l.brandName,
						ThirdId:      thirdId,
						GameId:       gameId,
						GameName:     gameName,
						BetAmount:    betAmount,
						WinAmount:    0,
						ValidBet:     0,
						ThirdTime:    thirdTime,
						Currency:     l.currency,
						RawData:      string(bodyBytes),
						State:        1,
						Fee:          0,
						DataState:    -1, // 未开奖状态
						CreateTime:   thirdTime,
					}
					e = tx.Table(tablePre).Create(&order).Error
					if e != nil {
						logs.Error("jili_single ", reqTypeStr, " 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
						respdata.ErrorCode = 5
						respdata.Message = "创建注单失败"
						return e
					}
					respdata.TxId = order.Id
				}
			} else { // 已有注单 追加下注金额
				if order.UserId != userId {
					logs.Error("jili_single ", reqTypeStr, " 注单用户不匹配 thirdId=", thirdId, " order=", order, " userId=", userId)
					respdata.ErrorCode = 5
					respdata.Message = "注单用户不匹配"
					e = errors.New("注单用户不匹配")
					return e
				}
				if order.DataState != -1 {
					if order.DataState == 1 {
						logs.Error("jili_single ", reqTypeStr, " 注单已结算 thirdId=", thirdId, " order=", order)
						respdata.ErrorCode = 1
						respdata.Message = "注单已结算"
						return e
					} else if order.DataState == -2 {
						logs.Error("jili_single ", reqTypeStr, " 注单已取消 thirdId=", thirdId, " order=", order)
						respdata.ErrorCode = 1
						respdata.Message = "注单已取消"
						return e
					} else {
						logs.Error("jili_single ", reqTypeStr, " 注单状态异常 thirdId=", thirdId, " order=", order)
						respdata.ErrorCode = 5
						respdata.Message = "注单状态异常"
						e = errors.New("注单状态异常")
						return e
					}
				}
				if order.State == -1 { // 撤销过下注的注单 不能继续投注
					logs.Error("jili_single ", reqTypeStr, " 不能继续投注取消过投注的注单 thirdId=", thirdId, " order=", order)
					respdata.ErrorCode = 1
					respdata.Message = "不能继续投注取消过投注的注单"
					return e
				}
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"BetAmount": daogorm.Expr("BetAmount + ?", betAmount),
					"ThirdTime": thirdTime,
					"RawData":   string(bodyBytes),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("jili_single ", reqTypeStr, " 更新注单失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
					respdata.ErrorCode = 5
					respdata.Message = "更新注单失败"
					return e
				}
			}

			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, betAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", betAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 扣款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "扣款失败"
				return e
			}
			// 创建下注账变记录
			amountLogBet := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       -betAmount,
				AfterAmount:  userBalance.Amount - betAmount,
				Reason:       utils.BalanceCReasonJiLiBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			if reqdata.Preserve > 0 {
				amountLogBet.Memo += fmt.Sprintf(",preserve:%g", reqdata.Preserve)
			}
			e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 创建下注账变记录失败 thirdId=", thirdId, " amountLogBet=", amountLogBet, " error=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "创建下注账变记录失败"
				return e
			}
			respdata.Balance = userBalance.Amount - betAmount
			return nil
		})
	} else if reqdata.Type == 2 {
		// 开始结算事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.ErrorCode = 5
					respdata.Message = "会员不存在"
				} else {
					respdata.ErrorCode = 5
					respdata.Message = "查询用户信息失败"
				}
				return e
			}
			respdata.Balance = userBalance.Amount

			// 查询注单是否存在
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e != daogorm.ErrRecordNotFound {
					logs.Error("jili_single ", reqTypeStr, " 查询注单失败 thirdId=", thirdId, " err=", e.Error())
					respdata.ErrorCode = 5
					respdata.Message = "查询注单失败"
					return e
				} else { // 没有注单
					logs.Error("jili_single ", reqTypeStr, " 注单不存在 thirdId=", thirdId, " err=", e.Error())
					respdata.ErrorCode = 5
					respdata.Message = "注单不存在"
					return e
				}
			}

			if order.UserId != userId {
				logs.Error("jili_single ", reqTypeStr, " 注单用户不匹配 thirdId=", thirdId, " order=", order, " userId=", userId)
				respdata.ErrorCode = 5
				respdata.Message = "注单用户不匹配"
				e = errors.New("注单用户不匹配")
				return e
			}
			if order.DataState != -1 {
				if order.DataState == 1 {
					logs.Error("jili_single ", reqTypeStr, " 注单已结算 thirdId=", thirdId, " order=", order)
					respdata.ErrorCode = 1
					respdata.Message = "注单已结算"
					return e
				} else if order.DataState == -2 {
					logs.Error("jili_single ", reqTypeStr, " 注单已取消 thirdId=", thirdId, " order=", order)
					respdata.ErrorCode = 1
					respdata.Message = "注单已取消"
					return e
				} else {
					logs.Error("jili_single ", reqTypeStr, " 注单状态异常 thirdId=", thirdId, " order=", order)
					respdata.ErrorCode = 5
					respdata.Message = "注单状态异常"
					e = errors.New("注单状态异常")
					return e
				}
			}

			validBet := math.Abs(winAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			upData := map[string]interface{}{
				"WinAmount": winAmount,
				"ValidBet":  validBet,
				"ThirdTime": thirdTime,
				"State":     1,
				"DataState": 1,
				"RawData":   string(bodyBytes),
			}
			if reqdata.Preserve > 0 {
				upData["BetAmount"] = reqdata.BetAmount
				upData["WinAmount"] = reqdata.WinloseAmount
				validBet = math.Abs(reqdata.WinloseAmount - reqdata.BetAmount)
				if validBet > math.Abs(reqdata.BetAmount) {
					validBet = math.Abs(reqdata.BetAmount)
				}
				upData["ValidBet"] = validBet
			}
			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(upData)
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 更新注单失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " err=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "更新注单失败"
				return e
			}

			// 生成正式订单
			order.Id = 0
			order.WinAmount = winAmount
			order.ValidBet = validBet
			if reqdata.Preserve > 0 {
				order.BetAmount = reqdata.BetAmount
				order.WinAmount = reqdata.WinloseAmount
			}
			order.ThirdTime = thirdTime
			order.State = 1
			order.DataState = 1
			order.RawData = string(bodyBytes)
			order.CreateTime = thirdTime
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "创建正式注单失败"
				return e
			}

			resultTmp = tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("jili_single ", reqTypeStr, " 加款失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " err=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "加款失败"
				return e
			}
			// 创建下注账变记录
			amountLogWin := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonJiLiWin,
				Memo:         l.brandName + " win,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			if reqdata.Preserve > 0 {
				amountLogWin.Memo += fmt.Sprintf(",preserve:%g", reqdata.Preserve)
			}
			e = tx.Table("x_amount_change_log").Create(&amountLogWin).Error
			if e != nil {
				logs.Error("jili_singl ", reqTypeStr, " 创建结算账变记录失败 thirdId=", thirdId, " amountLogWin=", amountLogWin, " error=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "创建结算账变记录失败"
				return e
			}
			respdata.Balance = userBalance.Amount + winAmount
			return nil
		})
	} else {
		logs.Error("jili_single ", reqTypeStr, " type不正确 thirdId=", thirdId, " reqdata.Type=", reqdata.Type)
		respdata.ErrorCode = 3
		respdata.Message = "type类型不正确"
		ctx.RespJson(respdata)
		return
	}

	if err != nil {
		logs.Error("jili_single ", reqTypeStr, " 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][jili_single] ", reqTypeStr, " 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][jili_single] ", reqTypeStr, " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))

		if reqdata.Type == 2 {
			// 推送派奖事件通知
			if l.thirdGamePush != nil {
				//l.thirdGamePush.PushRewardEvent(userId, betTran.GameName, l.brandName, requests.ValidBet, winAmount, l.currency)
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				l.thirdGamePush.PushRewardEvent(gameList.GameType, l.brandName, thirdId)
			}
			// 获取结果详细信息链接
			go func() {
				time.Sleep(time.Second * 2)
				dUrl, err := l.getGameDetailUrl(reqdata.Round)
				if err != nil {
					logs.Error("jili_single ", reqTypeStr, " 获取游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
					return
				}
				err = server.Db().GormDao().Table(tablePre).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"BetCtx":     dUrl,
					"GameRst":    dUrl,
					"BetCtxType": 2,
				}).Error
				if err != nil {
					logs.Error("jili_single ", reqTypeStr, " 更新预设表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
				}
				err = server.Db().GormDao().Table(table).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"BetCtx":     dUrl,
					"GameRst":    dUrl,
					"BetCtxType": 2,
				}).Error
				if err != nil {
					logs.Error("jili_single ", reqTypeStr, " 更新正式表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
				}
			}()
		}
	}

	respdata.Username = reqdata.UserId
	respdata.Currency = l.currency
	ctx.RespJson(respdata)
	logs.Info("jili_single ", reqTypeStr, " 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

// cancelSessionBet 取消牌局型注单 (回滚一笔下注)
/*
三方接口说明：
取消一笔注单。玩家的下注额应退回，而派奖则要扣除。
游戏商应持续发送直到获得营运商回应。此项请求可于玩家脱机时发送, token 可能已经失
效, 因此除了玩家最后一次使用的 token 之外还会带上 userId, 营运商可自行选择使用
userId 或 token 来识别玩家身分。
*/
// 错误码 1=该注单已取消 2=注单无效 3=参数无效 5=其他错误
func (l *JiLiSingleService) CancelSessionBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ReqId         string  `json:"reqId"`
		Currency      string  `json:"currency"`
		Game          int64   `json:"game"`          // 游戏代码
		Round         int64   `json:"round"`         // 下注请求唯一识别值
		Offline       bool    `json:"offline"`       // 是否离线模式 true=离线模式 false=在线模式
		BetAmount     float64 `json:"betAmount"`     // 押注金额
		WinloseAmount float64 `json:"winloseAmount"` // 派彩金额
		UserId        string  `json:"userId"`        // 玩家账号唯一值
		Token         string  `json:"token"`         // 该注单发生时的 token
		SessionId     int64   `json:"sessionId"`     // 牌局唯一识别值
		Type          int     `json:"type"`          // 1=下注
		Preserve      float64 `json:"preserve"`      // 预扣金额
	}

	type ResponseData struct {
		ErrorCode int     `json:"ErrorCode"`
		Message   string  `json:"message"`
		Username  string  `json:"username"`
		Currency  string  `json:"currency"`
		Balance   float64 `json:"balance"`
		TxId      int64   `json:"txId"` //营运商取消注单后提供的交易识别唯一值 (注单已取消也请附上)
	}
	respdata := ResponseData{
		ErrorCode: JiLi_Code_Success,
		Message:   "success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("jili_single 回滚一笔下注 读取请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 5
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("jili_single 回滚一笔下注 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("jili_single 回滚一笔下注 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.SessionId)

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.ReqId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.ErrorCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("jili_single 回滚一笔下注 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if reqdata.BetAmount < 0 {
		logs.Error("jili_single 回滚一笔下注 押注金额不能小于0 thirdId=", thirdId, " reqdata.BetAmount=", reqdata.BetAmount)
		respdata.ErrorCode = 3
		respdata.Message = "betAmount押注金额不能小于0"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Type != 1 {
		logs.Error("jili_single 回滚一笔下注 只能回滚下注 thirdId=", thirdId, " reqdata.Type=", reqdata.Type)
		respdata.ErrorCode = 3
		respdata.Message = "type类型不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("jili_single 回滚一笔下注 币种不正确 thirdId=", thirdId, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = 3
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	// token可能失效，所以使用userId验证
	// userId, _, err := l.authToken(reqdata.Token)
	// if err != nil {
	// 	logs.Error("jili_single 回滚一笔下注 token验证错误 thirdId=", thirdId, " token=", reqdata.Token, " err=", err.Error())
	// 	respdata.ErrorCode = 4
	// 	respdata.Message = "token验证错误"
	// 	ctx.RespJson(respdata)
	// 	return
	// }
	userIdInt64, err := l.getUserIdFromUserName(reqdata.UserId)
	if err != nil {
		logs.Error("jili_single 回滚一笔下注 会员账号错误 thirdId=", thirdId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}
	userId := int(userIdInt64)

	gameId := fmt.Sprintf("%d", reqdata.Game)
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
	if err != nil {
		logs.Error("jili_single 回滚一笔下注 获取游戏名称失败 thirdId=", thirdId, " reqdata.Game=", reqdata.Game, " err=", err.Error())
		respdata.ErrorCode = 3
		respdata.Message = "游戏未添加"
		ctx.RespJson(respdata)
		return
	}
	// gameName := gameList.Name

	tablePre := "x_third_dianzhi_pre_order"
	if gameList.GameType == 1 {
		tablePre = "x_third_dianzhi_pre_order"
	} else if gameList.GameType == 2 {
		tablePre = "x_third_qipai_pre_order"
	} else {
		logs.Error("jili_single 回滚一笔下注 游戏类型配置错误 thirdId=", thirdId, " gameList=", gameList)
		respdata.ErrorCode = 5
		respdata.Message = "游戏类型配置错误"
		ctx.RespJson(respdata)
		return
	}

	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betAmount := reqdata.BetAmount

	// 开始回滚一笔下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("jili_single 回滚一笔下注 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = 5
				respdata.Message = "会员不存在"
			} else {
				respdata.ErrorCode = 5
				respdata.Message = "查询用户信息失败"
			}
			return e
		}
		respdata.Balance = userBalance.Amount

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			if e != daogorm.ErrRecordNotFound {
				logs.Error("jili_single 回滚一笔下注 查询注单失败 thirdId=", thirdId, " err=", e.Error())
				respdata.ErrorCode = 5
				respdata.Message = "查询注单失败"
				return e
			} else {
				logs.Error("jili_single 回滚一笔下注 注单不存在 thirdId=", thirdId, " err=", e.Error())
				respdata.ErrorCode = 2
				respdata.Message = "注单不存在"
				return e
			}
		}

		if order.DataState != -1 {
			if order.DataState == -2 {
				logs.Error("jili_single 回滚一笔下注 注单已取消 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = 2
				respdata.Message = "注单已取消"
				return e
			} else if order.DataState == 1 {
				// 正常情况游戏商不会在结算后发送 type 1 取消。但有可能因为网络等待时间或其他因素，导致营运商在结算后才收到 type 1 的取消。若营运商收到此种 type 1 取消，应取消该笔下注，原因是游戏商是以该笔下注失败为前提进行结算。
				// 这里我们不处理这种情况，因为结算返给用户的金额是对的。统计数据已经统计，修改了没有意义。出现异常时，查日志记录就行。
				logs.Error("jili_single 回滚一笔下注 不能回滚已结算注单 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = 5
				respdata.Message = "不能回滚已结算注单"
				e = errors.New("不能回滚已结算注单")
				return e
			} else {
				logs.Error("jili_single 回滚一笔下注 注单状态异常 thirdId=", thirdId, " order=", order)
				respdata.ErrorCode = 5
				respdata.Message = "注单状态异常"
				e = errors.New("注单状态异常")
				return e
			}
		}

		// 接受取消后，这一局就不可再接受任何下注，只会有结算。之后游戏商对同一局发送的下注无效。
		if order.State == -1 {
			logs.Error("jili_single 回滚一笔下注 不能回滚已取消注单 thirdId=", thirdId, " order=", order)
			respdata.ErrorCode = 1
			respdata.Message = "不能回滚已取消注单"
			e = errors.New("不能回滚已取消注单")
			return e
		}

		if order.BetAmount < betAmount {
			logs.Error("jili_single 回滚一笔下注 注单回滚金额不能大于总下注金额 thirdId=", thirdId, " order=", order, " betAmount=", betAmount)
			respdata.ErrorCode = 5
			respdata.Message = "注单回滚金额不能大于总下注金额"
			e = errors.New("注单回滚金额不能大于总下注金额")
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚一笔下注 加款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "加款失败"
			return e
		}
		// 创建回滚下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       betAmount,
			AfterAmount:  userBalance.Amount + betAmount,
			Reason:       utils.BalanceCReasonJiLiCancel,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("jili_single 回滚一笔下注 创建回滚下注账变记录失败 thirdId=", thirdId, " amountLogBet=", amountLogBet, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "创建回滚下注账变记录失败"
			return e
		}

		// 扣除下注金额
		// if order.BetAmount > betAmount {
		resultTmp = tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"BetAmount": daogorm.Expr("BetAmount - ?", betAmount),
			"State":     -1, // 用此标识符表示已经取消过的牌局。
			"ThirdTime": thirdTime,
			"RawData":   string(bodyBytes),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("jili_single 回滚一笔下注 更新注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
			respdata.ErrorCode = 5
			respdata.Message = "更新注单失败"
			return e
		}
		// } else { // 下注金额全部扣除完了，等于撤销局 看三方是否记录这种注单
		// 	resultTmp = tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
		// 		"BetAmount": daogorm.Expr("BetAmount - ?", betAmount),
		//      "State"    : -1, // 用此标识符表示已经取消过的牌局。
		// 		"ThirdTime": thirdTime,
		// 		"DataState": -2,
		//      "RawData"  : string(bodyBytes),
		// 	})
		// 	e = resultTmp.Error
		// 	if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
		// 		e = errors.New("更新条数0")
		// 	}
		// 	if e != nil {
		// 		logs.Error("jili_single 回滚一笔下注 更新注单失败 thirdId=", thirdId, " orderPre=", order, " error=", e.Error())
		// 		respdata.ErrorCode = 5
		// 		respdata.Message = "更新注单失败"
		// 		return e
		// 	}
		// }
		respdata.TxId = order.Id
		return nil
	})

	if err != nil {
		logs.Error("jili_single 回滚一笔下注 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][jili_single] 回滚一笔下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][jili_single] 回滚一笔下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Username = reqdata.UserId
	respdata.Currency = l.currency
	ctx.RespJson(respdata)
	logs.Info("jili_single 回滚一笔下注 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

// 获取游戏列表 /GetGameList
func (l *JiLiSingleService) LoadGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Add string `json:"add"` // 1 添加数据库 非1不添加数据库
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	urlParamData := fmt.Sprintf("AgentId=%s", l.agentId)
	sign := l.getSign(urlParamData, time.Now().Unix())

	type GameName struct {
		En string `json:"en-US"` // 英文名称
		Zh string `json:"zh-CN"` // 中文名称
		Tw string `json:"zh-TW"` // 繁体名称
	}
	type GameData struct {
		GameId         int64    `json:"GameId"`         // 游戏编码
		Name           GameName `json:"name"`           // 游戏名称
		GameCategoryId int      `json:"GameCategoryId"` // 游戏类型
		JP             bool     `json:"JP"`             // true=此款游戏内建 false=无内建
		Freespin       bool     `json:"Freespin"`       // true=此款游戏支持免费游戏 false=不支持免费游戏
	}
	type ResponseGameListData struct {
		ErrorCode int        `json:"ErrorCode"`
		Message   string     `json:"Message"`
		Data      []GameData `json:"Data"`
	}
	respGameListData := ResponseGameListData{}

	client := &http.Client{}
	gameListUrl := fmt.Sprintf("%s/GetGameList?%s&Key=%s", l.apiUrl, urlParamData, sign)
	fmt.Println("gameListUrl=", gameListUrl)
	req, _ := http.NewRequest(http.MethodGet, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("jili_single 获取游戏列表请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("jili_single 获取游戏列表响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("jili_single 获取游戏列表请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respGameListData)
	if err != nil {
		logs.Error("jili_single 获取游戏列表 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if respGameListData.ErrorCode != JiLi_Code_Success {
		logs.Error("jili_single 获取游戏列表失败 错误信息=", respGameListData.Message)
		ctx.RespErrString(true, &errcode, respGameListData.Message)
		return
	}

	if reqdata.Add == "1" {
		for _, v := range respGameListData.Data {
			gameType := 1
			// GameCategoryId 1电子 2棋牌 5捕鱼 8押分机(含宾果)
			if v.GameCategoryId == 1 || v.GameCategoryId == 5 || v.GameCategoryId == 8 {
				gameType = 1 // 电子
			} else if v.GameCategoryId == 2 {
				gameType = 2 // 棋牌
			} else {
				continue
			}
			// GameType solt
			gameId := fmt.Sprintf("%d", v.GameId)
			betTran := thirdGameModel.GameList{}
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).First(&betTran).Error
			if err != daogorm.ErrRecordNotFound {
				continue
			}

			//写入数据库中
			gameInfo := xgo.H{
				"Brand":     l.brandName,
				"GameId":    gameId,
				"Name":      v.Name.Zh,
				"EName":     v.Name.En,
				"GameType":  gameType, // 1电子 2棋牌 3趣味 4彩票 5真人 6体育
				"HubType":   0,        // 0非聚合类型 1hub88类型
				"State":     2,
				"OpenState": 1,
			}
			server.Db().Table("x_game_list").Insert(gameInfo)
		}
	}

	ctx.RespOK(respGameListData)
	return
}

// 取得注单详细结果链接 /api1/GetGameDetailUrl
func (l *JiLiSingleService) getGameDetailUrl(wagersId int64, retryCount ...int) (dUrl string, err error) {
	// 设置最大重试次数
	maxRetry := 3
	currentRetry := 0
	if len(retryCount) > 0 {
		currentRetry = retryCount[0]
		if currentRetry >= maxRetry {
			logs.Error("jili_single 取得注单详细结果链接 已达到最大重试次数 wagersId=", wagersId)
			return "", errors.New("已达到最大重试次数")
		}
	}

	urlParamData := fmt.Sprintf("WagersId=%d&AgentId=%s", wagersId, l.agentId)
	sign := l.getSign(urlParamData, time.Now().Unix())

	type ResponseData struct {
		ErrorCode int    `json:"ErrorCode"`
		Message   string `json:"Message"`
		Data      struct {
			Url string `json:"Url"`
		} `json:"Data"`
	}
	respdata := ResponseData{}

	client := &http.Client{}
	url := fmt.Sprintf("%s/GetGameDetailUrl?%s&Lang=zh-CN&Key=%s", l.apiUrl, urlParamData, sign)
	fmt.Println("getGameDetailUrl url=", url)
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("jili_single 取得注单详细结果链接请求错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("jili_single 取得注单详细结果链接响应错误 err=", err.Error())
		return
	}
	logs.Info("jili_single 取得注单详细结果链接请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("jili_single 取得注单详细结果链接 解析响应消息体错误 err=", err.Error())
		return
	}
	if respdata.ErrorCode != JiLi_Code_Success {
		if respdata.ErrorCode == 101 { // 查不到记录等3秒再查一次，但有最大重试次数限制
			time.Sleep(time.Second * 3)
			return l.getGameDetailUrl(wagersId, currentRetry+1)
		}
		logs.Error("jili_single 取得注单详细结果链接失败 错误信息=", respdata.Message)
		err = errors.New(respdata.Message)
		return
	}

	dUrl = respdata.Data.Url
	return
}

// ///////////////////////////////工具函数/////////////////////////////////
func (l *JiLiSingleService) getUserNameFromUserId(userId int64) string {
	return fmt.Sprintf("%dhaxiliji", userId)
}

func (l *JiLiSingleService) getUserIdFromUserName(userName string) (userId int64, err error) {
	userId, err = strconv.ParseInt(userName, 10, 64)
	if err != nil {
		return
	}
	if !strings.EqualFold(userName, l.getUserNameFromUserId(userId)) {
		err = errors.New("会员账号格式错误")
		return
	}
	return
}

const srcRandStr string = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func (l *JiLiSingleService) getRandomStr(n int) (str string) {
	for i := 0; i < n; i++ {
		str += string(srcRandStr[rand.Intn(len(srcRandStr))])
	}
	return
}

// 计算签名字符串
func (l *JiLiSingleService) getSign(data string, t int64) (sign string) {
	tTime := time.Unix(t, 0).In(l.locMd5)
	// KeyG = MD5(DateTime.Now.ToString(“yyMMd”) + AgentId + AgentKey)
	// DateTime.Now = 当下 UTC-4 时间, 格式為 yyMMd
	// 年: 公元年分末两位
	// 月: 两位数, 1~9 前面須补 0
	// 日: 一位数或两位数, 1~9 前面请不要补 0
	keyG := base.MD5(fmt.Sprintf("%d%02d%d%s%s", tTime.Year()%100, tTime.Month(), tTime.Day(), l.agentId, l.agentKey))
	sign = l.getRandomStr(6) + base.MD5(data+keyG) + l.getRandomStr(6)
	return
}

// 生成token
func (l *JiLiSingleService) generateToken() (token string) {
	token = "jili_" + uuid.NewString()
	return
}

// 设置token
func (l *JiLiSingleService) setToken(userId int, account, newToken string) (err error) {
	type JiLiTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyJiLi + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token := string(v.([]byte))
		rKeyToken = cacheKeyJiLi + "token:" + token
		// 删除旧的token
		if err = server.Redis().Del(rKeyToken); err != nil {
			logs.Error("jili_single setToken del old token rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}
	}

	tokendata := JiLiTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyJiLi + "token:" + newToken
	// 设置新的token
	if err = server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("jili_single setToken set redis rKeyToken=", rKeyToken, " err=", err.Error())
		return
	}
	// 覆盖旧的设置
	if err = server.Redis().SetStringEx(rKeyUser, 86400, newToken); err != nil {
		logs.Error("jili_single setToken set redis rKeyUser=", rKeyUser, " err=", err.Error())
		return
	}
	return
}

// 验证token
func (l *JiLiSingleService) authToken(token string) (userId int, account string, err error) {
	type JiLiTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		err = errors.New("token为空")
		return
	}
	rKeyToken := cacheKeyJiLi + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := JiLiTokenData{}
		if err = json.Unmarshal(v.([]byte), &tokendata); err != nil {
			logs.Error("jili_single authToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}
		rKeyUser := cacheKeyJiLi + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		if err = server.Redis().Expire(rKeyToken, 86401); err != nil {
			logs.Error("jili_single authToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}
		if err = server.Redis().Expire(rKeyUser, 86400); err != nil {
			logs.Error("jili_single authToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		err = errors.New("不存在")
		logs.Error("jili_single authToken token=", token, " 不存在")
	}
	return
}
