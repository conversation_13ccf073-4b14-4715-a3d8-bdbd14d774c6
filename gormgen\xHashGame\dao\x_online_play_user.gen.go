// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOnlinePlayUser(db *gorm.DB, opts ...gen.DOOption) xOnlinePlayUser {
	_xOnlinePlayUser := xOnlinePlayUser{}

	_xOnlinePlayUser.xOnlinePlayUserDo.UseDB(db, opts...)
	_xOnlinePlayUser.xOnlinePlayUserDo.UseModel(&model.XOnlinePlayUser{})

	tableName := _xOnlinePlayUser.xOnlinePlayUserDo.TableName()
	_xOnlinePlayUser.ALL = field.NewAsterisk(tableName)
	_xOnlinePlayUser.ID = field.NewInt32(tableName, "Id")
	_xOnlinePlayUser.Online = field.NewInt32(tableName, "Online")
	_xOnlinePlayUser.CreateTime = field.NewTime(tableName, "CreateTime")

	_xOnlinePlayUser.fillFieldMap()

	return _xOnlinePlayUser
}

type xOnlinePlayUser struct {
	xOnlinePlayUserDo xOnlinePlayUserDo

	ALL        field.Asterisk
	ID         field.Int32
	Online     field.Int32 // 在线人数
	CreateTime field.Time  // 记录时间

	fieldMap map[string]field.Expr
}

func (x xOnlinePlayUser) Table(newTableName string) *xOnlinePlayUser {
	x.xOnlinePlayUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOnlinePlayUser) As(alias string) *xOnlinePlayUser {
	x.xOnlinePlayUserDo.DO = *(x.xOnlinePlayUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOnlinePlayUser) updateTableName(table string) *xOnlinePlayUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Online = field.NewInt32(table, "Online")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xOnlinePlayUser) WithContext(ctx context.Context) *xOnlinePlayUserDo {
	return x.xOnlinePlayUserDo.WithContext(ctx)
}

func (x xOnlinePlayUser) TableName() string { return x.xOnlinePlayUserDo.TableName() }

func (x xOnlinePlayUser) Alias() string { return x.xOnlinePlayUserDo.Alias() }

func (x xOnlinePlayUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xOnlinePlayUserDo.Columns(cols...)
}

func (x *xOnlinePlayUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOnlinePlayUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 3)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Online"] = x.Online
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xOnlinePlayUser) clone(db *gorm.DB) xOnlinePlayUser {
	x.xOnlinePlayUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOnlinePlayUser) replaceDB(db *gorm.DB) xOnlinePlayUser {
	x.xOnlinePlayUserDo.ReplaceDB(db)
	return x
}

type xOnlinePlayUserDo struct{ gen.DO }

func (x xOnlinePlayUserDo) Debug() *xOnlinePlayUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xOnlinePlayUserDo) WithContext(ctx context.Context) *xOnlinePlayUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOnlinePlayUserDo) ReadDB() *xOnlinePlayUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOnlinePlayUserDo) WriteDB() *xOnlinePlayUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOnlinePlayUserDo) Session(config *gorm.Session) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOnlinePlayUserDo) Clauses(conds ...clause.Expression) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOnlinePlayUserDo) Returning(value interface{}, columns ...string) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOnlinePlayUserDo) Not(conds ...gen.Condition) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOnlinePlayUserDo) Or(conds ...gen.Condition) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOnlinePlayUserDo) Select(conds ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOnlinePlayUserDo) Where(conds ...gen.Condition) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOnlinePlayUserDo) Order(conds ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOnlinePlayUserDo) Distinct(cols ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOnlinePlayUserDo) Omit(cols ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOnlinePlayUserDo) Join(table schema.Tabler, on ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOnlinePlayUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOnlinePlayUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOnlinePlayUserDo) Group(cols ...field.Expr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOnlinePlayUserDo) Having(conds ...gen.Condition) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOnlinePlayUserDo) Limit(limit int) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOnlinePlayUserDo) Offset(offset int) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOnlinePlayUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOnlinePlayUserDo) Unscoped() *xOnlinePlayUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOnlinePlayUserDo) Create(values ...*model.XOnlinePlayUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOnlinePlayUserDo) CreateInBatches(values []*model.XOnlinePlayUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOnlinePlayUserDo) Save(values ...*model.XOnlinePlayUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOnlinePlayUserDo) First() (*model.XOnlinePlayUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlinePlayUser), nil
	}
}

func (x xOnlinePlayUserDo) Take() (*model.XOnlinePlayUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlinePlayUser), nil
	}
}

func (x xOnlinePlayUserDo) Last() (*model.XOnlinePlayUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlinePlayUser), nil
	}
}

func (x xOnlinePlayUserDo) Find() ([]*model.XOnlinePlayUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOnlinePlayUser), err
}

func (x xOnlinePlayUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOnlinePlayUser, err error) {
	buf := make([]*model.XOnlinePlayUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOnlinePlayUserDo) FindInBatches(result *[]*model.XOnlinePlayUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOnlinePlayUserDo) Attrs(attrs ...field.AssignExpr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOnlinePlayUserDo) Assign(attrs ...field.AssignExpr) *xOnlinePlayUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOnlinePlayUserDo) Joins(fields ...field.RelationField) *xOnlinePlayUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOnlinePlayUserDo) Preload(fields ...field.RelationField) *xOnlinePlayUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOnlinePlayUserDo) FirstOrInit() (*model.XOnlinePlayUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlinePlayUser), nil
	}
}

func (x xOnlinePlayUserDo) FirstOrCreate() (*model.XOnlinePlayUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlinePlayUser), nil
	}
}

func (x xOnlinePlayUserDo) FindByPage(offset int, limit int) (result []*model.XOnlinePlayUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOnlinePlayUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOnlinePlayUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOnlinePlayUserDo) Delete(models ...*model.XOnlinePlayUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOnlinePlayUserDo) withDO(do gen.Dao) *xOnlinePlayUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
