// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentIndependentCommissionDetail(db *gorm.DB, opts ...gen.DOOption) xAgentIndependentCommissionDetail {
	_xAgentIndependentCommissionDetail := xAgentIndependentCommissionDetail{}

	_xAgentIndependentCommissionDetail.xAgentIndependentCommissionDetailDo.UseDB(db, opts...)
	_xAgentIndependentCommissionDetail.xAgentIndependentCommissionDetailDo.UseModel(&model.XAgentIndependentCommissionDetail{})

	tableName := _xAgentIndependentCommissionDetail.xAgentIndependentCommissionDetailDo.TableName()
	_xAgentIndependentCommissionDetail.ALL = field.NewAsterisk(tableName)
	_xAgentIndependentCommissionDetail.ID = field.NewInt64(tableName, "Id")
	_xAgentIndependentCommissionDetail.GameType = field.NewString(tableName, "GameType")
	_xAgentIndependentCommissionDetail.Ordertable = field.NewString(tableName, "Ordertable")
	_xAgentIndependentCommissionDetail.OrderID = field.NewInt64(tableName, "OrderId")
	_xAgentIndependentCommissionDetail.BetUserID = field.NewInt32(tableName, "BetUserId")
	_xAgentIndependentCommissionDetail.UserID = field.NewInt32(tableName, "UserId")
	_xAgentIndependentCommissionDetail.AgentID = field.NewInt32(tableName, "AgentId")
	_xAgentIndependentCommissionDetail.FromAddress = field.NewString(tableName, "FromAddress")
	_xAgentIndependentCommissionDetail.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xAgentIndependentCommissionDetail.Liushui = field.NewFloat64(tableName, "Liushui")
	_xAgentIndependentCommissionDetail.UserRate = field.NewFloat32(tableName, "UserRate")
	_xAgentIndependentCommissionDetail.AgentRate = field.NewFloat32(tableName, "AgentRate")
	_xAgentIndependentCommissionDetail.Rate = field.NewFloat32(tableName, "Rate")
	_xAgentIndependentCommissionDetail.Commission = field.NewFloat64(tableName, "Commission")
	_xAgentIndependentCommissionDetail.FenCheng = field.NewString(tableName, "FenCheng")
	_xAgentIndependentCommissionDetail.AgentFenCheng = field.NewString(tableName, "AgentFenCheng")
	_xAgentIndependentCommissionDetail.CommissionType = field.NewInt32(tableName, "CommissionType")
	_xAgentIndependentCommissionDetail.Symbol = field.NewString(tableName, "Symbol")
	_xAgentIndependentCommissionDetail.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentIndependentCommissionDetail.SysTime = field.NewTime(tableName, "SysTime")

	_xAgentIndependentCommissionDetail.fillFieldMap()

	return _xAgentIndependentCommissionDetail
}

type xAgentIndependentCommissionDetail struct {
	xAgentIndependentCommissionDetailDo xAgentIndependentCommissionDetailDo

	ALL            field.Asterisk
	ID             field.Int64
	GameType       field.String // haxiusdt,live,dianzi
	Ordertable     field.String // x_order,x_third_live
	OrderID        field.Int64  // x_order.id, x_third_live.id
	BetUserID      field.Int32  // 投注的玩家 orderUserid
	UserID         field.Int32
	AgentID        field.Int32
	FromAddress    field.String  // 玩家地址
	BetAmount      field.Float64 // 投注
	Liushui        field.Float64 // 流水
	UserRate       field.Float32 // 用户返佣比例
	AgentRate      field.Float32 // 上级的返佣比例
	Rate           field.Float32 // 计算用的返佣比例
	Commission     field.Float64 // 返佣
	FenCheng       field.String  // 分成
	AgentFenCheng  field.String  // 上级分成
	CommissionType field.Int32   // 0 不限 1 自身 2 直属 3 团队
	Symbol         field.String
	CreateTime     field.Time // 算佣金时间
	SysTime        field.Time // 订单时间

	fieldMap map[string]field.Expr
}

func (x xAgentIndependentCommissionDetail) Table(newTableName string) *xAgentIndependentCommissionDetail {
	x.xAgentIndependentCommissionDetailDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentIndependentCommissionDetail) As(alias string) *xAgentIndependentCommissionDetail {
	x.xAgentIndependentCommissionDetailDo.DO = *(x.xAgentIndependentCommissionDetailDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentIndependentCommissionDetail) updateTableName(table string) *xAgentIndependentCommissionDetail {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.GameType = field.NewString(table, "GameType")
	x.Ordertable = field.NewString(table, "Ordertable")
	x.OrderID = field.NewInt64(table, "OrderId")
	x.BetUserID = field.NewInt32(table, "BetUserId")
	x.UserID = field.NewInt32(table, "UserId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.FromAddress = field.NewString(table, "FromAddress")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.Liushui = field.NewFloat64(table, "Liushui")
	x.UserRate = field.NewFloat32(table, "UserRate")
	x.AgentRate = field.NewFloat32(table, "AgentRate")
	x.Rate = field.NewFloat32(table, "Rate")
	x.Commission = field.NewFloat64(table, "Commission")
	x.FenCheng = field.NewString(table, "FenCheng")
	x.AgentFenCheng = field.NewString(table, "AgentFenCheng")
	x.CommissionType = field.NewInt32(table, "CommissionType")
	x.Symbol = field.NewString(table, "Symbol")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.SysTime = field.NewTime(table, "SysTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentIndependentCommissionDetail) WithContext(ctx context.Context) *xAgentIndependentCommissionDetailDo {
	return x.xAgentIndependentCommissionDetailDo.WithContext(ctx)
}

func (x xAgentIndependentCommissionDetail) TableName() string {
	return x.xAgentIndependentCommissionDetailDo.TableName()
}

func (x xAgentIndependentCommissionDetail) Alias() string {
	return x.xAgentIndependentCommissionDetailDo.Alias()
}

func (x xAgentIndependentCommissionDetail) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentIndependentCommissionDetailDo.Columns(cols...)
}

func (x *xAgentIndependentCommissionDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentIndependentCommissionDetail) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 20)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["Ordertable"] = x.Ordertable
	x.fieldMap["OrderId"] = x.OrderID
	x.fieldMap["BetUserId"] = x.BetUserID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["FromAddress"] = x.FromAddress
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["Liushui"] = x.Liushui
	x.fieldMap["UserRate"] = x.UserRate
	x.fieldMap["AgentRate"] = x.AgentRate
	x.fieldMap["Rate"] = x.Rate
	x.fieldMap["Commission"] = x.Commission
	x.fieldMap["FenCheng"] = x.FenCheng
	x.fieldMap["AgentFenCheng"] = x.AgentFenCheng
	x.fieldMap["CommissionType"] = x.CommissionType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["SysTime"] = x.SysTime
}

func (x xAgentIndependentCommissionDetail) clone(db *gorm.DB) xAgentIndependentCommissionDetail {
	x.xAgentIndependentCommissionDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentIndependentCommissionDetail) replaceDB(db *gorm.DB) xAgentIndependentCommissionDetail {
	x.xAgentIndependentCommissionDetailDo.ReplaceDB(db)
	return x
}

type xAgentIndependentCommissionDetailDo struct{ gen.DO }

func (x xAgentIndependentCommissionDetailDo) Debug() *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentIndependentCommissionDetailDo) WithContext(ctx context.Context) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentIndependentCommissionDetailDo) ReadDB() *xAgentIndependentCommissionDetailDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentIndependentCommissionDetailDo) WriteDB() *xAgentIndependentCommissionDetailDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentIndependentCommissionDetailDo) Session(config *gorm.Session) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentIndependentCommissionDetailDo) Clauses(conds ...clause.Expression) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Returning(value interface{}, columns ...string) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentIndependentCommissionDetailDo) Not(conds ...gen.Condition) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Or(conds ...gen.Condition) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Select(conds ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Where(conds ...gen.Condition) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Order(conds ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Distinct(cols ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentIndependentCommissionDetailDo) Omit(cols ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentIndependentCommissionDetailDo) Join(table schema.Tabler, on ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentIndependentCommissionDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentIndependentCommissionDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentIndependentCommissionDetailDo) Group(cols ...field.Expr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentIndependentCommissionDetailDo) Having(conds ...gen.Condition) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentIndependentCommissionDetailDo) Limit(limit int) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentIndependentCommissionDetailDo) Offset(offset int) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentIndependentCommissionDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentIndependentCommissionDetailDo) Unscoped() *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentIndependentCommissionDetailDo) Create(values ...*model.XAgentIndependentCommissionDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentIndependentCommissionDetailDo) CreateInBatches(values []*model.XAgentIndependentCommissionDetail, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentIndependentCommissionDetailDo) Save(values ...*model.XAgentIndependentCommissionDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentIndependentCommissionDetailDo) First() (*model.XAgentIndependentCommissionDetail, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependentCommissionDetail), nil
	}
}

func (x xAgentIndependentCommissionDetailDo) Take() (*model.XAgentIndependentCommissionDetail, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependentCommissionDetail), nil
	}
}

func (x xAgentIndependentCommissionDetailDo) Last() (*model.XAgentIndependentCommissionDetail, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependentCommissionDetail), nil
	}
}

func (x xAgentIndependentCommissionDetailDo) Find() ([]*model.XAgentIndependentCommissionDetail, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentIndependentCommissionDetail), err
}

func (x xAgentIndependentCommissionDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentIndependentCommissionDetail, err error) {
	buf := make([]*model.XAgentIndependentCommissionDetail, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentIndependentCommissionDetailDo) FindInBatches(result *[]*model.XAgentIndependentCommissionDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentIndependentCommissionDetailDo) Attrs(attrs ...field.AssignExpr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentIndependentCommissionDetailDo) Assign(attrs ...field.AssignExpr) *xAgentIndependentCommissionDetailDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentIndependentCommissionDetailDo) Joins(fields ...field.RelationField) *xAgentIndependentCommissionDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentIndependentCommissionDetailDo) Preload(fields ...field.RelationField) *xAgentIndependentCommissionDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentIndependentCommissionDetailDo) FirstOrInit() (*model.XAgentIndependentCommissionDetail, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependentCommissionDetail), nil
	}
}

func (x xAgentIndependentCommissionDetailDo) FirstOrCreate() (*model.XAgentIndependentCommissionDetail, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependentCommissionDetail), nil
	}
}

func (x xAgentIndependentCommissionDetailDo) FindByPage(offset int, limit int) (result []*model.XAgentIndependentCommissionDetail, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentIndependentCommissionDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentIndependentCommissionDetailDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentIndependentCommissionDetailDo) Delete(models ...*model.XAgentIndependentCommissionDetail) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentIndependentCommissionDetailDo) withDO(do gen.Dao) *xAgentIndependentCommissionDetailDo {
	x.DO = *do.(*gen.DO)
	return x
}
