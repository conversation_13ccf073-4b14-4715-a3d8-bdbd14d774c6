package paycontroller

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

func (c *PayController) InitHambit() {
	server.Http().PostNoAuth("/api/hambitrecharge", c.hambitrecharge)
	//server.Http().PostNoAuth("/api/hambitwithward", c.hambitwithward)
}

func reflectJson2Map(j string) map[string]string {
	params := make(map[string]string)
	var event map[string]interface{}
	decoder := json.NewDecoder(bytes.NewBufferString(j))
	decoder.UseNumber()
	err := decoder.Decode(&event)
	if err != nil {
		return nil
	}
	for k, v := range event {
		params[k] = fmt.Sprintf("%v", reflect.ValueOf(v))
	}
	return params
}

func reflectStruct2Map(s interface{}) map[string]string {
	marshal, err := json.Marshal(s)
	if err != nil {
		return nil
	}
	params := reflectJson2Map(string(marshal))
	return params
}

func (c *PayController) hambit_http_post(jcfg map[string]interface{}, path string, param map[string]interface{}) (*map[string]interface{}, error) {
	url := jcfg["api"].(string) + path
	params := make(map[string]string)

	params["access_key"] = jcfg["key"].(string)
	params["timestamp"] = strconv.FormatInt(time.Now().UnixMilli(), 10)
	params["nonce"] = abugo.GetUuid()

	jsonStr, _ := json.Marshal(param)

	jsonMap := reflectJson2Map(string(jsonStr))

	for key, value := range jsonMap {
		params[key] = value

	}

	var sortKeys []string
	for k := range params {
		if params[k] != "" {
			sortKeys = append(sortKeys, k)
		}
	}
	sort.Strings(sortKeys)

	var finalStrForSign string
	for _, k := range sortKeys {
		finalStrForSign = finalStrForSign + k + "=" + params[k] + "&"
	}

	finalStrForSign = strings.TrimRight(finalStrForSign, "&")

	key := []byte(jcfg["secret"].(string))
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(finalStrForSign))

	sign := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	request, _ := http.NewRequest("POST", url, bytes.NewBuffer([]byte(jsonStr)))
	request.Header.Set("Content-type", "application/json;charset=utf-8")
	request.Header.Set("sign", sign)
	request.Header.Set("access_key", params["access_key"])
	request.Header.Set("timestamp", params["timestamp"])
	request.Header.Set("nonce", params["nonce"])

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		logs.Error("hamit_http_post:", err)
		return nil, err
	}
	defer resp.Body.Close()

	bodyData, _ := io.ReadAll(resp.Body)

	reqbody, _ := json.Marshal(param)
	logs.Debug("hambit_http_post:", url, string(reqbody), "|", string(bodyData))

	jdata := map[string]interface{}{}
	err = json.Unmarshal(bodyData, &jdata)
	if err != nil {
		logs.Error("hambit_http_post:", err)
		return nil, err
	}
	code := abugo.GetInt64FromInterface(jdata["code"])
	if code != 200 {
		return nil, errors.New(jdata["msg"].(string))
	}
	if td, ok := jdata["data"].(map[string]interface{}); ok {
		return &td, nil
	} else {
		r := map[string]interface{}{}
		r["data"] = jdata["data"]
		return &r, nil
	}
}

func (c *PayController) hambitrecharge(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Debug("hambitrecharge:", string(body))
	jdata := map[string]interface{}{}
	err := json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("hambitrecharge:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	OrderId := xgo.ToInt(jdata["externalOrderId"])
	orderdata, err := server.XDb().Table("x_recharge").Where("Id = ?", OrderId).First()
	if err != nil {
		logs.Error("hambitrecharge:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	if orderdata == nil {
		logs.Error("hambitrecharge 订单不存在", OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单不存在",
		})
		return
	}
	if orderdata.Int("State") != 3 {
		logs.Error("hambitrecharge 订单状态不正确", OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单状态不正确",
		})
		return
	}

	if strings.ToLower(orderdata.String("Symbol")) != strings.ToLower(jdata["currencyType"].(string)) {
		logs.Error("hambitrecharge 币种不正确", OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "币种不正确",
		})
		return
	}

	PayId := orderdata.Int("PayId")
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", PayId).First()
	if err != nil {
		logs.Error("hambitrecharge 支付方式不存在", PayId, err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	if paymethod == nil {
		logs.Error("hambitrecharge 支付方式不存在", PayId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "支付方式不存在",
		})
		return
	}

	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)

	fmt.Println("hambitrecharge:", orderdata.String("ThirdId"))
	checkdata := map[string]interface{}{}
	checkurl := ""
	if strings.ToLower(orderdata.String("Symbol")) == "brl" {
		checkurl = "/api/v3/bra/query/collectingOrder"
		checkdata["externalOrderId"] = fmt.Sprint(OrderId)
		checkdata["orderId"] = orderdata.String("ThirdId")
	} else if strings.ToLower(orderdata.String("Symbol")) == "mxn" {
		checkurl = "/api/v3/mex/bankTransfers/query/collectingOrder"
	} else if strings.ToLower(orderdata.String("Symbol")) == "pen" {
		checkurl = "/api/v3/per/bankTransfers/query/collectingOrder"
	} else if strings.ToLower(orderdata.String("Symbol")) == "inr" {
		checkurl = "/api/v3/ind/query/collectingOrder"
	} else if strings.ToLower(orderdata.String("Symbol")) == "idr" {
		checkurl = "/api/v3/idn/query/collectingOrder"
	} else if strings.ToLower(orderdata.String("Symbol")) == "ngn" {
		checkurl = "/api/v3/ng/query/collectingOrder"
	}
	jverify, err := c.hambit_http_post(jcfg, checkurl, checkdata)
	if err != nil {
		logs.Error("hambitrecharge:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	arrverify := (*jverify)["data"].([]interface{})
	if len(arrverify) == 0 {
		logs.Error("hambitrecharge:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单不存在",
		})
		return
	}
	verifydata := arrverify[0].(map[string]interface{})

	// if xgo.ToInt(verifydata["orderStatus"]) != 2 {
	// 	ctx.Gin().JSON(200, gin.H{
	// 		"code": 500,
	// 		"msg":  "订单反查状态不正确",
	// 	})
	// 	return
	// }

	if xgo.ToString(verifydata["currencyType"]) == strings.ToLower(orderdata.String("Symbol")) {
		logs.Error("hambitrecharge 币种不正确", OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "币种不正确",
		})
		return
	}

	orderamount := orderdata.Float64("Amount")

	if xgo.ToFloat(verifydata["orderAmount"])-orderamount > 0.01 {
		logs.Error("hambitrecharge 金额不正确", OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "金额不正确",
		})
		return
	}
	c.rechargeCallbackHandel(orderdata.Int("UserId"), int(xgo.ToInt(OrderId)), 5)
	ctx.Gin().JSON(200, gin.H{
		"code":    200,
		"success": true,
	})
}

func (c *PayController) hambit_create_recharge_order(ctx *abugo.AbuHttpContent, jcfg map[string]interface{}, rate float64, userdata *xgo.XMap, SpecialAgent int, token *server.TokenData, reqdata *CreateOrderReq, _ *xgo.XMap) {
	errcode := 0
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var currentRate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		currentRate = 0                  // 运营商ID为26时，汇率设为0
		amount = float64(reqdata.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		currentRate, err = c.getWithdrawRate(reqdata.Symbol) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(reqdata.Amount) / currentRate // 运营商ID不为26时，进行汇率转换
		rate = currentRate
	}
	OrderId, _ := server.Db().Table("x_recharge").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      5,
		"Amount":       reqdata.Amount,
		"RealAmount":   amount,
		"TransferRate": rate,
		"State":        3,
		"CSGroup":      userdata.Int("CSGroup"),
		"CSId":         userdata.Int("CSId"),
		"SpecialAgent": SpecialAgent,
		"TopAgentId":   userdata.Int("TopAgentId"),
		"OrderType":    reqdata.OrderType,
	})
	if reqdata.Symbol == "brl" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/bra/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "PIX",
			"externalOrderId": fmt.Sprint(OrderId),
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
			"userIdentity":    fmt.Sprint(token.UserId),
			"inputCpf":        0,
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(xgo.H{
			"payurl": (*data)["cashierUrl"],
		})
	} else if reqdata.Symbol == "mxn" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/mex/bankTransfers/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "BANK",
			"externalOrderId": fmt.Sprint(OrderId),
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
			"name":            fmt.Sprint(token.UserId),
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(data)
	} else if reqdata.Symbol == "pen" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/per/bankTransfers/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "BANK",
			"externalOrderId": fmt.Sprint(OrderId),
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
			"name":            fmt.Sprint(token.UserId),
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(data)
	} else if reqdata.Symbol == "inr" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/ind/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "BANK",
			"externalOrderId": fmt.Sprint(OrderId),
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
			"remark":          fmt.Sprint(token.UserId),
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(data)
	} else if reqdata.Symbol == "idr" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/idn/qris/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "QRIS",
			"externalOrderId": fmt.Sprint(OrderId),
			"accountName":     "OVO DANA LINKAJA SHOPEEPAY",
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(data)
	} else if reqdata.Symbol == "ngn" {
		data, err := c.hambit_http_post(jcfg, "/api/v3/ind/createCollectingOrder", xgo.H{
			"amount":          fmt.Sprintf("%.2f", reqdata.Amount),
			"channelType":     "BANK",
			"externalOrderId": fmt.Sprint(OrderId),
			"notifyUrl":       jcfg["notify"].(string) + "/api/hambitrecharge",
			"remark":          fmt.Sprint(token.UserId),
		})
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
		bytes, _ := json.Marshal(data)
		server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": (*data)["currencyOrderVo"].(map[string]interface{})["orderId"],
		})
		ctx.RespOK(data)
	}
}
