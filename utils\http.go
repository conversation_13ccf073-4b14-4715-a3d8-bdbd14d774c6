package utils

import (
	"github.com/go-resty/resty/v2"
	"net/http"
	"time"
)

var httpClient *resty.Client

func HttpClient() *resty.Client {
	if httpClient != nil {
		return httpClient
	}
	httpClient = resty.New()

	httpClient.SetDebug(true)

	httpClient.SetTimeout(30 * time.Second)
	httpClient.SetRetryWaitTime(5 * time.Second)
	httpClient.SetRetryCount(2)

	httpClient.SetTransport(&http.Transport{
		MaxIdleConns:        256,
		MaxIdleConnsPerHost: 64, // 对于每个主机，保持最大空闲连接数为 10
		MaxConnsPerHost:     128,

		IdleConnTimeout:       30 * time.Second, // 空闲连接超时时间为 30 秒
		TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间为 10 秒
		ResponseHeaderTimeout: 10 * time.Second, // 等待响应头的超时时间为 20 秒
	})
	return httpClient
}
