package single

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/imroc/req"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/oschwald/geoip2-golang"
)

// NewSabaService 沙巴体育初始化入口
/**
[GetBalance]
[PlaceBet]  单笔
[ConfirmBet] 单笔
[PlaceBetParlay] 多笔
[ConfirmBetParlay] 多笔
[Settle] 多笔
[Resettle] 单笔
[Unsettle] 单笔
[CancelBet]  串注 多笔，其它单笔
[AdjustBalance]
1、下注：有多笔注单，单笔失败 注单全部回滚，返回失败。
2、结算有多笔注单时，单笔失败，不回滚全部注单，返回失败，沙巴系统继续调用Settle重试，哈希系统收到继续结算未结算的注单。
3、confirmable/confirmatory只可能給玩家加錢, 不會再要扣錢,需要返回成功
*/
func NewSabaService(temp map[string]string, fc func(int) error) *SabaService {
	//暂未使用
	games := map[string]string{
		"足球":   "1",
		"篮球":   "2",
		"网球":   "3",
		"板球":   "7",
		"羽毛球":  "12",
		"美式足球": "13",
		"冰球":   "14",
		"桌球":   "15",
		"手球":   "19",
		"排球":   "21",
		"电子竞技": "20",
	}

	currencyEn := map[string]string{
		"2":   "MYR",
		"3":   "USD",
		"4":   "THB",
		"6":   "EUR",
		"12":  "GBP",
		"13":  "RMB",
		"15":  "IDR",
		"20":  "UUS",
		"32":  "JPY",
		"41":  "CHF",
		"42":  "PHP",
		"45":  "WON",
		"46":  "BND",
		"48":  "ZAR",
		"49":  "MXN",
		"50":  "CAD",
		"51":  "INH",
		"52":  "DKK",
		"53":  "SEK",
		"54":  "NOK",
		"55":  "RUB",
		"56":  "PLN",
		"57":  "CZK",
		"58":  "RON",
		"61":  "INR",
		"70":  "MMK",
		"71":  "KHR",
		"73":  "LIR",
		"79":  "KES",
		"80":  "GHS",
		"82":  "BRL",
		"83":  "CLP",
		"84":  "COP",
		"85":  "PEN",
		"86":  "ARS",
		"90":  "AED",
		"93":  "LAK",
		"96":  "USDT",
		"97":  "BDT",
		"121": "PKR",
		"122": "KZT",
		"123": "NPR",
		"124": "RMB2",
		"125": "INH2",
		"126": "TB2",
		"127": "INR2",
		"128": "PE2",
		"129": "IN2",
		"130": "Won2",
		"131": "RM2",
		"132": "US$2",
		"133": "TND",
		"134": "ZMW",
		"135": "TZS",
		"203": "JPY2",
		"204": "EUR2",
		"205": "BRL2",
		"206": "USDT2",
		"207": "NGN",
		"210": "DZD",
		"211": "MAD",
		"212": "FRF",
		"215": "BDT2",
		"216": "EGP",
		"217": "LBP",
	}

	gamesEn := map[string]string{
		"Soccer":           "足球",
		"Basketball":       "篮球",
		"Outright":         "冠军",
		"Tennis":           "网球",
		"Americanfootball": "美式橄榄球",
		"Cricket":          "板球",
		"Handball":         "手球",
		"Icehockey":        "冰球",
		"Snooker":          "斯诺克",
		"Volleyball":       "排球",
		"Badminton":        "羽毛球",
		"Multiple Parlay":  "过关",
		"Esport":           "电子竞技",
	}
	return &SabaService{
		url:                   temp["url"],
		walletToken:           temp["vendor_id"],
		vendorId:              temp["vendor_id"],
		operatorId:            temp["operator_id"],
		currency:              temp["currency"],
		platform:              temp["platform"],
		brandName:             "saba", //沙巴体育
		currencys:             currencyEn,
		games:                 games,
		games_en:              gamesEn,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

type SabaService struct {
	url                   string
	walletToken           string
	brandName             string
	vendorId              string
	operatorId            string
	currency              string
	platform              string
	currencys             map[string]string
	games                 map[string]string
	games_en              map[string]string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

var cacheKeySba = "cacheKeySaba:"

// 解压缩
func (l *SabaService) gunzip(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()
	// 读取解压缩后的数据
	uncompressed, err := ioutil.ReadAll(reader)
	if err != nil {
		return nil, err
	}
	return uncompressed, nil
}

// 用户登录
func (l *SabaService) login(userId string, platform string) (map[string]interface{}, error) {
	reqData := make(req.Param)
	reqData["vendor_id"] = l.vendorId
	reqData["vendor_member_id"] = userId
	reqData["platform"] = platform

	urlReq := l.url + "/GetSabaUrl"
	reqBytes, _ := json.Marshal(reqData)
	httpclient := httpc.DoRequest{
		UrlPath:    urlReq,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("saba login:", err)
		return nil, err
	}
	logs.Info("[Saba] login ==>", data)
	return data, nil
}

// 用户注册
func (l *SabaService) register(username string, userId int) (map[string]interface{}, error) {
	reqData := make(req.Param)
	reqData["vendor_id"] = l.vendorId    //token
	reqData["vendor_member_id"] = userId //用户id
	reqData["operatorid"] = l.operatorId
	reqData["username"] = username   //用户名等同用户id
	reqData["oddstype"] = 1          //赔率类型
	reqData["currency"] = l.currency //币种 测试环境设置为20
	reqData["maxtransfer"] = 999999  //最大转账金额
	reqData["mintransfer"] = 0       //最小转账金额
	urlReq := l.url + "/CreateMember"
	reqBytes, _ := json.Marshal(reqData)
	httpclient := httpc.DoRequest{
		UrlPath:    urlReq,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("saba register:", err)
		return nil, err
	}
	logs.Info("[saba] register ==>", data)
	return data, nil
}

// 沙巴体育登录
func (l *SabaService) SabaLoginAndRegister(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Lang     string
		Platform int
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("saba体育登录失败:", err)
		return
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeySba, token.UserId); err != nil { //判断用户状态是否正常
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("[saba] 体育登录，userId=", token.UserId)
	userId := strconv.Itoa(token.UserId)
	login, err := l.login(userId, l.platform) //先执行登录如果登录失败则注册用户
	if err != nil {
		logs.Error("saba体育登录失败:", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	userIP := ctx.GetIp()
	ot := getRegion(userIP)
	errorCodeFloat := login["error_code"].(float64) // 将类型更改为float64
	errorCode := int(errorCodeFloat)                // 将float64转换为int

	if errorCode == 0 { // 0:成功 2:会员不存在
		value := login["Data"]
		url := fmt.Sprintf("%s&langs=%s&OType=%d&forcedarkmode=true", value.(string), reqdata.Lang, ot)
		ctx.Put("url", url) //返回的游戏页面链接
		ctx.RespOK()
		logs.Info("[saba] 体育登录登录成功，userId=", token.UserId)
		return
	} else if errorCode == 2 { //如果用户不存在则注册用户
		logs.Info("[saba] 体育登录,用户不存在注册用户，userId=", token.UserId)
		register, err := l.register(userId, token.UserId)
		if err != nil {
			logs.Error("saba体育注册用户失败:userId=", userId, err)
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}

		errorCodeFloat = register["error_code"].(float64) // 将类型更改为float64
		errorCode = int(errorCodeFloat)                   // 将float64转换为int
		if errorCode == 0 || errorCode == 2 {             //0:成功 2:会员已存在 重新执行登录操作
			login, err := l.login(strconv.Itoa(token.UserId), l.platform)
			if err != nil {
				logs.Error("saba体育注册用户登录失败:userId=", userId, err)
				ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
				return
			}
			value := login["Data"]
			url := fmt.Sprintf("%s&lang=%s&OType=%d&forcedarkmode=true", value.(string), reqdata.Lang, ot)
			ctx.Put("url", url) //返回的游戏页面链接
			ctx.RespOK()
			return

		} else {
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
	} else {
		logs.Error("saba体育登录失败:", login)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
}

// GetBalance 余额请求
func (l *SabaService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Key     string `json:"key"`
		Message struct {
			Action string `json:"action"`
			UserId string `json:"userId"`
		} `json:"message"`
	}
	res := sabaResp{}
	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("数据解析失败:", err)
	}

	logs.Debug("saba GetBalance api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "参数错误！")
		logs.Error("saba GetBalance 数据解析错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba key不正确")
		return
	}

	currentDateString := utils.GetCurrentTime()
	userId_ := reqData.Message.UserId
	userId, _ := strconv.Atoi(userId_)
	_, balance, err := base.GetUserById(userId)
	if err != nil {
		l.respFail(ctx, "203", "查询用户错误！")
		ctx.RespJson(res)
		return
	}
	ctx.RespJson(map[string]interface{}{
		"status":    "0",
		"balance":   balance,
		"msg":       nil,
		"userId":    userId_,
		"balanceTs": currentDateString,
	})
}

// Message 订单消息体
type Message struct {
	Action          string  `json:"action"`           // PlaceBet
	OperationId     string  `json:"operationId"`      // 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
	UserId          string  `json:"userId"`           // 用户 id
	Currency        int     `json:"currency"`         // 沙巴体育货币币别 例如：1, 2, 20
	MatchId         int     `json:"matchId"`          // 例如：35627959
	HomeId          int     `json:"homeId"`           // 例如：23
	AwayId          int     `json:"awayId"`           // 例如：24
	HomeName        string  `json:"homeName"`         // 依据玩家的语系传入值。 例如：Chile (V)
	AwayName        string  `json:"awayName"`         // 依据玩家的语系传入值。例如：France (V)
	KickOffTime     string  `json:"kickOffTime"`      // 赛事开始时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	BetTime         string  `json:"betTime"`          // 下注时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	BetAmount       float64 `json:"betAmount"`        // 订单金额
	ActualAmount    float64 `json:"actualAmount"`     // 实际订单金额
	SportType       int     `json:"sportType"`        // 例如：1, 2, 3
	SportTypeName   string  `json:"sportTypeName"`    // 依据玩家的语系传入值。例如：Soccer
	BetType         int     `json:"betType"`          // 例如：1, 3
	BetTypeName     string  `json:"betTypeName"`      // 依据玩家的语系传入值。 例如：Handicap
	OddsType        int     `json:"oddsType"`         // 适用于 bettype = 8700(Player Tips)才会有值 例如：0,1, 2, 3, 4, 5
	OddsId          int     `json:"oddsId"`           // 例如：246903111
	Odds            float64 `json:"odds"`             // 例如：-0.95, 0.75
	UpdateTime      string  `json:"updateTime"`       // 更新时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	LeagueId        int     `json:"leagueId"`         // 例如：152765
	LeagueName      string  `json:"leagueName"`       // 依据玩家的语系传入值。 例如：SABA INTERNATIONAL FRIENDLY Virtual PES 20 – 20 Mins Play
	LeagueNameEn    string  `json:"leagueName_en"`    // 联赛名称的英文语系名称。 E.g. SABA INTERNATIONAL FRIENDLY Virtual PES 20 – 20 Mins Play
	SportTypeNameEn string  `json:"sportTypeName_en"` // 体育类型的英文语系名称。 E.g. Soccer
	BetTypeNameEn   string  `json:"betTypeName_en"`   // 投注类型的英文语系名称。 E.g. Handicap
	HomeNameEn      string  `json:"homeName_en"`      // 主队名称的英文语系名称。E.g. Chile (V)
	AwayNameEn      string  `json:"awayName_en"`      // 客队名称的英文语系名称。E.g. France (V)
	IP              string  `json:"IP"`               // 例如：************ (IPV4)
	IsLive          bool    `json:"isLive"`           // 例如：true, false
	RefId           string  `json:"refId"`            // 唯一 id.
	TsId            string  `json:"tsId"`             // 选填，用户登入会话 id，由商户提供
	Point           string  `json:"point"`            // 球头 在百练赛中(sporttype=161)表示下注时，前一颗
	AwayScore       int     `json:"awayScore"`        // 下注时客队得分。 在百练赛中(sporttype=161)表示已开出小于 37.5 的球数, l.g. 0
	IsBetCorrect    bool    `json:"isBetCorrect"`     // 若 bettype = 8700(Player Tips) 若是对的为 true，错的为 false
	IsWin           bool    `json:"isWin"`            // 若 bettype = 8700(Player Tips) 若是对的为 true，错的为 false
	IsSystemBet     bool    `json:"isSystemBet"`      // 是否为系统下注
	SystemBetId     string  `json:"systemBetId"`      // 系统下注唯一标示
	Outcome         string  `json:"outcome"`          // 下注结果
	BetAmountCNY    float64 `json:"betAmountCNY"`     // 人民币下注金额
	ActualAmountCNY float64 `json:"actualAmountCNY"`  // 实际人民币下注金额
	ResultAmountCNY float64 `json:"resultAmountCNY"`  // 人民币结果金额
	StartTime       string  `json:"startTime"`        // 赛事开始时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	EndTime         string  `json:"endTime"`          // 赛事结束时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	Status          int     `json:"status"`           // 例如：1, 2, 3
	CreateTime      string  `json:"createTime"`       // 创建时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
	Source          string  `json:"source"`           // 来源
	PlayerIp        string  `json:"playerIp"`         // 玩家 IP
	Platform        string  `json:"platform"`         // 平台
	IsTest          bool    `json:"isTest"`           // 是否为测试帐号
	CreditAmount    float64 `json:"CreditAmount"`     // 需增加在玩家的余额
	DebitAmount     float64 `json:"DebitAmount"`      // 需从玩家扣除的余额
}

type TicketDetail struct {
	MatchId         int     `json:"matchId"`         // 赛事 ID
	HomeId          int     `json:"homeId"`          // 主队 ID
	AwayId          int     `json:"awayId"`          // 客队 ID
	HomeName        string  `json:"homeName"`        // 主队名称
	AwayName        string  `json:"awayName"`        // 客队名称
	KickOffTime     string  `json:"kickOffTime"`     // 赛事开始时间
	SportType       int     `json:"sportType"`       // 体育类型
	SportTypeName   string  `json:"sportTypeName"`   // 体育类型名称
	BetType         int     `json:"betType"`         // 投注类型
	BetTypeName     string  `json:"betTypeName"`     // 投注类型名称
	OddsId          int     `json:"oddsId"`          // 赔率 ID
	Odds            float64 `json:"odds"`            // 赔率
	OddsType        int     `json:"oddsType"`        // 赔率类型
	BetChoice       string  `json:"betChoice"`       // 投注选择
	BetChoiceEN     string  `json:"betChoiceEN"`     // 投注选择英文名称
	LeagueId        int     `json:"leagueId"`        // 联赛 ID
	LeagueName      string  `json:"leagueName"`      // 联赛名称
	IsLive          bool    `json:"isLive"`          // 是否实时赛事
	Point           string  `json:"point"`           // 球头
	Point2          string  `json:"point2"`          // 球头2
	BetTeam         string  `json:"betTeam"`         // 下注对象
	HomeScore       int     `json:"homeScore"`       // 主队得分
	AwayScore       int     `json:"awayScore"`       // 客队得分
	BAStatus        bool    `json:"baStatus"`        // 会员是否为 BA 状态
	Excluding       string  `json:"excluding"`       // 排除字段
	LeagueNameEN    string  `json:"leagueNameEN"`    // 联赛名称英文
	SportTypeNameEN string  `json:"sportTypeNameEN"` // 体育类型英文名称
	HomeNameEN      string  `json:"homeNameEN"`      // 主队名称英文
	AwayNameEN      string  `json:"awayNameEN"`      // 客队名称英文
	BetTypeNameEN   string  `json:"betTypeNameEN"`   // 投注类型英文名称
	MatchDatetime   string  `json:"matchDatetime"`   // 开赛时间
	BetRemark       string  `json:"betRemark"`       // 投注备注
}

// Bet 下注 单条记录
func (l *SabaService) Bet(ctx *abugo.AbuHttpContent) {

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	//判断是否需要解压
	contentEncoding := ctx.Gin().Request.Header.Get("Content-Encoding")
	if contentEncoding == "gzip" {
		reqDataByte, err = l.gunzip(reqDataByte) //解压请求
		if err != nil {
			logs.Error("saba Bet 数据解压失败:", err)
		}
	}

	logs.Debug("saba Bet api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "参数错误！")
		logs.Error("saba Bet 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	userId_ := reqData.Message.UserId
	userId, _ := strconv.Atoi(userId_)

	user, balance, err := base.GetUserById(userId)
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			l.respFail(ctx, "201", "用户不存在")
		} else {
			l.respFail(ctx, "201", "查询用户余额失败！")
		}
		return
	}

	//三方来源的数据整理
	var (
		betAmount = reqData.Message.DebitAmount
		thirdId   = reqData.Message.RefId
		thirdTime = utils.GetCurrentTime()
	)
	gameCode := strconv.Itoa(reqData.Message.SportType) //三方游戏类型转换
	gameName := reqData.Message.SportTypeName           //l.games[gameCode]

	//保存订单详情
	resultCtx_, _ := json.Marshal(reqData.Message)
	resultCtx := l.convertLongToStr(string(resultCtx_))

	//游戏结果转中文
	gameBetZh := l.message2String(reqData.Message)
	//币种转换
	currency := l.currencys[strconv.Itoa(reqData.Message.Currency)]
	//判断用户余额
	if betAmount > balance {
		l.respFail(ctx, "502", "玩家余额不足！")
		logs.Error("saba Bet 玩家余额不足:userId=", userId, "balance=", balance)
		return
	}

	//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
	//获取投注渠道
	ChannelId := base.GetUserChannelId(ctx, user)
	tablePre := "x_third_sport_pre_order"
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询注单是否存在
		order := thirdGameModel.ThirdSportOrder{}
		e := tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e == nil {
			logs.Error("saba Bet 下注失败 注单已存在 thirdId=", thirdId, " order=", order)
			l.respFail(ctx, "1", "下注失败，订单已存在，重复投注！")
			return e
		}
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("saba Bet 下注失败 查询注单失败 thirdId=", thirdId, " err=", e.Error())
			l.respFail(ctx, "1", "下注失败，查询注单失败！")
			return e
		}

		//更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, betAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount-?", betAmount),
		})
		if resultTmp.Error != nil {
			logs.Error("saba Bet 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", resultTmp.Error)
			l.respFail(ctx, "901", "更新用户余额失败！")
			return resultTmp.Error
		}
		if resultTmp.RowsAffected <= 0 {
			d := errors.New("更新条数0")
			logs.Error("saba Bet 更新用户余额失败，更新条数0 userId=", userId, " thirdId=", thirdId, " e=", d.Error())
			l.respFail(ctx, "901", "更新用户余额失败！")
			return d
		}

		betTime := utils.ParseTime(reqData.Message.BetTime)
		order = thirdGameModel.ThirdSportOrder{
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gameCode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			BetTime:      betTime,
			BetLocalTime: utils.ParseTimeToLocal(reqData.Message.BetTime),
			Currency:     currency, //沙巴体育需要转换为真实的币种
			BetCtx:       gameBetZh,
			State:        1,
			DataState:    -1,
			BetCtxType:   3,
			CreateTime:   thirdTime,
			RawData:      resultCtx,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("saba Bet 创建订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "创建订单失败！")
			return e
		}
		//操作成功
		afterBalance := balance - betAmount
		//创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       0 - betAmount,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceCReasonSABABet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("saba Bet 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			l.respFail(ctx, "901", "创建账变失败！")
			return e
		}
		ctx.RespJson(map[string]interface{}{
			"status":       "0",
			"refId":        reqData.Message.RefId,
			"licenseeTxId": reqData.Message.RefId,
			"msg":          "",
		})
		logs.Info("[saba] Bet 下注成功 thirdId=", thirdId)
		return nil
	})

	//没有发生错误则发送账变通知
	if err == nil {
		// 推送下注事件到 CustomerIO
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, reqData.Message.SportTypeName, l.brandName, betAmount, l.currency)
		}

		// 发送余额变动通知
		go func(notifyUserId int) {
			v := l
			if v == nil || v.RefreshUserAmountFunc == nil {
				return
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][saba] Debit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					return
				}
			}
		}(userId)
	}
}

// BetParlay 串关下注
/**
1、下注：有多笔注单，单笔失败 注单全部回滚，返回失败。
2、结算有多笔注单时，单笔失败，不回滚全部注单，返回失败，你方系统继续调用Settle重试，我方继续结算未结算的注单。
3、confirmbet/confirmbetparlay只可能給玩家加錢, 不會再要扣錢,需要返回成功
*/
func (l *SabaService) BetParlay(ctx *abugo.AbuHttpContent) {

	type Txns struct {
		RefId        string  `json:"refId"`        // 唯一 id
		ParlayType   string  `json:"parlayType"`   // 例如：Parlay_Mix, Parlay_System, Parlay_Lucky, SingleBet_ViaLucky
		BetAmount    float64 `json:"betAmount"`    // 注单金额
		CreditAmount float64 `json:"creditAmount"` // 需增加在玩家的金额
		DebitAmount  float64 `json:"debitAmount"`  // 需从玩家扣除的金额
	}

	type ParlayDetail struct {
		Type     int     `json:"type"`     // 例如：0, 1, 2 (附录: 串关类型)
		Name     string  `json:"name"`     // 例如：Treble (1 Bet), Trixie (4 Bets) (附录: 串关类型)
		BetCount int     `json:"betCount"` // 例如：1, 3, 4
		Stake    float64 `json:"stake"`    // 输入注单金额
		Odds     float64 `json:"odds"`     // 只在 Parlay_Mix
	}

	type Message struct {
		Action         string         `json:"action"`         // PlaceBetParlay
		OperationId    string         `json:"operationId"`    // 唯一识别号，请勿对相同 operationId 的请求来做玩家钱包 的重复加款或扣款。
		UserId         string         `json:"userId"`         // 用户 id
		Currency       int            `json:"currency"`       // 沙巴体育货币币别 例如：1, 2, 20
		BetTime        string         `json:"betTime"`        // 下注时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
		UpdateTime     string         `json:"updateTime"`     // 更新时间 (yyyy-MM-dd HH:mm:ss.SSS) GMT-4
		TotalBetAmount float64        `json:"totalBetAmount"` // 总计注单金额 (for check balance)
		Ip             string         `json:"IP"`             // 例如：************ (IPV4)
		TsId           string         `json:"tsId"`           // 选填，用户登入会话 id，由商户提供
		BetFrom        string         `json:"betFrom"`        // 下注平台
		CreditAmount   float64        `json:"creditAmount"`   // 需增加在玩家的金额
		DebitAmount    float64        `json:"debitAmount"`    // 需从玩家扣除的金额
		VendorTransId  string         `json:"vendorTransId"`  // 只有透过数据源下注(direct API 和 odds feed API)才会回传此参数
		Txns           []Txns         `json:"txns"`           // 请参阅表 Combo Info
		TicketDetail   []TicketDetail `json:"ticketDetail"`   // 请参阅表 Ticket Detail
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	type Transaction struct {
		RefId        string `json:"refId"`
		LicenseeTxId int    `json:"licenseeTxId"`
	}

	type ResponseData struct { //请求结构体
		Status string        `json:"status"`
		Txns   []Transaction `json:"txns"`
	}
	reqData := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	//判断是否需要解压
	contentEncoding := ctx.Gin().Request.Header.Get("Content-Encoding")
	if contentEncoding == "gzip" {
		reqDataByte, err = l.gunzip(reqDataByte) //解压请求
		if err != nil {
			logs.Error("saba BetParlay 数据解压失败:", err)
		}
	}

	logs.Info("saba BetParlay api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "参数错误！")
		logs.Error("saba BetParlay 参数错误", err.Error())
		return
	}
	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	//记录串关投注数据
	_BetParlayCtx := l.getReqOrderInfo(string(reqDataByte))
	userId_ := reqData.Message.UserId
	userId, _ := strconv.Atoi(userId_)

	user, totalBalance, err := base.GetUserById(userId)
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			l.respFail(ctx, "201", "用户不存在")
		} else {
			l.respFail(ctx, "201", "查询用户余额失败！")
		}
		return
	}

	//三方来源的数据整理
	var (
		totalAmount = reqData.Message.TotalBetAmount
		thirdTime   = utils.GetCurrentTime()
	)

	//串关一笔订单对应多场赛事，拼接赛事体育类型
	uniqueSportTypeNames := make(map[string]struct{})
	var uniqueNames []string
	for _, detail := range reqData.Message.TicketDetail {
		if _, ok := uniqueSportTypeNames[detail.SportTypeName]; !ok {
			uniqueSportTypeNames[detail.SportTypeName] = struct{}{}
			uniqueNames = append(uniqueNames, detail.SportTypeName)
		}
	}
	// 去除重复值后的 TicketDetail.SportTypeName 值
	// 将 uniqueNames 切片重新转换为用逗号分隔的字符串
	uniqueSportTypeNamesString := strings.Join(uniqueNames, ",")
	gameCode := "串关"                       // strconv.Itoa((reqData.Message.SportType)) //三方游戏类型转换
	gameName := uniqueSportTypeNamesString // reqData.Message.SportTypeName             //l.games[gameCode]

	//币种转换
	currency := l.currencys[strconv.Itoa(reqData.Message.Currency)]
	//判断用户余额 是否够注单总的余额
	if totalAmount > totalBalance {
		l.respFail(ctx, "502", "玩家余额不足！")
		logs.Error("saba BetParlay 玩家余额不足:userId=", userId, "balance=", totalBalance)
		return
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
	tablePre := "x_third_sport_pre_order"
	trxResults := reqData.Message.Txns
	resData := ResponseData{}
	logs.Info("[saba] BetParlay 开始串关下注，注单总数：", len(trxResults))

	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		balance := totalBalance                //用户当前余额
		for _, trxResult := range trxResults { //下注：有多笔注单，单笔失败 注单全部回滚，返回失败
			thirdId := trxResult.RefId         //唯一ID,需要在confirm处改为三方txtId
			betAmount := trxResult.DebitAmount //下注金额
			//查询订单是否存在
			oldOrder := base.GetOrder(thirdId, l.brandName, "x_third_sport_pre_order")
			if oldOrder != nil {
				l.respFail(ctx, "1", "串关下注失败，订单已存在，重复投注！")
				logs.Error("saba BetParlay 串关下注失败，订单已存在，重复投注:userId=", userId, "thirdId=", thirdId)
				return errors.New("注单已存在")
			}

			//更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, betAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount-?", betAmount),
			})
			if resultTmp.Error != nil {
				logs.Error("saba BetParlay 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", resultTmp.Error)
				l.respFail(ctx, "901", "更新用户余额失败！")
				return resultTmp.Error
			}
			if resultTmp.RowsAffected <= 0 {
				d := errors.New("更新条数0")
				logs.Error("saba BetParlay 更新用户余额失败，更新条数0 userId=", userId, " thirdId=", thirdId, " e=", d.Error())
				l.respFail(ctx, "901", "更新用户余额失败！")
				return d
			}

			betTime := utils.ParseTime(reqData.Message.BetTime)
			order := thirdGameModel.ThirdSportOrder{
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameCode,
				GameName:     gameName,
				BetAmount:    betAmount,
				WinAmount:    0,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				BetTime:      betTime,
				BetLocalTime: utils.ParseTimeToLocal(reqData.Message.BetTime),
				Currency:     currency, //沙巴体育需要转换为真实的币种
				BetCtx:       _BetParlayCtx,
				RawData:      _BetParlayCtx,
				State:        1,
				DataState:    -1,
				BetCtxType:   3,
				CreateTime:   thirdTime,
			}
			var e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				logs.Error("saba BetParlay 创建订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				l.respFail(ctx, "901", "创建订单失败！")
				return e
			}
			//操作成功
			afterBalance := balance - betAmount
			//创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: balance,
				Amount:       0 - betAmount,
				AfterAmount:  afterBalance,
				Reason:       utils.BalanceCReasonSABABet,
				Memo:         l.brandName + " BetParlay,thirdId:" + thirdId,
				SellerId:     user.SellerId,
				ChannelId:    user.ChannelId,
				CreateTime:   thirdTime,
			}
			balance = afterBalance
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("saba BetParlay 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				l.respFail(ctx, "901", "创建账变失败！")
				return e
			}
			//串关需要将单号返回给三方
			resData.Txns = append(resData.Txns, Transaction{RefId: thirdId, LicenseeTxId: order.UserId})
			logs.Info("[saba] BetParlay 串关下注成功 thirdId=", thirdId)
		}
		return nil
	})

	//没有发生错误则发送账变通知
	if err == nil {
		// 发送余额变动通知
		go func(notifyUserId int) {
			v := l
			if v == nil || v.RefreshUserAmountFunc == nil {
				return
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][saba] Debit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					return
				}
			}
		}(userId)
		resData.Status = "0"
		ctx.RespJson(resData)
	}
	logs.Info("[saba] BetParlay 结束串关下注")
}

// ConfirmBetParlay   串关多笔确认投注
/**
  会出现多笔确认
  ConfirmBetParlay只會有加錢，不會有扣錢的操作
*/
func (l *SabaService) ConfirmBetParlay(ctx *abugo.AbuHttpContent) {

	type TicketDetail struct {
		MatchId   int     `json:"matchId"`   // 比赛id
		SportType int     `json:"sportType"` // 运动类型
		BetType   int     `json:"betType"`   // 投注类型
		OddsId    int     `json:"oddsId"`    // 赔率id
		Odds      float64 `json:"odds"`      // 赔率
		OddsType  int     `json:"oddsType"`  // 赔率类型
		LeagueId  int     `json:"leagueId"`  // 联赛id
		IsLive    bool    `json:"isLive"`    // 是否直播
	}

	type Txns struct {
		RefId         string  `json:"refId"`          // 参考id
		TxId          int     `json:"txId"`           // 交易id
		LicenseeTxId  string  `json:"licenseeTxId"`   // 商户系统交易id
		ActualAmount  float64 `json:"actualAmount"`   // 实际金额
		IsOddsChanged bool    `json:"isOddsChanged"`  // 赔率是否改变
		CreditAmount  float64 `json:"creditAmount"`   // 增加金额
		DebitAmount   float64 `json:"debitAmount"`    // 减少金额
		WinLostDate   string  `json:"winlostDate"`    // 胜负日期
		Odds          float64 `json:"odds,omitempty"` // 赔率
	}

	type Message struct {
		Action          string         `json:"action"`          // 行动
		OperationId     string         `json:"operationId"`     // 操作id
		UserId          string         `json:"userId"`          // 用户id
		UpdateTime      string         `json:"updateTime"`      // 更新时间
		CreditAmount    float64        `json:"creditAmount"`    // 信用金额
		DebitAmount     float64        `json:"debitAmount"`     // 借方金额
		Txns            []Txns         `json:"txns"`            // 交易信息
		TicketDetail    []TicketDetail `json:"ticketDetail"`    // 票据细节
		TransactionTime string         `json:"transactionTime"` // 交易时间
	}

	type RequestData struct { //请求结构体
		Key     string  `json:"key"`
		Message Message `json:"message"`
	}

	reqData := RequestData{}
	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	reqDataByte, err := l.gunzip(reqDataByte) //解压请求
	if err != nil {
		logs.Error("saba ConfirmBetParlay 数据解压失败:", err)
	}
	logs.Debug("saba configBet api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		l.respFail(ctx, "203", "序列化参数错误错误！")
		logs.Error("saba ConfirmBetParlay 参数错误", err.Error())
		return
	}

	authToken := reqData.Key
	if !l.checkToken(authToken) {
		l.respFail(ctx, "305", "密钥不正确！")
		logs.Error("saba 密钥不正确")
		return
	}

	trxResults := reqData.Message.Txns
	tablePre := "x_third_sport_pre_order"
	var failures = []map[string]interface{}{} //失败的注单
	var success []string                      //成功注单
	var retry []string                        //需要重试的注单 数据库操作失败的需要三方重试
	logs.Info("[saba] ConfirmBetParlay 开始确认串关注单，注单总数：", len(trxResults))
	for _, trxResult := range trxResults { //批量订单处理

		//creditAmount有值，就加錢 debitAmount有值就減錢
		//确认串关订单注单时需要给用户加钱
		amount := trxResult.CreditAmount
		thirdId := trxResult.RefId
		thirdTime := utils.GetCurrentTime()
		//保存订单详情
		//resultCtx_, _ := json.Marshal(trxResult)
		//resultCtx := l.convertLongToStr(string(resultCtx_))
		//logs.Debug("saba ConfirmBetParlay 订单详情 thirdId=", thirdId, "注单详情=", resultCtx)
		//开启事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			//查询订单是否存在
			order := thirdGameModel.ThirdSportOrder{}
			e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound {
					logs.Error("saba ConfirmBetParlay 订单不存在 thirdId=", thirdId, " e=", e.Error())
				} else {
					logs.Error("saba ConfirmBetParlay 查询订单错误 thirdId=", thirdId, " e=", e.Error())
				}
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "失败，查询订单错误",
				})
				return e
			}
			userId := order.UserId
			//绑定最新的订单号
			if order.DataState == 1 {
				logs.Error("saba ConfirmBetParlay 订单已处理 thirdId=", thirdId, " order=", order)
				failures = append(failures, map[string]interface{}{
					"refId": thirdId,
					"msg":   "失败，订单已处理",
				})
				//订单已经处理过 跳过
				return nil
			} else {
				//绑定新的订单号
				//取消投注/下注时保存的是refId ,因为三方还没生成txtId，三方对账是txtId，所以这里需要修改为三方对账是txtId
				logs.Info("[saba] ConfirmBetParlay 更新订单号 userId=", userId, " 原来thirdId=", trxResult.RefId, " 新的thirdId=", trxResult.TxId)
				thirdId = strconv.Itoa(trxResult.TxId)
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					//"RawData": resultCtx,
					"ThirdId": thirdId,
				})
				e = resultTmp.Error
				if e != nil {
					logs.Error("saba ConfirmBetParlay 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，更新订单失败",
					})
					retry = append(retry, thirdId)
					return e
				}
				//新订单号绑定到账变日志
				resultTmp = tx.Table("x_amount_change_log").Where("Memo=?", l.brandName+" bet,thirdId:"+trxResult.RefId).Updates(map[string]interface{}{
					"Memo": l.brandName + " bet,thirdId:" + thirdId,
				})
			}

			//处理结算 ConfirmBetParlay只會有加錢，不會有扣錢的操作>0才需要操作余额=0无需加钱，无需处理
			if amount > 0 {
				userBalance := thirdGameModel.UserBalance{}
				e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
				if e != nil {
					logs.Error("saba ConfirmBetParlay  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，查询用户余额失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount+?", amount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("saba ConfirmBetParlay 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，更新用户余额失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       amount,
					AfterAmount:  userBalance.Amount + amount,
					Reason:       utils.BalanceCReasonSABAConfirm,
					Memo:         l.brandName + " settle,old_thirdId:" + trxResult.RefId + " thirdId:" + thirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("saba ConfirmBetParlay 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					failures = append(failures, map[string]interface{}{
						"refId": thirdId,
						"msg":   "失败，创建账变失败",
					})
					retry = append(retry, thirdId)
					return e
				}

				// 发送余额变动通知
				go func(notifyUserId int) {
					if l.RefreshUserAmountFunc != nil {
						tmpErr := l.RefreshUserAmountFunc(notifyUserId)
						if tmpErr != nil {
							logs.Error("saba ConfirmBetParlay 发送余额变动通知错误", notifyUserId, tmpErr.Error())
						}
					} else {
						logs.Error("saba ConfirmBetParlay 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					}
				}(userId)
			}
			// 交易成功时记录订单号
			success = append(success, trxResult.RefId)
			logs.Info("[saba] ConfirmBetParlay 确认串关订单成功 thirdId=", thirdId)
			return nil
		})
	}

	l.respSucces(ctx)
	logs.Info("[saba] ConfirmBetParlay 结束确认串关订单，注单总数：", len(trxResults), "成功的注单数:", len(success), success, "失败的注单:", failures, "需要重试的注单:", retry)
}

func (l *SabaService) getBetCtxFromRowData(_rawData string) (_betCtx string) {
	// 拼接下注内容 开始
	_betCtx = _rawData
	// sport 游戏项目
	_betCtx = strings.ReplaceAll(_betCtx, "\"sport\"", "\"sport游戏项目\"")
	return
}

// 验证token
func (l *SabaService) checkToken(authToken string) bool {
	if l.walletToken == authToken {
		return true
	}
	return false
}

// 获取投注详情
func (l *SabaService) getReqOrderInfo(reqJson string) string {
	var data map[string]json.RawMessage
	err := json.Unmarshal([]byte(reqJson), &data)
	if err != nil {
		logs.Error("json解析注单详情失败", err.Error())
		return ""
	} else {
		messageJSON := data["message"]
		betInfo := string(messageJSON)
		return l.convertLongToStr(betInfo)
	}
}

// 获取投注详情
func (l *SabaService) convertLongToStr(reqJson string) string {
	//// 定义正则表达式模式，匹配 "txId": 数字过长前端没法正常显示
	re := regexp.MustCompile(`("txId":\s*)(\d+)`)
	// 使用正则替换，将数字替换为字符串
	modifiedJSON := re.ReplaceAllString(reqJson, "${1}\"$2\"")
	return modifiedJSON
}

// 根据IP返回国家和州信息
func getIPContinent(ipStr string) string {
	// 打开 MaxMind 数据库文件
	db, err := geoip2.Open("./config/GeoLite2-Country.mmdb")
	if err != nil {
		logs.Error("geoip2.Open", err)
		return ""
	}
	defer db.Close()

	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		logs.Error("Invalid IP address format")
		return ""
	}

	// 查询 IP 地址的国家和洲信息
	record, err := db.Country(ip)
	if err != nil {
		logs.Error("db.Country(ip)", err)
		return ""
	}
	// 返回洲信息
	return record.Continent.Names["zh-CN"]
}

// getRegion 根据 IP 地址返回对应的盘类
func getRegion(ipStr string) int {
	// 根据洲分配盘类
	continent := getIPContinent(ipStr)
	logs.Debug("continent", continent)
	// 亚洲地区全部改港赔、其他地区全部改欧赔
	switch continent {
	case "亚洲":
		return 2
	case "欧洲":
		return 1
	case "北美洲", "南美洲":
		return 5
	case "非洲", "南极洲", "大洋洲":
		return 1
	default:
		return 1
	}
}

// 响应给第三方结构体
// Balance float64 `json:"balance"`
type sabaResp struct {
	Status string `json:"status"`
	Msg    string `json:"msg"`
}

// 响应失败信息
func (l *SabaService) respFail(ctx *abugo.AbuHttpContent, code string, msg string) {
	resp := new(sabaResp)
	resp.Status = code
	resp.Msg = msg
	ctx.Gin().JSON(http.StatusOK, resp)
}

// 响应成功消息
func (l *SabaService) respSucces(ctx *abugo.AbuHttpContent) {
	resp := new(sabaResp)
	resp.Status = "0"
	//resp.Msg = msg
	ctx.Gin().JSON(http.StatusOK, resp)
}

// message2String 将Message转换为JSON字符串
func (s *SabaService) message2String(msg Message) string {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"操作类型\":\"%v\",", msg.Action))
	sb.WriteString(fmt.Sprintf("\"操作ID\":\"%v\",", msg.OperationId))
	sb.WriteString(fmt.Sprintf("\"用户ID\":\"%v\",", msg.UserId))
	sb.WriteString(fmt.Sprintf("\"币种\":%v,", msg.Currency))
	sb.WriteString(fmt.Sprintf("\"比赛ID\":%v,", msg.MatchId))
	sb.WriteString(fmt.Sprintf("\"主队ID\":%v,", msg.HomeId))
	sb.WriteString(fmt.Sprintf("\"客队ID\":%v,", msg.AwayId))
	sb.WriteString(fmt.Sprintf("\"主队名称\":\"%v\",", msg.HomeName))
	sb.WriteString(fmt.Sprintf("\"客队名称\":\"%v\",", msg.AwayName))
	sb.WriteString(fmt.Sprintf("\"开赛时间\":\"%v\",", msg.KickOffTime))
	sb.WriteString(fmt.Sprintf("\"下注时间\":\"%v\",", msg.BetTime))
	sb.WriteString(fmt.Sprintf("\"投注金额\":%v,", msg.BetAmount))
	sb.WriteString(fmt.Sprintf("\"实际投注金额\":%v,", msg.ActualAmount))
	sb.WriteString(fmt.Sprintf("\"体育类型\":%v,", msg.SportType))
	sb.WriteString(fmt.Sprintf("\"体育类型名称\":\"%v\",", msg.SportTypeName))
	sb.WriteString(fmt.Sprintf("\"投注类型\":%v,", msg.BetType))
	sb.WriteString(fmt.Sprintf("\"投注类型名称\":\"%v\",", msg.BetTypeName))
	sb.WriteString(fmt.Sprintf("\"赔率类型\":%v,", msg.OddsType))
	sb.WriteString(fmt.Sprintf("\"赔率ID\":%v,", msg.OddsId))
	sb.WriteString(fmt.Sprintf("\"赔率\":%v,", msg.Odds))
	sb.WriteString(fmt.Sprintf("\"更新时间\":\"%v\",", msg.UpdateTime))
	sb.WriteString(fmt.Sprintf("\"联赛ID\":%v,", msg.LeagueId))
	sb.WriteString(fmt.Sprintf("\"联赛名称\":\"%v\",", msg.LeagueName))
	sb.WriteString(fmt.Sprintf("\"联赛英文名称\":\"%v\",", msg.LeagueNameEn))
	sb.WriteString(fmt.Sprintf("\"体育类型英文名称\":\"%v\",", msg.SportTypeNameEn))
	sb.WriteString(fmt.Sprintf("\"投注类型英文名称\":\"%v\",", msg.BetTypeNameEn))
	sb.WriteString(fmt.Sprintf("\"主队英文名称\":\"%v\",", msg.HomeNameEn))
	sb.WriteString(fmt.Sprintf("\"客队英文名称\":\"%v\",", msg.AwayNameEn))
	sb.WriteString(fmt.Sprintf("\"IP地址\":\"%v\",", msg.IP))
	sb.WriteString(fmt.Sprintf("\"是否滚球\":%v,", msg.IsLive))
	sb.WriteString(fmt.Sprintf("\"注单ID\":\"%v\",", msg.RefId))
	sb.WriteString(fmt.Sprintf("\"会话ID\":\"%v\",", msg.TsId))
	sb.WriteString(fmt.Sprintf("\"球头\":\"%v\",", msg.Point))
	sb.WriteString(fmt.Sprintf("\"客队得分\":%v,", msg.AwayScore))
	sb.WriteString(fmt.Sprintf("\"投注是否正确\":%v,", msg.IsBetCorrect))
	sb.WriteString(fmt.Sprintf("\"是否获胜\":%v,", msg.IsWin))
	sb.WriteString(fmt.Sprintf("\"是否系统投注\":%v,", msg.IsSystemBet))
	sb.WriteString(fmt.Sprintf("\"系统投注ID\":\"%v\",", msg.SystemBetId))
	sb.WriteString(fmt.Sprintf("\"投注结果\":\"%v\",", msg.Outcome))
	sb.WriteString(fmt.Sprintf("\"人民币投注金额\":%v,", msg.BetAmountCNY))
	sb.WriteString(fmt.Sprintf("\"实际人民币投注金额\":%v,", msg.ActualAmountCNY))
	sb.WriteString(fmt.Sprintf("\"人民币结果金额\":%v,", msg.ResultAmountCNY))
	sb.WriteString(fmt.Sprintf("\"开始时间\":\"%v\",", msg.StartTime))
	sb.WriteString(fmt.Sprintf("\"结束时间\":\"%v\",", msg.EndTime))
	sb.WriteString(fmt.Sprintf("\"状态\":%v,", msg.Status))
	sb.WriteString(fmt.Sprintf("\"创建时间\":\"%v\",", msg.CreateTime))
	sb.WriteString(fmt.Sprintf("\"来源\":\"%v\",", msg.Source))
	sb.WriteString(fmt.Sprintf("\"玩家IP\":\"%v\",", msg.PlayerIp))
	sb.WriteString(fmt.Sprintf("\"平台\":\"%v\",", msg.Platform))
	sb.WriteString(fmt.Sprintf("\"是否测试\":%v,", msg.IsTest))
	sb.WriteString(fmt.Sprintf("\"增加余额\":%v,", msg.CreditAmount))
	sb.WriteString(fmt.Sprintf("\"扣除余额\":%v", msg.DebitAmount))
	sb.WriteString("}")
	return sb.String()
}

// ticketDetail2String 将TicketDetail转换为JSON字符串
func (s *SabaService) ticketDetail2String(ticket TicketDetail) string {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"比赛ID\":%v,", ticket.MatchId))
	sb.WriteString(fmt.Sprintf("\"主队ID\":%v,", ticket.HomeId))
	sb.WriteString(fmt.Sprintf("\"客队ID\":%v,", ticket.AwayId))
	sb.WriteString(fmt.Sprintf("\"主队名称\":\"%v\",", ticket.HomeName))
	sb.WriteString(fmt.Sprintf("\"客队名称\":\"%v\",", ticket.AwayName))
	sb.WriteString(fmt.Sprintf("\"开赛时间\":\"%v\",", ticket.KickOffTime))
	sb.WriteString(fmt.Sprintf("\"体育类型\":%v,", ticket.SportType))
	sb.WriteString(fmt.Sprintf("\"体育类型名称\":\"%v\",", ticket.SportTypeName))
	sb.WriteString(fmt.Sprintf("\"投注类型\":%v,", ticket.BetType))
	sb.WriteString(fmt.Sprintf("\"投注类型名称\":\"%v\",", ticket.BetTypeName))
	sb.WriteString(fmt.Sprintf("\"赔率ID\":%v,", ticket.OddsId))
	sb.WriteString(fmt.Sprintf("\"赔率\":%v,", ticket.Odds))
	sb.WriteString(fmt.Sprintf("\"赔率类型\":%v,", ticket.OddsType))
	sb.WriteString(fmt.Sprintf("\"投注选择\":\"%v\",", ticket.BetChoice))
	sb.WriteString(fmt.Sprintf("\"投注选择英文\":\"%v\",", ticket.BetChoiceEN))
	sb.WriteString(fmt.Sprintf("\"联赛ID\":%v,", ticket.LeagueId))
	sb.WriteString(fmt.Sprintf("\"联赛名称\":\"%v\",", ticket.LeagueName))
	sb.WriteString(fmt.Sprintf("\"是否滚球\":%v,", ticket.IsLive))
	sb.WriteString(fmt.Sprintf("\"球头\":\"%v\",", ticket.Point))
	sb.WriteString(fmt.Sprintf("\"球头2\":\"%v\",", ticket.Point2))
	sb.WriteString(fmt.Sprintf("\"下注对象\":\"%v\",", ticket.BetTeam))
	sb.WriteString(fmt.Sprintf("\"主队得分\":%v,", ticket.HomeScore))
	sb.WriteString(fmt.Sprintf("\"客队得分\":%v,", ticket.AwayScore))
	sb.WriteString(fmt.Sprintf("\"BA状态\":%v,", ticket.BAStatus))
	sb.WriteString(fmt.Sprintf("\"联赛英文名称\":\"%v\",", ticket.LeagueNameEN))
	sb.WriteString(fmt.Sprintf("\"体育类型英文名称\":\"%v\",", ticket.SportTypeNameEN))
	sb.WriteString(fmt.Sprintf("\"主队英文名称\":\"%v\",", ticket.HomeNameEN))
	sb.WriteString(fmt.Sprintf("\"客队英文名称\":\"%v\",", ticket.AwayNameEN))
	sb.WriteString(fmt.Sprintf("\"投注类型英文名称\":\"%v\",", ticket.BetTypeNameEN))
	sb.WriteString(fmt.Sprintf("\"开赛时间\":\"%v\",", ticket.MatchDatetime))
	sb.WriteString(fmt.Sprintf("\"投注备注\":\"%v\"", ticket.BetRemark))
	sb.WriteString("}")
	return sb.String()
}
