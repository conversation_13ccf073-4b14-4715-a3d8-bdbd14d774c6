package single

import (
	"bytes"
	"crypto/aes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// FB真人单一钱包类
// FB真人接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

type FBLiveSingleService struct {
	apiUrl                string            // api基础接口
	requestMd5Key         string            // 主动请求接口的商户MD5密钥
	callbackMd5Key        string            // 回调接口签名的MD5密钥
	merchantCode          string            // merchantCode商户号
	currency              string            //币种
	homeUrl               string            //跳转URL
	brandName             string            //厂商标识
	games                 map[string]string //游戏类型
	activitys             map[string]string //活动类型
	payoutTypes           map[string]string //派彩类型
	RefreshUserAmountFunc func(int) error   // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

func NewFBLiveSingleService(params map[string]string, fc func(int) error) *FBLiveSingleService {
	//游戏类型
	games := map[string]string{
		"BAC":       "经典百家乐",
		"BAC_LS":    "主播百家乐",
		"BAC_LS_SQ": "主播咪牌百家乐",
		"BAC_SP":    "极速百家乐",
		"BAC_LS_SP": "主播极速百家乐",
		"BAC_ON":    "经典包桌百家乐",
		"BAC_LS_ON": "主播包桌百家乐",
		"BAC_DT":    "赌场百家乐",
		"BAC_DT_CF": "赌场免佣百家乐",
		"DTB":       "经典龙虎",
		"DTB_LS":    "主播龙虎",
		"ROU_LS":    "主播美式轮盘",
		"NIU":       "经典牛牛",
		"NIU_LS":    "主播牛牛",
		"SIC":       "经典骰宝",
		"SIC_LS":    "主播骰宝",
		"LOT_SO":    "刮刮乐",
		"XBGS":      "扫雷高手",
		"INB_LS":    "主播射门",
		"GFLW":      "经典炸金花",
		"GFLW_LS":   "主播炸金花",
		"SD":        "经典色碟",
		"SD_LS":     "主播色碟",
		"BJ":        "经典21点",
		"SNIU":      "经典超牛",
		"SNIU_LS":   "主播超牛",
		"ANB":       "经典安达巴哈",
		"ANB_LS":    "主播安达巴哈",
		"FT":        "经典番摊",
		"FT_LS":     "主播番摊",
		"TT":        "经典三公",
		"TT_LS":     "主播三公",
	}

	//活动类型
	activityPayout := map[string]string{
		"DEDUCTION-GIFT":        "赠礼扣款",
		"DEDUCTION-POST":        "购买 FBfans 贴文",
		"DEDUCTION-SUBSTREAMER": "FBfans 订阅",
		"DEDUCTION-EFFECT":      "购买排场 (如坐驾、聊天室讯息装饰等)",
		"PAYOUT-WINRATE":        "胜率挑战",
		"PAYOUT-REDENVELOPE":    "抢红包",
		"ROLLBACK-GIFT":         "赠送礼物失败回滚",
		"ROLLBACK-POST":         "FBfans 购买贴文失败回滚",
		"ROLLBACK-SUBSTREAMER":  "FBfans 订阅失败回滚",
		"ROLLBACK-EFFECT":       "购买排场失败回滚",
	}

	//派彩类型
	payoutType := map[string]string{
		"CANCEL-ORDER":    "取消注单 (取消单一笔注单)",
		"CANCEL-ROUND":    "取消局 (整局取消)",
		"REPAYOUT":        "重算局 (整局结果重新计算)",
		"ADD-ORDER":       "新增注单 (系统补单)",
		"SYNC-ORDER-INFO": "更新注单 (更新注单资讯)",
		"PAYOUT":          "派彩",
	}

	return &FBLiveSingleService{
		apiUrl:                params["api_url"],          //API地址
		requestMd5Key:         params["request_md5_key"],  //	请求密钥
		callbackMd5Key:        params["callback_md5_key"], //回调密钥
		merchantCode:          params["merchant_code"],    //渠道号
		currency:              params["currency"],         //语系
		brandName:             "fblive",                   //厂商标识
		games:                 games,
		activitys:             activityPayout,
		payoutTypes:           payoutType,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyFBLive = "cacheKeyFBLive:"

// FBLive返回错误码
const (
	FBLive_Code_Success                 = 0     // 成功
	FBLive_Code_Fail_User_Not_Exist     = 1     // 	    会员不存在
	FBLive_Code_Fail_Not_Enough_Balance = 4     // 	    余额不足
	FBLive_Code_Fail_Signature_Error    = 2     // 	    签名验证失败
	FBLive_Code_Fail_Other_Error        = 99999 // 	    其他异常
	FBLive_Code_Fail_User_Account_Error = 100   // 	会员账号有误或冻结
	FBLive_Code_Fail_Logic_Error        = 99999 // 	逻辑业务异常
	FBLive_Code_Fail_Illegal_Parameter  = 3     // 	参数错误
	FBLive_Code_Fail_System_Error       = 99999 // 	其他系统错误
)

func (l *FBLiveSingleService) getLoginNameFromUserId(userId int64) string {
	return fmt.Sprintf("%s_%d", l.requestMd5Key, userId)
}

func (l *FBLiveSingleService) getUserIdFromLoginName(loginName string) (userId int64, err error) {
	userIdTmp := strings.Split(loginName, "_")
	if len(userIdTmp) != 2 {
		err = errors.New("会员账号格式错误")
		return
	}
	userId, err = strconv.ParseInt(userIdTmp[1], 10, 64)
	if err != nil {
		return
	}
	if !strings.EqualFold(loginName, l.getLoginNameFromUserId(userId)) {
		err = errors.New("会员账号格式错误")
		return
	}
	return
}

// 通过用户ID转换登录密码
func (l *FBLiveSingleService) getUserPwd(userId int) string {
	return base.MD5(fmt.Sprintf("%d", userId) + "_Hx_+-#&6")[:16]
}

// 会员离开桌台 /api/merchant/foreLeaveTable/v1
func (l *FBLiveSingleService) foreLeaveTable(userId int, tableId int64) (err error) {
	type RequestResetPwdParamsData struct {
		TableId   int64  `json:"tableId"`   // 当前桌台id
		LoginName string `json:"loginName"` // 用户名 需要包括商户的前缀。平台将对传入的完整账号统一转换成小写。注意：只能包含以下特殊字符[下划线、@、#、&、*  ]
		Timestamp int64  `json:"timestamp"` // 时间戳 当前时间，不足13位请补000
	}
	type RequestResetPwdData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}

	type ResponseResetPwdData struct {
		Code    int    `json:"code"`    // 返回码 200成功，其他失败
		Message string `json:"message"` // 返回信息
	}

	reqdata := RequestResetPwdData{
		MerchantCode: l.requestMd5Key,
	}
	reqParams := RequestResetPwdParamsData{
		TableId:   tableId,
		LoginName: l.getLoginNameFromUserId(int64(userId)),
		Timestamp: time.Now().UnixMilli(),
	}
	paramsBytes, _ := json.Marshal(reqParams)
	reqdata.Params, err = l.AESEncrypt([]byte(l.requestMd5Key), string(paramsBytes))
	if err != nil {
		logs.Error("FBLive_single 会员离开桌台 加密请求参数错误 userId=", userId, " err=", err.Error())
		return
	}
	reqdata.Signature = l.getRequestSign(string(paramsBytes))

	reqdataBytes, _ := json.Marshal(reqdata)
	payload := bytes.NewReader(reqdataBytes)
	url := fmt.Sprintf("%s/api/merchant/foreLeaveTable/v1", l.apiUrl)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("FBLive_single 会员离开桌台 请求错误 userId=", userId, " err=", err.Error())
		return
	}
	respBytes, err := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		logs.Error("FBLive_single 会员离开桌台 读取响应错误 userId=", userId, " err=", err.Error())
		return
	}
	logs.Info("FBLive_single 会员离开桌台 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respdata := ResponseResetPwdData{}
	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("FBLive_single 会员离开桌台 解析响应消息体错误 userId=", userId, " err=", err.Error())
		return
	}
	if respdata.Code == 20001 { // 游戏账号不存在
		logs.Error("FBLive_single 会员离开桌台失败 游戏账号不存在 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New("游戏账号不存在")
		return
	}
	if respdata.Code != FBLive_Code_Success {
		logs.Error("FBLive_single 会员离开桌台失败 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New(respdata.Message)
		return
	}
	return
}

// FBLiveSupportLang 支持的语言 简体中文cn,繁体中文tw,英文en,越南语vi,泰国语th,韩语kr
var FBLiveSupportLang = []string{"cn", "tw", "en", "vi", "th", "kr"}

// 支持的语言 1=zh-cn(简体中文) 2=zh-tw(䌓体中文） 3=en-us(英语) 4=euc-jp(日语) 5=ko(韩语) 6=th(泰文) 7=vi(越南文) 8=id(印尼语) 9=沙特阿拉伯语 10=德语 11=西班牙语 12=法语 13=俄语
// var FBLiveSupportLang2 = []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"}

// Login /api/v2/seamless/user-login
func (l *FBLiveSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestLoginParamsData struct {
		Username         string `json:"username"`           // 用户在游戏中的帐号 (限制长度为 1 至 24 字元, 且皆为小写英文或数字)
		Uuid             string `json:"uuid"`               // 自定义的唯一性请求序号
		Key              string `json:"key"`                // SHA256(username+lang+tels+平台密钥)
		PlatformID       string `json:"platform-id"`        // 平台方的产品ID
		DepositIframe    string `json:"deposit-iframe"`     // 点击存款时, 是否启用开启新页面, 预设为 0 不启用 (参考附录"Deposit-iframe 参数 ")
		DepositURL       string `json:"deposit-url"`        // 现金网存款网址, 选填
		EnterGameType    string `json:"enter-game-type"`    // 指定启动特定游戏类型 (参考附录"游戏类型"); 若带入之游戏类型不可使用 (如: 无桌台或维护中), 则预设进入游戏大厅
		EnterTable       string `json:"enter-table"`        // 指定启动特定桌台号; 若带入之桌台号不可使用 (如: 无桌台或维护中), 则预设进入游戏大厅
		ExtendSessionURL string `json:"extend-session-url"` // 现金网延长session 网址, 选填
		HomeURL          string `json:"home-url"`           // 现金网首页, 选填
		Lang             string `json:"lang"`               // 语系 (参考附录"语系"), 选填
		ServiceURL       string `json:"service-url"`        // 现金网客服首页, 选填
		Tels             string `json:"tels"`               // 客服联系电话, 多组电话以逗号隔开, 最多填写三组电话, 选填
		WithdrawURL      string `json:"withdraw-url"`       // 现金网取款网址, 选填
	}
	type RequestLoginData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}
	type ResponseLoginData struct {
		Error struct {
			Code int    `json:"code"` // 返回码 0成功，其他失败
			Msg  string `json:"msg"`
			Uuid string `json:"uuid"`
		} `json:"error"`
		RedirectURL string `json:"redirect-url"` // 游戏登陆地址
	}

	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("FBLive_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	loginLang := reqdata.LangCode
	//if reqdata.LangCode != "" {
	//	for _, v := range FBLiveSupportLang {
	//		if v == reqdata.LangCode {
	//			loginLang = reqdata.LangCode
	//			break
	//		}
	//	}
	//	if loginLang == "" {
	//		loginLang = "cn"
	//	}
	//} else {
	//	loginLang = "cn"
	//}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("FBLive_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyFBLive, userId); err != nil {
		logs.Error("FBLive_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("FBLive_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试2")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("FBLive_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("FBLive_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试4")
		return
	}

	loginHomeUrl := reqdata.HomeUrl
	loginName := strconv.Itoa(userId)
	reqParams := RequestLoginParamsData{
		Username:         loginName,
		PlatformID:       l.merchantCode,
		Key:              "",
		Uuid:             abugo.GetUuid(),
		DepositIframe:    "0",
		DepositURL:       "",
		EnterGameType:    reqdata.GameId,
		EnterTable:       "",
		ExtendSessionURL: "",
		HomeURL:          loginHomeUrl,
		Lang:             loginLang,
		ServiceURL:       "",
		Tels:             "",
		WithdrawURL:      "",
	}

	signKey := reqParams.Username + reqParams.Lang + reqParams.Tels
	reqParams.Key = l.getRequestSign(signKey)
	reqdataLoginBytes, _ := json.Marshal(reqParams)
	payload := bytes.NewReader(reqdataLoginBytes)
	logs.Info("FBLive_single 登录游戏 开始 userId=", userId, " reqdata=", string(reqdataLoginBytes))
	url := fmt.Sprintf("%s/api/v2/seamless/user-login", l.apiUrl)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("FBLive_single 登录游戏 请求错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "网络错误,请稍后再试6")
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("FBLive_single 登录游戏 读取响应错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}
	logs.Info("FBLive_single 登录游戏 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respData := ResponseLoginData{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		logs.Error("FBLive_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试8")
		return
	}

	if respData.Error.Code != FBLive_Code_Success {
		logs.Error("FBLive_single 登录游戏 登录失败 userId=", userId, " 错误码=", respData.Error.Code, " Message=", respData.Error.Msg)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试9")
		return
	}

	ctx.RespOK(respData.RedirectURL)
	return
}

// 计算签名字符串
func (l *FBLiveSingleService) getRequestSign(param string) (sign string) {
	src := param + l.requestMd5Key
	hash := sha256.New()
	hash.Write([]byte(src))
	sign = hex.EncodeToString(hash.Sum(nil))
	return
}

// 计算签名字符串
func (l *FBLiveSingleService) getCallbackSign(param string) (sign string) {
	src := param + l.callbackMd5Key
	hash := sha256.New()
	hash.Write([]byte(src))
	sign = hex.EncodeToString(hash.Sum(nil))
	return
}

// 生成FBLive token
func (l *FBLiveSingleService) getTokenByUser(userId int, account string) (token string) {
	type FBLiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyFBLive + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyFBLive + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("FBLive_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("FBLive_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "FBLive_" + uuid.NewString()
	tokendata := FBLiveTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyFBLive + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("FBLive_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("FBLive_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *FBLiveSingleService) getUserByToken(token string) (userId int, account string) {
	type FBLiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyFBLive + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := FBLiveTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("FBLive_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyFBLive + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("FBLive_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("FBLive_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("FBLive_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// GetHealth 心跳
func (l *FBLiveSingleService) GetHealth(ctx *abugo.AbuHttpContent) {
	ctx.RespOK()
	//logs.Info("FBLive_single 心跳检测 响应成功")
	return
}

// GetBalance 单个会员余额查询接口
// 应用场景：
// 1. 会员登录后需要拉取当前余额显示大厅。
// 2. 会员在下注、派彩等场景。
// 3. 其他场景。
func (l *FBLiveSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Currency string `json:"currency"` // 币种
			UserId   string `json:"user-id"`  // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "Success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 获取玩家余额 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 获取玩家余额 非法的商户编码 l.merchantCode=", l.merchantCode, " reqdata.MerchantCode=", reqdata.MerchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}
	signKey := reqdata.Params.UserId + reqdata.Params.Currency
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 获取玩家余额 签名错误 reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey), "signKey=", signKey)
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.Params.UserId)
	if err != nil {
		logs.Error("FBLive_single 获取玩家余额 会员账号错误 用户名转换错误 reqdataParam.UserId=", reqdata.Params.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Params.Currency, l.currency) {
		logs.Error("FBLive_single 获取玩家余额 非法的币种 reqdataParam.Currency=", reqdata.Params.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("FBLive_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.Code = FBLive_Code_Fail_User_Not_Exist
			respdata.Message = "会员不存在"
		} else {
			respdata.Code = FBLive_Code_Fail_System_Error
			respdata.Message = "查询会员余额失败"
		}
		ctx.RespJson(respdata)
		return
	}
	// 给Data赋值
	respdata.Data.Credit = userBalance.Amount
	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

// BetConfirm 下注确认回调接口
// 应用场景：
// 1. 会员在桌台内下注时，与运营商确认余额及扣减余额
// 2. 会员在多台内下注时，与运营商确认余额及扣减余额
func (l *FBLiveSingleService) BetConfirm(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Currency string `json:"currency"` // 币种
			Info     []struct {
				Amount        float64 `json:"amount"`         // 异动金额
				BetType       int     `json:"bet-type"`       // 玩法
				ClientIp      string  `json:"client-ip"`      // 客户端 ip
				GameType      string  `json:"game-type"`      // 游戏类型
				RoundCode     string  `json:"round-code"`     // 局号
				TableId       string  `json:"table-id"`       // 桌台编号
				Ticket        string  `json:"ticket"`         // 注单号
				TransactionId string  `json:"transaction-id"` // 下注唯一序号
			} `json:"info"`
			UserId string `json:"user-id"` // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "成功",
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 下注确认 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 下注确认 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 下注确认 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 下注确认 商户编码不正确 Uuid=", reqdata.Uuid, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}
	reqdataParam := reqdata.Params
	var concatenatedTransactionIds string
	for _, info := range reqdataParam.Info {
		concatenatedTransactionIds += info.TransactionId
	}
	signKey := reqdata.Params.UserId + reqdata.Params.Currency + concatenatedTransactionIds
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 下注确认 签名错误 Uuid=", reqdata.Uuid, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdataParam.UserId)
	if err != nil {
		logs.Error("FBLive_single 下注确认 会员账号错误 Uuid=", reqdata.Uuid, " reqdataParam.LoginName=", reqdataParam.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("FBLive_single 下注确认 非法的币种 Uuid=", reqdata.Uuid, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}
	if len(reqdataParam.Info) == 0 {
		logs.Error("FBLive_single 下注确认 下注明细为空 Uuid=", reqdata.Uuid, " BetInfo=", reqdataParam.Info)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "下注明细为空"
		ctx.RespJson(respdata)
		return
	}

	totalBetAmount := float64(0)
	for _, v := range reqdataParam.Info {
		if v.Amount < 0 {
			logs.Error("FBLive_single 下注确认 下注金额不能为负数 Uuid=", reqdata.Uuid, " BetAmount=", v.Amount, " Ticket=", v.Ticket)
			respdata.Code = FBLive_Code_Fail_Illegal_Parameter
			respdata.Message = "下注金额不能为负数"
			ctx.RespJson(respdata)
			return
		}
		totalBetAmount += v.Amount
	}

	// 按从小到大的下注金额排序
	sort.Slice(reqdataParam.Info, func(i, j int) bool {
		return reqdataParam.Info[i].Amount < reqdataParam.Info[j].Amount
	})

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId        //用户ID
	cacheSignKey.Brand = l.brandName    //三方厂商名称
	cacheSignKey.ThirdId = reqdata.Uuid //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.Uuid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.Uuid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	// 开始下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 下注确认 获取游戏名称失败 Uuid=", reqdata.Uuid, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respdata.Code = FBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			respdata.Data.Credit = userBalance.Amount
		}
		if totalBetAmount > userBalance.Amount {
			e = errors.New("余额不足")
			respdata.Code = FBLive_Code_Fail_Not_Enough_Balance
			respdata.Message = "余额不足"
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		for _, v := range reqdataParam.Info {
			gameId := fmt.Sprintf("%s", v.GameType)

			gameName := ""
			{
				gameList := thirdGameModel.GameList{}
				err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
				if err != nil {
					logs.Error("FBLive_single 下注确认 获取游戏列表失败 TransactionId=", v.TransactionId, " userId=", userId, " err=", err)
				} else {
					gameName = gameList.Name
				}
			}

			// 下注金额大于用户余额
			if v.Amount > userBalance.Amount {
				logs.Error("FBLive_single 下注确认 会员余额不足下注金额 终止后面的注单下注 TransactionId=", v.TransactionId, " v.Ticket=", v.Ticket, " v.BetAmount=", v.Amount, " userBalance.Amount=", userBalance.Amount)
				return nil
			}
			thirdId := fmt.Sprintf("%s", v.Ticket)
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    v.Amount,
				WinAmount:    0,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    -1, //未开奖
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				// 订单已存在不能跳过 否则 三方会认为下注成功，暂时修改为只允许下注一笔
				logs.Error("FBLive_single 下注确认 创建订单失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e)
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建订单失败"
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, v.Amount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", v.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("FBLive_single 下注确认 扣款失败 TransactionId=", v.TransactionId, " userId=", userId, " thirdId=", thirdId, " v.BetAmount=", v.Amount, " err=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "扣款失败"
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       -v.Amount,
				AfterAmount:  userBalance.Amount - v.Amount,
				Reason:       utils.BalanceCReasonFBLiveBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%s", v.TransactionId),
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("FBLive_single 下注确认 创建账变记录失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount -= v.Amount
			respdata.Data.Credit = userBalance.Amount

			// 推送下注事件通知
			if l.thirdGamePush != nil && v.Amount > 0 {
				l.thirdGamePush.PushBetEvent(userBalance.UserId, gameName, l.brandName, v.Amount, l.currency)
			}
		}
		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 下注确认 事务处理失败 err=", err)
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}
	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 下注确认 响应成功 respdata=", respdata)
	return
}

// 根据交易单号获取下注信息
func (l *FBLiveSingleService) getBetPayoutMapByTransferNo(transferNo int64) (res map[string]float64) {
	res = make(map[string]float64)

	type RequestBetInfo struct {
		BetId      int64   `json:"betId"`      // 下注ID
		BetPointId int64   `json:"betPointId"` // 下注玩法ID
		BetAmount  float64 `json:"betAmount"`  // 下注金额正数
	}
	type RequestParamData struct {
		TransferNo     int64            `json:"transferNo"`     // 交易单号
		GameTypeId     int64            `json:"gameTypeId"`     // 游戏类型ID
		RoundNo        string           `json:"roundNo"`        // 局号
		LoginName      string           `json:"loginName"`      // 会员账号
		BetTime        int64            `json:"betTime"`        // 下注时间 时间戳毫秒
		BetTotalAmount float64          `json:"betTotalAmount"` // 下注总金额正数
		Currency       string           `json:"currency"`       // 币种代码
		BetInfo        []RequestBetInfo `json:"betInfo"`        // 下注明细数组。一次下注可能包含多个玩法，多注。数组里面同玩法只会有一条。
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	thirdReqInfo := thirdGameModel.ThirdReqInfo{}
	err := server.Db().GormDao().Table("x_third_request_info").Select("ReqBody").Where("ReqId=? and Brand=?", strconv.FormatInt(transferNo, 10), l.brandName).First(&thirdReqInfo).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			logs.Error("FBLive_single getBetPayoutMapByTransferNo 交易单号不存在 transferNo=", transferNo)
		} else {
			logs.Error("FBLive_single getBetPayoutMapByTransferNo 查询错误 transferNo=", transferNo, " err=", err.Error())
		}
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal([]byte(thirdReqInfo.ReqBody), &reqdata)
	if err != nil {
		logs.Error("FBLive_single getBetPayoutMapByTransferNo 解析请求消息体错误 transferNo=", transferNo, " err=", err.Error())
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("FBLive_single getBetPayoutMapByTransferNo 解析请求参数params错误 transferNo=", transferNo, " err=", err.Error())
		return
	}

	for _, v := range reqdataParam.BetInfo {
		res[strconv.FormatInt(v.BetId, 10)] = v.BetAmount
	}
	return
}

// BetCancel betCancel 取消下注回调接口
// 应用场景：
// 1. 如果因《下注确认回调》时超时没有收到响应，将做失败处理，将触发此接口请求。
// 2. 收到运营商《下注确认回调》成功响应，但最终因系统问题导致下注失败，将触发此接口请求。
func (l *FBLiveSingleService) BetCancel(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Currency string `json:"currency"` // 币种
			Info     []struct {
				Amount              float64 `json:"amount"`                // 异动金额
				CancelTransactionId string  `json:"cancel-transaction-id"` // 要被取消的已下注序号, 对应 bet-confirm 的 transaction-id
				GameType            string  `json:"game-type"`             // 游戏类型
				RoundCode           string  `json:"round-code"`            // 局号
				Ticket              string  `json:"ticket"`                // 注单号
				TransactionId       string  `json:"transaction-id"`        // 下注唯一序号
			} `json:"info"`
			UserId string `json:"user-id"` // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "成功",
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 取消下注 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 取消下注 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 取消下注 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 取消下注 商户编码不正确 reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}
	reqdataParam := reqdata.Params
	var concatenatedTransactionIds string
	for _, info := range reqdataParam.Info {
		concatenatedTransactionIds += info.TransactionId
	}
	signKey := reqdata.Params.UserId + reqdata.Params.Currency + concatenatedTransactionIds
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 取消下注 签名错误 Uuid=", reqdata.Uuid, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdataParam.UserId)
	if err != nil {
		logs.Error("FBLive_single 取消下注 用户名转换错误 Uuid=", reqdata.Uuid, " reqdataParam.LoginName=", reqdataParam.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("FBLive_single 取消下注 非法的币种 Uuid=", reqdata.Uuid, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId        //用户ID
	cacheSignKey.Brand = l.brandName    //三方厂商名称
	cacheSignKey.ThirdId = reqdata.Uuid //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.Uuid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.Uuid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	// 开始取消下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 取消下注 获取用户余额失败 Uuid=", reqdata.Uuid, " userId=", userId, " err=", e)
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = FBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			respdata.Data.Credit = userBalance.Amount
		}

		for _, v := range reqdata.Params.Info {
			thirdId := v.Ticket
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Info("FBLive_single 取消下注 订单不存在 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					continue
				}
				logs.Error("FBLive_single 取消下注 查询订单失败 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询订单失败"
				return e
			}

			if order.DataState != -1 {
				e = errors.New("不能取消已结算订单")
				if order.DataState == 1 {
					logs.Error("FBLive_single 取消下注 不能取消已结算订单 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "注单已结算"
					return e
				} else if order.DataState == -2 {
					logs.Error("FBLive_single 取消下注 注单已取消 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "注单已取消"
					return e
				} else {
					logs.Error("FBLive_single 取消下注 注单状态异常 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "注单状态异常"
					e = errors.New("注单状态异常")
					return e
				}
			}

			if v.Amount != order.BetAmount {
				e = errors.New("取消金额与下注金额不一致")
				logs.Error("FBLive_single 取消下注 取消金额与下注金额不一致 Ticket=", v.Ticket, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_Logic_Error
				respdata.Message = "取消金额与下注金额不一致"
				return e
			}

			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"RawData":   string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("FBLive_single 取消下注 更新订单状态失败 Ticket=", v.Ticket, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "更新订单状态失败"
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", v.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("FBLive_single 取消下注 加款失败 Ticket=", v.Ticket, " userId=", userId, " thirdId=", thirdId, " BetAmount=", v.Amount, " err=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "加款失败"
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v.Amount,
				AfterAmount:  userBalance.Amount + v.Amount,
				Reason:       utils.BalanceCReasonFBLiveCancel,
				Memo:         l.brandName + " cancel,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%s", v.TransactionId),
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("FBLive_single 取消下注 创建账变记录失败 Ticket=", v.Ticket, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount += v.Amount
			respdata.Data.Credit = userBalance.Amount
		}

		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 取消下注 事务处理失败 Uuid=", reqdata.Uuid, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 取消下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 取消下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}
	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 取消下注 响应成功 Uuid=", reqdata.Uuid, " respdata=", respdata)
	return
}

// GamePayout 派彩回调接口
// 应用场景：
// 1. 游戏正常结算派彩的，需要通知增加余额；
// 2. 游戏跳局返回余额派彩的，需要通知增加余额；
// 3. 后台发起的取消局派彩的，需要通知增加/扣减余额；
// 4. 后台发起的重算局派彩的，需要通知增加/扣减余额；
// 接口：派彩/v1/game-payout
// 派彩类型：PAYOUT
//
// 接口：其余下注相关/v1/order-change
// 派彩类型：ADD-ORDER、CANCEL-ORDER、CANCEL-ROUND、REPAYOUT、SYNC-ORDER-INFO
//
// 接口：取消下注 /v1/bet-cancel
// 此接口无派彩类型，game-type是参考附录3.游戏类型并非派彩类型。
// 正数加钱 负数减钱
// 关于 ( SYNC-ORDER-INFO  )
// 1、正常派彩下，不会触发SYNC-ORDER-INFO，系统在完成正常的派彩流程后，所有数据都会自动结算并更新，无需额外的同步操作。
// 2、当注单的有效投注额出现异常（因投注数据异动导致的有效投注额变化）时，系统会自动触发 SYNC-ORDER-INFO 请求。这是为了确保在有效投注发生变动时
func (l *FBLiveSingleService) GamePayout(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Currency string          `json:"currency"` // 币种
			Info     []FBLiveBetInfo `json:"info"`
			UserId   string          `json:"user-id"` // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "成功",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 派彩 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 派彩 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 派彩 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 派彩 商户编码不正确 Uuid=", reqdata.Uuid, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}
	reqdataParam := reqdata.Params
	var concatenatedTransactionIds string
	for _, info := range reqdataParam.Info {
		concatenatedTransactionIds += info.TransactionId
	}
	signKey := reqdata.Params.UserId + reqdata.Params.Currency + concatenatedTransactionIds
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 派彩 签名错误 Uuid=", reqdata.Uuid, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdataParam.UserId)
	if err != nil {
		logs.Error("FBLive_single 派彩 会员账号错误 Uuid=", reqdata.Uuid, " reqdataParam.UserId=", reqdataParam.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("FBLive_single 派彩 非法的币种 Uuid=", reqdata.Uuid, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId        //用户ID
	cacheSignKey.Brand = l.brandName    //三方厂商名称
	cacheSignKey.ThirdId = reqdata.Uuid //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.Uuid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.Uuid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"
	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 派彩 获取用户余额失败 Uuid=", reqdata.Uuid, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = FBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			respdata.Data.Credit = userBalance.Amount
		}

		for _, v := range reqdataParam.Info {
			thirdId := v.Ticket
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("FBLive_single 派彩 订单不存在 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Data.Credit += v.Amount
					continue
				}
				logs.Error("FBLive_single 派彩 查询订单失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询订单失败"
				return e
			}

			betCtx := l.FBLiveGameRecord2Str(v, reqdataParam.UserId)
			if v.PayoutType == "PAYOUT" {
				if order.DataState != -1 {
					e = errors.New("订单已结算")
					logs.Error("FBLive_single 派彩 订单已结算 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "订单已结算"
					return e
				}

				// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
				validBet := math.Abs(v.Amount - order.BetAmount)
				if validBet > math.Abs(order.BetAmount) {
					validBet = math.Abs(order.BetAmount)
				}
				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState":  1,
					"ThirdTime":  thirdTime,
					"ValidBet":   validBet,
					"WinAmount":  v.Amount,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 派彩 更新订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				order.DataState = 1
				order.ThirdTime = thirdTime
				order.ValidBet = validBet
				order.WinAmount = v.Amount
				order.BetCtx = betCtx
				order.GameRst = betCtx
				order.BetCtxType = 3
				order.RawData = string(bodyBytes)
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("FBLive_single 派彩 创建正式表订单失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "创建订单失败"
					return e
				}

			} else {
				e = errors.New("非法的TransferType")
				logs.Error("FBLive_single 派彩 非法的TransferType TransactionId=", v.TransactionId, " TransferType=", v.PayoutType, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_Illegal_Parameter
				respdata.Message = "非法的TransferType"
				return e
			}
			if v.Amount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", v.Amount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Amount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("FBLive_single 派彩 加扣款失败 TransactionId=", v.TransactionId, " userId=", userId, " thirdId=", thirdId, " v=", v.Amount, " err=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "加扣款失败"
					return e
				}
			}
			//账变类型
			goldType := l.getGoldChangeType(v.PayoutType)
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v.Amount,
				AfterAmount:  userBalance.Amount + v.Amount,
				Reason:       goldType,
				Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%s", thirdId) + ",t:" + v.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("FBLive_single 派彩 创建账变记录失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount += v.Amount
			respdata.Data.Credit = userBalance.Amount

			// 推送派奖事件到 CustomerIO
			if l.thirdGamePush != nil {
				l.thirdGamePush.PushRewardEvent(5, l.brandName, thirdId)
			}
		}

		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 派彩 事务处理失败 Uuid=", reqdata.Uuid, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {

		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 派彩 响应成功 TransactionId=", reqdata.Uuid, " respdata=", respdata)
	return
}

// GameResettle 派彩重新结算回调接口
// 派彩类型：ADD-ORDER、CANCEL-ORDER、CANCEL-ROUND、REPAYOUT、SYNC-ORDER-INFO
// 下注开牌后即进入结算派彩，/v1/order-change会在游戏结算后才进行
func (l *FBLiveSingleService) GameResettle(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Currency string          `json:"currency"` // 币种
			Info     []FBLiveBetInfo `json:"info"`
			UserId   string          `json:"user-id"` // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "成功",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 重新派彩 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 重新派彩 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 重新派彩 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 重新派彩 商户编码不正确 Uuid=", reqdata.Uuid, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}
	reqdataParam := reqdata.Params
	var concatenatedTransactionIds string
	for _, info := range reqdataParam.Info {
		concatenatedTransactionIds += info.TransactionId
	}
	signKey := reqdata.Params.UserId + reqdata.Params.Currency + concatenatedTransactionIds
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 重新派彩 签名错误 Uuid=", reqdata.Uuid, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdataParam.UserId)
	if err != nil {
		logs.Error("FBLive_single 重新派彩 会员账号错误 Uuid=", reqdata.Uuid, " reqdataParam.UserId=", reqdataParam.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("FBLive_single 重新派彩 非法的币种 Uuid=", reqdata.Uuid, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId        //用户ID
	cacheSignKey.Brand = l.brandName    //三方厂商名称
	cacheSignKey.ThirdId = reqdata.Uuid //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.Uuid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.Uuid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"
	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 重新派彩 获取用户余额失败 Uuid=", reqdata.Uuid, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = FBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			respdata.Data.Credit = userBalance.Amount
		}

		for _, v := range reqdataParam.Info {
			thirdId := v.Ticket
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("FBLive_single 重新派彩 订单不存在 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Data.Credit += v.Amount
					continue
				}
				logs.Error("FBLive_single 重新派彩 查询订单失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询订单失败"
				return e
			}

			betCtx := l.FBLiveGameRecord2Str(v, reqdataParam.UserId)
			//1.重新结算后发起的ADD-ORDER注单会显示已结算。
			//2.新增的注单为已派彩，若该笔单号先前不存在会进行加注单
			if v.PayoutType == "ADD-ORDER" {
				if order.DataState != 1 {
					e = errors.New("订单不是已结算状态，不能重新结算")
					logs.Error("FBLive_single 重新派彩 订单已结算 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "订单已结算"
					return e
				}

				// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
				validBet := math.Abs(v.Amount - order.BetAmount)
				if validBet > math.Abs(order.BetAmount) {
					validBet = math.Abs(order.BetAmount)
				}
				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState":  1,
					"ThirdTime":  thirdTime,
					"ValidBet":   validBet,
					"WinAmount":  v.Amount,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				order.DataState = 1
				order.ThirdTime = thirdTime
				order.ValidBet = validBet
				order.WinAmount = v.Amount
				order.BetCtx = betCtx
				order.GameRst = betCtx
				order.BetCtxType = 3
				order.RawData = string(bodyBytes)
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 创建正式表订单失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "创建订单失败"
					return e
				}

			} else if v.PayoutType == "CANCEL-ORDER" || v.PayoutType == "CANCEL-ROUND" {
				if order.DataState != 1 {
					e = errors.New("订单未结算不能取消结算")
					logs.Error("FBLive_single 重新派彩 订单未结算不能取消结算 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "订单未结算不能取消结算"
					return e
				}

				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState":  -2,
					"ThirdTime":  thirdTime,
					"WinAmount":  daogorm.Expr("WinAmount + ?", v.Amount),
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新预设表订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"DataState":  -2,
					"ThirdTime":  thirdTime,
					"WinAmount":  daogorm.Expr("WinAmount + ?", v.Amount),
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新正式表订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

			} else if v.PayoutType == "REPAYOUT" { //重新结算
				if order.DataState != 1 {
					e = errors.New("订单未结算不能重新结算")
					logs.Error("FBLive_single 重新派彩 订单未结算不能重新结算 TransactionId=", v.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_Logic_Error
					respdata.Message = "订单未结算不能重新结算"
					return e
				}

				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"WinAmount":  daogorm.Expr("WinAmount + ?", v.Amount),
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新预设表订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"WinAmount":  daogorm.Expr("WinAmount + ?", v.Amount),
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

			} else if v.PayoutType == "SYNC-ORDER-INFO" { //更新已存在注单的资讯 (如有效投注金额), 故注单号会与原始注单号一致且异动金额必为零
				// 更新注单数据
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新预设表订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("FBLive_single 重新派彩 更新订单状态失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

			} else {
				e = errors.New("非法的TransferType")
				logs.Error("FBLive_single 重新派彩 非法的TransferType TransactionId=", v.TransactionId, " TransferType=", v.PayoutType, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_Illegal_Parameter
				respdata.Message = "非法的TransferType"
				return e
			}
			if v.Amount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", v.Amount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Amount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("FBLive_single 重新派彩 加扣款失败 TransactionId=", v.TransactionId, " userId=", userId, " thirdId=", thirdId, " v=", v.Amount, " err=", e.Error())
					respdata.Code = FBLive_Code_Fail_System_Error
					respdata.Message = "加扣款失败"
					return e
				}
			}
			//账变类型
			goldType := l.getGoldChangeType(v.PayoutType)
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v.Amount,
				AfterAmount:  userBalance.Amount + v.Amount,
				Reason:       goldType,
				Memo:         l.brandName + " settle,thirdId:" + thirdId + ",t:" + v.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("FBLive_single 重新派彩 创建账变记录失败 TransactionId=", v.TransactionId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount += v.Amount
			respdata.Data.Credit = userBalance.Amount
		}

		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 重新派彩 事务处理失败 Uuid=", reqdata.Uuid, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 重新派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 重新派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 重新派彩 响应成功 TransactionId=", reqdata.Uuid, " respdata=", respdata)
	return
}

// ActivityPayout 活动和小费类回调接口
// 应用场景：打赏主播
// DEDUCTION-GIFT 赠礼扣款
// DEDUCTION-POST 购买 FBfans 贴文
// DEDUCTION-SUBSTREAMER FBfans 订阅
// DEDUCTION-EFFECT 购买排场 (如坐驾、聊天室讯息装饰等)
// PAYOUT-WINRATE 胜率挑战
// PAYOUT-REDENVELOPE 抢红包
// ROLLBACK-GIFT 赠送礼物失败回滚
// ROLLBACK-POST FBfans 购买贴文失败回滚
// ROLLBACK-SUBSTREAMER FBfans 订阅失败回滚
// ROLLBACK-EFFECT 购买排场失败回滚
func (l *FBLiveSingleService) ActivityPayout(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		MerchantCode string `json:"merchant-code"` // 渠道号
		Params       struct {
			Amount              float64 `json:"amount"`                // 异动金额, 扣钱为负值
			CancelTransactionId string  `json:"cancel-transaction-id"` // 在 type-info 为 rollback 时, 要 rollback 的 transaction-id
			ClientIP            string  `json:"client-ip"`             // 客户端 ip
			Currency            string  `json:"currency"`              // 币种
			Ticket              string  `json:"ticket"`                // 在 type-info 为 deduction/payout 时的消费/返利单号
			TransactionId       string  `json:"transaction-id"`        // 交易唯一id
			TypeInfo            string  `json:"type-info"`             // 类型资讯 (参考附录"Activity-payout 类型")
			UserId              string  `json:"user-id"`               // 玩家帐号
		} `json:"params"`
		Signature string `json:"signature"` // 验证码, 对 params 和钱包金钥 hash sha256 的结果
		Timestamp int64  `json:"timestamp"` // UNIX time, 毫秒
		Uuid      string `json:"uuid"`      // 自定义的唯一性请求序号
	}

	type ResponseData struct {
		Code int `json:"code"` // 错误代码
		Data struct {
			Credit    float64 `json:"credit"`    // 玩家余额
			Timestamp int64   `json:"timestamp"` // 执行完成时间, UNIX epoch, 毫秒
			UserId    string  `json:"user-id"`   // 玩家帐号
		} `json:"data"`
		Message string `json:"message"` // 错误讯息
		Uuid    string `json:"uuid"`    // 自定义的唯一性请求序号
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Message: "成功",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 活动和小费 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 活动和小费 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 活动和小费 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 活动和小费 商户编码不正确 TransactionId=", reqdata.Params.TransactionId, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := reqdata.Params
	var transactionId = reqdataParam.TransactionId
	signKey := reqdata.Params.UserId + reqdata.Params.Currency + transactionId
	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(signKey)) {
		logs.Error("FBLive_single 活动和小费 签名错误 TransactionId=", reqdataParam.TransactionId, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(signKey))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdataParam.UserId)
	if err != nil {
		logs.Error("FBLive_single 活动和小费 用户名转换错误 TransactionId=", reqdataParam.TransactionId, " reqdataParam.LoginName=", reqdataParam.UserId, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Not_Exist
		respdata.Message = "会员不存在"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("FBLive_single 活动和小费 非法的币种 TransactionId=", reqdataParam.TransactionId, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	validTypes := []string{"DEDUCTION-GIFT", "DEDUCTION-POST", "DEDUCTION-SUBSTREAMER", "DEDUCTION-EFFECT",
		"PAYOUT-WINRATE", "PAYOUT-REDENVELOPE", "ROLLBACK-GIFT", "ROLLBACK-POST", "ROLLBACK-SUBSTREAMER", "ROLLBACK-EFFECT"}
	if strings.Index(strings.Join(validTypes, ","), reqdataParam.TypeInfo) == -1 {
		logs.Error("FBLive_single 活动和小费 非法的PayoutType TransactionId=", reqdataParam.TransactionId, " reqdataParam.PayoutType=", reqdataParam.TypeInfo)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "非法的PayoutType"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	// 开始活动和小费事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 活动和小费 获取用户余额失败 TransactionId=", reqdataParam.TransactionId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = FBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		}

		ChannelId := userBalance.ChannelId

		if userBalance.Amount+reqdataParam.Amount < 0 {
			logs.Error("FBLive_single 活动和小费 余额不足 TransactionId=", reqdataParam.TransactionId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdataParam.PayoutAmount=", reqdataParam.Amount)
			respdata.Code = FBLive_Code_Fail_Not_Enough_Balance
			respdata.Message = "余额不足"
			return e
		}
		respdata.Data.Credit = userBalance.Amount

		thirdId := fmt.Sprintf("%v", reqdataParam.TransactionId)
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("FBLive_single 活动和小费 查询订单失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " error=", e.Error())
			respdata.Code = FBLive_Code_Fail_System_Error
			respdata.Message = "查询订单失败"
			return e
		}

		if e != nil {
			validTypes := []string{"ROLLBACK-GIFT", "ROLLBACK-POST", "ROLLBACK-SUBSTREAMER", "ROLLBACK-EFFECT"}
			if strings.Index(strings.Join(validTypes, ","), reqdataParam.TypeInfo) != -1 { //如果是回滚订单
				logs.Error("FBLive_single 活动和小费 回滚订单不存在 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_Logic_Error
				respdata.Message = "回滚订单不存在"
				return e
			}
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       "activity_" + reqdataParam.TypeInfo,
				GameName:     "activity_" + l.activitys[reqdataParam.TypeInfo],
				BetAmount:    0,
				WinAmount:    reqdataParam.Amount,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    1,
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				logs.Error("FBLive_single 活动和小费 创建预设表订单失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建订单失败"
				return e
			}

			order.Id = 0
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("FBLive_single 活动和小费 创建正式表订单失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "创建正式表订单失败"
				return e
			}
		} else {
			validTypes := []string{"ROLLBACK-GIFT", "ROLLBACK-POST", "ROLLBACK-SUBSTREAMER", "ROLLBACK-EFFECT"}
			if strings.Index(strings.Join(validTypes, ","), reqdataParam.TypeInfo) == -1 { //如果不是回滚订单
				e = errors.New("订单已存在")
				logs.Error("FBLive_single 活动和小费 订单已存在 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " order=", order)
				respdata.Code = FBLive_Code_Fail_Logic_Error
				respdata.Message = "订单已存在"
				return e
			}

			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"WinAmount": daogorm.Expr("WinAmount + ?", reqdataParam.Amount),
			}).Error
			if e != nil {
				logs.Error("FBLive_single 活动和小费 回滚预设订单失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "回滚订单失败"
				return e
			}

			e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"WinAmount": daogorm.Expr("WinAmount + ?", reqdataParam.Amount),
			}).Error
			if e != nil {
				logs.Error("FBLive_single 活动和小费 回滚正式订单失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = FBLive_Code_Fail_System_Error
				respdata.Message = "回滚订单失败"
				return e
			}
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", reqdataParam.Amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdataParam.Amount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("FBLive_single 活动和小费 加扣款失败 TransactionId=", reqdataParam.TransactionId, " userId=", userId, " thirdId=", thirdId, " PayoutAmount=", reqdataParam.Amount, " err=", e.Error())
			respdata.Code = FBLive_Code_Fail_System_Error
			respdata.Message = "加扣款失败"
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdataParam.Amount,
			AfterAmount:  userBalance.Amount + reqdataParam.Amount,
			Reason:       utils.BalanceCReasonFBLiveTip,
			Memo:         l.brandName + " tip,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%d", reqdataParam.TransactionId) + ",t:" + reqdataParam.TypeInfo,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("FBLive_single 活动和小费 创建账变记录失败 TransactionId=", reqdataParam.TransactionId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
			respdata.Code = FBLive_Code_Fail_System_Error
			respdata.Message = "创建账变记录失败"
			return e
		}
		respdata.Data.Credit = userBalance.Amount + reqdataParam.Amount
		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 活动和小费 事务处理失败 TransactionId=", reqdataParam.TransactionId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 活动和小费 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 活动和小费 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Data.Timestamp = time.Now().UnixMilli()
	respdata.Data.UserId = reqdata.Params.UserId
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 活动和小费 响应成功 TransactionId=", reqdataParam.TransactionId, " respdata=", respdata)
	return
}

type FBLiveBetInfo struct {
	Amount        float64 `json:"amount"`         // 异动金额 (同 payout-amount)
	BetAmount     float64 `json:"bet-amount"`     // 投注金额
	BetType       int     `json:"bet-type"`       // 玩法
	BillTime      int64   `json:"bill-time"`      // 下注时间 (UNIX timestamp, 秒)
	ClientIP      string  `json:"client-ip"`      // 客户端 ip
	Detail        string  `json:"detail"`         // 结算结果
	GameType      string  `json:"game-type"`      // 游戏类型
	PayoutAmount  float64 `json:"payout-amount"`  // 派彩金额 (包含本金)
	PayoutType    string  `json:"payout-type"`    // 派彩类型
	ReckonTime    int64   `json:"reckon-time"`    // 结算时间 (UNIX timestamp, 秒)
	RoundCode     string  `json:"round-code"`     // 局号
	TableId       string  `json:"table-id"`       // 桌台编号
	Ticket        string  `json:"ticket"`         // 注单号
	TransactionId string  `json:"transaction-id"` // 下注唯一序号
	ValidAccount  float64 `json:"valid-account"`  // 有效投注金额
}

func (l *FBLiveSingleService) FBLiveGameRecord2Str(data FBLiveBetInfo, userId string) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单编号\":\"%s\",", data.Ticket))
	sb.WriteString(fmt.Sprintf("\"玩家账号\":\"%s\",", userId))
	sb.WriteString(fmt.Sprintf("\"投注额\":%g,", data.BetAmount))
	sb.WriteString(fmt.Sprintf("\"有效投注额\":%g,", data.ValidAccount))
	sb.WriteString(fmt.Sprintf("\"返奖金额\":%g,", data.Amount))
	sb.WriteString(fmt.Sprintf("\"输赢金额\":%g,", data.PayoutAmount))
	sb.WriteString(fmt.Sprintf("\"游戏类型ID\":\"%s\",", data.GameType))
	sb.WriteString(fmt.Sprintf("\"游戏名称\":\"%s\",", l.games[data.GameType]))
	sb.WriteString(fmt.Sprintf("\"玩法ID\":\"%d\",", data.BetType))
	sb.WriteString(fmt.Sprintf("\"局号\":\"%s\",", data.RoundCode))
	sb.WriteString(fmt.Sprintf("\"桌台编号\":\"%s\",", data.TableId))
	sb.WriteString(fmt.Sprintf("\"游戏结果\":%s,", data.Detail))
	sb.WriteString(fmt.Sprintf("\"派彩类型\":\"%s\",", data.PayoutType))
	sb.WriteString(fmt.Sprintf("\"派彩名称\":\"%s\"", l.payoutTypes[data.PayoutType]))
	sb.WriteString("}")
	res = sb.String()
	return
}

type FBLiveRequestBettingRecordList struct {
	Id             int64   `json:"id"`             // 注单编号
	PlayerId       int64   `json:"playerId"`       // 玩家编号
	PlayerName     string  `json:"playerName"`     // 玩家账号
	AgentId        int64   `json:"agentId"`        // 代理编号
	BetAmount      float64 `json:"betAmount"`      // 投注额
	ValidBetAmount float64 `json:"validBetAmount"` // 有效投注额
	NetAmount      float64 `json:"netAmount"`      // 输赢金额
	BeforeAmount   float64 `json:"beforeAmount"`   // 下注前余额
	GameTypeId     int64   `json:"gameTypeId"`     // 游戏类型
	PlatformId     int64   `json:"platformId"`     // 厅id
	PlatformName   string  `json:"platformName"`   // 厅名称
	BetStatus      int64   `json:"betStatus"`      // 下注状态 0=未结算 1=已结算
	BetFlag        int64   `json:"betFlag"`        // 重算标志 0=正常结算，2=取消指定局的结算，3=取消该注单的结算 4=重算指定局 5=重算指定注单
	BetPointId     int64   `json:"betPointId"`     // 玩法，下注点
	JudgeResult    string  `json:"judgeResult"`    // 结果
	Currency       string  `json:"currency"`       // 币种
	TableCode      string  `json:"tableCode"`      // 台桌号
	RoundNo        string  `json:"roundNo"`        // 局号
	BootNo         string  `json:"bootNo"`         // 靴号
	LoginIp        string  `json:"loginIp"`        // 游戏ip
	DeviceType     int64   `json:"deviceType"`     // 设备类型 1=网页，2=手机网页，3=Ios，4=Android，5=其他设备
	DeviceId       string  `json:"deviceId"`       // 设备id
	RecordType     int64   `json:"recordType"`     // 注单类别 0、试玩 1、正式  2、内部测试  3、机器人。 只有为1记录的才会返回给商户
	GameMode       int64   `json:"gameMode"`       // 游戏模式 0=常规 1=好路 3=多台
	NickName       string  `json:"nickName"`       // 会员昵称
	DealerName     string  `json:"dealerName"`     // 荷官昵称
	TableName      string  `json:"tableName"`      // 游戏桌台名称
	AgentCode      string  `json:"agentCode"`      // 代理code
	AgentName      string  `json:"agentName"`      // 代理名称
	BetPointName   string  `json:"betPointName"`   // 玩法名称
	GameTypeName   string  `json:"gameTypeName"`   // 游戏名称
	PayAmount      float64 `json:"payAmount"`      // 返奖金额 同派彩额，只是取消局和跳局会记录为投注额
}

func (l *FBLiveSingleService) FBLiveGameRecord2String(data FBLiveRequestBettingRecordList) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单编号\":%d,", data.Id))
	sb.WriteString(fmt.Sprintf("\"玩家账号\":\"%s\",", data.PlayerName))
	sb.WriteString(fmt.Sprintf("\"投注额\":%g,", data.BetAmount))
	sb.WriteString(fmt.Sprintf("\"有效投注额\":%g,", data.ValidBetAmount))
	sb.WriteString(fmt.Sprintf("\"返奖金额\":%g,", data.PayAmount))
	sb.WriteString(fmt.Sprintf("\"输赢金额\":%g,", data.NetAmount))
	sb.WriteString(fmt.Sprintf("\"游戏类型ID\":%d,", data.GameTypeId))
	sb.WriteString(fmt.Sprintf("\"游戏名称\":\"%s\",", data.GameTypeName))
	sb.WriteString(fmt.Sprintf("\"玩法ID\":%d,", data.BetPointId))
	sb.WriteString(fmt.Sprintf("\"玩法名称\":\"%s\",", data.BetPointName))
	sb.WriteString(fmt.Sprintf("\"厅名ID\":%d,", data.PlatformId))
	sb.WriteString(fmt.Sprintf("\"厅名称\":\"%s\",", data.PlatformName))
	sb.WriteString(fmt.Sprintf("\"结果\":\"%s\",", data.JudgeResult))
	if data.BetStatus == 0 {
		sb.WriteString("\"下注状态\":\"未结算\",")
	} else if data.BetStatus == 1 {
		sb.WriteString("\"下注状态\":\"已结算\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"下注状态\":\"%d\",", data.BetStatus))
	}
	if data.BetFlag == 0 {
		sb.WriteString("\"重算标志\":\"正常结算\",")
	} else if data.BetFlag == 2 {
		sb.WriteString("\"重算标志\":\"取消指定局的结算\",")
	} else if data.BetFlag == 3 {
		sb.WriteString("\"重算标志\":\"取消该注单的结算\",")
	} else if data.BetFlag == 4 {
		sb.WriteString("\"重算标志\":\"重算指定局\",")
	} else if data.BetFlag == 5 {
		sb.WriteString("\"重算标志\":\"重算指定注单\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"重算标志\":\"%d\",", data.BetFlag))
	}
	sb.WriteString(fmt.Sprintf("\"币种\":\"%s\",", data.Currency))
	sb.WriteString(fmt.Sprintf("\"台桌号\":\"%s\",", data.TableCode))
	sb.WriteString(fmt.Sprintf("\"局号\":\"%s\",", data.RoundNo))
	sb.WriteString(fmt.Sprintf("\"游戏IP\":\"%s\",", data.LoginIp))
	if data.GameMode == 0 {
		sb.WriteString("\"游戏模式\":\"常规\",")
	} else if data.GameMode == 1 {
		sb.WriteString("\"游戏模式\":\"好路\",")
	} else if data.GameMode == 3 {
		sb.WriteString("\"游戏模式\":\"多台\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"游戏模式\":\"%d\",", data.GameMode))
	}
	sb.WriteString(fmt.Sprintf("\"荷官昵称\":\"%s\",", data.DealerName))
	sb.WriteString(fmt.Sprintf("\"游戏桌台名称\":\"%s\"", data.TableName))
	sb.WriteString("}")
	res = sb.String()
	return
}

// Playerbetting 玩家下注推送接口
// 应用场景：
// 1. 游戏正常结算派彩的，需要通知下注记录；
// 2. 游戏跳局返回余额派彩的，需要通知下注记录；
// 3. 后台发起的取消局派彩的，需要通知下注记录；
// 4. 后台发起的重算局派彩的，需要通知下注记录；
// 5. 以上场景会主动推送注单数据给商户
// 已经问过三方了，是注单推送接口，不需要处理金额加减
func (l *FBLiveSingleService) Playerbetting(ctx *abugo.AbuHttpContent) {
	type RequestChangePayout struct {
		TransferNo   int64   `json:"transferNo"`   // 交易单号
		TransferType string  `json:"transferType"` // 单号类型
		GameTypeId   int64   `json:"gameTypeId"`   // 游戏类型
		RoundNo      string  `json:"roundNo"`      // 局号
		PlayerId     int64   `json:"playerId"`     // 玩家编号
		LoginName    string  `json:"loginName"`    // 会员账号
		PayoutTime   int64   `json:"payoutTime"`   // 派彩时间
		PayoutAmount float64 `json:"payoutAmount"` // 派彩金额
		Currency     string  `json:"currency"`     // 币种
	}
	type RequestParamData struct {
		ChangePayout      RequestChangePayout              `json:"changePayout"`
		BettingRecordList []FBLiveRequestBettingRecordList `json:"bettingRecordList"`
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		MerchantCode string `json:"merchantCode"` // 代理编码
	}
	type ResponseData struct {
		Code      int    `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 玩家下注推送 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	//logs.Info("FBLive_single 玩家下注推送 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 玩家下注推送 解析请求消息体错误 err=", err.Error(), "reqdata=", string(bodyBytes))
		respdata.Code = FBLive_Code_Fail_Other_Error
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 玩家下注推送 商户编码不正确 TransactionId=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("FBLive_single 玩家下注推送 签名错误 TransactionId=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params), "reqdata=", string(bodyBytes))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	var reqdataParam []RequestParamData
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("FBLive_single 玩家下注推送 解析请求参数params错误 TransactionId=", reqdata.TransferNo, " err=", err.Error(), "reqdata=", string(bodyBytes))
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}
	/*
		reqdataParam := RequestParamData{}
		err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
		if err != nil {
			logs.Error("FBLive_single 玩家下注推送 解析请求参数params错误 TransactionId=", reqdata.TransferNo, " err=", err.Error())
			respdata.Code = FBLive_Code_Fail_Illegal_Parameter
			respdata.Message = "json反序列化请求params错误"
			ctx.RespJson(respdata)
			return
		}*/

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	for _, data := range reqdataParam {
		for _, v := range data.BettingRecordList {
			if v.BetStatus != 1 { // 只更新已结算的
				continue
			}
			userId, err := l.getUserIdFromLoginName(v.PlayerName)
			if err != nil {
				logs.Error("FBLive_single 玩家下注推送 用户名转换错误 TransactionId=", reqdata.TransferNo, " v.PlayerName=", v.PlayerName, "v=", v, " err=", err.Error())
				continue
			}

			betCtx := l.FBLiveGameRecord2String(v)

			err = server.Db().GormDao().Table(tablePre).Where("Brand=? and ThirdId=? and UserId=?", l.brandName, fmt.Sprintf("%d", v.Id), userId).Updates(map[string]interface{}{
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
			}).Error
			if err != nil {
				logs.Error("FBLive_single 玩家下注推送 更新预设表失败 TransactionId=", reqdata.TransferNo, " v.Id=", v.Id, " userId=", userId, "v=", v, " error=", err.Error())
			}

			err = server.Db().GormDao().Table(table).Where("Brand=? and ThirdId=? and UserId=?", l.brandName, fmt.Sprintf("%d", v.Id), userId).Updates(map[string]interface{}{
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
			}).Error
			if err != nil {
				logs.Error("FBLive_single 玩家下注推送 更新正式表失败 TransactionId=", reqdata.TransferNo, " v.Id=", v.Id, " userId=", userId, "v=", v, " error=", err.Error())
			}
		}
	}

	params := ResponseParamData{
		MerchantCode: l.merchantCode,
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 玩家下注推送 响应成功 respdata=", respdata)
	return
}

// activityRebate  返利活动推送接口
// 应用场景：
// 1. 百家乐参与返利活动正常结算，需要通知增加返利金额。
// 2. 百家乐参与红包雨活动正常结算，需要通知增加返利金额。
// 3. 以上场景会推送活动彩金数据给商户；
func (l *FBLiveSingleService) ActivityRebate(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		DetailId     int64   `json:"detailId"`     // 明细表ID
		ActivityType int64   `json:"activityType"` // 活动类型
		AgentId      int64   `json:"agentId"`      // 代理编号
		AgentCode    string  `json:"agentCode"`    // 代理编码
		PlayerId     int64   `json:"playerId"`     // 玩家编号
		LoginName    string  `json:"loginName"`    // 玩家账号
		ActivityId   int64   `json:"activityId"`   // 活动ID
		ActivityName string  `json:"activityName"` // 活动名称
		CreatedTime  string  `json:"createdTime"`  // 创建时间
		RewardAmount float64 `json:"rewardAmount"` // 获取金额
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		MerchantCode string `json:"merchantCode"` // 代理编码
	}
	type ResponseData struct {
		Code      int    `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    FBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("FBLive_single 返利活动 读取请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("FBLive_single 返利活动 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("FBLive_single 返利活动 解析请求消息体错误 err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("FBLive_single 返利活动 商户编码不正确 TransactionId=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("FBLive_single 返利活动 签名错误 TransactionId=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = FBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("FBLive_single 返利活动 解析请求参数params错误 TransactionId=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("FBLive_single 返利活动 用户名转换错误 TransactionId=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = FBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	thirdId := fmt.Sprintf("reward_%v", reqdataParam.DetailId)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

	params := ResponseParamData{
		MerchantCode: l.merchantCode,
	}
	// 开始返利活动事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("FBLive_single 返利活动 获取用户余额失败 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		//获取投注渠道
		ChannelId := userBalance.ChannelId
		// 创建注单
		order := thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userBalance.UserId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       "reward_" + fmt.Sprintf("%d", reqdataParam.ActivityId),
			GameName:     reqdataParam.ActivityName,
			BetAmount:    0,
			WinAmount:    reqdataParam.RewardAmount,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			Fee:          0,
			DataState:    1,
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			// 检查是否是 MySQL 重复键值错误
			if strings.Contains(e.Error(), "Error 1062") || strings.Contains(e.Error(), "Duplicate entry") || errors.Is(e, daogorm.ErrDuplicatedKey) {
				logs.Error("FBLive_single 返利活动 预设表订单已存在 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
				return e
			}
			logs.Error("FBLive_single 返利活动 创建预设表订单失败 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
			return e
		}
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			// 检查是否是 MySQL 重复键值错误
			if strings.Contains(e.Error(), "Error 1062") || strings.Contains(e.Error(), "Duplicate entry") || errors.Is(e, daogorm.ErrDuplicatedKey) {
				logs.Error("FBLive_single 返利活动 正式表订单已存在 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
				return e
			}
			logs.Error("FBLive_single 返利活动 创建正式表订单失败 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", reqdataParam.RewardAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdataParam.RewardAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("FBLive_single 返利活动 加款失败 TransactionId=", reqdata.TransferNo, " userId=", userId, " thirdId=", thirdId, " RewardAmount=", reqdataParam.RewardAmount, " err=", e.Error())
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdataParam.RewardAmount,
			AfterAmount:  userBalance.Amount + reqdataParam.RewardAmount,
			Reason:       utils.BalanceCReasonFBLiveReward,
			Memo:         l.brandName + " reward,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("FBLive_single 返利活动 创建账变记录失败 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " err=", e.Error())
			return e
		}
		return nil
	})

	if err != nil {
		logs.Error("FBLive_single 返利活动 事务处理失败 TransactionId=", reqdata.TransferNo, " thirdId=", thirdId, " err=", err.Error())
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][FBLive_single] 返利活动 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][FBLive_single] 返利活动 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("FBLive_single 返利活动 响应成功 TransactionId=", reqdata.TransferNo, " reqdata=", reqdata, "respdata=", respdata)
	return
}

// 数据AES加密函数
func (l *FBLiveSingleService) AESEncrypt(key []byte, plaintext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	plainBytes := []byte(plaintext)
	// 对于ECB模式，直接使用cipher.NewCBCEncrypter即可
	//if len(plainBytes)%aes.BlockSize != 0 {
	plainBytes = l.AESPad(plainBytes, aes.BlockSize)
	//}
	ciphertext := make([]byte, len(plainBytes))
	for start := 0; start < len(plainBytes); start += aes.BlockSize {
		block.Encrypt(ciphertext[start:start+aes.BlockSize], plainBytes[start:start+aes.BlockSize])
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 数据AES解密函数
func (l *FBLiveSingleService) AESDecrypt(key []byte, ciphertext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext := make([]byte, len(cipherBytes))
	// 对于ECB模式，直接使用cipher.NewCBCDecrypter即可
	for start := 0; start < len(cipherBytes); start += aes.BlockSize {
		block.Decrypt(plaintext[start:start+aes.BlockSize], cipherBytes[start:start+aes.BlockSize])
	}
	return string(l.AESUnpad(plaintext)), nil
}

// pad 使用PKCS7填充
func (l *FBLiveSingleService) AESPad(buf []byte, blockSize int) []byte {
	padding := blockSize - (len(buf) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(buf, padText...)
}

// unpad 移除PKCS7填充
func (l *FBLiveSingleService) AESUnpad(buf []byte) []byte {
	length := len(buf)
	if length == 0 {
		return buf
	}
	padding := int(buf[length-1])
	return buf[:length-padding]
}

// 返回账变类型
func (l *FBLiveSingleService) getGoldChangeType(payoutType string) (goldType int) {
	goldType = 0
	switch payoutType {
	case "CANCEL-ORDER":
		goldType = utils.BalanceCReasonFBLiveCANCELORDER
	case "CANCEL-ROUND":
		goldType = utils.BalanceCReasonFBLiveCANCEROUND
	case "REPAYOUT":
		goldType = utils.BalanceCReasonFBLiveREPAYOUT
	case "ADD-ORDER":
		goldType = utils.BalanceCReasonFBLiveADDORDER
	case "PAYOUT":
		goldType = utils.BalanceCReasonFBLiveSettle

	}
	return
}
