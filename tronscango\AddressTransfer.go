package tronscango

import (
	"encoding/json"
	"errors"
)

func (clt *Client) AddressTransfer_GetList(req AddressTransferGetListReq) (list []AddressTransferGetListResData, err error) {
	url := clt.baseUrl + "/api/AddressTransfer_GetList"
	resp, err := clt.httpClient.R().SetBody(req).Post(url)
	if err != nil {
		return nil, err
	}
	res := AddressTransferGetListRes{}
	err = json.Unmarshal(resp.Body(), &res)
	if err != nil {
		return nil, err
	}
	if res.Code != 0 {
		return nil, errors.New(res.Msg)
	}
	return res.Data, err
}
