// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveLogUserSign = "x_active_log_user_sign"

// XActiveLogUserSign 用户活动签到记录
type XActiveLogUserSign struct {
	RecordTime    time.Time `gorm:"column:RecordTime;primaryKey;default:CURRENT_TIMESTAMP;comment:记录时间" json:"RecordTime"`               // 记录时间
	RecordID      int64     `gorm:"column:RecordId;primaryKey;autoIncrement:true;comment:记录Id" json:"RecordId"`                          // 记录Id
	SellerID      int32     `gorm:"column:SellerId;comment:运营商Id" json:"SellerId"`                                                       // 运营商Id
	ChannelID     int32     `gorm:"column:ChannelId;comment:渠道Id" json:"ChannelId"`                                                      // 渠道Id
	UserID        int32     `gorm:"column:UserId;not null;comment:用户id" json:"UserId"`                                                   // 用户id
	ActiveID      int32     `gorm:"column:ActiveId;comment:活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎" json:"ActiveId"` // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	LiuShui       float64   `gorm:"column:LiuShui;default:0.000000;comment:游戏流水" json:"LiuShui"`                                         // 游戏流水
	RewardAmount  float64   `gorm:"column:RewardAmount;default:0.000000;comment:签到奖励金额" json:"RewardAmount"`                             // 签到奖励金额
	FirstSignTime time.Time `gorm:"column:FirstSignTime;comment:首次签到时间" json:"FirstSignTime"`                                            // 首次签到时间
	LastSignTime  time.Time `gorm:"column:LastSignTime;comment:最后签到时间" json:"LastSignTime"`                                              // 最后签到时间
	Config        string    `gorm:"column:Config;comment:活动配置" json:"Config"`                                                            // 活动配置
	BaseConfig    string    `gorm:"column:BaseConfig;comment:基础配置" json:"BaseConfig"`                                                    // 基础配置
	Memo          string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                                  // 备注
}

// TableName XActiveLogUserSign's table name
func (*XActiveLogUserSign) TableName() string {
	return TableNameXActiveLogUserSign
}
