// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXCustomThird = "x_custom_third"

// XCustomThird mapped from table <x_custom_third>
type XCustomThird struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增id" json:"Id"`             // 自增id
	RecordDate   time.Time `gorm:"column:RecordDate;comment:记录时间" json:"RecordDate"`                           // 记录时间
	UserID       int32     `gorm:"column:UserId;comment:玩家id" json:"UserId"`                                   // 玩家id
	SpecialAgent int32     `gorm:"column:SpecialAgent;default:2;comment:玩家来源 1独立代理,2公司官网" json:"SpecialAgent"` // 玩家来源 1独立代理,2公司官网
	TopAgentID   int32     `gorm:"column:TopAgentId;comment:顶级id" json:"TopAgentId"`                           // 顶级id
	SellerID     int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                // 运营商
	ChannelID    int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                               // 渠道
	BetChannelID int32     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"`                     // 下注渠道Id
	/*
		三方类型
		1 lottery
		2 dianzhi
		3 qipai
		4 xiaoyouxi
		5 live
		6 sport

	*/
	ThirdType      int32   `gorm:"column:ThirdType;comment:三方类型\n1 lottery\n2 dianzhi\n3 qipai\n4 xiaoyouxi\n5 live\n6 sport\n" json:"ThirdType"`
	Brand          string  `gorm:"column:Brand;comment:三方品牌" json:"Brand"`                                         // 三方品牌
	Symbol         string  `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                         // 币种
	BetCount       int32   `gorm:"column:BetCount;comment:投注次数" json:"BetCount"`                                   // 投注次数
	WinCount       int32   `gorm:"column:WinCount;comment:赢次数" json:"WinCount"`                                    // 赢次数
	BetAmount      float64 `gorm:"column:BetAmount;default:0.000000;comment:投注金额" json:"BetAmount"`                // 投注金额
	BonusBetAmount float64 `gorm:"column:BonusBetAmount;default:0.000000;comment:Bonus投注金额" json:"BonusBetAmount"` // Bonus投注金额
	WinAmount      float64 `gorm:"column:WinAmount;default:0.000000;comment:返奖金额" json:"WinAmount"`                // 返奖金额
	BonusWinAmount float64 `gorm:"column:BonusWinAmount;default:0.000000;comment:Bonus返奖金额" json:"BonusWinAmount"` // Bonus返奖金额
	LiuSui         float64 `gorm:"column:LiuSui;default:0.000000;comment:有效投注" json:"LiuSui"`                      // 有效投注
	FirstLiuSui    float64 `gorm:"column:FirstLiuSui;default:0.000000;comment:扣减前有效投注" json:"FirstLiuSui"`         // 扣减前有效投注
	ValidBetAmount float64 `gorm:"column:ValidBetAmount;default:0.000000;comment:有效投注" json:"ValidBetAmount"`      // 有效投注
	Fee            float64 `gorm:"column:Fee;default:0.000000;comment:手续费" json:"Fee"`                             // 手续费
	IsFirst        int32   `gorm:"column:IsFirst;default:2;comment:是否首次参与 1是,2否" json:"IsFirst"`                   // 是否首次参与 1是,2否
	CSGroup        string  `gorm:"column:CSGroup" json:"CSGroup"`
	GameName       string  `gorm:"column:GameName" json:"GameName"`
	GameID         string  `gorm:"column:GameId;comment:游戏Id" json:"GameId"` // 游戏Id
}

// TableName XCustomThird's table name
func (*XCustomThird) TableName() string {
	return TableNameXCustomThird
}
