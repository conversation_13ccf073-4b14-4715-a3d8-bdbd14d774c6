// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOnlineGameUser = "x_online_game_user"

// XOnlineGameUser mapped from table <x_online_game_user>
type XOnlineGameUser struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Online     int32     `gorm:"column:Online;not null;comment:在线人数" json:"Online"`                                   // 在线人数
	GameID     string    `gorm:"column:GameId;not null;comment:游戏ID" json:"GameId"`                                   // 游戏ID
	Brand      string    `gorm:"column:Brand;not null;comment:游戏厂商" json:"Brand"`                                     // 游戏厂商
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"CreateTime"` // 记录时间
}

// TableName XOnlineGameUser's table name
func (*XOnlineGameUser) TableName() string {
	return TableNameXOnlineGameUser
}
