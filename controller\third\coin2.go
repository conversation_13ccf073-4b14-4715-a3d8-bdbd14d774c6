package third

import (
	"encoding/json"
	"xserver/server"
	"xserver/utils"
)

func NewCoin2Service(configValue string, fc func(int) error) *Coin2Service {
	coin2 := Coin2Service{}
	json.Unmarshal([]byte(configValue), &coin2)
	coin2.BrandName = "coin2"
	coin2.LoginPath = "/coin/api/login"
	coin2.OrderType = "quwei"
	coin2.BalanceCReasonBet = utils.BalanceCReasonCoin2Bet
	coin2.BalanceCReasonCancel = utils.BalanceCReasonCoin2Cancel
	coin2.BalanceCReasonWin = utils.BalanceCReasonCoin2Win
	coin2.RefreshUserAmountFunc = fc
	return &coin2
}

func (e *Coin2Service) BindRouter() {
	server.Http().Post("/api/third/coin2_login", e.Login)
	server.Http().PostNoAuth("/api/coin2/Balance/LockBalance", e.Bet)
	server.Http().PostNoAuth("/api/coin2/Balance/UnLockBalance", e.End)
	server.Http().PostNoAuth("/api/coin2/Balance/GetBalance", e.Balance)
}

type Coin2Service struct {
	WuShuangService
}
