package paycontroller

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var RMPay = new(rmpay)

type rmpay struct {
	Base
}

// 充值请求参数
type RmPayRechargeRequest struct {
	MerchantID   string  `json:"merchantId"`   // 商户ID
	UserID       string  `json:"userId"`       // 用户唯一标识
	OrderID      string  `json:"orderId"`      // 商户订单号
	OrderTime    string  `json:"orderTime"`    // 订单创建时间
	TerminalType string  `json:"terminalType"` // 终端类型
	Account      string  `json:"account"`      // 银行卡号
	Amount       float64 `json:"amount"`       // 金额
	Payer        string  `json:"payer"`        // 付款人姓名
	Remark       string  `json:"remark"`       // 备注
	PayWith      int     `json:"payWith"`      // 支付方式
	UseCounter   bool    `json:"useCounter"`   // 是否使用收银台
	NotifyURL    string  `json:"notifyUrl"`    // 回调地址
	Label        string  `json:"label"`        // 订单标签
	Nonce        string  `json:"nonce"`        // 随机字符串
	Timestamp    string  `json:"timestamp"`    // 时间戳
}

// 充值响应参数
type RMPayRechargeResponse struct {
	Code                    int     `json:"code"`                    // API调用状态码
	Msg                     string  `json:"msg"`                     // API调用信息
	TransID                 string  `json:"transId"`                 // 支付公司单号
	PlaceOrderAmount        float64 `json:"placeOrderAmount"`        // 彩商下单金额
	Amount                  float64 `json:"amount"`                  // 实际存款金额
	URL                     string  `json:"url"`                     // 收银台地址
	Account                 string  `json:"account"`                 // 银行卡号
	Bank                    string  `json:"bank"`                    // 银行编码
	Branch                  string  `json:"branch"`                  // 支行
	Holder                  string  `json:"holder"`                  // 持卡人姓名
	Postscript              string  `json:"postscript"`              // 转账附言
	GameMerchantBalance     float64 `json:"gameMerchantBalance"`     // 彩商余额
	GameMerchantDailyAmount float64 `json:"gameMerchantDailyAmount"` // 彩商当日收款额
}

// 支付配置
type RMPayConfig struct {
	MerchantID  string `json:"merchant_id"`  // 商户ID
	ApiKey      string `json:"api_key"`      // API密钥
	ApiURL      string `json:"api_url"`      // API地址
	CallbackURL string `json:"callback_url"` // 回调地址
}

// 充值回调数据结构
type RMPayRechargeCallback struct {
	MerchantID       string  `json:"merchantId"`       // 商户号
	OrderID          string  `json:"orderId"`          // 商户订单号
	TransID          string  `json:"transId"`          // 支付公司单号
	Status           int     `json:"status"`           // 订单状态：0-成功，11-取消
	Description      string  `json:"description"`      // 订单状态说明
	Fee              float64 `json:"fee"`              // 服务费
	Amount           float64 `json:"amount"`           // 实收金额
	PlaceOrderAmount float64 `json:"placeOrderAmount"` // 彩商下单金额
	Nonce            string  `json:"nonce"`            // 随机字符串
	Timestamp        int64   `json:"timestamp"`        // 时间戳
}

// 提现回调数据结构
type RMPayWithdrawCallback struct {
	MerchantID  string  `json:"merchantId"`  // 商户号
	OrderID     string  `json:"orderId"`     // 商户订单号
	TransID     string  `json:"transId"`     // 支付公司单号
	Status      int     `json:"status"`      // 订单状态：0-成功，7-撤单，11-取消
	Description string  `json:"description"` // 订单状态说明
	Fee         float64 `json:"fee"`         // 服务费
	Amount      float64 `json:"amount"`      // 实际支付金额
	Nonce       string  `json:"nonce"`       // 随机字符串
	Timestamp   int64   `json:"timestamp"`   // 时间戳
}

// 初始化设置HTTP回调接口端点
func (c *rmpay) Init() {
	server.Http().PostNoAuth("/api/rmpay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/rmpay/withdraw/callback", c.withdrawCallback)
}

// 充值
func (c *rmpay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		logs.Error("rmpay: 获取支付方式失败:", err)
		ctx.RespErr(errors.New("获取支付方式失败"), &errcode)
		return
	}

	var cfg RMPayConfig
	if err = json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("rmpay: 解析支付配置失败:", err)
		ctx.RespErr(errors.New("支付配置错误"), &errcode)
		return
	}

	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		logs.Error("rmpay: 获取用户信息失败:", err)
		ctx.RespErr(errors.New("获取用户信息失败"), &errcode)
		return
	}

	// 添加用户名字验证
	if req.RealName == "" {
		logs.Error("rmpay: 用户姓名为空")
		ctx.RespErr(errors.New("请输入付款人姓名"), &errcode)
		return
	}

	// 计算汇率和实际金额
	rate, err := c.getRechargeRate(req.Symbol)
	if err != nil {
		logs.Error("rmpay: 获取汇率失败:", err)
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}
	amount := float64(req.Amount) / rate

	// 创建订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      24, // rmpay 的唯一 ID
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	if err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder); err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 准备请求参数
	params := xgo.H{
		"merchantId":   cfg.MerchantID,
		"userId":       fmt.Sprintf("%d", user.UserID),
		"orderId":      fmt.Sprintf("%d", rechargeOrder.ID),
		"orderTime":    time.Now().Format("2006-01-02 15:04:05"),
		"terminalType": "PC",
		"amount":       fmt.Sprintf("%.2f", rechargeOrder.Amount),
		"payer":        req.RealName,
		"payWith":      payMethod.PayType,
		"useCounter":   true,
		"notifyUrl":    fmt.Sprintf("%s/api/rmpay/recharge/callback", cfg.CallbackURL),
		"nonce":        c.generateNonce(),
		"timestamp":    fmt.Sprintf("%d", time.Now().Unix()),
	}
	// 打印日志，显示原始数据
	logs.Info("rmpay: 充值原始数据", params)
	// 加密请求数据
	encryptedData, err := c.encryptData(params, cfg.ApiKey)
	if err != nil {
		logs.Error("rmpay: 加密请求数据失败:", err)
		tx.Rollback()
		ctx.RespErr(errors.New("加密请求数据失败"), &errcode)
		return
	}

	// 发送请求
	requestBody, _ := json.Marshal(map[string]string{
		"id":   cfg.MerchantID,
		"data": encryptedData,
	})

	resp, err := c.post(cfg.ApiURL+"/api/biz/place_deposit_order", map[string]string{
		"Content-Type": "application/json",
	}, requestBody)

	if err != nil {
		logs.Error("rmpay: 发送支付请求失败:", err)
		tx.Rollback()
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		return
	}

	// 解密响应数据
	decryptedData, err := c.decryptData(string(resp.Body()), cfg.ApiKey)
	if err != nil {
		logs.Error("rmpay: 解密响应数据失败:", err)
		tx.Rollback()
		ctx.RespErr(errors.New("解密响应数据失败"), &errcode)
		return
	}

	var response struct {
		Code    int     `json:"code"`
		Msg     string  `json:"msg"`
		TransID string  `json:"transId"`
		Amount  float64 `json:"amount"`
		URL     string  `json:"url"`
	}
	if err = json.Unmarshal(decryptedData, &response); err != nil {
		logs.Error("rmpay unmarshal decrypted response failed:", err)
		logs.Error("rmpay decrypted content:", string(decryptedData))
		tx.Rollback()
		ctx.RespErr(errors.New("解析响应数据失败"), &errcode)
		return
	}

	if response.Code != 0 {
		tx.Rollback()
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return
	}

	tx.Commit()

	// 更新三方订单号
	if err = c.updateThirdOrder(rechargeOrder.ID, response.TransID); err != nil {
		logs.Error("rmpay: 更新三方订单号失败:", err)
	}

	ctx.RespOK(xgo.H{
		"payurl": response.URL,
	})
}

// 充值回调
func (c *rmpay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 打印原始请求体
	body, _ := ctx.Gin().GetRawData()

	// 解析加密请求
	var encryptedReq struct {
		ID      string `json:"id"`
		Data    string `json:"data"`
		OrderID string `json:"orderId"`
	}
	if err := json.NewDecoder(bytes.NewReader(body)).Decode(&encryptedReq); err != nil {
		logs.Error("rmpay: 解析请求失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析请求失败"}`)
		return
	}

	// 获取订单信息
	orderId, _ := strconv.Atoi(encryptedReq.OrderID)
	order, err := c.getRechargeOrder(orderId)
	if err != nil {
		logs.Error("rmpay: 获取订单信息失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"订单不存在"}`)
		return
	}

	// 根据订单获取支付配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("rmpay: 获取支付配置失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"获取支付配置失败"}`)
		return
	}

	var cfg RMPayConfig
	if err = json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("rmpay: 解析支付配置失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析支付配置失败"}`)
		return
	}

	// 解密数据
	decryptedData, err := c.decryptData(encryptedReq.Data, cfg.ApiKey)
	if err != nil {
		logs.Error("rmpay: 解密数据失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解密数据失败"}`)
		return
	}

	// 解析回调数据
	var callback RMPayRechargeCallback
	if err = json.Unmarshal(decryptedData, &callback); err != nil {
		logs.Error("rmpay: 解析回调数据失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析回调数据失败"}`)
		return
	}

	// 检查订单状态
	if order.State == 5 {
		ctx.Gin().String(200, `{"code":0,"msg":"success"}`)
		return
	}

	// 检查金额
	if math.Abs(order.Amount-callback.Amount) > 0.01 {
		logs.Error("rmpay: 金额不匹配, 订单金额:", order.Amount, "回调金额:", callback.Amount)
		ctx.Gin().String(400, `{"code":1,"msg":"金额不匹配"}`)
		return
	}

	// 更新三方订单号
	if err = c.updateThirdOrder(order.ID, callback.TransID); err != nil {
		logs.Error("rmpay: 更新三方订单号失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"更新三方订单号失败"}`)
		return
	}

	// 处理订单状态
	if callback.Status == 0 { // 成功
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
		ctx.Gin().String(200, `{"code":0,"msg":"success"}`)
	} else {
		logs.Info("rmpay: 订单失败, 状态:", callback.Status, "描述:", callback.Description)
		ctx.Gin().String(200, `{"code":0,"msg":"failed"}`)
	}
}

// 提现回调
func (c *rmpay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 打印原始请求体
	body, _ := ctx.Gin().GetRawData()
	logs.Info("rmpay: 收到提现回调请求:", string(body))

	// 解析加密请求
	var encryptedReq struct {
		ID      string `json:"id"`
		Data    string `json:"data"`
		OrderID string `json:"orderId"`
	}
	if err := json.NewDecoder(bytes.NewReader(body)).Decode(&encryptedReq); err != nil {
		logs.Error("rmpay: 解析请求失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析请求失败"}`)
		return
	}

	// 获取订单信息
	orderId, _ := strconv.Atoi(encryptedReq.OrderID)
	order, err := c.getWithdrawOrder(orderId)
	if err != nil {
		logs.Error("rmpay: 获取订单信息失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"订单不存在"}`)
		return
	}

	// 根据订单获取支付配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 获取支付配置失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"获取支付配置失败"}`)
		return
	}

	var cfg RMPayConfig
	if err = json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 解析支付配置失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析支付配置失败"}`)
		return
	}

	// 解密数据
	decryptedData, err := c.decryptData(encryptedReq.Data, cfg.ApiKey)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 解密数据失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解密数据失败"}`)
		return
	}

	// 打印解密后的数据
	logs.Info("rmpay: 解密后的回调数据:", string(decryptedData))

	// 解析回调数据
	var callback RMPayWithdrawCallback
	if err = json.Unmarshal(decryptedData, &callback); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 解析回调数据失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"解析回调数据失败"}`)
		return
	}

	// 检查订单是否已经处理完成
	if order.State == 6 || order.State == 7 {
		logs.Info("rmpay: 订单已处理完成，忽略重复回调, 订单ID:", order.ID, "当前状态:", order.State)
		ctx.Gin().String(200, `{"code":0,"msg":"order already processed"}`)
		return
	}

	// 获取汇率
	rate, err := c.getWithdrawRate(order.Symbol)
	if err != nil {
		logs.Error("rmpay: 获取汇率失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"获取汇率失败"}`)
		return
	}
	amount := float64(order.Amount) * rate // 实际金额
	// 检查金额
	if math.Abs(amount-callback.Amount) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 金额不匹配, 订单金额:", amount, "回调金额:", callback.Amount)
		ctx.Gin().String(400, `{"code":1,"msg":"金额不匹配"}`)
		return
	}

	// 更新三方订单号
	if err = c.updateThirdOrder(order.ID, callback.TransID); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("rmpay: 更新三方订单号失败:", err)
		ctx.Gin().String(400, `{"code":1,"msg":"更新三方订单号失败"}`)
		return
	}

	// 处理订单状态
	var newState int
	switch callback.Status {
	case 0:
		newState = 6 // 成功
	case 7, 11:
		newState = 7 // 失败（包括撤单和取消）
	default:
		logs.Error("rmpay: 未知的订单状态:", callback.Status, "描述:", callback.Description)
		ctx.Gin().String(200, `{"code":1,"msg":"unknown status"}`)
		return
	}

	c.withdrawCallbackHandel(int(order.ID), newState)
	ctx.Gin().String(200, `{"code":0,"msg":"success"}`)
}

// 生成随机字符串
func (c *rmpay) generateNonce() string {
	// 生成10位随机数
	return fmt.Sprintf("%010d", rand.Intn(10000000000))
}

// encryptData AES-128-CBC 加密
func (c *rmpay) encryptData(data interface{}, key string) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	padded := c.pkcs7Padding(jsonData, block.BlockSize())
	ciphertext := make([]byte, len(padded))
	mode := cipher.NewCBCEncrypter(block, []byte(key))
	mode.CryptBlocks(ciphertext, padded)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptData AES-128-CBC 解密
func (c *rmpay) decryptData(encryptedData string, key string) ([]byte, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}

	plaintext := make([]byte, len(ciphertext))
	mode := cipher.NewCBCDecrypter(block, []byte(key))
	mode.CryptBlocks(plaintext, ciphertext)

	return c.pkcs7UnPadding(plaintext), nil
}

// pkcs7Padding PKCS7 填充
func (c *rmpay) pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// pkcs7UnPadding PKCS7 去除填充
func (c *rmpay) pkcs7UnPadding(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	return data[:(length - unpadding)]
}
