package qqwry

import (
	"fmt"
	"testing"
)

func TestNewQQwry(t *testing.T) {
	handler, err := NewQQwry("../qqwry/qqwry.dat")
	if err != nil {
		t.Error(err)
	}
	res := handler.Find("103.97.2.108")
	fmt.Println(res.Addr)

	res = handler.Find("35.213.121.118")
	fmt.Println(res.Addr)

	res = handler.Find("35.207.119.227")
	fmt.Println(res.Addr)

	res = handler.Find("114.114.114.114")
	fmt.Println(res.Addr)

	res = handler.Find("35.206.250.120")
	fmt.Println(res.Addr)

	res = handler.Find("")
	fmt.Println(res.Addr)
}
