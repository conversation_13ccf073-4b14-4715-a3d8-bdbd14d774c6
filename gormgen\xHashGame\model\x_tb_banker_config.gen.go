// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTbBankerConfig = "x_tb_banker_config"

// XTbBankerConfig 渠道上庄配置
type XTbBankerConfig struct {
	ChannelID       int32     `gorm:"column:ChannelId;primaryKey;comment:渠道id" json:"ChannelId"`                           // 渠道id
	SellerID        int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	BankerMinTime   int32     `gorm:"column:BankerMinTime;comment:上庄最小时间(单位：秒)" json:"BankerMinTime"`                      // 上庄最小时间(单位：秒)
	BankerMaxTime   int32     `gorm:"column:BankerMaxTime;comment:上庄最大时间(单位：秒)" json:"BankerMaxTime"`                      // 上庄最大时间(单位：秒)
	BankerMinAmount float64   `gorm:"column:BankerMinAmount;default:0.000000;comment:上庄最小金额" json:"BankerMinAmount"`       // 上庄最小金额
	BankerTgGroup   string    `gorm:"column:BankerTgGroup;comment:上庄tg群" json:"BankerTgGroup"`                             // 上庄tg群
	Status          int32     `gorm:"column:Status;default:1;comment:状态 1开启 2关闭" json:"Status"`                            // 状态 1开启 2关闭
	Memo            string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                  // 描述
	Operator        string    `gorm:"column:Operator;comment:操作员" json:"Operator"`                                         // 操作员
	OperUserID      int32     `gorm:"column:OperUserID;comment:操作员ID" json:"OperUserID"`                                   // 操作员ID
	DeviceType      int32     `gorm:"column:DeviceType;comment:设备类型" json:"DeviceType"`                                    // 设备类型
	DeviceID        string    `gorm:"column:DeviceID;comment:设备ID" json:"DeviceID"`                                        // 设备ID
	CreateTime      time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTbBankerConfig's table name
func (*XTbBankerConfig) TableName() string {
	return TableNameXTbBankerConfig
}
