package server

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	daoStat "xserver/gormgen/xHashStat/dao"
	"xserver/model/third"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/spf13/viper"
	"github.com/zhms/xgo/xgo"
)

var http *abugo.AbuHttp
var redis *abugo.AbuRedis
var credis *abugo.AbuRedis
var db *abugo.AbuDb
var daoXHashGame *dao.Query
var dbStat *abugo.AbuDb
var daoXHashStat *daoStat.Query
var buka *third.BuKa

var xredis *xgo.XRedis = &xgo.XRedis{}
var xdb *xgo.XDb = &xgo.XDb{}

var websocket *abugo.AbuWebsocket
var project string
var module string
var dbprefix string
var debug bool = false
var imageurl string

var LangMap = map[int]string{1: "zh-<PERSON>", 2: "en", 3: "td", 4: "ko", 5: "tr", 6: "vi", 7: "pt", 8: "hi", 9: "ja", 10: "ru", 11: "km", 12: "uk", 13: "zh-TW", 14: "es"}

func Init() {
	abugo.Init()
	xgo.Init()
	debug = viper.GetBool("server.debug")
	project = viper.GetString("server.project")
	module = viper.GetString("server.module")
	dbprefix = viper.GetString("server.dbprefix")
	imageurl = viper.GetString("image.url")
	db = new(abugo.AbuDb)
	db.Init("server.db")
	xdb.Init("server.db")
	daoXHashGame = dao.Use(db.GormDao())
	dbStat = new(abugo.AbuDb)
	dbStat.Init("server.dbStat")
	daoXHashStat = daoStat.Use(dbStat.GormDao())
	redis = new(abugo.AbuRedis)
	credis = new(abugo.AbuRedis)
	redis.Init("server.redis")
	credis.Init("server.credis")

	xredis.Init("server.redis")
	http = new(abugo.AbuHttp)
	http.Init("server.http.http.port")
	//buka = new(third.BuKa)
	//buka.InitConfig()
}

func Http() *abugo.AbuHttp {
	return http
}

func Redis() *abugo.AbuRedis {
	return redis
}
func CRedis() *abugo.AbuRedis {
	return credis
}
func Db() *abugo.AbuDb {
	return db
}

func DaoxHashGame() *dao.Query {
	return daoXHashGame
}
func DaoxHashStat() *daoStat.Query {
	return daoXHashStat
}

func XRedis() *xgo.XRedis {
	return xredis
}

func XDb() *xgo.XDb {
	return xdb
}

func Debug() bool {
	return debug
}

func Project() string {
	return project
}

func Module() string {
	return module
}

func DbPrefix() string {
	return dbprefix
}

func ImageUrl() string {
	return imageurl
}

func BuKa() *third.BuKa {
	return buka
}

func Run() {
	abugo.Run()
}

type TokenData struct {
	UserId       int
	SellerId     int
	Address      string
	Account      string
	VerifyState  int
	VerifyAmount int
	ChannelId    int
	AccountType  int
	Host         string
}

func GetToken(ctx *abugo.AbuHttpContent) *TokenData {
	td := TokenData{}
	err := json.Unmarshal([]byte(ctx.TokenData), &td)
	if err != nil {
		return nil
	}
	return &td
}

func GetTokenFromRedis(userToken string) *TokenData {
	defer func() {
		recover()
		logs.Error("GetTokenFromRedis recover: userToken", userToken)
	}()

	td := TokenData{}
	data := Http().GetToken(userToken)
	err := json.Unmarshal(data.([]byte), &td)
	if err != nil {
		return nil
	}
	return &td
}

func GetConfigString(SellerId int, ChannelId int, ConfigName string) string {
	cachekey := fmt.Sprintf("systemconfig:%d:%d:%s", SellerId, ChannelId, ConfigName)
	data, err := xredis.GetCacheString(cachekey, func() (string, error) {
		result, err := xdb.Table("x_config").Select("ConfigValue").Where("SellerId = ? and ChannelId = ? and ConfigName = ?", SellerId, ChannelId, ConfigName).First()
		if err != nil {
			logs.Error("GetConfigString1:", err)
			return "", err
		}
		if result == nil {
			return "", nil
		}
		return result.String("ConfigValue"), nil
	})
	if err != nil {
		logs.Error("GetConfigString2:", err)
		return ""
	}
	return data
}

func GetConfigInt(SellerId int, ChannelId int, ConfigName string) int64 {
	v := GetConfigString(SellerId, ChannelId, ConfigName)
	iv, err := strconv.ParseInt(v, 10, 64)
	if err != nil {
		return 0
	}
	return iv
}

func GetConfigFloat(SellerId int, ChannelId int, ConfigName string) float64 {
	v := GetConfigString(SellerId, ChannelId, ConfigName)
	iv, err := strconv.ParseFloat(v, 64)
	if err != nil {
		return 0
	}
	return iv
}

func GetChannel(ctx *abugo.AbuHttpContent, hostex string) (ChannelId, SellerId int) {
	defer func() {
		recover()
		logs.Error("GetChannel recover: hostex=", hostex)
	}()

	//token := GetToken(ctx)
	//if token != nil && token.SellerId != 0 && token.ChannelId != 0 {
	//	return token.ChannelId, token.SellerId
	//}
	host := ctx.Host()
	if hostex != "" {
		host = hostex
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	ChannelId = 1
	SellerId = 1

	cachekey := fmt.Sprintf("channelandseller:%s", host)
	str, err := xredis.GetCacheString(cachekey, func() (string, error) {
		channeldata, err := xdb.Table("x_channel_host").
			Join("inner join x_channel on x_channel.ChannelId = x_channel_host.ChannelId").
			Where("x_channel_host.host = ? and x_channel_host.state = 1", host).First()
		if err == nil && channeldata != nil {
			ChannelId = int(abugo.GetInt64FromInterface(channeldata.Int("ChannelId")))
			SellerId = int(abugo.GetInt64FromInterface(channeldata.Int("SellerId")))
		}
		// 独立代理
		agentdata, err := xdb.Table("x_agent_independence").Where("host = ? and state = 1", host).First()
		if err == nil && agentdata != nil {
			ChannelId = agentdata.Int("ChannelId")
			SellerId = agentdata.Int("SellerId")
		}
		str := fmt.Sprintf("%d,%d", ChannelId, SellerId)
		xredis.Set(cachekey, str, 60*5)
		return str, nil
	})
	if err != nil {
		logs.Error("GetChannel err:", err)
		return 1, 1
	}
	// logs.Debug("GetChannel: host=%s, value=%s", host, str)
	strArr := strings.Split(str, ",")
	if len(strArr) > 1 {
		ChannelId, _ = strconv.Atoi(strArr[0])
		SellerId, _ = strconv.Atoi(strArr[1])
	}
	return ChannelId, SellerId
}

func GetTopAgentId(ctx *abugo.AbuHttpContent, hostex string, agentCode string) int {
	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if hostex != "" {
		host = hostex
	}

	cachekey := fmt.Sprintf("topagentid:%s", host)
	TopAgentId, err := xredis.GetCacheInt(cachekey, func() (int64, error) {
		Id := 0
		agentdata, err := xdb.Table("x_agent_independence").Where("host = ? and state = 1", host).First()
		if err == nil && agentdata != nil {
			Id = agentdata.Int("UserId")
		}
		// 推广链接
		if len(agentCode) > 0 {
			promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, agentCode)
			indepenAgent, err := xdb.Table("x_agent_independence").Where("PromotionHost = ? and state = 1", promotionHost).First()
			if err == nil && indepenAgent != nil {
				Id = indepenAgent.Int("UserId")
			}
		}
		xredis.Set(cachekey, Id, 10)
		return int64(Id), nil
	})
	if err != nil {
		logs.Error("GetTopAgentId:", err)
		return 2
	}
	return int(TopAgentId)
}

func GetBalance(id int) float64 {
	defer recover()
	res, err := xdb.Table("x_user").Select("Amount").Where("UserId = ?", id).First()
	if err != nil {
		logs.Error("GetBalance1:", err)
		return 0
	}

	if res == nil {
		return 0
	}

	return res.Float64("Amount")
}

func GetAgentIndependenceUserId(ctx *abugo.AbuHttpContent, hostex string, agentCode string) int64 {
	host := ctx.Host()
	if hostex != "" {
		host = hostex
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	cachekey := fmt.Sprintf("AgentIndependenceUserId:%s", host)
	var userId int64
	var err error
	userId, err = xredis.GetCacheInt(cachekey, func() (int64, error) {
		// 独立代理
		agentdata, err := xdb.Table("x_agent_independence").Where("host = ? and state = 1", host).First()
		if err == nil && agentdata != nil {
			userId = agentdata.Int64("UserId")
		}
		// 推广链接
		if len(agentCode) > 0 {
			promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, agentCode)
			indepenAgent, err := xdb.Table("x_agent_independence").Where("PromotionHost = ? and state = 1", promotionHost).First()
			if err == nil && indepenAgent != nil {
				userId = indepenAgent.Int64("UserId")
			}
		}
		xredis.Set(cachekey, userId, 10)
		return userId, nil
	})
	if err != nil {
		logs.Error("GetAgentIndependenceUserId:", err)
		return 0
	}
	logs.Debug("AgentIndependenceUserId: host=%s, userId=%d", host, userId)

	return userId
}
