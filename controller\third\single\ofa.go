package single

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/url"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// OFA真人单一钱包类
// OFA真人接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

// 新版OFA API客户端
type OFALiveSingleService struct {
	apiUrl                string            // api基础接口
	clientId              string            // Client ID
	clientSecret          string            // Client Secret
	desKey                string            // DES-CBC加密的Key
	desIV                 string            // DES-CBC加密的偏移量
	systemCode            string            // systemCode商户号
	webId                 string            // webId商户号
	currency              string            //币种
	homeUrl               string            //跳转URL
	brandName             string            //厂商标识
	games                 map[string]string //游戏类型
	activitys             map[string]string //活动类型
	payoutTypes           map[string]string //派彩类型
	Debug                 bool              //日志调试模式
	RefreshUserAmountFunc func(int) error   // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

// 初始化OFA真人单一钱包
func NewOFALiveSingleService(params map[string]string, fc func(int) error) *OFALiveSingleService {
	//游戏类型
	games := map[string]string{
		"BAC": "百家乐",
		"DT":  "龙虎",
		"DTB": "区块链龙虎",
		"BAS": "极速百家乐",
		"RO":  "轮盘",
		"BAB": "区块链百家乐",
	}

	//活动类型
	activityPayout := map[string]string{
		"DEDUCTION-GIFT": "赠礼扣款",
		"DEDUCTION-POST": "购买 FBfans 贴文",
	}

	//派彩类型
	payoutType := map[string]string{
		"CANCEL-ORDER": "取消注单 (取消单一笔注单)",
	}

	return &OFALiveSingleService{
		apiUrl:                params["api_url"],       //API地址
		clientId:              params["client_id"],     //Client ID
		clientSecret:          params["client_secret"], //Client Secret
		desKey:                params["des_key"],       //DES-CBC加密的Key
		desIV:                 params["des_iv"],        //DES-CBC加密的偏移量
		systemCode:            params["system_code"],   //渠道号
		webId:                 params["web_id"],        //渠道号
		currency:              params["currency"],      //币种
		brandName:             "ofalive",               //厂商标识
		games:                 games,
		activitys:             activityPayout,
		payoutTypes:           payoutType,
		Debug:                 false, //是否调试模式
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyOFALive = "cacheKeyOFALive:"

// OFALive返回错误码
const (
	OFALive_Code_Success               = "00000" // 成功
	OFALive_Code_Fail                  = "00100" // 失败
	OFALive_Code_Data_Not_Exist        = "00101" // 资料不存在
	OFALive_Code_Data_Already_Exist    = "00102" // 资料已存在，无法重复建立
	OFALive_Code_Timeout               = "00200" // 超时交易
	OFALive_Code_Param_Error           = "00300" // 需求参数异常
	OFALive_Code_Param_Out_Of_Range    = "00301" // 超出参数范围
	OFALive_Code_System_Busy           = "00500" // 系统忙碌
	OFALive_Code_Incorrect_Param_Rules = "10001" // 参数异常
)

// 计算签名字符串
func (l *OFALiveSingleService) getRequestSign(param string) (sign string) {
	src := param + l.clientSecret
	hash := sha256.New()
	hash.Write([]byte(src))
	sign = hex.EncodeToString(hash.Sum(nil))
	return
}

// 计算签名字符串
func (l *OFALiveSingleService) getCallbackSign(param string) (sign string) {
	src := param + l.clientSecret
	hash := sha256.New()
	hash.Write([]byte(src))
	sign = hex.EncodeToString(hash.Sum(nil))
	return
}

// Balance 获取玩家余额 API URL 	Balance
func (l *OFALiveSingleService) Balance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode string `json:"system_code"` // 系统代码
		WebId      string `json:"web_id"`      // 站台代码，即代理唯一识别码 ID
		UserId     string `json:"user_id"`     // 玩家的唯一识别码，仅英数字
	}

	type ResponseData struct {
		Code      string `json:"code"`      // 00000即为成功，其它代码皆为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳记
		Data      struct {
			Balance float64 `json:"balance"` // 余额
		} `json:"data"`
	}
	respdata := ResponseData{
		Code:      OFALive_Code_Success,
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 获取玩家余额")
	if err != nil {
		logs.Error("OFALive_single 获取玩家余额 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = err.Error()
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}

	// 解析JSON数据
	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 获取玩家余额 解析JSON数据失败 decryptedMsg=", string(decryptedBytes), " err=", err.Error())
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 获取玩家余额 非法的商户编码 l.systemCode=", l.systemCode, " reqdata.SystemCode=", reqdata.SystemCode)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}

	if !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("OFALive_single 获取玩家余额 非法的WebId reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 获取玩家余额 会员账号错误 用户名转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "资料不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("OFALive_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.Code = OFALive_Code_Data_Not_Exist
			respdata.Message = "资料不存在"
		} else {
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
		}
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
		return
	}
	// 给Data赋值
	respdata.Data.Balance = userBalance.Amount

	l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 获取玩家余额")
	return
}

// Bet 下注 API URL 	Bet
func (l *OFALiveSingleService) Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode string  `json:"system_code"` // 系统代码，必填，最大长度10
		WebId      string  `json:"web_id"`      // 平台代码，必填，最大长度15
		UserId     string  `json:"user_id"`     // 玩家唯一标识，必填，最大长度20
		BetSn      string  `json:"bet_sn"`      // 注单号，必填，最大长度15
		GameCode   string  `json:"game_code"`   // 游戏代码，必填
		GameName   string  `json:"game_name"`   // 游戏名称，必填
		TableCode  string  `json:"table_code"`  // 桌台代码，必填
		PlayCode   string  `json:"play_code"`   // 玩法代码，必填
		PlayName   string  `json:"play_name"`   // 玩法名称，必填
		Odds       string  `json:"odds"`        // 赔率，必填
		OrderMoney float64 `json:"order_money"` // 下注金额，必填
		OrderTime  string  `json:"order_time"`  // 下注时间，必填
		SettleDate string  `json:"settle_date"` // 结算日期，必填
		Currency   string  `json:"currency"`    // 币种，必填
		Ip         string  `json:"ip"`          // IP地址，必填
	}

	type ResponseData struct {
		Code      string `json:"code"`      // 状态码，00000为成功，其他为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳
		Data      struct {
			Balance float64 `json:"balance"` // 玩家余额
		} `json:"data"`
	}
	respdata := ResponseData{
		Code:      "00000",
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 下注确认")
	if err != nil {
		logs.Error("OFALive_single 下注确认 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = err.Error()
		ctx.RespJson(respdata)
		return
	}

	// 解析JSON数据
	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 下注确认 解析JSON数据失败 decryptedMsg=", string(decryptedBytes), " err=", err.Error())
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 下注确认 商户编码不正确 BetSn=", reqdata.BetSn, " reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("OFALive_single 下注确认 非法的WebId reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "平台代码不正确"
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 下注确认 会员账号错误 BetSn=", reqdata.BetSn, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "此玩家帐户不存在"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("OFALive_single 下注确认 非法的币种 BetSn=", reqdata.BetSn, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	if reqdata.OrderMoney <= 0 {
		logs.Error("OFALive_single 下注确认 下注金额不能为负数或零 BetSn=", reqdata.BetSn, " OrderMoney=", reqdata.OrderMoney)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "下注金额不能为负数或零"
		ctx.RespJson(respdata)
		return
	}

	totalBetAmount := reqdata.OrderMoney
	// 获取游戏名称
	gameName := l.games[fmt.Sprintf("%v", reqdata.GameCode)]
	if gameName == "" {
		gameName = fmt.Sprintf("真人(%v)", reqdata.GameCode)
	}
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.BetSn //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.BetSn, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.BetSn, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	// 开始下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("OFALive_single 下注确认 获取用户信息失败 BetSn=", reqdata.BetSn, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "此玩家帐户不存在"
			} else {
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "系统忙碌"
			}
			return e
		} else {
			respdata.Data.Balance = userBalance.Amount
		}
		if totalBetAmount > userBalance.Amount {
			e = errors.New("余额不足")
			respdata.Code = OFALive_Code_Param_Out_Of_Range
			respdata.Message = "余额不足"
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		thirdId := reqdata.BetSn
		thirdTime := reqdata.OrderTime

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e == nil {
			logs.Error("OFALive_single 下注确认 单号已存在 BetSn=", reqdata.BetSn, " thirdId=", thirdId, " error=", e)
			respdata.Code = OFALive_Code_Data_Already_Exist
			respdata.Message = "重复单号"
			respdata.Data.Balance = userBalance.Amount
			return errors.New("订单号重复")
		}
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("OFALive_single 下注确认 查询已存在订单失败 BetSn=", reqdata.BetSn, " thirdId=", thirdId, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		}

		// 创建注单
		order = thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userBalance.UserId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       reqdata.GameCode,
			GameName:     gameName,
			BetAmount:    totalBetAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(decryptedBytes),
			State:        1,
			Fee:          0,
			DataState:    -1, //未开奖
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("OFALive_single 下注确认 创建订单失败 BetSn=", reqdata.BetSn, " thirdId=", thirdId, " order=", order, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "创建订单失败"
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, totalBetAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", totalBetAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && totalBetAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("OFALive_single 下注确认 扣款失败 BetSn=", reqdata.BetSn, " userId=", userId, " thirdId=", thirdId, " totalBetAmount=", totalBetAmount, " err=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "扣款失败"
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -totalBetAmount,
			AfterAmount:  userBalance.Amount - totalBetAmount,
			Reason:       utils.BalanceCReasonOFALiveBet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("OFALive_single 下注确认 创建账变记录失败 BetSn=", reqdata.BetSn, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "创建账变记录失败"
			return e
		}
		userBalance.Amount -= totalBetAmount
		respdata.Data.Balance = userBalance.Amount
		return nil
	})

	if err != nil {
		logs.Error("OFALive_single 下注确认 事务处理失败 err=", err)
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][OFALive_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][OFALive_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	respdata.Timestamp = time.Now().Format("2006-01-02 15:04:05")
	l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 下注确认")
	return
}

// CancelBet 取消單  API URL CancelBet
func (l *OFALiveSingleService) CancelBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode string `json:"system_code"` // 系统代码
		WebId      string `json:"web_id"`      // 站台代码，即代理唯一识别码 ID
		UserId     string `json:"user_id"`     // 玩家的唯一识别码，仅英数字
		BetSn      string `json:"bet_sn"`      // 注單號
	}

	type ResponseData struct {
		Code      string `json:"code"`      // 00000即为成功，其它代码皆为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳
		Data      struct {
			Balance float64 `json:"balance"` // 玩家余额
		} `json:"data"`
	}
	respdata := ResponseData{
		Code:      OFALive_Code_Success,
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 取消下注")
	if err != nil {
		logs.Error("OFALive_single 取消下注 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = err.Error()
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	}

	// 解析JSON数据
	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 取消下注 解析JSON数据失败 decryptedMsg=", string(decryptedBytes), " err=", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "需求参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 取消下注 非法的商户编码 l.systemCode=", l.systemCode, " reqdata.SystemCode=", reqdata.SystemCode)
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "需求参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 取消下注 用户名转换错误 Uuid=", reqdata.UserId, " reqdataParam.LoginName=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "会员账号错误"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	}

	if !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("OFALive_single 取消下注 非法的WebId reqdataParam.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "需求参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.BetSn //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		l.ProcessEncryptedResponse(ctx, duplicateResult, "OFALive_single 取消下注")
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.BetSn, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.BetSn, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	// 开始取消下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("OFALive_single 取消下注 获取用户余额失败 thirdId=", reqdata.BetSn, " userId=", userId, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			respdata.Data.Balance = userBalance.Amount
		}

		thirdId := reqdata.BetSn
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
				logs.Info("OFALive_single 取消下注 订单不存在  thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "單號不存在"
				return e
			}
			logs.Error("OFALive_single 取消下注 查询订单失败  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "查询订单失败"
			return e
		}

		betCtx := string(decryptedBytes) // l.OFALiveGameRecord2Str(v., reqdataParam.UserId)
		if order.DataState != -1 {
			e = errors.New("不能取消已结算订单")
			if order.DataState == 1 {
				logs.Error("OFALive_single 取消下注 不能取消已结算订单 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Already_Exist
				respdata.Message = "注单已结算"
				return e
			} else if order.DataState == -2 {
				logs.Error("FBLive_single 取消下注 注单已取消 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Already_Exist
				respdata.Message = "注单已取消"
				return e
			} else {
				logs.Error("FBLive_single 取消下注 注单状态异常 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Already_Exist
				respdata.Message = "注单状态异常"
				e = errors.New("注单状态异常")
				return e
			}
		}

		// 更新注单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  -2,
			"ThirdTime":  thirdTime,
			"ValidBet":   0,
			"WinAmount":  0,
			"BetCtx":     betCtx,
			"GameRst":    betCtx,
			"BetCtxType": 3,
			"RawData":    string(decryptedBytes),
		}).Error
		if e != nil {
			logs.Error("OFALive_single 取消下注 更新订单状态失败  thirdId=", thirdId, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "更新订单状态失败"
			return e
		}
		amount := order.BetAmount
		if amount != 0 { //金额不=0才需要更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("OFALive_single 取消下注 加扣款失败  userId=", userId, " thirdId=", thirdId, " amount=", amount, " err=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "加扣款失败"
				return e
			}
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       utils.BalanceCReasonOFALiveCancel,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("OFALive_single 取消下注 创建账变记录失败  thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "创建账变记录失败"
			return e
		}
		respdata.Data.Balance = userBalance.Amount + amount

		return nil
	})

	if err != nil {
		logs.Error("OFALive_single 取消下注 事务处理失败 Uuid=", reqdata.UserId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
		return
	} else {
		// 异步刷新用户余额
		go func(userId int64) {
			if l.RefreshUserAmountFunc != nil {
				e := l.RefreshUserAmountFunc(int(userId))
				if e != nil {
					logs.Error("OFALive_single 取消下注 刷新用户余额失败 userId=", userId, " err=", e.Error())
				}
			}
		}(int64(userId))
	}

	l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 取消下注")
	return
}

type OFARequestSettleData struct {
	SystemCode string  `json:"system_code"` // 系统代码
	WebId      string  `json:"web_id"`      // 站台代码，即代理唯一识别码 ID
	UserId     string  `json:"user_id"`     // 玩家的唯一识别码，仅英数字
	BetSn      string  `json:"bet_sn"`      // 注单序号
	ValidMoney float64 `json:"valid_money"` // 有效投注金额
	WinMoney   float64 `json:"win_money"`   // 中奖金额
	Profit     float64 `json:"profit"`      // 盈亏
	SettleTime string  `json:"settle_time"` // 结算时间
	SettleDate string  `json:"settle_date"` // 归账日期
	Status     int     `json:"status"`      // 注单状态 (2:未中奖,3: 中奖 ,4:和局)
}

// BetResult 結算 派彩回调接口 API URL 	BetResult
func (l *OFALiveSingleService) BetResult(ctx *abugo.AbuHttpContent) {

	type ResponseData struct {
		Code      string `json:"code"`      // 00000即为成功，其它代码皆为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳
		Data      struct {
			BetSn   string  `json:"bet_sn"`  // 注单序号
			Balance float64 `json:"balance"` // 会员该笔结算后余额
		} `json:"data"`
	}
	respdata := ResponseData{
		Code:      OFALive_Code_Success,
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 派彩")
	if err != nil {
		logs.Error("OFALive_single 派彩 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = err.Error()
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	}

	// 解析JSON数据
	reqdata := OFARequestSettleData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 派彩 解析JSON数据失败 decryptedMsg=", string(decryptedBytes), " err=", err.Error())
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 派彩 商户编码不正确 Uuid=", reqdata.UserId, " reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode)
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "商户编码不正确"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 派彩 会员账号错误 Uuid=", reqdata.UserId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "会员账号错误"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	}

	if !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("OFALive_single 派彩 非法的WebId Uuid=", reqdata.UserId, " reqdata.WebId=", reqdata.WebId, " l.clientId=", l.clientId)
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "需求参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.BetSn //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		l.ProcessEncryptedResponse(ctx, duplicateResult, "OFALive_single 派彩")
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.BetSn, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.BetSn, " err=", e.Error())
		}
	}()
	thirdId := reqdata.BetSn
	tablePre := "x_third_live_pre_order"
	table := "x_third_live"
	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("OFALive_single 派彩 获取用户余额失败 Uuid=", reqdata.UserId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "查询用户信息失败"
			}
			return e
		}

		thirdTime := reqdata.SettleTime
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
				logs.Error("OFALive_single 派彩 订单不存在  thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "单号不存在"
				return e
			}
			logs.Error("OFALive_single 派彩 查询订单失败  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "查询订单失败"
			return e
		}

		//betCtx := fmt.Sprintf("valid_money:%v,win_money:%v,profit:%v,settle_time:%v,settle_date:%v,status:%v",
		//	reqdata.ValidMoney, reqdata.WinMoney, reqdata.Profit, reqdata.SettleTime, reqdata.SettleDate, reqdata.Status)
		betCtx := l.BetRecord2String(reqdata)
		if order.DataState != -1 {
			e = errors.New("订单已结算")
			logs.Error("OFALive_single 派彩 订单已结算  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_Data_Already_Exist
			respdata.Message = "注单已结算"
			return e
		}

		// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
		validBet := math.Abs(reqdata.WinMoney - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}
		// 更新注单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  1,
			"ThirdTime":  thirdTime,
			"ValidBet":   validBet,
			"WinAmount":  reqdata.WinMoney,
			"BetCtx":     betCtx,
			"GameRst":    betCtx,
			"BetCtxType": 3,
			"RawData":    string(decryptedBytes),
		}).Error
		if e != nil {
			logs.Error("OFALive_single 派彩 更新订单状态失败  thirdId=", thirdId, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "更新订单状态失败"
			return e
		}

		order.DataState = 1
		order.ThirdTime = thirdTime
		order.ValidBet = validBet
		order.WinAmount = reqdata.WinMoney
		order.BetCtx = betCtx
		order.GameRst = betCtx
		order.BetCtxType = 3
		order.RawData = string(decryptedBytes)
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("OFALive_single 派彩 创建正式表订单失败  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "创建订单失败"
			return e
		}

		if reqdata.WinMoney != 0 { //金额不=0才需要更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", reqdata.WinMoney),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.WinMoney != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("OFALive_single 派彩 加扣款失败  userId=", userId, " thirdId=", thirdId, " v=", reqdata.WinMoney, " err=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "加扣款失败"
				return e
			}
		}
		//账变类型
		goldType := utils.BalanceCReasonOFALiveSettle
		afterAmount := userBalance.Amount + reqdata.WinMoney
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdata.WinMoney,
			AfterAmount:  afterAmount,
			Reason:       goldType,
			Memo:         l.brandName + " settle,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("OFALive_single 派彩 创建账变记录失败  thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "创建账变记录失败"
			return e
		}

		// 设置响应数据
		respdata.Data.BetSn = thirdId
		respdata.Data.Balance = afterAmount

		return nil
	})

	if err != nil {
		logs.Error("OFALive_single 派彩 事务处理失败 Uuid=", reqdata.UserId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
		return
	} else {
		// 发送余额变动通知
		// 推送派奖事件到 CustomerIO
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushRewardEvent(5, l.brandName, thirdId)
		}
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][OFALive_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][OFALive_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 派彩")
	return
}

// 数据DES-CBC加密函数
func (l *OFALiveSingleService) EncryptData(plaintext string) (string, error) {
	return l.DESEncrypt(plaintext)
}

// 数据DES-CBC解密函数
func (l *OFALiveSingleService) DecryptData(ciphertext string) (string, error) {
	return l.DESDecrypt(ciphertext)
}

// 创建API签名
func (l *OFALiveSingleService) CreateSignature(timestamp string, encryptedMsg string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(timestamp+l.clientSecret+l.clientId+encryptedMsg)))
}

// 验证API签名
func (l *OFALiveSingleService) VerifySignature(timestamp string, encryptedMsg string, signature string) bool {
	calculatedSignature := l.CreateSignature(timestamp, encryptedMsg)
	return strings.EqualFold(calculatedSignature, signature)
}

// 创建回调签名
func (l *OFALiveSingleService) CreateCallbackSignature(signKey string) string {
	return l.getCallbackSign(signKey)
}

// 验证回调签名
func (l *OFALiveSingleService) VerifyCallbackSignature(signKey string, signature string) bool {
	calculatedSignature := l.CreateCallbackSignature(signKey)
	return strings.EqualFold(calculatedSignature, signature)
}

// 数据DES-CBC加密函数
func (l *OFALiveSingleService) DESEncrypt(plaintext string) (string, error) {
	// 将密钥和IV转换为字节数组
	key := []byte(l.desKey)
	iv := []byte(l.desIV)

	// 创建DES密码块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS5填充
	plainBytes := []byte(plaintext)
	plainBytes = l.PKCS5Padding(plainBytes, block.BlockSize())

	// 创建加密器
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密
	ciphertext := make([]byte, len(plainBytes))
	mode.CryptBlocks(ciphertext, plainBytes)

	// 返回Base64编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 数据DES-CBC解密函数
func (l *OFALiveSingleService) DESDecrypt(ciphertext string) (string, error) {
	// 将密钥和IV转换为字节数组
	key := []byte(l.desKey)
	iv := []byte(l.desIV)

	// 创建DES密码块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 解码Base64密文
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建解密器
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密
	plaintext := make([]byte, len(cipherBytes))
	mode.CryptBlocks(plaintext, cipherBytes)

	// 去除PKCS5填充
	plaintext = l.PKCS5Unpadding(plaintext)

	return string(plaintext), nil
}

// PKCS5填充
func (l *OFALiveSingleService) PKCS5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// 去除PKCS5填充
func (l *OFALiveSingleService) PKCS5Unpadding(src []byte) []byte {
	length := len(src)
	if length == 0 {
		return src
	}
	unpadding := int(src[length-1])
	return src[:(length - unpadding)]
}

// ProcessEncryptedRequest 处理表单格式的加密请求
// 验证请求头，解析表单数据，验证签名，解密数据
// 返回: 解密后的字节数组, 原始请求体, 时间戳, 签名, 错误信息
func (l *OFALiveSingleService) ProcessEncryptedRequest(ctx *abugo.AbuHttpContent, logPrefix string) ([]byte, []byte, string, string, error) {
	// 验证请求头
	apiCI := ctx.Gin().GetHeader("APICI")
	apiSI := ctx.Gin().GetHeader("APISI")
	apiTS := ctx.Gin().GetHeader("APITS")

	if apiCI == "" || apiSI == "" || apiTS == "" {
		logs.Error(logPrefix, " 请求头缺失")
		return nil, nil, "", "", errors.New("请求头缺失")
	}

	// 验证Client ID
	if apiCI != l.clientId {
		logs.Error(logPrefix, " 非法的Client ID apiCI=", apiCI, " l.clientId=", l.clientId)
		return nil, nil, "", "", errors.New("非法的Client ID")
	}

	// 验证时间戳
	timestamp, err := strconv.ParseInt(apiTS, 10, 64)
	if err != nil {
		logs.Error(logPrefix, " 时间戳格式错误 apiTS=", apiTS, " err=", err.Error())
		return nil, nil, "", "", errors.New("时间戳格式错误")
	}

	// 检查时间戳是否在有效期内（30秒）
	currentTime := time.Now().Unix()
	if currentTime-timestamp > 30 || timestamp-currentTime > 30 {
		logs.Error(logPrefix, " 时间戳超出有效期 apiTS=", apiTS, " currentTime=", currentTime)
		return nil, nil, "", "", errors.New("请求超时")
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error(logPrefix, " 读取请求消息体错误 err=", err.Error())
		return nil, nil, "", "", errors.New("读取请求消息体错误")
	}

	// 解析请求参数
	formValues, err := url.ParseQuery(string(bodyBytes))
	if err != nil {
		logs.Error(logPrefix, " 解析表单数据错误 err=", err.Error())
		return nil, nil, "", "", errors.New("解析表单数据错误")
	}

	// 获取加密的消息
	encryptedMsg := formValues.Get("msg")
	if encryptedMsg == "" {
		logs.Error(logPrefix, " 缺少msg参数")
		return nil, nil, "", "", errors.New("缺少msg参数")
	}

	// 验证签名
	if !l.VerifySignature(apiTS, encryptedMsg, apiSI) {
		logs.Error(logPrefix, " 签名验证失败 apiTS=", apiTS, " encryptedMsg=", encryptedMsg, " apiSI=", apiSI)
		return nil, nil, "", "", errors.New("签名验证失败")
	}

	// 解密消息
	decryptedMsg, err := l.DecryptData(encryptedMsg)
	if err != nil {
		logs.Error(logPrefix, " 解密消息失败 err=", err.Error())
		return nil, nil, "", "", errors.New("解密消息失败")
	}

	s := "余额" //玩家余额请求刷新太频繁 不需要记录日志 投注、结算需要记录日志
	if !strings.Contains(logPrefix, s) {
		logs.Info(logPrefix, " Request.Body=", decryptedMsg)
	}

	return []byte(decryptedMsg), bodyBytes, apiTS, apiSI, nil
}

// ProcessEncryptedResponse 处理表单格式的加密响应
// 加密响应数据，生成签名，返回响应
// 返回: 错误信息
func (l *OFALiveSingleService) ProcessEncryptedResponse(ctx *abugo.AbuHttpContent, respdata interface{}, logPrefix string) error {
	// 将响应数据转换为JSON
	jsonData, err := json.Marshal(respdata)
	if err != nil {
		logs.Error(logPrefix, " 序列化响应数据失败 err=", err.Error())
		return errors.New("序列化响应数据失败")
	}

	// 加密响应数据
	encryptedData, err := l.EncryptData(string(jsonData))
	if err != nil {
		logs.Error(logPrefix, " 加密响应数据失败 err=", err.Error())
		return errors.New("加密响应数据失败")
	}
	// 返回响应
	ctx.RespJson(encryptedData)
	logs.Info(logPrefix, " 响应成功 response=", string(jsonData))
	return nil
}

func (l *OFALiveSingleService) BetRecord2String(reqdata OFARequestSettleData) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单号\":\"%v\",", reqdata.BetSn))
	sb.WriteString(fmt.Sprintf("\"玩家ID\":\"%v\",", reqdata.UserId))
	sb.WriteString(fmt.Sprintf("\"有效金额\":\"%v\",", reqdata.ValidMoney))
	sb.WriteString(fmt.Sprintf("\"赢取金额\":\"%v\",", reqdata.WinMoney))
	sb.WriteString(fmt.Sprintf("\"利润金额\":\"%v\",", reqdata.Profit))
	sb.WriteString(fmt.Sprintf("\"结算时间\":\"%v\",", reqdata.SettleTime))
	sb.WriteString(fmt.Sprintf("\"结算日期\":\"%v\",", reqdata.SettleDate))

	// 添加状态值文字描述 (2:未中獎,3: 中獎 ,4:和局
	switch reqdata.Status {
	case 2:
		sb.WriteString("\"状态\":\"未中奖\"")
	case 3:
		sb.WriteString("\"状态\":\"中奖\"")
	case 4:
		sb.WriteString("\"状态\":\"和局\"")
	default:
		sb.WriteString(fmt.Sprintf("\"状态\":\"%d\"", reqdata.Status))
	}

	sb.WriteString("}")
	res = sb.String()
	return
}
