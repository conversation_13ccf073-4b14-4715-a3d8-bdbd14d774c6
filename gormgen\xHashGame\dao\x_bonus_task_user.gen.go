// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXBonusTaskUser(db *gorm.DB, opts ...gen.DOOption) xBonusTaskUser {
	_xBonusTaskUser := xBonusTaskUser{}

	_xBonusTaskUser.xBonusTaskUserDo.UseDB(db, opts...)
	_xBonusTaskUser.xBonusTaskUserDo.UseModel(&model.XBonusTaskUser{})

	tableName := _xBonusTaskUser.xBonusTaskUserDo.TableName()
	_xBonusTaskUser.ALL = field.NewAsterisk(tableName)
	_xBonusTaskUser.ID = field.NewInt64(tableName, "Id")
	_xBonusTaskUser.UserID = field.NewInt32(tableName, "UserId")
	_xBonusTaskUser.Type = field.NewInt32(tableName, "Type")
	_xBonusTaskUser.Bonus = field.NewFloat64(tableName, "Bonus")
	_xBonusTaskUser.LiuShuiOdd = field.NewInt32(tableName, "LiuShuiOdd")
	_xBonusTaskUser.Memo = field.NewString(tableName, "Memo")
	_xBonusTaskUser.State = field.NewInt32(tableName, "State")
	_xBonusTaskUser.CreateTime = field.NewTime(tableName, "CreateTime")
	_xBonusTaskUser.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xBonusTaskUser.Condition1 = field.NewFloat64(tableName, "Condition1")
	_xBonusTaskUser.Condition2 = field.NewFloat64(tableName, "Condition2")
	_xBonusTaskUser.Condition3 = field.NewFloat64(tableName, "Condition3")
	_xBonusTaskUser.Progress1 = field.NewFloat64(tableName, "Progress1")
	_xBonusTaskUser.Progress2 = field.NewFloat64(tableName, "Progress2")
	_xBonusTaskUser.Progress3 = field.NewFloat64(tableName, "Progress3")
	_xBonusTaskUser.TimeLine = field.NewTime(tableName, "TimeLine")

	_xBonusTaskUser.fillFieldMap()

	return _xBonusTaskUser
}

type xBonusTaskUser struct {
	xBonusTaskUserDo xBonusTaskUserDo

	ALL        field.Asterisk
	ID         field.Int64
	UserID     field.Int32
	Type       field.Int32
	Bonus      field.Float64
	LiuShuiOdd field.Int32
	Memo       field.String
	State      field.Int32 // 1 开启 2关闭 3倒计时 4结束
	CreateTime field.Time
	UpdateTime field.Time
	Condition1 field.Float64
	Condition2 field.Float64
	Condition3 field.Float64
	Progress1  field.Float64
	Progress2  field.Float64
	Progress3  field.Float64
	TimeLine   field.Time

	fieldMap map[string]field.Expr
}

func (x xBonusTaskUser) Table(newTableName string) *xBonusTaskUser {
	x.xBonusTaskUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xBonusTaskUser) As(alias string) *xBonusTaskUser {
	x.xBonusTaskUserDo.DO = *(x.xBonusTaskUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xBonusTaskUser) updateTableName(table string) *xBonusTaskUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.Type = field.NewInt32(table, "Type")
	x.Bonus = field.NewFloat64(table, "Bonus")
	x.LiuShuiOdd = field.NewInt32(table, "LiuShuiOdd")
	x.Memo = field.NewString(table, "Memo")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.Condition1 = field.NewFloat64(table, "Condition1")
	x.Condition2 = field.NewFloat64(table, "Condition2")
	x.Condition3 = field.NewFloat64(table, "Condition3")
	x.Progress1 = field.NewFloat64(table, "Progress1")
	x.Progress2 = field.NewFloat64(table, "Progress2")
	x.Progress3 = field.NewFloat64(table, "Progress3")
	x.TimeLine = field.NewTime(table, "TimeLine")

	x.fillFieldMap()

	return x
}

func (x *xBonusTaskUser) WithContext(ctx context.Context) *xBonusTaskUserDo {
	return x.xBonusTaskUserDo.WithContext(ctx)
}

func (x xBonusTaskUser) TableName() string { return x.xBonusTaskUserDo.TableName() }

func (x xBonusTaskUser) Alias() string { return x.xBonusTaskUserDo.Alias() }

func (x xBonusTaskUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xBonusTaskUserDo.Columns(cols...)
}

func (x *xBonusTaskUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xBonusTaskUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 16)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Type"] = x.Type
	x.fieldMap["Bonus"] = x.Bonus
	x.fieldMap["LiuShuiOdd"] = x.LiuShuiOdd
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["Condition1"] = x.Condition1
	x.fieldMap["Condition2"] = x.Condition2
	x.fieldMap["Condition3"] = x.Condition3
	x.fieldMap["Progress1"] = x.Progress1
	x.fieldMap["Progress2"] = x.Progress2
	x.fieldMap["Progress3"] = x.Progress3
	x.fieldMap["TimeLine"] = x.TimeLine
}

func (x xBonusTaskUser) clone(db *gorm.DB) xBonusTaskUser {
	x.xBonusTaskUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xBonusTaskUser) replaceDB(db *gorm.DB) xBonusTaskUser {
	x.xBonusTaskUserDo.ReplaceDB(db)
	return x
}

type xBonusTaskUserDo struct{ gen.DO }

func (x xBonusTaskUserDo) Debug() *xBonusTaskUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xBonusTaskUserDo) WithContext(ctx context.Context) *xBonusTaskUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xBonusTaskUserDo) ReadDB() *xBonusTaskUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xBonusTaskUserDo) WriteDB() *xBonusTaskUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xBonusTaskUserDo) Session(config *gorm.Session) *xBonusTaskUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xBonusTaskUserDo) Clauses(conds ...clause.Expression) *xBonusTaskUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xBonusTaskUserDo) Returning(value interface{}, columns ...string) *xBonusTaskUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xBonusTaskUserDo) Not(conds ...gen.Condition) *xBonusTaskUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xBonusTaskUserDo) Or(conds ...gen.Condition) *xBonusTaskUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xBonusTaskUserDo) Select(conds ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xBonusTaskUserDo) Where(conds ...gen.Condition) *xBonusTaskUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xBonusTaskUserDo) Order(conds ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xBonusTaskUserDo) Distinct(cols ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xBonusTaskUserDo) Omit(cols ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xBonusTaskUserDo) Join(table schema.Tabler, on ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xBonusTaskUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xBonusTaskUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xBonusTaskUserDo) Group(cols ...field.Expr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xBonusTaskUserDo) Having(conds ...gen.Condition) *xBonusTaskUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xBonusTaskUserDo) Limit(limit int) *xBonusTaskUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xBonusTaskUserDo) Offset(offset int) *xBonusTaskUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xBonusTaskUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xBonusTaskUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xBonusTaskUserDo) Unscoped() *xBonusTaskUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xBonusTaskUserDo) Create(values ...*model.XBonusTaskUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xBonusTaskUserDo) CreateInBatches(values []*model.XBonusTaskUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xBonusTaskUserDo) Save(values ...*model.XBonusTaskUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xBonusTaskUserDo) First() (*model.XBonusTaskUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskUser), nil
	}
}

func (x xBonusTaskUserDo) Take() (*model.XBonusTaskUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskUser), nil
	}
}

func (x xBonusTaskUserDo) Last() (*model.XBonusTaskUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskUser), nil
	}
}

func (x xBonusTaskUserDo) Find() ([]*model.XBonusTaskUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XBonusTaskUser), err
}

func (x xBonusTaskUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XBonusTaskUser, err error) {
	buf := make([]*model.XBonusTaskUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xBonusTaskUserDo) FindInBatches(result *[]*model.XBonusTaskUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xBonusTaskUserDo) Attrs(attrs ...field.AssignExpr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xBonusTaskUserDo) Assign(attrs ...field.AssignExpr) *xBonusTaskUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xBonusTaskUserDo) Joins(fields ...field.RelationField) *xBonusTaskUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xBonusTaskUserDo) Preload(fields ...field.RelationField) *xBonusTaskUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xBonusTaskUserDo) FirstOrInit() (*model.XBonusTaskUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskUser), nil
	}
}

func (x xBonusTaskUserDo) FirstOrCreate() (*model.XBonusTaskUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XBonusTaskUser), nil
	}
}

func (x xBonusTaskUserDo) FindByPage(offset int, limit int) (result []*model.XBonusTaskUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xBonusTaskUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xBonusTaskUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xBonusTaskUserDo) Delete(models ...*model.XBonusTaskUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xBonusTaskUserDo) withDO(do gen.Dao) *xBonusTaskUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
