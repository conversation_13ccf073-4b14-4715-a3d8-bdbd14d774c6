package main

import (
	"xserver/controller"
	"xserver/controller/msg"
	paycontroller "xserver/controller/pay"
	"xserver/controller/roulette"
	"xserver/server"
)

func main() {
	server.Init()
	new(controller.UserController).Init()
	new(controller.GameController).Init()
	new(controller.AgentController).Init()
	new(controller.ActiveController).Init()
	new(controller.RankController).Init()
	new(controller.DuiHuanController).Init()
	new(controller.NoticeController).Init()
	new(controller.AppController).Init()
	//new(controller.SocketController).Init()
	controller.SocketHandler.Init()
	new(controller.DappController).Init()
	new(controller.LotteryController).Init()
	new(controller.ThirdController).Init()
	new(controller.ChatController).Init()
	new(controller.VipController).Init()
	new(controller.LuZhiController).Init()
	new(paycontroller.PayController).Init()
	//new(controller.PandaController).Init()
	new(controller.RecordController).Init()
	new(controller.RedisController).Init()
	new(controller.CellxpertController).Init()
	new(controller.UtOrderController).Init()
	new(controller.ShuangZhuangController).Init()
	new(controller.SumsubController).Init()
	new(roulette.GameController).Init()
	new(controller.OnlineSocketController).Init()
	new(controller.LiveController).Init()
	new(msg.UserMessageController).Init()
	server.Run()
}

/*
	go env -w GOARCH=amd64
	go env -w GOOS=windows
	go env -w GOOS=linux
*/
