// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCodeT1(db *gorm.DB, opts ...gen.DOOption) xAgentCodeT1 {
	_xAgentCodeT1 := xAgentCodeT1{}

	_xAgentCodeT1.xAgentCodeT1Do.UseDB(db, opts...)
	_xAgentCodeT1.xAgentCodeT1Do.UseModel(&model.XAgentCodeT1{})

	tableName := _xAgentCodeT1.xAgentCodeT1Do.TableName()
	_xAgentCodeT1.ALL = field.NewAsterisk(tableName)
	_xAgentCodeT1.ID = field.NewInt32(tableName, "Id")
	_xAgentCodeT1.UserID = field.NewInt32(tableName, "UserId")
	_xAgentCodeT1.AgentCode = field.NewString(tableName, "AgentCode")
	_xAgentCodeT1.FenCheng = field.NewString(tableName, "FenCheng")
	_xAgentCodeT1.RegisterCount = field.NewInt32(tableName, "RegisterCount")
	_xAgentCodeT1.BetCount = field.NewInt32(tableName, "BetCount")
	_xAgentCodeT1.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCodeT1.Memo = field.NewString(tableName, "Memo")

	_xAgentCodeT1.fillFieldMap()

	return _xAgentCodeT1
}

type xAgentCodeT1 struct {
	xAgentCodeT1Do xAgentCodeT1Do

	ALL           field.Asterisk
	ID            field.Int32  // id
	UserID        field.Int32  // 玩家id
	AgentCode     field.String // 推广码
	FenCheng      field.String // 分成比例
	RegisterCount field.Int32  // 注册人数
	BetCount      field.Int32  // 投注人数
	CreateTime    field.Time   // 创建时间
	Memo          field.String

	fieldMap map[string]field.Expr
}

func (x xAgentCodeT1) Table(newTableName string) *xAgentCodeT1 {
	x.xAgentCodeT1Do.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCodeT1) As(alias string) *xAgentCodeT1 {
	x.xAgentCodeT1Do.DO = *(x.xAgentCodeT1Do.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCodeT1) updateTableName(table string) *xAgentCodeT1 {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.AgentCode = field.NewString(table, "AgentCode")
	x.FenCheng = field.NewString(table, "FenCheng")
	x.RegisterCount = field.NewInt32(table, "RegisterCount")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Memo = field.NewString(table, "Memo")

	x.fillFieldMap()

	return x
}

func (x *xAgentCodeT1) WithContext(ctx context.Context) *xAgentCodeT1Do {
	return x.xAgentCodeT1Do.WithContext(ctx)
}

func (x xAgentCodeT1) TableName() string { return x.xAgentCodeT1Do.TableName() }

func (x xAgentCodeT1) Alias() string { return x.xAgentCodeT1Do.Alias() }

func (x xAgentCodeT1) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCodeT1Do.Columns(cols...)
}

func (x *xAgentCodeT1) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCodeT1) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AgentCode"] = x.AgentCode
	x.fieldMap["FenCheng"] = x.FenCheng
	x.fieldMap["RegisterCount"] = x.RegisterCount
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Memo"] = x.Memo
}

func (x xAgentCodeT1) clone(db *gorm.DB) xAgentCodeT1 {
	x.xAgentCodeT1Do.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCodeT1) replaceDB(db *gorm.DB) xAgentCodeT1 {
	x.xAgentCodeT1Do.ReplaceDB(db)
	return x
}

type xAgentCodeT1Do struct{ gen.DO }

func (x xAgentCodeT1Do) Debug() *xAgentCodeT1Do {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCodeT1Do) WithContext(ctx context.Context) *xAgentCodeT1Do {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCodeT1Do) ReadDB() *xAgentCodeT1Do {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCodeT1Do) WriteDB() *xAgentCodeT1Do {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCodeT1Do) Session(config *gorm.Session) *xAgentCodeT1Do {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCodeT1Do) Clauses(conds ...clause.Expression) *xAgentCodeT1Do {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCodeT1Do) Returning(value interface{}, columns ...string) *xAgentCodeT1Do {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCodeT1Do) Not(conds ...gen.Condition) *xAgentCodeT1Do {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCodeT1Do) Or(conds ...gen.Condition) *xAgentCodeT1Do {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCodeT1Do) Select(conds ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCodeT1Do) Where(conds ...gen.Condition) *xAgentCodeT1Do {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCodeT1Do) Order(conds ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCodeT1Do) Distinct(cols ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCodeT1Do) Omit(cols ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCodeT1Do) Join(table schema.Tabler, on ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCodeT1Do) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCodeT1Do) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCodeT1Do) Group(cols ...field.Expr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCodeT1Do) Having(conds ...gen.Condition) *xAgentCodeT1Do {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCodeT1Do) Limit(limit int) *xAgentCodeT1Do {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCodeT1Do) Offset(offset int) *xAgentCodeT1Do {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCodeT1Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCodeT1Do {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCodeT1Do) Unscoped() *xAgentCodeT1Do {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCodeT1Do) Create(values ...*model.XAgentCodeT1) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCodeT1Do) CreateInBatches(values []*model.XAgentCodeT1, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCodeT1Do) Save(values ...*model.XAgentCodeT1) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCodeT1Do) First() (*model.XAgentCodeT1, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCodeT1), nil
	}
}

func (x xAgentCodeT1Do) Take() (*model.XAgentCodeT1, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCodeT1), nil
	}
}

func (x xAgentCodeT1Do) Last() (*model.XAgentCodeT1, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCodeT1), nil
	}
}

func (x xAgentCodeT1Do) Find() ([]*model.XAgentCodeT1, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCodeT1), err
}

func (x xAgentCodeT1Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCodeT1, err error) {
	buf := make([]*model.XAgentCodeT1, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCodeT1Do) FindInBatches(result *[]*model.XAgentCodeT1, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCodeT1Do) Attrs(attrs ...field.AssignExpr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCodeT1Do) Assign(attrs ...field.AssignExpr) *xAgentCodeT1Do {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCodeT1Do) Joins(fields ...field.RelationField) *xAgentCodeT1Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCodeT1Do) Preload(fields ...field.RelationField) *xAgentCodeT1Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCodeT1Do) FirstOrInit() (*model.XAgentCodeT1, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCodeT1), nil
	}
}

func (x xAgentCodeT1Do) FirstOrCreate() (*model.XAgentCodeT1, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCodeT1), nil
	}
}

func (x xAgentCodeT1Do) FindByPage(offset int, limit int) (result []*model.XAgentCodeT1, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCodeT1Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCodeT1Do) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCodeT1Do) Delete(models ...*model.XAgentCodeT1) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCodeT1Do) withDO(do gen.Dao) *xAgentCodeT1Do {
	x.DO = *do.(*gen.DO)
	return x
}
