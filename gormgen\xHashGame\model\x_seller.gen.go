// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXSeller = "x_seller"

// XSeller mapped from table <x_seller>
type XSeller struct {
	SellerID      int32     `gorm:"column:SellerId;primaryKey;autoIncrement:true;comment:运营商" json:"SellerId"`           // 运营商
	SellerName    string    `gorm:"column:SellerName;comment:运营名称" json:"SellerName"`                                    // 运营名称
	State         int32     `gorm:"column:State;default:1;comment:状态 1启用 2禁用" json:"State"`                              // 状态 1启用 2禁用
	Remark        string    `gorm:"column:Remark;comment:备注" json:"Remark"`                                              // 备注
	CreateTime    time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	Icon          string    `gorm:"column:Icon;comment:标签ICON" json:"Icon"`                                              // 标签ICON
	Logo          string    `gorm:"column:Logo;comment:Logo" json:"Logo"`                                                // Logo
	Logo2         string    `gorm:"column:Logo2;comment:Logo2" json:"Logo2"`                                             // Logo2
	ShowName      string    `gorm:"column:ShowName;comment:显示名称" json:"ShowName"`                                        // 显示名称
	SampleName    string    `gorm:"column:SampleName;comment:运营商简称" json:"SampleName"`                                   // 运营商简称
	SocialLinks   string    `gorm:"column:SocialLinks;comment:社媒链接" json:"SocialLinks"`                                  // 社媒链接
	IosIcon       string    `gorm:"column:IosIcon;comment:标签ICON" json:"IosIcon"`                                        // 标签ICON
	ThirdAuth     string    `gorm:"column:ThirdAuth;comment:三方登录appId" json:"ThirdAuth"`                                 // 三方登录appId
	CustomerLinks string    `gorm:"column:CustomerLinks;comment:客服链接" json:"CustomerLinks"`                              // 客服链接
}

// TableName XSeller's table name
func (*XSeller) TableName() string {
	return TableNameXSeller
}
