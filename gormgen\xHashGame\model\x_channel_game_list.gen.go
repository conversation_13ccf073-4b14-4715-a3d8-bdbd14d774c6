// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXChannelGameList = "x_channel_game_list"

// XChannelGameList mapped from table <x_channel_game_list>
type XChannelGameList struct {
	HostID     int32     `gorm:"column:HostId;primaryKey;comment:域名Id 来源x_channel_host的Id" json:"HostId"`             // 域名Id 来源x_channel_host的Id
	Brand      string    `gorm:"column:Brand;primaryKey;comment:品牌" json:"Brand"`                                     // 品牌
	GameID     string    `gorm:"column:GameId;primaryKey;comment:游戏Id" json:"GameId"`                                 // 游戏Id
	ChannelID  int32     `gorm:"column:ChannelId;not null;comment:渠道Id" json:"ChannelId"`                             // 渠道Id
	Host       string    `gorm:"column:Host;not null;comment:域名" json:"Host"`                                         // 域名
	Sort       int32     `gorm:"column:Sort;comment:排序,数字越大越靠前" json:"Sort"`                                          // 排序,数字越大越靠前
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XChannelGameList's table name
func (*XChannelGameList) TableName() string {
	return TableNameXChannelGameList
}
