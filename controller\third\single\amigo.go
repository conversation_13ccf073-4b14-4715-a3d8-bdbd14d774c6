package single

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// AMIGO单一钱包类 日本弹珠

type AmigoSingleService struct {
	apiUrl                string
	reportUrl             string
	appUrl                string // 客户端登陆url
	agentId               string
	key                   string
	walletKey             string
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
}

func NewAmigoSingleService(params map[string]string, fc func(int) error) *AmigoSingleService {
	return &AmigoSingleService{
		apiUrl:                params["api_url"],
		reportUrl:             params["report_url"],
		appUrl:                params["app_url"],
		agentId:               params["agent_id"],
		key:                   params["key"],
		walletKey:             params["wallet_key"],
		currency:              params["currency"],
		brandName:             "amigo",
		RefreshUserAmountFunc: fc,
	}
}

const cacheKeyAmigo = "cacheKeyAmigo:"

// 支持的语言 lang 目前支持 cn=简体中文,tc=繁体中文,ja=日文
var amigoSupportLang = []string{"cn", "en", "tc", "ja"}

// 登录游戏 /foreign/login
func (l *AmigoSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId"`
		LangCode string `json:"LangCode"`
		HomeUrl  string `json:"HomeUrl"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("amigo_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	loginLang := ""
	if reqdata.LangCode != "" {
		for _, v := range amigoSupportLang {
			if v == reqdata.LangCode {
				loginLang = reqdata.LangCode
				break
			}
		}
		if loginLang == "" {
			loginLang = "en"
		}
	} else {
		loginLang = "en"
	}
	homeUrl := reqdata.HomeUrl
	if homeUrl == "" {
		homeUrl = ctx.Gin().Request.Host
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyAmigo, token.UserId); err != nil {
		logs.Error("amigo_single 登录游戏 获取用户登录状态错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 先注册用户
	if !l.register(token.UserId) {
		logs.Error("amigo_single 登录游戏 注册用户失败 userId=", token.UserId)
		ctx.RespErrString(true, &errcode, "服务器异常，请稍后再试")
		return
	}

	loginUserName := fmt.Sprintf("%s_%d", strings.ToUpper(l.agentId), token.UserId) // 用户名（用户名前方必须增加代理号，比如代理号为abc,则账号为大写的ABCXXX，统一为大写）
	loginPartnerId := l.agentId
	loginExternalToken := l.getTokenByUser(token.UserId, token.Account)
	if loginExternalToken == "" {
		logs.Error("amigo_single 登录游戏 生成token失败", token.UserId)
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}

	urlParamData, loginChecksum := l.getSign(map[string]interface{}{
		"userName":  loginUserName,
		"partnerId": loginPartnerId,
	})
	urlParamData += fmt.Sprintf("&externalToken=%s", loginExternalToken)
	urlParamData += fmt.Sprintf("&checksum=%s", loginChecksum)

	logs.Info("amigo_single 登录游戏 开始 userId=", token.UserId, " reqdata=", reqdata, " urlParamData=", urlParamData)

	type ResponseLoginData struct {
		IsSuccess bool   `json:"IsSuccess"`
		Message   string `json:"Message"`
		Value     struct {
			Token        string `json:"token"`
			RefreshToken string `json:"refreshToken"`
		} `json:"Value"`
	}
	respLoginData := ResponseLoginData{}

	client := &http.Client{}
	loginUrl := fmt.Sprintf("%s/foreign/login?%s", l.apiUrl, urlParamData)
	req, _ := http.NewRequest(http.MethodGet, loginUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 登录游戏 请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 登录游戏 读取响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("amigo_single 登录游戏 请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respLoginData)
	if err != nil {
		logs.Error("amigo_single 登录游戏 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if respLoginData.IsSuccess {
		lanchUrl := fmt.Sprintf("%s?token=%s", l.appUrl, respLoginData.Value.Token)
		if homeUrl != "" {
			homeUrl = regexp.MustCompile(`^((http://)|(https://))?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(/)`).FindString(homeUrl)
			lanchUrl += fmt.Sprintf("&home=%s", homeUrl)
		}
		if loginLang != "" {
			lanchUrl += fmt.Sprintf("&lang=%s", loginLang)
		}
		if reqdata.GameId != "" {
			lanchUrl += fmt.Sprintf("&gameId=%s", reqdata.GameId)
		}
		ctx.RespOK(lanchUrl)
		return
	}
	logs.Error("amigo_single 登录游戏 登录失败 错误信息=", respLoginData.Message)
	ctx.RespErrString(true, &errcode, respLoginData.Message)
	return
}

// 注册用户 /foreign/register
func (l *AmigoSingleService) register(userId int) bool {
	registerNickname := fmt.Sprintf("%d", userId)
	registerPartnerId := l.agentId
	registerPassword := fmt.Sprintf("%d_%d", userId, 100000+rand.New(rand.NewSource(time.Now().UnixNano())).Intn(899999)) // 密码
	registerUserName := fmt.Sprintf("%s_%d", strings.ToUpper(l.agentId), userId)                                          // 用户名（用户名前方必须增加代理号，比如代理号为abc,则账号为大写的ABCXXX，统一为大写）
	urlParamData, loginChecksum := l.getSign(map[string]interface{}{
		"nickname":  registerNickname,
		"partnerId": registerPartnerId,
		"password":  registerPassword,
		"userName":  registerUserName,
	})
	urlParamData += fmt.Sprintf("&currency=%s", l.currency) // 账号生成币种,默认值为TWD，可选（不参与签名）
	urlParamData += fmt.Sprintf("&checksum=%s", loginChecksum)

	type ResponseData struct {
		IsSuccess bool        `json:"IsSuccess"`
		Message   string      `json:"Message"`
		Value     interface{} `json:"Value"`
	}
	respdata := ResponseData{}

	client := &http.Client{}
	registerUrl := fmt.Sprintf("%s/foreign/register?%s", l.apiUrl, urlParamData)
	req, _ := http.NewRequest(http.MethodGet, registerUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 注册用户 请求错误 err=", err.Error())
		return false
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 注册用户 读取响应错误 err=", err.Error())
		return false
	}
	logs.Info("amigo_single 注册用户 请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("amigo_single 注册用户 解析响应消息体错误 err=", err.Error())
		return false
	}
	if !respdata.IsSuccess {
		logs.Error("amigo_single 注册用户 错误 respdata=", respdata)
	}
	return respdata.IsSuccess
}

// 计算签名字符串
func (l *AmigoSingleService) getSign(data map[string]interface{}) (paramStr, sign string) {
	keySlice := make([]string, 0, len(data))
	for k := range data {
		keySlice = append(keySlice, k)
	}
	sort.Slice(keySlice, func(i, j int) bool {
		return keySlice[i] < keySlice[j]
	})
	src := ""
	for _, k := range keySlice {
		src += fmt.Sprintf("%s:%v,", k, data[k])
		paramStr += fmt.Sprintf("%s=%v&", k, data[k])
	}
	src += l.key
	sign = base.MD5(src)
	return
}

// 生成amigo token
func (l *AmigoSingleService) getTokenByUser(userId int, account string) (token string) {
	type AmigoTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyAmigo + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyAmigo + token
		err := server.Redis().Expire(rKeyToken, 86500)
		if err != nil {
			logs.Error("amigo_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " error=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("amigo_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " error=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "amigo_" + uuid.NewString()
	tokendata := AmigoTokenData{
		UserId:  userId,
		Account: account,
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyAmigo + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("amigo_single getTokenByUser set redis rKeyUser=", rKeyUser, " error:%s", err)
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86500); err != nil {
		logs.Error("amigo_single getTokenByUser set redis rKeyToken=", rKeyToken, " error:%s", err)
		token = ""
		return
	}
	return
}

// 验证token
func (l *AmigoSingleService) getUserByToken(token string) (userId int, account string) {
	if token == "leotesttoken123456" {
		userId = ********
		account = "asen0525"
		return
	}
	type AmigoTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyAmigo + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := AmigoTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("Amigo_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " error=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("Amigo_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// get_balance 获取玩家余额 /amigo/external/getCurrency
func (l *AmigoSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserName string `json:"userName" validate:"required"`
		Token    string `json:"token" validate:"required"`
		Currency string `json:"currency" validate:"required"`
	}
	type ResponseData struct {
		IsSuccess bool   `json:"IsSuccess"`
		Message   string `json:"Message"`
		Value     struct {
			Money    float64 `json:"money"`    // 用户余额(最多支持4位小数)
			Currency string  `json:"currency"` // 币种
		} `json:"Value"`
	}
	respdata := ResponseData{
		IsSuccess: false,
		Message:   "",
	}
	respdata.Value.Money = 0
	respdata.Value.Currency = l.currency

	urlData := l.getUrlParam(ctx)
	urlStr := ctx.Gin().Request.URL.String()
	logs.Info("amigo_single 获取玩家余额 urlStr=", urlStr)
	reqdata := RequestData{}
	if v, ok := urlData["userName"]; !ok {
		logs.Error("amigo_single 获取玩家余额 请求参数错误 userName不存在 urlStr=", urlStr)
		respdata.Message = "userName not exist"
		ctx.RespJson(respdata)
		return
	} else {
		reqdata.UserName = v
	}
	if v, ok := urlData["token"]; !ok {
		logs.Error("amigo_single 获取玩家余额 请求参数错误 token不存在 urlStr=", urlStr)
		respdata.Message = "token not exist"
		ctx.RespJson(respdata)
		return
	} else {
		reqdata.Token = v
	}
	if v, ok := urlData["currency"]; !ok {
		logs.Error("amigo_single 获取玩家余额 请求参数错误 currency不存在 urlStr=", urlStr)
		respdata.Message = "currency not exist"
		ctx.RespJson(respdata)
		return
	} else {
		reqdata.Currency = v
	}

	if reqdata.Currency != "null" && reqdata.Currency != l.currency {
		logs.Error("amigo_single 获取玩家余额 不支持币种=", reqdata.Currency)
		respdata.Message = "illegal currency"
		ctx.RespJson(respdata)
		return
	}
	// 校验签名 没有签名校验

	// 获取用户ID
	userId, _ := l.getUserByToken(reqdata.Token)
	if userId <= 0 {
		logs.Error("amigo_single 玩家身份验证 用户未登录 userId=", userId)
		respdata.Message = "token expired"
		ctx.RespJson(respdata)
		return
	}
	userNameStr := fmt.Sprintf("%s_%d", strings.ToUpper(l.agentId), userId)
	if userNameStr != reqdata.UserName {
		logs.Error("amigo_single 获取玩家余额 用户名不匹配 userNameStr=", userNameStr, " reqdata.UserName=", reqdata.UserName)
		respdata.Message = "token not match userName"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err := server.Db().GormDao().Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("amigo_single 获取玩家余额 用户不存在 userId=", userId)
		respdata.Message = "user not exist"
		ctx.RespJson(respdata)
		return
	}

	respdata.IsSuccess = true
	respdata.Message = "success"
	respdata.Value.Money = userBalance.Amount
	respdata.Value.Currency = l.currency
	ctx.RespJson(respdata)
	logs.Info("amigo_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

// change_balance 改变玩家余额 /amigo/external/moneyTransfer
func (l *AmigoSingleService) ChangeBalance(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("amigo_single 改变玩家余额 获取原始数据失败 ", " err=", err)
		ctx.RespErr(err, nil)
		return
	}
	logs.Info("amigo_single 改变玩家余额:", string(bodyBytes))

	type RequestData struct {
		UserName      string  `json:"userName" validate:"required"`      // 用户名（用户名前方必须增加代理号，比如代理号为abc,则账号为大写的ABCXXX，统一为大写）
		Token         string  `json:"token" validate:"required"`         // 外部网站token
		Currency      string  `json:"currency" validate:"required"`      // 法币币种
		Money         float64 `json:"money" validate:"required"`         // 转换金额(投注为负 中奖为正/外赠为正/其他可为正或者负)
		Action        string  `json:"action" validate:"required"`        // 操作类型扣钱/增钱/投注/中奖/外赠/其他 bet/win/reward/other
		TransactionId string  `json:"transactionId" validate:"required"` // 订单号
		MachineId     int     `json:"machineId"`                         // 机台id
		GameId        int     `json:"gameId"`                            // 游戏id
		GameCode      string  `json:"gameCode"`                          // 游戏编码
		GameType      string  `json:"gameType"`                          // 游戏类型
		WalletId      int     `json:"walletId" validate:"required"`      // 钱包id
		Time          int64   `json:"time" validate:"required"`          // 时间戳
		PlfId         int     `json:"plfId" validate:"required"`         // 平台ID
		Sign          string  `json:"sign" validate:"required"`          // 签名
		TableId       string  `json:"tableId"`                           // 本轮的id
	}
	type ResponseData struct {
		IsSuccess bool   `json:"IsSuccess"`
		Message   string `json:"Message"`
		Value     struct {
			Money      float64 `json:"money"`      // 交易金额
			TradeNo    string  `json:"tradeNo"`    // 交易订单号
			AfterMoney float64 `json:"afterMoney"` // 交易后余额
			Currency   string  `json:"currency"`   // 币种
		} `json:"Value"`
	}
	respdata := ResponseData{
		IsSuccess: false,
		Message:   "",
	}
	respdata.Value.Currency = l.currency

	var rawData struct { //日期需要单独解析 money各种值影响验签 =1 1.00 1.0001 1.0000
		Money json.RawMessage `json:"money"`
	}
	if err := json.Unmarshal(bodyBytes, &rawData); err != nil {
		logs.Error("amigo_single 改变玩家余额 解析原始数据失败 err=", err.Error())
		respdata.Message = "invalid request parameters"
		ctx.RespJson(respdata)
		return
	}

	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.UseNumber() // 使用 Number 类型来保持数字的原始格式
	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		logs.Error("amigo_single 改变玩家余额 请求参数错误 err=", err.Error())
		respdata.Message = "invalid request parameters"
		ctx.RespJson(respdata)
		return
	}

	// 验证签名
	params := make(map[string]interface{})
	params["userName"] = reqdata.UserName
	params["token"] = reqdata.Token
	params["currency"] = reqdata.Currency
	params["money"] = string(bytes.TrimSpace(rawData.Money)) // 使用原始 JSON 值
	params["action"] = reqdata.Action
	params["transactionId"] = reqdata.TransactionId
	params["walletId"] = reqdata.WalletId
	params["time"] = reqdata.Time
	params["plfId"] = reqdata.PlfId

	// 按字母顺序排序参数
	keySlice := make([]string, 0)
	for k, v := range params {
		if v != nil && v != "" {
			keySlice = append(keySlice, k)
		}
	}
	sort.Strings(keySlice)

	// 构建签名字符串
	src := ""
	for _, k := range keySlice {
		src += fmt.Sprintf("%s=%v&", k, params[k])
	}
	src += fmt.Sprintf("secret=%s", l.walletKey)
	logs.Info("amigo_single 签名字符串:", src)

	expectedSign := strings.ToLower(base.MD5(src))
	requestSign := strings.ToLower(reqdata.Sign)
	logs.Info("amigo_single 期望签名:", expectedSign)
	logs.Info("amigo_single 实际签名:", requestSign)

	if expectedSign != requestSign {
		logs.Error("amigo_single 改变玩家余额 签名验证失败 expected=", expectedSign, " actual=", requestSign)
		respdata.Message = "invalid signature"
		ctx.RespJson(respdata)
		return
	}

	defer func() {
		// 记录请求响应日志
		tmpCode := 0
		if !respdata.IsSuccess {
			tmpCode = 1
		}
		tmpBodyBytes, _ := json.Marshal(reqdata)
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.TransactionId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(tmpBodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          tmpCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("amigo_single 改变玩家余额 记录请求响应日志失败 TransactionId=", reqdata.TransactionId, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if reqdata.Currency != l.currency {
		logs.Error("amigo_single 改变玩家余额 不支持币种=", reqdata.Currency)
		respdata.Message = "illegal currency"
		ctx.RespJson(respdata)
		return
	}
	//if reqdata.TableId == "" {
	//	logs.Error("amigo_single 改变玩家余额 TableId为空")
	//	respdata.Message = "TableId is empty"
	//	ctx.RespJson(respdata)
	//	return
	//}

	// 校验参数
	if reqdata.Action == "bet" { // 投注
		if reqdata.Money < 0 { // 投注金额必须是正数
			logs.Error("amigo_single 改变玩家余额 投注金额不能大于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.TransactionId)
			respdata.Message = "bet money must be positive"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Action == "win" { // 中奖
		if reqdata.Money < 0 { // 中奖金额必须是正数
			logs.Error("amigo_single 改变玩家余额 中奖金额不能小于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.TransactionId)
			respdata.Message = "win money must be positive"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Action == "reward" { // 外赠
		if reqdata.Money <= 0 { // 外赠金额必须是正数
			logs.Error("amigo_single 改变玩家余额 外赠金额不能小于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.TransactionId)
			respdata.Message = "reward money must be positive"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Action == "other" { // 其他
		// money可以为正数或者负数
		if reqdata.Money == 0 { // 其他金额是0没有意义
			logs.Error("amigo_single 改变玩家余额 其他金额不能等于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.TransactionId)
			respdata.Message = "other money can not be 0"
			ctx.RespJson(respdata)
			return
		}
	} else {
		logs.Error("amigo_single 改变玩家余额 不支持的 action reqdata.Action=", reqdata.Action, " thirdId=", reqdata.TransactionId)
		respdata.IsSuccess = false
		respdata.Message = "action not support"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户ID
	userId, _ := l.getUserByToken(reqdata.Token)
	if userId <= 0 {
		logs.Error("amigo_single 改变玩家余额 用户未登录 userId=", userId, " thirdId=", reqdata.TransactionId)
		respdata.Message = "token expired"
		ctx.RespJson(respdata)
		return
	}
	userNameStr := fmt.Sprintf("%s_%d", strings.ToUpper(l.agentId), userId)
	if userNameStr != reqdata.UserName {
		logs.Error("amigo_single 改变玩家余额 用户名不匹配 userNameStr=", userNameStr, " reqdata.UserName=", reqdata.UserName, " thirdId=", reqdata.TransactionId)
		respdata.Message = "token not match userName"
		ctx.RespJson(respdata)
		return
	}

	thirdId := reqdata.TableId
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	gameId := fmt.Sprintf("%d", reqdata.GameId)
	if reqdata.Action == "reward" {
		thirdId = reqdata.TransactionId
	} else if reqdata.Action == "other" {
		thirdId = reqdata.TransactionId
	}
	// 判断游戏是否存在
	// gameInfo := thirdGameModel.GameList{}
	// err := server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).First(&gameInfo).Error
	// if err != nil {
	// 	logs.Error("amigo_single 改变玩家余额 游戏不存在 gameId=", gameId, " error=", err.Error())
	// 	respdata.Message = "game not exist"
	// 	ctx.RespJson(respdata)
	// 	return
	// }
	// gameName := gameInfo.Name
	// if gameInfo.GameType != 1 {
	// 	logs.Error("amigo_single 改变玩家余额 游戏类型错误 gameId=", gameId, " gameType=", gameInfo.GameType)
	// 	respdata.Message = "gameType error"
	// 	ctx.RespJson(respdata)
	// 	return
	// }
	gameName := ""

	// 日本弹珠都是电子类型
	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		userBalance := thirdGameModel.UserBalance{}
		// 锁定用户余额
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("amigo_single 改变玩家余额 锁定用户余额失败 thirdId=", thirdId, " userId=", userId, " error=", e.Error())
			respdata.Message = "lock user balance fail"
			ctx.RespJson(respdata)
			return e
		}
		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

		switch reqdata.Action {
		case "bet": // 投注
			if userBalance.Amount-reqdata.Money < 0 {
				e = fmt.Errorf("余额不足")
				logs.Error("amigo_single 改变玩家余额 投注 余额不足 thirdId=", thirdId, " userId=", userId, " balance=", userBalance.Amount, " reqdata=", reqdata)
				respdata.Message = "not enough balance"
				ctx.RespJson(respdata)
				return e
			}

			order := thirdGameModel.ThirdOrder{}
			// 查询投注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("amigo_single 改变玩家余额 投注 查询订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Message = "查询订单失败"
				ctx.RespJson(respdata)
				return e
			}

			if e != nil {
				// 订单不存在 创建订单

				order = thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    reqdata.Money,
					WinAmount:    0,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					// RawData:    string(bodyBytes),
					State:      1,
					Fee:        0,
					DataState:  -1, //未开奖
					CreateTime: thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 投注 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				// 订单已存在
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("amigo_single 改变玩家余额 投注 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}

				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"BetAmount": daogorm.Expr("BetAmount + ?", reqdata.Money),
					"ThirdTime": thirdTime,
					// "RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				})
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 投注 更新订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "update order fail"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 下注扣费
			resultTmp := tx.Table("x_user").Where("UserId = ? and Amount>=?", userId, reqdata.Money).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", reqdata.Money),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 投注 更新用户余额失败 thirdId=", thirdId, " userId=", userId, " error=", e.Error())
				respdata.Message = "update user balance fail"
				ctx.RespJson(respdata)
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       -reqdata.Money,
				AfterAmount:  userBalance.Amount - reqdata.Money,
				Reason:       utils.BalanceCReasonAmigoBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId + ",tid:" + reqdata.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 投注 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Message = "create amount change log fail"
				ctx.RespJson(respdata)
				return e
			}

			respdata.IsSuccess = true
			respdata.Message = "success"
			respdata.Value.Money = reqdata.Money
			respdata.Value.TradeNo = fmt.Sprintf("%d", amountLog.Id)
			respdata.Value.AfterMoney = userBalance.Amount - reqdata.Money
			respdata.Value.Currency = l.currency
			logs.Info("amigo_single 改变玩家余额 投注 thirdId=", thirdId, " userId=", userId, " 投注成功 reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		case "win": // 中奖
			order := thirdGameModel.ThirdOrder{}
			// 查询订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("amigo_single 改变玩家余额 中奖 查询订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Message = "order not exist"
				ctx.RespJson(respdata)
				return e
			}

			if e != nil {
				// 订单不存在 创建投注订单
				order = thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    0,
					WinAmount:    reqdata.Money,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					// RawData:    string(bodyBytes),
					State:      1,
					Fee:        0,
					DataState:  -1, //未开奖
					CreateTime: thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 中奖 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("amigo_single 改变玩家余额 中奖 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}
				// 中奖
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"WinAmount": daogorm.Expr("WinAmount + ?", reqdata.Money),
					"ThirdTime": thirdTime,
					// "RawData":   string(bodyBytes),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 中奖 更改预设订单失败 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "settle order fail"
					ctx.RespJson(respdata)
					return e
				}
			}

			// 中奖添加余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 中奖 更新用户余额失败 thirdId=", thirdId, " userId=", userId, " error=", e.Error())
				respdata.Message = "更新用户余额失败"
				ctx.RespJson(respdata)
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonAmigoWin,
				Memo:         l.brandName + " win,thirdId:" + thirdId + ",tid:" + reqdata.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 中奖 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Message = "创建账变记录失败"
				ctx.RespJson(respdata)
				return e
			}

			respdata.IsSuccess = true
			respdata.Message = "success"
			respdata.Value.Money = reqdata.Money
			respdata.Value.TradeNo = fmt.Sprintf("%d", amountLog.Id)
			respdata.Value.AfterMoney = userBalance.Amount + reqdata.Money
			respdata.Value.Currency = l.currency
			logs.Info("amigo_single 改变玩家余额 中奖 thirdId=", thirdId, " userId=", userId, " 中奖成功 reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		case "reward": // 外赠
			order := thirdGameModel.ThirdOrder{}
			// 查询订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("amigo_single 改变玩家余额 外赠 查询订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Message = "查询订单失败"
				ctx.RespJson(respdata)
				return e
			}

			if e != nil {
				// 相当于赠送用户彩金 直接添加一个注单 添加用户余额就行
				// 创建送彩金订单
				order = thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    0,
					WinAmount:    reqdata.Money,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    1, // 已开奖
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 外赠 创建中间订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 外赠 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				// 订单已存在
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("amigo_single 改变玩家余额 外赠 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}

				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"WinAmount": daogorm.Expr("WinAmount + ?", reqdata.Money),
					"ThirdTime": thirdTime,
					"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				})
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 外赠 更新订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "更新订单失败"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 添加用户余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 外赠 更新用户余额失败 thirdId=", thirdId, " userId=", userId, " error=", e.Error())
				respdata.Message = "更新用户余额失败"
				ctx.RespJson(respdata)
				return e
			}

			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonAmigoReward,
				Memo:         l.brandName + " reward,thirdId:" + thirdId + ",tid:" + reqdata.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 外赠 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Message = "创建账变记录失败"
				ctx.RespJson(respdata)
				return e
			}

			respdata.IsSuccess = true
			respdata.Message = "success"
			respdata.Value.Money = reqdata.Money

			respdata.Value.TradeNo = fmt.Sprintf("%d", amountLog.Id)
			respdata.Value.AfterMoney = userBalance.Amount + reqdata.Money
			respdata.Value.Currency = l.currency
			logs.Info("amigo_single 改变玩家余额 外赠 thirdId=", thirdId, " userId=", userId, " reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		case "other": //
			if userBalance.Amount+reqdata.Money < 0 {
				e = fmt.Errorf("余额不足")
				logs.Error("amigo_single 改变玩家余额 其他 余额不足 thirdId=", thirdId, " userId=", userId, " balance=", userBalance.Amount, " reqdata=", reqdata)
				respdata.Message = "余额不足"
				ctx.RespJson(respdata)
				return e
			}

			order := thirdGameModel.ThirdOrder{}
			// 查询订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("amigo_single 改变玩家余额 其他 查询订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Message = "查询订单失败"
				ctx.RespJson(respdata)
				return e
			}
			if e != nil {
				// 相当于赠送用户彩金 直接添加一个注单 添加用户余额就行
				// 创建送彩金订单
				order = thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    0,
					WinAmount:    reqdata.Money,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    1, // 已开奖
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 外赠 创建中间订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "创建订单失败"
					ctx.RespJson(respdata)
					return e
				}
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 外赠 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Message = "创建订单失败"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("amigo_single 改变玩家余额 其他 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}
				// 其他
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"WinAmount": daogorm.Expr("WinAmount + ?", reqdata.Money),
					"ThirdTime": thirdTime,
					// "RawData":   string(bodyBytes),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("amigo_single 改变玩家余额 其他 更新订单失败 thirdId=", thirdId, " error=", e.Error())
					respdata.Message = "更新订单失败"
					ctx.RespJson(respdata)
					return e
				}
			}

			// 其他更改余额
			if reqdata.Money <= 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ? and Amount>=", userId, reqdata.Money).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
					e = errors.New("更新条数0")
				}
			} else {
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
					e = errors.New("更新条数0")
				}
			}

			if e != nil {
				logs.Error("amigo_single 改变玩家余额 其他 更新用户余额失败 thirdId=", thirdId, " userId=", userId, " error=", e.Error())
				respdata.Message = "更新用户余额失败"
				ctx.RespJson(respdata)
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonAmigoOther,
				Memo:         l.brandName + " other,thirdId:" + thirdId + ",tid:" + reqdata.TransactionId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("amigo_single 改变玩家余额 其他 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Message = "创建账变记录失败"
				ctx.RespJson(respdata)
				return e
			}

			respdata.IsSuccess = true
			respdata.Message = "success"
			respdata.Value.Money = reqdata.Money
			respdata.Value.TradeNo = fmt.Sprintf("%d", amountLog.Id)
			respdata.Value.AfterMoney = userBalance.Amount + reqdata.Money
			respdata.Value.Currency = l.currency
			logs.Info("amigo_single 改变玩家余额 其他 thirdId=", thirdId, " userId=", userId, " 其他成功 reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		default:
			logs.Error("amigo_single 改变玩家余额 非法的type reqdata.Action=", reqdata.Action, " thirdId=", reqdata.TransactionId)
			respdata.Message = "illegal Action"
			ctx.RespJson(respdata)
			return nil
		}
	})

	if err != nil {
		logs.Error("amigo_single 改变玩家余额 事务处理失败 thirdId=", reqdata.TransactionId, " err=", err.Error())
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil && reqdata.Money != 0 {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][AmigoSingleService] ChangeBalance 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][AmigoSingleService] ChangeBalance 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	return
}

// gameover 通知游戏结束 /amigo/external/gameOver
func (l *AmigoSingleService) GameOver(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId       int64  `json:"userId" validate:"required"`       // 用户ID
		UserName     string `json:"userName" validate:"required"`     // 用户名（用户名前方必须增加代理号，比如代理号为abc,则账号为大写的ABCXXX，统一为大写）
		MachineId    int64  `json:"machineId" validate:"required"`    // 机台id
		PlayRecordId string `json:"playRecordId" validate:"required"` // 游戏记录ID 前面的tableId
	}
	type ResponseData struct {
		IsSuccess bool   `json:"IsSuccess"`
		Message   string `json:"Message"`
		Value     struct {
			Receive bool `json:"receive"` // 是否接收
		} `json:"Value"`
	}
	respdata := ResponseData{
		IsSuccess: false,
		Message:   "",
	}
	respdata.Value.Receive = false

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("amigo_single 通知游戏结束 读取请求消息体错误 err=", err.Error())
		respdata.Message = "read request body error"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("amigo_single 通知游戏结束 Request.Body=", string(bodyBytes))
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("amigo_single 通知游戏结束 解析请求消息体错误 err=", err.Error())
		respdata.Message = "json.Unmarshal body error"
		ctx.RespJson(respdata)
		return
	}

	if reqdata.PlayRecordId == "" {
		logs.Error("amigo_single 通知游戏结束 PlayRecordId是空")
		respdata.Message = "playRecordId is empty"
		ctx.RespJson(respdata)
		return
	}

	userName := strings.Split(reqdata.UserName, "_")
	if len(userName) != 2 || userName[0] != strings.ToUpper(l.agentId) {
		logs.Error("amigo_single 通知游戏结束 用户名不匹配 reqdata.UserName=", reqdata.UserName, " bodyBytes=", string(bodyBytes))
		respdata.Message = "userName not match agentId"
		ctx.RespJson(respdata)
		return
	}

	thirdId := reqdata.PlayRecordId
	userIdTmp := strings.Split(reqdata.UserName, "_")
	if len(userIdTmp) != 2 {
		logs.Error("amigo_single 通知游戏结束 用户名格式错误 reqdata.UserName=", reqdata.UserName)
		respdata.Message = "userName format error"
		ctx.RespJson(respdata)
		return
	}
	/*
		userId, err := strconv.ParseInt(userIdTmp[2], 10, 64)
		if err != nil {
			logs.Error("amigo_single 通知游戏结束 用户名格式错误 reqdata.UserName=", reqdata.UserName, " err=", err.Error())
			respdata.Message = "userName format error"
			ctx.RespJson(respdata)
			return
		}
	*/

	// 日本弹珠都是电子类型
	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		order := thirdGameModel.ThirdOrder{}
		// 查询下注订单
		e := tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			logs.Error("amigo_single 通知游戏结束 汇总订单 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Message = "订单不存在"
			} else {
				respdata.Message = "查询注单错误"
			}
			ctx.RespJson(respdata)
			return e
		}
		if order.DataState != -1 {
			logs.Error("amigo_single 通知游戏结束 汇总订单 订单已处理 thirdId=", thirdId)
			respdata.Value.Receive = true
			respdata.Message = "订单已结算"
			ctx.RespJson(respdata)
			return nil
		}
		// 结算订单
		// 所有电子的有效流水取不大于下注金额的输赢绝对值
		validBet := math.Abs(order.WinAmount - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"DataState": 1,
			"ValidBet":  validBet,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("amigo_single 通知游戏结束 结算输的订单 更改预设订单失败 thirdId=", thirdId, " error=", e.Error())
			respdata.Message = "更新订单状态失败"
			ctx.RespJson(respdata)
			return e
		}

		order.Id = 0
		order.ValidBet = validBet
		order.DataState = 1
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("amigo_single 通知游戏结束 结算输的订单 创建正式订单失败 order=", order, " error=", e.Error())
			respdata.Message = "创建正式注单失败"
			ctx.RespJson(respdata)
			return e
		}

		respdata.Value.Receive = true
		logs.Info("amigo_single 通知游戏结束 汇总订单成功 thirdId=", thirdId, " 结算成功 reqdata=", reqdata, " respdata=", respdata)
		ctx.RespJson(respdata)
		return nil
	})

	if err != nil {
		logs.Error("amigo_single 通知游戏结束 事务处理失败 thirdId=", thirdId, " err=", err.Error())
	} else {
		go func() {
			// 拉取注单详情
			d, e := l.findGameRecord(reqdata.PlayRecordId, time.Now().Unix())
			if e != nil {
				logs.Error("amigo_single 通知游戏结束 拉取注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", e.Error())
				for retry := 1; retry < 4; retry++ {
					time.Sleep(time.Second * 3) // 等待3秒再请求
					d, e = l.findGameRecord(reqdata.PlayRecordId, time.Now().Unix())
					if e == nil {
						break
					} else {
						logs.Error("amigo_single 通知游戏结束 拉取注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", e.Error(), " retry=", retry)
					}
				}
				if e != nil {
					return
				}
			}

			dbyte, _ := json.Marshal(d)
			dbyteZh := l.amigoGameRecord2String(d)
			logs.Info("amigo_single 通知游戏结束 拉取注单详情成功 thirdId=", reqdata.PlayRecordId, " d=", string(dbyte))
			e = server.Db().GormDao().Table(tablePre).Where("ThirdId = ? and Brand=?", reqdata.PlayRecordId, l.brandName).Updates(map[string]interface{}{
				// "BetAmount":  d.InMoney,
				// "WinAmount":  d.OutMoney,
				// "ValidBet":   d.InMoney,
				// "DataState":  1,
				"GameName":   d.MachineName,
				"RawData":    string(dbyte),
				"BetCtx":     dbyteZh,
				"GameRst":    dbyteZh,
				"BetCtxType": 3,
			}).Error
			if e != nil {
				logs.Error("amigo_single 通知游戏结束 更新预设表注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", e.Error())
			}
			e = server.Db().GormDao().Table(table).Where("ThirdId = ? and Brand=?", reqdata.PlayRecordId, l.brandName).Updates(map[string]interface{}{
				// "BetAmount":  d.InMoney,
				// "WinAmount":  d.OutMoney,
				// "ValidBet":   d.InMoney,
				// "DataState":  1,
				"GameName":   d.MachineName,
				"RawData":    string(dbyte),
				"BetCtx":     dbyteZh,
				"GameRst":    dbyteZh,
				"BetCtxType": 3,
			}).Error
			if e != nil {
				logs.Error("amigo_single 通知游戏结束 更新正式表注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", e.Error())
			} else {
				logs.Info("amigo_single 通知游戏结束 更新注单详情成功 thirdId=", reqdata.PlayRecordId)
			}

			/*
				gameId := fmt.Sprint("%d", d.GameId)
				gameName := d.MachineName
				betAmount := d.InMoney
				winAmount := d.OutMoney
				thirdTime := d.SettlementTime // 三方也是北京时间
				if thirdTime == "" {
					thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
				}
				createTime := d.CreatedOn
				if createTime == "" {
					createTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
				}
				e = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
					var tmpe error
					userBalance := thirdGameModel.UserBalance{}

					// 获取用户信息
					tmpe = tx.Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
					if tmpe != nil {
						logs.Error("amigo_single 通知游戏结束 获取用户失败 thirdId=", thirdId, " userId=", userId, " error=", tmpe.Error())
						respdata.Message = "get user fail"
						ctx.RespJson(respdata)
						return tmpe
					}

					order := thirdGameModel.ThirdOrder{
						// Id: 0,
						SellerId:   userBalance.SellerId,
						ChannelId:  userBalance.ChannelId,
						UserId:     userBalance.UserId,
						Brand:      l.brandName,
						ThirdId:    thirdId,
						GameId:     gameId,
						GameName:   gameName,
						BetAmount:  betAmount,
						WinAmount:  winAmount,
						ValidBet:   betAmount,
						ThirdTime:  thirdTime,
						Currency:   l.currency,
						RawData:    string(dbyte),
						State:      1,
						Fee:        0,
						DataState:  1, //未开奖
						CreateTime: createTime,
						BetCtx:     string(dbyteZh),
						GameRst:    string(dbyteZh),
						BetCtxType: 3,
					}

					tmpe = tx.Table(tablePre).Create(&order).Error
					if tmpe != nil {
						logs.Error("amigo_single 通知游戏结束 插入预设表注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", tmpe.Error())
						return tmpe
					}

					order.Id = 0
					tmpe = tx.Table(table).Create(&order).Error
					if tmpe != nil {
						logs.Error("amigo_single 通知游戏结束 插入正式表注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", tmpe.Error())
						return tmpe
					}
					return nil
				})
				if e != nil {
					logs.Error("amigo_single 通知游戏结束 生成注单详情失败 thirdId=", reqdata.PlayRecordId, " err=", e.Error())
				} else {
					logs.Info("amigo_single 通知游戏结束 生成注单详情成功 thirdId=", reqdata.PlayRecordId)
				}
			*/
			return
		}()
	}

	return
}

// load_game_list 获取游戏列表 /foreign/gameList
func (l *AmigoSingleService) LoadGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Add string `json:"add"` // 1 添加数据库 非1不添加数据库
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	urlParamData, checksum := l.getSign(map[string]interface{}{
		"partnerId": l.agentId,
	})
	urlParamData += "&checksum=" + checksum

	type GameData struct {
		GameCode     string `json:"gamecode"`     // 游戏编码
		ChineseName  string `json:"chineseName"`  // 游戏名称中文
		EnglishName  string `json:"englishName"`  // 游戏名称英文
		JapaneseName string `json:"japaneseName"` // 游戏名称日文
		GameType     string `json:"GameType"`     // 游戏类型
		Gameurl      string `json:"gameurl"`      // 游戏图标
	}
	type ResponseGameListData struct {
		IsSuccess bool         `json:"IsSuccess"`
		Message   string       `json:"Message"`
		Value     [][]GameData `json:"Value"`
	}
	respGameListData := ResponseGameListData{}

	client := &http.Client{}
	gameListUrl := l.reportUrl + "/foreign/gameList?" + urlParamData
	req, _ := http.NewRequest(http.MethodGet, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("amigo_single 获取游戏列表请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respGameListData)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if !respGameListData.IsSuccess {
		logs.Error("amigo_single 获取游戏列表失败 错误信息=", respGameListData.Message)
		ctx.RespErrString(true, &errcode, respGameListData.Message)
		return
	}

	/*if reqdata.Add == "1" {
		for _, vArry := range respGameListData.Value {
			for _, v := range vArry {
				// GameType solt
				betTran := thirdGameModel.GameList{}
				err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, v.GameCode).First(&betTran).Error
				if err != daogorm.ErrRecordNotFound {
					continue
				}

				//写入数据库中
				gameInfo := xgo.H{
					"Brand":     l.brandName,
					"GameId":    v.GameCode,
					"Name":      v.ChineseName,
					"EName":     v.EnglishName,
					"GameType":  1, // 暂时全部放电子里面 1电子 2棋牌 3趣味 4彩票 5真人 6体育
					"HubType":   0, // 0非聚合类型 1hub88类型
					"State":     1,
					"OpenState": 1,
				}
				server.Db().Table("x_game_list").Insert(gameInfo)
			}
		}
	}*/

	ctx.RespOK(respGameListData)
	return
}

// load_game_list_status 动态获取机台状态列表 /foreign/gameListStatus
func (l *AmigoSingleService) LoadGameListStatus(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Add string `json:"add"` // 1 添加数据库 非1不添加数据库
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	urlParamData, checksum := l.getSign(map[string]interface{}{
		"partnerId": l.agentId,
	})
	urlParamData += "&checksum=" + checksum

	type GameData struct {
		GameId       int64   `json:"gameId"`       // 游戏ID
		GameType     string  `json:"gameType"`     // 游戏类型
		ChineseName  string  `json:"chineseName"`  // 游戏名称中文
		EnglishName  string  `json:"englishName"`  // 游戏名称英文
		JapaneseName string  `json:"japaneseName"` // 游戏名称日文
		GameCode     string  `json:"gamecode"`     // 游戏编码
		TotalSeats   int     `json:"totalSeats"`   // 总席位数
		SurplusSeats int     `json:"surplusSeats"` // 剩余席位数
		State        int     `json:"state"`        // 游戏状态 1:正常 2:停用
		CloseShop    bool    `json:"closeShop"`    // 闭店计划
		Odds         float64 `json:"odds"`         // odds
		Gameurl      string  `json:"gameurl"`      // 游戏图标
	}

	type ResponseGameListData struct {
		IsSuccess bool       `json:"IsSuccess"`
		Message   string     `json:"Message"`
		Value     []GameData `json:"Value"`
	}
	respGameListData := ResponseGameListData{}

	client := &http.Client{}
	gameListUrl := l.reportUrl + "/foreign/gameListStatus?" + urlParamData
	req, _ := http.NewRequest(http.MethodGet, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表状态请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表状态响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("amigo_single 获取游戏列表状态请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respGameListData)
	if err != nil {
		logs.Error("amigo_single 获取游戏列表状态 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	if !respGameListData.IsSuccess {
		logs.Error("amigo_single 获取游戏列表状态失败 错误信息=", respGameListData.Message)
		ctx.RespErrString(true, &errcode, respGameListData.Message)
		return
	}

	// 定义状态常量，提高代码可读性
	const (
		STATE_NORMAL   = 1 // 正常
		STATE_DISABLED = 2 // 停用
	)

	// 处理游戏列表数据
	for _, v := range respGameListData.Value {
		gameId := fmt.Sprintf("%d", v.GameId)

		// 查询游戏是否存在
		betTran := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).First(&betTran).Error

		// 优化3: 合并重复条件判断
		if err != nil && err != daogorm.ErrRecordNotFound {
			// 查询出错但不是"记录未找到"，跳过处理
			logs.Error("amigo_single 查询游戏记录出错 gameId=", gameId, " err=", err.Error())
			continue
		}

		// 优化5: 状态检查优化 - 更清晰地处理不同状态
		if err == nil {
			// 记录存在，检查状态是否需要更新
			if betTran.State != v.State {
				logs.Info("amigo_single 游戏状态变更 gameId=", gameId, " 旧状态=", betTran.State, " 新状态=", v.State)
				tmpe := server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).Update("State", v.State).Error
				if tmpe != nil {
					logs.Error("amigo_single 更新游戏状态失败 gameId=", gameId, " err=", tmpe.Error())
				}
			}
		} else if err == daogorm.ErrRecordNotFound {

			//1 才需要将不存在的记录添加到数据库
			if reqdata.Add != "1" {
				continue
			}

			// 记录不存在
			state := STATE_NORMAL
			if v.State != STATE_NORMAL {
				state = STATE_DISABLED
				logs.Info("amigo_single 跳过添加非正常状态游戏 gameId=", gameId, " state=", v.State)
				// 只有状态为正常(1)的游戏才需要添加到数据库
				//continue
			}

			// 准备插入新游戏记录
			logs.Info("amigo_single 添加新游戏 gameId=", gameId, " name=", v.ChineseName)
			icon := v.Gameurl
			eicon := v.Gameurl

			// 写入数据库中
			gameInfo := xgo.H{
				"Brand":     l.brandName,
				"GameId":    gameId,
				"Name":      v.JapaneseName,
				"EName":     v.EnglishName,
				"GameType":  1,     // 暂时全部放电子里面 1电子 2棋牌 3趣味 4彩票 5真人 6体育
				"HubType":   0,     // 0非聚合类型 1hub88类型
				"State":     state, // 使用常量提高可读性
				"OpenState": state,
				"Icon":      icon,
				"EIcon":     eicon,
			}
			_, insertErr := server.Db().Table("x_game_list").Insert(gameInfo)
			if insertErr != nil {
				logs.Error("amigo_single 插入游戏记录失败 gameId=", gameId, " err=", insertErr.Error())
			}
		}
	}

	ctx.RespOK(respGameListData)
	return
}

// set_game_hot_new 动态获取机台状态列表 /foreign/gameListStatus
func (l *AmigoSingleService) SetGameHotNew(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		IsHot bool   `json:"isHot"` // 是否热门
		IsNew bool   `json:"isNew"` // 是否新游戏
		Add   string `json:"add"`   // 1 添加数据库 非1不添加数据库
	}

	errcode := 0
	reqdata := RequestData{}

	urlData := l.getUrlParam(ctx)
	if v, ok := urlData["isHot"]; !ok {
		reqdata.IsHot = false
	} else {
		if v == "true" {
			reqdata.IsHot = true
		} else {
			reqdata.IsHot = false
		}
	}
	if v, ok := urlData["isNew"]; !ok {
		reqdata.IsNew = false
	} else {
		if v == "true" {
			reqdata.IsNew = true
		} else {
			reqdata.IsNew = false
		}
	}
	if v, ok := urlData["add"]; !ok {
		reqdata.Add = "0"
	} else {
		reqdata.Add = v
	}

	if !reqdata.IsHot && !reqdata.IsNew {
		ctx.RespErrString(true, &errcode, "IsHot和IsNew不能同时为false")
		return
	}
	if reqdata.IsHot && reqdata.IsNew {
		ctx.RespErrString(true, &errcode, "IsHot和IsNew不能同时为true")
		return
	}

	urlParamData, checksum := l.getSign(map[string]interface{}{
		"partnerId": l.agentId,
	})
	urlParamData += "&checksum=" + checksum
	if reqdata.IsNew {
		urlParamData += "&isNew=true"
	}
	if reqdata.IsHot {
		urlParamData += "&isHot=true"
	}

	type GameData struct {
		GameId       int64   `json:"gameId"`       // 游戏ID
		GameType     string  `json:"gameType"`     // 游戏类型
		ChineseName  string  `json:"chineseName"`  // 游戏名称中文
		EnglishName  string  `json:"englishName"`  // 游戏名称英文
		JapaneseName string  `json:"japaneseName"` // 游戏名称日文
		GameCode     string  `json:"gamecode"`     // 游戏编码
		TotalSeats   int     `json:"totalSeats"`   // 总席位数
		SurplusSeats int     `json:"surplusSeats"` // 剩余席位数
		State        int     `json:"state"`        // 游戏状态 1:正常 2:停用
		CloseShop    bool    `json:"closeShop"`    // 闭店计划
		Odds         float64 `json:"odds"`         // odds
		Gameurl      string  `json:"gameurl"`      // 游戏图标
	}
	type ResponseGameListData struct {
		IsSuccess bool       `json:"IsSuccess"`
		Message   string     `json:"Message"`
		Value     []GameData `json:"Value"`
	}
	respGameListData := ResponseGameListData{}

	client := &http.Client{}
	gameListUrl := "https://consoleapi.atsupachi.com/foreign/gameListStatus?" + urlParamData
	req, _ := http.NewRequest(http.MethodPost, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 获取热门最新游戏列表状态请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 获取热门最新游戏列表状态响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("amigo_single 获取热门最新游戏列表状态请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respGameListData)
	if err != nil {
		logs.Error("amigo_single 获取热门最新游戏列表状态 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if !respGameListData.IsSuccess {
		logs.Error("amigo_single 获取热门最新游戏列表状态失败 错误信息=", respGameListData.Message)
		ctx.RespErrString(true, &errcode, respGameListData.Message)
		return
	}

	if reqdata.Add == "1" {
		type OperationGameData struct {
			GameId   int64  `json:"gameId"`   // 游戏ID
			GameName string `json:"gameName"` // 游戏名称
		}
		type OperationResponseData struct {
			Operation string              `json:"operation"`
			List      []OperationGameData `json:"list"`
		}
		tmpresponsedata := OperationResponseData{}

		if reqdata.IsHot {
			tmpresponsedata.Operation = "设置热门游戏"
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=?", l.brandName).Updates(map[string]interface{}{
				"IsHot": 2,
			}).Error
			if err != nil {
				logs.Error("amigo_single 获取热门最新游戏列表状态 重置所有游戏非热门错误 err=", err.Error())
			}
		}
		if reqdata.IsNew {
			tmpresponsedata.Operation = "设置最新游戏"
			server.Db().GormDao().Table("x_game_list").Where("Brand=?", l.brandName).Updates(map[string]interface{}{
				"IsNew": 2,
			})
			if err != nil {
				logs.Error("amigo_single 获取热门最新游戏列表状态 重置所有游戏非最新错误 err=", err.Error())
			}
		}

		for _, v := range respGameListData.Value {
			gameId := fmt.Sprintf("%d", v.GameId)
			// GameType solt
			uData := make(map[string]interface{})
			if reqdata.IsHot {
				uData["IsHot"] = 1
			}
			if reqdata.IsNew {
				uData["IsNew"] = 1
			}
			tmpresponsedata.List = append(tmpresponsedata.List, OperationGameData{
				GameId:   v.GameId,
				GameName: v.ChineseName,
			})
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId=?", l.brandName, gameId).Updates(uData).Error
			if err != nil {
				logs.Error("amigo_single 获取热门最新游戏列表状态 更新游戏状态失败 gameId=", gameId, " uData=", uData, " err=", err.Error())
			}
		}

		ctx.RespOK(tmpresponsedata)
		return
	}

	ctx.RespOK(respGameListData)
	return
}

func (l *AmigoSingleService) getUrlParam(ctx *abugo.AbuHttpContent) (data map[string]string) {
	data = make(map[string]string, 0)
	query := ctx.Gin().Request.URL.Query()
	for k := range query {
		data[k] = ctx.Query(k)
	}
	return
}

type AmigoGameRecord struct {
	GameRecordSummaryId int64   `json:"gameRecordSummaryId"` // 注单ID
	MachineId           int64   `json:"machineId"`           // 机台
	Rb                  int64   `json:"rb"`                  // rb
	Bb                  int64   `json:"bb"`                  // bb
	Total               int64   `json:"total"`               // 总回转数
	InMoney             float64 `json:"inMoney"`             // 投注金额
	Points              float64 `json:"points"`              // 扣点
	OutMoney            float64 `json:"outMoney"`            // 出币金额
	WinMoney            float64 `json:"winMoney"`            // 盈亏金额
	CreatedOn           string  `json:"createdOn"`           // 创建时间 2022-03-21 14:10:04 北京时间
	UesSpecialMoney     float64 `json:"uesSpecialMoney"`     // 扣除专用代币数
	UseTotalMoney       float64 `json:"useTotalMoney"`       // 代币扣除总数
	GameId              int64   `json:"gameId"`              // 游戏id
	ChangeMoney         float64 `json:"changeMoney"`         // 扣币后余额
	UserName            string  `json:"userName"`            // 用户账号
	MachineName         string  `json:"machineName"`         // 机台名称
	PlayRecordId        string  `json:"playRecordId"`        // 游戏记录ID
	Status              int     `json:"status"`              // 订单状态(0未结算(还在游戏中),1已结算)
	Duration            int64   `json:"duration"`            // 游戏时长
	SettlementTime      string  `json:"settlementTime"`      // 更新时间(结算时间) 2022-03-21 14:10:04 北京时间
}

func (l *AmigoSingleService) amigoGameRecord2String(data AmigoGameRecord) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单ID\":%d,", data.GameRecordSummaryId))
	sb.WriteString(fmt.Sprintf("\"机台\":%d,", data.MachineId))
	sb.WriteString(fmt.Sprintf("\"总回转数\":%d,", data.Total))
	sb.WriteString(fmt.Sprintf("\"投注金额\":%g,", data.InMoney))
	sb.WriteString(fmt.Sprintf("\"扣点\":%g,", data.Points))
	sb.WriteString(fmt.Sprintf("\"出币金额\":%g,", data.OutMoney))
	sb.WriteString(fmt.Sprintf("\"盈亏金额\":%g,", data.WinMoney))
	sb.WriteString(fmt.Sprintf("\"创建时间\":\"%s\",", data.CreatedOn))
	sb.WriteString(fmt.Sprintf("\"扣除专用代币数\":%g,", data.UesSpecialMoney))
	sb.WriteString(fmt.Sprintf("\"代币扣除总数\":%g,", data.UseTotalMoney))
	sb.WriteString(fmt.Sprintf("\"游戏ID\":%d,", data.GameId))
	sb.WriteString(fmt.Sprintf("\"扣币后余额\":%g,", data.ChangeMoney))
	sb.WriteString(fmt.Sprintf("\"用户账号\":\"%s\",", data.UserName))
	sb.WriteString(fmt.Sprintf("\"机台名称\":\"%s\",", data.MachineName))
	sb.WriteString(fmt.Sprintf("\"游戏记录ID\":\"%s\",", data.PlayRecordId))
	sb.WriteString(fmt.Sprintf("\"订单状态\":%d,", data.Status))
	sb.WriteString(fmt.Sprintf("\"游戏时长\":%d,", data.Duration))
	sb.WriteString(fmt.Sprintf("\"结算时间\":\"%s\"", data.SettlementTime))
	sb.WriteString("}")
	res = sb.String()
	return
}

func (l *AmigoSingleService) findGameRecord(playRecordId string, settledTime int64) (data AmigoGameRecord, err error) {
	data, err = l.findGameRecordByMinute(playRecordId, settledTime)
	if err == nil {
		return
	}
	if err.Error() == "游戏记录未结算" {
		return
	}
	data, err = l.findGameRecordByDay(playRecordId, settledTime)
	return
}

func (l *AmigoSingleService) findGameRecordByMinute(playRecordId string, settledTime int64) (data AmigoGameRecord, err error) {
	startTime := settledTime - 120
	endTime := settledTime + 120
	totalPages := int64(1)
	var list []AmigoGameRecord
	page := int64(1)
	pageSize := int64(50)

	for {
		totalPages, list, err = l.getHistoryRecordList(startTime, endTime, pageSize, page)
		if err != nil {
			return
		}

		for _, v := range list {
			if v.PlayRecordId == playRecordId {
				if v.Status != 1 { // 0还在游戏中 1已结算的
					err = fmt.Errorf("游戏记录未结算")
					return
				}
				data = v
				return
			}
		}
		if page >= totalPages {
			break
		}
		page += 1
	}
	err = fmt.Errorf("未找到游戏记录")
	return
}

func (l *AmigoSingleService) findGameRecordByDay(playRecordId string, settledTime int64) (data AmigoGameRecord, err error) {
	totalPages := int64(1)
	var list []AmigoGameRecord
	page := int64(1)
	pageSize := int64(50)

	for {
		totalPages, list, err = l.getHistoryRecord(settledTime, pageSize, page)
		if err != nil {
			return
		}

		for _, v := range list {
			if v.PlayRecordId == playRecordId {
				if v.Status != 1 { // 0还在游戏中 1已结算的
					err = fmt.Errorf("游戏记录未结算")
					return
				}
				data = v
				return
			}
		}
		if page >= totalPages {
			break
		}
		page += 1
	}
	err = fmt.Errorf("未找到游戏记录")
	return
}

// 获取游戏记录天 /foreign/historyRecord
func (l *AmigoSingleService) getHistoryRecord(dayTime, pageSize, page int64) (totalPages int64, list []AmigoGameRecord, err error) {
	list = make([]AmigoGameRecord, 0)
	type ResponseGameListData struct {
		IsSuccess  bool              `json:"IsSuccess"`
		Message    string            `json:"Message"`
		Value      string            `json:"Value"`
		TotalPages int64             `json:"TotalPages"`
		Records    []AmigoGameRecord `json:"Records"`
	}

	start := time.Unix(dayTime, 0).In(tzUTC8).Format("2006-01-02") // 北京时间
	urlParamData, checksum := l.getSign(map[string]interface{}{
		"start":     start,
		"partnerId": l.agentId,
		"take":      pageSize,
		"page":      page,
	})
	urlParamData += "&checksum=" + checksum

	respRecordListData := ResponseGameListData{}
	client := &http.Client{}
	gameListUrl := l.reportUrl + "/foreign/historyRecord?" + urlParamData
	req, _ := http.NewRequest(http.MethodGet, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录天请求错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录天响应错误 err=", err.Error())
		return
	}
	logs.Info("amigo_single 获取游戏记录天1请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respRecordListData)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录天 解析响应消息体错误 err=", err.Error())
		return
	}
	if !respRecordListData.IsSuccess {
		logs.Error("amigo_single 获取游戏记录天失败 错误信息=", respRecordListData.Message)
		err = fmt.Errorf("请求错误:%s", respRecordListData.Message)
		return
	}

	// for k, v := range respRecordListData.Records {
	// 	// 处理游戏记录
	// 	logs.Info("amigo_single 获取游戏记录天成功 Records[", k, "]=", v)
	// }
	totalPages = respRecordListData.TotalPages
	list = respRecordListData.Records
	return
}

// 获取游戏记录时间段 /foreign/historyRecordList
func (l *AmigoSingleService) getHistoryRecordList(startTime, endTime, pageSize, page int64) (totalPages int64, list []AmigoGameRecord, err error) {
	list = make([]AmigoGameRecord, 0)
	type ResponseGameListData struct {
		IsSuccess  bool              `json:"IsSuccess"`
		Message    string            `json:"Message"`
		Value      string            `json:"Value"`
		TotalPages int64             `json:"TotalPages"`
		Records    []AmigoGameRecord `json:"Records"`
	}

	startDate := time.Unix(startTime, 0).In(tzUTC8).Format("2006-01-02 15:04:05") // 北京时间
	endDate := time.Unix(endTime, 0).In(tzUTC8).Format("2006-01-02 15:04:05")     // 北京时间

	urlParamData, checksum := l.getSign(map[string]interface{}{
		"startDate": startDate,
		"endDate":   endDate,
		"partnerId": l.agentId,
		"take":      pageSize,
		"page":      page,
	})
	urlParamData += "&checksum=" + checksum
	urlParamData = strings.ReplaceAll(urlParamData, " ", "%20") // url特殊字符转义 需要先生成签名再转义

	respRecordListData := ResponseGameListData{}
	client := &http.Client{}
	gameListUrl := l.reportUrl + "/foreign/historyRecordList?" + urlParamData
	req, _ := http.NewRequest(http.MethodGet, gameListUrl, nil)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录时间段请求错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录时间段响应错误 err=", err.Error())
		return
	}
	logs.Info("amigo_single 获取游戏记录时间段请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respRecordListData)
	if err != nil {
		logs.Error("amigo_single 获取游戏记录时间段 解析响应消息体错误 err=", err.Error())
		return
	}
	if !respRecordListData.IsSuccess {
		logs.Error("amigo_single 获取游戏记录时间段失败 错误信息=", respRecordListData.Message)
		err = fmt.Errorf("请求错误:%s", respRecordListData.Message)
		return
	}

	// for k, v := range respRecordListData.Records {
	// 	// 处理游戏记录
	// 	logs.Info("amigo_single 获取游戏记录时间段成功 Records[", k, "]=", v)
	// }
	totalPages = respRecordListData.TotalPages
	list = respRecordListData.Records
	return
}
