// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserAddressDailly = "x_user_address_dailly"

// XUserAddressDailly mapped from table <x_user_address_dailly>
type XUserAddressDailly struct {
	ChannelID    int32     `gorm:"column:ChannelId;primaryKey;default:1" json:"ChannelId"`
	Address      string    `gorm:"column:Address;primaryKey" json:"Address"`
	RecordDate   time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	BetCountTrx  int32     `gorm:"column:BetCountTrx" json:"BetCountTrx"`
	BetCountUsdt int32     `gorm:"column:BetCountUsdt" json:"BetCountUsdt"`
	BetTrx       float64   `gorm:"column:BetTrx;default:0.000000" json:"BetTrx"`
	BetUsdt      float64   `gorm:"column:BetUsdt;default:0.000000" json:"BetUsdt"`
	RewardTrx    float64   `gorm:"column:RewardTrx;default:0.000000" json:"RewardTrx"`
	RewardUsdt   float64   `gorm:"column:RewardUsdt;default:0.000000" json:"RewardUsdt"`
	LiuSuiTrx    float64   `gorm:"column:LiuSuiTrx;default:0.000000" json:"LiuSuiTrx"`
	LiuSuiUsdt   float64   `gorm:"column:LiuSuiUsdt;default:0.000000" json:"LiuSuiUsdt"`
	IsNew        int32     `gorm:"column:IsNew;default:2;comment:是否当日新增 1是 2不是" json:"IsNew"`    // 是否当日新增 1是 2不是
	IsValid      int32     `gorm:"column:IsValid;default:2;comment: 是否有效 1是 2不是" json:"IsValid"` //  是否有效 1是 2不是
	BetSymbol    string    `gorm:"column:BetSymbol" json:"BetSymbol"`
	IsValidTrx   int32     `gorm:"column:IsValidTrx;default:2;comment: 是否有效 1是 2不是" json:"IsValidTrx"`   //  是否有效 1是 2不是
	IsValidUsdt  int32     `gorm:"column:IsValidUsdt;default:2;comment: 是否有效 1是 2不是" json:"IsValidUsdt"` //  是否有效 1是 2不是
}

// TableName XUserAddressDailly's table name
func (*XUserAddressDailly) TableName() string {
	return TableNameXUserAddressDailly
}
