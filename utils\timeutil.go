package utils

import (
	"github.com/beego/beego/logs"
	"regexp"
	"strconv"
	"time"
)

func GlobalLocation() *time.Location {
	l, _ := time.LoadLocation("Asia/Shanghai")
	return l
}

func IsTimeBetween(time time.Time, LTime time.Time, RTime time.Time) bool {
	return time.Compare(LTime) >= 0 && time.Compare(RTime) < 0
}

func DayStartByTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
}

func FormatDate(t time.Time) string {
	return t.Format("2006-01-02")
}

func StrToTime(str string) time.Time {
	t, err := time.ParseInLocation("2006-01-02T15:04:05.999999999-0700", str, time.Local)

	//TODO 处理错误
	if err != nil {
		return time.Now()
	}
	return t
}

func FormatDateTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func FormatODateTime(t time.Time) string {
	return t.Format("2006-01-02T15:04:05.999999999-0700")
}

func ThisMondayStart(t time.Time) time.Time {
	offset := int(time.Monday - t.Weekday())
	if offset > 0 {
		offset = -6
	}
	weekStart := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).
		AddDate(0, 0, offset)
	return weekStart
}

func LastMonday(t time.Time) time.Time {
	monday := ThisMondayStart(t)
	return monday.AddDate(0, 0, -7)
}

func NextMonday(t time.Time) time.Time {
	thisMonday := ThisMondayStart(t)
	nextMonday := thisMonday.AddDate(0, 0, 7)
	return nextMonday
}

// 预编译正则表达式，提高性能
var (
	// 匹配数字时间戳（包括科学计数法）
	numericRegex = regexp.MustCompile(`^[0-9.eE+-]+$`)

	// 匹配ISO 8601格式 (2006-01-02T15:04:05...)
	isoFormatRegex = regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T`)

	// 匹配标准日期时间格式 (2006-01-02 15:04:05...)
	standardDateTimeRegex = regexp.MustCompile(`^\d{4}[-/]\d{2}[-/]\d{2} `)

	// 匹配仅日期格式 (2006-01-02, 2006/01/02)
	dateOnlyRegex = regexp.MustCompile(`^\d{4}[-/]\d{2}[-/]\d{2}$`)

	// 匹配欧洲/美国日期格式 (DD-MM-YYYY 或 MM-DD-YYYY)
	europeanDateRegex = regexp.MustCompile(`^\d{2}[-/]\d{2}[-/]\d{4}`)

	// 匹配紧凑格式 (20060102150405, 20060102)
	compactFormatRegex = regexp.MustCompile(`^\d{8,14}$`)

	// 匹配带有AM/PM的12小时制
	amPmRegex = regexp.MustCompile(`\d{1,2}:\d{2}:\d{2} [APap][Mm]$`)

	// 匹配中文格式
	chineseFormatRegex = regexp.MustCompile(`\d{4}年`)
)

// parseTimeWithFormats 通用的时间解析函数，根据格式类型解析时间
func parseTimeWithFormats(timeStr string) (time.Time, bool) {
	if timeStr == "" {
		return time.Time{}, false
	}

	var t time.Time
	var parsed bool

	// 根据正则表达式预判断格式类型
	switch {
	// 1. 处理ISO 8601格式
	case isoFormatRegex.MatchString(timeStr):
		isoLayouts := []string{
			"2006-01-02T15:04:05.999-07:00",
			"2006-01-02T15:04:05-07:00",
			"2006-01-02T15:04:05.999",
			"2006-01-02T15:04:05",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.999Z",
		}

		for _, layout := range isoLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 2. 处理标准日期时间格式
	case standardDateTimeRegex.MatchString(timeStr):
		standardLayouts := []string{
			"2006-01-02 15:04:05.999",
			"2006-01-02 15:04:05",
			"2006/01/02 15:04:05.999",
			"2006/01/02 15:04:05",
		}

		for _, layout := range standardLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 3. 处理数字时间戳（包括科学计数法）
	case numericRegex.MatchString(timeStr):
		// 尝试作为浮点数解析（处理科学计数法）
		if floatTime, err := strconv.ParseFloat(timeStr, 64); err == nil {
			unixTime := int64(floatTime)

			// 检查是否是毫秒时间戳 (13位)
			if unixTime > 1000000000000 {
				t = time.Unix(unixTime/1000, (unixTime%1000)*1000000)
			} else {
				// 假设是秒时间戳
				t = time.Unix(unixTime, 0)
			}
			parsed = true
		}

	// 4. 处理仅日期格式
	case dateOnlyRegex.MatchString(timeStr):
		dateLayouts := []string{
			"2006-01-02",
			"2006/01/02",
		}

		for _, layout := range dateLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 5. 处理欧洲/美国日期格式
	case europeanDateRegex.MatchString(timeStr):
		euUsLayouts := []string{
			"02-01-2006 15:04:05",
			"02/01/2006 15:04:05",
			"01/02/2006 15:04:05",
			"01-02-2006 15:04:05",
			"02-01-2006",
			"02/01/2006",
			"01/02/2006",
		}

		for _, layout := range euUsLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 6. 处理紧凑格式
	case compactFormatRegex.MatchString(timeStr):
		compactLayouts := []string{
			"20060102150405",
			"20060102",
		}

		for _, layout := range compactLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 7. 处理带有AM/PM的12小时制
	case amPmRegex.MatchString(timeStr):
		amPmLayouts := []string{
			"2006-01-02 3:04:05 PM",
			"2006-01-02 3:04:05 pm",
			"2006/01/02 3:04:05 PM",
		}

		for _, layout := range amPmLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 8. 处理中文格式
	case chineseFormatRegex.MatchString(timeStr):
		chineseLayouts := []string{
			"2006年01月02日 15:04:05",
			"2006年1月2日 15:04:05",
		}

		for _, layout := range chineseLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}

	// 9. 如果以上都不匹配，尝试所有格式（作为后备方案）
	default:
		// 尝试所有格式...
		allLayouts := []string{
			// ISO 8601
			"2006-01-02T15:04:05.999-07:00",
			"2006-01-02T15:04:05-07:00",
			"2006-01-02T15:04:05.999",
			"2006-01-02T15:04:05",
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.999Z",
			// 标准日期时间
			"2006-01-02 15:04:05.999",
			"2006-01-02 15:04:05",
			"2006/01/02 15:04:05.999",
			"2006/01/02 15:04:05",
			// 其他格式...
		}

		for _, layout := range allLayouts {
			if parsedTime, err := time.Parse(layout, timeStr); err == nil {
				t = parsedTime
				parsed = true
				break
			}
		}
	}

	return t, parsed
}

// ParseTimeToLocalStr 将时间转换为东八区-即北京时间，返回字符串
func ParseTimeToLocalStr(timeStr string) string {
	if timeStr == "" {
		return ""
	}

	t, parsed := parseTimeWithFormats(timeStr)

	if !parsed {
		logs.Error("解析日期错误，所有格式都失败:", timeStr)
		return ""
	}

	// 检查年份是否合理 (避免解析出不合理的日期)
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始字符串:", timeStr)
		return ""
	}

	// 转换为东八区
	east8 := time.FixedZone("UTC+8", 8*60*60)
	t = t.In(east8)
	return t.Format("2006-01-02 15:04:05")
}

// ParseTimeToLocal 将时间转换为东八区-即北京时间
func ParseTimeToLocal(timeStr string) *string {
	if timeStr == "" {
		return nil
	}

	t, parsed := parseTimeWithFormats(timeStr)

	if !parsed {
		logs.Error("解析日期错误，所有格式都失败:", timeStr)
		return nil
	}

	// 检查年份是否合理 (避免解析出不合理的日期)
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始字符串:", timeStr)
		return nil
	}

	// 转换为东八区
	east8 := time.FixedZone("UTC+8", 8*60*60)
	t = t.In(east8)
	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// ParseTime 转换时间保留原始时区
func ParseTime(timeStr string) *string {
	if timeStr == "" {
		return nil
	}

	t, parsed := parseTimeWithFormats(timeStr)

	if !parsed {
		logs.Error("解析日期错误，所有格式都失败:", timeStr)
		return nil
	}

	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// ParseTimeString 转换时间保留原始时区
func ParseTimeString(timeStr string) string {
	if timeStr == "" {
		return ""
	}

	t, parsed := parseTimeWithFormats(timeStr)

	if !parsed {
		logs.Error("解析日期错误，所有格式都失败:", timeStr)
		return ""
	}

	return t.Format("2006-01-02 15:04:05")
}

// ParseLongToLocalStr 将int64时间戳转换为东八区-即北京时间，返回字符串
func ParseLongToLocalStr(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}

	var t time.Time

	// 检查是否是毫秒时间戳 (13位)
	if timestamp > 1000000000000 {
		t = time.Unix(timestamp/1000, (timestamp%1000)*1000000)
	} else {
		// 假设是秒时间戳
		t = time.Unix(timestamp, 0)
	}

	// 检查年份是否合理 (避免解析出不合理的日期)
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始时间戳:", timestamp)
		return ""
	}

	// 转换为东八区
	east8 := time.FixedZone("UTC+8", 8*60*60)
	t = t.In(east8)
	return t.Format("2006-01-02 15:04:05")
}

// ParseLongToLocal 将int64时间戳转换为东八区-即北京时间，返回字符串指针
func ParseLongToLocal(timestamp int64) *string {
	if timestamp == 0 {
		return nil
	}

	var t time.Time

	// 检查是否是毫秒时间戳 (13位)
	if timestamp > 1000000000000 {
		t = time.Unix(timestamp/1000, (timestamp%1000)*1000000)
	} else {
		// 假设是秒时间戳
		t = time.Unix(timestamp, 0)
	}

	// 检查年份是否合理 (避免解析出不合理的日期)
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始时间戳:", timestamp)
		return nil
	}

	// 转换为东八区
	east8 := time.FixedZone("UTC+8", 8*60*60)
	t = t.In(east8)
	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// ParseLongTime 转换int64时间戳保留原始时区，返回字符串指针
func ParseLongTime(timestamp int64) *string {
	if timestamp == 0 {
		return nil
	}

	var t time.Time

	// 检查是否是毫秒时间戳 (13位)
	if timestamp > 1000000000000 {
		t = time.Unix(timestamp/1000, (timestamp%1000)*1000000)
	} else {
		// 假设是秒时间戳
		t = time.Unix(timestamp, 0)
	}

	// 检查年份是否合理
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始时间戳:", timestamp)
		return nil
	}

	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// ParseLongTimeString 转换int64时间戳保留原始时区，返回字符串
func ParseLongTimeString(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}

	var t time.Time

	// 检查是否是毫秒时间戳 (13位)
	if timestamp > 1000000000000 {
		t = time.Unix(timestamp/1000, (timestamp%1000)*1000000)
	} else {
		// 假设是秒时间戳
		t = time.Unix(timestamp, 0)
	}

	// 检查年份是否合理
	if t.Year() < 1970 || t.Year() > 2100 {
		logs.Error("解析出不合理的年份:", t.Year(), "原始时间戳:", timestamp)
		return ""
	}

	return t.Format("2006-01-02 15:04:05")
}

// ToWest4 将给定时间转换为西四区时间，返回格式化后的字符串
func ToWest4(t time.Time) string {
	west4 := time.FixedZone("UTC-4", -4*60*60)
	return t.In(west4).Format("2006-01-02 15:04:05")
}

// GetCurrentTime 获取当前时间 -东八区北京时间
func GetCurrentTime() string {
	var tzUTC8 = time.FixedZone("utc+8", 8*3600)
	return time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
}

// GetGMT2Time 体育用的是GMT+2时区
func GetGMT2Time() string {
	var tzGMT2 = time.FixedZone("GMT+2", 2*3600)
	return time.Now().In(tzGMT2).Format("2006-01-02 15:04:05")
}

// TimestampToLocal 时间戳转换 FB体育使用东八区时间
func TimestampToLocal(timestampStr string) *string {
	if timestampStr == "" {
		return nil
	}
	// 将字符串转换为int64
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return nil
	}
	// 将毫秒时间戳转换为东八区时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	t := time.UnixMilli(timestamp).In(loc)
	// 格式化为字符串
	result := t.Format("2006-01-02 15:04:05")
	return &result
}

// TimestampIntToLocal 时间戳转换 FB体育使用东八区时间
func TimestampIntToLocal(timestamp int64) *string {
	if timestamp == 0 {
		return nil
	}
	// 将毫秒时间戳转换为东八区时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	t := time.UnixMilli(timestamp).In(loc)
	// 格式化为字符串
	result := t.Format("2006-01-02 15:04:05")
	return &result
}

// TimestampToUTC 将毫秒时间戳转换为UTC时间字符串
func TimestampToUTC(timestamp int64) *string {
	if timestamp == 0 {
		return nil
	}
	// 将毫秒时间戳转换为UTC时间
	t := time.UnixMilli(timestamp).UTC()
	// 格式化为字符串
	result := t.Format("2006-01-02 15:04:05")
	// 格式化为字符串
	return &result
}

func GetDateTimeBeforeHours(hour int) string {
	return time.Now().Add(-time.Hour * time.Duration(hour)).Format("2006-01-02 15:04:05")
}

func GetDateBeforeDays(days int) string {
	return time.Now().Add(-time.Hour * time.Duration(days) * 24).Format("2006-01-02")
}

func GetDateTimeBeforeDays(days int) string {
	return time.Now().Add(-time.Hour * time.Duration(days) * 24).Format("2006-01-02 15:04:05")
}

// ConvertToBeijingTime 将前端时区的时间转换为北京时间（UTC+8）
func ConvertToBeijingTime(timestamp int64, timeZoneOffset int) int64 {
	// timeZoneOffset 已经是负数（对于东区）或正数（对于西区）
	// 北京是 UTC+8，所以是 -480
	// 计算用户时间到北京时间的偏移量
	beijingOffset := -480
	// 计算需要调整的分钟数
	adjustMinutes := beijingOffset - timeZoneOffset

	return timestamp + int64(adjustMinutes*60*1000)
}

// ConvertToUserTimezone 将北京时间转换回用户时区时间
func ConvertToUserTimezone(beijingDate string, timeZoneOffset int) string {
	// 解析北京时间
	t, _ := time.Parse("2006-01-02", beijingDate)

	// 计算从北京时间到用户时区的偏移
	// 用户时区偏移 - 北京时区偏移
	adjustMinutes := timeZoneOffset - (-480)

	// 转换为用户时区
	userTime := t.Add(time.Duration(adjustMinutes) * time.Minute)
	return userTime.Format("2006-01-02")
}
