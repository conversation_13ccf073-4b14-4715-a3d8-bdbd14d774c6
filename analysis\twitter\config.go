package twitter

import (
	"encoding/json"
	"github.com/beego/beego/logs"
	"sync"
	"xserver/server"
)

const apiUrlTpl = "https://ads-api.x.com/12/measurement/conversions/"

type Maidian struct {
	Twitter struct {
		BackEnd struct {
			PixelID              string `json:"PIXEL_ID"`
			AccessToken          string `json:"ACCESS_TOKEN"`
			CompleteRegistration string `json:"CompleteRegistration"`
			Purchase             string `json:"Purchase"`
			AddToCart            string `json:"AddToCart"`
			FirstRecharge        string `json:"FirstRecharge"`
		} `json:"BackEnd"`
		Front struct {
			PixelID              string `json:"PIXEL_ID"`
			CompleteRegistration string `json:"CompleteRegistration"`
			Purchase             string `json:"Purchase"`
			AddToCart            string `json:"AddToCart"`
			FirstRecharge        string `json:"FirstRecharge"`
		} `json:"Front"`
	} `json:"Twitter"`
}

var mu sync.Mutex

func loadConfig(host string) {
	mu.Lock()
	defer mu.Unlock()
	xChannelHost := server.DaoxHashGame().XChannelHost
	hostConfig, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	if hostConfig == nil {
		logs.Error("暂无Twitter埋点配置！")
		return
	}

	if hostConfig.Maidian == "" {
		logs.Error("暂无Twitter埋点配置")
		return
	}

	var cfg Maidian

	err := json.Unmarshal([]byte(hostConfig.Maidian), &cfg)
	if err != nil {
		logs.Error(err)
		return
	}

	clients[host] = &Client{
		host:                 host,
		api:                  apiUrlTpl + cfg.Twitter.BackEnd.PixelID,
		pixelId:              cfg.Twitter.BackEnd.PixelID,
		accessToken:          cfg.Twitter.BackEnd.AccessToken,
		completeRegistration: cfg.Twitter.BackEnd.CompleteRegistration,
		purchase:             cfg.Twitter.BackEnd.Purchase,
		addToCart:            cfg.Twitter.BackEnd.AddToCart,
		firstRecharge:        cfg.Twitter.BackEnd.FirstRecharge,
	}

	return
}
