package third

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/zhms/xgo/xgo"
)

//登录调用auth balance 投注rollout 结算rollin 补单roundcheck 退款refund

func NewAstarSwSrvice(channelId, vendorToken, gameUrl, reportUrl, currency string, cburl string, fc func(int) error) *AstarSwSrvice {
	return &AstarSwSrvice{
		ChannelId:             channelId,
		VendorToken:           vendorToken,
		GameUrl:               gameUrl,
		ReportUrl:             reportUrl,
		Currency:              currency,
		playerTokenEncryptKey: "dsw3CC3y3aIoKDrJAodQRHslHPF4ziqa",
		detailTokenEncryptKey: "oDig6KSWOLroKRBQpJScjdiESQtuezSo",
		brandName:             "astar",
		cacheKey:              "astarPlayerToken:",
		callbackurl:           cburl,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

type AstarSwSrvice struct {
	ChannelId             string
	VendorToken           string
	GameUrl               string
	ReportUrl             string
	Currency              string
	playerTokenEncryptKey string
	detailTokenEncryptKey string
	brandName             string
	cacheKey              string
	callbackurl           string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

// lang support
// zh_TW, 繁体中文
// zh_CN, 简体中文
// en_US, 英文
// vi_VN, 越南文
// pt_PT, 葡萄牙语-alpha
// hi_IN, 印度语-alpha
// fr_FR, 法语-alpha
// es_ES, 西班牙语-alpha
// de_DE, 德语-alpha

func (a *AstarSwSrvice) userId2token(userId int) string {
	token := uuid.NewString()
	if err := server.Redis().SetStringEx(a.cacheKey+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("AstarSwSrvice userId2token set redis error:%s", err)
	}
	return token
}

func (a *AstarSwSrvice) token2UserId(token string) int {
	redisdata := server.Redis().Get(a.cacheKey + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

func (a *AstarSwSrvice) GameLaunch(ctx *abugo.AbuHttpContent) {
	// 打印请求体

	type RequestData struct {
		Language string `json:"language"`
		TableId  interface{}
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	token := server.GetToken(ctx)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
	if data == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	param := url.Values{}
	param.Add("token", a.userId2token(token.UserId))
	if reqdata.TableId != nil {
		param.Add("tableId", fmt.Sprintf("%v", reqdata.TableId))
	}

	if reqdata.Language != "" {
		param.Add("language", reqdata.Language)
	}

	url := fmt.Sprintf("%s/%s?%s", strings.TrimRight(a.GameUrl, "/"), a.ChannelId, param.Encode())

	ctx.Put("url", url)
	ctx.RespOK()
}

func (a *AstarSwSrvice) GameReport(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ThirdId string `validate:"required"`
		Lang    string
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	detailToken := CBCEncrypt(reqdata.ThirdId, a.detailTokenEncryptKey)
	url := fmt.Sprintf("%s?token=%s&channel=%s", a.ReportUrl, detailToken, a.ChannelId)
	if reqdata.Lang != "" {
		url += "&language=" + reqdata.Lang
	}
	ctx.Put("url", url)
	ctx.RespOK()
}

func (a *AstarSwSrvice) getUser(userId int) (udata *map[string]interface{}, balance float64, err error) {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	udata, err = server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		return nil, 0, err
	}
	if udata == nil {
		logs.Error("astar sw getUser failed, uid: %d", userId)
		return nil, 0, errors.New("user not found")
	}
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	return udata, balance, nil
}

func (a *AstarSwSrvice) initResponse(code, message string) gin.H {
	if code == "" {
		code = "0"
		message = "success"
	}
	return gin.H{
		"data": nil,
		"status": gin.H{
			"code":      code,
			"message":   message,
			"datetime":  time.Now().Format(time.RFC3339),
			"tracecode": uuid.NewString(),
		},
	}
}

func (a *AstarSwSrvice) errorResponse(ctx *abugo.AbuHttpContent, code, message string) {
	if code == "1" && message == "" {
		message = "Insufficient balance"
	}
	if code == "2" && message == "" {
		message = "Player not found"
	}
	if code == "7" && message == "" {
		message = "Bad parameters"
	}
	if code == "9" && message == "" {
		message = "Invalid GameToken"
	}
	if code == "16" && message == "" {
		message = "Data not found"
	}
	r := a.initResponse(code, message)
	ctx.RespJson(r)
	logs.Debug("astar errorResponse code: %s, message: %s", code, message)
}

func (a *AstarSwSrvice) checkApiToken(ctx *abugo.AbuHttpContent) bool {
	inputToken := ctx.Gin().GetHeader("Authorization")
	logs.Debug("astar inputToken : %s, VendorToken: %s", inputToken, a.VendorToken)
	b := inputToken == a.VendorToken
	if !b {
		logs.Error("astar checkApiToken failed got token: %s", inputToken)
		a.errorResponse(ctx, "5", "Authorization invalid")
	}
	return b
}

func (a *AstarSwSrvice) Auth(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	gametoken := ctx.Gin().PostForm("gametoken")
	logs.Debug("astar Auth api receive: gametoken:%s", gametoken)

	userId := a.token2UserId(gametoken)
	if userId == -1 {
		a.errorResponse(ctx, "9", "")
		return
	}

	user, balance, err := a.getUser(userId)
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	betthreshold := `{"maximum":0,"minimum":0}`
	// none prod env set new params
	//if strings.Index(xgo.Env(), "prd") < 0 {
	//	betthreshold = `{"maximum":0,"minimum":0}`
	//}

	data := gin.H{
		"account":      abugo.GetStringFromInterface((*user)["Account"]),
		"balance":      balance,
		"betthreshold": betthreshold,
		"currency":     a.Currency,
		"id":           strconv.Itoa(userId),
		"parentid":     abugo.GetStringFromInterface((*user)["AgentId"]),
	}

	res := a.initResponse("", "")
	res["data"] = data
	ctx.RespJson(res)
}

func (a *AstarSwSrvice) GetBalance(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	id := ctx.Gin().Param("id")
	logs.Debug("astar GetBalance api receive: id:%s, ", id)
	userId, err := strconv.Atoi(id)
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	_, balance, err := a.getUser(userId)
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	data := gin.H{
		"balance":  balance,
		"currency": a.Currency,
	}

	res := a.initResponse("", "")
	res["data"] = data
	ctx.RespJson(res)
}

func (a *AstarSwSrvice) Logout(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	gametoken := ctx.Gin().PostForm("gametoken")
	logs.Debug("astar Logout api receive: gametoken:%s, ", gametoken)

	userId := a.token2UserId(gametoken)
	if userId == -1 {
		a.errorResponse(ctx, "9", "")
		return
	}

	res := a.initResponse("", "")
	res["data"] = gin.H{}

	ctx.RespJson(res)

	// reqdata := struct {
	// 	Token string
	// }{}
	// ctx.RequestData(&reqdata)
	// server.Http().DelToken(reqdata.Token)
	// server.Http().DelToken(ctx.Token)
	// ctx.RespOK()
}

func (a *AstarSwSrvice) Rollout(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	gametoken := ctx.Gin().PostForm("gametoken")
	id := ctx.Gin().PostForm("id")             // 玩家 id
	mtcode := ctx.Gin().PostForm("mtcode")     // 交易代碼
	round := ctx.Gin().PostForm("round")       // 遊戲回合編號
	amount := ctx.Gin().PostForm("amount")     // 取款金額 ※最大長度為 12 位數，及小數點後 4 位
	datetime := ctx.Gin().PostForm("datetime") // 取款時間 "2017-01-20T01:44:33-04:00"
	gamecode := ctx.Gin().PostForm("gamecode") // 我司遊戲代碼
	ip := ctx.Gin().PostForm("ip")             // 客端 ip
	tableid := ctx.Gin().PostForm("tableid")   // 客端 ip

	reqDataByte, _ := json.Marshal(gin.H{
		"gametoken": gametoken,
		"id":        id,
		"mtcode":    mtcode,
		"round":     round,
		"amount":    amount,
		"datetime":  datetime,
		"gamecode":  gamecode,
		"tableid":   tableid,
		"ip":        ip,
	})
	logs.Debug("astar Rollout api receive:%s, ", string(reqDataByte))

	userId := a.token2UserId(gametoken)
	if userId == -1 {
		a.errorResponse(ctx, "9", "")
		return
	}

	betAmt, err := strconv.ParseFloat(amount, 64)
	if err != nil {
		a.errorResponse(ctx, "7", "")
		return
	}
	var thirdTime time.Time
	if thirdTime, err = time.Parse(time.RFC3339, datetime); err != nil {
		thirdTime = time.Now()
	}

	udata, balance, err := a.getUser(userId)
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}
	if betAmt > balance {
		a.errorResponse(ctx, "1", "")
		return
	}

	thirdId := a.parseMTcode(mtcode)
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", a.brandName, nil)
	betTran, _ := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if betTran != nil {
		dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
		if dataState >= 1 {
			a.errorResponse(ctx, "13", "下注失败，注单已存在")
			return
		}
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	_, err = server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmt, userId, betAmt)
	if err != nil {
		a.errorResponse(ctx, "1", "")
		return
	}
	balance -= betAmt
	if betTran != nil {
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = server.Db().Conn().Exec(`update x_third_live_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmt, betId)
		if err != nil {
			logs.Error("astar sw Rollout error", err)
			a.errorResponse(ctx, "3", "下注失败")
			return
		}
	} else {
		order := xgo.H{
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
			"BetChannelId": ChannelId,
			"UserId":       userId,
			"Brand":        a.brandName,
			"ThirdId":      thirdId,
			"GameId":       tableid,
			"GameName":     gamecode,
			"BetAmount":    betAmt,
			"WinAmount":    0,
			"ValidBet":     0,
			"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			"Currency":     a.Currency,
			"RawData":      string(reqDataByte),
			"DataState":    -1,
		}
		_, err = server.Db().Table("x_third_live_pre_order").Insert(order)
		if err != nil {
			logs.Error("astar sw Rollout error", err)
			a.errorResponse(ctx, "3", "下注失败")
			return
		}
	}

	amountLog := xgo.H{
		"UserId":       userId,
		"BeforeAmount": balance + betAmt,
		"Amount":       0 - betAmt,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonAstrarBet,
		"Memo":         "astrar bet,thirdId:" + thirdId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)
	res := a.initResponse("", "")
	res["data"] = gin.H{
		"amount":   betAmt,
		"balance":  balance,
		"currency": a.Currency,
	}

	logs.Debug("astar sw :", "下注成功 thirdId=", thirdId)
	ctx.RespJson(res)
	// 推送投注事件
	if a.thirdGamePush != nil {
		a.thirdGamePush.PushBetEvent(userId, gamecode, a.brandName, betAmt, a.Currency)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if a.RefreshUserAmountFunc != nil {
			tmpErr := a.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][AstarSwSrvice] Rollout 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][AstarSwSrvice] Rollout 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (a *AstarSwSrvice) Rollin(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	// Rollin amount = rollout amount + win - rake - roomfee

	gametoken := ctx.Gin().PostForm("gametoken")
	id := ctx.Gin().PostForm("id")                   // 玩家 id
	mtcode := ctx.Gin().PostForm("mtcode")           // 交易代碼
	round := ctx.Gin().PostForm("round")             // 遊戲回合編號
	amount := ctx.Gin().PostForm("amount")           // 結算金額 ※最大長度為 12 位數，及小數點後 4 位
	datetime := ctx.Gin().PostForm("datetime")       // 結算時間 "2017-01-20T01:44:33-04:00"
	gamecode := ctx.Gin().PostForm("gamecode")       // 我司遊戲代碼
	bet := ctx.Gin().PostForm("bet")                 // 本局押分
	win := ctx.Gin().PostForm("win")                 // win 金額(可為負值) ※含 jackpot、不含抽水
	rake := ctx.Gin().PostForm("rake")               // 抽水金額
	validbet := ctx.Gin().PostForm("validbet")       // 有效投注
	roomfee := ctx.Gin().PostForm("roomfee")         // 開房費用
	roundnumber := ctx.Gin().PostForm("roundnumber") // 局號
	tabletype := ctx.Gin().PostForm("tabletype")     // 真人遊戲類別 1百家乐, 2龙虎, 3轮盘, 4骰宝, 5斗牛
	tableid := ctx.Gin().PostForm("tableid")         // 桌號

	reqDataByte, _ := json.Marshal(gin.H{
		"gametoken":   gametoken,
		"id":          id,
		"mtcode":      mtcode,
		"round":       round,
		"amount":      amount,
		"datetime":    datetime,
		"gamecode":    gamecode,
		"bet":         bet,
		"win":         win,
		"rake":        rake,
		"validbet":    validbet,
		"roomfee":     roomfee,
		"roundnumber": roundnumber,
		"tabletype":   tabletype,
		"tableid":     tableid,
	})
	logs.Debug("astar Rollin api receive:%s, ", string(reqDataByte))

	userId := a.token2UserId(gametoken)
	if userId == -1 {
		a.errorResponse(ctx, "9", "")
		return
	}

	payMoney, err := strconv.ParseFloat(amount, 64)
	if err != nil {
		a.errorResponse(ctx, "7", "")
		return
	}
	validbetAmt, err := strconv.ParseFloat(validbet, 64)
	if err != nil {
		a.errorResponse(ctx, "7", "")
		return
	}

	tableTypeId, _ := strconv.Atoi(a.removePrefix(tabletype))
	gameName := tabletype
	switch tableTypeId {
	case 1:
		gameName = "百家乐"
	case 2:
		gameName = "龙虎"
	case 3:
		gameName = "轮盘"
	case 4:
		gameName = "骰宝"
	case 5:
		gameName = "斗牛"
	}

	// var thirdTime time.Time
	// if thirdTime, err = time.Parse(time.RFC3339, datetime); err != nil {
	// 	thirdTime = time.Now()
	// }

	thirdId := a.parseMTcode(mtcode)
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", a.brandName, nil)
	betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		a.errorResponse(ctx, "11", "Invalid MTCode")
		return
	}
	betId := abugo.GetInt64FromInterface((*betTran)["Id"])
	winAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"])
	if winAmount > 0 {
		a.errorResponse(ctx, "14", "Rollin is exist")
		return
	}

	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	// 除了体育外有效流水取不大于下注金额的输赢绝对值
	validbetAmt = math.Abs(payMoney - betAmount)
	if validbetAmt > math.Abs(betAmount) {
		validbetAmt = math.Abs(betAmount)
	}

	server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", payMoney, userId)
	server.Db().Conn().Exec(`update x_third_live_pre_order set
		WinAmount = ?,
		ValidBet = ?,
		GameName = ?,
		RawData = ?,
		DataState = 1
		where Id = ?`,
		payMoney,
		validbetAmt,
		gameName,
		string(reqDataByte),
		betId,
	)

	udata, balance, err := a.getUser(userId)
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	betTranData := *betTran
	delete(betTranData, "Id")
	delete(betTranData, "CreateTime")

	// 获取游戏名称
	gameListDao := server.DaoxHashGame().XGameList
	gameListDb := gameListDao.WithContext(nil)
	gameInfos, _ := gameListDb.Where(gameListDao.GameID.Eq(betTranData["GameId"].(string))).Where(gameListDao.Brand.Eq("astar")).First()
	betTranData["GameName"] = gameName
	if gameInfos != nil {
		betTranData["GameName"] = gameInfos.Name
	}

	betTranData["WinAmount"] = payMoney
	betTranData["ValidBet"] = validbetAmt
	betTranData["RawData"] = string(reqDataByte)
	betTranData["DataState"] = 1
	// 获取订单链接
	detailToken := CBCEncrypt(thirdId, a.detailTokenEncryptKey)
	betOrderUrl := fmt.Sprintf("%s?token=%s&language=zh-cn&channel=%s", a.ReportUrl, detailToken, a.ChannelId)
	betTranData["BetCtx"] = betOrderUrl
	betTranData["BetCtxType"] = 2
	server.Db().Table("x_third_live").Insert(betTranData)

	amountLog := xgo.H{
		"UserId":       userId,
		"BeforeAmount": balance - payMoney,
		"Amount":       payMoney,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonAstrarWin,
		"Memo":         "astrar settle,thirdId:" + thirdId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)
	res := a.initResponse("", "")
	res["data"] = gin.H{
		"balance":  balance,
		"currency": a.Currency,
	}

	logs.Debug("astar sw :", "結算成功 thirdId=", thirdId)
	ctx.RespJson(res)

	// 推送派奖事件
	if a.thirdGamePush != nil {
		//betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
		//a.thirdGamePush.PushRewardEvent(userId, gameName, a.brandName, betAmount, payMoney, a.Currency)
		a.thirdGamePush.PushRewardEvent(5, a.brandName, thirdId)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if a.RefreshUserAmountFunc != nil {
			tmpErr := a.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (a *AstarSwSrvice) CheckDetailToken(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	detailToken := ctx.Gin().PostForm("token")
	logs.Debug("astar Auth api receive: token:%s, ", detailToken)

	thirdId := CBCDecrypt(detailToken, a.detailTokenEncryptKey)
	if thirdId == "" {
		a.errorResponse(ctx, "4", "Token invalid")
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", a.brandName, nil)
	betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		a.errorResponse(ctx, "16", "")
		return
	}

	rawData := gin.H{}
	json.Unmarshal([]byte(abugo.GetStringFromInterface((*betTran)["RawData"])), &rawData)

	// 获取用户account
	userId := abugo.GetInt64FromInterface((*betTran)["UserId"])
	where = abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	user, _ := server.Db().Table("x_user").Select("Account").Where(where).GetOne()
	if user == nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	data := gin.H{
		"id":       abugo.GetStringFromInterface((*betTran)["UserId"]),
		"account":  abugo.GetStringFromInterface((*user)["Account"]),
		"roundid":  rawData["round"],
		"paccount": "",
	}

	res := a.initResponse("", "")
	res["data"] = data

	ctx.RespJson(res)
}

// 获取未完成订单
func (a *AstarSwSrvice) GameRoundCheck(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	fromdate := ctx.Gin().Query("fromdate") // 開始時間
	todate := ctx.Gin().Query("todate")     // 結束時間

	reqDataByte, _ := json.Marshal(gin.H{
		"fromdate": fromdate,
		"todate":   todate,
	})
	logs.Debug("astar GameRoundCheck api receive:%s, ", string(reqDataByte))

	startTime, err := time.Parse(time.RFC3339, fromdate)
	if err != nil {
		a.errorResponse(ctx, "7", "")
		return
	}
	endTime, err := time.Parse(time.RFC3339, todate)
	if err != nil {
		a.errorResponse(ctx, "7", "")
		return
	}
	// startTime = startTime.Add(-12 * time.Hour) // 中间过渡，因为之前是返回的查询时间晚了12小时，中间过渡，把早12小时的订单全部返回，解决了注单之后注释掉变为正常
	locAsia, _ := time.LoadLocation("Asia/Shanghai")

	where := abugo.AbuDbWhere{}
	where.Add("and", "Brand", "=", a.brandName, nil)
	where.Add("and", "CreateTime", ">=", startTime.In(locAsia), nil)
	where.Add("and", "CreateTime", "<=", endTime.In(locAsia), nil)
	where.Add("and", "DataState", "=", -1, "")
	sel := `ThirdId`
	betTrans, _ := server.Db().Table("x_third_live_pre_order").Select(sel).Where(where).OrderBy("CreateTime DESC").GetList()
	indexids := []string{}

	if betTrans != nil {
		for _, v := range *betTrans {
			thirdId := abugo.GetStringFromInterface(v["ThirdId"])
			indexids = append(indexids, thirdId)
		}
	}

	res := a.initResponse("", "")
	res["data"] = indexids

	logs.Debug("astar sw :", "获取未完成订单成功 startTime.In(locAsia)=", startTime.In(locAsia), " endTime.In(locAsia)=", endTime.In(locAsia), " indexids=", indexids)
	ctx.RespJson(res)
}

func (a *AstarSwSrvice) Refund(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != a.callbackurl {
			return
		}
	}

	if !a.checkApiToken(ctx) {
		return
	}

	userid := ctx.Gin().PostForm("userid") // 玩家 id
	mtcode := ctx.Gin().PostForm("mtcode") // 交易代碼

	reqDataByte, _ := json.Marshal(gin.H{
		"userid": userid,
		"mtcode": mtcode,
	})
	logs.Debug("astar Refund api receive:%s, ", string(reqDataByte))

	thirdId := a.parseMTcode(mtcode)
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", a.brandName, nil)
	betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		a.errorResponse(ctx, "16", "")
		return
	}
	betId := abugo.GetInt64FromInterface((*betTran)["Id"])
	userId := abugo.GetInt64FromInterface((*betTran)["UserId"])
	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	winAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"])

	if betAmount == 0 {
		a.errorResponse(ctx, "18", "Already refunded")
		return
	}

	refundMoney := betAmount - winAmount
	server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", refundMoney, userId)
	server.Db().Conn().Exec("update x_third_live_pre_order set BetAmount = 0, WinAmount = 0, ValidBet = 0, DataState = -2 where Id = ?", betId)

	udata, balance, err := a.getUser(int(userId))
	if err != nil {
		a.errorResponse(ctx, "2", "")
		return
	}

	amountLog := xgo.H{
		"UserId":       userId,
		"BeforeAmount": balance - refundMoney,
		"Amount":       refundMoney,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonAstrarCancel,
		"Memo":         "astrar cancel,thirdId:" + thirdId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)

	res := a.initResponse("", "")
	res["data"] = gin.H{
		"balance":  balance,
		"currency": a.Currency,
	}

	logs.Debug("astar sw :", "取消下注成功 thirdId=", thirdId)
	ctx.RespJson(res)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if a.RefreshUserAmountFunc != nil {
			tmpErr := a.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][AstarSwSrvice] Refund 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][AstarSwSrvice] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(int(userId))
}

func (a *AstarSwSrvice) parseMTcode(mtcode string) (roundid string) {
	datas := strings.Split(mtcode, "-")
	return datas[len(datas)-1]
}

func (a *AstarSwSrvice) removePrefix(data string) string {
	return strings.TrimLeft(data, a.ChannelId)
}
