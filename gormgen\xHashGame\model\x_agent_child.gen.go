// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentChild = "x_agent_child"

// XAgentChild mapped from table <x_agent_child>
type XAgentChild struct {
	ID         int32     `gorm:"column:Id;not null" json:"Id"`
	UserID     int32     `gorm:"column:UserId;primaryKey;comment:代理id" json:"UserId"` // 代理id
	SellerID   int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID  int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	Child      int32     `gorm:"column:Child;primaryKey;comment:下级id" json:"Child"`                                     // 下级id
	ChildLevel int32     `gorm:"column:ChildLevel;primaryKey;comment:下级层级,0直属下级,数值越大代理层级越深" json:"ChildLevel"`          // 下级层级,0直属下级,数值越大代理层级越深
	DataState  int32     `gorm:"column:DataState;not null;comment:数据统计状态" json:"DataState"`                             // 数据统计状态
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:关系生成时间" json:"CreateTime"` // 关系生成时间
}

// TableName XAgentChild's table name
func (*XAgentChild) TableName() string {
	return TableNameXAgentChild
}
