// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentIndependence = "x_agent_independence"

// XAgentIndependence mapped from table <x_agent_independence>
type XAgentIndependence struct {
	ID                      int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`                        // id
	CommissionID            int32     `gorm:"column:CommissionId;comment:佣金方案ID" json:"CommissionId"`                              // 佣金方案ID
	SellerID                int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                         // 运营商
	ChannelID               int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                                        // 渠道
	UserID                  int32     `gorm:"column:UserId;comment:代理id" json:"UserId"`                                            // 代理id
	Host                    string    `gorm:"column:Host;comment:代理域名" json:"Host"`                                                // 代理域名
	CustomeService          string    `gorm:"column:CustomeService;comment:自己的客服" json:"CustomeService"`                           // 自己的客服
	IsSelfHost              int32     `gorm:"column:IsSelfHost;default:2;comment:是否独立域名" json:"IsSelfHost"`                        // 是否独立域名
	IsDuiHuan               int32     `gorm:"column:IsDuiHuan;default:1;comment:是否开启兑换" json:"IsDuiHuan"`                          // 是否开启兑换
	IsSelfBackOffice        int32     `gorm:"column:IsSelfBackOffice;default:2;comment:是否独立后台" json:"IsSelfBackOffice"`            // 是否独立后台
	IsSelfTgBot             int32     `gorm:"column:IsSelfTgBot;default:2;comment:是否独立机器人" json:"IsSelfTgBot"`                     // 是否独立机器人
	IsSelfActive            int32     `gorm:"column:IsSelfActive;default:2;comment:是否独立活动" json:"IsSelfActive"`                    // 是否独立活动
	IsSelfMajorGameOrder    int32     `gorm:"column:IsSelfMajorGameOrder;default:2;comment:是否自定义游戏主类" json:"IsSelfMajorGameOrder"` // 是否自定义游戏主类
	IsSelfMinorGameOrder    int32     `gorm:"column:IsSelfMinorGameOrder;default:2;comment:是否自定义游戏小类" json:"IsSelfMinorGameOrder"` // 是否自定义游戏小类
	IsSelfAddress           int32     `gorm:"column:IsSelfAddress;default:2;comment:是否独立地址" json:"IsSelfAddress"`                  // 是否独立地址
	IsSelfCustomService     int32     `gorm:"column:IsSelfCustomService;default:2;comment:是否独立客服" json:"IsSelfCustomService"`      // 是否独立客服
	MajorGameOrder          string    `gorm:"column:MajorGameOrder;comment:游戏大类排序" json:"MajorGameOrder"`                          // 游戏大类排序
	MinorGameOrder          string    `gorm:"column:MinorGameOrder;comment:游戏小类排序" json:"MinorGameOrder"`                          // 游戏小类排序
	CreateTime              time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`          // 创建时间
	CommissionRate          float64   `gorm:"column:CommissionRate;comment:返佣比例" json:"CommissionRate"`                            // 返佣比例
	Memo                    string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	TgInfo                  string    `gorm:"column:TgInfo;comment:独立机器人" json:"TgInfo"`                                           // 独立机器人
	ActiveInfo              string    `gorm:"column:ActiveInfo;comment:独立活动" json:"ActiveInfo"`                                    // 独立活动
	BackOffaceInfo          string    `gorm:"column:BackOffaceInfo;comment:独立后台信息" json:"BackOffaceInfo"`                          // 独立后台信息
	AgentCode               string    `gorm:"column:AgentCode;comment:代理邀请码" json:"AgentCode"`                                     // 代理邀请码
	State                   int32     `gorm:"column:State;default:1;comment:状态 1启用,2禁用" json:"State"`                              // 状态 1启用,2禁用
	Icon                    string    `gorm:"column:Icon;comment:网页标签icon" json:"Icon"`                                            // 网页标签icon
	AgentName               string    `gorm:"column:AgentName;comment:代理名称(后台显示用)" json:"AgentName"`                               // 代理名称(后台显示用)
	ShowName                string    `gorm:"column:ShowName;comment:网页标签显示名称" json:"ShowName"`                                    // 网页标签显示名称
	Logo                    string    `gorm:"column:Logo;comment:前端页面显示的logo" json:"Logo"`                                         // 前端页面显示的logo
	IsSuper                 int32     `gorm:"column:IsSuper;default:2;comment:每日零点是否盈利清理" json:"IsSuper"`                          // 每日零点是否盈利清理
	AgentUseID              int32     `gorm:"column:AgentUseId" json:"AgentUseId"`
	MajorGameOrderNew       string    `gorm:"column:MajorGameOrderNew;comment:新游戏大类排序" json:"MajorGameOrderNew"`                                // 新游戏大类排序
	IsSelfMajorGameOrderNew int32     `gorm:"column:IsSelfMajorGameOrderNew;default:2;comment:新版分类是否开启1:开启2:关闭" json:"IsSelfMajorGameOrderNew"` // 新版分类是否开启1:开启2:关闭
	Logo2                   string    `gorm:"column:Logo2;comment:Logo2" json:"Logo2"`                                                          // Logo2
	PromotionHost           string    `gorm:"column:PromotionHost;comment:推广域名" json:"PromotionHost"`                                           // 推广域名
	IosIcon                 string    `gorm:"column:IosIcon;comment:标签ICON" json:"IosIcon"`                                                     // 标签ICON
}

// TableName XAgentIndependence's table name
func (*XAgentIndependence) TableName() string {
	return TableNameXAgentIndependence
}
