package model

type RecodeData struct {
	Id            int     `gorm:"column:Id"`
	GameId        int     `gorm:"column:GameId"`
	Memo          string  `gorm:"column:Memo"`
	RewardAmount  float64 `gorm:"column:RewardAmount"`
	CreateTime    string  `gorm:"column:CreateTime"`
	BlockHash     string  `gorm:"column:BlockHash"`
	BlockNum      int     `gorm:"column:BlockNum"`
	Symbol        string  `gorm:"column:Symbol"`
	Amount        float64 `gorm:"column:Amount"`
	IsWin         int     `gorm:"column:IsWin"`
	State         int     `gorm:"column:State"`
	Period        string  `gorm:"column:Period"`
	NextBlockHash string  `gorm:"column:NextBlockHash"`
	BetArea       string  `gorm:"column:Bet<PERSON>rea"`
	LiuSui        float64 `gorm:"column:LiuSui"`
	TxId          string  `gorm:"column:TxId"`
	OpenArea      string  `gorm:"column:OpenArea"`
}

type UtOrderList struct {
	Id            int32   `gorm:"column:Id"`
	GameId        int32   `gorm:"column:GameId"`
	Memo          string  `gorm:"column:Memo"`
	RewardAmount  float64 `gorm:"column:RewardAmount"`
	CreateTime    string  `gorm:"column:CreateTime"`
	BlockHash     string  `gorm:"column:BlockHash"`
	BlockNum      int32   `gorm:"column:BlockNum"`
	Symbol        string  `gorm:"column:Symbol"`
	Amount        float64 `gorm:"column:Amount"`
	IsWin         int32   `gorm:"column:IsWin"`
	State         int32   `gorm:"column:State"`
	Period        string  `gorm:"column:Period"`
	NextBlockHash string  `gorm:"column:NextBlockHash"`
	BetArea       string  `gorm:"column:BetArea"`
	TxId          string  `gorm:"column:TxId"`
	OpenArea      string  `gorm:"column:OpenArea"`
	UserAmount    float64 `gorm:"column:UserAmount"`
}
