package base

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	val "github.com/go-playground/validator/v10"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"sort"
	"time"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
)

// CacheRequest 防止三方接口重复提交的(如非幂等性重要操作)，具体办法是当请求第一次提交时将
// 请求参数进行sign签名作为value保存到redis,key为订单号，并设置超时时间，
// 当同一个请求第二次访问时会先检测redis中的key与当前参数加sign对比，如果相同则证明重复提交了，
// 接口就不再继续调用了，返回前一次响应
// CacheRequest 用于存储请求缓存数据结构
type CacheRequest struct {
	Req string      `json:"req"`
	Res interface{} `json:"res"`
}

// CacheSignKey CacheRequest 用于存储请求缓存数据结构
type CacheSignKey struct {
	Brand   string `validate:"required"` //三方厂商代码
	ReqUrl  string `validate:"required"` //请求方法名
	ThirdId string `validate:"required"` //三方注单ID
	UserId  int    `validate:"required"` //用户ID
}

// sortJSONKeys 对JSON对象的键按照字母顺序进行排序
func sortJSONKeys(data map[string]interface{}) []string {
	keys := make([]string, 0, len(data))
	for key := range data {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	return keys
}

// sortJSONParams 对JSON对象的键按照字母顺序进行排序，并返回排序后的JSON字符串
func sortJSONParams(data map[string]interface{}) (string, error) {
	keys := sortJSONKeys(data)
	sortedData := make(map[string]interface{})
	for _, key := range keys {
		sortedData[key] = data[key]
	}

	sortedBytes, err := json.Marshal(sortedData)
	if err != nil {
		return "", err
	}

	return string(sortedBytes), nil
}

// calculateMD5 计算在排除某些字段后的数据的按键升序排序后的MD5哈希值
// excludeFields 支持以下格式：
// - "timestamp" - 排除顶层字段
// - "provider" - 排除整个provider对象及其所有子字段
// 示例:
// excludeFields := []string{"provider"} // 这将排除整个 provider 对象
// excludeFields := []string{"timestamp", "provider"} // 排除 timestamp 和整个 provider 对象
func calculateMD5(data map[string]interface{}, excludeFields []string) (string, error) {
	// 默认排除的字段
	defaultExcludes := []string{"timestamp", "Timestamp"}
	// 合并默认排除字段和传入的排除字段
	excludeFields = append(excludeFields, defaultExcludes...)

	// 创建一个新的映射，排除指定的字段
	filteredData := make(map[string]interface{})
	for key, value := range data {
		if !contains(excludeFields, key) {
			filteredData[key] = value
		}
	}

	// 对数据按键进行升序排序
	sortedParams, err := sortJSONParams(filteredData)
	if err != nil {
		return "", err
	}

	// 计算MD5哈希值
	hash := md5.Sum([]byte(sortedParams))
	md5Hash := hex.EncodeToString(hash[:])
	//logs.Info("MD5加签名排序字段，sortedParams=", sortedParams, "md5=", md5Hash)
	return md5Hash, nil
}

// SetRequestCacheRedis 使用MD5哈希值作为值在Redis中设置请求缓存
func SetRequestCacheRedis(cacheSignKey CacheSignKey, reqData interface{}, resData interface{}, excludeFields []string) error {
	validator := val.New()
	err := validator.Struct(cacheSignKey)
	if err != nil {
		return fmt.Errorf("参数不能为空 %v", err)
	}
	expireTime := 1800 //30分钟缓存
	cacheKeyTa := fmt.Sprintf("dedup:brand=%vM=%vU=%vT=%v",
		cacheSignKey.Brand, cacheSignKey.ReqUrl, cacheSignKey.UserId, cacheSignKey.ThirdId)
	reqDataMap := map[string]interface{}{}
	reqDataBytes, err := json.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("序列化请求数据时出错: %v", err)
	}

	json.Unmarshal(reqDataBytes, &reqDataMap)
	reqDataMD5, err := calculateMD5(reqDataMap, excludeFields)
	if err != nil {
		return fmt.Errorf("计算MD5哈希值时出错: %v", err)
	}
	cacheData := CacheRequest{}
	cacheData.Req = reqDataMD5
	cacheData.Res = resData
	cacheValueByte, err := json.Marshal(cacheData)
	if err := server.Redis().SetStringEx(cacheKeyTa, expireTime, string(cacheValueByte)); err != nil {
		return fmt.Errorf("在Redis中设置缓存时出错: %v", err)
	}

	return nil
}

// CheckDuplicateRedis
/**
 * 使用MD5哈希值检查重复请求（从redis中检查）
 * 防止重复提交数据，检查当前请求数据的MD5值和从Redis中的取得的MD5值是否相等，如果相等则表示数据已经处理过，直接返回上次的响应
 * cacheSignKey 唯一key生成格式： 三方厂商:请求方法:用户ID:三方订单ID
 * reqData 请求结构体
 * excludeFields 需要排除的字段, 如timestamp每次请求都不一样,需要剔除,否则判断失效
 * 返回上次响应报文
 */
func CheckDuplicateRedis(cacheSignKey CacheSignKey, reqData interface{}, excludeFields []string) (interface{}, error) {
	validator := val.New()
	err := validator.Struct(cacheSignKey)
	if err != nil {
		return nil, fmt.Errorf("参数不能为空 %v", err)
	}
	cacheKeyTa := fmt.Sprintf("dedup:brand=%vM=%vU=%vT=%v",
		cacheSignKey.Brand, cacheSignKey.ReqUrl, cacheSignKey.UserId, cacheSignKey.ThirdId)

	reqDataMap := map[string]interface{}{}
	reqDataBytes, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据时出错: %v", err)
	}
	json.Unmarshal(reqDataBytes, &reqDataMap)
	reqDataMD5, err := calculateMD5(reqDataMap, excludeFields)
	if err != nil {
		return nil, fmt.Errorf("计算MD5哈希值时出错: %v", err)
	}
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		err := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes)
		if err != nil {
			return nil, err
		}
		if cacheRes.Req == reqDataMD5 {
			return cacheRes.Res, nil
		}
	}
	return nil, nil
}

// contains 检查字符串是否存在于字符串切片中
func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// CheckDuplicateDB /**
/**
 * 判断是否是重复请求（从数据库判断）
 */
func CheckDuplicateDB(brandName string, thirdRefId string, reqUrl string) (bool, interface{}, error) {
	thirdReqInfo := thirdGameModel.ThirdReqInfo{}
	err := server.Db().GormDao().Table("x_third_request_info").Where("Brand=? and ReqId = ? and ReqUrl = ? ", brandName, thirdRefId, reqUrl).First(&thirdReqInfo).Error
	if err == nil {
		logs.Error(brandName, " 请求序列号重复请求，thirdId=", thirdRefId)
		var resData interface{}
		err := json.Unmarshal([]byte(thirdReqInfo.RespBody), &resData)
		if err == nil {
			return true, resData, nil
		} else {
			logs.Error(brandName, " 查询请求序列号是否存在 解析原响应内容错误 err=", err.Error())
			return true, nil, err
		}
	} else {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			return false, nil, nil // No duplicate request
		}
		logs.Error(brandName, " 查询请求序列号是否存在 发生错误 thirdRefId=", thirdRefId, " err=", err.Error())
		return false, nil, err // 其它错误
	}
}

/**
 * 记录请求日志（保存到数据库）
 */
func AddRequestDB(reqId string, reqBody string, respData interface{}, respCode int, brandName string, url string) {
	respBodyBytes, _ := json.Marshal(respData)
	// 记录请求响应日志
	thirdReqInfo := thirdGameModel.ThirdReqInfo{
		Brand:         brandName,
		ReqId:         reqId, //唯一请求Id
		ReqUrl:        url,
		ReqBody:       reqBody,
		RespBody:      string(respBodyBytes),
		RepeatReqData: "[]",
		Code:          respCode, // 0成功 非0异常
		CreatedAt:     time.Now().Unix(),
	}
	thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
	if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
		Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
		DoUpdates: daogormclause.Assignments(map[string]interface{}{
			"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
		}),
	}).Create(&thirdReqInfo).Error; e != nil {
		logs.Error(brandName, reqBody, " 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
	}
}
