package single

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// OMG单一钱包类

type OmgSingleService struct {
	url                   string
	appId                 string
	key                   string
	currency              string
	brandName             string
	cacheKeyOmg           string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func NewOmgSingleService(params map[string]string, fc func(int) error) *OmgSingleService {
	return &OmgSingleService{
		url:                   params["url"],
		appId:                 params["app_id"],
		key:                   params["key"],
		currency:              params["currency"],
		brandName:             "omg",
		cacheKeyOmg:           params["cacheKeyOmg"],
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

//const cacheKeyOmg = "cacheKeyOmg:"

// Omg返回错误码
const (
	Omg_Code_Success                                  = 0     // 成功
	Omg_Code_Fail_Not_Enough_Balance                  = 10001 // 10001	余额不足，下分失败
	Omg_Code_Fail_Sign_Verify_Fail                    = 10002 // 10002	签名验证未通过
	Omg_Code_Fail_Order_Processing                    = 10003 // 10003	订单正在处理中
	Omg_Code_Fail_Order_Not_Exist                     = 10004 // 10004	订单不存在
	Omg_Code_Fail_Order_Repeat                        = 10005 // 10005	订单重复
	Omg_Code_Fail_Request_Too_Frequent                = 10006 // 10006	请求过于频繁
	Omg_Code_Fail_Game_Playing_Can_Not_Change_Balance = 10007 // 10007	游戏中不能上下分
	Omg_Code_Fail_Game_Closed                         = 10008 // 10008	游戏已关闭
	Omg_Code_Fail_Illegal_Parameter                   = 10009 // 10009	非法参数
	Omg_Code_Fail                                     = 10010 // 10010	失败
	Omg_Code_Fail_Token_Verify_Fail                   = 10011 // 10011	玩家登录token验证失败
	Omg_Code_Fail_Illegal_Appid                       = 10012 // 10012	非法的appid
	Omg_Code_Fail_User_Not_Exist                      = 10013 // 10013	玩家不存在
	Omg_Code_Fail_User_Banned                         = 10014 // 10014	玩家被禁止登录
	Omg_Code_Fail_Server_Internal_Error               = 10015 // 10015	服务器内部错误
	Omg_Code_Fail_Game_Not_Exist                      = 10016 // 10016	游戏不存在
	Omg_Code_Fail_Game_Not_Open                       = 10017 // 10017	游戏未开通
)

// 支持的语言 en英语,zh中文,es西班牙语,vi越南语,it意大利语,da丹麦语,fi芬兰语,fr法语,my缅甸语,nl荷兰语,nb书面挪威语,pl波兰语,ro罗马尼亚语,pt葡萄牙语,tr土耳其语,uk乌克兰语,de德语,th泰语,id印尼语,ja日语,ko韩语,ru俄语,sv瑞典语
var omgSupportLang = []string{"en", "zh", "es", "vi", "it", "da", "fi", "fr", "my", "nl", "nb", "pl", "ro", "pt", "tr", "uk", "de", "th", "id", "ja", "ko", "ru", "sv"}

// Login  进入游戏 /api/usr/ingame
func (l *OmgSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"`
		LangCode string `json:"LangCode" validate:"required"`
		Symbol   string `json:"Symbol" validate:""`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("omg_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	loginLang := "zh"
	for _, v := range omgSupportLang {
		if v == reqdata.LangCode {
			loginLang = reqdata.LangCode
			break
		}
	}
	//货币选择
	CurrencyId := 176 //默认货币ID
	switch strings.ToUpper(reqdata.Symbol) {
	case "USDT":
		CurrencyId = 176
		break
	case "PHP":
		CurrencyId = 165
		break
	case "THB":
		CurrencyId = 173
		break
	default:
	}
	//获取会员登录token
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(l.cacheKeyOmg, token.UserId); err != nil {
		logs.Error("omg_single 登录游戏 获取用户登录状态错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("omg_single Login 查询游戏错误 userId=", token.UserId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if gameList.OpenState != 1 {
		logs.Error("omg_single Login 游戏不可用 userId=", token.UserId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	LogintNick := token.Account
	loginToken := l.getTokenByUser(token.UserId, token.Account)
	if loginToken == "" {
		logs.Error("omg_single 登录游戏 生成token失败", token.UserId)
		ctx.RespErrString(true, &errcode, "生成Token失败")
		return
	}
	logs.Info("omg_single 登录游戏 开始 userId=", token.UserId, "reqdata=", reqdata)
	type RequestLoginData struct {
		GameId    string `json:"gameid"`
		Token     string `json:"token"` // 用户验证token，用于单一钱包的身份验证接口
		Lang      string `json:"lang"`
		Nick      string `json:"nick"`
		Appid     string `json:"appid"`
		Cid       int    `json:"cid"` //货币选择
		Timestamp int64  `json:"timestamp"`
	}
	type ResponseLoginData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			GameUrl string `json:"gameurl"`
		} `json:"data"`
	}
	reqLoginData := RequestLoginData{
		GameId:    reqdata.GameId,
		Token:     loginToken,
		Lang:      loginLang,
		Nick:      LogintNick,
		Appid:     l.appId,
		Cid:       CurrencyId,
		Timestamp: time.Now().Unix(),
	}
	respLoginData := ResponseLoginData{}
	//拼接请求url
	loginUrlParam := fmt.Sprintf("trace_id=%s", abugo.GetUuid())
	loginBodyByte, _ := json.Marshal(reqLoginData)
	loginSign := l.getSign(loginUrlParam, string(loginBodyByte))
	loginUrl := fmt.Sprintf("%s/api/usr/ingame?%s", l.url, loginUrlParam)

	client := &http.Client{}
	payload := bytes.NewReader(loginBodyByte)
	logs.Info("omg_single 登录游戏 请求接口参数 userId=", token.UserId, "reqdata=", reqLoginData)
	req, _ := http.NewRequest(http.MethodPost, loginUrl, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	req.Header.Add("sign", loginSign)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("omg_single 登录游戏 请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("omg_single 登录游戏 读取响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("omg_single 登录游戏 请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respLoginData)
	if err != nil {
		logs.Error("omg_single 登录游戏 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if respLoginData.Code != Omg_Code_Success {
		logs.Error("omg_single 登录游戏 登录失败 错误码=", respLoginData.Code)
		errcode = respLoginData.Code
		ctx.RespErrString(true, &errcode, respLoginData.Msg)
		return
	}
	ctx.RespOK(respLoginData.Data.GameUrl)
	return
}

// 计算签名字符串
func (l *OmgSingleService) getSign(urlParamStr, bodyStr string) (sign string) {
	src := fmt.Sprintf("%s%s%s", urlParamStr, bodyStr, l.key)
	sign = base.MD5(src)
	return
}

// 生成omg token
func (l *OmgSingleService) getTokenByUser(userId int, account string) (token string) {
	type OmgTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := l.cacheKeyOmg + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = l.cacheKeyOmg + token
		err := server.Redis().Expire(rKeyToken, 86500)
		if err != nil {
			logs.Error("omg_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " error=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("omg_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " error=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "omg_" + uuid.NewString()
	tokendata := OmgTokenData{
		UserId:  userId,
		Account: account,
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = l.cacheKeyOmg + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("omg_single getTokenByUser set redis rKeyUser=", rKeyUser, " error:%s", err)
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86500); err != nil {
		logs.Error("omg_single getTokenByUser set redis rKeyToken=", rKeyToken, " error:%s", err)
		token = ""
		return
	}
	return
}

// 验证token
func (l *OmgSingleService) getUserByToken(token string) (userId int, account string) {
	type OmgTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}
	if token == "" {
		return
	}
	rKeyToken := l.cacheKeyOmg + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := OmgTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("omg_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " error=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("omg_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// VerifySession verify_session 玩家身份验证 /api/luck/user/verify_session
func (l *OmgSingleService) VerifySession(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AppId                 string `json:"app_id" validate:"required"`
		Timestamp             int64  `json:"timestamp" validate:"required"`
		OperatorPlayerSession string `json:"operator_player_session" validate:"required"`
		Ip                    string `json:"ip"`
		CustomParameter       string `json:"custom_parameter"`
		GameId                int    `json:"game_id"`
	}
	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Uname    string  `json:"uname"`    // 用户名，最长30个字节，用户的唯一标识
			Nickname string  `json:"nickname"` // 用户呢称
			Balance  float64 `json:"balance"`  // 用户余额(最多支持4位小数)
		} `json:"data"`
	}
	respdata := ResponseData{
		Code: 1,
		Msg:  "ok",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("omg_single 玩家身份验证 读取请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "read request body error"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("omg_single 玩家身份验证 Request.Body=", string(bodyBytes))
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)

	if err != nil {
		logs.Error("omg_single 玩家身份验证 解析请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "json.Unmarshal body error"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.AppId != l.appId {
		logs.Error("omg_single 玩家身份验证 非法的appid reqdata.AppId=", reqdata.AppId)
		respdata.Code = Omg_Code_Fail_Illegal_Appid
		respdata.Msg = "illegal appid"
		ctx.RespJson(respdata)
		return
	}

	reqParam := ctx.Gin().Request.URL.String() // 校验签名
	urlIndex := strings.Index(reqParam, "?")
	if urlIndex > 0 {
		reqParam = reqParam[urlIndex+1:]
	} else {
		reqParam = ""
	}
	sign := ctx.Gin().Request.Header.Get("sign")
	newSign := l.getSign(reqParam, string(bodyBytes))
	if sign != newSign {
		logs.Error("omg_single 玩家身份验证 签名验证未通过 sign=", sign, " newSign=", newSign)
		respdata.Code = Omg_Code_Fail_Sign_Verify_Fail
		respdata.Msg = "sign verify fail"
		ctx.RespJson(respdata)
		return
	}

	userId, userAccount := l.getUserByToken(reqdata.OperatorPlayerSession) // 获取用户ID
	if userId <= 0 {
		logs.Error("omg_single 玩家身份验证 用户未登录 userId=", userId)
		respdata.Code = Omg_Code_Fail_Token_Verify_Fail
		respdata.Msg = "token verify fail"
		ctx.RespJson(respdata)
		return
	}
	userIdStr := fmt.Sprintf("%d", userId)

	// 获取用户余额
	_, balance, err := base.GetUserBalance(userId, l.cacheKeyOmg)
	if err != nil {
		logs.Error("omg_single 玩家身份验证 用户不存在 userId=", userId)
		respdata.Code = Omg_Code_Fail_User_Not_Exist
		respdata.Msg = "user not exist"
		ctx.RespJson(respdata)
		return
	}

	respdata.Data.Uname = userIdStr
	respdata.Data.Nickname = userAccount
	respdata.Data.Balance = balance
	ctx.RespJson(respdata)
	logs.Info("omg_single 玩家身份验证 响应成功 respdata=", respdata)
	return
}

// get_balance 获取玩家余额 /api/luck/balance/get_balance
func (l *OmgSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AppId string `json:"app_id" validate:"required"`
		//PlayerLoginToken string `json:"player_login_token"` omg已经删除改字段
		Uname     string `json:"uname" validate:"required"`
		GameId    int    `json:"game_id"`
		Timestamp int64  `json:"timestamp" validate:"required"`
	}
	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Balance float64 `json:"balance"` // 用户余额(最多支持4位小数)
		} `json:"data"`
	}
	respdata := ResponseData{
		Code: 1,
		Msg:  "ok",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("omg_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "read request body error"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("omg_single 获取玩家余额0 Request.Body=", string(bodyBytes))
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("omg_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "json.Unmarshal body error"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.AppId != l.appId {
		logs.Error("omg_single 获取玩家余额 非法的appid reqdata.AppId=", reqdata.AppId)
		respdata.Code = Omg_Code_Fail_Illegal_Appid
		respdata.Msg = "illegal appid"
		ctx.RespJson(respdata)
		return
	}
	// 校验签名
	reqParam := ctx.Gin().Request.URL.String()
	urlIndex := strings.Index(reqParam, "?")
	if urlIndex > 0 {
		reqParam = reqParam[urlIndex+1:]
	} else {
		reqParam = ""
	}
	sign := ctx.Gin().Request.Header.Get("sign")
	newSign := l.getSign(reqParam, string(bodyBytes))
	if sign != newSign {
		logs.Error("omg_single 获取玩家余额 签名验证未通过 sign=", sign, " newSign=", newSign)
		respdata.Code = Omg_Code_Fail_Sign_Verify_Fail
		respdata.Msg = "sign verify fail"
		ctx.RespJson(respdata)
		return
	}
	// 获取用户ID
	userId, err := strconv.Atoi(reqdata.Uname)
	logs.Info("omg_single 获取玩家余额获取到的1", "userId=", userId, "reqdata.Uname", reqdata.Uname)
	if err != nil {
		logs.Error("omg_single Error converting string to int:", reqdata.Uname)
		respdata.Code = Omg_Code_Fail_Token_Verify_Fail
		respdata.Msg = "token not exist"
		ctx.RespJson(respdata)
		return
	}

	if userId <= 0 {
		logs.Error("omg_single 玩家身份验证2 用户未登录 userId=", userId)
		respdata.Code = Omg_Code_Fail_Token_Verify_Fail
		respdata.Msg = "token not exist"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	_, balance, err := base.GetUserBalance(userId, l.cacheKeyOmg)
	if err != nil {
		logs.Error("omg_single 获取玩家余额3 用户不存在 userId=", userId)
		respdata.Code = Omg_Code_Fail_User_Not_Exist
		respdata.Msg = "user not exist"
		ctx.RespJson(respdata)
		return
	}

	respdata.Data.Balance = balance
	ctx.RespJson(respdata)
	logs.Info("omg_single 获取玩家余额4 响应成功 respdata=", respdata, "reqdata.Uname=", reqdata.Uname)
	return
}

// ChangeBalance change_balance 改变玩家余额 /api/luck/balance/change_balance
func (l *OmgSingleService) ChangeBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AppId    string  `json:"app_id" validate:"required"`
		BetStr   string  `json:"bet" validate:"required"`
		Bet      float64 `json:"betFloat" validate:"required"`
		Type     int     `json:"type" validate:"required"`
		GameId   int     `json:"game_id" validate:"required"`
		MoneyStr string  `json:"money" validate:"required"`
		Money    float64 `json:"moneyFloat" validate:"required"`
		OrderId  string  `json:"order_id" validate:"required"`
		//PlayerLoginToken string `json:"player_login_token"` omg已经删除改字段
		SessionId     string `json:"session_id" validate:"required"`
		Timestamp     int64  `json:"timestamp" validate:"required"`
		Uname         string `json:"uname" validate:"required"`
		EndRound      bool   `json:"end_round"`
		CancelOrderId string `json:"cancel_order_id"`
	}
	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Balance float64 `json:"balance"` // 用户余额(最多支持4位小数)
		} `json:"data"`
	}
	respdata := ResponseData{
		Code: 1,
		Msg:  "ok",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("omg_single 改变玩家余额1 读取请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "read request body error"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("omg_single 改变玩家余额2 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("omg_single 改变玩家余额2 解析请求消息体错误 err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "json.Unmarshal body error"
		ctx.RespJson(respdata)
		return
	}

	if reqdata.OrderId == "" {
		logs.Error("omg_single 改变玩家余额 请求序列号不能为空 reqdata.OrderId=", reqdata.OrderId)
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "order_id invalid error"
		ctx.RespJson(respdata)
		return
	}
	{
		// 成功处理过的order_id直接返回原数据
		thirdReqInfo := thirdGameModel.ThirdReqInfo{}
		err := server.Db().GormDao().Table("x_third_request_info").Where("Brand=? and ReqId = ?", l.brandName, reqdata.OrderId).First(&thirdReqInfo).Error
		if err == nil {
			logs.Error("omg_single 改变玩家余额 请求序列号重复请求 reqdata.OrderId=", reqdata.OrderId)
			if thirdReqInfo.Code == 0 && thirdReqInfo.RespBody != "" {
				// 已经成功处理过的请求，直接返回原来的数据
				err := json.Unmarshal([]byte(thirdReqInfo.RespBody), &respdata)
				if err == nil {
					logs.Error("omg_single 改变玩家余额 请求序列号重复请求 reqdata.OrderId=", reqdata.OrderId, " 直接返回原来的数据成功 respdata=", respdata)
					ctx.RespJson(respdata)
					return
				} else {
					logs.Error("omg_single 改变玩家余额 请求序列号重复请求 reqdata.OrderId=", reqdata.OrderId, " 解析原响应内容错误 err=", err.Error())
				}
			}
		} else {
			if err != daogorm.ErrRecordNotFound {
				logs.Error("omg_single 改变玩家余额 查询请求序列号是否存在错误 reqdata.OrderId=", reqdata.OrderId, " err=", err.Error())
			}
		}
	}

	defer func() {
		if reqdata.Type == 4 {
			// 不记录对局结束的请求，不下注对方也会一直推送
			return
		}
		tmpCode := respdata.Code
		if tmpCode == 1 {
			tmpCode = 0
		}
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.OrderId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          tmpCode, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("omg_single 改变玩家余额 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	reqdata.Bet, err = strconv.ParseFloat(reqdata.BetStr, 64)
	if err != nil {
		logs.Error("omg_single 改变玩家余额 解析请求消息体错误 thirdId=", reqdata.SessionId, " err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "Bet invalid error"
		ctx.RespJson(respdata)
		return
	}
	reqdata.Money, err = strconv.ParseFloat(reqdata.MoneyStr, 64)
	if err != nil {
		logs.Error("omg_single 改变玩家余额 解析请求消息体错误 thirdId=", reqdata.SessionId, " err=", err.Error())
		respdata.Code = Omg_Code_Fail_Illegal_Parameter
		respdata.Msg = "Money invalid error"
		ctx.RespJson(respdata)
		return
	}
	if reqdata.AppId != l.appId {
		logs.Error("omg_single 改变玩家余额 非法的appid reqdata.AppId=", reqdata.AppId, " thirdId=", reqdata.SessionId)
		respdata.Code = Omg_Code_Fail_Illegal_Appid
		respdata.Msg = "illegal appid"
		ctx.RespJson(respdata)
		return
	}
	// 校验签名
	reqParam := ctx.Gin().Request.URL.String()
	urlIndex := strings.Index(reqParam, "?")
	if urlIndex > 0 {
		reqParam = reqParam[urlIndex+1:]
	} else {
		reqParam = ""
	}
	sign := ctx.Gin().Request.Header.Get("sign")
	newSign := l.getSign(reqParam, string(bodyBytes))
	if sign != newSign {
		logs.Error("omg_single 改变玩家余额 签名验证未通过 sign=", sign, " newSign=", newSign, " thirdId=", reqdata.SessionId)
		respdata.Code = Omg_Code_Fail_Sign_Verify_Fail
		respdata.Msg = "sign verify fail"
		ctx.RespJson(respdata)
		return
	}

	// 校验参数
	if reqdata.Type == 1 { // 下注
		if reqdata.Bet <= 0 { // 下注必须是正数
			logs.Error("omg_single 改变玩家余额 下注金额不能小于0 reqdata.Bet=", reqdata.Bet, " thirdId=", reqdata.SessionId)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "bet must be positive"
			ctx.RespJson(respdata)
			return
		}
		if reqdata.Bet != -reqdata.Money { // 扣钱数必须等于下注金额
			logs.Error("omg_single 改变玩家余额 下注金额和金额不匹配 reqdata.Bet=", reqdata.Bet, " reqdata.Money=", reqdata.Money, " thirdId=", reqdata.SessionId)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "bet must be equal to -money"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Type == 2 { // 撤销下注
		if reqdata.Money <= 0 { // 金额必须是正数
			logs.Error("omg_single 改变玩家余额 撤销下注金额不能小于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.SessionId)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "money must be positive"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Type == 3 { // 结算
		if reqdata.Money < 0 { // 金额必须是正数
			logs.Error("omg_single 改变玩家余额 结算金额不能小于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.SessionId)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "money must be positive"
			ctx.RespJson(respdata)
			return
		}
	} else if reqdata.Type == 5 { // 其他
		if reqdata.Money <= 0 { // 金额必须是正数
			logs.Error("omg_single 改变玩家余额 派彩金额不能小于0 reqdata.Money=", reqdata.Money, " thirdId=", reqdata.SessionId)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "money must be positive"
			ctx.RespJson(respdata)
			return
		}
	}

	// 获取用户ID
	userId, err := strconv.Atoi(reqdata.Uname)
	if err != nil {
		logs.Error("omg_single Error converting string to int:", reqdata.Uname)
		respdata.Code = Omg_Code_Fail_Token_Verify_Fail
		respdata.Msg = "token not exist"
		ctx.RespJson(respdata)
		return
	}

	if userId <= 0 {
		logs.Error("omg_single 玩家身份验证 用户未登录 userId=", userId)
		respdata.Code = Omg_Code_Fail_Token_Verify_Fail
		respdata.Msg = "token not exist"
		ctx.RespJson(respdata)
		return
	}

	thirdId := fmt.Sprintf("%s_%s", reqdata.SessionId, reqdata.Uname)
	thirdTime := time.Now().Format("2006-01-02 15:04:05")
	gameId := fmt.Sprintf("%d", reqdata.GameId)

	// 判断游戏是否存在
	gameInfo := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, gameId).First(&gameInfo).Error
	if err != nil {
		logs.Error("omg_single 改变玩家余额 游戏不存在 gameId=", gameId, " error=", err.Error())
		respdata.Code = Omg_Code_Fail_Game_Not_Exist
		respdata.Msg = "game not exist"
		ctx.RespJson(respdata)
		return
	}
	gameName := gameInfo.Name

	// 暂时把所有的订单都放在电子中
	table := "x_third_dianzhi"
	tablePre := "x_third_dianzhi_pre_order"
	if gameInfo.GameType == 1 {
		table = "x_third_dianzhi"
		tablePre = "x_third_dianzhi_pre_order"
	} else if gameInfo.GameType == 3 {
		table = "x_third_quwei"
		tablePre = "x_third_quwei_pre_order"
	} else {
		logs.Error("omg_single 改变玩家余额 游戏类型错误 gameId=", gameId, " gameType=", gameInfo.GameType)
		respdata.Code = Omg_Code_Fail
		respdata.Msg = "gameType error"
		ctx.RespJson(respdata)
		return
	}

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		userBalance := thirdGameModel.UserBalance{}
		// 锁定用户余额
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("omg_single 改变玩家余额 下注 锁定用户余额失败 userId=", userId, " error=", e.Error())
			respdata.Code = Omg_Code_Fail
			respdata.Msg = "lock user balance fail"
			ctx.RespJson(respdata)
			return e
		}

		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

		respdata.Data.Balance = userBalance.Amount
		if reqdata.Money < 0 && userBalance.Amount+reqdata.Money < 0 {
			e = fmt.Errorf("余额不足")
			logs.Error("omg_single 改变玩家余额 下注 余额不足 userId=", userId, " balance=", userBalance.Amount, " reqdata=", reqdata)
			respdata.Code = Omg_Code_Fail_Not_Enough_Balance
			respdata.Msg = "not enough balance"
			ctx.RespJson(respdata)
			return e
		}

		switch reqdata.Type {
		case 1: // 下注
			order := thirdGameModel.ThirdOrder{}
			// 查询下注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("omg_single 改变玩家余额 取消下注 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail_Server_Internal_Error
				respdata.Msg = "查询注单失败"
				ctx.RespJson(respdata)
				return e
			}

			if e != nil {
				// 创建下注订单
				order = thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    reqdata.Bet,
					WinAmount:    0,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    -1, //未开奖
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("omg_single 改变玩家余额 下注 创建订单失败 order=", order, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("omg_single 改变玩家余额 下注 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}

				if reqdata.Money != 0 {
					resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
						"BetAmount": daogorm.Expr("BetAmount + ?", reqdata.Bet),
						"ThirdTime": thirdTime,
						"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
					})
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("omg_single 改变玩家余额 下注 更新订单失败 order=", order, " error=", e.Error())
						respdata.Code = Omg_Code_Fail
						respdata.Msg = "更新订单信息失败"
						ctx.RespJson(respdata)
						return e
					}
				}
			}
			// 下注扣费
			if reqdata.Money != 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ? and Amount>=?", userId, -reqdata.Money).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("omg_single 改变玩家余额 下注 更新用户余额失败 userId=", userId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail_Not_Enough_Balance
					respdata.Msg = "update user balance fail"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonOMGBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 下注 创建账变记录失败 amountLog=", amountLog, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "create amount change log fail"
				ctx.RespJson(respdata)
				return e
			}

			respdata.Data.Balance = userBalance.Amount + reqdata.Money
			logs.Info("omg_single 改变玩家余额 下注 userId=", userId, " 下注成功 reqdata=", reqdata, " respdata=", respdata)
			// 推送下注事件通知
			if l.thirdGamePush != nil {
				l.thirdGamePush.PushBetEvent(userId, order.GameName, l.brandName, math.Abs(reqdata.Money), l.currency)
			}
			ctx.RespJson(respdata)
			return nil

		case 2: // 取消下注
			order := thirdGameModel.ThirdOrder{}
			// 查询下注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 取消下注 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.Code = Omg_Code_Fail_Order_Not_Exist
					respdata.Msg = "order not exist"
				} else {
					respdata.Code = Omg_Code_Fail_Server_Internal_Error
					respdata.Msg = "查询注单失败"
				}
				ctx.RespJson(respdata)
				return e
			}
			if order.DataState != -1 {
				e = fmt.Errorf("订单状态不是未开奖")
				logs.Error("omg_single 改变玩家余额 取消下注 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "can not cancel settled order"
				ctx.RespJson(respdata)
				return e
			}
			if reqdata.Money > order.BetAmount { // 取消下注金额必须等于下注金额
				e = fmt.Errorf("取消下注金额大于下注金额")
				logs.Error("omg_single 改变玩家余额 取消下注 取消下注金额大于下注金额 reqdata.Money=", reqdata.Money, " order.BetAmount=", order.BetAmount)
				respdata.Code = Omg_Code_Fail_Illegal_Parameter
				respdata.Msg = "Cancel bet amount greater than bet amount"
				ctx.RespJson(respdata)
				return e
			}
			// 判断cancel_order_id是否一致 // 因为有双重下注，所以不能这样判断取消的注单ID了
			// tmpBetReqData := RequestData{}
			// json.Unmarshal([]byte(order.RawData), &tmpBetReqData)
			// if tmpBetReqData.OrderId != reqdata.CancelOrderId || tmpBetReqData.Uname != reqdata.Uname {
			// 	e = fmt.Errorf("取消下注单号和下注单号不相等")
			// 	logs.Error("omg_single 改变玩家余额 取消下注 请求订单号不匹配 thirdId=", thirdId, " error=", e.Error())
			// 	respdata.Code = Omg_Code_Fail
			// 	respdata.Msg = "ancel_order_id not eq bet_order_id"
			// 	ctx.RespJson(respdata)
			// 	return e
			// }

			// 取消下注
			resultTmp := tx.Table(tablePre).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).Updates(map[string]interface{}{
				// "DataState": -2,
				"BetAmount": daogorm.Expr("BetAmount - ?", reqdata.Money),
				"RawData":   string(bodyBytes),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("omg_single 改变玩家余额 取消下注 更改订单状态失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "cancel order fail"
				ctx.RespJson(respdata)
				return e
			}
			// 返还下注金额
			if reqdata.Money != 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("omg_single 改变玩家余额 取消下注 更新用户余额失败 userId=", userId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail_User_Not_Exist
					respdata.Msg = "user not exist"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonOMGCancel,
				Memo:         l.brandName + " cancel,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 取消下注 创建账变记录失败 amountLog=", amountLog, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "create amount change log fail"
				ctx.RespJson(respdata)
				return e
			}

			respdata.Data.Balance = userBalance.Amount + reqdata.Money
			logs.Info("omg_single 改变玩家余额 取消下注成功 userId=", userId, " 取消下注成功 reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		case 3: // 返奖
			order := thirdGameModel.ThirdOrder{}
			// 查询下注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 返奖 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail_Order_Not_Exist
				respdata.Msg = "order not exist"
				ctx.RespJson(respdata)
				return e
			}
			if order.DataState != -1 {
				e = fmt.Errorf("订单不是未开奖状态")
				logs.Error("omg_single 改变玩家余额 返奖 订单状态不正确 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "can not settle settled order"
				ctx.RespJson(respdata)
				return e
			}
			// 返奖
			resultTmp := tx.Table(tablePre).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).Updates(map[string]interface{}{
				"WinAmount": daogorm.Expr("WinAmount + ?", reqdata.Money),
				"RawData":   string(bodyBytes),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("omg_single 改变玩家余额 返奖 更改预设订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "settle order fail"
				ctx.RespJson(respdata)
				return e
			}

			// 派奖添加余额
			if reqdata.Money != 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("omg_single 改变玩家余额 返奖 更新用户余额失败 userId=", userId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail_User_Not_Exist
					respdata.Msg = "user not exist"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonOMGSettle,
				Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 返奖 创建账变记录失败 amountLog=", amountLog, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "create amount change log fail"
				ctx.RespJson(respdata)
				return e
			}

			respdata.Data.Balance = userBalance.Amount + reqdata.Money
			logs.Info("omg_single 改变玩家余额 返奖 userId=", userId, " 返奖成功 reqdata=", reqdata, " respdata=", respdata)
			// 推送奖励事件通知
			if l.thirdGamePush != nil {
				//l.thirdGamePush.PushRewardEvent(userId, order.GameName, l.brandName, order.BetAmount, reqdata.Money, l.currency)
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				l.thirdGamePush.PushRewardEvent(gameInfo.GameType, l.brandName, thirdId)
			}
			ctx.RespJson(respdata)
			return nil
		case 4: // 验证对局结束
			// 通知对局结束 注单如果没有收到派奖协议，就算用户用输，需要结算订单
			order := thirdGameModel.ThirdOrder{}
			// 查询下注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 结算订单 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail_Order_Not_Exist
				respdata.Msg = "order not exist"
				ctx.RespJson(respdata)
				return e
			}
			if order.DataState != -1 {
				respdata.Data.Balance = userBalance.Amount
				ctx.RespJson(respdata)
				return nil
			}
			// 结算订单
			// 所有电子的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(order.WinAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			resultTmp := tx.Table(tablePre).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).Updates(map[string]interface{}{
				"DataState": 1,
				"ValidBet":  validBet,
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("omg_single 改变玩家余额 结算订单 更改预设订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "settle order fail"
				ctx.RespJson(respdata)
				return e
			}

			order.Id = 0
			order.ValidBet = validBet
			order.DataState = 1
			order.ThirdTime = thirdTime

			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 结算订单 创建正式订单失败 betTrans=", order, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "create settle order fail"
				ctx.RespJson(respdata)
				return e
			}

			if order.WinAmount == 0 {
				// 创建账变记录
				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       0,
					AfterAmount:  userBalance.Amount,
					Reason:       utils.BalanceCReasonOMGSettle,
					Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("omg_single 改变玩家余额 结算订单 创建账变记录失败 amountLog=", amountLog, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "create amount change log fail"
					ctx.RespJson(respdata)
					return e
				}
			}

			respdata.Data.Balance = userBalance.Amount
			logs.Info("omg_single 改变玩家余额 结算订单 userId=", userId, " 结算成功 reqdata=", reqdata, " respdata=", respdata)
			// 推送奖励事件通知
			if l.thirdGamePush != nil && reqdata.Money > 0 {
				//l.thirdGamePush.PushRewardEvent(userId, order.GameName, l.brandName, order.BetAmount, reqdata.Money, l.currency)
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				l.thirdGamePush.PushRewardEvent(gameInfo.GameType, l.brandName, thirdId)
			}
			ctx.RespJson(respdata)
			return nil
		case 5: // luck49宝箱领取
			order := thirdGameModel.ThirdOrder{}
			// 查询下注订单
			e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId = ? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil && e != daogorm.ErrRecordNotFound {
				logs.Error("omg_single 改变玩家余额 取消下注 查询下注订单失败 thirdId=", thirdId, " error=", e.Error())
				respdata.Code = Omg_Code_Fail_Server_Internal_Error
				respdata.Msg = "查询注单失败"
				ctx.RespJson(respdata)
				return e
			}
			if e != nil {
				// 相当于赠送用户彩金 直接添加一个注单 添加用户余额就行
				// 创建送彩金订单
				order := thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    0,
					WinAmount:    reqdata.Money,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    1, // 已开奖
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 创建中间订单失败 order=", order, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 创建订单失败 order=", order, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "create order fail"
					ctx.RespJson(respdata)
					return e
				}
			} else {
				if order.DataState != -1 {
					e = fmt.Errorf("订单已结算")
					logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 订单已结算 thirdId=", thirdId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "订单已结算"
					ctx.RespJson(respdata)
					return e
				}

				if reqdata.Money != 0 {
					resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
						"WinAmount": daogorm.Expr("WinAmount + ?", reqdata.Money),
						"ThirdTime": thirdTime,
						"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
					})
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 更新订单失败 order=", order, " error=", e.Error())
						respdata.Code = Omg_Code_Fail
						respdata.Msg = "更新订单信息失败"
						ctx.RespJson(respdata)
						return e
					}
				}
			}
			// 添加用户余额
			if reqdata.Money != 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", reqdata.Money),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 更新用户余额失败 userId=", userId, " error=", e.Error())
					respdata.Code = Omg_Code_Fail
					respdata.Msg = "update user balance fail"
					ctx.RespJson(respdata)
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Money,
				AfterAmount:  userBalance.Amount + reqdata.Money,
				Reason:       utils.BalanceCReasonOMGSettle,
				Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("omg_single 改变玩家余额 luck49宝箱送彩金 创建账变记录失败 amountLog=", amountLog, " error=", e.Error())
				respdata.Code = Omg_Code_Fail
				respdata.Msg = "create amount change log fail"
				ctx.RespJson(respdata)
				return e
			}

			respdata.Data.Balance = userBalance.Amount + reqdata.Money
			logs.Info("omg_single 改变玩家余额 luck49宝箱送彩金成功 userId=", userId, " reqdata=", reqdata, " respdata=", respdata)
			ctx.RespJson(respdata)
			return nil
		default:
			logs.Error("omg_single 改变玩家余额 非法的type reqdata.Type=", reqdata.Type)
			respdata.Code = Omg_Code_Fail_Illegal_Parameter
			respdata.Msg = "illegal parameter"
			ctx.RespJson(respdata)
			return nil
		}
	}) // <--- 这里缺少了闭合括号

	if err != nil {
		logs.Error("omg_single 改变玩家余额 事务处理失败 thirdId=", reqdata.SessionId, " err=", err.Error())
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][OmgSingleService] ChangeBalance 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][OmgSingleService] ChangeBalance 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	return
}

// load_game_list 获取游戏列表 /api/game/loadlist
func (l *OmgSingleService) LoadGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Add string `json:"add"` // 1 添加数据库 非1不添加数据库
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	type RequestGameListData struct {
		Appid string `json:"appid"`
	}
	type GameData struct {
		GameId   string `json:"gameid"`   // 游戏id
		Name     string `json:"name"`     // 游戏名称
		Platform string `json:"platform"` // 游戏所属平台
		GameType int    `json:"gametype"` // 游戏类型
		Status   int    `json:"status"`   // 游戏状态，0：关闭,1：开启
	}
	type ResponseGameListData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			GameList []GameData `json:"glist"`
		} `json:"data"`
	}
	reqGameListData := RequestGameListData{
		Appid: l.appId,
	}
	respGameListData := ResponseGameListData{}

	gameListUrlParam := fmt.Sprintf("trace_id=%s", abugo.GetUuid())
	gameListBodyByte, _ := json.Marshal(reqGameListData)
	gameListSign := l.getSign(gameListUrlParam, string(gameListBodyByte))
	gameListUrl := fmt.Sprintf("%s/api/game/loadlist?%s", l.url, gameListUrlParam)

	client := &http.Client{}
	payload := bytes.NewReader(gameListBodyByte)
	logs.Info("omg_single 获取游戏列表 请求接口参数 userId=", reqdata)
	req, _ := http.NewRequest(http.MethodPost, gameListUrl, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	req.Header.Add("sign", gameListSign)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("omg_single 获取游戏列表请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("omg_single 获取游戏列表响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("omg_single 获取游戏列表请求成功 respBytes=", string(respBytes))

	err = json.Unmarshal(respBytes, &respGameListData)
	if err != nil {
		logs.Error("omg_single 获取游戏列表 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if respGameListData.Code != Omg_Code_Success {
		logs.Error("omg_single 获取游戏列表失败 错误码=", respGameListData.Code)
		errcode = respGameListData.Code
		ctx.RespErrString(true, &errcode, respGameListData.Msg)
		return
	}

	if reqdata.Add == "1" {
		for _, v := range respGameListData.Data.GameList {
			if v.Status != 1 {
				continue
			}
			// gametype 1=SLOTS 2=百人场 3=捕鱼 4=MiniGame
			betTran := GameListHub88{}
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", l.brandName, v.GameId).First(&betTran).Error
			if err != daogorm.ErrRecordNotFound {
				continue
			}

			gameType := 1
			if v.GameId == "9" || v.GameId == "100001" { // 扫雷 红飞机 放加密游戏
				gameType = 3
			}

			//写入数据库中
			gameInfo := xgo.H{
				"Brand":     l.brandName,
				"GameId":    v.GameId,
				"Name":      v.Name,
				"EName":     v.Name,
				"GameType":  gameType, // 1电子 2棋牌 3趣味 4彩票 5真人 6体育
				"HubType":   0,        // 0非聚合类型 1hub88类型
				"State":     2,
				"OpenState": 1,
			}
			server.Db().Table("x_game_list").Insert(gameInfo)
		}
	}

	ctx.RespOK(respGameListData)
	return
}
