// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipMonthly(db *gorm.DB, opts ...gen.DOOption) xVipMonthly {
	_xVipMonthly := xVipMonthly{}

	_xVipMonthly.xVipMonthlyDo.UseDB(db, opts...)
	_xVipMonthly.xVipMonthlyDo.UseModel(&model.XVipMonthly{})

	tableName := _xVipMonthly.xVipMonthlyDo.TableName()
	_xVipMonthly.ALL = field.NewAsterisk(tableName)
	_xVipMonthly.ID = field.NewInt32(tableName, "Id")
	_xVipMonthly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xVipMonthly.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipMonthly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipMonthly.UserID = field.NewInt32(tableName, "UserId")
	_xVipMonthly.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xVipMonthly.State = field.NewInt32(tableName, "State")
	_xVipMonthly.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xVipMonthly.GetTime = field.NewTime(tableName, "GetTime")

	_xVipMonthly.fillFieldMap()

	return _xVipMonthly
}

type xVipMonthly struct {
	xVipMonthlyDo xVipMonthlyDo

	ALL          field.Asterisk
	ID           field.Int32
	RecordDate   field.Time
	SellerID     field.Int32
	ChannelID    field.Int32
	UserID       field.Int32
	VipLevel     field.Int32   // vip等级
	State        field.Int32   // 状态 1 未领取 2已领取
	RewardAmount field.Float64 // 领取金额
	GetTime      field.Time    // 领取时间

	fieldMap map[string]field.Expr
}

func (x xVipMonthly) Table(newTableName string) *xVipMonthly {
	x.xVipMonthlyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipMonthly) As(alias string) *xVipMonthly {
	x.xVipMonthlyDo.DO = *(x.xVipMonthlyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipMonthly) updateTableName(table string) *xVipMonthly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.State = field.NewInt32(table, "State")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.GetTime = field.NewTime(table, "GetTime")

	x.fillFieldMap()

	return x
}

func (x *xVipMonthly) WithContext(ctx context.Context) *xVipMonthlyDo {
	return x.xVipMonthlyDo.WithContext(ctx)
}

func (x xVipMonthly) TableName() string { return x.xVipMonthlyDo.TableName() }

func (x xVipMonthly) Alias() string { return x.xVipMonthlyDo.Alias() }

func (x xVipMonthly) Columns(cols ...field.Expr) gen.Columns { return x.xVipMonthlyDo.Columns(cols...) }

func (x *xVipMonthly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipMonthly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["State"] = x.State
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["GetTime"] = x.GetTime
}

func (x xVipMonthly) clone(db *gorm.DB) xVipMonthly {
	x.xVipMonthlyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipMonthly) replaceDB(db *gorm.DB) xVipMonthly {
	x.xVipMonthlyDo.ReplaceDB(db)
	return x
}

type xVipMonthlyDo struct{ gen.DO }

func (x xVipMonthlyDo) Debug() *xVipMonthlyDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipMonthlyDo) WithContext(ctx context.Context) *xVipMonthlyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipMonthlyDo) ReadDB() *xVipMonthlyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipMonthlyDo) WriteDB() *xVipMonthlyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipMonthlyDo) Session(config *gorm.Session) *xVipMonthlyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipMonthlyDo) Clauses(conds ...clause.Expression) *xVipMonthlyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipMonthlyDo) Returning(value interface{}, columns ...string) *xVipMonthlyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipMonthlyDo) Not(conds ...gen.Condition) *xVipMonthlyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipMonthlyDo) Or(conds ...gen.Condition) *xVipMonthlyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipMonthlyDo) Select(conds ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipMonthlyDo) Where(conds ...gen.Condition) *xVipMonthlyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipMonthlyDo) Order(conds ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipMonthlyDo) Distinct(cols ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipMonthlyDo) Omit(cols ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipMonthlyDo) Join(table schema.Tabler, on ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipMonthlyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipMonthlyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipMonthlyDo) Group(cols ...field.Expr) *xVipMonthlyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipMonthlyDo) Having(conds ...gen.Condition) *xVipMonthlyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipMonthlyDo) Limit(limit int) *xVipMonthlyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipMonthlyDo) Offset(offset int) *xVipMonthlyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipMonthlyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipMonthlyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipMonthlyDo) Unscoped() *xVipMonthlyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipMonthlyDo) Create(values ...*model.XVipMonthly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipMonthlyDo) CreateInBatches(values []*model.XVipMonthly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipMonthlyDo) Save(values ...*model.XVipMonthly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipMonthlyDo) First() (*model.XVipMonthly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipMonthly), nil
	}
}

func (x xVipMonthlyDo) Take() (*model.XVipMonthly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipMonthly), nil
	}
}

func (x xVipMonthlyDo) Last() (*model.XVipMonthly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipMonthly), nil
	}
}

func (x xVipMonthlyDo) Find() ([]*model.XVipMonthly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipMonthly), err
}

func (x xVipMonthlyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipMonthly, err error) {
	buf := make([]*model.XVipMonthly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipMonthlyDo) FindInBatches(result *[]*model.XVipMonthly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipMonthlyDo) Attrs(attrs ...field.AssignExpr) *xVipMonthlyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipMonthlyDo) Assign(attrs ...field.AssignExpr) *xVipMonthlyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipMonthlyDo) Joins(fields ...field.RelationField) *xVipMonthlyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipMonthlyDo) Preload(fields ...field.RelationField) *xVipMonthlyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipMonthlyDo) FirstOrInit() (*model.XVipMonthly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipMonthly), nil
	}
}

func (x xVipMonthlyDo) FirstOrCreate() (*model.XVipMonthly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipMonthly), nil
	}
}

func (x xVipMonthlyDo) FindByPage(offset int, limit int) (result []*model.XVipMonthly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipMonthlyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipMonthlyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipMonthlyDo) Delete(models ...*model.XVipMonthly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipMonthlyDo) withDO(do gen.Dao) *xVipMonthlyDo {
	x.DO = *do.(*gen.DO)
	return x
}
