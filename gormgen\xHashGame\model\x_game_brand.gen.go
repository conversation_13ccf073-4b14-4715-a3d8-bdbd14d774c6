// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXGameBrand = "x_game_brand"

// XGameBrand 游戏厂商
type XGameBrand struct {
	GameType       int32     `gorm:"column:GameType;primaryKey;comment:游戏大类" json:"GameType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // 游戏大类
	Brand          string    `gorm:"column:Brand;primaryKey;comment:厂商唯一标识" json:"Brand"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 厂商唯一标识
	BrandName      string    `gorm:"column:BrandName;not null;comment:厂商名" json:"BrandName"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 厂商名
	ClientGameType int32     `gorm:"column:ClientGameType;comment:客户端定义的GameType" json:"ClientGameType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 客户端定义的GameType
	GameSortExID   string    `gorm:"column:GameSortExId;comment:GameSortEx(游戏小类排序)中的Id" json:"GameSortExId"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // GameSortEx(游戏小类排序)中的Id
	BrandType      int32     `gorm:"column:BrandType;comment:游戏厂商分类 1自营 2三方" json:"BrandType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      // 游戏厂商分类 1自营 2三方
	Status         int32     `gorm:"column:Status;not null;default:1;comment:状态 1有效 2无效" json:"Status"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             // 状态 1有效 2无效
	CountryList    string    `gorm:"column:CountryList;not null;default:AE,AF,AZ,BD,BH,BN,BT,CN,CY,HK,ID,IL,IN,IO,IQ,IR,JO,JP,KG,KH,KP,KR,KW,KZ,LA,LB,LK,MM,MN,MO,MV,MY,NP,OM,PH,PK,PS,QA,RU,SA,SY,TH,TJ,TL,TM,TW,UZ,VN,YE,AD,AL,AM,AT,AX,BA,BE,BG,BY,CH,CZ,DE,DK,EE,FI,FO,FR,GB,GE,GG,GI,GR,HR,HU,IE,IM,IS,IT,JE,LI,LT,LU,LV,MC,MD,ME,MK,MT,NL,NO,PL,PT,RO,RS,SE,SI,SJ,SK,SM,TR,UA,VA,AG,AI,AW,BB,BL,BM,BQ,BS,BZ,CA,CR,CU,CW,DM,DO,GD,GL,GP,GT,HN,HT,JM,KN,KY,LC,MF,MQ,MS,MX,NI,PA,PM,PR,SV,SX,TC,TT,US,VC,VG,VI,AR,BO,BR,CL,CO,EC,FK,GF,GS,GY,PE,PY,SR,UY,VE,AO,BF,BI,BJ,BW,CD,CF,CG,CI,CM,CV,DJ,DZ,EG,EH,ER,ET,GA,GH,GM,GN,GQ,GW,KE,KM,LR,LS,LY,MA,MG,ML,MR,MU,MW,MZ,NA,NE,NG,RE,RW,SC,SD,SH,SL,SN,SO,SS,ST,SZ,TD,TG,TN,TZ,UG,YT,ZA,ZM,ZW,AQ,BV,TF,AS,AU,CC,CK,CX,FJ,FM,GU,HM,KI,MH,MP,NC,NF,NR,NU,NZ,PF,PG,PN,PW,SB,TK,TO,TV,UM,VU,WF,WS,SG;comment:支持的地区（二位字母国家代码，英文逗号分隔）" json:"CountryList"` // 支持的地区（二位字母国家代码，英文逗号分隔）
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 更新时间
	SpecialGames   string    `gorm:"column:SpecialGames;comment:特殊跳转到omg的游戏" json:"SpecialGames"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // 特殊跳转到omg的游戏
	DisplayGames   string    `gorm:"column:DisplayGames;comment:如果区域限制了，必须开启的游戏" json:"DisplayGames"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               // 如果区域限制了，必须开启的游戏
}

// TableName XGameBrand's table name
func (*XGameBrand) TableName() string {
	return TableNameXGameBrand
}
