// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentIndependentCommissionDetail = "x_agent_independent_commission_detail"

// XAgentIndependentCommissionDetail mapped from table <x_agent_independent_commission_detail>
type XAgentIndependentCommissionDetail struct {
	ID             int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	GameType       string    `gorm:"column:GameType;not null;comment:haxiusdt,live,dianzi" json:"GameType"`      // haxiusdt,live,dianzi
	Ordertable     string    `gorm:"column:Ordertable;not null;comment:x_order,x_third_live" json:"Ordertable"`  // x_order,x_third_live
	OrderID        int64     `gorm:"column:OrderId;not null;comment:x_order.id, x_third_live.id" json:"OrderId"` // x_order.id, x_third_live.id
	BetUserID      int32     `gorm:"column:BetUserId;comment:投注的玩家 orderUserid" json:"BetUserId"`                // 投注的玩家 orderUserid
	UserID         int32     `gorm:"column:UserId;not null" json:"UserId"`
	AgentID        int32     `gorm:"column:AgentId;not null" json:"AgentId"`
	FromAddress    string    `gorm:"column:FromAddress;comment:玩家地址" json:"FromAddress"`                      // 玩家地址
	BetAmount      float64   `gorm:"column:BetAmount;not null;comment:投注" json:"BetAmount"`                   // 投注
	Liushui        float64   `gorm:"column:Liushui;not null;comment:流水" json:"Liushui"`                       // 流水
	UserRate       float32   `gorm:"column:UserRate;not null;comment:用户返佣比例" json:"UserRate"`                 // 用户返佣比例
	AgentRate      float32   `gorm:"column:AgentRate;not null;comment:上级的返佣比例" json:"AgentRate"`              // 上级的返佣比例
	Rate           float32   `gorm:"column:Rate;not null;comment:计算用的返佣比例" json:"Rate"`                       // 计算用的返佣比例
	Commission     float64   `gorm:"column:Commission;not null;comment:返佣" json:"Commission"`                 // 返佣
	FenCheng       string    `gorm:"column:FenCheng;comment:分成" json:"FenCheng"`                              // 分成
	AgentFenCheng  string    `gorm:"column:AgentFenCheng;comment:上级分成" json:"AgentFenCheng"`                  // 上级分成
	CommissionType int32     `gorm:"column:CommissionType;comment:0 不限 1 自身 2 直属 3 团队" json:"CommissionType"` // 0 不限 1 自身 2 直属 3 团队
	Symbol         string    `gorm:"column:Symbol" json:"Symbol"`
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:算佣金时间" json:"CreateTime"` // 算佣金时间
	SysTime        time.Time `gorm:"column:SysTime;not null;default:CURRENT_TIMESTAMP;comment:订单时间" json:"SysTime"`        // 订单时间
}

// TableName XAgentIndependentCommissionDetail's table name
func (*XAgentIndependentCommissionDetail) TableName() string {
	return TableNameXAgentIndependentCommissionDetail
}
