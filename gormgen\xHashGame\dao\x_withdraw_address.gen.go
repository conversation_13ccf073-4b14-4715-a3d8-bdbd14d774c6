// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXWithdrawAddress(db *gorm.DB, opts ...gen.DOOption) xWithdrawAddress {
	_xWithdrawAddress := xWithdrawAddress{}

	_xWithdrawAddress.xWithdrawAddressDo.UseDB(db, opts...)
	_xWithdrawAddress.xWithdrawAddressDo.UseModel(&model.XWithdrawAddress{})

	tableName := _xWithdrawAddress.xWithdrawAddressDo.TableName()
	_xWithdrawAddress.ALL = field.NewAsterisk(tableName)
	_xWithdrawAddress.UserID = field.NewInt32(tableName, "UserId")
	_xWithdrawAddress.Net = field.NewString(tableName, "Net")
	_xWithdrawAddress.Address = field.NewString(tableName, "Address")
	_xWithdrawAddress.State = field.NewInt32(tableName, "State")
	_xWithdrawAddress.Memo = field.NewString(tableName, "Memo")
	_xWithdrawAddress.CreateTime = field.NewTime(tableName, "CreateTime")
	_xWithdrawAddress.Symbol = field.NewString(tableName, "Symbol")

	_xWithdrawAddress.fillFieldMap()

	return _xWithdrawAddress
}

// xWithdrawAddress 用户区块链提现地址
type xWithdrawAddress struct {
	xWithdrawAddressDo xWithdrawAddressDo

	ALL        field.Asterisk
	UserID     field.Int32  // 用户Id
	Net        field.String // 区块链网络
	Address    field.String // 地址
	State      field.Int32  // 状态 1已验证 2未验证
	Memo       field.String // 备注
	CreateTime field.Time   // 创建时间
	Symbol     field.String // 币种

	fieldMap map[string]field.Expr
}

func (x xWithdrawAddress) Table(newTableName string) *xWithdrawAddress {
	x.xWithdrawAddressDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xWithdrawAddress) As(alias string) *xWithdrawAddress {
	x.xWithdrawAddressDo.DO = *(x.xWithdrawAddressDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xWithdrawAddress) updateTableName(table string) *xWithdrawAddress {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.Net = field.NewString(table, "Net")
	x.Address = field.NewString(table, "Address")
	x.State = field.NewInt32(table, "State")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Symbol = field.NewString(table, "Symbol")

	x.fillFieldMap()

	return x
}

func (x *xWithdrawAddress) WithContext(ctx context.Context) *xWithdrawAddressDo {
	return x.xWithdrawAddressDo.WithContext(ctx)
}

func (x xWithdrawAddress) TableName() string { return x.xWithdrawAddressDo.TableName() }

func (x xWithdrawAddress) Alias() string { return x.xWithdrawAddressDo.Alias() }

func (x xWithdrawAddress) Columns(cols ...field.Expr) gen.Columns {
	return x.xWithdrawAddressDo.Columns(cols...)
}

func (x *xWithdrawAddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xWithdrawAddress) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Net"] = x.Net
	x.fieldMap["Address"] = x.Address
	x.fieldMap["State"] = x.State
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Symbol"] = x.Symbol
}

func (x xWithdrawAddress) clone(db *gorm.DB) xWithdrawAddress {
	x.xWithdrawAddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xWithdrawAddress) replaceDB(db *gorm.DB) xWithdrawAddress {
	x.xWithdrawAddressDo.ReplaceDB(db)
	return x
}

type xWithdrawAddressDo struct{ gen.DO }

func (x xWithdrawAddressDo) Debug() *xWithdrawAddressDo {
	return x.withDO(x.DO.Debug())
}

func (x xWithdrawAddressDo) WithContext(ctx context.Context) *xWithdrawAddressDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xWithdrawAddressDo) ReadDB() *xWithdrawAddressDo {
	return x.Clauses(dbresolver.Read)
}

func (x xWithdrawAddressDo) WriteDB() *xWithdrawAddressDo {
	return x.Clauses(dbresolver.Write)
}

func (x xWithdrawAddressDo) Session(config *gorm.Session) *xWithdrawAddressDo {
	return x.withDO(x.DO.Session(config))
}

func (x xWithdrawAddressDo) Clauses(conds ...clause.Expression) *xWithdrawAddressDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xWithdrawAddressDo) Returning(value interface{}, columns ...string) *xWithdrawAddressDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xWithdrawAddressDo) Not(conds ...gen.Condition) *xWithdrawAddressDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xWithdrawAddressDo) Or(conds ...gen.Condition) *xWithdrawAddressDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xWithdrawAddressDo) Select(conds ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xWithdrawAddressDo) Where(conds ...gen.Condition) *xWithdrawAddressDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xWithdrawAddressDo) Order(conds ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xWithdrawAddressDo) Distinct(cols ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xWithdrawAddressDo) Omit(cols ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xWithdrawAddressDo) Join(table schema.Tabler, on ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xWithdrawAddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xWithdrawAddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xWithdrawAddressDo) Group(cols ...field.Expr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xWithdrawAddressDo) Having(conds ...gen.Condition) *xWithdrawAddressDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xWithdrawAddressDo) Limit(limit int) *xWithdrawAddressDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xWithdrawAddressDo) Offset(offset int) *xWithdrawAddressDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xWithdrawAddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xWithdrawAddressDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xWithdrawAddressDo) Unscoped() *xWithdrawAddressDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xWithdrawAddressDo) Create(values ...*model.XWithdrawAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xWithdrawAddressDo) CreateInBatches(values []*model.XWithdrawAddress, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xWithdrawAddressDo) Save(values ...*model.XWithdrawAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xWithdrawAddressDo) First() (*model.XWithdrawAddress, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawAddress), nil
	}
}

func (x xWithdrawAddressDo) Take() (*model.XWithdrawAddress, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawAddress), nil
	}
}

func (x xWithdrawAddressDo) Last() (*model.XWithdrawAddress, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawAddress), nil
	}
}

func (x xWithdrawAddressDo) Find() ([]*model.XWithdrawAddress, error) {
	result, err := x.DO.Find()
	return result.([]*model.XWithdrawAddress), err
}

func (x xWithdrawAddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XWithdrawAddress, err error) {
	buf := make([]*model.XWithdrawAddress, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xWithdrawAddressDo) FindInBatches(result *[]*model.XWithdrawAddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xWithdrawAddressDo) Attrs(attrs ...field.AssignExpr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xWithdrawAddressDo) Assign(attrs ...field.AssignExpr) *xWithdrawAddressDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xWithdrawAddressDo) Joins(fields ...field.RelationField) *xWithdrawAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xWithdrawAddressDo) Preload(fields ...field.RelationField) *xWithdrawAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xWithdrawAddressDo) FirstOrInit() (*model.XWithdrawAddress, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawAddress), nil
	}
}

func (x xWithdrawAddressDo) FirstOrCreate() (*model.XWithdrawAddress, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawAddress), nil
	}
}

func (x xWithdrawAddressDo) FindByPage(offset int, limit int) (result []*model.XWithdrawAddress, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xWithdrawAddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xWithdrawAddressDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xWithdrawAddressDo) Delete(models ...*model.XWithdrawAddress) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xWithdrawAddressDo) withDO(do gen.Dao) *xWithdrawAddressDo {
	x.DO = *do.(*gen.DO)
	return x
}
