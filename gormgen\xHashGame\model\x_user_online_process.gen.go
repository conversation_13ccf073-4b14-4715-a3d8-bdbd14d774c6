// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserOnlineProcess = "x_user_online_process"

// XUserOnlineProcess mapped from table <x_user_online_process>
type XUserOnlineProcess struct {
	ID         int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID     int32     `gorm:"column:UserId;not null" json:"UserId"`
	Time       time.Time `gorm:"column:Time;not null" json:"Time"`
	State      int32     `gorm:"column:State;not null;default:1" json:"State"`
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XUserOnlineProcess's table name
func (*XUserOnlineProcess) TableName() string {
	return TableNameXUserOnlineProcess
}
