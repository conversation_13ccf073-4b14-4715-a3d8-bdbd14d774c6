package single

import (
	"bytes"
	"crypto/ed25519"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/golang-jwt/jwt/v4"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// 用户相关错误代码 (2000-2999)
const (
	PaErrUserNotFound            = 2001
	PaErrUserDisabled            = 2002
	PaErrCreateUserConflict      = 2003
	PaErrInsufficientBalance     = 2004
	PaErrDuplicateTradeOrderID   = 2005
	PaErrTradePointsVerification = 2006
	PaErrTradePointsZero         = 2007
)

// PaAPIError API错误响应结构
type PaAPIError struct {
	Code   int    `json:"code"`   // 错误代码
	Msg    string `json:"msg"`    // 错误信息(英文)
	MsgZh  string `json:"msg_zh"` // 错误信息(中文)
	Detail string `json:"detail"` // 详细错误信息
}

// Error 实现error接口
func (e *PaAPIError) Error() string {
	return fmt.Sprintf("API Error %d: %s (%s) - %s", e.Code, e.Msg, e.MsgZh, e.Detail)
}

// IsErrUserNotFound 检查是否为用户不存在错误
func IsErrUserNotFound(err error) bool {
	if apiErr, ok := err.(*PaAPIError); ok {
		return apiErr.Code == PaErrUserNotFound
	}
	return false
}

// IsErrInsufficientBalance 检查是否为余额不足错误
func IsErrInsufficientBalance(err error) bool {
	if apiErr, ok := err.(*PaAPIError); ok {
		return apiErr.Code == PaErrInsufficientBalance
	}
	return false
}

// IsErrDuplicateTradeOrderID 检查是否为转点单号重复错误
func IsErrDuplicateTradeOrderID(err error) bool {
	if apiErr, ok := err.(*PaAPIError); ok {
		return apiErr.Code == PaErrDuplicateTradeOrderID
	}
	return false
}

// ParseEd25519PrivateKey 解析Ed25519私钥
func ParseEd25519PrivateKey(pemEncodedKey []byte) (ed25519.PrivateKey, error) {
	block, _ := pem.Decode(pemEncodedKey)
	if block == nil || block.Type != "PRIVATE KEY" {
		return nil, errors.New("failed to decode PEM block containing private key")
	}

	privKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	edKey, ok := privKey.(ed25519.PrivateKey)
	if !ok {
		return nil, errors.New("private key is not an Ed25519 private key")
	}

	return edKey, nil
}

// GenerateBodyHash 生成请求体哈希
func GenerateBodyHash(body []byte) string {
	hash := sha256.Sum256(body)
	return hex.EncodeToString(hash[:])
}

// GenerateJWT 生成JWT令牌
func GenerateJWT(privateKey ed25519.PrivateKey, kid string, path string, method string, bodyHash string) (string, error) {
	// Create claims
	claims := jwt.MapClaims{
		"iat":      time.Now().Unix(),
		"aud":      "public-api-v2",
		"jti":      time.Now().Unix(),
		"path":     path,
		"method":   method,
		"bodyHash": bodyHash,
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodEdDSA, claims)

	// Set header parameters
	token.Header["alg"] = "EdDSA"
	token.Header["kid"] = kid

	// Sign the token
	tokenString, err := token.SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("error signing token: %v", err)
	}

	return tokenString, nil
}

// SendAPIRequest 发送API请求
func SendAPIRequest(method, url string, jwtToken string, requestBody []byte) (*http.Response, error) {
	var req *http.Request
	var err error

	if method == "GET" {
		req, err = http.NewRequest(method, url, nil)
	} else {
		req, err = http.NewRequest(method, url, bytes.NewBuffer(requestBody))
	}

	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	// Set the request headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", jwtToken))
	if method != "GET" {
		req.Header.Set("Content-Type", "application/json")
	}

	// Send the request
	client := &http.Client{}
	return client.Do(req)
}

func (s *PachinkoService) makeAPIRequest(method string, path string, requestBody []byte) (*http.Response, error) {
	// Generate body hash (empty for GET requests)
	bodyHash := ""
	if len(requestBody) > 0 {
		bodyHash = GenerateBodyHash(requestBody)
	}
	key := fmt.Sprintf("-----BEGIN PRIVATE KEY-----\n%s\n-----END PRIVATE KEY-----",
		strings.TrimSpace(s.secretIv))
	// 解析私钥
	privateKey_, err := ParseEd25519PrivateKey([]byte(key))

	// Generate JWT token with correct path
	jwtToken, err := GenerateJWT(privateKey_, s.appIdKey, path, method, bodyHash)
	if err != nil {
		return nil, fmt.Errorf("error generating JWT: %v", err)
	}

	// Construct full URL
	baseURL := s.apiUrl
	fullURL := baseURL + path

	// Log request details
	if len(requestBody) > 0 {
		logs.Info("Request: %s %s\nBody: %s", method, fullURL, string(requestBody))
	} else {
		logs.Info("Request: %s %s", method, fullURL)
	}

	// Send request
	resp, err := SendAPIRequest(method, fullURL, jwtToken, requestBody)
	if err != nil {
		return nil, err
	}

	// Read and log response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}
	logs.Info("Response: %s", string(body))

	// Create new response with the same body
	newResp := *resp
	newResp.Body = ioutil.NopCloser(bytes.NewBuffer(body))
	return &newResp, nil
}

func (s *PachinkoService) TradePoints(request PaTradePointsRequest) (*PaTradePointsResponse, error) {
	// Convert request to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("POST", "/api/v2/users/trade_points", requestBody)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaTradePointsResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) CreateUser(request PaCreateUserRequest) error {
	// Convert request to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("error marshaling request: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("POST", "/api/v2/users", requestBody)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return fmt.Errorf("error parsing error response: %v", err)
		}
		return &errorResp
	}

	return nil
}

func (s *PachinkoService) QueryGameTypes(params PaQueryParams) (*PaQueryResponse, error) {
	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/gametypes", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaQueryResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryGameLogs(params PaQueryGameLogsParams) (*PaGameLogResponse, error) {
	// Convert params to JSON
	requestBody, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/game_logs", requestBody)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaGameLogResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryGameRounds(params PaQueryGameRoundsParams) (*PaGameRoundResponse, error) {
	// Convert params to JSON
	requestBody, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/game_rounds", requestBody)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaGameRoundResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryUsers(params PaQueryUsersParams) (*PaUserResponse, error) {
	// Build query string
	values := url.Values{}
	if params.UID != "" {
		values.Add("uid", params.UID)
	}
	if params.WalletGte != 0 {
		values.Add("wallet_gte", fmt.Sprintf("%f", params.WalletGte))
	}
	if params.WalletLte != 0 {
		values.Add("wallet_lte", fmt.Sprintf("%f", params.WalletLte))
	}
	if params.IsDisabled != nil {
		values.Add("is_disabled", fmt.Sprintf("%v", *params.IsDisabled))
	}
	if params.SortBy != "" {
		values.Add("sort_by", params.SortBy)
	}
	if params.SortDir != "" {
		values.Add("sort_dir", params.SortDir)
	}
	if params.Size != 0 {
		values.Add("size", fmt.Sprintf("%d", params.Size))
	}
	if params.Page != 0 {
		values.Add("page", fmt.Sprintf("%d", params.Page))
	}
	if params.Count {
		values.Add("count", "true")
	}

	// Add query string to path
	path := "/api/v2/users"
	if len(values) > 0 {
		path += "?" + values.Encode()
	}

	// Make API request
	resp, err := s.makeAPIRequest("GET", path, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaUserResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) AcceptRollback(request PaAcceptRollbackRequest) (*PaAcceptRollbackResponse, error) {
	// Convert request to JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("POST", "/api/v2/single_wallet/accept_rollback", requestBody)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaAcceptRollbackResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryModels(params PaQueryParams) (*PaQueryResponse, error) {
	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/models", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaQueryResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryCategories(params PaQueryParams) (*PaQueryResponse, error) {
	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/categories", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaQueryResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) QueryMachines(params PaQueryMachinesParams) (*PaMachineResponse, error) {
	// Convert params to JSON
	requestBody, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %v", err)
	}

	// Make API request
	resp, err := s.makeAPIRequest("GET", "/api/v2/arcade/machines", requestBody)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errorResp PaAPIError
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %v", err)
		}
		return nil, &errorResp
	}

	// Parse response
	var response PaMachineResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	return &response, nil
}

func (s *PachinkoService) GenerateAccessURL(uid string, returnURL string, urlExp int) (string, error) {
	// 创建请求体
	requestBody, err := json.Marshal(map[string]interface{}{
		"uid":        uid,
		"return_url": returnURL,
		"url_exp":    urlExp,
	})
	if err != nil {
		return "", err
	}

	// 发起API请求
	resp, err := s.makeAPIRequest("POST", "/api/v2/users/generate_access_url", requestBody)
	if err != nil {
		return "", err
	}

	// 解析响应
	var result struct {
		AccessURL string `json:"access_url"`
	}
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", err
	}

	return result.AccessURL, nil
}

func (s *PachinkoService) GenerateGameAccessURL(uid string, returnURL string, urlExp int) (string, error) {
	// 检查用户是否存在
	logs.Info("检查用户是否存在: %s", uid)
	userResp, err := s.QueryUsers(PaQueryUsersParams{
		UID: uid,
	})
	if err != nil {
		if IsErrUserNotFound(err) {
			logs.Info("用户不存在，创建用户: %s", uid)
			// 创建用户
			err = s.CreateUser(PaCreateUserRequest{
				UID:      uid,
				Referrer: returnURL,
				Email:    fmt.Sprintf("%<EMAIL>", uid),
				Comment:  "",
			})
			if err != nil {
				return "", fmt.Errorf("创建用户失败: %v", err)
			}
			// 生成访问网址
			logs.Info("用户创建成功，生成访问网址: %s", uid)
			accessURL, err := s.GenerateAccessURL(uid, returnURL, urlExp)
			if err != nil {
				return "", fmt.Errorf("生成访问网址失败: %v", err)
			}
			logs.Info("生成的访问网址: %s", accessURL)
			return accessURL, nil
		} else {
			return "", fmt.Errorf("查询用户失败: %v", err)
		}
	}

	// 检查用户是否存在
	if len(userResp.Items) == 0 {
		logs.Info("用户不存在，尝试创建用户: %s", uid)
		// 调用 CreateUser 方法创建用户
		createErr := s.CreateUser(PaCreateUserRequest{
			UID:      uid,
			Referrer: returnURL,
			Email:    fmt.Sprintf("%<EMAIL>", uid),
			Comment:  "",
		})
		if createErr != nil {
			logs.Info("创建用户失败: %v", createErr)
			return "", fmt.Errorf("创建用户失败: %v", createErr)
		}

		// 再次查询用户以确认创建成功
		userResp, err = s.QueryUsers(PaQueryUsersParams{
			UID: uid,
		})
		if err != nil || len(userResp.Items) == 0 {
			logs.Info("用户创建后查询失败或用户仍不存在: %v", err)
			return "", fmt.Errorf("用户创建后查询失败或用户仍不存在: %v", err)
		}
	}

	logs.Info("查询成功")
	for _, user := range userResp.Items {
		logs.Info("用户ID: %s, 钱包余额: %.2f, 是否禁用: %v, 创建时间: %s", user.UID, user.Wallet, user.IsDisabled, user.CreatedAt)
	}

	// 用户存在，生成访问网址
	logs.Info("用户存在，生成访问网址: %s", uid)
	accessURL, err := s.GenerateAccessURL(uid, returnURL, urlExp)
	if err != nil {
		logs.Info("生成访问网址失败: %v", err)
		return "", err
	}
	return accessURL, nil
}

// PaUserResponse 用户查询响应结构
type PaUserResponse struct {
	HasNext bool     `json:"hasNext"` // 是否有下一页
	Total   int      `json:"total"`   // 总记录数
	Items   []PaUser `json:"items"`   // 用户列表
}

// PaUser 用户信息结构
type PaUser struct {
	UID        string    `json:"uid"`         // 用户ID
	Wallet     float64   `json:"wallet"`      // 钱包余额
	IsDisabled bool      `json:"is_disabled"` // 是否禁用
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
}

// PaQueryUsersParams 查询用户的参数结构
type PaQueryUsersParams struct {
	UID        string  `json:"uid,omitempty"`         // 用户ID
	WalletGte  float64 `json:"wallet_gte,omitempty"`  // 钱包余额大于等于
	WalletLte  float64 `json:"wallet_lte,omitempty"`  // 钱包余额小于等于
	IsDisabled *bool   `json:"is_disabled,omitempty"` // 是否禁用
	SortBy     string  `json:"sort_by,omitempty"`     // 排序字段
	SortDir    string  `json:"sort_dir,omitempty"`    // 排序方向
	Size       int     `json:"size,omitempty"`        // 每页数量
	Page       int     `json:"page,omitempty"`        // 页码
	Count      bool    `json:"count,omitempty"`       // 是否返回总数
}

// PaCreateUserRequest 创建用户的请求结构
type PaCreateUserRequest struct {
	UID      string `json:"uid"`      // 用户ID
	Referrer string `json:"referrer"` // 推荐人ID
	Email    string `json:"email"`    // 电子邮箱
	Comment  string `json:"comment"`  // 备注
}

// PaTradePointsRequest 转点请求结构
type PaTradePointsRequest struct {
	UID     string  `json:"uid"`      // 用户ID
	OrderID string  `json:"order_id"` // 订单ID
	Points  float64 `json:"points"`   // 转点数量
	Comment string  `json:"comment"`  // 备注
}

// PaTradePointsResponse 转点响应结构
type PaTradePointsResponse struct {
	OrderID string  `json:"order_id"` // 订单ID
	UID     string  `json:"uid"`      // 用户ID
	Balance float64 `json:"balance"`  // 转点后余额
}

// PaGameLog 游戏日志结构
type PaGameLog struct {
	ID                int    `json:"id"`                  // 日志ID
	RoundSerialNumber string `json:"round_serial_number"` // 游戏轮次序列号
	UID               string `json:"uid"`                 // 用户ID
	Machine           struct {
		ID         int    `json:"id"`          // 机台ID
		GameTypeID int    `json:"gametype_id"` // 游戏类型ID
		Name       string `json:"name"`        // 机台名称
	} `json:"machine"` // 机台信息
	GameType struct {
		ID   int    `json:"id"`   // 游戏类型ID
		Name string `json:"name"` // 游戏类型名称
	} `json:"gametype"` // 游戏类型信息
	StartedAt  time.Time `json:"started_at"`  // 开始时间
	EndedAt    time.Time `json:"ended_at"`    // 结束时间
	Bet        float64   `json:"bet"`         // 下注金额
	Win        float64   `json:"win"`         // 赢得金额
	Balance    float64   `json:"balance"`     // 余额
	GameState  string    `json:"game_state"`  // 游戏状态
	GameResult string    `json:"game_result"` // 游戏结果
}

// PaGameLogResponse 游戏日志查询响应结构
type PaGameLogResponse struct {
	HasNext bool        `json:"hasNext"` // 是否有下一页
	Total   int         `json:"total"`   // 总记录数
	Items   []PaGameLog `json:"items"`   // 游戏日志列表
}

// PaQueryGameLogsParams 查询游戏日志的参数结构
type PaQueryGameLogsParams struct {
	ID                int       `json:"id,omitempty"`                  // 日志ID
	RoundSerialNumber string    `json:"round_serial_number,omitempty"` // 游戏轮次序列号
	UID               string    `json:"uid,omitempty"`                 // 用户ID
	MachineID         int       `json:"machine_id,omitempty"`          // 机台ID
	GameTypeID        int       `json:"gametype_id,omitempty"`         // 游戏类型ID
	StartedAtStart    time.Time `json:"started_at_start,omitempty"`    // 开始时间起始
	StartedAtEnd      time.Time `json:"started_at_end,omitempty"`      // 开始时间结束
	EndedAtStart      time.Time `json:"ended_at_start,omitempty"`      // 结束时间起始
	EndedAtEnd        time.Time `json:"ended_at_end,omitempty"`        // 结束时间结束
	SortBy            string    `json:"sort_by,omitempty"`             // 排序字段
	SortDir           string    `json:"sort_dir,omitempty"`            // 排序方向
	Size              int       `json:"size,omitempty"`                // 每页记录数
	Page              int       `json:"page,omitempty"`                // 页码
	Count             bool      `json:"count,omitempty"`               // 是否返回总记录数
}

// PaMachine 游戏机台结构
type PaMachine struct {
	ID          int     `json:"id"`          // 机台ID
	GameTypeID  int     `json:"gametype_id"` // 游戏类型ID
	CategoryID  int     `json:"category_id"` // 机台类别ID
	ModelID     int     `json:"model_id"`    // 机台型号ID
	Title       string  `json:"title"`       // 机台标题
	TitleJP     string  `json:"title_jp"`    // 机台标题（日文）
	Description string  `json:"description"` // 机台描述
	OddsX       float64 `json:"odds_x"`      // 赔率X
	OddsY       float64 `json:"odds_y"`      // 赔率Y
	PlayerID    int     `json:"player_id"`   // 玩家ID
	IsPublic    bool    `json:"is_public"`   // 是否公开
}

// PaMachineResponse 游戏机台查询响应结构
type PaMachineResponse struct {
	HasNext bool        `json:"hasNext"` // 是否有下一页
	Total   int         `json:"total"`   // 总记录数
	Items   []PaMachine `json:"items"`   // 机台列表
}

// PaQueryMachinesParams 查询游戏机台的参数结构
type PaQueryMachinesParams struct {
	ID         int    `json:"id,omitempty"`          // 机台ID
	GameTypeID int    `json:"gametype_id,omitempty"` // 游戏类型ID
	CategoryID int    `json:"category_id,omitempty"` // 机台类别ID
	ModelID    int    `json:"model_id,omitempty"`    // 机台型号ID
	SortBy     string `json:"sort_by,omitempty"`     // 排序字段
	SortDir    string `json:"sort_dir,omitempty"`    // 排序方向
	Size       int    `json:"size,omitempty"`        // 每页记录数
	Page       int    `json:"page,omitempty"`        // 页码
	Count      bool   `json:"count,omitempty"`       // 是否返回总记录数
}

// PaGameRound 游戏轮次结构
type PaGameRound struct {
	ID           int       `json:"id"`            // 轮次ID
	SerialNumber string    `json:"serial_number"` // 轮次序列号
	MachineID    int       `json:"machine_id"`    // 机台ID
	GameTypeID   int       `json:"gametype_id"`   // 游戏类型ID
	Status       string    `json:"status"`        // 轮次状态
	StartAt      time.Time `json:"start_at"`      // 开始时间
	EndAt        time.Time `json:"end_at"`        // 结束时间
	GameResult   string    `json:"game_result"`   // 游戏结果
	GameHash     string    `json:"game_hash"`     // 游戏哈希
	GameSeed     string    `json:"game_seed"`     // 游戏种子
	GameNonce    string    `json:"game_nonce"`    // 游戏随机数
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
}

// PaGameRoundResponse 游戏轮次查询响应结构
type PaGameRoundResponse struct {
	HasNext bool          `json:"hasNext"` // 是否有下一页
	Total   int           `json:"total"`   // 总记录数
	Items   []PaGameRound `json:"items"`   // 轮次列表
}

// PaQueryGameRoundsParams 查询游戏轮次的参数结构
type PaQueryGameRoundsParams struct {
	ID           int       `json:"id,omitempty"`             // 轮次ID
	SerialNumber string    `json:"serial_number,omitempty"`  // 轮次序列号
	MachineID    int       `json:"machine_id,omitempty"`     // 机台ID
	GameTypeID   int       `json:"gametype_id,omitempty"`    // 游戏类型ID
	Status       string    `json:"status,omitempty"`         // 轮次状态
	StartAtStart time.Time `json:"start_at_start,omitempty"` // 开始时间起始
	StartAtEnd   time.Time `json:"start_at_end,omitempty"`   // 开始时间结束
	EndAtStart   time.Time `json:"end_at_start,omitempty"`   // 结束时间起始
	EndAtEnd     time.Time `json:"end_at_end,omitempty"`     // 结束时间结束
	CreatedStart time.Time `json:"created_start,omitempty"`  // 创建时间起始
	CreatedEnd   time.Time `json:"created_end,omitempty"`    // 创建时间结束
	SortBy       string    `json:"sort_by,omitempty"`        // 排序字段
	SortDir      string    `json:"sort_dir,omitempty"`       // 排序方向
	Size         int       `json:"size,omitempty"`           // 每页记录数
	Page         int       `json:"page,omitempty"`           // 页码
	Count        bool      `json:"count,omitempty"`          // 是否返回总记录数
}

// PaSingleWalletTradeLog 单钱包交易日志
type PaSingleWalletTradeLog struct {
	ID        int       `json:"id"`         // 日志ID
	OrderID   string    `json:"order_id"`   // 订单ID
	MachineID int       `json:"machine_id"` // 机台ID
	GameRound string    `json:"game_round"` // 游戏轮次
	UID       string    `json:"uid"`        // 用户ID
	Points    float64   `json:"points"`     // 交易金额
	Reason    string    `json:"reason"`     // 交易原因
	Comment   string    `json:"comment"`    // 备注
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// PaSingleWalletTradeLogResponse 单钱包交易日志查询响应结构
type PaSingleWalletTradeLogResponse struct {
	HasNext bool                     `json:"hasNext"` // 是否有下一页
	Total   int                      `json:"total"`   // 总记录数
	Items   []PaSingleWalletTradeLog `json:"items"`   // 交易日志列表
}

// PaQuerySingleWalletTradeLogsParams 查询单钱包交易日志的参数结构
type PaQuerySingleWalletTradeLogsParams struct {
	OrderID      string    `json:"order_id,omitempty"`      // 订单ID
	UID          string    `json:"uid,omitempty"`           // 用户ID
	PointsGte    float64   `json:"points_gte,omitempty"`    // 交易金额大于等于
	PointsLte    float64   `json:"points_lte,omitempty"`    // 交易金额小于等于
	CreatedStart time.Time `json:"created_start,omitempty"` // 创建时间起始
	CreatedEnd   time.Time `json:"created_end,omitempty"`   // 创建时间结束
	SortBy       string    `json:"sort_by,omitempty"`       // 排序字段
	SortDir      string    `json:"sort_dir,omitempty"`      // 排序方向
	Size         int       `json:"size,omitempty"`          // 每页记录数
	Page         int       `json:"page,omitempty"`          // 页码
	Count        bool      `json:"count,omitempty"`         // 是否返回总记录数
}

// PaAcceptRollbackRequest 接受回滚的请求结构
type PaAcceptRollbackRequest struct {
	OrderIDs []string `json:"order_ids"` // 订单ID列表
}

// PaAcceptRollbackResponse 接受回滚的响应结构
type PaAcceptRollbackResponse struct {
	SuccessCount int `json:"success_count"` // 成功回滚的订单数量
}

// PaQueryParams 查询参数结构
type PaQueryParams struct {
	Size    int    `json:"size,omitempty"`     // 每页记录数
	Page    int    `json:"page,omitempty"`     // 页码
	Count   bool   `json:"count,omitempty"`    // 是否返回总记录数
	SortBy  string `json:"sort_by,omitempty"`  // 排序字段
	SortDir string `json:"sort_dir,omitempty"` // 排序方向
}

// PaQueryResponse 查询响应结构
type PaQueryResponse struct {
	HasNext bool   `json:"hasNext"` // 是否有下一页
	Total   int    `json:"total"`   // 总记录数
	Items   []Item `json:"items"`   // 记录列表
}

// Item 记录结构
type Item struct {
	ID   int    `json:"id"`   // 记录ID
	Name string `json:"name"` // 记录名称
}
