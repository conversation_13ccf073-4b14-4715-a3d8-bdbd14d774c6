// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXCustomDailly(db *gorm.DB, opts ...gen.DOOption) xCustomDailly {
	_xCustomDailly := xCustomDailly{}

	_xCustomDailly.xCustomDaillyDo.UseDB(db, opts...)
	_xCustomDailly.xCustomDaillyDo.UseModel(&model.XCustomDailly{})

	tableName := _xCustomDailly.xCustomDaillyDo.TableName()
	_xCustomDailly.ALL = field.NewAsterisk(tableName)
	_xCustomDailly.ID = field.NewInt32(tableName, "Id")
	_xCustomDailly.SellerID = field.NewInt32(tableName, "SellerId")
	_xCustomDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xCustomDailly.Symbol = field.NewString(tableName, "Symbol")
	_xCustomDailly.UserID = field.NewInt32(tableName, "UserId")
	_xCustomDailly.Address = field.NewString(tableName, "Address")
	_xCustomDailly.GameID = field.NewInt32(tableName, "GameId")
	_xCustomDailly.RoomLevel = field.NewInt32(tableName, "RoomLevel")
	_xCustomDailly.ToAddress = field.NewString(tableName, "ToAddress")
	_xCustomDailly.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xCustomDailly.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xCustomDailly.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xCustomDailly.BonusRewardAmount = field.NewFloat64(tableName, "BonusRewardAmount")
	_xCustomDailly.Fee = field.NewFloat64(tableName, "Fee")
	_xCustomDailly.GasFee = field.NewFloat64(tableName, "GasFee")
	_xCustomDailly.NewGuys = field.NewInt32(tableName, "NewGuys")
	_xCustomDailly.IsGameAddress = field.NewInt32(tableName, "IsGameAddress")
	_xCustomDailly.BetCount = field.NewInt32(tableName, "BetCount")
	_xCustomDailly.WinCount = field.NewInt32(tableName, "WinCount")
	_xCustomDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xCustomDailly.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xCustomDailly.IsTest = field.NewInt32(tableName, "IsTest")
	_xCustomDailly.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xCustomDailly.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xCustomDailly.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xCustomDailly.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xCustomDailly.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xCustomDailly.STopAgentID = field.NewInt32(tableName, "STopAgentId")
	_xCustomDailly.CSGroup = field.NewString(tableName, "CSGroup")

	_xCustomDailly.fillFieldMap()

	return _xCustomDailly
}

type xCustomDailly struct {
	xCustomDaillyDo xCustomDaillyDo

	ALL               field.Asterisk
	ID                field.Int32
	SellerID          field.Int32   // 运营商
	RecordDate        field.Time    // 记录日期
	Symbol            field.String  // 币种
	UserID            field.Int32   // 玩家id
	Address           field.String  // 投注地址
	GameID            field.Int32   // 游戏id
	RoomLevel         field.Int32   // 房间等级
	ToAddress         field.String  // 官方地址
	BetAmount         field.Float64 // 下注金额
	BonusBetAmount    field.Float64 // Bonus下注金额
	RewardAmount      field.Float64 // 返奖金额
	BonusRewardAmount field.Float64 // Bonus返奖金额
	Fee               field.Float64 // 平台手续费
	GasFee            field.Float64 // gas费
	NewGuys           field.Int32   // 是否是第一次来平台完, 1是,2不是
	IsGameAddress     field.Int32   // 是否是游戏地址 1 是 ,2 不是
	BetCount          field.Int32   // 投注次数
	WinCount          field.Int32   // 中奖次数
	ChannelID         field.Int32   // 渠道
	BetChannelID      field.Int32   // 下注渠道Id
	IsTest            field.Int32   // 是否是测试玩家
	LiuSui            field.Float64 // 有效投注
	FirstLiuSui       field.Float64 // 扣减前有效投注
	ValidBetAmount    field.Float64 // 有效投注
	TopAgentID        field.Int32   // 顶级代理
	SpecialAgent      field.Int32   // 是否独立代理
	STopAgentID       field.Int32
	CSGroup           field.String

	fieldMap map[string]field.Expr
}

func (x xCustomDailly) Table(newTableName string) *xCustomDailly {
	x.xCustomDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xCustomDailly) As(alias string) *xCustomDailly {
	x.xCustomDaillyDo.DO = *(x.xCustomDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xCustomDailly) updateTableName(table string) *xCustomDailly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.Symbol = field.NewString(table, "Symbol")
	x.UserID = field.NewInt32(table, "UserId")
	x.Address = field.NewString(table, "Address")
	x.GameID = field.NewInt32(table, "GameId")
	x.RoomLevel = field.NewInt32(table, "RoomLevel")
	x.ToAddress = field.NewString(table, "ToAddress")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.BonusRewardAmount = field.NewFloat64(table, "BonusRewardAmount")
	x.Fee = field.NewFloat64(table, "Fee")
	x.GasFee = field.NewFloat64(table, "GasFee")
	x.NewGuys = field.NewInt32(table, "NewGuys")
	x.IsGameAddress = field.NewInt32(table, "IsGameAddress")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.WinCount = field.NewInt32(table, "WinCount")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.IsTest = field.NewInt32(table, "IsTest")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.STopAgentID = field.NewInt32(table, "STopAgentId")
	x.CSGroup = field.NewString(table, "CSGroup")

	x.fillFieldMap()

	return x
}

func (x *xCustomDailly) WithContext(ctx context.Context) *xCustomDaillyDo {
	return x.xCustomDaillyDo.WithContext(ctx)
}

func (x xCustomDailly) TableName() string { return x.xCustomDaillyDo.TableName() }

func (x xCustomDailly) Alias() string { return x.xCustomDaillyDo.Alias() }

func (x xCustomDailly) Columns(cols ...field.Expr) gen.Columns {
	return x.xCustomDaillyDo.Columns(cols...)
}

func (x *xCustomDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xCustomDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 29)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["RoomLevel"] = x.RoomLevel
	x.fieldMap["ToAddress"] = x.ToAddress
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["BonusRewardAmount"] = x.BonusRewardAmount
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["GasFee"] = x.GasFee
	x.fieldMap["NewGuys"] = x.NewGuys
	x.fieldMap["IsGameAddress"] = x.IsGameAddress
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["WinCount"] = x.WinCount
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["IsTest"] = x.IsTest
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["STopAgentId"] = x.STopAgentID
	x.fieldMap["CSGroup"] = x.CSGroup
}

func (x xCustomDailly) clone(db *gorm.DB) xCustomDailly {
	x.xCustomDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xCustomDailly) replaceDB(db *gorm.DB) xCustomDailly {
	x.xCustomDaillyDo.ReplaceDB(db)
	return x
}

type xCustomDaillyDo struct{ gen.DO }

func (x xCustomDaillyDo) Debug() *xCustomDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xCustomDaillyDo) WithContext(ctx context.Context) *xCustomDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xCustomDaillyDo) ReadDB() *xCustomDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xCustomDaillyDo) WriteDB() *xCustomDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xCustomDaillyDo) Session(config *gorm.Session) *xCustomDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xCustomDaillyDo) Clauses(conds ...clause.Expression) *xCustomDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xCustomDaillyDo) Returning(value interface{}, columns ...string) *xCustomDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xCustomDaillyDo) Not(conds ...gen.Condition) *xCustomDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xCustomDaillyDo) Or(conds ...gen.Condition) *xCustomDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xCustomDaillyDo) Select(conds ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xCustomDaillyDo) Where(conds ...gen.Condition) *xCustomDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xCustomDaillyDo) Order(conds ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xCustomDaillyDo) Distinct(cols ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xCustomDaillyDo) Omit(cols ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xCustomDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xCustomDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xCustomDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xCustomDaillyDo) Group(cols ...field.Expr) *xCustomDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xCustomDaillyDo) Having(conds ...gen.Condition) *xCustomDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xCustomDaillyDo) Limit(limit int) *xCustomDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xCustomDaillyDo) Offset(offset int) *xCustomDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xCustomDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xCustomDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xCustomDaillyDo) Unscoped() *xCustomDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xCustomDaillyDo) Create(values ...*model.XCustomDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xCustomDaillyDo) CreateInBatches(values []*model.XCustomDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xCustomDaillyDo) Save(values ...*model.XCustomDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xCustomDaillyDo) First() (*model.XCustomDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomDailly), nil
	}
}

func (x xCustomDaillyDo) Take() (*model.XCustomDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomDailly), nil
	}
}

func (x xCustomDaillyDo) Last() (*model.XCustomDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomDailly), nil
	}
}

func (x xCustomDaillyDo) Find() ([]*model.XCustomDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XCustomDailly), err
}

func (x xCustomDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XCustomDailly, err error) {
	buf := make([]*model.XCustomDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xCustomDaillyDo) FindInBatches(result *[]*model.XCustomDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xCustomDaillyDo) Attrs(attrs ...field.AssignExpr) *xCustomDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xCustomDaillyDo) Assign(attrs ...field.AssignExpr) *xCustomDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xCustomDaillyDo) Joins(fields ...field.RelationField) *xCustomDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xCustomDaillyDo) Preload(fields ...field.RelationField) *xCustomDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xCustomDaillyDo) FirstOrInit() (*model.XCustomDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomDailly), nil
	}
}

func (x xCustomDaillyDo) FirstOrCreate() (*model.XCustomDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomDailly), nil
	}
}

func (x xCustomDaillyDo) FindByPage(offset int, limit int) (result []*model.XCustomDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xCustomDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xCustomDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xCustomDaillyDo) Delete(models ...*model.XCustomDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xCustomDaillyDo) withDO(do gen.Dao) *xCustomDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
