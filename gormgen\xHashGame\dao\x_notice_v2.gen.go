// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXNoticeV2(db *gorm.DB, opts ...gen.DOOption) xNoticeV2 {
	_xNoticeV2 := xNoticeV2{}

	_xNoticeV2.xNoticeV2Do.UseDB(db, opts...)
	_xNoticeV2.xNoticeV2Do.UseModel(&model.XNoticeV2{})

	tableName := _xNoticeV2.xNoticeV2Do.TableName()
	_xNoticeV2.ALL = field.NewAsterisk(tableName)
	_xNoticeV2.UID = field.NewInt32(tableName, "UId")
	_xNoticeV2.SellerID = field.NewInt32(tableName, "SellerId")
	_xNoticeV2.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xNoticeV2.ID = field.NewInt32(tableName, "Id")
	_xNoticeV2.Nick = field.NewString(tableName, "Nick")
	_xNoticeV2.Title = field.NewString(tableName, "Title")
	_xNoticeV2.LangID = field.NewInt32(tableName, "LangId")
	_xNoticeV2.Content = field.NewString(tableName, "Content")
	_xNoticeV2.State = field.NewInt32(tableName, "State")
	_xNoticeV2.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xNoticeV2.Sort = field.NewInt32(tableName, "Sort")
	_xNoticeV2.Type = field.NewInt32(tableName, "Type")
	_xNoticeV2.Img = field.NewString(tableName, "Img")
	_xNoticeV2.Link = field.NewString(tableName, "Link")
	_xNoticeV2.IsNew = field.NewInt32(tableName, "IsNew")
	_xNoticeV2.IsPop = field.NewInt32(tableName, "IsPop")
	_xNoticeV2.PcLink = field.NewString(tableName, "PcLink")
	_xNoticeV2.GameType = field.NewString(tableName, "GameType")

	_xNoticeV2.fillFieldMap()

	return _xNoticeV2
}

type xNoticeV2 struct {
	xNoticeV2Do xNoticeV2Do

	ALL        field.Asterisk
	UID        field.Int32
	SellerID   field.Int32  // 运营商
	ChannelID  field.Int32  // 渠道id
	ID         field.Int32  // 公共id
	Nick       field.String // 公告名字
	Title      field.String // 公告标题
	LangID     field.Int32  // 语言id
	Content    field.String // 公告内容
	State      field.Int32  // 状态 1启用 2禁用
	UpdateTime field.Time   // 更新时间
	Sort       field.Int32  // 排序 数字越大越靠前
	Type       field.Int32  // 公告类型 1系统 2活动
	Img        field.String // 图片
	Link       field.String // 跳转路径
	IsNew      field.Int32  // 新公告 1是,2不是
	IsPop      field.Int32  // 弹窗 1是，2不是
	PcLink     field.String
	GameType   field.String // 游戏类型

	fieldMap map[string]field.Expr
}

func (x xNoticeV2) Table(newTableName string) *xNoticeV2 {
	x.xNoticeV2Do.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xNoticeV2) As(alias string) *xNoticeV2 {
	x.xNoticeV2Do.DO = *(x.xNoticeV2Do.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xNoticeV2) updateTableName(table string) *xNoticeV2 {
	x.ALL = field.NewAsterisk(table)
	x.UID = field.NewInt32(table, "UId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ID = field.NewInt32(table, "Id")
	x.Nick = field.NewString(table, "Nick")
	x.Title = field.NewString(table, "Title")
	x.LangID = field.NewInt32(table, "LangId")
	x.Content = field.NewString(table, "Content")
	x.State = field.NewInt32(table, "State")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.Sort = field.NewInt32(table, "Sort")
	x.Type = field.NewInt32(table, "Type")
	x.Img = field.NewString(table, "Img")
	x.Link = field.NewString(table, "Link")
	x.IsNew = field.NewInt32(table, "IsNew")
	x.IsPop = field.NewInt32(table, "IsPop")
	x.PcLink = field.NewString(table, "PcLink")
	x.GameType = field.NewString(table, "GameType")

	x.fillFieldMap()

	return x
}

func (x *xNoticeV2) WithContext(ctx context.Context) *xNoticeV2Do {
	return x.xNoticeV2Do.WithContext(ctx)
}

func (x xNoticeV2) TableName() string { return x.xNoticeV2Do.TableName() }

func (x xNoticeV2) Alias() string { return x.xNoticeV2Do.Alias() }

func (x xNoticeV2) Columns(cols ...field.Expr) gen.Columns { return x.xNoticeV2Do.Columns(cols...) }

func (x *xNoticeV2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xNoticeV2) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 18)
	x.fieldMap["UId"] = x.UID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Nick"] = x.Nick
	x.fieldMap["Title"] = x.Title
	x.fieldMap["LangId"] = x.LangID
	x.fieldMap["Content"] = x.Content
	x.fieldMap["State"] = x.State
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["Type"] = x.Type
	x.fieldMap["Img"] = x.Img
	x.fieldMap["Link"] = x.Link
	x.fieldMap["IsNew"] = x.IsNew
	x.fieldMap["IsPop"] = x.IsPop
	x.fieldMap["PcLink"] = x.PcLink
	x.fieldMap["GameType"] = x.GameType
}

func (x xNoticeV2) clone(db *gorm.DB) xNoticeV2 {
	x.xNoticeV2Do.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xNoticeV2) replaceDB(db *gorm.DB) xNoticeV2 {
	x.xNoticeV2Do.ReplaceDB(db)
	return x
}

type xNoticeV2Do struct{ gen.DO }

func (x xNoticeV2Do) Debug() *xNoticeV2Do {
	return x.withDO(x.DO.Debug())
}

func (x xNoticeV2Do) WithContext(ctx context.Context) *xNoticeV2Do {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xNoticeV2Do) ReadDB() *xNoticeV2Do {
	return x.Clauses(dbresolver.Read)
}

func (x xNoticeV2Do) WriteDB() *xNoticeV2Do {
	return x.Clauses(dbresolver.Write)
}

func (x xNoticeV2Do) Session(config *gorm.Session) *xNoticeV2Do {
	return x.withDO(x.DO.Session(config))
}

func (x xNoticeV2Do) Clauses(conds ...clause.Expression) *xNoticeV2Do {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xNoticeV2Do) Returning(value interface{}, columns ...string) *xNoticeV2Do {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xNoticeV2Do) Not(conds ...gen.Condition) *xNoticeV2Do {
	return x.withDO(x.DO.Not(conds...))
}

func (x xNoticeV2Do) Or(conds ...gen.Condition) *xNoticeV2Do {
	return x.withDO(x.DO.Or(conds...))
}

func (x xNoticeV2Do) Select(conds ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Select(conds...))
}

func (x xNoticeV2Do) Where(conds ...gen.Condition) *xNoticeV2Do {
	return x.withDO(x.DO.Where(conds...))
}

func (x xNoticeV2Do) Order(conds ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Order(conds...))
}

func (x xNoticeV2Do) Distinct(cols ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xNoticeV2Do) Omit(cols ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xNoticeV2Do) Join(table schema.Tabler, on ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xNoticeV2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xNoticeV2Do) RightJoin(table schema.Tabler, on ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xNoticeV2Do) Group(cols ...field.Expr) *xNoticeV2Do {
	return x.withDO(x.DO.Group(cols...))
}

func (x xNoticeV2Do) Having(conds ...gen.Condition) *xNoticeV2Do {
	return x.withDO(x.DO.Having(conds...))
}

func (x xNoticeV2Do) Limit(limit int) *xNoticeV2Do {
	return x.withDO(x.DO.Limit(limit))
}

func (x xNoticeV2Do) Offset(offset int) *xNoticeV2Do {
	return x.withDO(x.DO.Offset(offset))
}

func (x xNoticeV2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *xNoticeV2Do {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xNoticeV2Do) Unscoped() *xNoticeV2Do {
	return x.withDO(x.DO.Unscoped())
}

func (x xNoticeV2Do) Create(values ...*model.XNoticeV2) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xNoticeV2Do) CreateInBatches(values []*model.XNoticeV2, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xNoticeV2Do) Save(values ...*model.XNoticeV2) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xNoticeV2Do) First() (*model.XNoticeV2, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XNoticeV2), nil
	}
}

func (x xNoticeV2Do) Take() (*model.XNoticeV2, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XNoticeV2), nil
	}
}

func (x xNoticeV2Do) Last() (*model.XNoticeV2, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XNoticeV2), nil
	}
}

func (x xNoticeV2Do) Find() ([]*model.XNoticeV2, error) {
	result, err := x.DO.Find()
	return result.([]*model.XNoticeV2), err
}

func (x xNoticeV2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XNoticeV2, err error) {
	buf := make([]*model.XNoticeV2, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xNoticeV2Do) FindInBatches(result *[]*model.XNoticeV2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xNoticeV2Do) Attrs(attrs ...field.AssignExpr) *xNoticeV2Do {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xNoticeV2Do) Assign(attrs ...field.AssignExpr) *xNoticeV2Do {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xNoticeV2Do) Joins(fields ...field.RelationField) *xNoticeV2Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xNoticeV2Do) Preload(fields ...field.RelationField) *xNoticeV2Do {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xNoticeV2Do) FirstOrInit() (*model.XNoticeV2, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XNoticeV2), nil
	}
}

func (x xNoticeV2Do) FirstOrCreate() (*model.XNoticeV2, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XNoticeV2), nil
	}
}

func (x xNoticeV2Do) FindByPage(offset int, limit int) (result []*model.XNoticeV2, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xNoticeV2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xNoticeV2Do) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xNoticeV2Do) Delete(models ...*model.XNoticeV2) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xNoticeV2Do) withDO(do gen.Dao) *xNoticeV2Do {
	x.DO = *do.(*gen.DO)
	return x
}
