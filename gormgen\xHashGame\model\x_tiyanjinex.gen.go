// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTiyanjinex = "x_tiyanjinex"

// XTiyanjinex mapped from table <x_tiyanjinex>
type XTiyanjinex struct {
	ID         int32     `gorm:"column:Id;primaryKey" json:"Id"`
	State      int32     `gorm:"column:State;comment:状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成" json:"State"` // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
}

// TableName XTiyanjinex's table name
func (*XTiyanjinex) TableName() string {
	return TableNameXTiyanjinex
}
