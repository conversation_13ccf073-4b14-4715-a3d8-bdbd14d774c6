// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserAddress = "x_user_address"

// XUserAddress mapped from table <x_user_address>
type XUserAddress struct {
	ChannelID    int32     `gorm:"column:ChannelId;primaryKey;default:1" json:"ChannelId"`
	Address      string    `gorm:"column:Address;primaryKey" json:"Address"`
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	BetCountTrx  int32     `gorm:"column:BetCountTrx" json:"BetCountTrx"`
	BetCountUsdt int32     `gorm:"column:BetCountUsdt" json:"BetCountUsdt"`
	BetTrx       float64   `gorm:"column:BetTrx;default:0.000000" json:"BetTrx"`
	BetUsdt      float64   `gorm:"column:BetUsdt;default:0.000000" json:"BetUsdt"`
	RewardTrx    float64   `gorm:"column:RewardTrx;default:0.000000" json:"RewardTrx"`
	RewardUsdt   float64   `gorm:"column:RewardUsdt;default:0.000000" json:"RewardUsdt"`
	LiuSuiTrx    float64   `gorm:"column:LiuSuiTrx;default:0.000000" json:"LiuSuiTrx"`
	LiuSuiUsdt   float64   `gorm:"column:LiuSuiUsdt;default:0.000000" json:"LiuSuiUsdt"`
}

// TableName XUserAddress's table name
func (*XUserAddress) TableName() string {
	return TableNameXUserAddress
}
