package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"gorm.io/gorm"

	"github.com/spf13/viper"

	"github.com/beego/beego/logs"
	goset "github.com/deckarep/golang-set/v2"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/gorilla/websocket"
	"github.com/zeromicro/go-zero/core/threading"
)

var last_block_num int

var upGrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

type SocketController struct {
	address_conn     sync.Map
	blockid          int
	blockEthId       int
	blockBscId       int
	connections      sync.Map
	connection_count int
	blockEthTime     time.Time
	writeQueue       chan []byte
	endpoints        []string
	writeChannels    sync.Map // 存储每个连接的写入通道
}

var SocketHandler = &SocketController{}

func (c *SocketController) initWebSocketConfig() {
	if eps := viper.GetString("websocketapi"); eps != "" {
		c.endpoints = strings.Split(eps, ",")
		// 清理空格并移除空字符串
		validEndpoints := make([]string, 0)
		for _, ep := range c.endpoints {
			ep = strings.TrimSpace(ep)
			if ep != "" {
				validEndpoints = append(validEndpoints, ep)
			}
		}
		c.endpoints = validEndpoints
	}
}

func (c *SocketController) Init() {
	c.connection_count = 0
	c.initWebSocketConfig()

	server.Http().GetNoAuth("/api/ws", c.ws)
	server.Http().PostNoAuth("/api/uvawmbpyvikb", c.push_order)
	server.Http().PostNoAuth("/api/zneamnq6awc4", c.push_block)
	server.Http().PostNoAuth("/api/nwluxic3xjfu", c.push_win)
	server.Http().PostNoAuth("/api/frdklfdsrgvi", c.push_luzi)
	server.Http().PostNoAuth("/api/ewokkr83wnvc", c.push_block_eth_or_bsc)
	server.Http().PostNoAuth("/api/b924kr3uoi2v", c.push_block_sol)
	server.Http().PostNoAuth("/api/znsfdsfd2ghd", c.push_message)
	server.Http().PostNoAuth("/api/ws/refresh_amount", c.refresh_amount)

	c.writeQueue = make(chan []byte, 100)
	c.StartWriter()
	threading.GoSafe(func() {
		for {
			time.Sleep(time.Minute)
			c.statisticOnlineTime()
		}
	})
}

func (c *SocketController) ws(ctx *abugo.AbuHttpContent) {
	conn, err := upGrader.Upgrade(ctx.Gin().Writer, ctx.Gin().Request, nil)
	if err != nil {
		return
	}
	c.connection_count++
	// logs.Debug("ws connection count:", c.connection_count)

	// 为每个连接创建一个消息通道
	writeChan := make(chan []byte, 256)
	c.writeChannels.Store(conn, writeChan)

	// 启动专用的写入goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logs.Error("WebSocket writer goroutine panic:", r)
			}
		}()

		for message := range writeChan {
			err := conn.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				logs.Error("Failed to write to WebSocket:", err)
				break
			}
		}
	}()

	c.connections.Store(conn, 1)

	// 发送初始区块信息
	{
		if c.blockid > 0 {
			obj := map[string]interface{}{
				"MsgType": "block_update",
				"Data": gin.H{
					"Id": c.blockid,
				},
			}
			bytes, _ := json.Marshal(&obj)
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		}
	}
	{
		if c.blockEthId > 0 {
			now := carbon.Parse(carbon.Now().String()).StdTime()
			obj := map[string]interface{}{
				"MsgType": utils.EthBlockUpdateMsgType,
				"Data": gin.H{
					"Id":        c.blockEthId,
					"BlockTime": now.Sub(c.blockEthTime).Seconds(),
				},
			}
			bytes, _ := json.Marshal(&obj)
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		}
	}
	{
		if c.blockBscId > 0 {
			obj := map[string]interface{}{
				"MsgType": utils.BscBlockUpdateMsgType,
				"Data": gin.H{
					"Id": c.blockBscId,
				},
			}
			bytes, _ := json.Marshal(&obj)
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		}
	}

	UserId := 0
	for {
		_, bytes, err := conn.ReadMessage()
		if err != nil {
			break
		}
		msg := string(bytes)
		if msg == "ping" {
			c.safeWriteMessage(conn, websocket.TextMessage, []byte("ping"))
			continue
		}

		jmsg := make(map[string]interface{})
		err = json.Unmarshal(bytes, &jmsg)
		if err != nil {
			break
		}
		msgtype := abugo.GetStringFromInterface(jmsg["MsgType"])
		msgbytes, err := json.Marshal(jmsg["Data"])
		if err != nil {
			break
		}
		if msgtype != "attach" {
			break
		}

		type ReqData struct {
			UserId int
		}
		reqdata := ReqData{}
		err = json.Unmarshal(msgbytes, &reqdata)
		if err != nil {
			break
		}
		UserId = reqdata.UserId
		if UserId <= 0 {
			break
		}
		c.address_conn.Store(UserId, conn)
		obj := map[string]interface{}{
			"MsgType": "attach",
			"Data": gin.H{
				"Result":  "ok",
				"Balance": GetUserAmount(UserId),
			},
		}
		retbytes, _ := json.Marshal(&obj)
		c.safeWriteMessage(conn, websocket.TextMessage, retbytes)
	}

	// 清理资源
	c.connection_count--

	// 关闭写入通道
	if ch, ok := c.writeChannels.Load(conn); ok {
		close(ch.(chan []byte))
		c.writeChannels.Delete(conn)
	}

	conn.Close()
	c.address_conn.Delete(UserId)
	c.connections.Delete(conn)
	logs.Debug("ws connection count:", c.connection_count)
}

func (c *SocketController) push_bet(GameId int, Amount float64, UserId int, ChainType int) {
	if UserId <= 0 {
		return
	}
	// 获取用户昵称
	Nickname := ""
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, err := server.Db().Table("x_user").Select("NickName").Where(where).GetOne()
	if err == nil && udata != nil {
		Nickname = abugo.GetStringFromInterface((*udata)["NickName"])
	}

	obj := map[string]interface{}{
		"MsgType": "bet",
		"Data": gin.H{
			"GameId":    GameId,
			"Amount":    Amount,
			"UserId":    UserId,
			"Nickname":  Nickname,
			"ChainType": ChainType,
		},
	}
	bytes, _ := json.Marshal(&obj)
	c.connections.Range(func(key, _ interface{}) bool {
		conn := key.(*websocket.Conn)
		c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		return true
	})
	return
}

func (c *SocketController) push_order(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, 0)
	data, err := server.Db().Table("x_order").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(data == nil, &errcode, "订单不存在 ") {
		return
	}
	Amount := 0.0
	UserId := int(abugo.GetInt64FromInterface((*data)["UserId"]))
	if UserId > 0 {
		uwhere := abugo.AbuDbWhere{}
		uwhere.Add("and", "UserId", "=", UserId, nil)
		udata, err := server.Db().Table("x_user").Select("Amount").Where(uwhere).GetOne()
		if err != nil {
			Amount = abugo.GetFloat64FromInterface((*udata)["Amount"])
		}
	}
	ic, ok := c.address_conn.Load(UserId)
	if ok {
		conn := ic.(*websocket.Conn)
		if conn != nil {
			chainType := abugo.GetInt64FromInterface((*data)["ChainType"])
			msgType := utils.TrxOrderUpdateMsgType
			//if chainType == utils.EthType {
			//	msgType = utils.EthOrderUpdateMsgType
			//} else if chainType == utils.BscType {
			//	msgType = utils.BscOrderUpdateMsgType
			//}
			obj := map[string]interface{}{
				"MsgType": msgType,
				"Data": gin.H{
					"Id":            reqdata.Id,
					"GameId":        (*data)["GameId"],
					"Memo":          (*data)["Memo"],
					"RewardAmount":  abugo.GetFloat64FromInterface((*data)["RewardAmount"]),
					"CreateTime":    (*data)["CreateTime"],
					"BlockHash":     (*data)["BlockHash"],
					"BlockNum":      (*data)["BlockNum"],
					"Symbol":        (*data)["Symbol"],
					"Amount":        (*data)["Amount"],
					"IsWin":         (*data)["IsWin"],
					"State":         (*data)["State"],
					"Period":        (*data)["Period"],
					"NextBlockHash": (*data)["NextBlockHash"],
					"BetArea":       (*data)["BetArea"],
					"TxId":          (*data)["TxId"],
					"OpenArea":      (*data)["OpenArea"],
					"UserAmount":    Amount,
					"ChainType":     chainType,
				},
			}
			bytes, _ := json.Marshal(&obj)
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		}
	}
	ctx.RespOK()
}

func (c *SocketController) push_block(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Id == 0 {
		return
	}
	c.blockid = reqdata.Id
	last_block_num = reqdata.Id
	server.Db().Conn().Exec("update x_config set ConfigValue = ? where ConfigName = 'LastBlockNum'", fmt.Sprintf("%v", reqdata.Id))
	obj := map[string]interface{}{
		"MsgType": "block_update",
		"Data": gin.H{
			"Id": reqdata.Id,
		},
	}
	bytes, _ := json.Marshal(&obj)
	c.connections.Range(func(key, _ interface{}) bool {
		conn := key.(*websocket.Conn)
		c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		return true
	})
	ctx.RespOK()
}

func (c *SocketController) push_message(ctx *abugo.AbuHttpContent) {
	errcode := 0
	// 解析请求数据
	var reqData struct {
		Type      string                 `json:"Type"`      // 业务类型，如 "1"=领取彩金提醒,"3"=充值到账提醒,"4": 提现成功提醒,"5": 提现提交提醒,"6": 提现审核提醒
		UserId    int                    `json:"UserId"`    // 用户Id
		Amount    float64                `json:"Amount"`    // 涉及金额
		Currency  string                 `json:"Currency"`  // 涉及币种
		Variables map[string]interface{} `json:"Variables"` // 对应消息模板扩展变量，没有就不传
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("消息推送  api Body=", string(bodyBytes))

	if err := json.Unmarshal(bodyBytes, &reqData); err != nil {
		logs.Error("消息推送 JSON解析失败: err=", err)
		ctx.RespErrString(true, &errcode, "解析请求数据失败")
		return
	}

	// 验证必填参数
	if reqData.Type == "" {
		logs.Error("消息推送 业务类型不能为空 userId=", reqData.UserId)
		ctx.RespErrString(true, &errcode, "业务类型不能为空")
		return
	}

	if reqData.UserId <= 0 {
		logs.Error("消息推送 用户ID无效 userId=", reqData.UserId)
		ctx.RespErrString(true, &errcode, "用户ID无效")
		return
	}
	// 查询用户信息
	var user struct {
		UserId int `gorm:"column:UserId"`
		State  int `gorm:"column:State"`
	}
	err = server.Db().GormDao().Table("x_user").Where("UserId = ?", reqData.UserId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("消息推送 用户ID无效 userId=", reqData.UserId, " error=", err)
			ctx.RespErrString(true, &errcode, "用户ID无效")
		} else {
			logs.Error("消息推送 查询用户失败 userId=", reqData.UserId, " error=", err)
			ctx.RespErrString(true, &errcode, "查询用户失败")
		}
		return
	}

	if user.State != 1 {
		logs.Error("消息推送 该账号被禁用 userId=", reqData.UserId)
		ctx.RespErrString(true, &errcode, "该账号被禁用")
		return
	}
	// 创建消息发送服务
	messageService := msg.NewSendMessageAPIService()
	// 触发发送消息
	err = messageService.SendAccountMessage(reqData.Type, int(reqData.UserId), reqData.Amount, reqData.Currency, reqData.Variables)
	if err != nil {
		logs.Error("发送消息失败: ", " userId=", reqData.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, "发送消息失败: "+err.Error())
		return
	}

	ctx.RespOK()
}

func (c *SocketController) push_win(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id       int `validate:"required"`
		GameType string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	err = c.WinBoradcast(reqdata.Id, reqdata.GameType)

	if ctx.RespErr(err, &errcode) {
		return
	}

	// if symbol == "usdt" && amount < 100 {
	// 	ctx.RespErrString(true, &errcode, "无需推送")
	// 	return
	// }
	// if symbol == "trx" && amount < 1000 {
	// 	ctx.RespErrString(true, &errcode, "无需推送")
	// 	return
	// }

	// 调用 BaseController 的 PushCustomerIO
	// userid := (*data)["UserId"].(string)
	// baseCtrl := &BaseController{}
	// baseCtrl.PushCustomerIO(userid, gin.H{
	// 	"account_id": userid,
	// }, "bonus_issued", xgo.H{
	// 	"game_id":       (*data)["GameId"],
	// 	"account_id":    userid,
	// 	"from_address":  (*data)["FromAddress"],
	// 	"reward_amount": abugo.GetFloat64FromInterface((*data)["RewardAmount"]),
	// 	"amount":        (*data)["Amount"],
	// 	"currency":      (*data)["Symbol"],
	// })
	ctx.RespOK()
}

func (c *SocketController) WinBoradcast(Id int, GameType string) error {

	var data *map[string]interface{}
	var err error
	var amount float64
	var betAmount float64
	var iswin int64
	var symbol string
	if GameType == "Hash" {
		where := abugo.AbuDbWhere{}
		where.Add("and", "Id", "=", Id, 0)
		data, err = server.Db().Table("x_order").Where(where).GetOne()
		amount = abugo.GetFloat64FromInterface((*data)["RewardAmount"])
		betAmount = abugo.GetFloat64FromInterface((*data)["Amount"])
		iswin = abugo.GetInt64FromInterface((*data)["IsWin"])
		symbol = abugo.GetStringFromInterface((*data)["Symbol"])
	} else {
		// TODO 三方的订单数据参照 Dianzi
		if GameType == "Dianzi" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_dianzhi").Where(where).GetOne()
		}

		if GameType == "Qukuailian" || GameType == "UpDown" || GameType == "Trading" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_quiwei").Where(where).GetOne()
		}

		if GameType == "Zhenren" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_live").Where(where).GetOne()
		}

		if GameType == "Qipai" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_qipai").Where(where).GetOne()
		}

		if GameType == "Sport" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_sport").Where(where).GetOne()
		}

		if GameType == "Texas" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_texas").Where(where).GetOne()
		}

		if GameType == "Lottery" {
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", Id, 0)
			data, err = server.Db().Table("x_third_lottery").Where(where).GetOne()
		}

		amount = abugo.GetFloat64FromInterface((*data)["WinAmount"])
		betAmount = abugo.GetFloat64FromInterface((*data)["BetAmount"])
		symbol = "usdt"
		if amount > 0 {
			iswin = 1
		} else {
			iswin = 2
		}

	}

	if err != nil {
		return err
	}

	if data == nil {
		return errors.New("订单不存在")
	}

	type BroadcastRewardConfig struct {
		BaseConfig struct {
			Hash       float64
			Dianzi     float64
			Qukuailian float64
			Zhenren    float64
			Qipai      float64
			Sport      float64
			Texas      float64
			Lottery    float64
			Og         float64
		}
		RepeatConfig []struct {
			Min   float64
			Max   float64
			Times int
		}
		Template string
	}
	var config BroadcastRewardConfig
	// 获取播报配置
	redisData := server.CRedis().HGet("CONFIG", "BROADCAST_REWARD_CONFIG")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 config
		if err := json.Unmarshal([]byte(dataStr), &config); err != nil {
			return errors.New("序列化失败")

		}
	}

	if iswin != 1 {
		return errors.New("无需推送")
	}

	if GameType == "Hash" && config.BaseConfig.Hash != 0 && amount < config.BaseConfig.Hash {
		return errors.New("无需推送")
	}

	if GameType == "Dianzi" && config.BaseConfig.Dianzi != 0 && amount < config.BaseConfig.Dianzi {
		return errors.New("无需推送")
	}

	if GameType == "Qukuailian" && config.BaseConfig.Qukuailian != 0 && amount < config.BaseConfig.Qukuailian {
		return errors.New("无需推送")
	}

	if GameType == "Zhenren" && config.BaseConfig.Zhenren != 0 && amount < config.BaseConfig.Zhenren {
		return errors.New("无需推送")
	}

	if GameType == "Qipai" && config.BaseConfig.Qipai != 0 && amount < config.BaseConfig.Qipai {
		return errors.New("无需推送")
	}

	if GameType == "Sport" && config.BaseConfig.Sport != 0 && amount < config.BaseConfig.Sport {
		return errors.New("无需推送")
	}

	if GameType == "Texas" && config.BaseConfig.Texas != 0 && amount < config.BaseConfig.Texas {
		return errors.New("无需推送")
	}

	if GameType == "Lottery" && config.BaseConfig.Lottery != 0 && amount < config.BaseConfig.Lottery {
		return errors.New("无需推送")
	}

	if GameType == "Og" && config.BaseConfig.Og != 0 && amount < config.BaseConfig.Og {
		return errors.New("无需推送")
	}

	var times int
	times = 1
	for _, v := range config.RepeatConfig {
		if amount >= v.Min && amount < v.Max {
			times = v.Times
		}
	}

	obj := map[string]interface{}{
		"MsgType": "order_win",
		"Data": gin.H{
			"Id":           Id,
			"GameId":       (*data)["GameId"],
			"UserId":       (*data)["UserId"],
			"FromAddress":  (*data)["FromAddress"],
			"RewardAmount": amount,
			"Amount":       betAmount,
			"Symbol":       symbol,
			"GameType":     GameType,
		},
	}

	bytes, _ := json.Marshal(&obj)
	c.connections.Range(func(key, _ interface{}) bool {
		conn := key.(*websocket.Conn)
		for i := 0; i < times; i++ {
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		}
		//logs.Info("推送派奖成功", " id=", Id, " GameType=", GameType, " bytes=", string(bytes))
		return true
	})

	return nil
}

func (c *SocketController) push_luzi(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id        int `validate:"required"`
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.Id, "")
	data, _ := server.Db().Table("x_blockopen").Where(where).GetOne()
	if data != nil {
		var obj map[string]interface{}
		if reqdata.ChainType == 1 || reqdata.ChainType == 0 {
			obj = map[string]interface{}{
				"MsgType": "luzi",
				"Data":    data,
			}
		}

		if reqdata.ChainType == 3 {
			obj = map[string]interface{}{
				"MsgType": "bsc_luzi",
				"Data":    data,
			}
		}

		bytes, _ := json.Marshal(&obj)
		c.connections.Range(func(key, _ interface{}) bool {
			conn := key.(*websocket.Conn)
			c.safeWriteMessage(conn, websocket.TextMessage, bytes)
			return true
		})
	}
	ctx.RespOK()
}

func (c *SocketController) StartWriter() {
	go func() {
		for message := range c.writeQueue {
			c.connections.Range(func(key, _ interface{}) bool {
				conn := key.(*websocket.Conn)
				c.safeWriteMessage(conn, websocket.TextMessage, message)
				return true
			})
		}
	}()
}

func (c *SocketController) SubHashBetMessage(msg string) {
	//var data = make(map[string]any)
	//err := json.Unmarshal([]byte(msg), &data)
	//if err != nil {
	//	logs.Error("subHashBetMessage json Unmarshal err :", err)
	//	return
	//}
	//obj := map[string]interface{}{
	//	"MsgType": "hash_bet_info",
	//	"Data":    data,
	//}
	//bytes, _ := json.Marshal(&obj)
	//c.connections.Range(func(key, _ interface{}) bool {
	//	conn := key.(*websocket.Conn)
	//	conn.WriteMessage(websocket.TextMessage, bytes)
	//	return true
	//})

	var data = make(map[string]any)
	err := json.Unmarshal([]byte(msg), &data)
	if err != nil {
		logs.Error("subHashBetMessage json Unmarshal err :", err)
		return
	}
	obj := map[string]interface{}{
		"MsgType": "hash_bet_info",
		"Data":    data,
	}
	bytes, _ := json.Marshal(&obj)

	// 将消息发送到队列
	c.writeQueue <- bytes
}

// GetUserAmount 获取用户余额
func GetUserAmount(UserId int) float64 {
	uwhere := abugo.AbuDbWhere{}
	uwhere.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Select("Amount").Where(uwhere).GetOne()
	if udata == nil {
		return 0
	}

	return abugo.GetFloat64FromInterface((*udata)["Amount"])
}

func (c *SocketController) RefreshUserAmount(userId int) (err error) {
	defer func() {
		// 只是刷新余额通知，有时候链接已关闭的时候，conn.WriteMessage会panic但是无关紧要，在这里recover处理掉就行
		recover()
	}()

	// 1. 尝试本地推送
	if ic, ok := c.address_conn.Load(userId); ok {
		if ic == nil {
			err = fmt.Errorf("用户%d连接为空", userId)
			return
		}

		conn := ic.(*websocket.Conn)
		obj := map[string]interface{}{
			"MsgType": "attach",
			"Data": gin.H{
				"Result":  "ok",
				"Balance": GetUserAmount(userId),
			},
		}
		retbytes, _ := json.Marshal(&obj)
		c.safeWriteMessage(conn, websocket.TextMessage, retbytes)
		return nil
	}

	// 2. 尝试配置的所有服务器
	amount := GetUserAmount(userId)
	localIPs := c.GetLocalIPs()

	for _, endpoint := range c.endpoints {
		endpoint = strings.TrimSpace(endpoint)
		if endpoint == "" {
			continue
		}

		// 检查endpoint是否包含任何本机IP
		isLocalEndpoint := false
		for _, ip := range localIPs {
			if strings.Contains(endpoint, ip) {
				isLocalEndpoint = true
				logs.Info("跳过本机URL:", endpoint, " IP:", ip)
				break
			}
		}
		if isLocalEndpoint {
			continue
		}

		// 发送HTTP请求到服务器
		url := fmt.Sprintf("%s/refresh_amount", endpoint)
		body := map[string]interface{}{
			"userId": userId,
			"amount": amount,
		}
		jsonBody, _ := json.Marshal(body)

		resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonBody))
		if err != nil {
			logs.Warning("发送余额变动通知 Failed to send request to endpoint:", endpoint, "error:", err)
			continue
		}

		// 读取响应内容
		defer resp.Body.Close()
		var result struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
		}
		if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
			logs.Warning("发送余额变动通知 Failed to decode response from endpoint:", endpoint, "error:", err)
			continue
		}

		// 检查响应状态
		if resp.StatusCode == http.StatusOK && result.Code == 200 {
			logs.Info("成功发送余额变动通知:", endpoint, "code:", result.Code, "msg:", result.Msg)
			return nil
		}
		logs.Warning("发送余额变动通知 returned error from endpoint:", endpoint, "code:", result.Code, "msg:", result.Msg)
	}

	logs.Error("发送余额变动通知错误")
	return fmt.Errorf("failed to refresh user amount on all endpoints")
}

func (c *SocketController) push_block_eth_or_bsc(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id        int `validate:"required" json:"Id" form:"Id"`
		ChainType int `validate:"required" json:"ChainType" form:"ChainType"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	var configName string
	var msgType string
	var res = gin.H{
		"Id": reqdata.Id,
	}
	if reqdata.ChainType == 1 {
		c.blockEthId = reqdata.Id
		configName = utils.EthLastBlockNum
		msgType = utils.EthBlockUpdateMsgType
		blockTime := carbon.Parse(carbon.Now().String()).StdTime()
		logs.Info("push_block_eth_or_bsc blockTime", blockTime)
		c.blockEthTime = blockTime
		res["BlockTime"] = 0
	} else if reqdata.ChainType == 2 {
		c.blockBscId = reqdata.Id
		configName = utils.BscLastBlockNum
		msgType = utils.BscBlockUpdateMsgType
	} else {
		return
	}
	configTb := server.DaoxHashGame().XConfig
	configDb := server.DaoxHashGame().XConfig.WithContext(context.Background())
	_, err = configDb.Where(configTb.ConfigName.Eq(configName)).Update(configTb.ConfigValue, reqdata.Id)
	if err != nil {
		logs.Error("push_block_eth_or_bsc err: ", err)
	}
	obj := map[string]interface{}{
		"MsgType": msgType,
		"Data":    res,
	}
	bytes, _ := json.Marshal(&obj)
	c.connections.Range(func(key, _ interface{}) bool {
		conn := key.(*websocket.Conn)
		c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		return true
	})
	ctx.RespOK()
}

func (c *SocketController) push_block_sol(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Id == 0 {
		return
	}
	c.blockid = reqdata.Id
	configTb := server.DaoxHashGame().XConfig
	configDb := server.DaoxHashGame().XConfig.WithContext(context.Background())
	_, err = configDb.Where(configTb.ConfigName.Eq(utils.SolLastBlockNum)).Update(configTb.ConfigValue, reqdata.Id)
	if err != nil {
		logs.Error("push_block_sol err: ", err)
	}
	obj := map[string]interface{}{
		"MsgType": utils.SolBlockUpdateMsgType,
		"Data": gin.H{
			"Id": reqdata.Id,
		},
	}
	bytes, _ := json.Marshal(&obj)
	c.connections.Range(func(key, _ interface{}) bool {
		conn := key.(*websocket.Conn)
		c.safeWriteMessage(conn, websocket.TextMessage, bytes)
		return true
	})
	ctx.RespOK()
}

func (c *SocketController) statisticOnlineTime() {
	set := goset.NewSet[int]()
	process := []*model.XUserOnlineProcess{}
	now := carbon.Parse(carbon.Now().StartOfMinute().ToDateTimeString()).StdTime()
	c.address_conn.Range(func(key, value any) bool {
		userId := key.(int)
		if set.Add(userId) {
			process = append(process, &model.XUserOnlineProcess{
				UserID: int32(userId),
				Time:   now,
			})
		} else {
			logs.Info("statisticOnlineTime repeat ", userId)
		}
		return true
	})
	if len(process) > 0 {
		tb := server.DaoxHashGame().XUserOnlineProcess
		err := tb.WithContext(context.Background()).Create(process...)
		if err != nil {
			logs.Error("statisticOnlineTime Create ", err)
		}
	}
}

func (c *SocketController) refresh_amount(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId int     `validate:"required" json:"userId" form:"userId"`
		Amount float64 `validate:"required" json:"amount" form:"amount"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		logs.Error("refresh_amount 解析数据出错", " err=", err)
		return
	}

	ic, ok := c.address_conn.Load(reqdata.UserId)
	if !ok {
		logs.Error("refresh_amount 发送余额变动通知 user not connected", " userId=", reqdata.UserId)
		ctx.RespErr(fmt.Errorf(" 发送余额变动通知 user %d not connected", reqdata.UserId), &errcode)
		return
	}

	conn := ic.(*websocket.Conn)
	if conn == nil {
		logs.Error("refresh_amount 发送余额变动通知 user not connected", " userId=", reqdata.UserId)
		ctx.RespErr(fmt.Errorf(" 发送余额变动通知 user %d connection is nil", reqdata.UserId), &errcode)
		return
	}

	obj := map[string]interface{}{
		"MsgType": "attach",
		"Data": gin.H{
			"Result":  "ok",
			"Balance": reqdata.Amount,
		},
	}
	retbytes, _ := json.Marshal(&obj)
	c.safeWriteMessage(conn, websocket.TextMessage, retbytes)

	ctx.RespOK()
}

// GetLocalIPs 获取所有本机IPv4地址
func (c *SocketController) GetLocalIPs() []string {
	var ips []string
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ips
	}
	for _, addr := range addrs {
		// 检查ip地址判断是否回环地址
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipv4 := ipnet.IP.To4(); ipv4 != nil {
				ips = append(ips, ipv4.String())
			}
		}
	}
	return ips
}

// safeWriteMessage 安全地向WebSocket连接发送消息
// 使用连接对应的消息通道发送消息，避免并发写入问题
func (c *SocketController) safeWriteMessage(conn *websocket.Conn, messageType int, data []byte) {
	if ch, ok := c.writeChannels.Load(conn); ok {
		select {
		case ch.(chan []byte) <- data:
			// 消息成功发送到通道
		default:
			// 通道已满，记录错误
			logs.Error("WebSocket write channel is full, message dropped")
		}
	} else {
		logs.Error("No write channel found for connection")
	}
}
