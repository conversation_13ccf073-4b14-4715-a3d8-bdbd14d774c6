// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentChild(db *gorm.DB, opts ...gen.DOOption) xAgentChild {
	_xAgentChild := xAgentChild{}

	_xAgentChild.xAgentChildDo.UseDB(db, opts...)
	_xAgentChild.xAgentChildDo.UseModel(&model.XAgentChild{})

	tableName := _xAgentChild.xAgentChildDo.TableName()
	_xAgentChild.ALL = field.NewAsterisk(tableName)
	_xAgentChild.ID = field.NewInt32(tableName, "Id")
	_xAgentChild.UserID = field.NewInt32(tableName, "UserId")
	_xAgentChild.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentChild.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentChild.Child = field.NewInt32(tableName, "Child")
	_xAgentChild.ChildLevel = field.NewInt32(tableName, "ChildLevel")
	_xAgentChild.DataState = field.NewInt32(tableName, "DataState")
	_xAgentChild.CreateTime = field.NewTime(tableName, "CreateTime")

	_xAgentChild.fillFieldMap()

	return _xAgentChild
}

type xAgentChild struct {
	xAgentChildDo xAgentChildDo

	ALL        field.Asterisk
	ID         field.Int32
	UserID     field.Int32 // 代理id
	SellerID   field.Int32
	ChannelID  field.Int32
	Child      field.Int32 // 下级id
	ChildLevel field.Int32 // 下级层级,0直属下级,数值越大代理层级越深
	DataState  field.Int32 // 数据统计状态
	CreateTime field.Time  // 关系生成时间

	fieldMap map[string]field.Expr
}

func (x xAgentChild) Table(newTableName string) *xAgentChild {
	x.xAgentChildDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentChild) As(alias string) *xAgentChild {
	x.xAgentChildDo.DO = *(x.xAgentChildDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentChild) updateTableName(table string) *xAgentChild {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Child = field.NewInt32(table, "Child")
	x.ChildLevel = field.NewInt32(table, "ChildLevel")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentChild) WithContext(ctx context.Context) *xAgentChildDo {
	return x.xAgentChildDo.WithContext(ctx)
}

func (x xAgentChild) TableName() string { return x.xAgentChildDo.TableName() }

func (x xAgentChild) Alias() string { return x.xAgentChildDo.Alias() }

func (x xAgentChild) Columns(cols ...field.Expr) gen.Columns { return x.xAgentChildDo.Columns(cols...) }

func (x *xAgentChild) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentChild) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Child"] = x.Child
	x.fieldMap["ChildLevel"] = x.ChildLevel
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xAgentChild) clone(db *gorm.DB) xAgentChild {
	x.xAgentChildDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentChild) replaceDB(db *gorm.DB) xAgentChild {
	x.xAgentChildDo.ReplaceDB(db)
	return x
}

type xAgentChildDo struct{ gen.DO }

func (x xAgentChildDo) Debug() *xAgentChildDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentChildDo) WithContext(ctx context.Context) *xAgentChildDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentChildDo) ReadDB() *xAgentChildDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentChildDo) WriteDB() *xAgentChildDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentChildDo) Session(config *gorm.Session) *xAgentChildDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentChildDo) Clauses(conds ...clause.Expression) *xAgentChildDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentChildDo) Returning(value interface{}, columns ...string) *xAgentChildDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentChildDo) Not(conds ...gen.Condition) *xAgentChildDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentChildDo) Or(conds ...gen.Condition) *xAgentChildDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentChildDo) Select(conds ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentChildDo) Where(conds ...gen.Condition) *xAgentChildDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentChildDo) Order(conds ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentChildDo) Distinct(cols ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentChildDo) Omit(cols ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentChildDo) Join(table schema.Tabler, on ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentChildDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentChildDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentChildDo) Group(cols ...field.Expr) *xAgentChildDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentChildDo) Having(conds ...gen.Condition) *xAgentChildDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentChildDo) Limit(limit int) *xAgentChildDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentChildDo) Offset(offset int) *xAgentChildDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentChildDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentChildDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentChildDo) Unscoped() *xAgentChildDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentChildDo) Create(values ...*model.XAgentChild) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentChildDo) CreateInBatches(values []*model.XAgentChild, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentChildDo) Save(values ...*model.XAgentChild) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentChildDo) First() (*model.XAgentChild, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentChild), nil
	}
}

func (x xAgentChildDo) Take() (*model.XAgentChild, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentChild), nil
	}
}

func (x xAgentChildDo) Last() (*model.XAgentChild, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentChild), nil
	}
}

func (x xAgentChildDo) Find() ([]*model.XAgentChild, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentChild), err
}

func (x xAgentChildDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentChild, err error) {
	buf := make([]*model.XAgentChild, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentChildDo) FindInBatches(result *[]*model.XAgentChild, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentChildDo) Attrs(attrs ...field.AssignExpr) *xAgentChildDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentChildDo) Assign(attrs ...field.AssignExpr) *xAgentChildDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentChildDo) Joins(fields ...field.RelationField) *xAgentChildDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentChildDo) Preload(fields ...field.RelationField) *xAgentChildDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentChildDo) FirstOrInit() (*model.XAgentChild, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentChild), nil
	}
}

func (x xAgentChildDo) FirstOrCreate() (*model.XAgentChild, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentChild), nil
	}
}

func (x xAgentChildDo) FindByPage(offset int, limit int) (result []*model.XAgentChild, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentChildDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentChildDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentChildDo) Delete(models ...*model.XAgentChild) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentChildDo) withDO(do gen.Dao) *xAgentChildDo {
	x.DO = *do.(*gen.DO)
	return x
}
