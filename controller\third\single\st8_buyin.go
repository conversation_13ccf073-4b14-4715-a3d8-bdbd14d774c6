package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	"io"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
)

// Buyin 处理非游戏相关的扣款（如活动或比赛入场费）
func (s *ST8Service) Buyin(ctx *abugo.AbuHttpContent) {
	response := ST8Response{
		Status:   ST8_STATUS_UNKNOWN,
		Balance:  "0",
		Currency: "",
	}

	// 解析请求数据
	type RequestData struct {
		TransactionId string `json:"transaction_id"` // 交易ID
		Player        string `json:"player"`         // 玩家ID
		Site          string `json:"site"`           // 站点
		Amount        string `json:"amount"`         // 金额
		Currency      string `json:"currency"`       // 货币
		GameCode      string `json:"game_code"`      // 游戏代码
		DeveloperCode string `json:"developer_code"` // 开发商代码
		ProviderKind  string `json:"provider_kind"`  // 提供商交易类型
		Provider      struct {
			TransactionId string `json:"transaction_id"`
			Amount        string `json:"amount"`
			Currency      string `json:"currency"`
			Player        string `json:"player"`
		} `json:"provider"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Refund api Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("st8 Buyin json解析失败: err=", err.Error(), ", body=", string(bodyBytes))
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	// 获取用户ID
	userId, err := strconv.Atoi(reqdata.Player)
	if err != nil {
		logs.Error("st8 Buyin 玩家ID格式错误: player=", reqdata.Player)
		response.Status = ST8_STATUS_PLAYER_NOT_FOUND
		ctx.RespJson(response)
		return
	}

	//判断是否是重复请求数据
	transactionId := reqdata.TransactionId
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, transactionId, ctx.Gin().Request.URL.String())
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 检测是否重复请求 发生错误 transactionId=", transactionId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("st8 Buyin 检测到重复请求 transactionId=", transactionId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
		}
		return
	}

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if response.Status != ST8_STATUS_OK {
			respCode = 1
		}
		base.AddRequestDB(transactionId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	logs.Info("st8 Buyin 收到请求: player=%s, transactionId=%s, amount=%s, currency=%s, developerCode=%s",
		reqdata.Player, reqdata.TransactionId, reqdata.Amount, reqdata.Currency, reqdata.DeveloperCode)

	// 检查必要参数
	if reqdata.Player == "" || reqdata.TransactionId == "" || reqdata.Amount == "" || reqdata.Currency == "" || reqdata.DeveloperCode == "" {
		logs.Error("st8 Buyin 参数错误: player=%s, transactionId=%s, amount=%s, currency=%s, developerCode=%s",
			reqdata.Player, reqdata.TransactionId, reqdata.Amount, reqdata.Currency, reqdata.DeveloperCode)
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 检查站点
	if reqdata.Site != s.SiteID {
		logs.Error("st8 Buyin 站点错误: site=%s", reqdata.Site)
		response.Status = ST8_STATUS_SITE_DISABLED
		ctx.RespJson(response)
		return
	}

	// 获取品牌名称
	//providerCode := s.getProviderCode(reqdata.DeveloperCode)
	//if providerCode == "" {
	//	logs.Error("st8 Buyin 开发商代码错误: developerCode=", reqdata.DeveloperCode)
	//	response.Status = ST8_STATUS_UNKNOWN
	//	ctx.RespJson(response)
	//	return
	//}

	brandName := s.brandName + reqdata.DeveloperCode

	// 检查游戏状态
	//if !s.checkGameState(brandName) {
	//	logs.Error("st8 Buyin 游戏已禁用: brand=", brandName)
	//	response.Status = ST8_STATUS_GAME_DISABLED
	//	ctx.RespJson(response)
	//	return
	//}

	// 加锁

	// 开启事务
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("st8 Buyin 开启事务失败: err=", tx.Error.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}
	defer tx.Rollback()

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	e := tx.Table("x_user").
		Where("UserId = ?", userId).
		First(&userBalance).Error
	if e != nil {
		logs.Error("st8 Buyin 查询用户余额失败: err=", e.Error())
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			response.Status = ST8_STATUS_PLAYER_NOT_FOUND
		} else {
			response.Status = ST8_STATUS_UNKNOWN
		}
		ctx.RespJson(response)
		return
	}

	// 检查用户状态
	if userBalance.State != 1 {
		logs.Error("st8 Buyin 用户已锁定: userId=", userId)
		response.Status = ST8_STATUS_PLAYER_LOCKED
		ctx.RespJson(response)
		return
	}

	// 解析金额
	amount, err := strconv.ParseFloat(reqdata.Amount, 64)
	if err != nil || amount <= 0 {
		logs.Error("st8 Buyin 金额格式错误: amount=", reqdata.Amount)
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}
	amount = math.Abs(amount) // 确保为正数
	betAmount := amount

	// 检查余额 支持负余额 不需要检查余额
	if userBalance.Amount < betAmount {
		logs.Error("st8 Buyin 余额不足,产生负余额: userAmount=", userBalance.Amount, ", betAmount=", betAmount, ", transactionId=", reqdata.TransactionId,
			", userId=", reqdata.Player)

		//response.Status = ST8_STATUS_NOT_ENOUGH_MONEY
		//ctx.RespJson(response)
		//return
	}

	// 更新用户余额
	// 移除了 amount >= ? 的条件检查，允许余额变为负数
	afterAmount := userBalance.Amount - betAmount
	if betAmount != 0 {
		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).
			Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount-?", betAmount),
			})
		if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
			logs.Error("st8 Buyin 更新用户余额失败: err=", resultTmp.Error)
			response.Status = ST8_STATUS_NOT_ENOUGH_MONEY
			ctx.RespJson(response)
			return
		}
	}

	// 获取账变类型
	goldType := s.getSt8GoldChangeType(K_ST8CHANGETYPEBuyin, brandName)
	//根据交易类型代码获取交易类型说明
	var memoType = s.getTransactionType(reqdata.ProviderKind)
	// 记录账变
	thirdTime := time.Now().In(tzUTC8St8).Format("2006-01-02 15:04:05")
	amountLog := AmountChangeLogSt8{
		UserId:       userId,
		BeforeAmount: userBalance.Amount,
		Amount:       -betAmount,
		AfterAmount:  afterAmount,
		Reason:       goldType,
		Memo:         brandName + " " + memoType + " buyin,tranId:" + reqdata.TransactionId,
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   thirdTime,
	}

	e = tx.Table("x_amount_change_log").Create(&amountLog).Error
	if e != nil {
		logs.Error("st8 Buyin 插入账变记录失败: err=", e.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 记录三方账变
	memo := amountLog.Memo
	e = s.AddThirdAmountLog(tx, -betAmount, ST8_AMOUNT_DEBIT, userId, reqdata.TransactionId, reqdata.TransactionId, memo)
	if e != nil {
		logs.Error("st8 Buyin 插入三方账变记录失败: err=", e.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logs.Error("st8 Buyin 提交事务失败: err=", err.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	logs.Info("st8 Buyin 处理成功: userId=%d, transactionId=%s, amount=%.2f, beforeAmount=%.2f, afterAmount=%.2f",
		userId, reqdata.TransactionId, betAmount, userBalance.Amount, afterAmount)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if s.RefreshUserAmountFunc != nil {
			tmpErr := s.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知错误: userId=", notifyUserId, " err=", tmpErr)
			}
		} else {
			logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	// 返回成功响应
	response.Status = ST8_STATUS_OK
	response.Balance = strconv.FormatFloat(afterAmount, 'f', 2, 64)
	response.Currency = reqdata.Currency
	ctx.RespJson(response)

	//// 异步解锁
	//go func(userId int) {
	//	time.Sleep(time.Millisecond * 200)
	//	key := fmt.Sprintf("lock:st8:user:%d", userId)
	//	utils.RedisUnLock(key)
	//}(userId)
}

// Payout 处理非游戏相关的加款（如活动或比赛奖励）
func (s *ST8Service) Payout(ctx *abugo.AbuHttpContent) {
	response := ST8Response{
		Status:   ST8_STATUS_UNKNOWN,
		Balance:  "0",
		Currency: "",
	}

	// 解析请求数据
	type RequestData struct {
		TransactionId string `json:"transaction_id"` // 交易ID
		Player        string `json:"player"`         // 玩家ID
		Site          string `json:"site"`           // 站点
		Amount        string `json:"amount"`         // 金额
		Currency      string `json:"currency"`       // 货币
		GameCode      string `json:"game_code"`      // 游戏代码
		DeveloperCode string `json:"developer_code"` // 开发商代码
		ProviderKind  string `json:"provider_kind"`  // 提供商交易类型
		Provider      struct {
			TransactionId string `json:"transaction_id"`
			Amount        string `json:"amount"`
			Currency      string `json:"currency"`
			Player        string `json:"player"`
		} `json:"provider"`
		Bonus struct {
			InstanceId string `json:"instance_id"`
			Status     string `json:"status"`
			BonusId    string `json:"bonus_id"`
		} `json:"bonus"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Payout api Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("st8 Payout 解析请求数据失败: err=", err.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	// 获取用户ID
	userId, err := strconv.Atoi(reqdata.Player)
	if err != nil {
		logs.Error("st8 Payout 玩家ID格式错误: player=", reqdata.Player)
		response.Status = ST8_STATUS_PLAYER_NOT_FOUND
		ctx.RespJson(response)
		return
	}

	//判断是否是重复请求数据
	transactionId := reqdata.TransactionId
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, transactionId, ctx.Gin().Request.URL.String())
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 检测是否重复请求 发生错误 transactionId=", transactionId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("st8 Payout 检测到重复请求 transactionId=", transactionId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
		}
		return
	}

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if response.Status != ST8_STATUS_OK {
			respCode = 1
		}
		base.AddRequestDB(transactionId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	logs.Info("st8 Payout 收到请求: player=%s, transactionId=%s, amount=%s, currency=%s, developerCode=%s",
		reqdata.Player, reqdata.TransactionId, reqdata.Amount, reqdata.Currency, reqdata.DeveloperCode)

	// 检查必要参数
	if reqdata.TransactionId == "" || reqdata.Player == "" || reqdata.Site == "" ||
		reqdata.Amount == "" || reqdata.Currency == "" || reqdata.DeveloperCode == "" {
		logs.Error("st8 Payout 缺少必要参数")
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 检查站点ID
	if reqdata.Site != s.SiteID {
		logs.Error("st8 Payout 站点ID错误: site=", reqdata.Site)
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 获取品牌名称
	brandName := s.brandName + reqdata.DeveloperCode

	// 开启事务
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("st8 Payout 开启事务失败: err=", tx.Error.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}
	defer tx.Rollback()

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	e := tx.Table("x_user").
		Where("UserId = ?", userId).
		First(&userBalance).Error
	if e != nil {
		logs.Error("st8 Payout 查询用户余额失败: err=", e.Error())
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			response.Status = ST8_STATUS_PLAYER_NOT_FOUND
		} else {
			response.Status = ST8_STATUS_UNKNOWN
		}
		ctx.RespJson(response)
		return
	}

	// 检查用户状态
	if userBalance.State != 1 {
		logs.Error("st8 Payout 用户已锁定: userId=", userId)
		response.Status = ST8_STATUS_PLAYER_LOCKED
		ctx.RespJson(response)
		return
	}

	// 解析金额
	amount, err := strconv.ParseFloat(reqdata.Amount, 64)
	if err != nil || amount <= 0 {
		logs.Error("st8 Payout 金额格式错误: amount=", reqdata.Amount)
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}
	amount = math.Abs(amount) // 确保为正数
	payoutAmount := amount

	// 更新用户余额
	afterAmount := userBalance.Amount + payoutAmount
	resultTmp := tx.Table("x_user").Where("UserId = ?", userId).
		Updates(map[string]interface{}{
			"amount": daogorm.Expr("amount+?", payoutAmount),
		})
	if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
		logs.Error("st8 Payout 更新用户余额失败: err=", resultTmp.Error)
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 获取账变类型
	goldType := s.getSt8GoldChangeType(K_ST8CHANGETYPEPayout, brandName)
	//根据交易类型代码获取交易类型说明
	var memoType = s.getTransactionType(reqdata.ProviderKind)
	// 记录账变
	thirdTime := time.Now().In(tzUTC8St8).Format("2006-01-02 15:04:05")
	amountLog := AmountChangeLogSt8{
		UserId:       userId,
		BeforeAmount: userBalance.Amount,
		Amount:       payoutAmount,
		AfterAmount:  afterAmount,
		Reason:       goldType,
		Memo:         brandName + " " + memoType + " payout,tranId:" + reqdata.TransactionId,
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		CreateTime:   thirdTime,
	}

	e = tx.Table("x_amount_change_log").Create(&amountLog).Error
	if e != nil {
		logs.Error("st8 Payout 插入账变记录失败: err=", e.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 记录三方账变
	memo := amountLog.Memo
	e = s.AddThirdAmountLog(tx, payoutAmount, ST8_AMOUNT_PAYOUT, userId, reqdata.TransactionId, reqdata.TransactionId, memo)
	if e != nil {
		logs.Error("st8 Payout 插入三方账变记录失败: err=", e.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logs.Error("st8 Payout 提交事务失败: err=", err.Error())
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		return
	}

	logs.Info("st8 Payout 处理成功: userId=%d, transactionId=%s, amount=%.2f, beforeAmount=%.2f, afterAmount=%.2f",
		userId, reqdata.TransactionId, payoutAmount, userBalance.Amount, afterAmount)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if s.RefreshUserAmountFunc != nil {
			tmpErr := s.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知错误: userId=", notifyUserId, " err=", tmpErr)
			}
		} else {
			logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	// 返回成功响应
	response.Status = ST8_STATUS_OK
	response.Balance = strconv.FormatFloat(afterAmount, 'f', 2, 64)
	response.Currency = reqdata.Currency
	ctx.RespJson(response)
	return

}

// PlayerProfile 获取玩家信息
func (s *ST8Service) PlayerProfile(ctx *abugo.AbuHttpContent) {
	// 请求结构体
	type RequestData struct {
		Player string `json:"player"` // 运营商平台中的唯一玩家ID(区分大小写)
		Site   string `json:"site"`   // 运营商站点标识
	}

	type Attributes struct {
		Labels []string `json:"labels"` // 玩家标签列表
	}

	// 响应结构体
	type PlayerProfileResponse struct {
		Status          string      `json:"status"`           // 操作状态
		Id              string      `json:"id"`               // 运营商平台中的唯一玩家ID(区分大小写)
		Jurisdiction    string      `json:"jurisdiction"`     // 管辖区域代码
		DefaultCurrency string      `json:"default_currency"` // 默认货币代码,必须符合ISO 4217标准
		RegCountry      string      `json:"reg_country"`      // 玩家注册国家,必须符合ISO 3166-1 Alpha-2标准
		Affiliate       string      `json:"affiliate"`        // 代理ID
		BetLimits       string      `json:"bet_limits"`       // 投注限额级别: low/medium/high
		BirthDate       string      `json:"birth_date"`       // 出生日期
		RegDate         string      `json:"reg_date"`         // 注册日期
		Attributes      *Attributes `json:"attributes"`       // 玩家元数据
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 PlayerProfile api Body=", string(bodyBytes))

	// 解析请求数据
	var reqdata RequestData
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		response := PlayerProfileResponse{
			Status: ST8_STATUS_UNKNOWN,
		}
		logs.Error("st8 Bet JSON解析失败: err=", err)
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response := PlayerProfileResponse{
			Status: ST8_STATUS_AUTH_FAILED,
		}
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response := PlayerProfileResponse{
			Status: ST8_STATUS_AUTH_FAILED,
		}
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response := PlayerProfileResponse{
			Status: ST8_STATUS_AUTH_FAILED,
		}
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	// 获取玩家信息
	userId, _ := strconv.Atoi(reqdata.Player)
	user, _, err := base.GetUserById(userId)
	if err != nil || user == nil {
		response := PlayerProfileResponse{
			Status: ST8_STATUS_PLAYER_NOT_FOUND,
		}
		ctx.RespJson(response)
		logs.Error("st8 PlayerProfile 玩家不存在: userId=", userId)
		return
	}

	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			response := PlayerProfileResponse{
				Status: ST8_STATUS_PLAYER_NOT_FOUND,
			}
			ctx.RespJson(response)
			logs.Error("st8 PlayerProfile 玩家不存在: userId=", userId)
			return
		}
		response := PlayerProfileResponse{
			Status: ST8_STATUS_UNKNOWN,
		}
		ctx.RespJson(response)
		logs.Error("st8 PlayerProfile 查询玩家信息失败: err=", err)
		return
	}

	// 构建响应
	response := PlayerProfileResponse{
		Status:          ST8_STATUS_OK,
		Id:              reqdata.Player,
		Jurisdiction:    "CW",       // 默认使用Curacao管辖区
		DefaultCurrency: s.Currency, // 使用系统配置的默认货币
		RegCountry:      "AU",       //abugo.GetStringFromInterface(userInfo["Country"]), // 注册国家
		Affiliate:       "",         // 代理ID
		BetLimits:       "low",      // 默认使用低额度限制
		BirthDate:       "",         // 出生日期
		RegDate:         "",         // 注册时间
		Attributes: &Attributes{
			Labels: []string{"default_player"}, // 默认标签
		},
	}

	ctx.RespJson(response)
}

// Check 验证玩家和会话信息
func (s *ST8Service) Check(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token    string `json:"token"`     // 游戏启动时提供的玩家token
		GameCode string `json:"game_code"` // 正在启动的游戏代码
	}

	// SiteInfo ST8站点信息结构体
	type SiteInfo struct {
		Id      string `json:"id"`      // 站点ID
		Lobby   string `json:"lobby"`   // 游戏大厅URL
		Deposit string `json:"deposit"` // 充值页面URL
	}

	// CheckResponse ST8 Check方法的响应结构体
	type CheckResponse struct {
		Status   string   `json:"status"`    // 操作状态
		Token    string   `json:"token"`     // 玩家游戏会话令牌,最大长度255字符
		Currency string   `json:"currency"`  // 玩家当前钱包的货币代码,必须符合ISO 4217标准
		GameCode string   `json:"game_code"` // ST8系统中的游戏唯一标识符
		Country  string   `json:"country"`   // 玩家所在国家的ISO 3166-1 Alpha-2代码,可为空
		Player   string   `json:"player"`    // 运营商平台中的唯一玩家ID(区分大小写)
		Lang     string   `json:"lang"`      // 游戏启动使用的语言代码,必须小写且符合ISO 639-3标准
		Site     SiteInfo `json:"site"`      // 站点信息
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Check api Body=", string(bodyBytes))

	// 解析请求数据
	var reqdata RequestData
	response := CheckResponse{
		Status: "unknown",
	}
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		logs.Error("st8 Bet JSON解析失败: err=", err)
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	// 查询游戏列表
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", fmt.Sprintf("%s%%", s.brandName), reqdata.GameCode).First(&gameList).Error
	if err != nil {
		response := CheckResponse{
			Status: ST8_STATUS_GAME_DISABLED,
		}
		ctx.RespJson(response)
		logs.Error("st8 check 游戏ID获取失败 GameId=", reqdata.GameCode, " token=", reqdata.Token)
		return
	}

	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("st8 check 游戏不可用  gamecode=", reqdata.GameCode, " gameList=", gameList)
		response := CheckResponse{
			Status: ST8_STATUS_GAME_DISABLED,
		}
		ctx.RespJson(response)
		return
	}

	providerCode := gameList.Brand
	providerCode = strings.TrimPrefix(providerCode, s.brandName)
	// 获取区域配置
	var publicKey string
	_, _, _, publicKey, _, _ = s.getRegionConfigByProviderCode(providerCode)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	userId := s.token2UserId(reqdata.Token)
	// 从缓存或数据库中获取玩家信息
	if userId == -1 {
		response := CheckResponse{
			Status: ST8_STATUS_PLAYER_NOT_FOUND,
		}
		ctx.RespJson(response)
		logs.Error("st8 Check 获取玩家信息失败: err=", err)
		return
	}

	// 构建响应
	response = CheckResponse{
		Status:   ST8_STATUS_OK,
		Token:    reqdata.Token,
		Currency: s.Currency,
		GameCode: reqdata.GameCode,
		Country:  "",
		Player:   strconv.Itoa(userId),
		Lang:     s.Language,
		Site: SiteInfo{
			Id:      s.SiteID,
			Lobby:   s.Lobby,
			Deposit: s.Deposit,
		},
	}

	ctx.RespJson(response)
}

//debit: 常规游戏投注，从玩家余额扣款
//credit: 常规游戏派彩，向玩家余额加款
//
//jackpot_credit: 彩金派发
//可以在玩家不在游戏中时处理
//可以不关联任何游戏注单
//promo_credit: 促销活动奖励
//可用于运营商或供应商组织的锦标赛
//可以在玩家不在游戏时处理
//不需要关联游戏注单
//
//free_debit: 使用奖金的扣款
//通常金额为0
//如果运营商有"奖金余额"概念，应从奖金余额扣除
//需要包含奖金ID引用
//free_credit: 使用奖金的派彩
//如果有奖金余额，应该计入奖金余额
//可能没有对应的free_debit
//需要包含奖金ID引用
//
//correction_credit: 修正派彩
//用于体育内容提供商因赔率/投注额变化的修正
//也用于比赛结果重新结算
//correction_debit: 修正扣款
//重要特性：
//即使玩家余额不足也必须接受
//可以将余额降至负数或设为0
//必须响应"ok"
//可能为之前派彩的全额修正
//奖励游戏购买:
//
//bonus_buy_debit: 购买游戏内奖励功能的扣款
//可能超过游戏最大投注限制
//bonus_buy_credit: 购买的奖励游戏的派彩

// GetTransactionType 根据交易类型代码获取交易类型说明
func (s *ST8Service) getTransactionType(code string) string {
	transactions := map[string]string{
		"debit":             "投注",         // 从玩家余额扣款
		"credit":            "派彩",         // 向玩家余额加款
		"jackpot_credit":    "彩金派发",       // 可在玩家不在游戏时处理
		"promo_credit":      "促销活动奖励",     // 用于运营商或供应商的锦标赛活动
		"free_debit":        "使用奖金的扣款",    // 通常金额为0
		"free_credit":       "使用奖金的派彩",    // 可能没有对应的free_debit
		"correction_credit": "修正派彩",       // 用于体育赔率/投注额变化的修正
		"correction_debit":  "修正扣款",       // 即使余额不足也必须接受
		"bonus_buy_debit":   "购买游戏内奖励扣款",  // 可能超过游戏最大投注限制
		"bonus_buy_credit":  "购买的奖励游戏的派彩", // 奖励游戏的派彩
	}

	if name, exists := transactions[code]; exists {
		return name
	}
	return code // 如果找不到对应的交易类型说明，返回原始代码
}

// GetTransactionFeature 根据交易类型获取特殊处理说明
func (s *ST8Service) getTransactionFeature(code string) string {
	features := map[string]string{
		"debit": `
            - 常规游戏投注操作
            - 在特定游戏回合和活动游戏会话范围内处理
            - 需要检查玩家余额是否足够
        `,
		"credit": `
            - 常规游戏赢钱操作
            - 在特定游戏回合和活动游戏会话范围内处理
            - 增加玩家余额
        `,
		"jackpot_credit": `
            - 可在玩家不在游戏时处理
            - 可以不关联任何回合
            - 可以引用已关闭的回合
        `,
		"promo_credit": `
            - 可在玩家不在游戏时处理
            - 不会引用任何回合
            - 用于锦标赛等促销活动
        `,
		"free_debit": `
            - 通常金额为0，不实际扣款
            - 如有奖金余额概念，应从奖金余额扣除
            - 在活动游戏会话和回合范围内处理
            - 需包含奖金ID引用
        `,
		"free_credit": `
            - 如有奖金余额概念，应计入奖金余额
            - 在活动游戏会话和回合范围内处理
            - 可能没有对应的free_debit
            - 需包含奖金ID引用
        `,
		"correction_credit": `
            - 用于体育内容提供商的赔率变化
            - 用于投注金额变化
            - 用于比赛结果重新结算
        `,
		"correction_debit": `
            - 即使余额不足也必须接受
            - 可以将余额降至负数或设为0
            - 必须返回成功响应
            - 可能为之前派彩的全额修正
        `,
		"bonus_buy_debit": `
            - 购买游戏内奖励功能的扣款
            - 可能超过游戏最大投注限制
            - 需要检查玩家余额
        `,
		"bonus_buy_credit": `
            - 购买的奖励游戏的派彩
            - 与bonus_buy_debit相关联
            - 增加玩家余额
        `,
	}

	if feature, exists := features[code]; exists {
		return feature
	}
	return "未知交易类型" // 如果找不到对应的特殊处理说明，返回未知
}
