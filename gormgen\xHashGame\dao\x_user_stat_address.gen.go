// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserStatAddress(db *gorm.DB, opts ...gen.DOOption) xUserStatAddress {
	_xUserStatAddress := xUserStatAddress{}

	_xUserStatAddress.xUserStatAddressDo.UseDB(db, opts...)
	_xUserStatAddress.xUserStatAddressDo.UseModel(&model.XUserStatAddress{})

	tableName := _xUserStatAddress.xUserStatAddressDo.TableName()
	_xUserStatAddress.ALL = field.NewAsterisk(tableName)
	_xUserStatAddress.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserStatAddress.FromAddressCrc = field.NewInt64(tableName, "FromAddressCrc")
	_xUserStatAddress.FromAddress = field.NewString(tableName, "FromAddress")
	_xUserStatAddress.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserStatAddress.UseCount = field.NewInt32(tableName, "UseCount")
	_xUserStatAddress.Memo = field.NewString(tableName, "Memo")
	_xUserStatAddress.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserStatAddress.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserStatAddress.fillFieldMap()

	return _xUserStatAddress
}

// xUserStatAddress 渠道用户转账地址
type xUserStatAddress struct {
	xUserStatAddressDo xUserStatAddressDo

	ALL            field.Asterisk
	ChannelID      field.Int32  // 渠道id
	FromAddressCrc field.Int64  // 玩家地址crc
	FromAddress    field.String // 玩家地址
	SellerID       field.Int32  // 运营商id
	UseCount       field.Int32  // 使用次数
	Memo           field.String // 备注
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserStatAddress) Table(newTableName string) *xUserStatAddress {
	x.xUserStatAddressDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserStatAddress) As(alias string) *xUserStatAddress {
	x.xUserStatAddressDo.DO = *(x.xUserStatAddressDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserStatAddress) updateTableName(table string) *xUserStatAddress {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.FromAddressCrc = field.NewInt64(table, "FromAddressCrc")
	x.FromAddress = field.NewString(table, "FromAddress")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.UseCount = field.NewInt32(table, "UseCount")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserStatAddress) WithContext(ctx context.Context) *xUserStatAddressDo {
	return x.xUserStatAddressDo.WithContext(ctx)
}

func (x xUserStatAddress) TableName() string { return x.xUserStatAddressDo.TableName() }

func (x xUserStatAddress) Alias() string { return x.xUserStatAddressDo.Alias() }

func (x xUserStatAddress) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserStatAddressDo.Columns(cols...)
}

func (x *xUserStatAddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserStatAddress) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["FromAddressCrc"] = x.FromAddressCrc
	x.fieldMap["FromAddress"] = x.FromAddress
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["UseCount"] = x.UseCount
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserStatAddress) clone(db *gorm.DB) xUserStatAddress {
	x.xUserStatAddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserStatAddress) replaceDB(db *gorm.DB) xUserStatAddress {
	x.xUserStatAddressDo.ReplaceDB(db)
	return x
}

type xUserStatAddressDo struct{ gen.DO }

func (x xUserStatAddressDo) Debug() *xUserStatAddressDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserStatAddressDo) WithContext(ctx context.Context) *xUserStatAddressDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserStatAddressDo) ReadDB() *xUserStatAddressDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserStatAddressDo) WriteDB() *xUserStatAddressDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserStatAddressDo) Session(config *gorm.Session) *xUserStatAddressDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserStatAddressDo) Clauses(conds ...clause.Expression) *xUserStatAddressDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserStatAddressDo) Returning(value interface{}, columns ...string) *xUserStatAddressDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserStatAddressDo) Not(conds ...gen.Condition) *xUserStatAddressDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserStatAddressDo) Or(conds ...gen.Condition) *xUserStatAddressDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserStatAddressDo) Select(conds ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserStatAddressDo) Where(conds ...gen.Condition) *xUserStatAddressDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserStatAddressDo) Order(conds ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserStatAddressDo) Distinct(cols ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserStatAddressDo) Omit(cols ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserStatAddressDo) Join(table schema.Tabler, on ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserStatAddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserStatAddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserStatAddressDo) Group(cols ...field.Expr) *xUserStatAddressDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserStatAddressDo) Having(conds ...gen.Condition) *xUserStatAddressDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserStatAddressDo) Limit(limit int) *xUserStatAddressDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserStatAddressDo) Offset(offset int) *xUserStatAddressDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserStatAddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserStatAddressDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserStatAddressDo) Unscoped() *xUserStatAddressDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserStatAddressDo) Create(values ...*model.XUserStatAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserStatAddressDo) CreateInBatches(values []*model.XUserStatAddress, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserStatAddressDo) Save(values ...*model.XUserStatAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserStatAddressDo) First() (*model.XUserStatAddress, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserStatAddress), nil
	}
}

func (x xUserStatAddressDo) Take() (*model.XUserStatAddress, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserStatAddress), nil
	}
}

func (x xUserStatAddressDo) Last() (*model.XUserStatAddress, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserStatAddress), nil
	}
}

func (x xUserStatAddressDo) Find() ([]*model.XUserStatAddress, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserStatAddress), err
}

func (x xUserStatAddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserStatAddress, err error) {
	buf := make([]*model.XUserStatAddress, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserStatAddressDo) FindInBatches(result *[]*model.XUserStatAddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserStatAddressDo) Attrs(attrs ...field.AssignExpr) *xUserStatAddressDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserStatAddressDo) Assign(attrs ...field.AssignExpr) *xUserStatAddressDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserStatAddressDo) Joins(fields ...field.RelationField) *xUserStatAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserStatAddressDo) Preload(fields ...field.RelationField) *xUserStatAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserStatAddressDo) FirstOrInit() (*model.XUserStatAddress, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserStatAddress), nil
	}
}

func (x xUserStatAddressDo) FirstOrCreate() (*model.XUserStatAddress, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserStatAddress), nil
	}
}

func (x xUserStatAddressDo) FindByPage(offset int, limit int) (result []*model.XUserStatAddress, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserStatAddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserStatAddressDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserStatAddressDo) Delete(models ...*model.XUserStatAddress) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserStatAddressDo) withDO(do gen.Dao) *xUserStatAddressDo {
	x.DO = *do.(*gen.DO)
	return x
}
