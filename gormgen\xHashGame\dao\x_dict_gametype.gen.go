// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXDictGametype(db *gorm.DB, opts ...gen.DOOption) xDictGametype {
	_xDictGametype := xDictGametype{}

	_xDictGametype.xDictGametypeDo.UseDB(db, opts...)
	_xDictGametype.xDictGametypeDo.UseModel(&model.XDictGametype{})

	tableName := _xDictGametype.xDictGametypeDo.TableName()
	_xDictGametype.ALL = field.NewAsterisk(tableName)
	_xDictGametype.GameType = field.NewInt32(tableName, "GameType")
	_xDictGametype.GameTypeName = field.NewString(tableName, "GameTypeName")
	_xDictGametype.ParentType = field.NewInt32(tableName, "ParentType")
	_xDictGametype.ParentTypeName = field.NewString(tableName, "ParentTypeName")
	_xDictGametype.Memo = field.NewString(tableName, "Memo")
	_xDictGametype.Status = field.NewInt32(tableName, "Status")
	_xDictGametype.CreateTime = field.NewTime(tableName, "CreateTime")
	_xDictGametype.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xDictGametype.fillFieldMap()

	return _xDictGametype
}

// xDictGametype 游戏场馆分类
type xDictGametype struct {
	xDictGametypeDo xDictGametypeDo

	ALL            field.Asterisk
	GameType       field.Int32  // 游戏分类
	GameTypeName   field.String // 游戏分类名
	ParentType     field.Int32  // 上级分类
	ParentTypeName field.String // 上级分类名
	Memo           field.String // 描述
	Status         field.Int32  // 1有效 2无效
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xDictGametype) Table(newTableName string) *xDictGametype {
	x.xDictGametypeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xDictGametype) As(alias string) *xDictGametype {
	x.xDictGametypeDo.DO = *(x.xDictGametypeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xDictGametype) updateTableName(table string) *xDictGametype {
	x.ALL = field.NewAsterisk(table)
	x.GameType = field.NewInt32(table, "GameType")
	x.GameTypeName = field.NewString(table, "GameTypeName")
	x.ParentType = field.NewInt32(table, "ParentType")
	x.ParentTypeName = field.NewString(table, "ParentTypeName")
	x.Memo = field.NewString(table, "Memo")
	x.Status = field.NewInt32(table, "Status")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xDictGametype) WithContext(ctx context.Context) *xDictGametypeDo {
	return x.xDictGametypeDo.WithContext(ctx)
}

func (x xDictGametype) TableName() string { return x.xDictGametypeDo.TableName() }

func (x xDictGametype) Alias() string { return x.xDictGametypeDo.Alias() }

func (x xDictGametype) Columns(cols ...field.Expr) gen.Columns {
	return x.xDictGametypeDo.Columns(cols...)
}

func (x *xDictGametype) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xDictGametype) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["GameTypeName"] = x.GameTypeName
	x.fieldMap["ParentType"] = x.ParentType
	x.fieldMap["ParentTypeName"] = x.ParentTypeName
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xDictGametype) clone(db *gorm.DB) xDictGametype {
	x.xDictGametypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xDictGametype) replaceDB(db *gorm.DB) xDictGametype {
	x.xDictGametypeDo.ReplaceDB(db)
	return x
}

type xDictGametypeDo struct{ gen.DO }

func (x xDictGametypeDo) Debug() *xDictGametypeDo {
	return x.withDO(x.DO.Debug())
}

func (x xDictGametypeDo) WithContext(ctx context.Context) *xDictGametypeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xDictGametypeDo) ReadDB() *xDictGametypeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xDictGametypeDo) WriteDB() *xDictGametypeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xDictGametypeDo) Session(config *gorm.Session) *xDictGametypeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xDictGametypeDo) Clauses(conds ...clause.Expression) *xDictGametypeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xDictGametypeDo) Returning(value interface{}, columns ...string) *xDictGametypeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xDictGametypeDo) Not(conds ...gen.Condition) *xDictGametypeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xDictGametypeDo) Or(conds ...gen.Condition) *xDictGametypeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xDictGametypeDo) Select(conds ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xDictGametypeDo) Where(conds ...gen.Condition) *xDictGametypeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xDictGametypeDo) Order(conds ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xDictGametypeDo) Distinct(cols ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xDictGametypeDo) Omit(cols ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xDictGametypeDo) Join(table schema.Tabler, on ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xDictGametypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xDictGametypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xDictGametypeDo) Group(cols ...field.Expr) *xDictGametypeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xDictGametypeDo) Having(conds ...gen.Condition) *xDictGametypeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xDictGametypeDo) Limit(limit int) *xDictGametypeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xDictGametypeDo) Offset(offset int) *xDictGametypeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xDictGametypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xDictGametypeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xDictGametypeDo) Unscoped() *xDictGametypeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xDictGametypeDo) Create(values ...*model.XDictGametype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xDictGametypeDo) CreateInBatches(values []*model.XDictGametype, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xDictGametypeDo) Save(values ...*model.XDictGametype) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xDictGametypeDo) First() (*model.XDictGametype, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictGametype), nil
	}
}

func (x xDictGametypeDo) Take() (*model.XDictGametype, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictGametype), nil
	}
}

func (x xDictGametypeDo) Last() (*model.XDictGametype, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictGametype), nil
	}
}

func (x xDictGametypeDo) Find() ([]*model.XDictGametype, error) {
	result, err := x.DO.Find()
	return result.([]*model.XDictGametype), err
}

func (x xDictGametypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XDictGametype, err error) {
	buf := make([]*model.XDictGametype, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xDictGametypeDo) FindInBatches(result *[]*model.XDictGametype, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xDictGametypeDo) Attrs(attrs ...field.AssignExpr) *xDictGametypeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xDictGametypeDo) Assign(attrs ...field.AssignExpr) *xDictGametypeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xDictGametypeDo) Joins(fields ...field.RelationField) *xDictGametypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xDictGametypeDo) Preload(fields ...field.RelationField) *xDictGametypeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xDictGametypeDo) FirstOrInit() (*model.XDictGametype, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictGametype), nil
	}
}

func (x xDictGametypeDo) FirstOrCreate() (*model.XDictGametype, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XDictGametype), nil
	}
}

func (x xDictGametypeDo) FindByPage(offset int, limit int) (result []*model.XDictGametype, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xDictGametypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xDictGametypeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xDictGametypeDo) Delete(models ...*model.XDictGametype) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xDictGametypeDo) withDO(do gen.Dao) *xDictGametypeDo {
	x.DO = *do.(*gen.DO)
	return x
}
