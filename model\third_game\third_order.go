package third_game

// x_third_dianzhi x_third_dianzhi_pre_order
// x_third_qipai x_third_qipai_pre_order
// x_third_quwei x_third_quwei_pre_order
// x_third_lottery x_third_lottery_pre_order
// x_third_live x_third_live_pre_order
// x_third_sport x_third_sport_pre_order
type ThirdOrder struct {
	Id           int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `json:"BetChannelId" gorm:"column:BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	ThirdRefId   *string `json:"ThirdRefId" gorm:"column:ThirdRefId"` //三方备用号
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"` //投注金额
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"` //派奖金额
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`   //有效流水
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`

	// 新增混合投注字段
	BetType int `json:"BetType" gorm:"column:BetType"` // 下注类型：1=纯真金 2=混合下注 3=彩金
	//RealBetAmount  float64 `json:"RealBetAmount" gorm:"column:RealBetAmount"`   // 真金下注金额
	BonusBetAmount float64 `json:"BonusBetAmount" gorm:"column:BonusBetAmount"` // Bonus币下注金额
	//RealWinAmount  float64 `json:"RealWinAmount" gorm:"column:RealWinAmount"`   // 真金派彩金额
	BonusWinAmount float64 `json:"BonusWinAmount" gorm:"column:BonusWinAmount"` // Bonus币派彩金额

	//RealProfit  float64 `json:"RealProfit" gorm:"column:RealProfit"`   // 平台真金输赢
	//BonusProfit float64 `json:"BonusProfit" gorm:"column:BonusProfit"` // 平台Bonus币输赢
	//TotalProfit float64 `json:"TotalProfit" gorm:"column:TotalProfit"` // 平台合计输赢

}

// x_third_sport x_third_sport_pre_order 增加重新结算字段
type ThirdSportOrder struct {
	Id             int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	SellerId       int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId      int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId   int     `json:"BetChannelId" gorm:"column:BetChannelId"` // 下注渠道Id
	UserId         int     `json:"UserId" gorm:"column:UserId"`
	Brand          string  `json:"Brand" gorm:"column:Brand"`
	ThirdId        string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId         string  `json:"GameId" gorm:"column:GameId"`
	GameName       string  `json:"GameName" gorm:"column:GameName"`
	BetAmount      float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount      float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet       float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime      string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency       string  `json:"Currency" gorm:"column:Currency"`
	RawData        string  `json:"RawData" gorm:"column:RawData"`
	State          int     `json:"State" gorm:"column:State"`
	Fee            float64 `json:"Fee" gorm:"column:Fee"`
	DataState      int     `json:"DataState" gorm:"column:DataState"`
	CreateTime     string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup        string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId           string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId     int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent   int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx         string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst        string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType     int     `json:"BetCtxType" gorm:"column:BetCtxType"`
	ResettleState  int     `json:"ResettleState" gorm:"column:ResettleState"`
	ResettleTime   *string `json:"ResettleTime" gorm:"column:ResettleTime"`
	ResettleNumber int     `json:"ResettleNumber" gorm:"column:ResettleNumber"`
	ThirdRefId     string  `json:"ThirdRefId" gorm:"column:ThirdRefId"`
	BetTime        *string `json:"BetTime" gorm:"column:BetTime"`
	SettleTime     *string `json:"SettleTime" gorm:"column:SettleTime"`
	CancelTime     *string `json:"CancelTime" gorm:"column:CancelTime"`
	BetLocalTime   *string `json:"BetLocalTime" gorm:"column:BetLocalTime"`

	// 新增混合投注字段
	BetType        int     `json:"BetType" gorm:"column:BetType"`               // 下注类型：1=纯真金 2=混合下注 3=彩金
	BonusBetAmount float64 `json:"BonusBetAmount" gorm:"column:BonusBetAmount"` // Bonus币下注金额
	BonusWinAmount float64 `json:"BonusWinAmount" gorm:"column:BonusWinAmount"` // Bonus币派彩金额

}
