// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentDailly(db *gorm.DB, opts ...gen.DOOption) xAgentDailly {
	_xAgentDailly := xAgentDailly{}

	_xAgentDailly.xAgentDaillyDo.UseDB(db, opts...)
	_xAgentDailly.xAgentDaillyDo.UseModel(&model.XAgentDailly{})

	tableName := _xAgentDailly.xAgentDaillyDo.TableName()
	_xAgentDailly.ALL = field.NewAsterisk(tableName)
	_xAgentDailly.ID = field.NewInt32(tableName, "Id")
	_xAgentDailly.UserID = field.NewInt32(tableName, "UserId")
	_xAgentDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentDailly.Address = field.NewString(tableName, "Address")
	_xAgentDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xAgentDailly.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentDailly.BetTrx = field.NewFloat64(tableName, "BetTrx")
	_xAgentDailly.BetUsdt = field.NewFloat64(tableName, "BetUsdt")
	_xAgentDailly.RewardTrx = field.NewFloat64(tableName, "RewardTrx")
	_xAgentDailly.RewardUsdt = field.NewFloat64(tableName, "RewardUsdt")
	_xAgentDailly.LiuSuiTrx = field.NewFloat64(tableName, "LiuSuiTrx")
	_xAgentDailly.LiuSuiUsdt = field.NewFloat64(tableName, "LiuSuiUsdt")
	_xAgentDailly.DictBetTrx = field.NewFloat64(tableName, "DictBetTrx")
	_xAgentDailly.DictBetUsdt = field.NewFloat64(tableName, "DictBetUsdt")
	_xAgentDailly.DictLiuSuiUsdt = field.NewFloat64(tableName, "DictLiuSuiUsdt")
	_xAgentDailly.DictLiuSuiTrx = field.NewFloat64(tableName, "DictLiuSuiTrx")
	_xAgentDailly.DictRewardTrx = field.NewFloat64(tableName, "DictRewardTrx")
	_xAgentDailly.DictRewardUsdt = field.NewFloat64(tableName, "DictRewardUsdt")
	_xAgentDailly.TotalCommissionTrx = field.NewFloat64(tableName, "TotalCommissionTrx")
	_xAgentDailly.TotalCommissionUsdt = field.NewFloat64(tableName, "TotalCommissionUsdt")
	_xAgentDailly.GetedCommissionTrx = field.NewFloat64(tableName, "GetedCommissionTrx")
	_xAgentDailly.GetedCommissionUsdt = field.NewFloat64(tableName, "GetedCommissionUsdt")
	_xAgentDailly.FineCommissionTrx = field.NewFloat64(tableName, "FineCommissionTrx")
	_xAgentDailly.FineCommissionUsdt = field.NewFloat64(tableName, "FineCommissionUsdt")
	_xAgentDailly.NewChildCount = field.NewInt32(tableName, "NewChildCount")
	_xAgentDailly.DictNewChildCount = field.NewInt32(tableName, "DictNewChildCount")
	_xAgentDailly.SelfBetTrx = field.NewFloat64(tableName, "SelfBetTrx")
	_xAgentDailly.SelfBetUsdt = field.NewFloat64(tableName, "SelfBetUsdt")
	_xAgentDailly.SelfLiuSuiTrx = field.NewFloat64(tableName, "SelfLiuSuiTrx")
	_xAgentDailly.SelfLiuSuiUsdt = field.NewFloat64(tableName, "SelfLiuSuiUsdt")
	_xAgentDailly.SelfRewardTrx = field.NewFloat64(tableName, "SelfRewardTrx")
	_xAgentDailly.SelfRewardUsdt = field.NewFloat64(tableName, "SelfRewardUsdt")
	_xAgentDailly.NewLiuSuiTrx = field.NewFloat64(tableName, "NewLiuSuiTrx")
	_xAgentDailly.NewDictLiuSuiTrx = field.NewFloat64(tableName, "NewDictLiuSuiTrx")
	_xAgentDailly.NewLiuSuiHaXiRouletteTrx = field.NewFloat64(tableName, "NewLiuSuiHaXiRouletteTrx")
	_xAgentDailly.NewLiuSuiHaXiRouletteDictTrx = field.NewFloat64(tableName, "NewLiuSuiHaXiRouletteDictTrx")
	_xAgentDailly.NewLiuSui = field.NewFloat64(tableName, "NewLiuSui")
	_xAgentDailly.NewLiuSuiDict = field.NewFloat64(tableName, "NewLiuSuiDict")
	_xAgentDailly.NewLiuSuiHaXi = field.NewFloat64(tableName, "NewLiuSuiHaXi")
	_xAgentDailly.NewLiuSuiHaXiDict = field.NewFloat64(tableName, "NewLiuSuiHaXiDict")
	_xAgentDailly.NewLiuSuiHaXiRoulette = field.NewFloat64(tableName, "NewLiuSuiHaXiRoulette")
	_xAgentDailly.NewLiuSuiHaXiRouletteDict = field.NewFloat64(tableName, "NewLiuSuiHaXiRouletteDict")
	_xAgentDailly.NewLiuSuiLottery = field.NewFloat64(tableName, "NewLiuSuiLottery")
	_xAgentDailly.NewLiuSuiLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLotteryDict")
	_xAgentDailly.NewLiuSuiLowLottery = field.NewFloat64(tableName, "NewLiuSuiLowLottery")
	_xAgentDailly.NewLiuSuiLowLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLowLotteryDict")
	_xAgentDailly.NewLiuSuiLiuHeLottery = field.NewFloat64(tableName, "NewLiuSuiLiuHeLottery")
	_xAgentDailly.NewLiuSuiLiuHeLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLiuHeLotteryDict")
	_xAgentDailly.NewLiuSuiQiPai = field.NewFloat64(tableName, "NewLiuSuiQiPai")
	_xAgentDailly.NewLiuSuiQiPaiDict = field.NewFloat64(tableName, "NewLiuSuiQiPaiDict")
	_xAgentDailly.NewLiuSuiDianZhi = field.NewFloat64(tableName, "NewLiuSuiDianZhi")
	_xAgentDailly.NewLiuSuiDianZhiDict = field.NewFloat64(tableName, "NewLiuSuiDianZhiDict")
	_xAgentDailly.NewLiuSuiXiaoYouXi = field.NewFloat64(tableName, "NewLiuSuiXiaoYouXi")
	_xAgentDailly.NewLiuSuiXiaoYouXiDict = field.NewFloat64(tableName, "NewLiuSuiXiaoYouXiDict")
	_xAgentDailly.NewLiuSuiCryptoMarket = field.NewFloat64(tableName, "NewLiuSuiCryptoMarket")
	_xAgentDailly.NewLiuSuiCryptoMarketDict = field.NewFloat64(tableName, "NewLiuSuiCryptoMarketDict")
	_xAgentDailly.NewLiuSuiLive = field.NewFloat64(tableName, "NewLiuSuiLive")
	_xAgentDailly.NewLiuSuiLiveDict = field.NewFloat64(tableName, "NewLiuSuiLiveDict")
	_xAgentDailly.NewLiuSuiSport = field.NewFloat64(tableName, "NewLiuSuiSport")
	_xAgentDailly.NewLiuSuiSportDict = field.NewFloat64(tableName, "NewLiuSuiSportDict")
	_xAgentDailly.NewLiuSuiTexas = field.NewFloat64(tableName, "NewLiuSuiTexas")
	_xAgentDailly.NewLiuSuiTexasDict = field.NewFloat64(tableName, "NewLiuSuiTexasDict")
	_xAgentDailly.NewCommissionTrx = field.NewFloat64(tableName, "NewCommissionTrx")
	_xAgentDailly.NewCommission = field.NewFloat64(tableName, "NewCommission")
	_xAgentDailly.NewCommissionHaXi = field.NewFloat64(tableName, "NewCommissionHaXi")
	_xAgentDailly.NewCommissionHaXiRoulette = field.NewFloat64(tableName, "NewCommissionHaXiRoulette")
	_xAgentDailly.NewCommissionLottery = field.NewFloat64(tableName, "NewCommissionLottery")
	_xAgentDailly.NewCommissionLowLottery = field.NewFloat64(tableName, "NewCommissionLowLottery")
	_xAgentDailly.NewCommissionLiuHeLottery = field.NewFloat64(tableName, "NewCommissionLiuHeLottery")
	_xAgentDailly.NewCommissionQiPai = field.NewFloat64(tableName, "NewCommissionQiPai")
	_xAgentDailly.NewCommissionDianZhi = field.NewFloat64(tableName, "NewCommissionDianZhi")
	_xAgentDailly.NewCommissionXiaoYouXi = field.NewFloat64(tableName, "NewCommissionXiaoYouXi")
	_xAgentDailly.NewCommissionCryptoMarket = field.NewFloat64(tableName, "NewCommissionCryptoMarket")
	_xAgentDailly.NewCommissionLive = field.NewFloat64(tableName, "NewCommissionLive")
	_xAgentDailly.NewCommissionSport = field.NewFloat64(tableName, "NewCommissionSport")
	_xAgentDailly.NewCommissionTexas = field.NewFloat64(tableName, "NewCommissionTexas")
	_xAgentDailly.NewStateTrx = field.NewInt32(tableName, "NewStateTrx")
	_xAgentDailly.NewState = field.NewInt32(tableName, "NewState")
	_xAgentDailly.LastUpdateTime = field.NewTime(tableName, "LastUpdateTime")
	_xAgentDailly.NewSelfLiuSuiTrx = field.NewFloat64(tableName, "NewSelfLiuSuiTrx")
	_xAgentDailly.NewSelfLiuSuiHaXi = field.NewFloat64(tableName, "NewSelfLiuSuiHaXi")
	_xAgentDailly.NewSelfLiuSuiHaXiRouletteTrx = field.NewFloat64(tableName, "NewSelfLiuSuiHaXiRouletteTrx")
	_xAgentDailly.NewSelfLiuSuiHaXiRoulette = field.NewFloat64(tableName, "NewSelfLiuSuiHaXiRoulette")
	_xAgentDailly.NewSelfLiuSuiLottery = field.NewFloat64(tableName, "NewSelfLiuSuiLottery")
	_xAgentDailly.NewSelfLiuSuiLowLottery = field.NewFloat64(tableName, "NewSelfLiuSuiLowLottery")
	_xAgentDailly.NewSelfLiuSuiLiuHeLottery = field.NewFloat64(tableName, "NewSelfLiuSuiLiuHeLottery")
	_xAgentDailly.NewSelfLiuSuiQiPai = field.NewFloat64(tableName, "NewSelfLiuSuiQiPai")
	_xAgentDailly.NewSelfLiuSuiDianZhi = field.NewFloat64(tableName, "NewSelfLiuSuiDianZhi")
	_xAgentDailly.NewSelfLiuSuiXiaoYouXi = field.NewFloat64(tableName, "NewSelfLiuSuiXiaoYouXi")
	_xAgentDailly.NewSelfLiuSuiCryptoMarket = field.NewFloat64(tableName, "NewSelfLiuSuiCryptoMarket")
	_xAgentDailly.NewSelfLiuSuiLive = field.NewFloat64(tableName, "NewSelfLiuSuiLive")
	_xAgentDailly.NewSelfLiuSuiSport = field.NewFloat64(tableName, "NewSelfLiuSuiSport")
	_xAgentDailly.NewSelfLiuSuiTexas = field.NewFloat64(tableName, "NewSelfLiuSuiTexas")
	_xAgentDailly.TmpCommissionTrx = field.NewFloat64(tableName, "TmpCommissionTrx")
	_xAgentDailly.TmpCommission = field.NewFloat64(tableName, "TmpCommission")
	_xAgentDailly.LiuSuiHashTrxT1 = field.NewFloat64(tableName, "LiuSuiHashTrx_t1")
	_xAgentDailly.LiuSuiHashUsdtT1 = field.NewFloat64(tableName, "LiuSuiHashUsdt_t1")
	_xAgentDailly.LiuSuiHaXiRouletteTrxT1 = field.NewFloat64(tableName, "LiuSuiHaXiRouletteTrx_t1")
	_xAgentDailly.LiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(tableName, "LiuSuiHaXiRouletteUsdt_t1")
	_xAgentDailly.LiuSuiDianZhiUsdtT1 = field.NewFloat64(tableName, "LiuSuiDianZhiUsdt_t1")
	_xAgentDailly.LiuSuiQiPaiUsdtT1 = field.NewFloat64(tableName, "LiuSuiQiPaiUsdt_t1")
	_xAgentDailly.LiuSuiLiveUsdtT1 = field.NewFloat64(tableName, "LiuSuiLiveUsdt_t1")
	_xAgentDailly.LiuSuiChainUsdtT1 = field.NewFloat64(tableName, "LiuSuiChainUsdt_t1")
	_xAgentDailly.LiuSuiCryptoMarketUsdtT1 = field.NewFloat64(tableName, "LiuSuiCryptoMarketUsdt_t1")
	_xAgentDailly.LiuSuiLotteryUsdtT1 = field.NewFloat64(tableName, "LiuSuiLotteryUsdt_t1")
	_xAgentDailly.LiuSuiLowLotteryUsdtT1 = field.NewFloat64(tableName, "LiuSuiLowLotteryUsdt_t1")
	_xAgentDailly.LiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(tableName, "LiuSuiLiuHeLotteryUsdt_t1")
	_xAgentDailly.LiuSuiTexasUsdtT1 = field.NewFloat64(tableName, "LiuSuiTexasUsdt_t1")
	_xAgentDailly.SelfLiuSuiHashTrxT1 = field.NewFloat64(tableName, "SelfLiuSuiHashTrx_t1")
	_xAgentDailly.SelfLiuSuiHashUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiHashUsdt_t1")
	_xAgentDailly.SelfLiuSuiHaXiRouletteTrxT1 = field.NewFloat64(tableName, "SelfLiuSuiHaXiRouletteTrx_t1")
	_xAgentDailly.SelfLiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiHaXiRouletteUsdt_t1")
	_xAgentDailly.SelfLiuSuiDianZhiUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiDianZhiUsdt_t1")
	_xAgentDailly.SelfLiuSuiQiPaiUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiQiPaiUsdt_t1")
	_xAgentDailly.SelfLiuSuiLiveUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiLiveUsdt_t1")
	_xAgentDailly.SelfLiuSuiChainUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiChainUsdt_t1")
	_xAgentDailly.SelfLiuSuiCryptoMarketUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiCryptoMarketUsdt_t1")
	_xAgentDailly.SelfLiuSuiLotteryUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiLotteryUsdt_t1")
	_xAgentDailly.SelfLiuSuiLowLotteryUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiLowLotteryUsdt_t1")
	_xAgentDailly.SelfLiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiLiuHeLotteryUsdt_t1")
	_xAgentDailly.LiuSuiSportUsdtT1 = field.NewFloat64(tableName, "LiuSuiSportUsdt_t1")
	_xAgentDailly.SelfLiuSuiSportUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiSportUsdt_t1")
	_xAgentDailly.SelfLiuSuiTexasUsdtT1 = field.NewFloat64(tableName, "SelfLiuSuiTexasUsdt_t1")
	_xAgentDailly.DirectLiuSuiHashTrxT1 = field.NewFloat64(tableName, "DirectLiuSuiHashTrx_t1")
	_xAgentDailly.DirectLiuSuiHashUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiHashUsdt_t1")
	_xAgentDailly.DirectLiuSuiHaXiRouletteTrxT1 = field.NewFloat64(tableName, "DirectLiuSuiHaXiRouletteTrx_t1")
	_xAgentDailly.DirectLiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiHaXiRouletteUsdt_t1")
	_xAgentDailly.DirectLiuSuiDianZhiUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiDianZhiUsdt_t1")
	_xAgentDailly.DirectLiuSuiQiPaiUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiQiPaiUsdt_t1")
	_xAgentDailly.DirectLiuSuiLiveUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiLiveUsdt_t1")
	_xAgentDailly.DirectLiuSuiChainUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiChainUsdt_t1")
	_xAgentDailly.DirectLiuSuiCryptoMarketUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiCryptoMarketUsdt_t1")
	_xAgentDailly.DirectLiuSuiLotteryUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiLotteryUsdt_t1")
	_xAgentDailly.DirectLiuSuiLowLotteryUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiLowLotteryUsdt_t1")
	_xAgentDailly.DirectLiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiLiuHeLotteryUsdt_t1")
	_xAgentDailly.DirectLiuSuiSportUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiSportUsdt_t1")
	_xAgentDailly.DirectLiuSuiTexasUsdtT1 = field.NewFloat64(tableName, "DirectLiuSuiTexasUsdt_t1")
	_xAgentDailly.SelfCommissionUsdtT1 = field.NewFloat64(tableName, "SelfCommissionUsdt_t1")
	_xAgentDailly.DirectCommissionUsdtT1 = field.NewFloat64(tableName, "DirectCommissionUsdt_t1")
	_xAgentDailly.TeamCommissionUsdtT1 = field.NewFloat64(tableName, "TeamCommissionUsdt_t1")
	_xAgentDailly.SelfCommissionTrxT1 = field.NewFloat64(tableName, "SelfCommissionTrx_t1")
	_xAgentDailly.DirectCommissionTrxT1 = field.NewFloat64(tableName, "DirectCommissionTrx_t1")
	_xAgentDailly.TeamCommissionTrxT1 = field.NewFloat64(tableName, "TeamCommissionTrx_t1")
	_xAgentDailly.TotalCommissionTrxT1 = field.NewFloat64(tableName, "TotalCommissionTrx_t1")
	_xAgentDailly.FineCommissionTrxT1 = field.NewFloat64(tableName, "FineCommissionTrx_t1")
	_xAgentDailly.AvailableCommissionTrxT1 = field.NewFloat64(tableName, "AvailableCommissionTrx_t1")
	_xAgentDailly.BackCommissionTrxT1 = field.NewFloat64(tableName, "BackCommissionTrx_t1")
	_xAgentDailly.GetedCommissionTrxT1 = field.NewFloat64(tableName, "GetedCommissionTrx_t1")
	_xAgentDailly.TotalCommissionUsdtT1 = field.NewFloat64(tableName, "TotalCommissionUsdt_t1")
	_xAgentDailly.FineCommissionUsdtT1 = field.NewFloat64(tableName, "FineCommissionUsdt_t1")
	_xAgentDailly.AvailableCommissionUsdtT1 = field.NewFloat64(tableName, "AvailableCommissionUsdt_t1")
	_xAgentDailly.BackCommissionUsdtT1 = field.NewFloat64(tableName, "BackCommissionUsdt_t1")
	_xAgentDailly.GetedCommissionUsdtT1 = field.NewFloat64(tableName, "GetedCommissionUsdt_t1")

	_xAgentDailly.fillFieldMap()

	return _xAgentDailly
}

type xAgentDailly struct {
	xAgentDaillyDo xAgentDaillyDo

	ALL                            field.Asterisk
	ID                             field.Int32
	UserID                         field.Int32
	ChannelID                      field.Int32
	Address                        field.String
	RecordDate                     field.Time
	SellerID                       field.Int32
	BetTrx                         field.Float64 // Trx下注
	BetUsdt                        field.Float64 // usdt下注
	RewardTrx                      field.Float64 // trx返奖
	RewardUsdt                     field.Float64 // usdt返奖
	LiuSuiTrx                      field.Float64 // trx流水
	LiuSuiUsdt                     field.Float64 // usdt流水
	DictBetTrx                     field.Float64 // Trx下注
	DictBetUsdt                    field.Float64 // usdt下注
	DictLiuSuiUsdt                 field.Float64 // usdt流水
	DictLiuSuiTrx                  field.Float64 // trx流水
	DictRewardTrx                  field.Float64 // trx返奖
	DictRewardUsdt                 field.Float64 // usdt返奖
	TotalCommissionTrx             field.Float64 // 当日产生佣金
	TotalCommissionUsdt            field.Float64
	GetedCommissionTrx             field.Float64 // 当日领取佣金
	GetedCommissionUsdt            field.Float64
	FineCommissionTrx              field.Float64 // 当日罚没佣金
	FineCommissionUsdt             field.Float64
	NewChildCount                  field.Int32 // 当日新增下级
	DictNewChildCount              field.Int32 // 当日新增直属下级
	SelfBetTrx                     field.Float64
	SelfBetUsdt                    field.Float64
	SelfLiuSuiTrx                  field.Float64
	SelfLiuSuiUsdt                 field.Float64
	SelfRewardTrx                  field.Float64
	SelfRewardUsdt                 field.Float64
	NewLiuSuiTrx                   field.Float64 // 新版代理trx业绩,包含直属的业绩
	NewDictLiuSuiTrx               field.Float64 // 新版代理trx直属业绩
	NewLiuSuiHaXiRouletteTrx       field.Float64
	NewLiuSuiHaXiRouletteDictTrx   field.Float64
	NewLiuSui                      field.Float64 // 新版代理业绩,包含直属业绩
	NewLiuSuiDict                  field.Float64 // 新版代理直属业绩
	NewLiuSuiHaXi                  field.Float64 // 新版代理哈希业绩,包含直属
	NewLiuSuiHaXiDict              field.Float64 // 新版代理哈希直属业绩
	NewLiuSuiHaXiRoulette          field.Float64
	NewLiuSuiHaXiRouletteDict      field.Float64
	NewLiuSuiLottery               field.Float64
	NewLiuSuiLotteryDict           field.Float64
	NewLiuSuiLowLottery            field.Float64
	NewLiuSuiLowLotteryDict        field.Float64
	NewLiuSuiLiuHeLottery          field.Float64
	NewLiuSuiLiuHeLotteryDict      field.Float64
	NewLiuSuiQiPai                 field.Float64
	NewLiuSuiQiPaiDict             field.Float64
	NewLiuSuiDianZhi               field.Float64
	NewLiuSuiDianZhiDict           field.Float64
	NewLiuSuiXiaoYouXi             field.Float64
	NewLiuSuiXiaoYouXiDict         field.Float64
	NewLiuSuiCryptoMarket          field.Float64
	NewLiuSuiCryptoMarketDict      field.Float64
	NewLiuSuiLive                  field.Float64
	NewLiuSuiLiveDict              field.Float64
	NewLiuSuiSport                 field.Float64
	NewLiuSuiSportDict             field.Float64
	NewLiuSuiTexas                 field.Float64
	NewLiuSuiTexasDict             field.Float64
	NewCommissionTrx               field.Float64 // 佣金trx
	NewCommission                  field.Float64 // 佣金
	NewCommissionHaXi              field.Float64
	NewCommissionHaXiRoulette      field.Float64
	NewCommissionLottery           field.Float64
	NewCommissionLowLottery        field.Float64
	NewCommissionLiuHeLottery      field.Float64
	NewCommissionQiPai             field.Float64
	NewCommissionDianZhi           field.Float64
	NewCommissionXiaoYouXi         field.Float64
	NewCommissionCryptoMarket      field.Float64
	NewCommissionLive              field.Float64
	NewCommissionSport             field.Float64
	NewCommissionTexas             field.Float64
	NewStateTrx                    field.Int32 // 1当天还没结算完,2结算完未领取 ,3已领取
	NewState                       field.Int32 // 1当天还没结算完,2结算完未领取 ,3已领取
	LastUpdateTime                 field.Time
	NewSelfLiuSuiTrx               field.Float64
	NewSelfLiuSuiHaXi              field.Float64
	NewSelfLiuSuiHaXiRouletteTrx   field.Float64
	NewSelfLiuSuiHaXiRoulette      field.Float64
	NewSelfLiuSuiLottery           field.Float64
	NewSelfLiuSuiLowLottery        field.Float64
	NewSelfLiuSuiLiuHeLottery      field.Float64
	NewSelfLiuSuiQiPai             field.Float64
	NewSelfLiuSuiDianZhi           field.Float64
	NewSelfLiuSuiXiaoYouXi         field.Float64
	NewSelfLiuSuiCryptoMarket      field.Float64
	NewSelfLiuSuiLive              field.Float64
	NewSelfLiuSuiSport             field.Float64
	NewSelfLiuSuiTexas             field.Float64
	TmpCommissionTrx               field.Float64
	TmpCommission                  field.Float64
	LiuSuiHashTrxT1                field.Float64
	LiuSuiHashUsdtT1               field.Float64
	LiuSuiHaXiRouletteTrxT1        field.Float64
	LiuSuiHaXiRouletteUsdtT1       field.Float64
	LiuSuiDianZhiUsdtT1            field.Float64
	LiuSuiQiPaiUsdtT1              field.Float64
	LiuSuiLiveUsdtT1               field.Float64
	LiuSuiChainUsdtT1              field.Float64
	LiuSuiCryptoMarketUsdtT1       field.Float64
	LiuSuiLotteryUsdtT1            field.Float64
	LiuSuiLowLotteryUsdtT1         field.Float64
	LiuSuiLiuHeLotteryUsdtT1       field.Float64
	LiuSuiTexasUsdtT1              field.Float64
	SelfLiuSuiHashTrxT1            field.Float64
	SelfLiuSuiHashUsdtT1           field.Float64
	SelfLiuSuiHaXiRouletteTrxT1    field.Float64
	SelfLiuSuiHaXiRouletteUsdtT1   field.Float64
	SelfLiuSuiDianZhiUsdtT1        field.Float64
	SelfLiuSuiQiPaiUsdtT1          field.Float64
	SelfLiuSuiLiveUsdtT1           field.Float64
	SelfLiuSuiChainUsdtT1          field.Float64
	SelfLiuSuiCryptoMarketUsdtT1   field.Float64
	SelfLiuSuiLotteryUsdtT1        field.Float64
	SelfLiuSuiLowLotteryUsdtT1     field.Float64
	SelfLiuSuiLiuHeLotteryUsdtT1   field.Float64
	LiuSuiSportUsdtT1              field.Float64
	SelfLiuSuiSportUsdtT1          field.Float64
	SelfLiuSuiTexasUsdtT1          field.Float64
	DirectLiuSuiHashTrxT1          field.Float64
	DirectLiuSuiHashUsdtT1         field.Float64
	DirectLiuSuiHaXiRouletteTrxT1  field.Float64
	DirectLiuSuiHaXiRouletteUsdtT1 field.Float64
	DirectLiuSuiDianZhiUsdtT1      field.Float64
	DirectLiuSuiQiPaiUsdtT1        field.Float64
	DirectLiuSuiLiveUsdtT1         field.Float64
	DirectLiuSuiChainUsdtT1        field.Float64
	DirectLiuSuiCryptoMarketUsdtT1 field.Float64
	DirectLiuSuiLotteryUsdtT1      field.Float64
	DirectLiuSuiLowLotteryUsdtT1   field.Float64
	DirectLiuSuiLiuHeLotteryUsdtT1 field.Float64
	DirectLiuSuiSportUsdtT1        field.Float64
	DirectLiuSuiTexasUsdtT1        field.Float64
	SelfCommissionUsdtT1           field.Float64
	DirectCommissionUsdtT1         field.Float64
	TeamCommissionUsdtT1           field.Float64
	SelfCommissionTrxT1            field.Float64
	DirectCommissionTrxT1          field.Float64
	TeamCommissionTrxT1            field.Float64
	TotalCommissionTrxT1           field.Float64 // 独立代理—历史总佣金
	FineCommissionTrxT1            field.Float64 // 独立代理-审核拒绝,罚没佣金
	AvailableCommissionTrxT1       field.Float64 // 独立代理-可领取佣金trx
	BackCommissionTrxT1            field.Float64 // 独立代理-退回佣金
	GetedCommissionTrxT1           field.Float64 // 独立代理-已领取佣金
	TotalCommissionUsdtT1          field.Float64
	FineCommissionUsdtT1           field.Float64
	AvailableCommissionUsdtT1      field.Float64
	BackCommissionUsdtT1           field.Float64
	GetedCommissionUsdtT1          field.Float64

	fieldMap map[string]field.Expr
}

func (x xAgentDailly) Table(newTableName string) *xAgentDailly {
	x.xAgentDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentDailly) As(alias string) *xAgentDailly {
	x.xAgentDaillyDo.DO = *(x.xAgentDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentDailly) updateTableName(table string) *xAgentDailly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Address = field.NewString(table, "Address")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.BetTrx = field.NewFloat64(table, "BetTrx")
	x.BetUsdt = field.NewFloat64(table, "BetUsdt")
	x.RewardTrx = field.NewFloat64(table, "RewardTrx")
	x.RewardUsdt = field.NewFloat64(table, "RewardUsdt")
	x.LiuSuiTrx = field.NewFloat64(table, "LiuSuiTrx")
	x.LiuSuiUsdt = field.NewFloat64(table, "LiuSuiUsdt")
	x.DictBetTrx = field.NewFloat64(table, "DictBetTrx")
	x.DictBetUsdt = field.NewFloat64(table, "DictBetUsdt")
	x.DictLiuSuiUsdt = field.NewFloat64(table, "DictLiuSuiUsdt")
	x.DictLiuSuiTrx = field.NewFloat64(table, "DictLiuSuiTrx")
	x.DictRewardTrx = field.NewFloat64(table, "DictRewardTrx")
	x.DictRewardUsdt = field.NewFloat64(table, "DictRewardUsdt")
	x.TotalCommissionTrx = field.NewFloat64(table, "TotalCommissionTrx")
	x.TotalCommissionUsdt = field.NewFloat64(table, "TotalCommissionUsdt")
	x.GetedCommissionTrx = field.NewFloat64(table, "GetedCommissionTrx")
	x.GetedCommissionUsdt = field.NewFloat64(table, "GetedCommissionUsdt")
	x.FineCommissionTrx = field.NewFloat64(table, "FineCommissionTrx")
	x.FineCommissionUsdt = field.NewFloat64(table, "FineCommissionUsdt")
	x.NewChildCount = field.NewInt32(table, "NewChildCount")
	x.DictNewChildCount = field.NewInt32(table, "DictNewChildCount")
	x.SelfBetTrx = field.NewFloat64(table, "SelfBetTrx")
	x.SelfBetUsdt = field.NewFloat64(table, "SelfBetUsdt")
	x.SelfLiuSuiTrx = field.NewFloat64(table, "SelfLiuSuiTrx")
	x.SelfLiuSuiUsdt = field.NewFloat64(table, "SelfLiuSuiUsdt")
	x.SelfRewardTrx = field.NewFloat64(table, "SelfRewardTrx")
	x.SelfRewardUsdt = field.NewFloat64(table, "SelfRewardUsdt")
	x.NewLiuSuiTrx = field.NewFloat64(table, "NewLiuSuiTrx")
	x.NewDictLiuSuiTrx = field.NewFloat64(table, "NewDictLiuSuiTrx")
	x.NewLiuSuiHaXiRouletteTrx = field.NewFloat64(table, "NewLiuSuiHaXiRouletteTrx")
	x.NewLiuSuiHaXiRouletteDictTrx = field.NewFloat64(table, "NewLiuSuiHaXiRouletteDictTrx")
	x.NewLiuSui = field.NewFloat64(table, "NewLiuSui")
	x.NewLiuSuiDict = field.NewFloat64(table, "NewLiuSuiDict")
	x.NewLiuSuiHaXi = field.NewFloat64(table, "NewLiuSuiHaXi")
	x.NewLiuSuiHaXiDict = field.NewFloat64(table, "NewLiuSuiHaXiDict")
	x.NewLiuSuiHaXiRoulette = field.NewFloat64(table, "NewLiuSuiHaXiRoulette")
	x.NewLiuSuiHaXiRouletteDict = field.NewFloat64(table, "NewLiuSuiHaXiRouletteDict")
	x.NewLiuSuiLottery = field.NewFloat64(table, "NewLiuSuiLottery")
	x.NewLiuSuiLotteryDict = field.NewFloat64(table, "NewLiuSuiLotteryDict")
	x.NewLiuSuiLowLottery = field.NewFloat64(table, "NewLiuSuiLowLottery")
	x.NewLiuSuiLowLotteryDict = field.NewFloat64(table, "NewLiuSuiLowLotteryDict")
	x.NewLiuSuiLiuHeLottery = field.NewFloat64(table, "NewLiuSuiLiuHeLottery")
	x.NewLiuSuiLiuHeLotteryDict = field.NewFloat64(table, "NewLiuSuiLiuHeLotteryDict")
	x.NewLiuSuiQiPai = field.NewFloat64(table, "NewLiuSuiQiPai")
	x.NewLiuSuiQiPaiDict = field.NewFloat64(table, "NewLiuSuiQiPaiDict")
	x.NewLiuSuiDianZhi = field.NewFloat64(table, "NewLiuSuiDianZhi")
	x.NewLiuSuiDianZhiDict = field.NewFloat64(table, "NewLiuSuiDianZhiDict")
	x.NewLiuSuiXiaoYouXi = field.NewFloat64(table, "NewLiuSuiXiaoYouXi")
	x.NewLiuSuiXiaoYouXiDict = field.NewFloat64(table, "NewLiuSuiXiaoYouXiDict")
	x.NewLiuSuiCryptoMarket = field.NewFloat64(table, "NewLiuSuiCryptoMarket")
	x.NewLiuSuiCryptoMarketDict = field.NewFloat64(table, "NewLiuSuiCryptoMarketDict")
	x.NewLiuSuiLive = field.NewFloat64(table, "NewLiuSuiLive")
	x.NewLiuSuiLiveDict = field.NewFloat64(table, "NewLiuSuiLiveDict")
	x.NewLiuSuiSport = field.NewFloat64(table, "NewLiuSuiSport")
	x.NewLiuSuiSportDict = field.NewFloat64(table, "NewLiuSuiSportDict")
	x.NewLiuSuiTexas = field.NewFloat64(table, "NewLiuSuiTexas")
	x.NewLiuSuiTexasDict = field.NewFloat64(table, "NewLiuSuiTexasDict")
	x.NewCommissionTrx = field.NewFloat64(table, "NewCommissionTrx")
	x.NewCommission = field.NewFloat64(table, "NewCommission")
	x.NewCommissionHaXi = field.NewFloat64(table, "NewCommissionHaXi")
	x.NewCommissionHaXiRoulette = field.NewFloat64(table, "NewCommissionHaXiRoulette")
	x.NewCommissionLottery = field.NewFloat64(table, "NewCommissionLottery")
	x.NewCommissionLowLottery = field.NewFloat64(table, "NewCommissionLowLottery")
	x.NewCommissionLiuHeLottery = field.NewFloat64(table, "NewCommissionLiuHeLottery")
	x.NewCommissionQiPai = field.NewFloat64(table, "NewCommissionQiPai")
	x.NewCommissionDianZhi = field.NewFloat64(table, "NewCommissionDianZhi")
	x.NewCommissionXiaoYouXi = field.NewFloat64(table, "NewCommissionXiaoYouXi")
	x.NewCommissionCryptoMarket = field.NewFloat64(table, "NewCommissionCryptoMarket")
	x.NewCommissionLive = field.NewFloat64(table, "NewCommissionLive")
	x.NewCommissionSport = field.NewFloat64(table, "NewCommissionSport")
	x.NewCommissionTexas = field.NewFloat64(table, "NewCommissionTexas")
	x.NewStateTrx = field.NewInt32(table, "NewStateTrx")
	x.NewState = field.NewInt32(table, "NewState")
	x.LastUpdateTime = field.NewTime(table, "LastUpdateTime")
	x.NewSelfLiuSuiTrx = field.NewFloat64(table, "NewSelfLiuSuiTrx")
	x.NewSelfLiuSuiHaXi = field.NewFloat64(table, "NewSelfLiuSuiHaXi")
	x.NewSelfLiuSuiHaXiRouletteTrx = field.NewFloat64(table, "NewSelfLiuSuiHaXiRouletteTrx")
	x.NewSelfLiuSuiHaXiRoulette = field.NewFloat64(table, "NewSelfLiuSuiHaXiRoulette")
	x.NewSelfLiuSuiLottery = field.NewFloat64(table, "NewSelfLiuSuiLottery")
	x.NewSelfLiuSuiLowLottery = field.NewFloat64(table, "NewSelfLiuSuiLowLottery")
	x.NewSelfLiuSuiLiuHeLottery = field.NewFloat64(table, "NewSelfLiuSuiLiuHeLottery")
	x.NewSelfLiuSuiQiPai = field.NewFloat64(table, "NewSelfLiuSuiQiPai")
	x.NewSelfLiuSuiDianZhi = field.NewFloat64(table, "NewSelfLiuSuiDianZhi")
	x.NewSelfLiuSuiXiaoYouXi = field.NewFloat64(table, "NewSelfLiuSuiXiaoYouXi")
	x.NewSelfLiuSuiCryptoMarket = field.NewFloat64(table, "NewSelfLiuSuiCryptoMarket")
	x.NewSelfLiuSuiLive = field.NewFloat64(table, "NewSelfLiuSuiLive")
	x.NewSelfLiuSuiSport = field.NewFloat64(table, "NewSelfLiuSuiSport")
	x.NewSelfLiuSuiTexas = field.NewFloat64(table, "NewSelfLiuSuiTexas")
	x.TmpCommissionTrx = field.NewFloat64(table, "TmpCommissionTrx")
	x.TmpCommission = field.NewFloat64(table, "TmpCommission")
	x.LiuSuiHashTrxT1 = field.NewFloat64(table, "LiuSuiHashTrx_t1")
	x.LiuSuiHashUsdtT1 = field.NewFloat64(table, "LiuSuiHashUsdt_t1")
	x.LiuSuiHaXiRouletteTrxT1 = field.NewFloat64(table, "LiuSuiHaXiRouletteTrx_t1")
	x.LiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(table, "LiuSuiHaXiRouletteUsdt_t1")
	x.LiuSuiDianZhiUsdtT1 = field.NewFloat64(table, "LiuSuiDianZhiUsdt_t1")
	x.LiuSuiQiPaiUsdtT1 = field.NewFloat64(table, "LiuSuiQiPaiUsdt_t1")
	x.LiuSuiLiveUsdtT1 = field.NewFloat64(table, "LiuSuiLiveUsdt_t1")
	x.LiuSuiChainUsdtT1 = field.NewFloat64(table, "LiuSuiChainUsdt_t1")
	x.LiuSuiCryptoMarketUsdtT1 = field.NewFloat64(table, "LiuSuiCryptoMarketUsdt_t1")
	x.LiuSuiLotteryUsdtT1 = field.NewFloat64(table, "LiuSuiLotteryUsdt_t1")
	x.LiuSuiLowLotteryUsdtT1 = field.NewFloat64(table, "LiuSuiLowLotteryUsdt_t1")
	x.LiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(table, "LiuSuiLiuHeLotteryUsdt_t1")
	x.LiuSuiTexasUsdtT1 = field.NewFloat64(table, "LiuSuiTexasUsdt_t1")
	x.SelfLiuSuiHashTrxT1 = field.NewFloat64(table, "SelfLiuSuiHashTrx_t1")
	x.SelfLiuSuiHashUsdtT1 = field.NewFloat64(table, "SelfLiuSuiHashUsdt_t1")
	x.SelfLiuSuiHaXiRouletteTrxT1 = field.NewFloat64(table, "SelfLiuSuiHaXiRouletteTrx_t1")
	x.SelfLiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(table, "SelfLiuSuiHaXiRouletteUsdt_t1")
	x.SelfLiuSuiDianZhiUsdtT1 = field.NewFloat64(table, "SelfLiuSuiDianZhiUsdt_t1")
	x.SelfLiuSuiQiPaiUsdtT1 = field.NewFloat64(table, "SelfLiuSuiQiPaiUsdt_t1")
	x.SelfLiuSuiLiveUsdtT1 = field.NewFloat64(table, "SelfLiuSuiLiveUsdt_t1")
	x.SelfLiuSuiChainUsdtT1 = field.NewFloat64(table, "SelfLiuSuiChainUsdt_t1")
	x.SelfLiuSuiCryptoMarketUsdtT1 = field.NewFloat64(table, "SelfLiuSuiCryptoMarketUsdt_t1")
	x.SelfLiuSuiLotteryUsdtT1 = field.NewFloat64(table, "SelfLiuSuiLotteryUsdt_t1")
	x.SelfLiuSuiLowLotteryUsdtT1 = field.NewFloat64(table, "SelfLiuSuiLowLotteryUsdt_t1")
	x.SelfLiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(table, "SelfLiuSuiLiuHeLotteryUsdt_t1")
	x.LiuSuiSportUsdtT1 = field.NewFloat64(table, "LiuSuiSportUsdt_t1")
	x.SelfLiuSuiSportUsdtT1 = field.NewFloat64(table, "SelfLiuSuiSportUsdt_t1")
	x.SelfLiuSuiTexasUsdtT1 = field.NewFloat64(table, "SelfLiuSuiTexasUsdt_t1")
	x.DirectLiuSuiHashTrxT1 = field.NewFloat64(table, "DirectLiuSuiHashTrx_t1")
	x.DirectLiuSuiHashUsdtT1 = field.NewFloat64(table, "DirectLiuSuiHashUsdt_t1")
	x.DirectLiuSuiHaXiRouletteTrxT1 = field.NewFloat64(table, "DirectLiuSuiHaXiRouletteTrx_t1")
	x.DirectLiuSuiHaXiRouletteUsdtT1 = field.NewFloat64(table, "DirectLiuSuiHaXiRouletteUsdt_t1")
	x.DirectLiuSuiDianZhiUsdtT1 = field.NewFloat64(table, "DirectLiuSuiDianZhiUsdt_t1")
	x.DirectLiuSuiQiPaiUsdtT1 = field.NewFloat64(table, "DirectLiuSuiQiPaiUsdt_t1")
	x.DirectLiuSuiLiveUsdtT1 = field.NewFloat64(table, "DirectLiuSuiLiveUsdt_t1")
	x.DirectLiuSuiChainUsdtT1 = field.NewFloat64(table, "DirectLiuSuiChainUsdt_t1")
	x.DirectLiuSuiCryptoMarketUsdtT1 = field.NewFloat64(table, "DirectLiuSuiCryptoMarketUsdt_t1")
	x.DirectLiuSuiLotteryUsdtT1 = field.NewFloat64(table, "DirectLiuSuiLotteryUsdt_t1")
	x.DirectLiuSuiLowLotteryUsdtT1 = field.NewFloat64(table, "DirectLiuSuiLowLotteryUsdt_t1")
	x.DirectLiuSuiLiuHeLotteryUsdtT1 = field.NewFloat64(table, "DirectLiuSuiLiuHeLotteryUsdt_t1")
	x.DirectLiuSuiSportUsdtT1 = field.NewFloat64(table, "DirectLiuSuiSportUsdt_t1")
	x.DirectLiuSuiTexasUsdtT1 = field.NewFloat64(table, "DirectLiuSuiTexasUsdt_t1")
	x.SelfCommissionUsdtT1 = field.NewFloat64(table, "SelfCommissionUsdt_t1")
	x.DirectCommissionUsdtT1 = field.NewFloat64(table, "DirectCommissionUsdt_t1")
	x.TeamCommissionUsdtT1 = field.NewFloat64(table, "TeamCommissionUsdt_t1")
	x.SelfCommissionTrxT1 = field.NewFloat64(table, "SelfCommissionTrx_t1")
	x.DirectCommissionTrxT1 = field.NewFloat64(table, "DirectCommissionTrx_t1")
	x.TeamCommissionTrxT1 = field.NewFloat64(table, "TeamCommissionTrx_t1")
	x.TotalCommissionTrxT1 = field.NewFloat64(table, "TotalCommissionTrx_t1")
	x.FineCommissionTrxT1 = field.NewFloat64(table, "FineCommissionTrx_t1")
	x.AvailableCommissionTrxT1 = field.NewFloat64(table, "AvailableCommissionTrx_t1")
	x.BackCommissionTrxT1 = field.NewFloat64(table, "BackCommissionTrx_t1")
	x.GetedCommissionTrxT1 = field.NewFloat64(table, "GetedCommissionTrx_t1")
	x.TotalCommissionUsdtT1 = field.NewFloat64(table, "TotalCommissionUsdt_t1")
	x.FineCommissionUsdtT1 = field.NewFloat64(table, "FineCommissionUsdt_t1")
	x.AvailableCommissionUsdtT1 = field.NewFloat64(table, "AvailableCommissionUsdt_t1")
	x.BackCommissionUsdtT1 = field.NewFloat64(table, "BackCommissionUsdt_t1")
	x.GetedCommissionUsdtT1 = field.NewFloat64(table, "GetedCommissionUsdt_t1")

	x.fillFieldMap()

	return x
}

func (x *xAgentDailly) WithContext(ctx context.Context) *xAgentDaillyDo {
	return x.xAgentDaillyDo.WithContext(ctx)
}

func (x xAgentDailly) TableName() string { return x.xAgentDaillyDo.TableName() }

func (x xAgentDailly) Alias() string { return x.xAgentDaillyDo.Alias() }

func (x xAgentDailly) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentDaillyDo.Columns(cols...)
}

func (x *xAgentDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 153)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["BetTrx"] = x.BetTrx
	x.fieldMap["BetUsdt"] = x.BetUsdt
	x.fieldMap["RewardTrx"] = x.RewardTrx
	x.fieldMap["RewardUsdt"] = x.RewardUsdt
	x.fieldMap["LiuSuiTrx"] = x.LiuSuiTrx
	x.fieldMap["LiuSuiUsdt"] = x.LiuSuiUsdt
	x.fieldMap["DictBetTrx"] = x.DictBetTrx
	x.fieldMap["DictBetUsdt"] = x.DictBetUsdt
	x.fieldMap["DictLiuSuiUsdt"] = x.DictLiuSuiUsdt
	x.fieldMap["DictLiuSuiTrx"] = x.DictLiuSuiTrx
	x.fieldMap["DictRewardTrx"] = x.DictRewardTrx
	x.fieldMap["DictRewardUsdt"] = x.DictRewardUsdt
	x.fieldMap["TotalCommissionTrx"] = x.TotalCommissionTrx
	x.fieldMap["TotalCommissionUsdt"] = x.TotalCommissionUsdt
	x.fieldMap["GetedCommissionTrx"] = x.GetedCommissionTrx
	x.fieldMap["GetedCommissionUsdt"] = x.GetedCommissionUsdt
	x.fieldMap["FineCommissionTrx"] = x.FineCommissionTrx
	x.fieldMap["FineCommissionUsdt"] = x.FineCommissionUsdt
	x.fieldMap["NewChildCount"] = x.NewChildCount
	x.fieldMap["DictNewChildCount"] = x.DictNewChildCount
	x.fieldMap["SelfBetTrx"] = x.SelfBetTrx
	x.fieldMap["SelfBetUsdt"] = x.SelfBetUsdt
	x.fieldMap["SelfLiuSuiTrx"] = x.SelfLiuSuiTrx
	x.fieldMap["SelfLiuSuiUsdt"] = x.SelfLiuSuiUsdt
	x.fieldMap["SelfRewardTrx"] = x.SelfRewardTrx
	x.fieldMap["SelfRewardUsdt"] = x.SelfRewardUsdt
	x.fieldMap["NewLiuSuiTrx"] = x.NewLiuSuiTrx
	x.fieldMap["NewDictLiuSuiTrx"] = x.NewDictLiuSuiTrx
	x.fieldMap["NewLiuSuiHaXiRouletteTrx"] = x.NewLiuSuiHaXiRouletteTrx
	x.fieldMap["NewLiuSuiHaXiRouletteDictTrx"] = x.NewLiuSuiHaXiRouletteDictTrx
	x.fieldMap["NewLiuSui"] = x.NewLiuSui
	x.fieldMap["NewLiuSuiDict"] = x.NewLiuSuiDict
	x.fieldMap["NewLiuSuiHaXi"] = x.NewLiuSuiHaXi
	x.fieldMap["NewLiuSuiHaXiDict"] = x.NewLiuSuiHaXiDict
	x.fieldMap["NewLiuSuiHaXiRoulette"] = x.NewLiuSuiHaXiRoulette
	x.fieldMap["NewLiuSuiHaXiRouletteDict"] = x.NewLiuSuiHaXiRouletteDict
	x.fieldMap["NewLiuSuiLottery"] = x.NewLiuSuiLottery
	x.fieldMap["NewLiuSuiLotteryDict"] = x.NewLiuSuiLotteryDict
	x.fieldMap["NewLiuSuiLowLottery"] = x.NewLiuSuiLowLottery
	x.fieldMap["NewLiuSuiLowLotteryDict"] = x.NewLiuSuiLowLotteryDict
	x.fieldMap["NewLiuSuiLiuHeLottery"] = x.NewLiuSuiLiuHeLottery
	x.fieldMap["NewLiuSuiLiuHeLotteryDict"] = x.NewLiuSuiLiuHeLotteryDict
	x.fieldMap["NewLiuSuiQiPai"] = x.NewLiuSuiQiPai
	x.fieldMap["NewLiuSuiQiPaiDict"] = x.NewLiuSuiQiPaiDict
	x.fieldMap["NewLiuSuiDianZhi"] = x.NewLiuSuiDianZhi
	x.fieldMap["NewLiuSuiDianZhiDict"] = x.NewLiuSuiDianZhiDict
	x.fieldMap["NewLiuSuiXiaoYouXi"] = x.NewLiuSuiXiaoYouXi
	x.fieldMap["NewLiuSuiXiaoYouXiDict"] = x.NewLiuSuiXiaoYouXiDict
	x.fieldMap["NewLiuSuiCryptoMarket"] = x.NewLiuSuiCryptoMarket
	x.fieldMap["NewLiuSuiCryptoMarketDict"] = x.NewLiuSuiCryptoMarketDict
	x.fieldMap["NewLiuSuiLive"] = x.NewLiuSuiLive
	x.fieldMap["NewLiuSuiLiveDict"] = x.NewLiuSuiLiveDict
	x.fieldMap["NewLiuSuiSport"] = x.NewLiuSuiSport
	x.fieldMap["NewLiuSuiSportDict"] = x.NewLiuSuiSportDict
	x.fieldMap["NewLiuSuiTexas"] = x.NewLiuSuiTexas
	x.fieldMap["NewLiuSuiTexasDict"] = x.NewLiuSuiTexasDict
	x.fieldMap["NewCommissionTrx"] = x.NewCommissionTrx
	x.fieldMap["NewCommission"] = x.NewCommission
	x.fieldMap["NewCommissionHaXi"] = x.NewCommissionHaXi
	x.fieldMap["NewCommissionHaXiRoulette"] = x.NewCommissionHaXiRoulette
	x.fieldMap["NewCommissionLottery"] = x.NewCommissionLottery
	x.fieldMap["NewCommissionLowLottery"] = x.NewCommissionLowLottery
	x.fieldMap["NewCommissionLiuHeLottery"] = x.NewCommissionLiuHeLottery
	x.fieldMap["NewCommissionQiPai"] = x.NewCommissionQiPai
	x.fieldMap["NewCommissionDianZhi"] = x.NewCommissionDianZhi
	x.fieldMap["NewCommissionXiaoYouXi"] = x.NewCommissionXiaoYouXi
	x.fieldMap["NewCommissionCryptoMarket"] = x.NewCommissionCryptoMarket
	x.fieldMap["NewCommissionLive"] = x.NewCommissionLive
	x.fieldMap["NewCommissionSport"] = x.NewCommissionSport
	x.fieldMap["NewCommissionTexas"] = x.NewCommissionTexas
	x.fieldMap["NewStateTrx"] = x.NewStateTrx
	x.fieldMap["NewState"] = x.NewState
	x.fieldMap["LastUpdateTime"] = x.LastUpdateTime
	x.fieldMap["NewSelfLiuSuiTrx"] = x.NewSelfLiuSuiTrx
	x.fieldMap["NewSelfLiuSuiHaXi"] = x.NewSelfLiuSuiHaXi
	x.fieldMap["NewSelfLiuSuiHaXiRouletteTrx"] = x.NewSelfLiuSuiHaXiRouletteTrx
	x.fieldMap["NewSelfLiuSuiHaXiRoulette"] = x.NewSelfLiuSuiHaXiRoulette
	x.fieldMap["NewSelfLiuSuiLottery"] = x.NewSelfLiuSuiLottery
	x.fieldMap["NewSelfLiuSuiLowLottery"] = x.NewSelfLiuSuiLowLottery
	x.fieldMap["NewSelfLiuSuiLiuHeLottery"] = x.NewSelfLiuSuiLiuHeLottery
	x.fieldMap["NewSelfLiuSuiQiPai"] = x.NewSelfLiuSuiQiPai
	x.fieldMap["NewSelfLiuSuiDianZhi"] = x.NewSelfLiuSuiDianZhi
	x.fieldMap["NewSelfLiuSuiXiaoYouXi"] = x.NewSelfLiuSuiXiaoYouXi
	x.fieldMap["NewSelfLiuSuiCryptoMarket"] = x.NewSelfLiuSuiCryptoMarket
	x.fieldMap["NewSelfLiuSuiLive"] = x.NewSelfLiuSuiLive
	x.fieldMap["NewSelfLiuSuiSport"] = x.NewSelfLiuSuiSport
	x.fieldMap["NewSelfLiuSuiTexas"] = x.NewSelfLiuSuiTexas
	x.fieldMap["TmpCommissionTrx"] = x.TmpCommissionTrx
	x.fieldMap["TmpCommission"] = x.TmpCommission
	x.fieldMap["LiuSuiHashTrx_t1"] = x.LiuSuiHashTrxT1
	x.fieldMap["LiuSuiHashUsdt_t1"] = x.LiuSuiHashUsdtT1
	x.fieldMap["LiuSuiHaXiRouletteTrx_t1"] = x.LiuSuiHaXiRouletteTrxT1
	x.fieldMap["LiuSuiHaXiRouletteUsdt_t1"] = x.LiuSuiHaXiRouletteUsdtT1
	x.fieldMap["LiuSuiDianZhiUsdt_t1"] = x.LiuSuiDianZhiUsdtT1
	x.fieldMap["LiuSuiQiPaiUsdt_t1"] = x.LiuSuiQiPaiUsdtT1
	x.fieldMap["LiuSuiLiveUsdt_t1"] = x.LiuSuiLiveUsdtT1
	x.fieldMap["LiuSuiChainUsdt_t1"] = x.LiuSuiChainUsdtT1
	x.fieldMap["LiuSuiCryptoMarketUsdt_t1"] = x.LiuSuiCryptoMarketUsdtT1
	x.fieldMap["LiuSuiLotteryUsdt_t1"] = x.LiuSuiLotteryUsdtT1
	x.fieldMap["LiuSuiLowLotteryUsdt_t1"] = x.LiuSuiLowLotteryUsdtT1
	x.fieldMap["LiuSuiLiuHeLotteryUsdt_t1"] = x.LiuSuiLiuHeLotteryUsdtT1
	x.fieldMap["LiuSuiTexasUsdt_t1"] = x.LiuSuiTexasUsdtT1
	x.fieldMap["SelfLiuSuiHashTrx_t1"] = x.SelfLiuSuiHashTrxT1
	x.fieldMap["SelfLiuSuiHashUsdt_t1"] = x.SelfLiuSuiHashUsdtT1
	x.fieldMap["SelfLiuSuiHaXiRouletteTrx_t1"] = x.SelfLiuSuiHaXiRouletteTrxT1
	x.fieldMap["SelfLiuSuiHaXiRouletteUsdt_t1"] = x.SelfLiuSuiHaXiRouletteUsdtT1
	x.fieldMap["SelfLiuSuiDianZhiUsdt_t1"] = x.SelfLiuSuiDianZhiUsdtT1
	x.fieldMap["SelfLiuSuiQiPaiUsdt_t1"] = x.SelfLiuSuiQiPaiUsdtT1
	x.fieldMap["SelfLiuSuiLiveUsdt_t1"] = x.SelfLiuSuiLiveUsdtT1
	x.fieldMap["SelfLiuSuiChainUsdt_t1"] = x.SelfLiuSuiChainUsdtT1
	x.fieldMap["SelfLiuSuiCryptoMarketUsdt_t1"] = x.SelfLiuSuiCryptoMarketUsdtT1
	x.fieldMap["SelfLiuSuiLotteryUsdt_t1"] = x.SelfLiuSuiLotteryUsdtT1
	x.fieldMap["SelfLiuSuiLowLotteryUsdt_t1"] = x.SelfLiuSuiLowLotteryUsdtT1
	x.fieldMap["SelfLiuSuiLiuHeLotteryUsdt_t1"] = x.SelfLiuSuiLiuHeLotteryUsdtT1
	x.fieldMap["LiuSuiSportUsdt_t1"] = x.LiuSuiSportUsdtT1
	x.fieldMap["SelfLiuSuiSportUsdt_t1"] = x.SelfLiuSuiSportUsdtT1
	x.fieldMap["SelfLiuSuiTexasUsdt_t1"] = x.SelfLiuSuiTexasUsdtT1
	x.fieldMap["DirectLiuSuiHashTrx_t1"] = x.DirectLiuSuiHashTrxT1
	x.fieldMap["DirectLiuSuiHashUsdt_t1"] = x.DirectLiuSuiHashUsdtT1
	x.fieldMap["DirectLiuSuiHaXiRouletteTrx_t1"] = x.DirectLiuSuiHaXiRouletteTrxT1
	x.fieldMap["DirectLiuSuiHaXiRouletteUsdt_t1"] = x.DirectLiuSuiHaXiRouletteUsdtT1
	x.fieldMap["DirectLiuSuiDianZhiUsdt_t1"] = x.DirectLiuSuiDianZhiUsdtT1
	x.fieldMap["DirectLiuSuiQiPaiUsdt_t1"] = x.DirectLiuSuiQiPaiUsdtT1
	x.fieldMap["DirectLiuSuiLiveUsdt_t1"] = x.DirectLiuSuiLiveUsdtT1
	x.fieldMap["DirectLiuSuiChainUsdt_t1"] = x.DirectLiuSuiChainUsdtT1
	x.fieldMap["DirectLiuSuiCryptoMarketUsdt_t1"] = x.DirectLiuSuiCryptoMarketUsdtT1
	x.fieldMap["DirectLiuSuiLotteryUsdt_t1"] = x.DirectLiuSuiLotteryUsdtT1
	x.fieldMap["DirectLiuSuiLowLotteryUsdt_t1"] = x.DirectLiuSuiLowLotteryUsdtT1
	x.fieldMap["DirectLiuSuiLiuHeLotteryUsdt_t1"] = x.DirectLiuSuiLiuHeLotteryUsdtT1
	x.fieldMap["DirectLiuSuiSportUsdt_t1"] = x.DirectLiuSuiSportUsdtT1
	x.fieldMap["DirectLiuSuiTexasUsdt_t1"] = x.DirectLiuSuiTexasUsdtT1
	x.fieldMap["SelfCommissionUsdt_t1"] = x.SelfCommissionUsdtT1
	x.fieldMap["DirectCommissionUsdt_t1"] = x.DirectCommissionUsdtT1
	x.fieldMap["TeamCommissionUsdt_t1"] = x.TeamCommissionUsdtT1
	x.fieldMap["SelfCommissionTrx_t1"] = x.SelfCommissionTrxT1
	x.fieldMap["DirectCommissionTrx_t1"] = x.DirectCommissionTrxT1
	x.fieldMap["TeamCommissionTrx_t1"] = x.TeamCommissionTrxT1
	x.fieldMap["TotalCommissionTrx_t1"] = x.TotalCommissionTrxT1
	x.fieldMap["FineCommissionTrx_t1"] = x.FineCommissionTrxT1
	x.fieldMap["AvailableCommissionTrx_t1"] = x.AvailableCommissionTrxT1
	x.fieldMap["BackCommissionTrx_t1"] = x.BackCommissionTrxT1
	x.fieldMap["GetedCommissionTrx_t1"] = x.GetedCommissionTrxT1
	x.fieldMap["TotalCommissionUsdt_t1"] = x.TotalCommissionUsdtT1
	x.fieldMap["FineCommissionUsdt_t1"] = x.FineCommissionUsdtT1
	x.fieldMap["AvailableCommissionUsdt_t1"] = x.AvailableCommissionUsdtT1
	x.fieldMap["BackCommissionUsdt_t1"] = x.BackCommissionUsdtT1
	x.fieldMap["GetedCommissionUsdt_t1"] = x.GetedCommissionUsdtT1
}

func (x xAgentDailly) clone(db *gorm.DB) xAgentDailly {
	x.xAgentDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentDailly) replaceDB(db *gorm.DB) xAgentDailly {
	x.xAgentDaillyDo.ReplaceDB(db)
	return x
}

type xAgentDaillyDo struct{ gen.DO }

func (x xAgentDaillyDo) Debug() *xAgentDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentDaillyDo) WithContext(ctx context.Context) *xAgentDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentDaillyDo) ReadDB() *xAgentDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentDaillyDo) WriteDB() *xAgentDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentDaillyDo) Session(config *gorm.Session) *xAgentDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentDaillyDo) Clauses(conds ...clause.Expression) *xAgentDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentDaillyDo) Returning(value interface{}, columns ...string) *xAgentDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentDaillyDo) Not(conds ...gen.Condition) *xAgentDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentDaillyDo) Or(conds ...gen.Condition) *xAgentDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentDaillyDo) Select(conds ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentDaillyDo) Where(conds ...gen.Condition) *xAgentDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentDaillyDo) Order(conds ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentDaillyDo) Distinct(cols ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentDaillyDo) Omit(cols ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentDaillyDo) Group(cols ...field.Expr) *xAgentDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentDaillyDo) Having(conds ...gen.Condition) *xAgentDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentDaillyDo) Limit(limit int) *xAgentDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentDaillyDo) Offset(offset int) *xAgentDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentDaillyDo) Unscoped() *xAgentDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentDaillyDo) Create(values ...*model.XAgentDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentDaillyDo) CreateInBatches(values []*model.XAgentDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentDaillyDo) Save(values ...*model.XAgentDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentDaillyDo) First() (*model.XAgentDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDailly), nil
	}
}

func (x xAgentDaillyDo) Take() (*model.XAgentDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDailly), nil
	}
}

func (x xAgentDaillyDo) Last() (*model.XAgentDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDailly), nil
	}
}

func (x xAgentDaillyDo) Find() ([]*model.XAgentDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentDailly), err
}

func (x xAgentDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentDailly, err error) {
	buf := make([]*model.XAgentDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentDaillyDo) FindInBatches(result *[]*model.XAgentDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentDaillyDo) Attrs(attrs ...field.AssignExpr) *xAgentDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentDaillyDo) Assign(attrs ...field.AssignExpr) *xAgentDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentDaillyDo) Joins(fields ...field.RelationField) *xAgentDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentDaillyDo) Preload(fields ...field.RelationField) *xAgentDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentDaillyDo) FirstOrInit() (*model.XAgentDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDailly), nil
	}
}

func (x xAgentDaillyDo) FirstOrCreate() (*model.XAgentDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentDailly), nil
	}
}

func (x xAgentDaillyDo) FindByPage(offset int, limit int) (result []*model.XAgentDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentDaillyDo) Delete(models ...*model.XAgentDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentDaillyDo) withDO(do gen.Dao) *xAgentDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
