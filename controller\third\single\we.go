package single

import (
	"bytes"
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"

	// "encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"

	//"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"gorm.io/gorm"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// WE真人单一钱包类
// WE真人接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

type WESingleService struct {
	apiDomain             string            // api基础接口
	publicKey             string            // 主动请求接口的商户MD5密钥
	privateKey            string            // 回调接口签名的MD5密钥
	channelId             int               // merchantCode商户号
	currency              string            //币种
	homeUrl               string            //跳转URL
	brandName             string            //厂商标识
	games                 map[string]string //游戏类型
	gameTableTypes        map[int]string    //游戏表类型
	RefreshUserAmountFunc func(int) error   // 余额更新回调
}

func NewWESingleService(params map[string]string, fc func(int) error) *WESingleService {
	//游戏类型
	games := map[string]string{
		"weh_live":        "WEH 真人",
		"baccarat":        "百家乐",
		"dragon_tiger":    "龙虎",
		"roulette":        "轮盘",
		"sic_bo":          "骰子游戏",
		"slot":            "老虎机",
		"sport":           "体育",
		"fishing":         "捕鱼",
		"bull_bull":       "牛牛",
		"win_three_cards": "炸金花",
		"gof_baccarat":    "财神百家乐",
		"gof_roulette":    "财神轮盘",
		"gof_sic_bo":      "财神骰宝",
		"fantan":          "番摊",
		"lucky_wheel":     "彩虹幸运轮",
		"mini_game":       "迷你游戏",
		"color_game":      "色彩游戏",
		"one_lucky_9":     "唯一幸运9",
		"hi_lo":           "hi  lo",
		"dropBall":        "DropBall",
	}

	//logs.Info(params)

	//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
	gameTableTypes := map[int]string{
		1: "x_third_dianzhi",
		2: "x_third_qipai",
		3: "x_third_quwei",
		4: "x_third_lottery",
		5: "x_third_live",
		6: "x_third_sport",
	}

	channel_id, err := strconv.Atoi(params["channel_id"])
	//logs.Info("读取到的配置：", " channel_id=", channel_id, " params=", params)
	if err != nil {
		logs.Error("we channel_id转换错误:", err, " params=", params)
	}

	return &WESingleService{
		apiDomain:             params["api_domain"], //API地址
		publicKey:             params["public_key"],
		privateKey:            params["private_key"],
		channelId:             channel_id, //渠道号
		currency:              params["currency"],
		brandName:             "we", //厂商标识
		games:                 games,
		gameTableTypes:        gameTableTypes,
		RefreshUserAmountFunc: fc,
	}
}

const cacheKeyWE = "cacheKeyWE:"

// WE返回错误码
const (
	WE_Code_Success                 = 200  // 成功
	WE_Code_Fail_Has_Exist          = 201  // 订单重复
	WE_Code_Fail_User_Not_Exist     = 4037 // 会员不存在
	WE_Code_Fail_Not_Enough_Balance = 1003 // 余额不足
	WE_Code_Fail_Signature_Error    = 4026 // 签名验证失败
	WE_Code_Fail_Other_Error        = 500  // 其他异常
	WE_Code_Fail_User_Account_Error = 401  // 会员账号有误或冻结
	WE_Code_Fail_Logic_Error        = 500  // 逻辑业务异常
	WE_Code_Fail_Illegal_Parameter  = 4025 // 参数错误
	WE_Code_Fail_System_Error       = 500  // 其他系统错误
	WE_Code_Fail_Channel_Not_Exist  = 202  // 渠道不存在
	WE_Code_Fail_Token_Expired      = 410  // 无效的Token
	WE_Code_Fail_Record_Not_Found   = 208  // 记录不存在
)

// WESupportLang 支持的语言
// zh_tw繁体中文
// zh_cn简体中文
// th_th泰文
// vi_vn越南文
// ko_kr韩文
// ja_jp日文
// in_id印尼文
// pt_pt葡萄牙文
// tl_tl菲律宾文
// my_my缅甸文
// in_in印度文
// es_es西班牙文
// tr_tr土耳其文
var WESupportLang = []string{"en_us", "zh_tw", "zh_cn", "th_th", "vi_vn", "ko_kr", "ja_jp", "in_id", "pt_pt", "tl_tl", "my_my", "in_in", "es_es", "tr_tr"}

func (l *WESingleService) Login(ctx *abugo.AbuHttpContent) {

	type RequestLoginParamsData struct {
		ChannelId     int    `json:"channelId"`     // 渠道ID，用于标识该请求来源的渠道，必填
		Timestamp     int64  `json:"timestamp"`     // 请求时间戳，单位为毫秒，格式为Unix Time。必填，确保请求的时效性
		Signature     string `json:"signature"`     // 签名字段，通常是通过拼接 `username + timestamp` 进行加密生成，必填，用于验证请求的完整性和来源
		ClientIP      string `json:"clientIP"`      // 玩家IP地址，必填，用于记录请求来源的IP地址
		UserName      string `json:"username"`      // 用户名，必填，要求用户名不能包含HTTP保留字符
		TableCode     string `json:"tableCode"`     // 游戏桌ID，用于指定玩家进入的游戏桌ID，必填，必须与 `category` 或 `tableCode` 之一填写
		Category      string `json:"category"`      // 游戏组别，用于指定游戏组别，必填，必须与 `category` 或 `tableCode` 之一填写
		IsDemo        bool   `json:"isDemo"`        // 是否为访客模式，`true` 表示访客模式，`false` 或不填表示正式登录模式，选填
		SubChannelId  int64  `json:"subChannelId"`  // 子渠道ID，选填，用于标识具体的子渠道
		Platform      string `json:"platform"`      // 用户设备类型，表示用户访问的设备类型，可以是 Desktop、Mobile、Unknown 等，选填
		SessionToken  string `json:"sessionToken"`  // sessionToken，用于单一钱包认证，单一钱包认证时必填，转账钱包时非必要
		Language      string `json:"language"`      // 语言，用户语言设置，选填，默认会使用代理的语言
		RedirectUrl   string `json:"redirectUrl"`   // 当进入指定桌号时，点击返回键后的跳转网址，选填，支持多种跳转方式，如 `empty`、`postback`、`histback` 或具体 URL
		UiMode        string `json:"uiMode"`        // 显示方式，指定游戏的显示模式，`auto` 为自动配置，`mobile` 为手机版竖版，`desktop` 为桌面版横版，选填
		BetlimitIndex int    `json:"betlimitindex"` // 默认限红组，指定玩家的游戏限额组，值为 0（第一组限红）、1（第二组限红）、2（第三组限红），选填
	}

	type ResponseData struct {
		Error struct {
			Code int    `json:"code"` // 返回码 0成功，其他失败
			Msg  string `json:"msg"`
			Uuid string `json:"uuid"`
		} `json:"error"`
		RedirectURL string `json:"redirect-url"` // 游戏登陆地址
	}

	type RequestLoginData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}

	type ResponseLoginData struct {
		Status       int    `json:"status"`    // 返回码 200成功，其他失败
		LaunchUrl    string `json:"launchUrl"` // 游戏登陆地址
		ErrorMessage string `json:"errorMessage"`
	}

	type RequestData struct {
		Language    string `json:"language"`    // 语言
		RedirectUrl string `json:"redirectUrl"` // 返回商户地址
		GameId      string `json:"gameId"`
		Platform    string `json:"platform"` // 用户设备类型，表示用户访问的设备类型，可以是 Desktop、Mobile、Unknown 等，选填
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("WE_single1 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("WE_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyWE, userId); err != nil {
		logs.Error("WE_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏Id不存在!")
			return
		}
		logs.Error("WE_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试2")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("WE_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("WE_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试4")
		return
	}

	loginLang := reqdata.Language
	loginHomeUrl := reqdata.RedirectUrl
	loginName := strconv.Itoa(userId)
	loginIp := ctx.GetIp()
	reqParams := RequestLoginParamsData{
		ChannelId: l.channelId,
		Timestamp: time.Now().UnixMilli(),
		ClientIP:  loginIp,
		UserName:  loginName,
		TableCode: reqdata.GameId,
		//Category:		"Live",
		Platform:     reqdata.Platform,
		SessionToken: loginToken,
		Language:     loginLang,
		RedirectUrl:  loginHomeUrl,
	}
	// 将 Timestamp 转换为 string
	timestampStr := strconv.FormatInt(reqParams.Timestamp, 10)
	signKey := reqParams.UserName + timestampStr
	// 调用 RsaSign 函数
	signature := RsaSign(signKey, l.privateKey)
	reqParams.Signature = signature
	reqdataLoginBytes, _ := json.Marshal(reqParams)
	payload := bytes.NewReader(reqdataLoginBytes)
	logs.Info("WE_single 登录游戏 开始 userId=", userId, " url=", l.apiDomain, " reqdata=", string(reqdataLoginBytes))
	url := fmt.Sprintf("%s/api/playerlaunch", l.apiDomain)
	client := &http.Client{}

	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("WE_single 登录游戏 请求错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "网络错误,请稍后再试6")
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("WE_single 登录游戏 读取响应错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}
	logs.Info("WE_single 登录游戏 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respData := ResponseLoginData{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		logs.Error("WE_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试8")
		return
	}

	if respData.Status != 200 {
		logs.Error("WE_single 登录游戏 登录失败 userId=", userId, " 错误码=", respData.Status, " Message=", respData.ErrorMessage)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试9")
		return
	}

	ctx.RespOK(respData.LaunchUrl)
	return
}

func (l *WESingleService) Validate(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ChannelId    int64  `json:"channelId"`    // 渠道ID
		Timestamp    int64  `json:"timestamp"`    // 时间戳，单位：毫秒（Unix Time）
		Signature    string `json:"signature"`    // 签名：使用timestamp和其他信息计算生成的签名
		Event        string `json:"event"`        // API名称
		SessionToken string `json:"sessionToken"` // 用于验证的SessionToken
		IP           string `json:"ip"`           // 玩家IP地址
		SeqNo        string `json:"seqNo"`        // WE的序列号（返回相同的值）
	}
	type ResponseData struct {
		Status    int64    `json:"status"`    // 回应状态，整数类型
		SeqNo     string   `json:"seqNo"`     // WE的序列号，字符串类型，请返回相同的值
		Timestamp int64    `json:"timestamp"` // 时间戳，单位为毫秒（Unix Time），int64 类型
		Username  string   `json:"username"`  // 用户名，字符串类型，不可使用http保留字元
		Currency  string   `json:"currency"`  // 货币代码，字符串类型
		Money     float64  `json:"money"`     // 玩家余额，浮动数字类型
		BetLimit  []string `json:"betLimit"`  // 下注限制，最多三组，字符串数组类型
		Nickname  string   `json:"nickname"`  // 玩家昵称，最大长度 80 字符，字符串类型
	}
	respdata := ResponseData{
		Status: WE_Code_Success,
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single validate 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single validate Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single validate 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	userId, _ := l.getUserByToken(reqdata.SessionToken)
	if userId <= 0 {
		logs.Error("WE_single validate 登录失效，重新登录")
		respdata.Status = WE_Code_Fail_Token_Expired
		ctx.RespJson(respdata)
		return
	}

	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("WE_single validate 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.Status = WE_Code_Fail_User_Not_Exist
		} else {
			respdata.Status = WE_Code_Fail_System_Error
		}
		ctx.RespJson(respdata)
		return
	}

	respdata.SeqNo = reqdata.SeqNo
	respdata.Timestamp = time.Now().UnixMilli()
	respdata.Username = strconv.Itoa(userId)
	respdata.Currency = l.currency
	respdata.Money = userBalance.Amount
	//respdata.BetLimit=[]
	respdata.Nickname = strconv.Itoa(userId)

	ctx.RespJson(respdata)
	return
}

func (l *WESingleService) SyncCredit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ChannelId    int    `json:"channelId"`    // 渠道ID，整数类型。标识请求来源的渠道。
		Username     string `json:"username"`     // 用户名，字符串类型。用户名不可以使用HTTP保留字字符。
		TableType    int64  `json:"tableType"`    // 游戏类型，整数类型。标识游戏的种类或类型。
		Currency     string `json:"currency"`     // 货币代码，字符串类型。通常是国家或地区的货币缩写，如“CNY”表示人民币、“USD”表示美元。
		Event        string `json:"event"`        // API名称，字符串类型。标识此API请求的功能或事件类型。
		Timestamp    int64  `json:"timestamp"`    // 时间戳，单位为毫秒（Unix Time），整数类型。表示请求的时间。
		SessionToken string `json:"sessionToken"` // SessionToken，字符串类型。由渠道提供或随机生成，标识会话。
		SeqNo        string `json:"seqNo"`        // WE的序列号，字符串类型。每个请求的唯一标识符，请返回相同的值。
	}

	type ResponseData struct {
		Username  string  `json:"username"`  // 用户名，字符串类型。不可使用http保留字元。
		Money     float64 `json:"money"`     // 当前金额，浮动类型。表示用户的当前余额。
		Currency  string  `json:"currency"`  // 货币代码，字符串类型。通常是国家或地区的货币缩写，如“CNY”表示人民币。
		Status    int     `json:"status"`    // 响应状态，整数类型。标识请求是否成功，200表示成功。
		Event     string  `json:"event"`     // API 名称，字符串类型。标识此API请求的功能或事件类型，如“syncCredit”。
		SeqNo     string  `json:"seqNo"`     // 序列号，字符串类型。每个请求的唯一标识符，请返回相同的值。
		Timestamp int64   `json:"timestamp"` // 时间戳，单位为毫秒（Unix Time），整数类型。表示请求的时间。
	}
	respdata := ResponseData{
		Status: WE_Code_Success,
		Event:  "syncCredit",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single 获取玩家余额 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	if reqdata.ChannelId != l.channelId {
		logs.Error("WE_single 获取玩家余额 非法的渠道Id l.channelId=", l.channelId, " reqdata.ChannelId=", reqdata.ChannelId)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	userId, _ := l.getUserByToken(reqdata.SessionToken)
	if userId <= 0 {
		logs.Error("WE_single validate 登录失效，重新登录")
		respdata.Status = WE_Code_Fail_Token_Expired
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("WE_single 获取玩家余额 非法的币种 reqdataParam.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("WE_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.Status = WE_Code_Fail_User_Not_Exist
		} else {
			respdata.Status = WE_Code_Fail_System_Error
		}
		ctx.RespJson(respdata)
		return
	}
	// 给Data赋值
	respdata.Username = strconv.Itoa(userBalance.UserId)
	respdata.Money = userBalance.Amount
	respdata.Currency = l.currency
	respdata.SeqNo = reqdata.SeqNo
	respdata.Timestamp = time.Now().UnixMilli()
	ctx.RespJson(respdata)
	logs.Info("WE_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

func (l *WESingleService) NetCheck(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Event string `json:"event"` // API名称
	}
	type ResponseData struct {
		Status     int64  `json:"status"`     // 回应状态，整数类型
		ApiVersion string `json:"apiVersion"` // API版本号
	}
	respdata := ResponseData{
		Status:     WE_Code_Success,
		ApiVersion: "1.3.55",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single NetCheck 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single NetCheck Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single NetCheck 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	ctx.RespJson(respdata)
	return
}

// WithHoldingItem 结构体表示预扣返还项
type WithHoldingItem struct {
	WithHolding   float64 `json:"withHolding"`   // 预扣金额，withHolding = 0 时代表预扣失败
	Status        int     `json:"status"`        // 回应状态
	BetType       int64   `json:"betType"`       // 投注牌型
	WithHoldingID string  `json:"withHoldingId"` // 预扣ID
	SeqNo         string  `json:"seqNo"`         // WE的序列号，返回相同的值
	Refund        float64 `json:"refund"`        // 牛牛返还金额，仅 type: 28 时需要此字段
}

// Selection 结构体表示体育类的投注选择
type Selection struct {
	GameCode string `json:"gameCode"` // 投注球种
	BetCode  int64  `json:"betCode"`  // 投注项目
}

// BetItem 结构体表示投注项
type BetItem struct {
	BetType       int64       `json:"betType"`       // 投注牌型
	BetMoney      float64     `json:"betMoney"`      // 投注金额，当 betMoney = 0 时，视为下注失败
	SeqNo         string      `json:"seqNo"`         // WE的序列号，返回相同的值
	Payout        float64     `json:"payout"`        // 派彩总额
	Number        string      `json:"number"`        // 游戏下注的号码，特定项目才有此字段
	BetId         string      `json:"betId"`         // 注单ID
	SelectionList []Selection `json:"selectionList"` // 仅体育类有此参数
	Odds          float64     `json:"odds"`          // 赔率
	ValidBet      float64     `json:"validBet"`      // 有效投注
	RebateAmount  float64     `json:"rebateAmount"`  // 返水，仅 type: 2 时有效
	JPWinRank     int         `json:"jpWinRank"`     // Jackpot等级，0: NONE, 1: MINI, 2: MINOR, 3: MAJOR, 4: GRAND
	JPWinAmt      float64     `json:"jpWinAmt"`      // Jackpot金额
	OLNActionCode string      `json:"olnActionCode"` // 行为代码，值如 hit, stop
}

// 该API是用于通知渠道更改玩家金额。
// Note 1: seqNo是唯一值。 避免重复处理金額，请勿记录相同的支出seqNo。
// Note 2: 我们有一个retry机制。如果之前已经成功处理过，请向WE返回201（重复seqNo）。
// Note 3: 如果网路发生或是贵方回传系统错误,系统忙碌, 我方会尝试重送3次。
// Note 4: 当betMoney = 0时，视为下注失败。
func (l *WESingleService) IncreaseCredit(ctx *abugo.AbuHttpContent) {

	// Detail 结构体表示响应中的详细数据部分
	type Detail struct {
		TableCode         string            `json:"tableCode"`         // 游戏桌ID
		TableType         int               `json:"tableType"`         // 游戏类型
		TableSubType      int               `json:"tableSubType"`      // 游戏桌类型
		DealerName        string            `json:"dealerName"`        // 荷官名，仅 type: 2 时有效
		BootsCardID       string            `json:"bootsCardId"`       // 靴盒号，仅 type: 2 时有效
		RoundCode         string            `json:"roundCode"`         // 牌局号码
		BetTime           int64             `json:"betTime"`           // 下注时间（Unix时间格式）
		TotalBet          float64           `json:"totalBet"`          // 投注成功的总额
		ValidBet          float64           `json:"validBet"`          // 有效投注
		RebateAmount      float64           `json:"rebateAmount"`      // 返水，仅 type: 2 时有效
		WithholdingSeqNo  string            `json:"withholdingSeqNo"`  // 牛牛预扣投注退款请求序号，仅 type: 11 时需要此字段
		SrcResults        []interface{}     `json:"srcResults"`        // 荷官开牌结果，参见牌面代码
		BrokerageRequired bool              `json:"brokerageRequired"` // 是否免佣金百家乐，仅百家乐有此参数
		Results           []int             `json:"results"`           // 中奖项目
		Payout            float64           `json:"payout"`            // 派彩总额
		PayoutTime        int64             `json:"payoutTime"`        // 派彩时间（Unix时间格式）
		BetList           []BetItem         `json:"betList"`           // 投注列表
		ModifyBetList     []BetItem         `json:"modifyBetList"`     // 修改后的投注列表，仅 type = 37 时有此字段
		EventBonusType    int               `json:"eventBonusType"`    // 活动奖励类型，仅 type: 38 时有此字段
		EventBonusCode    string            `json:"eventBonusCode"`    // 活动奖励名称，仅 type: 38 时有此字段
		BetStopTime       int64             `json:"betStopTime"`       // 下注停止时间，仅派彩事件需要此字段
		MatchID           int               `json:"matchId"`           // 大赛代码，仅 type: 8 或 9 时需要此字段
		WithHoldingList   []WithHoldingItem `json:"withHoldingList"`   // 预扣返还列表
		GameName          string            `json:"gameName"`          // 游戏名称
	}
	type RequestData struct {
		UserName     string  `json:"username"`     // 用户名，不可使用http保留字元。
		ChannelId    int     `json:"channelId"`    // 渠道ID
		Money        float64 `json:"money"`        // 用户当前金额
		Type         int     `json:"type"`         // 交易事务类型
		Platform     int     `json:"platform"`     // 使用设备类型
		Currency     string  `json:"currency"`     // 货币代码
		SeqNo        string  `json:"seqNo"`        // WE的序列号，返回相同的值
		Event        string  `json:"event"`        // API 名称
		Timestamp    int64   `json:"timestamp"`    // 时间戳（Unix Time），单位为毫秒
		SessionToken string  `json:"sessionToken"` // 渠道提供或随机生成的SessionToken
		Signature    string  `json:"signature"`    // 签名，字串拼接: seqNo+event+channelId+timestamp+username+money
		Detail       Detail  `json:"detail"`       // 详细数据，嵌套结构体
	}
	type ResponseData struct {
		UserName    string  `json:"username"`    // 用户名，不可使用http保留字元
		Money       float64 `json:"money"`       // 经过增减后的金额
		MoneyBefore float64 `json:"moneyBefore"` // 玩家原始的金额
		Status      int     `json:"status"`      // 回应状态
		Event       string  `json:"event"`       // API 名称
		SeqNo       string  `json:"seqNo"`       // WE的序列号，返回相同的值
		Timestamp   int64   `json:"timestamp"`   // 时间戳（Unix时间格式，单位为毫秒）
	}
	respdata := ResponseData{
		Status: WE_Code_Success,
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single 下注确认 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single IncreaseCredit Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single IncreaseCredit 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	if reqdata.ChannelId != l.channelId {
		logs.Error("WE_single IncreaseCredit 非法的渠道Id l.channelId=", l.channelId, " reqdata.ChannelId=", reqdata.ChannelId)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	//seqNo+event+channelId+timestamp+username+money
	signKey := reqdata.SeqNo + reqdata.Event + strconv.Itoa(l.channelId) + strconv.FormatInt(reqdata.Timestamp, 10) + reqdata.UserName + strconv.FormatFloat(reqdata.Money, 'f', -1, 64)
	callbackSign := RsaSign(signKey, l.privateKey)
	if !strings.EqualFold(reqdata.Signature, callbackSign) {
		logs.Error("WE_single 下注确认 签名错误 seqNo=", reqdata.SeqNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", callbackSign)
		respdata.Status = WE_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.UserName)
	if err != nil {
		logs.Error("WE_single 下注确认 会员账号错误 seqNo=", reqdata.SeqNo, " reqdata.UserName=", reqdata.UserName, " err=", err.Error())
		respdata.Status = WE_Code_Fail_User_Account_Error
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("WE_single 下注确认 非法的币种 seqNo=", reqdata.SeqNo, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	gameId := reqdata.Detail.TableCode

	gameTableType := ""
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("WE_single 下注确认 获取游戏列表失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
			gameTableType = l.gameTableTypes[gameList.GameType]
		}
	}

	logs.Info(gameName)

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.SeqNo //三方序列号
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 SeqNo=", reqdata.SeqNo, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 SeqNo=", reqdata.SeqNo, " err=", e.Error())
		}
	}()

	// 1	投注	用户进行投注的纪录	当玩家点击确认下注时会记录相关
	// 2	派彩	派彩给用户的纪录	在牌局结束计算后派彩给用户
	// 3	打赏	用户打赏荷官的纪录	在投注期间打赏给荷官
	// 4	轉入	转入金额到用户钱包	在充值api中，金额为正数的纪录
	// 5	轉出	从用户钱包转出金额	在充值api中，金额为负数的纪录
	// 22	下注失败退款	下注失败已经退还的纪录（仅供查询用）	渠道在退款请求中返回已处理，ebet纪录为已退款的状态
	// 51	免費旋轉	免費旋轉派彩	免費旋轉后会发放
	// 52	獎勵遊戲	獎勵遊戲派彩	獎勵遊戲后会发放
	// 61	重新結算	重新結算	當注單重新結算
	// 71	活动派奖	活动派奖	活动派奖

	totalBetAmount := reqdata.Detail.TotalBet
	tablePre := gameTableType + "_pre_order"
	table := gameTableType
	if reqdata.Type == 1 {
		logs.Info("WE_single 下注")
		if totalBetAmount < 0 {
			logs.Error("WE_single 下注确认 下注金额不能为负数 seqNo=", reqdata.SeqNo, " TotalBetAmount=", totalBetAmount, " BetTime=", reqdata.Detail.BetTime)
			respdata.Status = WE_Code_Fail_Illegal_Parameter
			ctx.RespJson(respdata)
			return
		}
		// 开始下注事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single 下注 获取用户余额失败 TransactionId=", reqdata.SeqNo, " err=", e)
				if errors.Is(e, daogorm.ErrRecordNotFound) {
					respdata.Status = WE_Code_Fail_User_Not_Exist
				} else {
					respdata.Status = WE_Code_Fail_System_Error
				}
				return e
			} else {
				respdata.Money = userBalance.Amount
			}
			if totalBetAmount > userBalance.Amount {
				e = errors.New("余额不足")
				respdata.Status = WE_Code_Fail_Not_Enough_Balance
				return e
			}
			respdata.MoneyBefore = userBalance.Amount
			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

			for _, v := range reqdata.Detail.BetList {
				thirdId := v.BetId
				thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

				// 下注金额大于用户余额
				if v.BetMoney > userBalance.Amount {
					logs.Error("WE_single 下注确认 会员余额不足下注金额 终止后面的注单下注 TransactionId=", v.SeqNo, " ThirdId=", thirdId, " v.BetMoney=", v.BetMoney, " userBalance.Amount=", userBalance.Amount)
					return nil
				}
				// 创建注单
				order := thirdGameModel.ThirdOrder{
					// Id: 0,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					UserId:       userBalance.UserId,
					Brand:        l.brandName,
					ThirdId:      thirdId, //临时  后续抓取为真实三方订单号
					ThirdRefId:   &thirdId,
					GameId:       gameId,
					GameName:     gameName,
					BetAmount:    v.BetMoney,
					WinAmount:    0,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     l.currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    -1, //-2-已取消 -1-未开奖 1-已结算
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
						logs.Error("WE_single 下注确认 订单已存在 TransactionId=", v.SeqNo, "  thirdId=", thirdId, " order=", order, " error=", e)
						continue
					}
					logs.Error("WE_single 下注确认 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e)
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, v.BetMoney).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount - ?", v.BetMoney),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.BetMoney != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("WE_single 下注确认 扣款失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " userId=", userId, " v.BetAmount=", v.BetMoney, " err=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				// 创建账变记录
				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userBalance.UserId,
					BeforeAmount: userBalance.Amount,
					Amount:       -v.BetMoney,
					AfterAmount:  userBalance.Amount - v.BetMoney,
					Reason:       utils.BalanceCReasonWEBet,
					Memo:         l.brandName + " bet,thirdId:" + thirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("WE_single 下注确认 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				userBalance.Amount -= v.BetMoney
				respdata.Money = userBalance.Amount
			}
			return nil
		})

		if err != nil {
			logs.Error("WE_single 下注确认 事务处理失败 err=", err)
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][WE_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][WE_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}
		respdata.UserName = strconv.Itoa(userId)
		respdata.Event = "increaseCredit"
		respdata.SeqNo = reqdata.SeqNo
		respdata.Timestamp = time.Now().UnixMilli()

		ctx.RespJson(respdata)
		logs.Info("WE_single 下注确认 响应成功 respdata=", respdata)
		return

	} else if reqdata.Type == 2 {
		logs.Info("WE_single 派彩")

		betIds := l.GetBetIdMap(reqdata.UserName)

		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single 派彩 获取用户余额失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.Status = WE_Code_Fail_User_Not_Exist
				} else {
					respdata.Status = WE_Code_Fail_System_Error
				}
				return e
			} else {
				respdata.MoneyBefore = userBalance.Amount
			}

			for _, v := range reqdata.Detail.BetList {
				thirdId := v.BetId
				thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

				//真实betId
				thirdRefId := betIds[thirdId]
				logs.Info("真实betId ", thirdId, " - ", thirdRefId)

				// 查询注单
				order := thirdGameModel.ThirdOrder{}
				e = tx.Table(tablePre).Where("ThirdRefId=? and Brand=?", thirdId, l.brandName).First(&order).Error
				if e != nil {
					if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
						logs.Error("WE_single 派彩 订单不存在 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
						continue
					}
					logs.Error("WE_single 派彩 查询订单失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				betCtx := l.WEGameRecord2Str(v, strconv.Itoa(userId), gameName, "已派奖")

				if order.DataState != -1 {
					e = errors.New("订单已结算")
					logs.Error("WE_single 派彩 订单已结算 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_Has_Exist
					return e
				}
				// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
				validBet := math.Abs(v.Payout - order.BetAmount)
				if validBet > math.Abs(order.BetAmount) {
					validBet = math.Abs(order.BetAmount)
				}
				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState":  1, // -2-已取消 -1-未开奖 1-已结算
					"ThirdTime":  thirdTime,
					"ValidBet":   validBet,
					"WinAmount":  v.Payout,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
					"ThirdRefId": thirdId,
					"ThirdId":    thirdRefId,
				}).Error
				if e != nil {
					logs.Error("WE_single 派彩 更新订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				//旧账变记录的注单号更改
				e := tx.Table("x_amount_change_log").Where("UserId = ? and Reason=?", userId, utils.BalanceCReasonWEBet).Updates(map[string]interface{}{
					"Memo": l.brandName + " bet,thirdId:" + thirdRefId,
				}).Error
				if e != nil {
					logs.Error("WE_single 派彩 更新下注log注单号失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				order.DataState = 1 // -2-已取消 -1-未开奖 1-已结算
				order.ThirdTime = thirdTime
				order.ValidBet = validBet
				order.WinAmount = v.Payout
				order.BetCtx = betCtx
				order.GameRst = betCtx
				order.BetCtxType = 3 //1-默认类型 2-链接类型 3-json
				order.RawData = string(bodyBytes)
				order.Id = 0
				order.ThirdId = thirdRefId
				order.ThirdRefId = &thirdId
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("WE_single 派彩 创建正式表订单失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				if v.Payout != 0 { //金额不=0才需要更新用户余额
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount + ?", v.Payout),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Payout != 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("WE_single 派彩 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdId, " v.Payout", v.Payout, " err=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
				}
				// 创建账变记录
				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userBalance.UserId,
					BeforeAmount: userBalance.Amount,
					Amount:       v.Payout,
					AfterAmount:  userBalance.Amount + v.Payout,
					Reason:       utils.BalanceCReasonWESettle,
					Memo:         l.brandName + " settle,thirdId:" + thirdRefId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("WE_single 派彩 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				userBalance.Amount += v.Payout

				//返水额度不为0
				if v.RebateAmount > 0 {
					thirdIdRabate := thirdRefId + "_rebate"
					// 创建注单-返水
					orderRebate := thirdGameModel.ThirdOrder{
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						BetChannelId: order.ChannelId,
						UserId:       userBalance.UserId,
						Brand:        l.brandName,
						ThirdId:      thirdIdRabate,
						GameId:       gameId,
						GameName:     gameName + "_返水",
						BetAmount:    0,
						WinAmount:    v.RebateAmount,
						ValidBet:     0,
						ThirdTime:    thirdTime,
						Currency:     l.currency,
						RawData:      string(bodyBytes),
						State:        1,
						Fee:          0,
						DataState:    1,
						CreateTime:   thirdTime,
					}
					e = tx.Table(table).Create(&orderRebate).Error
					if e != nil {
						if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
							logs.Error("WE_single 返水 订单已存在 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " order=", orderRebate, " error=", e)
							continue
						}
						logs.Error("WE_single 返水 创建订单失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " order=", orderRebate, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount + ?", v.RebateAmount),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.RebateAmount != 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("WE_single 返水 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdIdRabate, " v.RebateAmount=", v.RebateAmount, " err=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					// 创建账变记录
					amountLogRabate := thirdGameModel.AmountChangeLog{
						UserId:       userBalance.UserId,
						BeforeAmount: userBalance.Amount,
						Amount:       v.RebateAmount,
						AfterAmount:  userBalance.Amount + v.RebateAmount,
						Reason:       utils.BalanceCReasonWERebate,
						Memo:         l.brandName + " settle,thirdId:" + thirdIdRabate,
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						CreateTime:   thirdTime,
					}
					e = tx.Table("x_amount_change_log").Create(&amountLogRabate).Error
					if e != nil {
						logs.Error("WE_single 返水 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " amountLog=", amountLogRabate, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					userBalance.Amount += v.RebateAmount
				}
				respdata.Money = userBalance.Amount

				// // 推送派奖事件到 CustomerIO
				// if l.thirdGamePush != nil {
				// 	l.thirdGamePush.PushRewardEvent(5, l.brandName, thirdId)
				// }
			}
			return nil
		})

		if err != nil {
			logs.Error("WE_single 派彩 事务处理失败 TransactionId=", reqdata.SeqNo, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][WE_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][WE_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}
		respdata.UserName = strconv.Itoa(userId)
		respdata.Event = "increaseCredit"
		respdata.SeqNo = reqdata.SeqNo
		respdata.Timestamp = time.Now().UnixMilli()

		ctx.RespJson(respdata)
		logs.Info("WE_single 派彩 响应成功 respdata=", respdata)
		return
	} else if reqdata.Type == 3 {
		logs.Info("WE_single 打赏")
		totalBetAmount = -reqdata.Money
		if totalBetAmount < 0 {
			logs.Error("WE_single 打赏 金额不能为负数 TransactionId=", reqdata.SeqNo, " TotalBetAmount=", totalBetAmount, " BetTime=", reqdata.Detail.BetTime)
			respdata.Status = WE_Code_Fail_Illegal_Parameter
			ctx.RespJson(respdata)
			return
		}
		// 开始打赏事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single 打赏 获取用户余额失败 TransactionId=", reqdata.SeqNo, " err=", e)
				if errors.Is(e, daogorm.ErrRecordNotFound) {
					respdata.Status = WE_Code_Fail_User_Not_Exist
				} else {
					respdata.Status = WE_Code_Fail_System_Error
				}
				return e
			} else {
				respdata.Money = userBalance.Amount
			}
			if totalBetAmount > userBalance.Amount {
				e = errors.New("余额不足")
				respdata.Status = WE_Code_Fail_Not_Enough_Balance
				return e
			}
			respdata.MoneyBefore = userBalance.Amount
			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

			thirdId := reqdata.SeqNo
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName + "打赏",
				BetAmount:    0,
				WinAmount:    -totalBetAmount,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    1,
				CreateTime:   thirdTime,
			}
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
					logs.Error("WE_single 打赏 订单已存在  TransactionId=", reqdata.SeqNo, "  thirdId=", thirdId, " order=", order, " error=", e)
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				logs.Error("WE_single 打赏 创建订单失败  TransactionId=", reqdata.SeqNo, "  thirdId=", thirdId, " order=", order, " error=", e)
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, totalBetAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", totalBetAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && totalBetAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("WE_single 打赏 扣款失败  TransactionId=", reqdata.SeqNo, " thirdId=", thirdId, "  userId=", userId, " v.BetAmount=", totalBetAmount, " err=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       -totalBetAmount,
				AfterAmount:  userBalance.Amount - totalBetAmount,
				Reason:       utils.BalanceCReasonWEBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("WE_single 打赏 创建账变记录失败  TransactionId=", reqdata.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			userBalance.Amount -= totalBetAmount
			respdata.Money = userBalance.Amount
			return nil
		})

		if err != nil {
			logs.Error("WE_single 打赏 事务处理失败 err=", err)
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][WE_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][WE_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}
		respdata.UserName = strconv.Itoa(userId)
		respdata.Event = "increaseCredit"
		respdata.SeqNo = reqdata.SeqNo
		respdata.Timestamp = time.Now().UnixMilli()

		ctx.RespJson(respdata)
		logs.Info("WE_single 打赏 响应成功 respdata=", respdata)
		return

	} else if reqdata.Type == 4 {
		logs.Info("WE_single 轉入")
	} else if reqdata.Type == 5 {
		logs.Info("WE_single 轉出")
	} else if reqdata.Type == 22 {
		logs.Info("WE_single 下注失败退款")
	} else if reqdata.Type == 61 {
		logs.Info("WE_single increaseCredit 重新結算")
		// 开始重新結算事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single 重新結算 获取用户余额失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.Status = WE_Code_Fail_User_Not_Exist
				} else {
					respdata.Status = WE_Code_Fail_System_Error
				}
				return e
			} else {
				respdata.MoneyBefore = userBalance.Amount
			}

			for _, v := range reqdata.Detail.BetList {
				thirdId := v.BetId
				thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
				// 查询注单
				order := thirdGameModel.ThirdOrder{}
				e = tx.Table(tablePre).Where("ThirdRefId=? and Brand=?", thirdId, l.brandName).First(&order).Error
				if e != nil {
					if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
						logs.Error("WE_single 重新結算 订单不存在 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
						continue
					}
					logs.Error("WE_single 重新結算 查询订单失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				betCtx := l.WEGameRecord2Str(v, strconv.Itoa(userId), gameName, "重新结算")
				if order.DataState != 1 {
					e = errors.New("订单未结算不能重新结算")
					logs.Error("WE_single 重新結算 订单未结算不能重新结算 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
				validBet := math.Abs(v.Payout - order.BetAmount)
				if validBet > math.Abs(order.BetAmount) {
					validBet = math.Abs(order.BetAmount)
				}
				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"ValidBet":   validBet,
					"WinAmount":  v.Payout,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("WE_single 重新結算 更新订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}

				// 更新注单状态
				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"ThirdTime":  thirdTime,
					"ValidBet":   validBet,
					"WinAmount":  v.Payout,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("WE_single 重新結算 更新订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				if v.Payout != 0 { //金额不=0才需要更新用户余额
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount + ?", v.Payout),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Payout != 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("WE_single 重新結算 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdId, " v.Payout", v.Payout, " err=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
				}
				// 创建账变记录
				amountLog := thirdGameModel.AmountChangeLog{
					UserId:       userBalance.UserId,
					BeforeAmount: userBalance.Amount,
					Amount:       v.Payout,
					AfterAmount:  userBalance.Amount + v.Payout,
					Reason:       utils.BalanceCReasonWEREPAYOUT,
					Memo:         l.brandName + " resettle,thirdId:" + order.ThirdId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("WE_single 重新結算 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				userBalance.Amount += v.Payout
				thirdIdRabate := order.ThirdId + "_rebate"
				orderRebate := thirdGameModel.ThirdOrder{}
				e = tx.Table(table).Where("ThirdId=? and Brand=?", thirdIdRabate, l.brandName).First(&orderRebate).Error
				if e != nil {
					if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
						logs.Error("WE_single 重新結算 返水记录不存在 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
					} else {
						logs.Error("WE_single 重新結算 查询返水记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
				}
				if orderRebate.ThirdId != "" {
					// 更新返水注单状态
					e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdIdRabate, l.brandName, userId).Updates(map[string]interface{}{
						"DataState":  1,
						"ThirdTime":  thirdTime,
						"ValidBet":   0,
						"WinAmount":  v.RebateAmount,
						"BetCtx":     betCtx,
						"GameRst":    betCtx,
						"BetCtxType": 3,
						"RawData":    string(bodyBytes),
					}).Error
					if e != nil {
						logs.Error("WE_single 重新結算 更新返水订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
				} else {
					//新返水注单
					//返水额度不为0
					if v.RebateAmount > 0 {
						thirdIdRabate := thirdId + "_rebate"
						// 创建注单-返水
						orderRebate := thirdGameModel.ThirdOrder{
							SellerId:     userBalance.SellerId,
							ChannelId:    userBalance.ChannelId,
							BetChannelId: order.ChannelId,
							UserId:       userBalance.UserId,
							Brand:        l.brandName,
							ThirdId:      thirdIdRabate,
							GameId:       gameId,
							GameName:     gameName + "_返水",
							BetAmount:    0,
							WinAmount:    v.RebateAmount,
							ValidBet:     0,
							ThirdTime:    thirdTime,
							Currency:     l.currency,
							RawData:      string(bodyBytes),
							State:        1,
							Fee:          0,
							DataState:    1,
							CreateTime:   thirdTime,
						}
						e = tx.Table(table).Create(&orderRebate).Error
						if e != nil {
							if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
								logs.Error("WE_single 重新結算  返水订单已存在 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " order=", orderRebate, " error=", e)
								continue
							}
							logs.Error("WE_single  重新結算 返水 创建订单失败 BetId=", v.SeqNo, " SeqNo=", v.SeqNo, " order=", orderRebate, " error=", e)
							respdata.Status = WE_Code_Fail_System_Error
							return e
						}
					}
				}

				if v.RebateAmount > 0 {
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount + ?", v.RebateAmount),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.RebateAmount != 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("WE_single 重新結算 返水 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdIdRabate, " v.RebateAmount=", v.RebateAmount, " err=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					// 创建账变记录
					amountLogRabate := thirdGameModel.AmountChangeLog{
						UserId:       userBalance.UserId,
						BeforeAmount: userBalance.Amount,
						Amount:       v.RebateAmount,
						AfterAmount:  userBalance.Amount + v.RebateAmount,
						Reason:       utils.BalanceCReasonWERebateREPAYOUT,
						Memo:         l.brandName + " settle,thirdId:" + thirdIdRabate,
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						CreateTime:   thirdTime,
					}
					e = tx.Table("x_amount_change_log").Create(&amountLogRabate).Error
					if e != nil {
						logs.Error("WE_single 重新結算 返水 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " amountLog=", amountLogRabate, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					userBalance.Amount += v.RebateAmount
				}
				respdata.Money = userBalance.Amount
			}

			return nil
		})

		if err != nil {
			logs.Error("WE_single 重新結算 事务处理失败 SeqNo=", reqdata.SeqNo, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][WE_single] 重新結算 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][WE_single] 重新結算 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.UserName = strconv.Itoa(userId)
		respdata.Event = "increaseCredit"
		respdata.SeqNo = reqdata.SeqNo
		respdata.Timestamp = time.Now().UnixMilli()

		ctx.RespJson(respdata)
		logs.Info("WE_single 重新結算 响应成功 respdata=", respdata)
		return

	} else if reqdata.Type == 51 || reqdata.Type == 52 || reqdata.Type == 71 {
		typeName := "免費旋轉派彩"
		reasonType := utils.BalanceCReasonWEReward1
		if reqdata.Type == 52 {
			typeName = "獎勵遊戲派彩"
			reasonType = utils.BalanceCReasonWEReward2
		}
		if reqdata.Type == 71 {
			typeName = "活动派奖"
			reasonType = utils.BalanceCReasonWEReward3
		}

		logs.Info("WE_single " + typeName)

		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single "+typeName+" 获取用户余额失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.Status = WE_Code_Fail_User_Not_Exist
				} else {
					respdata.Status = WE_Code_Fail_System_Error
				}
				return e
			} else {
				respdata.MoneyBefore = userBalance.Amount
			}
			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
			for _, v := range reqdata.Detail.BetList {
				thirdId := v.BetId
				thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
				rewardAmount := v.Payout

				//派彩额度不为0
				if rewardAmount > 0 {
					// 创建注单-返水
					orderRebate := thirdGameModel.ThirdOrder{
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						BetChannelId: ChannelId,
						UserId:       userBalance.UserId,
						Brand:        l.brandName,
						ThirdId:      thirdId,
						GameId:       gameId,
						GameName:     gameName + "_" + typeName,
						BetAmount:    0,
						WinAmount:    rewardAmount,
						ValidBet:     0,
						ThirdTime:    thirdTime,
						Currency:     l.currency,
						RawData:      string(bodyBytes),
						State:        1,
						Fee:          0,
						DataState:    1,
						CreateTime:   thirdTime,
					}
					e = tx.Table(table).Create(&orderRebate).Error
					if e != nil {
						if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
							logs.Error("WE_single "+typeName+"  订单已存在 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", orderRebate, " error=", e)
							continue
						}
						logs.Error("WE_single "+typeName+"  创建订单失败 BetId=", v.SeqNo, " SeqNo=", v.SeqNo, " order=", orderRebate, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"Amount": daogorm.Expr("Amount + ?", rewardAmount),
					})
					e = resultTmp.Error
					if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && rewardAmount != 0 {
						e = errors.New("更新条数0")
					}
					if e != nil {
						logs.Error("WE_single "+typeName+"  加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdId, " v.rewardAmount=", rewardAmount, " err=", e.Error())
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					// 创建账变记录
					amountLog := thirdGameModel.AmountChangeLog{
						UserId:       userBalance.UserId,
						BeforeAmount: userBalance.Amount,
						Amount:       rewardAmount,
						AfterAmount:  userBalance.Amount + rewardAmount,
						Reason:       reasonType,
						Memo:         l.brandName + " settle,thirdId:" + thirdId + ",t:" + v.SeqNo,
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						CreateTime:   thirdTime,
					}
					e = tx.Table("x_amount_change_log").Create(&amountLog).Error
					if e != nil {
						logs.Error("WE_single "+typeName+"  创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
					userBalance.Amount += rewardAmount
				}
				respdata.Money = userBalance.Amount
			}

			return nil
		})

		if err != nil {
			logs.Error("WE_single "+typeName+"  事务处理失败 SeqNo=", reqdata.SeqNo, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][WE_single] "+typeName+"  发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][WE_single] " + typeName + "  发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.UserName = strconv.Itoa(userId)
		respdata.Event = "increaseCredit"
		respdata.SeqNo = reqdata.SeqNo
		respdata.Timestamp = time.Now().UnixMilli()

		ctx.RespJson(respdata)
		logs.Info("WE_single "+typeName+" 响应成功 respdata=", respdata)
		return

	} else {
		logs.Error("DG_single  Transfer 非法的TransferType TransactionId=", reqdata.SeqNo, " TransferType=", reqdata.Type)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
}

// 此API是WE对失败投注進行手动退款请求。
// 备注:1.rollback傳送失敗不會再傳送，請確保線路暢通
func (l *WESingleService) RefundSingleWallet(ctx *abugo.AbuHttpContent) {

	// RefundItem 结构体表示单个退款项目
	type RefundItem struct {
		BetType     int64   `json:"betType"`     // 投注牌型
		RefundSeqNo string  `json:"refundSeqNo"` // 退款投注序列号
		UserName    string  `json:"username"`    // 用户名
		RoundCode   string  `json:"roundCode"`   // 牌局号码
		RefundMoney float64 `json:"refundMoney"` // 退款金额（负数表示退款）
	}

	// Request 结构体表示整个请求体
	type RequestData struct {
		ChannelId    int          `json:"channelId"`    // 渠道ID
		UserName     string       `json:"username"`     // 用户名
		RoundCode    string       `json:"roundCode"`    // 牌局号码
		SeqNo        string       `json:"seqNo"`        // WE的序列号
		Event        string       `json:"event"`        // API 名称
		Timestamp    int64        `json:"timestamp"`    // 时间戳
		SessionToken string       `json:"sessionToken"` // 会话令牌
		Currency     string       `json:"currency"`     // 货币代码
		Signature    string       `json:"signature"`    // 签名
		RefundList   []RefundItem `json:"refundList"`   // 退款列表
	}

	// RefundResult 结构体表示退款处理的状态
	type RefundResult struct {
		RefundSeqNo string `json:"refundSeqNo"` // 退款投注序列号
		Status      int    `json:"status"`      // 退款处理的状态
	}

	// Response 结构体表示整个响应数据
	type ResponseData struct {
		Status      int            `json:"status"`      // 回应状态
		SeqNo       string         `json:"seqNo"`       // WE的序列号
		Event       string         `json:"event"`       // API 名称
		Timestamp   int64          `json:"timestamp"`   // 时间戳
		MoneyBefore float64        `json:"moneyBefore"` // 玩家余额（退款前）
		MoneyAfter  float64        `json:"moneyAfter"`  // 玩家余额（退款后）
		RefundMoney float64        `json:"refundMoney"` // 退款金额
		ResultList  []RefundResult `json:"resultList"`  // 退款处理状态列表
	}
	respdata := ResponseData{
		Status: WE_Code_Success,
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single RefundSingleWallet 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single RefundSingleWallet Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single RefundSingleWallet 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	if reqdata.ChannelId != l.channelId {
		logs.Error("WE_single RefundSingleWallet 渠道Id不正确 reqdata.ChannelId=", reqdata.ChannelId, " l.channelId=", l.channelId)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	signKey := reqdata.UserName + strconv.FormatInt(reqdata.Timestamp, 10)
	callbackSign := RsaSign(signKey, l.privateKey)
	if !strings.EqualFold(reqdata.Signature, callbackSign) {
		logs.Error("WE_single RefundSingleWallet 签名错误 TransactionId=", reqdata.SeqNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", callbackSign)
		respdata.Status = WE_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.UserName)
	if err != nil {
		logs.Error("WE_single RefundSingleWallet 用户名转换错误 TransactionId=", reqdata.SeqNo, " reqdata.LoginName=", reqdata.UserName, " err=", err.Error())
		respdata.Status = WE_Code_Fail_User_Account_Error
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("WE_single RefundSingleWallet 非法的币种 TransactionId=", reqdata.SeqNo, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.SeqNo //三方序列号
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 TransactionId=", reqdata.SeqNo, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 TransactionId=", reqdata.SeqNo, " err=", e.Error())
		}
	}()

	// 创建一个空的 ResultList 切片
	var resultList []RefundResult
	var totalRefundMoney float64

	tablePre := ""
	tablePro := ""

	betIds := l.GetBetIdMap(reqdata.UserName)

	// 开始手动退款事务,不退赢的
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("WE_single RefundSingleWallet 获取用户余额失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", e)
			if e == daogorm.ErrRecordNotFound {
				respdata.Status = WE_Code_Fail_User_Not_Exist
			} else {
				respdata.Status = WE_Code_Fail_System_Error
			}
			return e
		} else {
			respdata.MoneyBefore = userBalance.Amount
		}

		for _, v := range reqdata.RefundList {
			thirdId := fmt.Sprintf("%s-%d", v.RefundSeqNo, v.BetType)
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

			//真实betId
			thirdRefId := betIds[thirdId]
			logs.Info("真实betId ", thirdId, " - ", thirdRefId)

			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
			tablePrefixes := []string{"x_third_live_pre_order", "x_third_dianzhi_pre_order", "x_third_quwei_pre_order", "x_third_qipai_pre_order", "x_third_lottery_pre_order", "x_third_sport_pre_order"}
			var foundOrder bool
			for _, tablePre1 := range tablePrefixes {
				e := tx.Table(tablePre1).Where("ThirdRefId=? and Brand=?", thirdId, l.brandName).First(&order).Error
				if e != nil {
					if e == daogorm.ErrRecordNotFound {
						continue
					}
					logs.Error("WE_single RefundSingleWallet 查询订单失败 TransactionId=", v.RefundSeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				// If order is found, set the flag to true and return the order
				foundOrder = true
				tablePre = tablePre1
				tablePro = strings.Replace(tablePre, "_pre_order", "", 1)
				break
			}

			// If no order is found after querying all tables, return an error
			if !foundOrder {
				e = errors.New("订单不存在")
				logs.Info("WE_single RefundSingleWallet 订单不存在 TransactionId=", v.RefundSeqNo, " thirdId=", thirdId, " error=Order not found after checking all tables")
				respdata.Status = WE_Code_Fail_Record_Not_Found
				return e
			}

			//不能退赢的
			if order.DataState == 1 {
				if order.WinAmount > 0 {
					e = errors.New("不能退款已结算赢的订单")
					logs.Error("WE_single RefundSingleWallet 不能退款已结算赢的订单 TransactionId=", v.RefundSeqNo, "  thirdId=", order.ThirdId, " order=", order, " error=", e.Error())
					respdata.Status = WE_Code_Fail_Logic_Error
					return e
				}
			} else if order.DataState == -2 {
				e = errors.New("注单已取消")
				logs.Error("WE_single RefundSingleWallet 注单已取消 TransactionId=", v.RefundSeqNo, "  thirdId=", order.ThirdId, " order=", order, " error=", e.Error())
				respdata.Status = 209 //209: 渠道已自行退款
				return e
			} else {
				e = errors.New("注单状态异常")
				logs.Error("WE_single RefundSingleWallet 注单状态异常 TransactionId=", v.RefundSeqNo, "  thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Status = WE_Code_Fail_Logic_Error
				return e
			}

			refundMoney := -v.RefundMoney
			if refundMoney != order.BetAmount {
				e = errors.New("取消金额与下注金额不一致")
				logs.Error("WE_single RefundSingleWallet 取消金额与下注金额不一致 TransactionId=", v.RefundSeqNo, "  thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Status = WE_Code_Fail_Logic_Error
				return e
			}
			betCtx := gorm.Expr(`JSON_SET(BetCtx, '$."订单状态"', ?)`, "取消下注")
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState":      -2,
				"BetAmount":      0,
				"WinAmount":      0,
				"ValidBet":       0,
				"ValidBetAmount": 0,
				"ThirdTime":      thirdTime,
				"RawData":        string(bodyBytes),
				"BetCtx":         betCtx,
				"GameRst":        betCtx,
				"ThirdId":        thirdRefId,
			}).Error
			if e != nil {
				logs.Error("WE_single RefundSingleWallet 更新订单状态失败 TransactionId=", v.RefundSeqNo, "   thirdId=", thirdRefId, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}

			//旧账变记录的注单号更改
			e := tx.Table("x_amount_change_log").Where("UserId = ? and Reason=?", userId, utils.BalanceCReasonWEBet).Updates(map[string]interface{}{
				"Memo": l.brandName + " bet,thirdId:" + thirdRefId,
			}).Error
			if e != nil {
				logs.Error("WE_single 派彩 更新下注log注单号失败 TransactionId=", v.RefundSeqNo, " thirdId=", thirdRefId, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}

			// 更新正式注单状态
			e = tx.Table(tablePro).Where("ThirdId=? and Brand=? and UserId=?", order.ThirdId, l.brandName, userId).Updates(map[string]interface{}{
				"DataState":      -2,
				"BetAmount":      0,
				"WinAmount":      0,
				"ValidBet":       0,
				"ValidBetAmount": 0,
				"ThirdTime":      thirdTime,
				"RawData":        string(bodyBytes),
				"BetCtx":         betCtx,
				"GameRst":        betCtx,
			}).Error
			if e != nil {
				logs.Error("WE_single RefundSingleWallet 更新正式订单状态失败 TransactionId=", v.RefundSeqNo, "   thirdId=", thirdRefId, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", refundMoney),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && refundMoney != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("WE_single RefundSingleWallet 加款失败 TransactionId=", v.RefundSeqNo, "  thirdId=", thirdId, " userId=", userId, " RefundMoney=", refundMoney, " err=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       refundMoney,
				AfterAmount:  userBalance.Amount + refundMoney,
				Reason:       utils.BalanceCReasonWECancel,
				Memo:         l.brandName + " cancel,thirdId:" + thirdRefId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("WE_single RefundSingleWallet 创建账变记录失败 TransactionId=", v.RefundSeqNo, "  thirdId=", thirdRefId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			userBalance.Amount += refundMoney
			respdata.MoneyAfter = userBalance.Amount
			totalRefundMoney += refundMoney
			refundResult := RefundResult{
				RefundSeqNo: v.RefundSeqNo,
				Status:      200, //200: 渠道已收到WE请求并已对其进行处理
			}

			resultList = append(resultList, refundResult)
		}

		return nil
	})

	if err != nil {
		logs.Error("WE_single RefundSingleWallet 事务处理失败 TransactionId=", reqdata.SeqNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][WE_single] RefundSingleWallet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][WE_single] RefundSingleWallet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}
	respdata.SeqNo = reqdata.SeqNo
	respdata.Event = "refundSingleWallet"
	respdata.Timestamp = time.Now().UnixMilli()
	respdata.RefundMoney = totalRefundMoney
	respdata.ResultList = resultList
	ctx.RespJson(respdata)
	logs.Info("WE_single RefundSingleWallet 响应成功 TransactionId=", reqdata.SeqNo, " respdata=", respdata)
}

// 该API是注单结算后，WE判断需更改游戏结果时，回传该局必须修正的金额。
// https://api-doc.unweg.com/resettlement
func (l *WESingleService) Resettlement(ctx *abugo.AbuHttpContent) {

	type Selection struct {
		GameCode string `json:"gameCode"` // 游戏代码
		BetCode  int    `json:"betCode"`  // 投注项代码
	}

	type Withholding struct {
		WithHolding   float64 `json:"withHolding"`   // 预扣金额
		Status        int     `json:"status"`        // 状态
		BetType       int     `json:"betType"`       // 投注牌型
		WithHoldingId string  `json:"withHoldingId"` // 预扣ID
		SeqNo         string  `json:"seqNo"`         // 序列号
		Refund        float64 `json:"refund"`        // 返还金额
	}

	type EventDetail struct {
		TableCode         string        `json:"tableCode"`         // 游戏桌ID
		TableType         int           `json:"tableType"`         // 游戏类型
		TableSubType      int           `json:"tableSubType"`      // 游戏桌类型
		DealerName        string        `json:"dealerName"`        // 荷官名
		BootsCardId       string        `json:"bootsCardId"`       // 靴盒号
		RoundCode         string        `json:"roundCode"`         // 牌局号码
		BetTime           int64         `json:"betTime"`           // 下注时间（毫秒）
		TotalBet          float64       `json:"totalBet"`          // 投注总额
		ValidBet          float64       `json:"validBet"`          // 有效投注
		RebateAmount      float64       `json:"rebateAmount"`      // 返水金额
		WithholdingSeqNo  string        `json:"withholdingSeqNo"`  // 牛牛预扣序号
		SrcResults        []*int        `json:"srcResults"`        // 荷官开牌结果（允许 null）
		BrokerageRequired bool          `json:"brokerageRequired"` // 是否为免佣百家乐
		Results           []int         `json:"results"`           // 中奖项目
		Payout            float64       `json:"payout"`            // 派彩金额
		PayoutTime        int64         `json:"payoutTime"`        // 派彩时间
		BetList           []BetItem     `json:"betList"`           // 投注列表
		ModifyBetList     []BetItem     `json:"modifyBetList"`     // 修改注单列表
		EventBonusType    int           `json:"eventBonusType"`    // 活动奖励类型
		EventBonusCode    string        `json:"eventBonusCode"`    // 活动奖励代码
		BetStopTime       int64         `json:"betStopTime"`       // 停止下注时间
		MatchId           int           `json:"matchId"`           // 比赛ID
		WithHoldingList   []Withholding `json:"withHoldingList"`   // 预扣返还列表
		GameName          string        `json:"gameName"`          // 游戏名称
	}

	type RequestData struct {
		UserName     string      `json:"username"`     // 用户名
		ChannelId    int         `json:"channelId"`    // 渠道ID
		Money        float64     `json:"money"`        // 当前金额
		Type         int         `json:"type"`         // 钱包类型
		Platform     int         `json:"platform"`     // 使用装置
		Currency     string      `json:"currency"`     // 货币代码
		SeqNo        string      `json:"seqNo"`        // 序列号（必须回传）
		Event        string      `json:"event"`        // API 名称
		Timestamp    int64       `json:"timestamp"`    // 时间戳（毫秒）
		SessionToken string      `json:"sessionToken"` // 会话令牌
		Signature    string      `json:"signature"`    // 签名字段, 字串拼接:seqNo+event+channelId+timestamp+username+money
		Detail       EventDetail `json:"detail"`       // 派彩详情
	}

	type ResponseData struct {
		UserName    string  `json:"username"`    // 用户名。不可使用 http 保留字符
		Money       float64 `json:"money"`       // 当前金额
		MoneyBefore float64 `json:"moneyBefore"` // 变动前金额
		Status      int     `json:"status"`      // 回应状态
		Event       string  `json:"event"`       // API 名称（例如 increaseCredit）
		SeqNo       string  `json:"seqNo"`       // 序列号，需返回相同值
		Timestamp   int64   `json:"timestamp"`   // 时间戳，毫秒单位 Unix 时间
	}
	respdata := ResponseData{
		Status: WE_Code_Success,
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("WE_single 重新结算 读取请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Other_Error
		ctx.RespJson(respdata)
		return
	}
	logs.Info("WE_single 重新结算 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("WE_single IncreaseCredit 解析请求消息体错误 err=", err.Error())
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	if reqdata.ChannelId != l.channelId {
		logs.Error("WE_single 重新结算 非法的渠道Id l.channelId=", l.channelId, " reqdata.ChannelId=", reqdata.ChannelId)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	//seqNo+event+channelId+timestamp+username+money
	signKey := reqdata.SeqNo + reqdata.Event + strconv.Itoa(l.channelId) + strconv.FormatInt(reqdata.Timestamp, 10) + reqdata.UserName + strconv.FormatFloat(reqdata.Money, 'f', -1, 64)
	callbackSign := RsaSign(signKey, l.privateKey)
	if !strings.EqualFold(reqdata.Signature, callbackSign) {
		logs.Error("WE_single 重新结算 签名错误 TransactionId=", reqdata.SeqNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", callbackSign)
		respdata.Status = WE_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.UserName)
	if err != nil {
		logs.Error("WE_single 重新结算 会员账号错误 TransactionId=", reqdata.SeqNo, " reqdata.UserName=", reqdata.UserName, " err=", err.Error())
		respdata.Status = WE_Code_Fail_User_Account_Error
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("WE_single 重新结算 非法的币种 TransactionId=", reqdata.SeqNo, " reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.Status = WE_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	gameId := reqdata.Detail.TableCode

	gameTableType := ""
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("WE_single 重新结算 获取游戏列表失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
			gameTableType = l.gameTableTypes[gameList.GameType]
		}
	}

	logs.Info(gameName)

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.SeqNo //三方序列号
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.brandName, " 检测到重复请求 SeqNo=", reqdata.SeqNo, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 SeqNo=", reqdata.SeqNo, " err=", e.Error())
		}
	}()

	tablePre := gameTableType + "_pre_order"
	table := gameTableType
	logs.Info("WE_single 重新結算")
	// 开始重新结算事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("WE_single 重新結算 获取用户余额失败 TransactionId=", reqdata.SeqNo, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Status = WE_Code_Fail_User_Not_Exist
			} else {
				respdata.Status = WE_Code_Fail_System_Error
			}
			return e
		} else {
			respdata.MoneyBefore = userBalance.Amount
		}
		for _, v := range reqdata.Detail.BetList {
			thirdId := v.BetId
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdRefId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("WE_single 重新結算 订单不存在 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					continue
				}
				logs.Error("FBLive_single 重新結算 查询订单失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			betCtx := l.WEGameRecord2Str(v, strconv.Itoa(userId), gameName, "重新结算")
			if order.DataState != 1 {
				e = errors.New("订单未结算不能重新结算")
				logs.Error("WE_single 重新結算 订单未结算不能重新结算 TransactionId=", v.SeqNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(v.Payout - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  v.Payout,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("WE_single 重新結算 更新订单状态失败 TransactionId=", v.SeqNo, " thirdId=", order.ThirdId, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			// 更新注单状态
			e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", order.ThirdId, l.brandName, userId).Updates(map[string]interface{}{
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  v.Payout,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("WE_single 重新結算 更新订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			if v.Payout != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", v.Payout),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.Payout != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("WE_single 重新結算 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdId, " v.Payout", v.Payout, " err=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v.Payout,
				AfterAmount:  userBalance.Amount + v.Payout,
				Reason:       utils.BalanceCReasonWEREPAYOUT,
				Memo:         l.brandName + " resettle,thirdId:" + order.ThirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("WE_single 重新結算 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Status = WE_Code_Fail_System_Error
				return e
			}
			userBalance.Amount += v.Payout
			thirdIdRabate := order.ThirdId + "_rebate"
			orderRebate := thirdGameModel.ThirdOrder{}
			e = tx.Table(table).Where("ThirdId=? and Brand=?", thirdIdRabate, l.brandName).First(&orderRebate).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("WE_single 重新結算 返水记录不存在 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
				} else {
					logs.Error("WE_single 重新結算 查询返水记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
			}
			if orderRebate.ThirdId != "" {
				// 更新返水注单状态
				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdIdRabate, l.brandName, userId).Updates(map[string]interface{}{
					"DataState":  1,
					"ThirdTime":  thirdTime,
					"ValidBet":   0,
					"WinAmount":  v.RebateAmount,
					"BetCtx":     betCtx,
					"GameRst":    betCtx,
					"BetCtxType": 3,
					"RawData":    string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("WE_single 重新結算 更新返水订单状态失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " error=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
			} else {
				//新返水注单
				//返水额度不为0
				if v.RebateAmount > 0 {
					// 创建注单-返水
					orderRebate := thirdGameModel.ThirdOrder{
						SellerId:     userBalance.SellerId,
						ChannelId:    userBalance.ChannelId,
						BetChannelId: order.ChannelId,
						UserId:       userBalance.UserId,
						Brand:        l.brandName,
						ThirdId:      thirdIdRabate,
						GameId:       gameId,
						GameName:     gameName + "_返水",
						BetAmount:    0,
						WinAmount:    v.RebateAmount,
						ValidBet:     0,
						ThirdTime:    thirdTime,
						Currency:     l.currency,
						RawData:      string(bodyBytes),
						State:        1,
						Fee:          0,
						DataState:    1,
						CreateTime:   thirdTime,
					}
					e = tx.Table(table).Create(&orderRebate).Error
					if e != nil {
						if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
							logs.Error("WE_single 重新結算  返水订单已存在 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " order=", orderRebate, " error=", e)
							continue
						}
						logs.Error("WE_single  重新結算 返水 创建订单失败 BetId=", v.SeqNo, " SeqNo=", v.SeqNo, " order=", orderRebate, " error=", e)
						respdata.Status = WE_Code_Fail_System_Error
						return e
					}
				}
			}
			if v.RebateAmount > 0 {
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", v.RebateAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.RebateAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("WE_single 重新結算 返水 加扣款失败 TransactionId=", v.SeqNo, " userId=", userId, " thirdId=", thirdIdRabate, " v.RebateAmount=", v.RebateAmount, " err=", e.Error())
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				// 创建账变记录
				amountLogRabate := thirdGameModel.AmountChangeLog{
					UserId:       userBalance.UserId,
					BeforeAmount: userBalance.Amount,
					Amount:       v.RebateAmount,
					AfterAmount:  userBalance.Amount + v.RebateAmount,
					Reason:       utils.BalanceCReasonWERebateREPAYOUT,
					Memo:         l.brandName + " resettle,thirdId:" + thirdIdRabate,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLogRabate).Error
				if e != nil {
					logs.Error("WE_single 重新結算 返水 创建账变记录失败 TransactionId=", v.SeqNo, " thirdId=", thirdIdRabate, " amountLog=", amountLogRabate, " error=", e)
					respdata.Status = WE_Code_Fail_System_Error
					return e
				}
				userBalance.Amount += v.RebateAmount
			}
			respdata.Money = userBalance.Amount
		}
		return nil
	})

	if err != nil {
		logs.Error("WE_single 重新結算 事务处理失败 TransactionId=", reqdata.SeqNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][WE_single] 重新結算 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][WE_single] 重新結算 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.UserName = strconv.Itoa(userId)
	respdata.Event = "resettlement"
	respdata.SeqNo = reqdata.SeqNo
	respdata.Timestamp = time.Now().UnixMilli()

	ctx.RespJson(respdata)
	logs.Info("WE_single 重新結算 响应成功 respdata=", respdata)
}

func (l *WESingleService) WEGameRecord2Str(data BetItem, userId string, gameName string, status string) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"渠道订单编号\":\"%s\",", data.BetId))
	sb.WriteString(fmt.Sprintf("\"玩家账号\":\"%s\",", userId))
	sb.WriteString(fmt.Sprintf("\"投注额\":%g,", data.BetMoney))
	sb.WriteString(fmt.Sprintf("\"有效投注额\":%g,", data.ValidBet))
	sb.WriteString(fmt.Sprintf("\"返奖金额\":%g,", data.Payout))
	sb.WriteString(fmt.Sprintf("\"返水金额\":%g,", data.RebateAmount))
	sb.WriteString(fmt.Sprintf("\"输赢金额\":%g,", data.Payout-data.BetMoney))
	sb.WriteString(fmt.Sprintf("\"游戏名\":\"%s\",", gameName))
	sb.WriteString(fmt.Sprintf("\"投注牌型\":\"%d\",", data.BetType))
	sb.WriteString(fmt.Sprintf("\"下注号码\":\"%s\",", data.Number))
	sb.WriteString(fmt.Sprintf("\"赔率\":\"%g\",", data.Odds))
	sb.WriteString(fmt.Sprintf("\"订单状态\":\"%s\"", status))
	sb.WriteString("}")
	res = sb.String()
	return
}

// 生成WE token
func (l *WESingleService) getTokenByUser(userId int, account string) (token string) {
	type WETokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyWE + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyWE + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("WE_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("WE_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "WE_" + uuid.NewString()
	tokendata := WETokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyWE + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("WE_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("WE_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *WESingleService) getUserByToken(token string) (userId int, account string) {
	type WETokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyWE + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := WETokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("WE_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyWE + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("WE_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("WE_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("WE_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// RsaSign 用于生成 RSA 签名
func RsaSign(data string, privatekey string) string {
	// 清除私钥的头尾标记
	privatekey = strings.Replace(privatekey, "-----BEGIN PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----END PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----BEGIN RSA PRIVATE KEY-----", "", -1)
	privatekey = strings.Replace(privatekey, "-----END RSA PRIVATE KEY-----", "", -1)

	// 将私钥解码成字节
	privatekeybase64, errb := base64.StdEncoding.DecodeString(privatekey)
	if errb != nil {
		logs.Error("Error decoding private key:", errb)
		return ""
	}

	// 解析 PKCS8 格式的 RSA 私钥
	privatekeyx509, errc := x509.ParsePKCS8PrivateKey(privatekeybase64)
	if errc != nil {
		logs.Error("Error parsing private key:", errc)
		return ""
	}

	// 步骤 1：计算 MD5 哈希
	hashmd5 := md5.Sum([]byte(data))
	hashed := hashmd5[:]

	// 步骤 2：使用私钥对 MD5 哈希值进行签名
	sign, errd := rsa.SignPKCS1v15(rand.Reader, privatekeyx509.(*rsa.PrivateKey), crypto.MD5, hashed)
	if errd != nil {
		logs.Error("Error signing:", errd)
		return ""
	}

	// 步骤 3：返回 Base64 编码的签名
	return base64.StdEncoding.EncodeToString(sign)
}

// 验证签名方法
func RsaVerifySign(data, signature, publicKey string) error {
	// 清理公钥格式
	publicKey = strings.Replace(publicKey, "-----BEGIN PUBLIC KEY-----", "", -1)
	publicKey = strings.Replace(publicKey, "-----END PUBLIC KEY-----", "", -1)

	// 将公钥解码
	publicKeyBytes, err := base64.StdEncoding.DecodeString(publicKey)
	if err != nil {
		return err
	}

	// 解析公钥
	pubKey, err := x509.ParsePKIXPublicKey(publicKeyBytes)
	if err != nil {
		return err
	}

	rsaPubKey, ok := pubKey.(*rsa.PublicKey)
	if !ok {
		return errors.New("公钥解析错误")
	}

	// 对数据进行 SHA-256 哈希
	hash := sha256.New()
	hash.Write([]byte(data))
	hashed := hash.Sum(nil)

	// 解码签名
	decodedSign, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return err
	}

	// 使用 RSA 公钥验证签名
	err = rsa.VerifyPKCS1v15(rsaPubKey, crypto.SHA256, hashed, decodedSign)
	if err != nil {
		return fmt.Errorf("签名验证失败: %v", err)
	}

	return nil
}

// 获取每个注单的真实投注Id
// https://api-doc.unweg.com/userbethistory
func (l *WESingleService) GetBetIdMap(userName string) map[string]string {
	type BetMapItem struct {
		BetType  int     `json:"betType"`
		BetMoney float64 `json:"betMoney"`
	}

	type BetHistory struct {
		BetId        string       `json:"betId"`
		BetHistoryId string       `json:"betHistoryId"`
		BetMap       []BetMapItem `json:"betMap"`
	}

	type BetHistoryResponse struct {
		Status       int          `json:"status"`
		BetHistories []BetHistory `json:"betHistories"`
		ErrorMessage string       `json:"errorMessage"`
	}

	type RequestParams struct {
		ChannelId int    `json:"channelId"`
		Timestamp int64  `json:"timestamp"`
		Signature string `json:"signature"`
		UserName  string `json:"username"`
		PageSize  int    `json:"pageSize"`
		Version   string `json:"version"`
	}

	// 签名构建
	timestamp := time.Now().UnixMilli()
	signKey := userName + strconv.FormatInt(timestamp, 10)
	signature := RsaSign(signKey, l.privateKey)

	// 请求参数
	reqParams := RequestParams{
		ChannelId: l.channelId,
		Timestamp: timestamp,
		Signature: signature,
		UserName:  userName,
		PageSize:  50,
		Version:   "0",
	}

	reqBytes, _ := json.Marshal(reqParams)
	payload := bytes.NewReader(reqBytes)
	url := fmt.Sprintf("%s/api/userbethistory", l.apiDomain)

	logs.Info("历史注单获取 reqdata=", string(reqBytes))

	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("WE_single 获取注单ID 请求失败：", err)
		return nil
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("WE_single 获取注单ID 读取响应失败：", err)
		return nil
	}
	logs.Info("WE_single 获取注单ID 请求成功  respBytes=", string(respBytes))

	respData := BetHistoryResponse{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		logs.Error("WE_single 获取注单ID 解析响应消息体错误  err=", err.Error())
		return nil
	}

	if respData.Status != 200 {
		logs.Warn("WE_single 获取注单ID 返回状态异常：", respData.Status, " msg=", respData.ErrorMessage)
		return nil
	}
	result := make(map[string]string)
	for _, history := range respData.BetHistories {
		if len(history.BetMap) > 0 {
			key := fmt.Sprintf("%s-%d", history.BetId, history.BetMap[0].BetType)
			result[key] = history.BetHistoryId
		}
	}

	return result
}
