package tiktok

const (
	EventName_CompleteRegistration = "CompleteRegistration" // 完成注册
	EventName_Purchase             = "CompletePayment"      // 完成购买或结账流程
	EventName_AddToCart            = "AddToCart"            // 添加到购物车
	EventName_FirstRecharge        = "FirstRecharge"        // 首次充值
)

type PostData struct {
	EventSource   string `json:"event_source"`
	EventSourceId string `json:"event_source_id"`
	Data          []Data `json:"data"`
}

type Data struct {
	Event          string     `json:"event"`
	EventTime      int64      `json:"event_time"`
	EventId        string     `json:"event_id"`
	User           User       `json:"user"`
	Properties     Properties `json:"properties"`
	Page           Page       `json:"page"`
	LimitedDataUse bool       `json:"limited_data_use"`
}

type User struct {
	Ttclid     string `json:"ttclid"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	ExternalId string `json:"external_id"`
	Ttp        string `json:"ttp"`
	Ip         string `json:"ip"`
	UserAgent  string `json:"user_agent"`
}

type Page struct {
	Url      string `json:"url"`
	Referrer string `json:"referrer"`
}

type Properties struct {
	Currency    string    `json:"currency"`
	Value       float64   `json:"value"`
	ContentType string    `json:"content_type"`
	OrderId     string    `json:"order_id"`
	Contents    []Content `json:"contents"`
}

type Content struct {
	ContentId string `json:"content_id"`
}
