package tiktok

import (
	"encoding/json"
	"github.com/beego/beego/logs"
	"sync"
	"xserver/server"
)

const apiUrlTpl = "https://business-api.tiktok.com/open_api/v1.3/event/track/"

type Maidian struct {
	Tiktok struct {
		BackEnd struct {
			PixelID     string `json:"PIXEL_ID"`
			AccessToken string `json:"ACCESS_TOKEN"`
		} `json:"BackEnd"`
		Front struct {
			PixelID string `json:"PIXEL_ID"`
		} `json:"Front"`
	} `json:"Tiktok"`
}

var mu sync.Mutex

func loadConfig(host string) {
	mu.Lock()
	defer mu.Unlock()
	clients = make(map[string]*Client)

	xChannelHost := server.DaoxHashGame().XChannelHost
	hostConfig, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	if hostConfig == nil {
		logs.Error("暂无Tiktok埋点配置！")
		return
	}

	if hostConfig.Maidian == "" {
		logs.Error("暂无Tiktok埋点配置")
		return
	}

	var cfg Maidian

	err := json.Unmarshal([]byte(hostConfig.Maidian), &cfg)
	if err != nil {
		logs.Error(err)
		return
	}

	clients[host] = &Client{
		host:        host,
		api:         apiUrlTpl,
		pixelId:     cfg.Tiktok.BackEnd.PixelID,
		accessToken: cfg.Tiktok.BackEnd.AccessToken,
	}

	return
}
