// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXLiuhecaiMap(db *gorm.DB, opts ...gen.DOOption) xLiuhecaiMap {
	_xLiuhecaiMap := xLiuhecaiMap{}

	_xLiuhecaiMap.xLiuhecaiMapDo.UseDB(db, opts...)
	_xLiuhecaiMap.xLiuhecaiMapDo.UseModel(&model.XLiuhecaiMap{})

	tableName := _xLiuhecaiMap.xLiuhecaiMapDo.TableName()
	_xLiuhecaiMap.ALL = field.NewAsterisk(tableName)
	_xLiuhecaiMap.ID = field.NewInt32(tableName, "Id")
	_xLiuhecaiMap.PName = field.NewString(tableName, "PName")
	_xLiuhecaiMap.Sid = field.NewInt32(tableName, "Sid")
	_xLiuhecaiMap.SName = field.NewString(tableName, "SName")

	_xLiuhecaiMap.fillFieldMap()

	return _xLiuhecaiMap
}

type xLiuhecaiMap struct {
	xLiuhecaiMapDo xLiuhecaiMapDo

	ALL   field.Asterisk
	ID    field.Int32  // 六合彩大类ID
	PName field.String // 大类名称
	Sid   field.Int32  // 六合彩小类ID
	SName field.String // 小类名称

	fieldMap map[string]field.Expr
}

func (x xLiuhecaiMap) Table(newTableName string) *xLiuhecaiMap {
	x.xLiuhecaiMapDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xLiuhecaiMap) As(alias string) *xLiuhecaiMap {
	x.xLiuhecaiMapDo.DO = *(x.xLiuhecaiMapDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xLiuhecaiMap) updateTableName(table string) *xLiuhecaiMap {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.PName = field.NewString(table, "PName")
	x.Sid = field.NewInt32(table, "Sid")
	x.SName = field.NewString(table, "SName")

	x.fillFieldMap()

	return x
}

func (x *xLiuhecaiMap) WithContext(ctx context.Context) *xLiuhecaiMapDo {
	return x.xLiuhecaiMapDo.WithContext(ctx)
}

func (x xLiuhecaiMap) TableName() string { return x.xLiuhecaiMapDo.TableName() }

func (x xLiuhecaiMap) Alias() string { return x.xLiuhecaiMapDo.Alias() }

func (x xLiuhecaiMap) Columns(cols ...field.Expr) gen.Columns {
	return x.xLiuhecaiMapDo.Columns(cols...)
}

func (x *xLiuhecaiMap) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xLiuhecaiMap) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 4)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["PName"] = x.PName
	x.fieldMap["Sid"] = x.Sid
	x.fieldMap["SName"] = x.SName
}

func (x xLiuhecaiMap) clone(db *gorm.DB) xLiuhecaiMap {
	x.xLiuhecaiMapDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xLiuhecaiMap) replaceDB(db *gorm.DB) xLiuhecaiMap {
	x.xLiuhecaiMapDo.ReplaceDB(db)
	return x
}

type xLiuhecaiMapDo struct{ gen.DO }

func (x xLiuhecaiMapDo) Debug() *xLiuhecaiMapDo {
	return x.withDO(x.DO.Debug())
}

func (x xLiuhecaiMapDo) WithContext(ctx context.Context) *xLiuhecaiMapDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xLiuhecaiMapDo) ReadDB() *xLiuhecaiMapDo {
	return x.Clauses(dbresolver.Read)
}

func (x xLiuhecaiMapDo) WriteDB() *xLiuhecaiMapDo {
	return x.Clauses(dbresolver.Write)
}

func (x xLiuhecaiMapDo) Session(config *gorm.Session) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Session(config))
}

func (x xLiuhecaiMapDo) Clauses(conds ...clause.Expression) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xLiuhecaiMapDo) Returning(value interface{}, columns ...string) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xLiuhecaiMapDo) Not(conds ...gen.Condition) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xLiuhecaiMapDo) Or(conds ...gen.Condition) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xLiuhecaiMapDo) Select(conds ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xLiuhecaiMapDo) Where(conds ...gen.Condition) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xLiuhecaiMapDo) Order(conds ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xLiuhecaiMapDo) Distinct(cols ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xLiuhecaiMapDo) Omit(cols ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xLiuhecaiMapDo) Join(table schema.Tabler, on ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xLiuhecaiMapDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xLiuhecaiMapDo) RightJoin(table schema.Tabler, on ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xLiuhecaiMapDo) Group(cols ...field.Expr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xLiuhecaiMapDo) Having(conds ...gen.Condition) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xLiuhecaiMapDo) Limit(limit int) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xLiuhecaiMapDo) Offset(offset int) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xLiuhecaiMapDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xLiuhecaiMapDo) Unscoped() *xLiuhecaiMapDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xLiuhecaiMapDo) Create(values ...*model.XLiuhecaiMap) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xLiuhecaiMapDo) CreateInBatches(values []*model.XLiuhecaiMap, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xLiuhecaiMapDo) Save(values ...*model.XLiuhecaiMap) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xLiuhecaiMapDo) First() (*model.XLiuhecaiMap, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLiuhecaiMap), nil
	}
}

func (x xLiuhecaiMapDo) Take() (*model.XLiuhecaiMap, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLiuhecaiMap), nil
	}
}

func (x xLiuhecaiMapDo) Last() (*model.XLiuhecaiMap, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLiuhecaiMap), nil
	}
}

func (x xLiuhecaiMapDo) Find() ([]*model.XLiuhecaiMap, error) {
	result, err := x.DO.Find()
	return result.([]*model.XLiuhecaiMap), err
}

func (x xLiuhecaiMapDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XLiuhecaiMap, err error) {
	buf := make([]*model.XLiuhecaiMap, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xLiuhecaiMapDo) FindInBatches(result *[]*model.XLiuhecaiMap, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xLiuhecaiMapDo) Attrs(attrs ...field.AssignExpr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xLiuhecaiMapDo) Assign(attrs ...field.AssignExpr) *xLiuhecaiMapDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xLiuhecaiMapDo) Joins(fields ...field.RelationField) *xLiuhecaiMapDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xLiuhecaiMapDo) Preload(fields ...field.RelationField) *xLiuhecaiMapDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xLiuhecaiMapDo) FirstOrInit() (*model.XLiuhecaiMap, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLiuhecaiMap), nil
	}
}

func (x xLiuhecaiMapDo) FirstOrCreate() (*model.XLiuhecaiMap, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLiuhecaiMap), nil
	}
}

func (x xLiuhecaiMapDo) FindByPage(offset int, limit int) (result []*model.XLiuhecaiMap, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xLiuhecaiMapDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xLiuhecaiMapDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xLiuhecaiMapDo) Delete(models ...*model.XLiuhecaiMap) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xLiuhecaiMapDo) withDO(do gen.Dao) *xLiuhecaiMapDo {
	x.DO = *do.(*gen.DO)
	return x
}
