package utils

import (
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"sort"
)

type KeyValue struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

func SortMapByKeys(data map[string]interface{}) map[string]interface{} {
	var sortedKeys []string
	for key := range data {
		sortedKeys = append(sortedKeys, key)
	}
	sort.Strings(sortedKeys)

	var sortedData []KeyValue
	for _, key := range sortedKeys {
		sortedData = append(sortedData, KeyValue{Key: key, Value: data[key]})
	}

	filteredData := filterEmptyOrZeroValues(sortedData)

	return filteredData
}

// 排除空或为零值的项
func filterEmptyOrZeroValues(data []KeyValue) map[string]interface{} {
	filteredData := make(map[string]interface{})
	for _, kv := range data {
		switch v := kv.Value.(type) {
		case string:
			if v != "" {
				filteredData[kv.Key] = v
			}
		//case int:
		//	if v != 0 {
		//		filteredData[kv.Key] = v
		//	}
		default:
			filteredData[kv.Key] = v
		}
	}
	return filteredData
}

func Md5V(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// GenerateHash256Sign Based on RSA-HASH256 generate sign
func GenerateHash256Sign(data, key string) (sign string) {
	// 使用 SHA256 计算数据的哈希值
	hash := sha256.Sum256([]byte(data))

	// 解码 PEM 编码的私钥
	block, _ := pem.Decode([]byte(key))
	if block == nil {
		fmt.Println("Failed to decode private key PEM")
		return
	}

	// 解析私钥
	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		fmt.Println("Failed to parse private key:", err)
		return
	}

	// 对哈希值进行签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey.(*rsa.PrivateKey), crypto.SHA256, hash[:])
	if err != nil {
		fmt.Println("Failed to sign the data:", err)
		return
	}

	sign = base64.StdEncoding.EncodeToString(signature)
	fmt.Println("Signature:", sign)

	return sign
}
