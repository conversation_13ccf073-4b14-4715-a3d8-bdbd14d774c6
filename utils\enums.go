package utils

const UnDefineId = 0

const (
	IsActiveTest = true
)

const (
	CurrencyPrecision = 2
)

const (
	KActiveIdEachBetFanShui  = 1
	KActiveIdRechange        = 2
	KActiveIdHaXiBreakout    = 3  // 哈希闯关
	KActiveIdHaXiBreakout_15 = 15 // 哈希闯关
	KActiveIdHaXiBreakout_16 = 16 // 哈希闯关
	KActiveIdHaXiBreakout_17 = 17 // 哈希闯关
	KActiveIdHaXiBreakout_18 = 18 // 哈希闯关
	KActiveIdQipaiBreakout   = 4  // 棋牌闯关
	KActiveIdDianziBreakout  = 5  // 电子闯关
	KActiveIdRescue          = 6
	KActiveIdInvite          = 7
	KActiveIdVipFanShui      = 8
	KActiveIdXianglongfuhu   = 9
	KActiveIdCiRiSong        = 10
	KActiveIdMeiZhouSong     = 11

	//ActiveErrorCode
	ActiveSuccess = 200 //成功

	ActiveMinLiuShuiPercent = -22 //参数错误 流水不存在
	ActiveExpired           = -23 //活动过期
	ActiveLevelNoHave       = -24 //活动定义不存在
	ActiveLiuShui           = -25 //流水不满足

	ActiveIAccountNoHave = -1 //账号不存在
	ActiveRAccountNoHave = "账号不存在"
	ActiveIIsTest        = -5 //测试玩家禁止领取
	ActiveRIsTest        = "该玩家禁止领取"
	ActiveIAlreadyApply  = -41 //已申请
	ActiveRAlreadyApply  = "已申请"
	ActiveINotDefine     = -21 //活动失效
	ActiveRNotDefine     = "活动已失效"
	ActiveIParamsIllegal = -22     //参数错误 流水不存在
	ActiveRParamsIllegal = "活动已失效" //参数错误 流水不存在
	ActiveITimeExpire    = -23     //参数错误 流水不存在
	ActiveRTimeExpire    = "活动已失效" //参数错误 流水不存在
	ActiveILevelErr      = -24
	ActiveRLevelErr      = "活动已失效"
	ActiveINotMatch      = -25
	ActiveRNotMatch      = "条件不满足"
	ActiveIInternal      = -26     //参数错误 流水不存在
	ActiveRInternal      = "活动已失效" //参数错误 流水不存在
	ActiveISuccess       = 200     //活动失效

	ActiveIsExcludeFirstReCharge      = 1  //充值活动是否互相排斥
	KActiveIdCumulativeWeeklyRecharge = 60 // 累计周充值，豪礼享不停
	KActiveIdSignReward               = 55 // 签到奖励
	KActiveIdRecommendFriendReward    = 54 // 推荐好友奖励
	KActiveIdDianziMeiZhouSong        = 11 // 电子周末狂欢送
	KActiveIdZhenrenMeiZhouSong       = 70 // 真人视讯周末狂欢送
	KActiveIdQiPaiMeiZhouSong         = 71 // 棋牌游戏周末狂欢送
	KActiveIdZhenrenBreakout          = 69 // 真人视讯闯关
	KActiveIdOuZhouBeiTianTianSong    = 72 // 欧洲杯彩金天天送
	KActiveIdOuZhouBeiMeiRiJiaZeng    = 73 // 助力欧洲杯每日盈利加赠
	KActiveIdOuZhouBeiRongYaoZhiZheng = 74 // 欧洲杯冠军荣耀之争，你能猜对吗？
	KActiveIdOuZhouBeiZhuLiOuJue      = 75 // 助战欧决，为您喜爱的球队助力
	KActiveIdOuZhouBeiTiyuZhouLiuShui = 76 // 体育周流水
	KActiveIdNewFirstDeposit          = 77 // 新首存活动180
	KActiveIdMidAutumnZhenrenBreakout = 84 // 中秋每日真人闯关
	KActiveIdMidAutumnBreakout        = 85 // 中秋每日流水闯关
	KActiveIdCryptoGamesBreakout      = 86 // 加密游戏闯关

	KDIYActiveIdRecharge = 1001 // 自定义充值活动

	ActiveINocondition     = -31
	ActiveRNocondition     = "条件不满足"
	ActiveINoSigncondition = -5554 // 签到不满足条件单独返回状态
)

const (
	RegisterGift          = 1000 // 注册赠送活动
	NewUserDepositGift    = 1001 // 新用户充值赠送活动
	SpecialBonusGift      = 1002 // 特殊礼金活动
	RedeemCodeGift        = 1003 // 兑换码活动
	SocialMediaFollowGift = 1004 // 关注社媒，福利领不停
	ChatRoomBonusGift     = 1005 // 聊天室红包活动
	FirstDepositGift      = 1006 // 首充活动
	MultipleDepositGift   = 1007 // 复充活动
)

// 活动的常量

//const ActiveCiRiLiuShuiBeiShu = 10

const ActiveAuditRenGong = 1
const ActiveAuditAuto = 2

const ActiveStateOpen = 1
const ActiveStateClose = 2

const ActiveAwardAuditStateWait = 1     //等待审核
const ActiveAwardAuditStateRefuse = 2   //人工拒绝
const ActiveAwardAuditStatePass = 3     //手动通过
const ActiveAwardAuditStateAutoPass = 4 //自动通过

// 用户的常量
const UserTestType = 1

// 充值状态
const RechargeCantFindUser = 1
const RechargeTooSmall = 1
const RechargeSuccess = 5

const (
	HttpHNType = "content-type"
)

const (
	TimeFormatStr = "2006-01-02 15:04:05"
	DateFormatStr = "2006-01-02"
)

const (
	RedisKPResetMoney = "reset.money"
	RedisKPTokenIncr  = "reset.token"
)

const (
	SendBySms      = 1
	SendByEmail    = 2
	SendByTransfer = 3

	ResetPasswordMin = 1
	ResetPasswordMax = 5

	ResetTokenMin           = 100000
	ResetTokenMax           = 999999
	ResetTokenTimeOutMinute = 10
	ResetTokenTimeOut       = 60 * ResetTokenTimeOutMinute
)

// 账变类型
const (
	BalanceCReasonCharge         = 1
	BalanceCReasonWithdraw       = 2
	BalanceCReasonWithdrawRefuse = 6

	BalanceCReasonBet = 9
	BalanceCReasonWin = 16

	BalanceCReasonFaHongBao     = 32
	BalanceCReasonQiangHongBao  = 33
	BalanceCReasonHongBaoFanHui = 34

	BalanceCReasonASend                         = 3
	BalanceCReasonARiFanShui                    = 17
	BalanceCReasonAShengJi                      = 18
	BalanceCReasonAYueLiJin                     = 19
	BalanceCReasonANengLiang                    = 20
	BalanceCReasonAChongZhiRenWu                = 25
	BalanceCReasonAHashChuangGuan               = 26 // 哈希闯关活动新(自动)
	BalanceCReasonAQiPaiChuangGuan              = 27 // 棋牌闯关活动新(自动)
	BalanceCReasonADianZiChuangGuan             = 28 // 电子闯关活动新(自动)
	BalanceCReasonAZhenRenChuangGuan            = 56 // 真人闯关活动新(自动)
	BalanceCReasonAMidAutumnZhenrenChuangGuan   = 64 // 中秋每日真人闯关(自动)
	BalanceCReasonAMidAutumnMidAutumnChuangGuan = 65 // 中秋每日流水闯关(自动)
	BalanceCReasonACryptoGamesChuangGuan        = 67 // 加密游戏闯关(自动)
	BalanceCReasonAJiuYuanJin                   = 29
	BalanceCReasonAYaoQing                      = 30
	BalanceCReasonAXiangLong                    = 31
	BalanceCReasonARegisterGift                 = 88 // 注册赠送礼金(自动)

	BalanceCReasonLOut    = 7
	BalanceCReasonLIn     = 8
	BalanceCReasonDZOut   = 10
	BalanceCReasonDZIn    = 11
	BalanceCReasonQPDZOut = 12
	BalanceCReasonQPDZIn  = 13
	BalanceCReasonIGOut   = 14
	BalanceCReasonIGIn    = 15

	BalanceCReasonPGOut  = 100
	BalanceCReasonPGIn   = 101
	BalanceCReasonPPOut  = 102
	BalanceCReasonPPIn   = 103
	BalanceCReasonEVOOut = 104
	BalanceCReasonEVOIn  = 106
	BalanceCReasonWMOut  = 105
	BalanceCReasonWMIn   = 107
	BalanceCReasonAGOut  = 108
	BalanceCReasonAGIn   = 109

	BalanceCReasonBrokerage = 4
	BalanceCReasonBackend   = 5
	BalanceCReasonTrial     = 201
	BalanceCReasonTest      = 226
	BalanceCReasonClear     = 50

	BalanceCReasonLucky        = 221
	BalanceCReasonLossCall     = 222
	BalanceCReasonAHongBao     = 223
	BalanceCReasonAFirstCharge = 224
	BalanceCReasonADice        = 225
	BalanceCReasonACiRiSong    = 227
	BalanceCReasonAMeiZhouSong = 228
	BalanceCReasonAGuoQing     = 229

	//三方单一钱包账变类型
	//保加利亚
	BalanceCReasonUPBet         = 2500
	BalanceCReasonUPCancel      = 2501
	BalanceCReasonUPWin         = 2502
	BalanceCReasonAstrarBet     = 2503
	BalanceCReasonAstrarCancel  = 2504
	BalanceCReasonAstrarWin     = 2505
	BalanceCReasoneEasybetBet   = 2506
	BalanceCReasonEasybetCancel = 2507
	BalanceCReasonEasybetWin    = 2508
	//三昇体育
	BalanceCReasonThreeupBet    = 2509
	BalanceCReasonThreeupCancel = 2510
	BalanceCReasonThreeupWin    = 2511
	//IDN 德州对接
	BalanceCReasonIDNOut = 2512
	BalanceCReasonIDNIn  = 2513
	//JDB
	BalanceCReasonJDBBet    = 2514
	BalanceCReasonJDBCancel = 2515
	BalanceCReasonJDBWin    = 2516
	//JD
	BalanceCReasonJDBet    = 2517
	BalanceCReasonJDCancel = 2518
	BalanceCReasonJDWin    = 2519
	//coincoin
	BalanceCReasonCoin2Bet    = 2520
	BalanceCReasonCoin2Cancel = 2521
	BalanceCReasonCoin2Win    = 2522

	//牛熊
	BalanceCReasonBearCowBet    = 2523
	BalanceCReasonBearCowCancel = 2524
	BalanceCReasonBearCowWin    = 2525

	// 财富邮轮
	BalanceCReasonFortuneBet    = 2526
	BalanceCReasonFortuneCancel = 2527
	BalanceCReasonFortuneWin    = 2528

	// 採星
	BalanceCReasonStargrabberBet    = 2529
	BalanceCReasonStargrabberCancel = 2530
	BalanceCReasonStargrabberWin    = 2531

	// 区块 Lottery
	BalanceCReasonHashLottoryBet    = 2532
	BalanceCReasonHashLottoryCancel = 2533
	BalanceCReasonHashLottoryWin    = 2534

	// 秒合约 Trading
	BalanceCReasonHashTradingBet    = 2535
	BalanceCReasonHashTradingCancel = 2536
	BalanceCReasonHashTradingWin    = 2537

	// OG Games Tpye
	BalanceCReasonOgGameBet    = 2538
	BalanceCReasonOgGameCancel = 2539
	BalanceCReasonOgGameWin    = 2540
	// updown game
	BalanceCReasonUpDownGameBet    = 2541
	BalanceCReasonUpDownGameCancel = 2542
	BalanceCReasonUpDownGameWin    = 2543

	//evo 单一钱包账变类型
	BalanceCReasonEVOBet    = 3001
	BalanceCReasonEVOSettle = 3002
	BalanceCReasonEVOCancel = 3003

	//pp 单一钱包账变类型
	BalanceCReasonPPBet        = 3004
	BalanceCReasonPPSettle     = 3005
	BalanceCReasonPPCancel     = 3006
	BalanceCReasonPPBonusWin   = 3007
	BalanceCReasonPPJackpotWin = 3008
	BalanceCReasonPPAdjustment = 3009

	//pg 单一钱包账变类型
	BalanceCReasonPGBet        = 3010
	BalanceCReasonPGSettle     = 3011
	BalanceCReasonPGAdjustment = 3012

	//gfg 单一钱包账变类型
	BalanceCReasonGFGBet          = 3013
	BalanceCReasonGFGSettle       = 3014
	BalanceCReasonGFGCancel       = 3017 // GFG取消注单
	BalanceCReasonGFGBetDianZi    = 3015
	BalanceCReasonGFGSettleDianZi = 3016
	BalanceCReasonGFGDeduction    = 3045 // 预扣款
	BalanceCReasonGFGReturn       = 3046 // 返还预扣款

	//ag 单一钱包账变类型
	BalanceCReasonAGBet    = 3017
	BalanceCReasonAGSettle = 3018
	BalanceCReasonAGREFUND = 3019

	// //hub88 单一钱包账变类型 废弃
	// //OneTouch Generic
	// BalanceCReasonOntBet    = 3020
	// BalanceCReasonOntSettle = 3021
	// BalanceCReasonOntREFUND = 3022
	// //Kalamba
	// BalanceCReasonKlbBet    = 3023
	// BalanceCReasonKlbSettle = 3024
	// BalanceCReasonKlbREFUND = 3025
	// //Hacksaw Gaming
	// BalanceCReasonHsgBet    = 3026
	// BalanceCReasonHsgSettle = 3027
	// BalanceCReasonHsgREFUND = 3028
	// //Nolimit City
	// BalanceCReasonNlcBet    = 3029
	// BalanceCReasonNlcSettle = 3030
	// BalanceCReasonNlcREFUND = 3031
	// //Live88
	// BalanceCReasonBblBet    = 3032
	// BalanceCReasonBblSettle = 3033
	// BalanceCReasonBblREFUND = 3034
	// //OneTouch Table game
	// BalanceCReasonOnttBet    = 3035
	// BalanceCReasonOnttSettle = 3036
	// BalanceCReasonOnttREFUND = 3037
	// //AvatarUX
	// BalanceCReasonAVXBet    = 3038
	// BalanceCReasonAVXSettle = 3039
	// BalanceCReasonAVXREFUND = 3040 废弃

	//gfg_hash 单一钱包账变类型
	BalanceCReasonGfGHASHBet    = 3041
	BalanceCReasonGfGHASHSettle = 3042
	BalanceCReasonGfGHASHREFUND = 3043
	BalanceCReasonGfGHASHReturn = 3044

	// hub88子厂商账变类型
	/*
		"3Oaks",            // 第一批上
		"Retro Gaming",     // 第一批上
		"SlotMill Games",   // 第一批上
		"Booming Games",    // 第一批上
		"BetsyGames",       // 第一批上
		"Mancala Gaming",   // 第一批上
		"Habanero",         // 第一批上
		"Play'n Go",        // 第一批上
		"OneTouch Generic", // 测试服专用
		"OneTouch",         // 第二批上
		"Live88",           // 第二批上

		插入脚本
		INSERT INTO `x_hash_game`.`x_dict_changetype`(`ChangeType`, `ChangeName`, `ParentType`, `Sort`, `Memo`, `Status`, `Operator`, `OperUserID`, `DeviceType`, `DeviceID`, `CreateTime`, `UpdateTime`) VALUES
	*/
	// hub88_3Oaks
	// (3045, '3Oaks单一钱包-下注', 108, 1, '3Oaks单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3046, '3Oaks单一钱包-结算', 108, 2, '3Oaks单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3047, '3Oaks单一钱包-回滚', 108, 3, '3Oaks单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub883OaksBet      = 3045
	BalanceCReasonHub883OaksSettle   = 3046
	BalanceCReasonHub883OaksRollback = 3047
	// "hub88_Retro_Gaming"
	// (3048, 'Retro Gaming单一钱包-下注', 108, 1, 'Retro Gaming单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3049, 'Retro Gaming单一钱包-结算', 108, 2, 'Retro Gaming单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3050, 'Retro Gaming单一钱包-回滚', 108, 3, 'Retro Gaming单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88RetroGamingBet      = 3048
	BalanceCReasonHub88RetroGamingSettle   = 3049
	BalanceCReasonHub88RetroGamingRollback = 3050
	// "hub88_SlotMill_Games"
	// (3051, 'SlotMill Games单一钱包-下注', 108, 1, 'SlotMill Games单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3052, 'SlotMill Games单一钱包-结算', 108, 2, 'SlotMill Games单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3053, 'SlotMill Games单一钱包-回滚', 108, 3, 'SlotMill Games单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88SlotMillGamesBet      = 3051
	BalanceCReasonHub88SlotMillGamesSettle   = 3052
	BalanceCReasonHub88SlotMillGamesRollback = 3053
	// "hub88_Booming_Games"
	// (3054, 'Booming Games单一钱包-下注', 108, 1, 'Booming Games单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3055, 'Booming Games单一钱包-结算', 108, 2, 'Booming Games单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3056, 'Booming Games单一钱包-回滚', 108, 3, 'Booming Games单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88BoomingGamesBet      = 3054
	BalanceCReasonHub88BoomingGamesSettle   = 3055
	BalanceCReasonHub88BoomingGamesRollback = 3056
	// "hub88_BetsyGames"
	// (3057, 'BetsyGames单一钱包-下注', 108, 1, 'BetsyGames单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3058, 'BetsyGames单一钱包-结算', 108, 2, 'BetsyGames单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3059, 'BetsyGames单一钱包-回滚', 108, 3, 'BetsyGames单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88BetsyGamesBet      = 3057
	BalanceCReasonHub88BetsyGamesSettle   = 3058
	BalanceCReasonHub88BetsyGamesRollback = 3059
	// "hub88_Mancala_Gaming"
	// (3060, 'Mancala Gaming单一钱包-下注', 108, 1, 'Mancala Gaming单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3061, 'Mancala Gaming单一钱包-结算', 108, 2, 'Mancala Gaming单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3062, 'Mancala Gaming单一钱包-回滚', 108, 3, 'Mancala Gaming单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88MancalaGamingBet      = 3060
	BalanceCReasonHub88MancalaGamingSettle   = 3061
	BalanceCReasonHub88MancalaGamingRollback = 3062
	// "hub88_Habanero"
	// (3063, 'Habanero单一钱包-下注', 108, 1, 'Habanero单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3064, 'Habanero单一钱包-结算', 108, 2, 'Habanero单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3065, 'Habanero单一钱包-回滚', 108, 3, 'Habanero单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88HabaneroBet      = 3063
	BalanceCReasonHub88HabaneroSettle   = 3064
	BalanceCReasonHub88HabaneroRollback = 3065
	// "hub88_Play_n_Go"
	// (3066, 'Play_n Go单一钱包-下注', 108, 1, 'Play_n Go单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3067, 'Play_n Go单一钱包-结算', 108, 2, 'Play_n Go单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3068, 'Play_n Go单一钱包-回滚', 108, 3, 'Play_n Go单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88PlaynGoBet      = 3066
	BalanceCReasonHub88PlaynGoSettle   = 3067
	BalanceCReasonHub88PlaynGoRollback = 3068
	// "hub88_OneTouch_Generic"
	// (3069, 'OneTouch Generic单一钱包-下注', 108, 1, 'OneTouch Generic单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3070, 'OneTouch Generic单一钱包-结算', 108, 2, 'OneTouch Generic单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	// (3071, 'OneTouch Generic单一钱包-回滚', 108, 3, 'OneTouch Generic单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-06-25 14:00:00', '2024-06-25 14:00:00'),
	BalanceCReasonHub88OneTouchGenericBet      = 3069
	BalanceCReasonHub88OneTouchGenericSettle   = 3070
	BalanceCReasonHub88OneTouchGenericRollback = 3071
	// "hub88_OneTouch"
	// (3072, 'OneTouch单一钱包-下注', 108, 1, 'OneTouch单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	// (3073, 'OneTouch单一钱包-结算', 108, 2, 'OneTouch单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	// (3074, 'OneTouch单一钱包-回滚', 108, 3, 'OneTouch单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	BalanceCReasonHub88OneTouchBet      = 3072
	BalanceCReasonHub88OneTouchSettle   = 3073
	BalanceCReasonHub88OneTouchRollback = 3074
	// "hub88_Live88"
	// (3075, 'Live88单一钱包-下注', 108, 1, 'Live88单一钱包-下注', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	// (3076, 'Live88单一钱包-结算', 108, 2, 'Live88单一钱包-结算', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	// (3077, 'Live88单一钱包-回滚', 108, 3, 'Live88单一钱包-回滚', 1, 'test0327', 187, 4, '20.218.160.133', '2024-07-22 15:00:00', '2024-07-22 15:00:00'),
	BalanceCReasonHub88Live88Bet      = 3075
	BalanceCReasonHub88Live88Settle   = 3076
	BalanceCReasonHub88Live88Rollback = 3077
	/*
		// 起始3072
		// hub88_7Mojos
		BalanceCReasonHub887MojosBet      = 3072
		BalanceCReasonHub887MojosSettle   =
		BalanceCReasonHub887MojosRollback =
		// hub88_7Mojos_Live
		BalanceCReasonHub887MojosLiveBet      =
		BalanceCReasonHub887MojosLiveSettle   =
		BalanceCReasonHub887MojosLiveRollback =
		// "hub88_AirDice"
		BalanceCReasonHub88AirDiceBet      =
		BalanceCReasonHub88AirDiceSettle   =
		BalanceCReasonHub88AirDiceRollback =
		// "hub88_Alchemy_Gaming"
		BalanceCReasonHub88AlchemyGamingBet      =
		BalanceCReasonHub88AlchemyGamingSettle   =
		BalanceCReasonHub88AlchemyGamingRollback =
		// "hub88_All41"
		BalanceCReasonHub88All41Bet      =
		BalanceCReasonHub88All41Settle   =
		BalanceCReasonHub88All41Rollback =
		// "hub88_Apparat_Gaming"
		BalanceCReasonHub88ApparatGamingBet      =
		BalanceCReasonHub88ApparatGamingSettle   =
		BalanceCReasonHub88ApparatGamingRollback =
		// "hub88_Asia_Gaming"
		BalanceCReasonHub88AsiaGamingBet      =
		BalanceCReasonHub88AsiaGamingSettle   =
		BalanceCReasonHub88AsiaGamingRollback =
		// "hub88_Atomic_Slot_Lab"
		BalanceCReasonHub88AtomicSlotLabBet      =
		BalanceCReasonHub88AtomicSlotLabSettle   =
		BalanceCReasonHub88AtomicSlotLabRollback =
		// "hub88_AvatarUX"
		BalanceCReasonHub88AvatarUXBet      =
		BalanceCReasonHub88AvatarUXSettle   =
		BalanceCReasonHub88AvatarUXRollback =
		// "hub88_BGaming"
		BalanceCReasonHub88BGamingBet      =
		BalanceCReasonHub88BGamingSettle   =
		BalanceCReasonHub88BGamingRollback =
		// "hub88_Barbarabang"
		BalanceCReasonHub88BarbarabangBet      =
		BalanceCReasonHub88BarbarabangSettle   =
		BalanceCReasonHub88BarbarabangRollback =
		// "hub88_BetGames.TV"
		BalanceCReasonHub88BetGamesTVBet      =
		BalanceCReasonHub88BetGamesTVSettle   =
		BalanceCReasonHub88BetGamesTVRollback =
		// "hub88_Betsoft"
		BalanceCReasonHub88BetsoftBet      =
		BalanceCReasonHub88BetsoftSettle   =
		BalanceCReasonHub88BetsoftRollback =
		// "hub88_Blue_Guru"
		BalanceCReasonHub88BlueGuruBet      =
		BalanceCReasonHub88BlueGuruSettle   =
		BalanceCReasonHub88BlueGuruRollback =
		// "hub88_Blueprint_Gaming"
		BalanceCReasonHub88BlueprintGamingBet      =
		BalanceCReasonHub88BlueprintGamingSettle   =
		BalanceCReasonHub88BlueprintGamingRollback =
		// "hub88_Buck_Stakes_Entertainment"
		BalanceCReasonHub88BuckStakesEntertainmentBet      =
		BalanceCReasonHub88BuckStakesEntertainmentSettle   =
		BalanceCReasonHub88BuckStakesEntertainmentRollback =
		// "hub88_Caleta_Gaming"
		BalanceCReasonHub88CaletaGamingBet      =
		BalanceCReasonHub88CaletaGamingSettle   =
		BalanceCReasonHub88CaletaGamingRollback =
		// "hub88_CandleBets"
		BalanceCReasonHub88CandleBetsBet      =
		BalanceCReasonHub88CandleBetsSettle   =
		BalanceCReasonHub88CandleBetsRollback =
		// "hub88_Elbet"
		BalanceCReasonHub88ElbetBet      =
		BalanceCReasonHub88ElbetSettle   =
		BalanceCReasonHub88ElbetRollback =
		// "hub88_Evolution_Gaming"
		BalanceCReasonHub88EvolutionGamingBet      =
		BalanceCReasonHub88EvolutionGamingSettle   =
		BalanceCReasonHub88EvolutionGamingRollback =
		// "hub88_Evoplay_Entertainment"
		BalanceCReasonHub88EvoplayEntertainmentBet      =
		BalanceCReasonHub88EvoplayEntertainmentSettle   =
		BalanceCReasonHub88EvoplayEntertainmentRollback =
		// "hub88_Ezugi"
		BalanceCReasonHub88EzugiBet      =
		BalanceCReasonHub88EzugiSettle   =
		BalanceCReasonHub88EzugiRollback =
		// "hub88_FBastards"
		BalanceCReasonHub88FBastardsBet      =
		BalanceCReasonHub88FBastardsSettle   =
		BalanceCReasonHub88FBastardsRollback =
		// "hub88_Fantasma"
		BalanceCReasonHub88FantasmaBet      =
		BalanceCReasonHub88FantasmaSettle   =
		BalanceCReasonHub88FantasmaRollback =
		// "hub88_Foxium"
		BalanceCReasonHub88FoxiumBet      =
		BalanceCReasonHub88FoxiumSettle   =
		BalanceCReasonHub88FoxiumRollback =
		// "hub88_Fugaso"
		BalanceCReasonHub88FugasoBet      =
		BalanceCReasonHub88FugasoSettle   =
		BalanceCReasonHub88FugasoRollback =
		// "hub88_Gameburger"
		BalanceCReasonHub88GameburgerBet      =
		BalanceCReasonHub88GameburgerSettle   =
		BalanceCReasonHub88GameburgerRollback =
		// "hub88_Gametech"
		BalanceCReasonHub88GametechBet      =
		BalanceCReasonHub88GametechSettle   =
		BalanceCReasonHub88GametechRollback =
		// "hub88_Gamomat"
		BalanceCReasonHub88GamomatBet      =
		BalanceCReasonHub88GamomatSettle   =
		BalanceCReasonHub88GamomatRollback =
		// "hub88_Gamzix"
		BalanceCReasonHub88GamzixBet      =
		BalanceCReasonHub88GamzixSettle   =
		BalanceCReasonHub88GamzixRollback =
		// "hub88_Genii"
		BalanceCReasonHub88GeniiBet      =
		BalanceCReasonHub88GeniiSettle   =
		BalanceCReasonHub88GeniiRollback =
		// "hub88_Golden_Hero"
		BalanceCReasonHub88GoldenHeroBet      =
		BalanceCReasonHub88GoldenHeroSettle   =
		BalanceCReasonHub88GoldenHeroRollback =
		// "hub88_Golden_Race"
		BalanceCReasonHub88GoldenRaceBet      =
		BalanceCReasonHub88GoldenRaceSettle   =
		BalanceCReasonHub88GoldenRaceRollback =
		// "hub88_Golden_Rock_Studios"
		BalanceCReasonHub88GoldenRockStudiosBet      =
		BalanceCReasonHub88GoldenRockStudiosSettle   =
		BalanceCReasonHub88GoldenRockStudiosRollback =
		// "hub88_Hacksaw_Gaming"
		BalanceCReasonHub88HacksawGamingBet      =
		BalanceCReasonHub88HacksawGamingSettle   =
		BalanceCReasonHub88HacksawGamingRollback =
		// "hub88_JFTW"
		BalanceCReasonHub88JFTWBet      =
		BalanceCReasonHub88JFTWSettle   =
		BalanceCReasonHub88JFTWRollback =
		// "hub88_JVL"
		BalanceCReasonHub88JVLBet      =
		BalanceCReasonHub88JVLSettle   =
		BalanceCReasonHub88JVLRollback =
		// "hub88_Kalamba"
		BalanceCReasonHub88KalambaBet      =
		BalanceCReasonHub88KalambaSettle   =
		BalanceCReasonHub88KalambaRollback =
		// "hub88_Kalamba_Games"
		BalanceCReasonHub88KalambaGamesBet      =
		BalanceCReasonHub88KalambaGamesSettle   =
		BalanceCReasonHub88KalambaGamesRollback =
		// "hub88_Live_Solutions"
		BalanceCReasonHub88LiveSolutionsBet      =
		BalanceCReasonHub88LiveSolutionsSettle   =
		BalanceCReasonHub88LiveSolutionsRollback =
		// "hub88_Lotto_Instant_Win"
		BalanceCReasonHub88LottoInstantWinBet      =
		BalanceCReasonHub88LottoInstantWinSettle   =
		BalanceCReasonHub88LottoInstantWinRollback =
		// "hub88_MG_Live"
		BalanceCReasonHub88MGLiveBet      =
		BalanceCReasonHub88MGLiveSettle   =
		BalanceCReasonHub88MGLiveRollback =
		// "hub88_MG_Slots"
		BalanceCReasonHub88MGSlotsBet      =
		BalanceCReasonHub88MGSlotsSettle   =
		BalanceCReasonHub88MGSlotsRollback =
		// "hub88_Mascot_Gaming"
		BalanceCReasonHub88MascotGamingBet      =
		BalanceCReasonHub88MascotGamingSettle   =
		BalanceCReasonHub88MascotGamingRollback =
		// "hub88_Microgaming"
		BalanceCReasonHub88MicrogamingBet      =
		BalanceCReasonHub88MicrogamingSettle   =
		BalanceCReasonHub88MicrogamingRollback =
		// "hub88_Neon_Valley"
		BalanceCReasonHub88NeonValleyBet      =
		BalanceCReasonHub88NeonValleySettle   =
		BalanceCReasonHub88NeonValleyRollback =
		// "hub88_NetEnt"
		BalanceCReasonHub88NetEntBet      =
		BalanceCReasonHub88NetEntSettle   =
		BalanceCReasonHub88NetEntRollback =
		// "hub88_NetGaming"
		BalanceCReasonHub88NetGamingBet      =
		BalanceCReasonHub88NetGamingSettle   =
		BalanceCReasonHub88NetGamingRollback =
		// "hub88_Nolimit_City"
		BalanceCReasonHub88NolimitCityBet      =
		BalanceCReasonHub88NolimitCitySettle   =
		BalanceCReasonHub88NolimitCityRollback =
		// "hub88_Northern_Lights_Gaming"
		BalanceCReasonHub88NorthernLightsGamingBet      =
		BalanceCReasonHub88NorthernLightsGamingSettle   =
		BalanceCReasonHub88NorthernLightsGamingRollback =
		// "hub88_Old_Skool"
		BalanceCReasonHub88OldSkoolBet      =
		BalanceCReasonHub88OldSkoolSettle   =
		BalanceCReasonHub88OldSkoolRollback =
		// "hub88_OneGame"
		BalanceCReasonHub88OneGameBet      =
		BalanceCReasonHub88OneGameSettle   =
		BalanceCReasonHub88OneGameRollback =
		// "hub88_OneTouch_Table_game"
		BalanceCReasonHub88OneTouchTablegameBet      =
		BalanceCReasonHub88OneTouchTablegameSettle   =
		BalanceCReasonHub88OneTouchTablegameRollback =
		// "hub88_Onlyplay"
		BalanceCReasonHub88OnlyplayBet      =
		BalanceCReasonHub88OnlyplaySettle   =
		BalanceCReasonHub88OnlyplayRollback =
		// "hub88_Oryx_Gaming"
		BalanceCReasonHub88OryxGamingBet      =
		BalanceCReasonHub88OryxGamingSettle   =
		BalanceCReasonHub88OryxGamingRollback =
		// "hub88_PGSoft"
		BalanceCReasonHub88PGSoftBet      =
		BalanceCReasonHub88PGSoftSettle   =
		BalanceCReasonHub88PGSoftRollback =
		// "hub88_Peter_&_Sons"
		BalanceCReasonHub88PeterSonsBet      =
		BalanceCReasonHub88PeterSonsSettle   =
		BalanceCReasonHub88PeterSonsRollback =
		// "hub88_Playson"
		BalanceCReasonHub88PlaysonBet      =
		BalanceCReasonHub88PlaysonSettle   =
		BalanceCReasonHub88PlaysonRollback =
		// "hub88_Pragmatic_Play"
		BalanceCReasonHub88PragmaticPlayBet      =
		BalanceCReasonHub88PragmaticPlaySettle   =
		BalanceCReasonHub88PragmaticPlayRollback =
		// "hub88_Pulse_8"
		BalanceCReasonHub88Pulse8Bet      =
		BalanceCReasonHub88Pulse8Settle   =
		BalanceCReasonHub88Pulse8Rollback =
		// "hub88_Push_Gaming"
		BalanceCReasonHub88PushGamingBet      =
		BalanceCReasonHub88PushGamingSettle   =
		BalanceCReasonHub88PushGamingRollback =
		// "hub88_Rabcat"
		BalanceCReasonHub88RabcatBet      =
		BalanceCReasonHub88RabcatSettle   =
		BalanceCReasonHub88RabcatRollback =
		// "hub88_Red_Rake_Gaming"
		BalanceCReasonHub88RedRakeGamingBet      =
		BalanceCReasonHub88RedRakeGamingSettle   =
		BalanceCReasonHub88RedRakeGamingRollback =
		// "hub88_Red_Tiger_Gaming"
		BalanceCReasonHub88RedTigerGamingBet      =
		BalanceCReasonHub88RedTigerGamingSettle   =
		BalanceCReasonHub88RedTigerGamingRollback =
		// "hub88_Relax_Gaming"
		BalanceCReasonHub88RelaxGamingBet      =
		BalanceCReasonHub88RelaxGamingSettle   =
		BalanceCReasonHub88RelaxGamingRollback =
		// "hub88_Revolver_Gaming"
		BalanceCReasonHub88RevolverGamingBet      =
		BalanceCReasonHub88RevolverGamingSettle   =
		BalanceCReasonHub88RevolverGamingRollback =
		// "hub88_RubyPlay"
		BalanceCReasonHub88RubyPlayBet      =
		BalanceCReasonHub88RubyPlaySettle   =
		BalanceCReasonHub88RubyPlayRollback =
		// "hub88_SkyWind"
		BalanceCReasonHub88SkyWindBet      =
		BalanceCReasonHub88SkyWindSettle   =
		BalanceCReasonHub88SkyWindRollback =
		// "hub88_Slingshot"
		BalanceCReasonHub88SlingshotBet      =
		BalanceCReasonHub88SlingshotSettle   =
		BalanceCReasonHub88SlingshotRollback =
		// "hub88_Snowborn_Studios"
		BalanceCReasonHub88SnowbornStudiosBet      =
		BalanceCReasonHub88SnowbornStudiosSettle   =
		BalanceCReasonHub88SnowbornStudiosRollback =
		// "hub88_Spadegaming"
		BalanceCReasonHub88SpadegamingBet      =
		BalanceCReasonHub88SpadegamingSettle   =
		BalanceCReasonHub88SpadegamingRollback =
		// "hub88_SpinPlay_Games"
		BalanceCReasonHub88SpinPlayGamesBet      =
		BalanceCReasonHub88SpinPlayGamesSettle   =
		BalanceCReasonHub88SpinPlayGamesRollback =
		// "hub88_Spinomenal"
		BalanceCReasonHub88SpinomenalBet      =
		BalanceCReasonHub88SpinomenalSettle   =
		BalanceCReasonHub88SpinomenalRollback =
		// "hub88_Spribe"
		BalanceCReasonHub88SpribeBet      =
		BalanceCReasonHub88SpribeSettle   =
		BalanceCReasonHub88SpribeRollback =
		// "hub88_Stormcraft"
		BalanceCReasonHub88StormcraftBet      =
		BalanceCReasonHub88StormcraftSettle   =
		BalanceCReasonHub88StormcraftRollback =
		// "hub88_Tangente"
		BalanceCReasonHub88TangenteBet      =
		BalanceCReasonHub88TangenteSettle   =
		BalanceCReasonHub88TangenteRollback =
		// "hub88_Thunderkick"
		BalanceCReasonHub88ThunderkickBet      =
		BalanceCReasonHub88ThunderkickSettle   =
		BalanceCReasonHub88ThunderkickRollback =
		// "hub88_TopSpin_Games"
		BalanceCReasonHub88TopSpinGamesBet      =
		BalanceCReasonHub88TopSpinGamesSettle   =
		BalanceCReasonHub88TopSpinGamesRollback =
		// "hub88_Triple_Edge"
		BalanceCReasonHub88TripleEdgeBet      =
		BalanceCReasonHub88TripleEdgeSettle   =
		BalanceCReasonHub88TripleEdgeRollback =
		// "hub88_TrueLab"
		BalanceCReasonHub88TrueLabBet      =
		BalanceCReasonHub88TrueLabSettle   =
		BalanceCReasonHub88TrueLabRollback =
		// "hub88_Turbogames"
		BalanceCReasonHub88TurbogamesBet      =
		BalanceCReasonHub88TurbogamesSettle   =
		BalanceCReasonHub88TurbogamesRollback =
		// "hub88_WinFast"
		BalanceCReasonHub88WinFastBet      =
		BalanceCReasonHub88WinFastSettle   =
		BalanceCReasonHub88WinFastRollback =
		// "hub88_Yolted"
		BalanceCReasonHub88YoltedBet      =
		BalanceCReasonHub88YoltedSettle   =
		BalanceCReasonHub88YoltedRollback =
		// "hub88_Zillion":
		BalanceCReasonHub88ZillionBet      =
		BalanceCReasonHub88ZillionSettle   =
		BalanceCReasonHub88ZillionRollback = 3332
		// 截止3332
	*/

	// omg单一钱包账变类型
	BalanceCReasonOMGBet    = 3333 // omg下注
	BalanceCReasonOMGSettle = 3334 // omg结算
	BalanceCReasonOMGCancel = 3335 // omg取消

	// amigo单一钱包账变类型
	BalanceCReasonAmigoBet    = 3336 // amigo投注
	BalanceCReasonAmigoWin    = 3337 // amigo中奖
	BalanceCReasonAmigoOther  = 3338 // amigo其他
	BalanceCReasonAmigoReward = 3339 // amigo外赠

	// DB真人单一钱包账变类型
	BalanceCReasonDBLiveBet    = 3340 // DB真人下注
	BalanceCReasonDBLiveSettle = 3341 // DB真人派奖
	BalanceCReasonDBLiveCancel = 3342 // DB真人取消下注
	BalanceCReasonDBLiveReward = 3343 // DB真人返利
	BalanceCReasonDBLiveTip    = 3344 // DB真人活动和小费

	// JiLi吉利单一钱包账变类型
	BalanceCReasonJiLiBet    = 3345 // JiLi吉利下注
	BalanceCReasonJiLiWin    = 3346 // JiLi吉利中奖
	BalanceCReasonJiLiCancel = 3347 // JiLi吉利回滚

	// kaiyuan开元单一钱包账变类型
	BalanceCReasonKaiYuanBet    = 3348 // kaiyuan开元下注
	BalanceCReasonKaiYuanSettle = 3349 // kaiyuan开元派奖
	BalanceCReasonKaiYuanCancel = 3350 // kaiyuan开元撤销

	BalanceCReasonFirstDepositGift         = 52 // 新用户首存豪礼（自动）
	BalanceCReasonCumulativeWeeklyRecharge = 55 // 周累充（自动）
	BalanceCReasonSignReward               = 54 // 每日签到礼金（自动）
	BalanceCReasonRecommendFriendReward    = 53 // 推荐好友奖励（自动）

	BalanceCReasonAHashWeekendDaPaiSong    = 57 // 哈希周末狂欢送(自动)
	BalanceCReasonAQiPaihWeekendDaPaiSong  = 58 // 棋牌周末狂欢送(自动)
	BalanceCReasonADianZiWeekendDaPaiSong  = 59 // 电子周末狂欢送(自动)
	BalanceCReasonAZhenRenWeekendDaPaiSong = 60 // 真人周末狂欢送(自动)
	BalanceCReasonNewFirstDeposit          = 61 // 新首存活动180(自动)

	// BalanceCReasonCBKBet cbk 单一钱包账变类型
	BalanceCReasonCBKBet      = 3351 //板球投注
	BalanceCReasonCBKSettle   = 3352 //板球结算
	BalanceCReasonCBKCancel   = 3353 //板球撤单
	BalanceCReasonCBKResettle = 3354 //板球重新结算

	// BalanceCReasonSABABet 沙巴体育 单一钱包账变类型
	BalanceCReasonSABABet           = 3355 //沙巴体育投注
	BalanceCReasonSABAConfirm       = 3356 //沙巴体育订单确认
	BalanceCReasonSABASettle        = 3357 //沙巴体育结算
	BalanceCReasonSABAUNSettle      = 3358 //沙巴体育撤销结算
	BalanceCReasonSABACancel        = 3359 //沙巴体育撤单
	BalanceCReasonSABAResettle      = 3360 //沙巴体育重新结算
	BalanceCReasonSABAadjustBalance = 3362 //沙巴体育推广活动钱包操作

	// BalanceCReasonFBBet FB体育 单一钱包账变类型
	BalanceFB_Bet                         = 3363 // 押注
	BalanceFB_Win                         = 3364 // 派彩
	BalanceFB_Refund                      = 3365 // 退款 订单撤单
	BalanceFB_Cashout                     = 3366 // 提前结算
	BalanceFB_CancelDeduct                = 3367 // 订单取消补扣
	BalanceFB_CancelReturn                = 3368 // 订单取消返还
	BalanceFB_SettlementRollbackDeduct    = 3369 // 结算回滚补扣
	BalanceFB_CashoutCancelDeduct         = 3370 // 提前结算订单取消补扣
	BalanceFB_CashoutCancelReturn         = 3371 // 提前结算订单取消返还
	BalanceFB_CashoutCancelRollbackDeduct = 3372 // 提前结算取消回滚补扣
	BalanceFB_CashoutCancelRollbackReturn = 3373 // 提前结算取消回滚返还

	// fc发财单一钱包账变类型
	BalanceCReasonFCBet      = 3374 // fc下注
	BalanceCReasonFCWin      = 3375 // fc中奖
	BalanceCReasonFCCancel   = 3376 // fc撤单回滚
	BalanceCReasonFCSpBet    = 3377 // fc特殊游戏下注
	BalanceCReasonFCSpWin    = 3378 // fc特殊游戏中奖
	BalanceCReasonFCSpCancel = 3379 // fc特殊游戏撤单回滚

	// BGaming
	BalanceCReasonOPSBGamingBet      = 3100
	BalanceCReasonOPSBGamingSettle   = 3101
	BalanceCReasonOPSBGamingRollback = 3102

	// CQ9
	BalanceCReasonOPSCQ9Bet      = 3103
	BalanceCReasonOPSCQ9Settle   = 3104
	BalanceCReasonOPSCQ9Rollback = 3105

	// Funky Games
	BalanceCReasonOPSFunkyGamesBet      = 3106
	BalanceCReasonOPSFunkyGamesSettle   = 3107
	BalanceCReasonOPSFunkyGamesRollback = 3108

	// PlayStar
	BalanceCReasonOPSPlayStarBet      = 3109
	BalanceCReasonOPSPlayStarSettle   = 3110
	BalanceCReasonOPSPlayStarRollback = 3111

	// JDB
	BalanceCReasonOPSJDBBet      = 3112
	BalanceCReasonOPSJDBSettle   = 3113
	BalanceCReasonOPSJDBRollback = 3114

	// Hacksaw
	BalanceCReasonOPSHacksawBet      = 3115
	BalanceCReasonOPSHacksawSettle   = 3116
	BalanceCReasonOPSHacksawRollback = 3117

	// PlaynGo
	BalanceCReasonOPSPlaynGoBet      = 3118
	BalanceCReasonOPSPlaynGoSettle   = 3119
	BalanceCReasonOPSPlaynGoRollback = 3120

	// Bng
	BalanceCReasonOPSBngBet      = 3121
	BalanceCReasonOPSBngSettle   = 3122
	BalanceCReasonOPSBngRollback = 3123

	// FB真人单一钱包账变类型
	BalanceCReasonFBLiveBet         = 3401 //下注
	BalanceCReasonFBLiveSettle      = 3402 // 派彩
	BalanceCReasonFBLiveCANCELORDER = 3403 // 取消注单
	BalanceCReasonFBLiveCANCEROUND  = 3405 //整局取消
	BalanceCReasonFBLiveADDORDER    = 3406 // 系统补单
	BalanceCReasonFBLiveREPAYOUT    = 3407 //重算局
	BalanceCReasonFBLiveCancel      = 3408 // 取消下注
	BalanceCReasonFBLiveReward      = 3409 // 返利
	BalanceCReasonFBLiveTip         = 3410 // 活动和小费

	// WE真人单一钱包账变类型
	BalanceCReasonWEBet            = 3701 //下注
	BalanceCReasonWESettle         = 3702 // 派彩
	BalanceCReasonWECANCELORDER    = 3703 // 取消注单
	BalanceCReasonWERebate         = 3704 // 返水
	BalanceCReasonWETip            = 3705 //打赏
	BalanceCReasonWEREPAYOUT       = 3706 //重算局
	BalanceCReasonWECancel         = 3707 // 取消下注
	BalanceCReasonWERebateREPAYOUT = 3708 // 重新结算返水
	BalanceCReasonWEReward1        = 3709 // 免費旋轉派彩
	BalanceCReasonWEReward2        = 3710 // 獎勵遊戲派彩
	BalanceCReasonWEReward3        = 3711 // 活动派奖

	// DG真人单一钱包账变类型
	//转账类型(1:下注 2:派彩 3:补单 5:红包 6:小费)
	BalanceCReasonDGBet         = 3601 //下注
	BalanceCReasonDGSettle      = 3602 // 派彩
	BalanceCReasonDGEditOrder   = 3603 // 补单
	BalanceCReasonDGRedpack     = 3604 // 红包
	BalanceCReasonDGTip         = 3605 // 小费
	BalanceCReasonDGCancelOrder = 3606 // 退回下注
	BalanceCReasonDGReturnTip   = 3507 // 退回小费

	// BalanceCReasonQQPokerBet QQ棋牌 单一钱包账变类型
	BalanceCReasonQQPokerBet = 3901 //QQ棋牌投注
	BalanceCReasonQQPokerWin = 3902 //QQ棋牌结算

	//  日本弹珠账变类型
	BalanceCReasonPachinkoBet = 3903 //日本弹珠下分
	BalanceCReasonPachinkoWin = 3904 //日本弹珠上分

	// OFA真人单一钱包账变类型
	BalanceCReasonOFALiveBet          = 3451 //下注
	BalanceCReasonOFALiveSettle       = 3452 // 派彩
	BalanceCReasonOFALiveSettleCancel = 3453 //重算局扣款
	BalanceCReasonOFALiveCancel       = 3454 // 取消下注
	BalanceCReasonOFALiveTip          = 3455 // 小费
	BalanceCReasonOFALiveRESettle     = 3456 //重算局派奖

	// ab单一钱包账变类型
	BalanceCReasonABBet         = 3501 // ab单下注
	BalanceCReasonABWin         = 3502 // ab单中奖
	BalanceCReasonABCancel      = 3503 // ab单取消
	BalanceCReasonABRollback    = 3504 // ab单回滚
	BalanceCReasonABRWin        = 3505 //手动结算
	BalanceCReasonABTransferOut = 3506
	BalanceCReasonABActivity    = 3507 //送礼物
)

// RSG电子游戏账变类型
const (
	BalanceCReasonRSGBet       = 3721 // RSG电子游戏下注
	BalanceCReasonRSGWin       = 3722 // RSG电子游戏派彩
	BalanceCReasonRSGCancelBet = 3723 // RSG电子游戏取消下注
	BalanceCReasonRSGJackpot   = 3724 // RSG电子游戏彩池奖金
	BalanceCReasonRSGPrepay    = 3725 // RSG电子游戏预付款
	BalanceCReasonRSGRefund    = 3726 // RSG电子游戏退款
)

const (
	TableActiveDefine      = "x_active_define"
	TableActiveDefineOld   = "x_active_define_old"
	TableActiveInfo        = "x_active_info"
	TableActiveInfoOld     = "x_active_info_old"
	TableUser              = "x_user"
	TableActiveRewardAudit = "x_active_reward_audit"
	TableActiveReward      = "x_active_reward"
	TableAmountChangeLog   = "x_amount_change_log"
	TableVipInfo           = "x_vip_info"
	TableVipDailly         = "x_vip_dailly"
	TableUserDailly        = "x_user_dailly"
	TableCaiJinDetail      = "x_caijing_detail"
	TableRecharge          = "x_recharge"
)

const (
	SymbolUSDT = "usdt"
	SymbolTRX  = "trx"
)

// 周末送领取第几天
const WeekendMeiZhouSongDays = 1

// 区分上庄类型
const (
	RechargeType            = 21
	RechargeShangZhuangType = 22
	WithdrawType            = 31
	WithdrawShangZhuangType = 32
)

// 新首存活动180 判断首次充值时间在这个时间之后的就是新用户
const NewFirstDepositDate = "2024-07-06 00:00:00"

const (
	ShuangZhuangStateOpen = 1
	ShuangZhuangQueue     = 1
	ShuangZhuangCurrently = 2
	ShuangZhuangCancel    = 3
	QueueListKey          = "shangzhuang:list:%d"
	DataKey               = "shangzhuang:data:%d"
	XiaZhuangKey          = "xiazhuang"
	XiaZhuangMinTime      = 5
)

const (
	BlockTimeSecond = 5
	GamePeriodState = 1
)

const (
	// 状态 0 待审核 1审核拒绝 2审核通过  8拒绝发放 4已发放 5正在出款 6出款完成  7失败退回
	WithdrawAwaitingReview   = 0
	WithdrawExaminatioPassed = 2
	WithdrawIssued           = 4
	WithdrawalInProgress     = 5
	WithdrawalCompleted      = 6
	WithdrawFailedReturn     = 7
)

const (
	TrxType     = 1
	EthType     = 2
	BscType     = 3
	TrxTypeName = "trx"
	EthTypeName = "eth"
	BscTypeName = "bsc"
)

const (
	EthLastBlockNum       = "EthLastBlockNum"
	BscLastBlockNum       = "BscLastBlockNum"
	EthBlockUpdateMsgType = "block_update_eth"
	BscBlockUpdateMsgType = "block_update_bsc"
	TrxOrderUpdateMsgType = "order_update"
	EthOrderUpdateMsgType = "order_update_eth"
	BscOrderUpdateMsgType = "order_update_bsc"
	SolLastBlockNum       = "SolLastBlockNum"
	SolBlockUpdateMsgType = "block_update_sol"
)

var GameTypeHaXi = []int32{201, 202, 203, 204, 205}
var GameTypeOneMinuteHaXi = []int32{101, 102, 103, 104, 105}
var GameTypeThreeMinuteHaXi = []int32{131, 132, 133, 134, 135}

var GameTypeHaXiMap = map[int32]struct{}{
	201: {},
	202: {},
	203: {},
	204: {},
	205: {},
}
var GameTypeMinuteHaXiMap = map[int32]struct{}{
	101: {},
	102: {},
	103: {},
	104: {},
	105: {},
	131: {},
	132: {},
	133: {},
	134: {},
	135: {},
}

const (
	ThirdBrandOgName = "og"
)

var ThirdBrandOgGameId = []string{"29", "35"}

const (
	TopAgentPromotionHost = "%v/#/?AgentCode=%v"
)
