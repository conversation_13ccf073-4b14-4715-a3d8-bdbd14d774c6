package single

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/beego/beego/logs"
)

// OFAUser 表示OFA系统中的用户
type OFAUser struct {
	UserName      string `json:"user_name"`       // 用户名
	UserId        string `json:"user_id"`         // 用户ID
	Currency      string `json:"currency"`        // 货币类型
	UserLevel     string `json:"user_level"`      // 用户等级
	UserType      string `json:"user_type"`       // 用户类型
	ParentId      string `json:"parent_id"`       // 父级ID
	ParentName    string `json:"parent_name"`     // 父级名称
	Status        string `json:"status"`          // 状态
	Balance       string `json:"balance"`         // 余额
	CreateTime    string `json:"create_time"`     // 创建时间
	LastLoginTime string `json:"last_login_time"` // 最后登录时间
	LastLoginIP   string `json:"last_login_ip"`   // 最后登录IP
}

// OFACreateUserParams 表示创建用户的参数
type OFACreateUserParams struct {
	SystemCode string `json:"system_code"` // 系统代码
	WebId      string `json:"web_id"`      // 站台代码
	UserId     string `json:"user_id"`     // 玩家的唯一识别码
	UserName   string `json:"user_name"`   // 玩家名称
	Currency   string `json:"currency"`    // 币别代码
}

// CreateUser 在OFA系统中创建新用户
func (c *OFALiveSingleService) CreateUser(userId, userName, currency string) (*OFAUser, error) {
	params := OFACreateUserParams{
		SystemCode: c.systemCode,
		WebId:      c.webId,
		UserId:     userId,
		UserName:   userName,
		Currency:   currency,
	}

	response, err := c.sendRequest("Player/CreateUser", params)
	if err != nil {
		return nil, err
	}

	// 从响应中解析用户数据
	userData, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("error marshaling user data: %v", err)
	}

	var user OFAUser
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("error parsing user data: %v", err)
	}

	return &user, nil
}

// OFAGetUserParams 表示获取用户信息的参数
type OFAGetUserParams struct {
	SystemCode string `json:"system_code"` // 系统代码
	WebId      string `json:"web_id"`      // 站台代码
	UserId     string `json:"user_id"`     // 玩家的唯一识别码
}

// GetUser 从OFA系统获取用户信息
func (c *OFALiveSingleService) GetUser(userId string) (*OFAUser, error) {
	params := OFAGetUserParams{
		SystemCode: c.systemCode,
		WebId:      c.webId,
		UserId:     userId,
	}

	response, err := c.sendRequest("Player/CheckUser", params)
	if err != nil {
		return nil, err
	}

	// 从响应中解析用户数据
	userData, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("error marshaling user data: %v", err)
	}

	var user OFAUser
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("error parsing user data: %v", err)
	}

	return &user, nil
}

// UserExists 检查用户是否存在
func (c *OFALiveSingleService) UserExists(userId string) (bool, error) {
	// 尝试获取用户信息
	user, err := c.GetUser(userId)

	// 如果没有错误并且用户信息有效，则用户存在
	if err == nil && user != nil && user.UserId != "" {
		return true, nil
	}

	// 如果错误是"Data does not exist"，则用户不存在
	if err != nil && strings.Contains(err.Error(), "Data does not exist") {
		return false, nil
	}

	// 其他错误则返回错误
	if err != nil {
		return false, err
	}

	// 如果没有错误但用户信息无效，则用户不存在
	return false, nil
}

// GetOrCreateUserGameURL 获取游戏平台网址，如果用户不存在则先创建用户
// 该方法组合了GetUser、CreateUser和GetGameURL方法
func (c *OFALiveSingleService) GetOrCreateUserGameURL(userId, username, currency, language, exitAction string) (*OFAGameURLResult, error) {
	// 步骤1: 判断用户是否存在
	userExists, err := c.UserExists(userId)
	if err != nil {
		return nil, fmt.Errorf("检查用户是否存在失败: %v", err)
	}

	// 步骤2: 如果用户不存在，则创建用户
	if !userExists {
		if c.Debug {
			logs.Info("用户 %s 不存在，将创建新用户", userId)
		}

		_, err := c.CreateUser(userId, username, currency)
		if err != nil {
			// 如果创建用户时返回"Data already exists"错误，则表示用户已存在
			// 这可能是因为在检查用户是否存在和创建用户之间，用户被其他进程创建了
			if strings.Contains(err.Error(), "Data already exists") {
				if c.Debug {
					logs.Info("用户 %s 已存在，将直接获取游戏URL", userId)
				}
			} else {
				return nil, fmt.Errorf("创建用户失败: %v", err)
			}
		} else if c.Debug {
			logs.Info("成功创建用户 %s", userId)
		}
	} else if c.Debug {
		logs.Info("用户 %s 已存在，将直接获取游戏URL", userId)
	}

	// 步骤3: 获取游戏URL
	gameURL, err := c.GetGameURL(userId, language, exitAction)
	if err != nil {
		return nil, fmt.Errorf("获取游戏URL失败: %v", err)
	}

	return gameURL, nil
}

// OFAGetGameURLParams 表示获取游戏URL的参数
type OFAGetGameURLParams struct {
	SystemCode string `json:"system_code"` // 系统代码
	WebId      string `json:"web_id"`      // 站台代码
	UserId     string `json:"user_id"`     // 玩家的唯一识别码
	Language   string `json:"language"`    // 语言
	ExitAction string `json:"exit_action"` // 退出动作
}

// OFAGameURLResult 表示获取游戏URL操作的结果
type OFAGameURLResult struct {
	URL string `json:"url"` // 游戏URL
}

// GetGameURL 从OFA系统获取游戏URL
func (c *OFALiveSingleService) GetGameURL(userId, language, exitAction string) (*OFAGameURLResult, error) {
	params := OFAGetGameURLParams{
		SystemCode: c.systemCode,
		WebId:      c.webId,
		UserId:     userId,
		Language:   language,
		ExitAction: exitAction,
	}

	response, err := c.sendRequest("Player/GetURLToken", params)
	if err != nil {
		return nil, err
	}

	// 从响应中解析结果数据
	resultData, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化结果数据错误: %v", err)
	}

	var result OFAGameURLResult
	if err := json.Unmarshal(resultData, &result); err != nil {
		return nil, fmt.Errorf("解析结果数据错误: %v", err)
	}

	return &result, nil
}
