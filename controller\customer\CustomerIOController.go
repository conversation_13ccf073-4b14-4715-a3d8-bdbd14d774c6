package customer

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/logs"
	"github.com/customerio/go-customerio/v3"
)

// CustomerIO的siteID和apiKey
const (
	SITEID = "********************"
	APIKEY = "f23c112ec6f42b898dd8"
)

// CustomerIOController 封装了 Customer.io 的所有操作
// 提供异步的客户识别和事件跟踪功能
type CustomerIOController struct {
	client *customerio.CustomerIO
}

// NewCustomerIOController 创建并返回一个新的 CustomerIOController 实例
func NewCustomerIOController() *CustomerIOController {
	// 初始化 Customer.io 客户端

	if SITEID == "" || APIKEY == "" {
		logs.Error("Customer.io 客户端初始化失败: SITEID 或 APIKEY 为空")
		return nil
	}

	client := customerio.NewTrackClient(SITEID, APIKEY)
	if client == nil {
		logs.Error("Customer.io 客户端初始化失败: NewTrackClient 返回 nil")
		return nil
	}

	return &CustomerIOController{
		client: client,
	}
}

// IdentifyCustomer 异步创建或更新客户信息
// customerID: 客户唯一标识符
// attributes: 客户属性键值对
// 返回一个只读的错误通道
func (c *CustomerIOController) IdentifyCustomer(customerID string, attributes map[string]interface{}) <-chan error {
	errChan := make(chan error, 1)

	// 检查客户端是否为空
	if c == nil || c.client == nil {
		logs.Error("IdentifyCustomer 失败: CustomerIO 客户端未初始化或为空")
		errChan <- fmt.Errorf("CustomerIO 客户端未初始化或为空")
		close(errChan)
		return errChan
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 添加重试机制
		retryCount := 5
		var lastErr error
		for i := 0; i < retryCount; i++ {
			select {
			case <-ctx.Done():
				logs.Error("IdentifyCustomer 超时，customerID: %s", customerID)
				errChan <- ctx.Err()
				return
			default:
				if err := c.client.Identify(customerID, attributes); err == nil {
					errChan <- nil
					return
				} else {
					logs.Error("IdentifyCustomer 第 %d 次尝试失败，customerID: %s, 错误: %v", i+1, customerID, err)
					lastErr = err
					time.Sleep(time.Duration(i+1) * time.Second)
				}
			}
		}
		logs.Error("IdentifyCustomer 重试 %d 次后失败，customerID: %s, 最后错误: %v", retryCount, customerID, lastErr)
		errChan <- fmt.Errorf("重试 %d 次后失败: %w", retryCount, lastErr)
	}()
	return errChan
}

// TrackCustomerEvent 异步跟踪客户事件
// customerID: 客户唯一标识符
// eventName: 事件名称
// data: 事件数据键值对
// 返回一个只读的错误通道
func (c *CustomerIOController) TrackCustomerEvent(customerID, eventName string, data map[string]interface{}) <-chan error {
	errChan := make(chan error, 1)

	// 检查客户端是否为空
	if c == nil || c.client == nil {
		logs.Error("TrackCustomerEvent 失败: CustomerIO 客户端未初始化或为空")
		errChan <- fmt.Errorf("CustomerIO 客户端未初始化或为空")
		close(errChan)
		return errChan
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		select {
		case <-ctx.Done():
			logs.Error("TrackCustomerEvent 超时，customerID: %s, eventName: %s", customerID, eventName)
			errChan <- ctx.Err()
		default:
			if err := c.client.Track(customerID, eventName, data); err != nil {
				logs.Error("TrackCustomerEvent 失败，customerID: %s, eventName: %s, 错误: %v", customerID, eventName, err)
				errChan <- err
			} else {
				errChan <- nil
			}
		}
	}()
	return errChan
}

// ExecuteIdentifyAndTrack 异步执行客户识别和事件跟踪
// customerID: 客户唯一标识符
// attributes: 客户属性键值对
// eventName: 事件名称
// eventData: 事件数据键值对
// 返回一个只读的错误通道
func (c *CustomerIOController) ExecuteIdentifyAndTrack(customerID string, attributes map[string]interface{}, eventName string, eventData map[string]interface{}) <-chan error {
	errChan := make(chan error, 1)

	// 检查客户端是否为空
	if c == nil || c.client == nil {
		logs.Error("ExecuteIdentifyAndTrack 失败: CustomerIO 客户端未初始化或为空")
		errChan <- fmt.Errorf("CustomerIO 客户端未初始化或为空")
		close(errChan)
		return errChan
	}

	go func() {

		// 异步识别客户
		identifyErr := <-c.IdentifyCustomer(customerID, attributes)
		if identifyErr != nil {
			logs.Error("ExecuteIdentifyAndTrack 客户识别失败，customerID: %s, 错误: %v", customerID, identifyErr)
			errChan <- fmt.Errorf("[CIO-1001] 客户识别失败: %v (请检查客户ID格式是否正确，并确保网络连接正常)", identifyErr)
			return
		}

		// 异步跟踪事件
		trackErr := <-c.TrackCustomerEvent(customerID, eventName, eventData)
		if trackErr != nil {
			logs.Error("ExecuteIdentifyAndTrack 事件跟踪失败，customerID: %s, eventName: %s, 错误: %v", customerID, eventName, trackErr)
			errChan <- fmt.Errorf("[CIO-1002] 事件跟踪失败: %v (请检查事件名称和数据格式是否正确)", trackErr)
			return
		}

		errChan <- nil
	}()
	return errChan
}

// Run 执行完整的客户识别和事件跟踪流程
// customerID: 客户唯一标识符
// attributes: 客户属性键值对
// eventName: 事件名称
// eventData: 事件数据键值对
func (c *CustomerIOController) Run(customerID string, attributes map[string]interface{}, eventName string, eventData map[string]interface{}) {

	// 检查客户端是否为空
	if c == nil || c.client == nil {
		logs.Error("[CIO-1000] 操作失败: CustomerIO 客户端未初始化或为空")
		return
	}

	errChan := c.ExecuteIdentifyAndTrack(customerID, attributes, eventName, eventData)
	if err := <-errChan; err != nil {
		logs.Error("[CIO-1003] 操作失败",
			"error", err,
			"message", "请检查配置是否正确，并确保API服务可用")
		return
	}
}
