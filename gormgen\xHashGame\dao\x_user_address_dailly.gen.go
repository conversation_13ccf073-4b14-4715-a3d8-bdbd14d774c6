// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserAddressDailly(db *gorm.DB, opts ...gen.DOOption) xUserAddressDailly {
	_xUserAddressDailly := xUserAddressDailly{}

	_xUserAddressDailly.xUserAddressDaillyDo.UseDB(db, opts...)
	_xUserAddressDailly.xUserAddressDaillyDo.UseModel(&model.XUserAddressDailly{})

	tableName := _xUserAddressDailly.xUserAddressDaillyDo.TableName()
	_xUserAddressDailly.ALL = field.NewAsterisk(tableName)
	_xUserAddressDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserAddressDailly.Address = field.NewString(tableName, "Address")
	_xUserAddressDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xUserAddressDailly.BetCountTrx = field.NewInt32(tableName, "BetCountTrx")
	_xUserAddressDailly.BetCountUsdt = field.NewInt32(tableName, "BetCountUsdt")
	_xUserAddressDailly.BetTrx = field.NewFloat64(tableName, "BetTrx")
	_xUserAddressDailly.BetUsdt = field.NewFloat64(tableName, "BetUsdt")
	_xUserAddressDailly.RewardTrx = field.NewFloat64(tableName, "RewardTrx")
	_xUserAddressDailly.RewardUsdt = field.NewFloat64(tableName, "RewardUsdt")
	_xUserAddressDailly.LiuSuiTrx = field.NewFloat64(tableName, "LiuSuiTrx")
	_xUserAddressDailly.LiuSuiUsdt = field.NewFloat64(tableName, "LiuSuiUsdt")
	_xUserAddressDailly.IsNew = field.NewInt32(tableName, "IsNew")
	_xUserAddressDailly.IsValid = field.NewInt32(tableName, "IsValid")
	_xUserAddressDailly.BetSymbol = field.NewString(tableName, "BetSymbol")
	_xUserAddressDailly.IsValidTrx = field.NewInt32(tableName, "IsValidTrx")
	_xUserAddressDailly.IsValidUsdt = field.NewInt32(tableName, "IsValidUsdt")

	_xUserAddressDailly.fillFieldMap()

	return _xUserAddressDailly
}

type xUserAddressDailly struct {
	xUserAddressDaillyDo xUserAddressDaillyDo

	ALL          field.Asterisk
	ChannelID    field.Int32
	Address      field.String
	RecordDate   field.Time
	BetCountTrx  field.Int32
	BetCountUsdt field.Int32
	BetTrx       field.Float64
	BetUsdt      field.Float64
	RewardTrx    field.Float64
	RewardUsdt   field.Float64
	LiuSuiTrx    field.Float64
	LiuSuiUsdt   field.Float64
	IsNew        field.Int32 // 是否当日新增 1是 2不是
	IsValid      field.Int32 //  是否有效 1是 2不是
	BetSymbol    field.String
	IsValidTrx   field.Int32 //  是否有效 1是 2不是
	IsValidUsdt  field.Int32 //  是否有效 1是 2不是

	fieldMap map[string]field.Expr
}

func (x xUserAddressDailly) Table(newTableName string) *xUserAddressDailly {
	x.xUserAddressDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserAddressDailly) As(alias string) *xUserAddressDailly {
	x.xUserAddressDaillyDo.DO = *(x.xUserAddressDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserAddressDailly) updateTableName(table string) *xUserAddressDailly {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Address = field.NewString(table, "Address")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.BetCountTrx = field.NewInt32(table, "BetCountTrx")
	x.BetCountUsdt = field.NewInt32(table, "BetCountUsdt")
	x.BetTrx = field.NewFloat64(table, "BetTrx")
	x.BetUsdt = field.NewFloat64(table, "BetUsdt")
	x.RewardTrx = field.NewFloat64(table, "RewardTrx")
	x.RewardUsdt = field.NewFloat64(table, "RewardUsdt")
	x.LiuSuiTrx = field.NewFloat64(table, "LiuSuiTrx")
	x.LiuSuiUsdt = field.NewFloat64(table, "LiuSuiUsdt")
	x.IsNew = field.NewInt32(table, "IsNew")
	x.IsValid = field.NewInt32(table, "IsValid")
	x.BetSymbol = field.NewString(table, "BetSymbol")
	x.IsValidTrx = field.NewInt32(table, "IsValidTrx")
	x.IsValidUsdt = field.NewInt32(table, "IsValidUsdt")

	x.fillFieldMap()

	return x
}

func (x *xUserAddressDailly) WithContext(ctx context.Context) *xUserAddressDaillyDo {
	return x.xUserAddressDaillyDo.WithContext(ctx)
}

func (x xUserAddressDailly) TableName() string { return x.xUserAddressDaillyDo.TableName() }

func (x xUserAddressDailly) Alias() string { return x.xUserAddressDaillyDo.Alias() }

func (x xUserAddressDailly) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserAddressDaillyDo.Columns(cols...)
}

func (x *xUserAddressDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserAddressDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 16)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["BetCountTrx"] = x.BetCountTrx
	x.fieldMap["BetCountUsdt"] = x.BetCountUsdt
	x.fieldMap["BetTrx"] = x.BetTrx
	x.fieldMap["BetUsdt"] = x.BetUsdt
	x.fieldMap["RewardTrx"] = x.RewardTrx
	x.fieldMap["RewardUsdt"] = x.RewardUsdt
	x.fieldMap["LiuSuiTrx"] = x.LiuSuiTrx
	x.fieldMap["LiuSuiUsdt"] = x.LiuSuiUsdt
	x.fieldMap["IsNew"] = x.IsNew
	x.fieldMap["IsValid"] = x.IsValid
	x.fieldMap["BetSymbol"] = x.BetSymbol
	x.fieldMap["IsValidTrx"] = x.IsValidTrx
	x.fieldMap["IsValidUsdt"] = x.IsValidUsdt
}

func (x xUserAddressDailly) clone(db *gorm.DB) xUserAddressDailly {
	x.xUserAddressDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserAddressDailly) replaceDB(db *gorm.DB) xUserAddressDailly {
	x.xUserAddressDaillyDo.ReplaceDB(db)
	return x
}

type xUserAddressDaillyDo struct{ gen.DO }

func (x xUserAddressDaillyDo) Debug() *xUserAddressDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserAddressDaillyDo) WithContext(ctx context.Context) *xUserAddressDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserAddressDaillyDo) ReadDB() *xUserAddressDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserAddressDaillyDo) WriteDB() *xUserAddressDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserAddressDaillyDo) Session(config *gorm.Session) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserAddressDaillyDo) Clauses(conds ...clause.Expression) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserAddressDaillyDo) Returning(value interface{}, columns ...string) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserAddressDaillyDo) Not(conds ...gen.Condition) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserAddressDaillyDo) Or(conds ...gen.Condition) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserAddressDaillyDo) Select(conds ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserAddressDaillyDo) Where(conds ...gen.Condition) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserAddressDaillyDo) Order(conds ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserAddressDaillyDo) Distinct(cols ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserAddressDaillyDo) Omit(cols ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserAddressDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserAddressDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserAddressDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserAddressDaillyDo) Group(cols ...field.Expr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserAddressDaillyDo) Having(conds ...gen.Condition) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserAddressDaillyDo) Limit(limit int) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserAddressDaillyDo) Offset(offset int) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserAddressDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserAddressDaillyDo) Unscoped() *xUserAddressDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserAddressDaillyDo) Create(values ...*model.XUserAddressDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserAddressDaillyDo) CreateInBatches(values []*model.XUserAddressDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserAddressDaillyDo) Save(values ...*model.XUserAddressDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserAddressDaillyDo) First() (*model.XUserAddressDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddressDailly), nil
	}
}

func (x xUserAddressDaillyDo) Take() (*model.XUserAddressDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddressDailly), nil
	}
}

func (x xUserAddressDaillyDo) Last() (*model.XUserAddressDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddressDailly), nil
	}
}

func (x xUserAddressDaillyDo) Find() ([]*model.XUserAddressDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserAddressDailly), err
}

func (x xUserAddressDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserAddressDailly, err error) {
	buf := make([]*model.XUserAddressDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserAddressDaillyDo) FindInBatches(result *[]*model.XUserAddressDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserAddressDaillyDo) Attrs(attrs ...field.AssignExpr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserAddressDaillyDo) Assign(attrs ...field.AssignExpr) *xUserAddressDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserAddressDaillyDo) Joins(fields ...field.RelationField) *xUserAddressDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserAddressDaillyDo) Preload(fields ...field.RelationField) *xUserAddressDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserAddressDaillyDo) FirstOrInit() (*model.XUserAddressDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddressDailly), nil
	}
}

func (x xUserAddressDaillyDo) FirstOrCreate() (*model.XUserAddressDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddressDailly), nil
	}
}

func (x xUserAddressDaillyDo) FindByPage(offset int, limit int) (result []*model.XUserAddressDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserAddressDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserAddressDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserAddressDaillyDo) Delete(models ...*model.XUserAddressDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserAddressDaillyDo) withDO(do gen.Dao) *xUserAddressDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
