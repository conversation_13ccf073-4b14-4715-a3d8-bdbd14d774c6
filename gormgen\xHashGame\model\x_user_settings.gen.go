// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserSetting = "x_user_settings"

// XUserSetting 用户设置
type XUserSetting struct {
	UserID     int32     `gorm:"column:UserId;primaryKey;comment:用户id" json:"UserId"` // 用户id
	SellerID   int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`       // 运营商id
	ChannelID  int32     `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`      // 渠道id
	TopAgentID int32     `gorm:"column:TopAgentId;comment:顶级代理" json:"TopAgentId"`    // 顶级代理
	AgentID    int32     `gorm:"column:AgentId;comment:代理id" json:"AgentId"`          // 代理id
	ChipList   string    `gorm:"column:ChipList;comment:筹码" json:"ChipList"`          // 筹码
	Memo       string    `gorm:"column:Memo" json:"Memo"`
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XUserSetting's table name
func (*XUserSetting) TableName() string {
	return TableNameXUserSetting
}
