package active

import (
	"context"
	"encoding/json"
	"errors"
	"time"
	"xserver/gormgen/xHashGame/dao"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/model"

	"xserver/server"
	"xserver/utils"

	"gorm.io/gorm"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
)

// 获取活动定义
func GetActiveDefine(sellerId, channelId, activeId int32) (model.ActiveDefine, error) {
	// 使用 gen 方式查询
	activeDefineTb := server.DaoxHashGame().XActiveDefine
	activeDefineDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	// 执行查询
	aDefine, err := activeDefineDb.Where(activeDefineTb.SellerID.Eq(sellerId)).
		Where(activeDefineTb.ChannelID.Eq(channelId)).
		Where(activeDefineTb.ActiveID.Eq(activeId)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Info("GetActiveDefine 未找到活动配置: SellerId=%d, ChannelId=%d, ActiveId=%d",
				sellerId, channelId, activeId)
		} else {
			logs.Error("GetActiveDefine 查询错误: SellerId=%d, ChannelId=%d, ActiveId=%d, Error=%v",
				sellerId, channelId, activeId, err)
		}
		return model.ActiveDefine{}, err
	}

	logs.Info("GetActiveDefine 查询成功: SellerId=%d, ChannelId=%d, ActiveId=%d, Title=%s, State=%d",
		sellerId, channelId, activeId, aDefine.Title, aDefine.State)

	// 将 gen 模型转换为原始模型
	result := model.ActiveDefine{
		Id:              int(aDefine.ID),
		SellerId:        int(aDefine.SellerID),
		ChannelId:       int(aDefine.ChannelID),
		ActiveId:        int(aDefine.ActiveID),
		Memo:            aDefine.Memo,
		AuditType:       int(aDefine.AuditType),
		State:           int(aDefine.State),
		Sort:            int(aDefine.Sort),
		EffectStartTime: aDefine.EffectStartTime,
		EffectEndTime:   aDefine.EffectEndTime,
		Title:           aDefine.Title,
		TitleImg:        aDefine.TitleImg,
		MinLiuShui:      decimal.NewFromFloat(aDefine.MinLiuShui),
		ExtReward:       decimal.NewFromFloat(aDefine.ExtReward),
		MinDeposit:      decimal.NewFromFloat(aDefine.MinDeposit),
		MaxReward:       decimal.NewFromFloat(aDefine.MaxReward),
		ValidRecharge:   decimal.NewFromFloat(aDefine.ValidRecharge),
		ValidLiuShui:    decimal.NewFromFloat(aDefine.ValidLiuShui),
		TrxPrice:        decimal.NewFromFloat(aDefine.TrxPrice),
		Config:          aDefine.Config,
		BaseConfig:      aDefine.BaseConfig,
		GameType:        aDefine.GameType,
	}

	return result, nil
}

func VerifyActiveDefine(ADefine model.ActiveDefine, tn time.Time) (bool, error) {
	//筛选出符合条件的次日奖励活动项
	if ADefine.Id == 0 {
		return false, errors.New(utils.ActiveRNotDefine)
	}
	//校验状态
	if ADefine.State != utils.ActiveStateOpen {
		return false, errors.New(utils.ActiveRTimeExpire)
	}
	//校验时间
	if ADefine.EffectStartTime != 0 &&
		ADefine.EffectEndTime != 0 {
		LTime, RTime := time.UnixMilli(ADefine.EffectStartTime), time.UnixMilli(ADefine.EffectEndTime)
		if !utils.IsTimeBetween(tn, LTime, RTime) {
			return false, errors.New(utils.ActiveRTimeExpire)
		}
	}
	return true, nil
}

func VerifyActiveInfo(AInfo model.ActiveInfo) error {
	if AInfo.Id == 0 || AInfo.Level == 0 {
		return errors.New(utils.ActiveRLevelErr)
	}
	return nil
}

func SaveActiveData(tx *dao.Query, user *model2.XUser, dataInfo model.SaveActiveDataInfo) error {
	defer recover()
	if tx == nil {
		tx = server.DaoxHashGame()
	}
	now := carbon.Parse(carbon.Now().String()).StdTime()
	date := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	minLiuShui, _ := dataInfo.MinLiuShui.Float64()
	if dataInfo.AuditType == 1 { // 需要审核
		rerr := tx.Transaction(func(tx *dao.Query) error {
			// 玩家日统计
			userDaillyTb := tx.XUserDailly
			userDaillyDb := tx.XUserDailly.WithContext(context.Background())
			_, err := userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					userDailly := &model2.XUserDailly{
						SellerID:   dataInfo.SellerID,
						ChannelID:  dataInfo.ChannelID,
						UserID:     user.UserID,
						RecordDate: date,
					}
					err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
					if err != nil {
						logs.Error(err)
						return err
					}
				} else {
					logs.Error(err)
					return err
				}
			}
			data := &model2.XActiveRewardAudit{
				SellerID:          dataInfo.SellerID,
				ChannelID:         dataInfo.ChannelID,
				UserID:            user.UserID,
				ActiveID:          int32(dataInfo.ActiveId),
				ActiveMemo:        dataInfo.ActiveMemo,
				ActiveLevel:       int32(dataInfo.Level),
				RecordDate:        now,
				Amount:            dataInfo.RealAmount,
				AuditState:        1,
				CreateTime:        now,
				LiuShui:           dataInfo.TotalLiushui,
				Config:            string(dataInfo.ConfigStr),
				FirstRecharge:     dataInfo.FirstRecharge,
				BaseConfig:        string(dataInfo.BastConfigStr),
				TotalRecharge:     dataInfo.TotalRecharge,
				InviteRewardType:  dataInfo.InviteRewardType,
				ChildUserID:       dataInfo.ChildUserID,
				InviteRewardUsers: dataInfo.InviteRewardUsers,
				OrderID:           dataInfo.OrderId,
				GameType:          dataInfo.GameType,
				MinLiuShui:        minLiuShui,
				UserIP:            dataInfo.UserIP,
				DeviceID:          dataInfo.DeviceID,
			}
			// 签到时间
			if dataInfo.FirstSignTime != nil {
				data.FirstSignTime = *dataInfo.FirstSignTime
			}
			if dataInfo.LastSignTime != nil {
				data.LastSignTime = *dataInfo.LastSignTime
			}
			activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
			activeRewardAuditDb := tx.XActiveRewardAudit.WithContext(context.Background())
			if dataInfo.FirstSignTime == nil && dataInfo.LastSignTime == nil {
				activeRewardAuditDb = activeRewardAuditDb.Omit(activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime)
			}
			err = activeRewardAuditDb.Select(activeRewardAuditTb.SellerID, activeRewardAuditTb.ChannelID,
				activeRewardAuditTb.UserID, activeRewardAuditTb.ActiveID, activeRewardAuditTb.ActiveMemo,
				activeRewardAuditTb.ActiveLevel, activeRewardAuditTb.RecordDate, activeRewardAuditTb.Amount,
				activeRewardAuditTb.AuditState, activeRewardAuditTb.CreateTime, activeRewardAuditTb.LiuShui,
				activeRewardAuditTb.Config, activeRewardAuditTb.FirstRecharge, activeRewardAuditTb.BaseConfig,
				activeRewardAuditTb.TotalRecharge, activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime,
				activeRewardAuditTb.InviteRewardType, activeRewardAuditTb.ChildUserID, activeRewardAuditTb.InviteRewardUsers,
				activeRewardAuditTb.OrderID, activeRewardAuditTb.GameType, activeRewardAuditTb.MinLiuShui,
				activeRewardAuditTb.UserIP, activeRewardAuditTb.DeviceID,
			).Create(data)
			if err != nil {
				logs.Error(err)
				return err
			}
			return nil
		})
		if rerr != nil {
			return rerr
		}
	} else {
		rerr := tx.Transaction(func(tx *dao.Query) error {
			// 玩家日统计
			userDaillyTb := tx.XUserDailly
			userDaillyDb := tx.XUserDailly.WithContext(context.Background())
			_, err := userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					userDailly := &model2.XUserDailly{
						SellerID:   dataInfo.SellerID,
						ChannelID:  dataInfo.ChannelID,
						UserID:     user.UserID,
						RecordDate: date,
					}
					err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
					if err != nil {
						logs.Error(err)
						return err
					}
				} else {
					logs.Error(err)
					return err
				}
			}
			reward := &model2.XActiveReward{
				SellerID:   dataInfo.SellerID,
				ChannelID:  dataInfo.ChannelID,
				UserID:     user.UserID,
				State:      4,
				ActiveID:   10000 + int32(dataInfo.ActiveId),
				ActiveName: dataInfo.ActiveName,
				Amount:     dataInfo.RealAmount,
				AuditTime:  now,
				CreateTime: now,
			}
			activeRewardDb := tx.XActiveReward.WithContext(context.Background())
			err = activeRewardDb.Create(reward)
			if err != nil {
				logs.Error(err)
				return err
			}
			audit := &model2.XActiveRewardAudit{
				SellerID:          dataInfo.SellerID,
				ChannelID:         dataInfo.ChannelID,
				UserID:            user.UserID,
				ActiveID:          int32(dataInfo.ActiveId),
				ActiveMemo:        dataInfo.ActiveMemo,
				ActiveLevel:       int32(dataInfo.Level),
				RecordDate:        now,
				Amount:            dataInfo.RealAmount,
				AuditState:        4,
				CreateTime:        now,
				AuditTime:         now,
				LiuShui:           dataInfo.TotalLiushui,
				Config:            string(dataInfo.ConfigStr),
				FirstRecharge:     dataInfo.FirstRecharge,
				BaseConfig:        string(dataInfo.BastConfigStr),
				TotalRecharge:     dataInfo.TotalRecharge,
				InviteRewardType:  dataInfo.InviteRewardType,
				ChildUserID:       dataInfo.ChildUserID,
				InviteRewardUsers: dataInfo.InviteRewardUsers,
				OrderID:           dataInfo.OrderId,
				GameType:          dataInfo.GameType,
				MinLiuShui:        minLiuShui,
				UserIP:            dataInfo.UserIP,
				DeviceID:          dataInfo.DeviceID,
			}
			// 签到时间
			if dataInfo.FirstSignTime != nil {
				audit.FirstSignTime = *dataInfo.FirstSignTime
			}
			if dataInfo.LastSignTime != nil {
				audit.LastSignTime = *dataInfo.LastSignTime
			}
			activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
			activeRewardAuditDb := tx.XActiveRewardAudit.WithContext(context.Background())
			if dataInfo.FirstSignTime == nil && dataInfo.LastSignTime == nil {
				activeRewardAuditDb = activeRewardAuditDb.Omit(activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime)
			}
			err = activeRewardAuditDb.Select(activeRewardAuditTb.SellerID, activeRewardAuditTb.ChannelID,
				activeRewardAuditTb.UserID, activeRewardAuditTb.ActiveID, activeRewardAuditTb.ActiveMemo,
				activeRewardAuditTb.ActiveLevel, activeRewardAuditTb.RecordDate, activeRewardAuditTb.Amount,
				activeRewardAuditTb.AuditState, activeRewardAuditTb.CreateTime, activeRewardAuditTb.AuditTime, activeRewardAuditTb.LiuShui,
				activeRewardAuditTb.Config, activeRewardAuditTb.FirstRecharge, activeRewardAuditTb.BaseConfig,
				activeRewardAuditTb.TotalRecharge, activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime,
				activeRewardAuditTb.InviteRewardType, activeRewardAuditTb.ChildUserID, activeRewardAuditTb.InviteRewardUsers,
				activeRewardAuditTb.OrderID, activeRewardAuditTb.GameType, activeRewardAuditTb.MinLiuShui,
				activeRewardAuditTb.UserIP, activeRewardAuditTb.DeviceID,
			).Create(audit)
			if err != nil {
				logs.Error(err)
				return err
			}
			userTb := server.DaoxHashGame().XUser
			userDb := tx.XUser.WithContext(context.Background())
			if user.WithdrawLiuSui > user.TotalLiuSui {
				_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
					"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
					"WithdrawLiuSui": gorm.Expr("WithdrawLiuSui + ?", dataInfo.WithdrawLiuSuiAdd),
				})
			} else {
				_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
					"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
					"WithdrawLiuSui": dataInfo.WithdrawLiuSuiAdd,
					"TotalLiuSui":    0,
				})
			}
			if err != nil {
				logs.Error(err)
				return err
			}

			amountChangeLog := &model2.XAmountChangeLog{
				UserID:       user.UserID,
				BeforeAmount: user.Amount,
				Amount:       dataInfo.RealAmount,
				AfterAmount:  user.Amount + dataInfo.RealAmount,
				Reason:       int32(dataInfo.BalanceCReason),
				Memo:         dataInfo.ActiveName,
				SellerID:     dataInfo.SellerID,
				ChannelID:    dataInfo.ChannelID,
			}
			amountChangeLogDB := tx.XAmountChangeLog.WithContext(context.Background())
			err = amountChangeLogDB.Create(amountChangeLog)
			if err != nil {
				logs.Error(err)
				return err
			}

			_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).Updates(map[string]any{
				"TotalCaiJin": gorm.Expr("TotalCaiJin + ?", dataInfo.RealAmount),
			})

			vipInfoTb := tx.XVipInfo
			vipInfoDb := tx.XVipInfo.WithContext(context.Background())
			_, err = vipInfoDb.Where(vipInfoTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"CaiJin": gorm.Expr("CaiJin + ?", dataInfo.RealAmount),
			})
			if dataInfo.RealAmount > 0 {
				caijingDetailDb := tx.XCaijingDetail.WithContext(context.Background())
				caijingDetail := &model2.XCaijingDetail{
					UserID:     user.UserID,
					SType:      dataInfo.ActiveName,
					Symbol:     "usdt",
					Amount:     dataInfo.RealAmount,
					CSGroup:    user.CSGroup,
					CSID:       user.CSID,
					TopAgentID: user.TopAgentID,
					MinLiuShui: minLiuShui,
				}
				err = caijingDetailDb.Create(caijingDetail)
				if err != nil {
					logs.Error(err)
					return err
				}
			}
			// 彩金统计
			raw := server.Db().Gorm().Raw("CALL SystemManage_x_user_reward_commission_Stat(?,?,?,?,?,?,?,?)", now.String(), user.UserID,
				dataInfo.SellerID, dataInfo.ChannelID, user.TopAgentID, 1, 1, dataInfo.RealAmount)
			err = raw.Error
			if err != nil {
				logs.Error("CALL SystemManage_x_user_reward_commission_Stat err :", err)
				logs.Error("CALL SystemManage_x_user_reward_commission_Stat parameter :", now.String(), user.UserID,
					dataInfo.SellerID, dataInfo.ChannelID, user.TopAgentID, 1, 1, dataInfo.RealAmount)
				return err
			}
			return nil
		})
		if rerr != nil {
			return rerr
		}
	}
	return nil
}

// 计算用户总流水
// userId: 用户ID
// startTime: 开始时间
// endTime: 结束时间
// minBetAmount: 单笔投注最小限制，为0表示不限制
// maxBetAmount: 单笔投注最大限制，为0表示不限制
// isExcludeBetLimit: 是否排除超出投注限制的流水
// sellerId: 运营商ID
// channelId: 渠道ID
// 返回: 总流水, 错误信息
func CalculateUserTotalFlow(user *model2.XUser, startTime time.Time, endTime time.Time, minBetAmount, maxBetAmount decimal.Decimal, isExcludeBetLimit bool) (decimal.Decimal, error) {
	// 初始化总流水为0
	total := decimal.NewFromFloat(0)

	// 定义流水汇总结构体
	type BetAmountSum struct {
		TotalBetAmount decimal.Decimal
	}

	// 1. 查询 x_order 表中的 amount 字段
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())

	type OrderAmountSum struct {
		TotalAmount decimal.Decimal
	}
	var orderAmountSum OrderAmountSum

	var err error

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		orderQuery := orderDb.Where(orderTb.UserID.Eq(user.UserID)).
			Where(orderTb.SellerID.Eq(user.SellerID)).
			Where(orderTb.ChannelID.Eq(user.ChannelID)).
			Where(orderTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			orderQuery = orderQuery.Where(orderTb.ValidBetAmount.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			orderQuery = orderQuery.Where(orderTb.ValidBetAmount.Lte(maxBetFloat))
		}

		// 执行查询
		err = orderQuery.Select(orderTb.ValidBetAmount.Sum().As("TotalAmount")).
			Scan(&orderAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_order amount with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = orderDb.Select(orderTb.ValidBetAmount.Sum().As("TotalAmount")).
			Where(orderTb.UserID.Eq(user.UserID)).
			Where(orderTb.SellerID.Eq(user.SellerID)).
			Where(orderTb.ChannelID.Eq(user.ChannelID)).
			Where(orderTb.CreateTime.Between(startTime, endTime)).
			Scan(&orderAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_order amount err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_order 表的流水
	total = total.Add(orderAmountSum.TotalAmount)

	// 2. 查询 x_third_dianzhi 表中的 ValidBet 字段（有效流水）
	thirdDianzhiTb := server.DaoxHashGame().XThirdDianzhi
	thirdDianzhiDb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())

	var dianzhiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		dianzhiQuery := thirdDianzhiDb.Where(thirdDianzhiTb.UserID.Eq(user.UserID)).
			Where(thirdDianzhiTb.SellerID.Eq(user.SellerID)).
			Where(thirdDianzhiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdDianzhiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			dianzhiQuery = dianzhiQuery.Where(thirdDianzhiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			dianzhiQuery = dianzhiQuery.Where(thirdDianzhiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = dianzhiQuery.Select(thirdDianzhiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&dianzhiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_dianzhi ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdDianzhiDb.Select(thirdDianzhiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdDianzhiTb.UserID.Eq(user.UserID)).
			Where(thirdDianzhiTb.SellerID.Eq(user.SellerID)).
			Where(thirdDianzhiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdDianzhiTb.CreateTime.Between(startTime, endTime)).
			Scan(&dianzhiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_dianzhi ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_dianzhi 表的流水
	total = total.Add(dianzhiBetAmountSum.TotalBetAmount)

	// 3. 查询 x_third_live 表中的 ValidBet 字段（有效流水）
	thirdLiveTb := server.DaoxHashGame().XThirdLive
	thirdLiveDb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())

	var liveBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		liveQuery := thirdLiveDb.Where(thirdLiveTb.UserID.Eq(user.UserID)).
			Where(thirdLiveTb.SellerID.Eq(user.SellerID)).
			Where(thirdLiveTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLiveTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			liveQuery = liveQuery.Where(thirdLiveTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			liveQuery = liveQuery.Where(thirdLiveTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = liveQuery.Select(thirdLiveTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&liveBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_live ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdLiveDb.Select(thirdLiveTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdLiveTb.UserID.Eq(user.UserID)).
			Where(thirdLiveTb.SellerID.Eq(user.SellerID)).
			Where(thirdLiveTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLiveTb.CreateTime.Between(startTime, endTime)).
			Scan(&liveBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_live ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_live 表的流水
	total = total.Add(liveBetAmountSum.TotalBetAmount)

	// 4. 查询 x_third_lottery 表中的 ValidBet 字段（有效流水）
	thirdLotteryTb := server.DaoxHashGame().XThirdLottery
	thirdLotteryDb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())

	var lotteryBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		lotteryQuery := thirdLotteryDb.Where(thirdLotteryTb.UserID.Eq(user.UserID)).
			Where(thirdLotteryTb.SellerID.Eq(user.SellerID)).
			Where(thirdLotteryTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLotteryTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			lotteryQuery = lotteryQuery.Where(thirdLotteryTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			lotteryQuery = lotteryQuery.Where(thirdLotteryTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = lotteryQuery.Select(thirdLotteryTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&lotteryBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_lottery ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdLotteryDb.Select(thirdLotteryTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdLotteryTb.UserID.Eq(user.UserID)).
			Where(thirdLotteryTb.SellerID.Eq(user.SellerID)).
			Where(thirdLotteryTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLotteryTb.CreateTime.Between(startTime, endTime)).
			Scan(&lotteryBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_lottery ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_lottery 表的流水
	total = total.Add(lotteryBetAmountSum.TotalBetAmount)

	// 5. 查询 x_third_qipai 表中的 ValidBet 字段（有效流水）
	thirdQipaiTb := server.DaoxHashGame().XThirdQipai
	thirdQipaiDb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())

	var qipaiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		qipaiQuery := thirdQipaiDb.Where(thirdQipaiTb.UserID.Eq(user.UserID)).
			Where(thirdQipaiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQipaiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQipaiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			qipaiQuery = qipaiQuery.Where(thirdQipaiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			qipaiQuery = qipaiQuery.Where(thirdQipaiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = qipaiQuery.Select(thirdQipaiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&qipaiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_qipai ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdQipaiDb.Select(thirdQipaiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdQipaiTb.UserID.Eq(user.UserID)).
			Where(thirdQipaiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQipaiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQipaiTb.CreateTime.Between(startTime, endTime)).
			Scan(&qipaiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_qipai ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_qipai 表的流水
	total = total.Add(qipaiBetAmountSum.TotalBetAmount)

	// 6. 查询 x_third_quwei 表中的 ValidBet 字段（有效流水）
	thirdQuweiTb := server.DaoxHashGame().XThirdQuwei
	thirdQuweiDb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())

	var quweiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		quweiQuery := thirdQuweiDb.Where(thirdQuweiTb.UserID.Eq(user.UserID)).
			Where(thirdQuweiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQuweiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQuweiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			quweiQuery = quweiQuery.Where(thirdQuweiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			quweiQuery = quweiQuery.Where(thirdQuweiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = quweiQuery.Select(thirdQuweiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&quweiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_quwei ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdQuweiDb.Select(thirdQuweiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdQuweiTb.UserID.Eq(user.UserID)).
			Where(thirdQuweiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQuweiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQuweiTb.CreateTime.Between(startTime, endTime)).
			Scan(&quweiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_quwei ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_quwei 表的流水
	total = total.Add(quweiBetAmountSum.TotalBetAmount)

	// 7. 查询 x_third_sport 表中的 ValidBet 字段（有效流水）
	thirdSportTb := server.DaoxHashGame().XThirdSport
	thirdSportDb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())

	var sportBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		sportQuery := thirdSportDb.Where(thirdSportTb.UserID.Eq(user.UserID)).
			Where(thirdSportTb.SellerID.Eq(user.SellerID)).
			Where(thirdSportTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdSportTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			sportQuery = sportQuery.Where(thirdSportTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			sportQuery = sportQuery.Where(thirdSportTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = sportQuery.Select(thirdSportTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&sportBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_sport ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdSportDb.Select(thirdSportTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdSportTb.UserID.Eq(user.UserID)).
			Where(thirdSportTb.SellerID.Eq(user.SellerID)).
			Where(thirdSportTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdSportTb.CreateTime.Between(startTime, endTime)).
			Scan(&sportBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_sport ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_sport 表的流水
	total = total.Add(sportBetAmountSum.TotalBetAmount)

	// 8. 查询 x_third_texas 表中的 ValidBet 字段（有效流水）
	thirdTexasTb := server.DaoxHashGame().XThirdTexa
	thirdTexasDb := server.DaoxHashGame().XThirdTexa.WithContext(context.Background())

	var texasBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		texasQuery := thirdTexasDb.Where(thirdTexasTb.UserID.Eq(user.UserID)).
			Where(thirdTexasTb.SellerID.Eq(user.SellerID)).
			Where(thirdTexasTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdTexasTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			texasQuery = texasQuery.Where(thirdTexasTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			texasQuery = texasQuery.Where(thirdTexasTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = texasQuery.Select(thirdTexasTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&texasBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_texas ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdTexasDb.Select(thirdTexasTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdTexasTb.UserID.Eq(user.UserID)).
			Where(thirdTexasTb.SellerID.Eq(user.SellerID)).
			Where(thirdTexasTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdTexasTb.CreateTime.Between(startTime, endTime)).
			Scan(&texasBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_texas ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_texas 表的流水
	total = total.Add(texasBetAmountSum.TotalBetAmount)

	// 9. 查询 x_user_reduce 表中的 RealReduceLiuShui 字段（减免流水）
	userReduceTb := server.DaoxHashGame().XUserReduce
	userReduceDb := server.DaoxHashGame().XUserReduce.WithContext(context.Background())

	type ReduceLiuShuiSum struct {
		TotalReduceLiuShui decimal.Decimal
	}
	var reduceLiuShuiSum ReduceLiuShuiSum

	// 查询用户在指定时间范围内的减免流水总和
	err = userReduceDb.Select(userReduceTb.RealReduceLiuShui.Sum().As("TotalReduceLiuShui")).
		Where(userReduceTb.UserID.Eq(user.UserID)).
		Where(userReduceTb.CreateTime.Between(startTime, endTime)).
		Scan(&reduceLiuShuiSum)

	if err != nil {
		logs.Error("CalculateUserTotalFlow query x_user_reduce RealReduceLiuShui err", err)
		// 查询出错不影响其他流水计算，继续处理
	} else {
		// 累加减免流水
		total = total.Add(reduceLiuShuiSum.TotalReduceLiuShui)
	}

	// 记录日志，方便调试
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		logs.Info("用户活动期间总流水(已排除超出投注限制的流水) : 用户ID=%v, 最小投注限制=%v, 最大投注限制=%v, x_order=%v, x_third_dianzhi=%v, x_third_live=%v, x_third_lottery=%v, x_third_qipai=%v, x_third_quwei=%v, x_third_sport=%v, x_third_texas=%v, x_user_reduce=%v, total=%v",
			user.UserID,
			minBetAmount,
			maxBetAmount,
			orderAmountSum.TotalAmount,
			dianzhiBetAmountSum.TotalBetAmount,
			liveBetAmountSum.TotalBetAmount,
			lotteryBetAmountSum.TotalBetAmount,
			qipaiBetAmountSum.TotalBetAmount,
			quweiBetAmountSum.TotalBetAmount,
			sportBetAmountSum.TotalBetAmount,
			texasBetAmountSum.TotalBetAmount,
			reduceLiuShuiSum.TotalReduceLiuShui,
			total)
	} else {
		logs.Info("用户活动期间总流水 : 用户ID=%v, x_order=%v, x_third_dianzhi=%v, x_third_live=%v, x_third_lottery=%v, x_third_qipai=%v, x_third_quwei=%v, x_third_sport=%v, x_third_texas=%v, x_user_reduce=%v, total=%v",
			user.UserID,
			orderAmountSum.TotalAmount,
			dianzhiBetAmountSum.TotalBetAmount,
			liveBetAmountSum.TotalBetAmount,
			lotteryBetAmountSum.TotalBetAmount,
			qipaiBetAmountSum.TotalBetAmount,
			quweiBetAmountSum.TotalBetAmount,
			sportBetAmountSum.TotalBetAmount,
			texasBetAmountSum.TotalBetAmount,
			reduceLiuShuiSum.TotalReduceLiuShui,
			total)
	}

	return total, nil
}

// CheckActiveWithdrawable 检查用户是否满足活动流水要求，以确定是否可以提现
// 参数:
//   - user: 用户
//   - activeId: 活动ID
//   - amount: 提现金额
//
// 返回:
//   - bool: 是否可以提现 (true: 可以, false: 不可以)
//   - string: 错误信息 (如果不可以提现)
func CheckActiveWithdrawable(userId int32, activeId int32, amount float64) (bool, string) {
	logs.Info("CheckActiveWithdrawable 开始检查: 用户ID=%v, 活动ID=%v, 提现金额=%v", userId, activeId, amount)
	// 获取用户信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	user, err := userDb.Where(userTb.UserID.Eq(userId)).First()
	if err != nil {
		logs.Error("CheckActiveWithdrawable 步骤4错误: 查询用户信息错误: %v", err)
		return false, "查询用户信息错误" // 查询出错，阻止提现
	}
	// 1. 获取活动配置
	activeTb := server.DaoxHashGame().XActiveDefine
	activeDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	activeDefine, err := activeDb.Where(activeTb.ActiveID.Eq(activeId)).
		Where(activeTb.SellerID.Eq(user.SellerID)).
		Where(activeTb.ChannelID.Eq(user.ChannelID)).
		Where(activeTb.State.Eq(1)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 活动不存在，不阻止提现
			logs.Info("CheckActiveWithdrawable 步骤1通过: 活动ID=%v 不存在，不阻止提现", activeId)
			return true, "success"
		}
		logs.Error("CheckActiveWithdrawable 步骤1错误: 获取活动配置错误: %v", err)
		return true, "success" // 查询出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤1完成: 找到活动ID=%v, 活动标题=%v", activeId, activeDefine.Title)

	// 2. 查询用户是否参与了该活动，并且不是被拒绝的记录

	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	userActivity, err := activeRewardAuditDb.
		Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
		Where(activeRewardAuditTb.ActiveID.Eq(activeId)).
		Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
		Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
		Where(activeRewardAuditTb.AuditState.Neq(utils.ActiveAwardAuditStateRefuse)). // 排除被拒绝的记录
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户未参与活动或者所有记录都被拒绝，直接返回通过
			logs.Info("CheckActiveWithdrawable 步骤2通过: 用户ID=%v 未参与活动ID=%v 或所有记录都被拒绝，不阻止提现", userId, activeId)
			return true, "success"
		}
		logs.Error("CheckActiveWithdrawable 步骤2错误: 查询用户活动记录错误: %v", err)
		return true, "success" // 查询出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤2完成: 用户ID=%v 参与了活动ID=%v, 活动记录ID=%v, 审核状态=%v", userId, activeId, userActivity.ID, userActivity.AuditState)

	// 3. 获取活动流水要求
	requiredLiuShui := userActivity.MinLiuShui
	logs.Info("CheckActiveWithdrawable 步骤3: 活动流水要求=%v", requiredLiuShui)

	// 如果没有要求流水，直接通过
	if requiredLiuShui <= 0 {
		logs.Info("CheckActiveWithdrawable 步骤3通过: 活动没有流水要求(requiredLiuShui=%v)，不阻止提现", requiredLiuShui)
		return true, "success"
	}

	logs.Info("CheckActiveWithdrawable 步骤4完成: 用户ID=%v, 注册时间=%v, 当前总流水=%v, 提现流水要求=%v",
		userId, user.RegisterTime, user.TotalLiuSui, user.WithdrawLiuSui)

	// 5. 解析活动配置，获取投注限制参数
	var minBetAmount, maxBetAmount decimal.Decimal
	var isExcludeBetLimit bool

	// 根据活动ID判断活动类型，解析不同的配置
	if activeId == utils.FirstDepositGift || activeId == utils.MultipleDepositGift { // 首充复充活动
		var baseData model.FirstDepositGiftBaseConfig
		err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
		if err != nil {
			logs.Error("CheckActiveWithdrawable 步骤5错误: 解析活动配置错误: %v", err)
		} else {
			minBetAmount = baseData.MinBetAmount
			maxBetAmount = baseData.MaxBetAmount
			isExcludeBetLimit = !minBetAmount.IsZero() || !maxBetAmount.IsZero()
			logs.Info("CheckActiveWithdrawable 步骤5完成: 活动ID=%v, 最小投注限制=%v, 最大投注限制=%v",
				activeId, minBetAmount, maxBetAmount)
		}
	}

	// 6. 计算用户在活动期间的总流水
	var startTime, endTime time.Time

	// 如果活动有明确的开始和结束时间，使用活动时间范围
	if activeDefine.EffectStartTime > 0 {
		startTime = time.UnixMilli(activeDefine.EffectStartTime)
	} else {
		// 如果活动没有明确的开始时间，使用用户注册时间
		startTime = user.RegisterTime
	}

	if activeDefine.EffectEndTime > 0 {
		endTime = time.UnixMilli(activeDefine.EffectEndTime)
		// 如果当前时间早于活动结束时间，使用当前时间
		if time.Now().Before(endTime) {
			endTime = time.Now()
		}
	} else {
		// 如果活动没有明确的结束时间，使用当前时间
		endTime = time.Now()
	}

	logs.Info("CheckActiveWithdrawable 步骤6: 计算流水时间范围: 开始时间=%v, 结束时间=%v", startTime, endTime)

	// 使用封装的函数计算用户总流水
	total, err := CalculateUserTotalFlow(user, startTime, endTime, minBetAmount, maxBetAmount, isExcludeBetLimit)
	if err != nil {
		logs.Error("CheckActiveWithdrawable 步骤6错误: 计算用户总流水错误: %v", err)
		return true, "success" // 计算出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤6完成: 计算得到用户总流水=%v", total)

	// 7. 检查用户总流水是否达到要求
	requiredLiushuiDecimal := decimal.NewFromFloat(requiredLiuShui)
	isEnough := total.GreaterThanOrEqual(requiredLiushuiDecimal)

	// 记录日志，方便调试
	logs.Info("CheckActiveWithdrawable 步骤7: 用户提现条件检查: 用户ID=%v, 活动ID=%v, 当前总流水=%s, 需要完成的总流水=%v, 是否满足条件=%v",
		userId, activeId, total.StringFixed(2), requiredLiuShui, isEnough)

	if !isEnough {
		// 计算差额并保留小数点后2位
		diff := requiredLiushuiDecimal.Sub(total)
		// 使用Round(2)进行四舍五入，然后使用StringFixed(2)格式化为2位小数
		roundedDiff := diff.Round(2)
		logs.Info("CheckActiveWithdrawable 步骤7结果: 流水不足! 需要流水=%s, 当前流水=%s, 差额=%s, 四舍五入后=%s",
			requiredLiushuiDecimal.StringFixed(2), total.StringFixed(2), diff.StringFixed(2), roundedDiff.StringFixed(2))
		return false, "流水不足，还需要" + roundedDiff.StringFixed(2)
	}

	logs.Info("CheckActiveWithdrawable 步骤7结果: 流水充足! 用户ID=%v, 活动ID=%v 通过检查", userId, activeId)
	return true, "success"
}
