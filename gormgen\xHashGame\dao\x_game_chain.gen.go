// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXGameChain(db *gorm.DB, opts ...gen.DOOption) xGameChain {
	_xGameChain := xGameChain{}

	_xGameChain.xGameChainDo.UseDB(db, opts...)
	_xGameChain.xGameChainDo.UseModel(&model.XGameChain{})

	tableName := _xGameChain.xGameChainDo.TableName()
	_xGameChain.ALL = field.NewAsterisk(tableName)
	_xGameChain.ChainType = field.NewInt32(tableName, "ChainType")
	_xGameChain.Chain = field.NewString(tableName, "Chain")
	_xGameChain.State = field.NewInt32(tableName, "State")
	_xGameChain.CreateTime = field.NewTime(tableName, "CreateTime")
	_xGameChain.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xGameChain.TranState = field.NewInt32(tableName, "TranState")

	_xGameChain.fillFieldMap()

	return _xGameChain
}

type xGameChain struct {
	xGameChainDo xGameChainDo

	ALL        field.Asterisk
	ChainType  field.Int32
	Chain      field.String // 名称
	State      field.Int32  // 状态 1开启 2关闭
	CreateTime field.Time
	UpdateTime field.Time
	TranState  field.Int32

	fieldMap map[string]field.Expr
}

func (x xGameChain) Table(newTableName string) *xGameChain {
	x.xGameChainDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xGameChain) As(alias string) *xGameChain {
	x.xGameChainDo.DO = *(x.xGameChainDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xGameChain) updateTableName(table string) *xGameChain {
	x.ALL = field.NewAsterisk(table)
	x.ChainType = field.NewInt32(table, "ChainType")
	x.Chain = field.NewString(table, "Chain")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.TranState = field.NewInt32(table, "TranState")

	x.fillFieldMap()

	return x
}

func (x *xGameChain) WithContext(ctx context.Context) *xGameChainDo {
	return x.xGameChainDo.WithContext(ctx)
}

func (x xGameChain) TableName() string { return x.xGameChainDo.TableName() }

func (x xGameChain) Alias() string { return x.xGameChainDo.Alias() }

func (x xGameChain) Columns(cols ...field.Expr) gen.Columns { return x.xGameChainDo.Columns(cols...) }

func (x *xGameChain) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xGameChain) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["ChainType"] = x.ChainType
	x.fieldMap["Chain"] = x.Chain
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["TranState"] = x.TranState
}

func (x xGameChain) clone(db *gorm.DB) xGameChain {
	x.xGameChainDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xGameChain) replaceDB(db *gorm.DB) xGameChain {
	x.xGameChainDo.ReplaceDB(db)
	return x
}

type xGameChainDo struct{ gen.DO }

func (x xGameChainDo) Debug() *xGameChainDo {
	return x.withDO(x.DO.Debug())
}

func (x xGameChainDo) WithContext(ctx context.Context) *xGameChainDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xGameChainDo) ReadDB() *xGameChainDo {
	return x.Clauses(dbresolver.Read)
}

func (x xGameChainDo) WriteDB() *xGameChainDo {
	return x.Clauses(dbresolver.Write)
}

func (x xGameChainDo) Session(config *gorm.Session) *xGameChainDo {
	return x.withDO(x.DO.Session(config))
}

func (x xGameChainDo) Clauses(conds ...clause.Expression) *xGameChainDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xGameChainDo) Returning(value interface{}, columns ...string) *xGameChainDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xGameChainDo) Not(conds ...gen.Condition) *xGameChainDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xGameChainDo) Or(conds ...gen.Condition) *xGameChainDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xGameChainDo) Select(conds ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xGameChainDo) Where(conds ...gen.Condition) *xGameChainDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xGameChainDo) Order(conds ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xGameChainDo) Distinct(cols ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xGameChainDo) Omit(cols ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xGameChainDo) Join(table schema.Tabler, on ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xGameChainDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xGameChainDo) RightJoin(table schema.Tabler, on ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xGameChainDo) Group(cols ...field.Expr) *xGameChainDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xGameChainDo) Having(conds ...gen.Condition) *xGameChainDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xGameChainDo) Limit(limit int) *xGameChainDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xGameChainDo) Offset(offset int) *xGameChainDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xGameChainDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xGameChainDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xGameChainDo) Unscoped() *xGameChainDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xGameChainDo) Create(values ...*model.XGameChain) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xGameChainDo) CreateInBatches(values []*model.XGameChain, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xGameChainDo) Save(values ...*model.XGameChain) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xGameChainDo) First() (*model.XGameChain, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameChain), nil
	}
}

func (x xGameChainDo) Take() (*model.XGameChain, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameChain), nil
	}
}

func (x xGameChainDo) Last() (*model.XGameChain, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameChain), nil
	}
}

func (x xGameChainDo) Find() ([]*model.XGameChain, error) {
	result, err := x.DO.Find()
	return result.([]*model.XGameChain), err
}

func (x xGameChainDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XGameChain, err error) {
	buf := make([]*model.XGameChain, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xGameChainDo) FindInBatches(result *[]*model.XGameChain, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xGameChainDo) Attrs(attrs ...field.AssignExpr) *xGameChainDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xGameChainDo) Assign(attrs ...field.AssignExpr) *xGameChainDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xGameChainDo) Joins(fields ...field.RelationField) *xGameChainDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xGameChainDo) Preload(fields ...field.RelationField) *xGameChainDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xGameChainDo) FirstOrInit() (*model.XGameChain, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameChain), nil
	}
}

func (x xGameChainDo) FirstOrCreate() (*model.XGameChain, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameChain), nil
	}
}

func (x xGameChainDo) FindByPage(offset int, limit int) (result []*model.XGameChain, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xGameChainDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xGameChainDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xGameChainDo) Delete(models ...*model.XGameChain) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xGameChainDo) withDO(do gen.Dao) *xGameChainDo {
	x.DO = *do.(*gen.DO)
	return x
}
