// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveDefine(db *gorm.DB, opts ...gen.DOOption) xActiveDefine {
	_xActiveDefine := xActiveDefine{}

	_xActiveDefine.xActiveDefineDo.UseDB(db, opts...)
	_xActiveDefine.xActiveDefineDo.UseModel(&model.XActiveDefine{})

	tableName := _xActiveDefine.xActiveDefineDo.TableName()
	_xActiveDefine.ALL = field.NewAsterisk(tableName)
	_xActiveDefine.ID = field.NewInt32(tableName, "Id")
	_xActiveDefine.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveDefine.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xActiveDefine.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveDefine.Memo = field.NewString(tableName, "Memo")
	_xActiveDefine.AuditType = field.NewInt32(tableName, "AuditType")
	_xActiveDefine.State = field.NewInt32(tableName, "State")
	_xActiveDefine.Sort = field.NewInt32(tableName, "Sort")
	_xActiveDefine.EffectStartTime = field.NewInt64(tableName, "EffectStartTime")
	_xActiveDefine.EffectEndTime = field.NewInt64(tableName, "EffectEndTime")
	_xActiveDefine.Title = field.NewString(tableName, "Title")
	_xActiveDefine.TitleImg = field.NewString(tableName, "TitleImg")
	_xActiveDefine.TitleImgCn = field.NewString(tableName, "TitleImgCn")
	_xActiveDefine.TitleImgEn = field.NewString(tableName, "TitleImgEn")
	_xActiveDefine.TitleImgLang = field.NewString(tableName, "TitleImgLang")
	_xActiveDefine.TopImg = field.NewString(tableName, "TopImg")
	_xActiveDefine.TopImgEn = field.NewString(tableName, "TopImgEn")
	_xActiveDefine.TopImgLang = field.NewString(tableName, "TopImgLang")
	_xActiveDefine.IsTop = field.NewInt32(tableName, "IsTop")
	_xActiveDefine.TopSort = field.NewInt32(tableName, "TopSort")
	_xActiveDefine.GameType = field.NewString(tableName, "GameType")
	_xActiveDefine.MinLiuShui = field.NewFloat64(tableName, "MinLiuShui")
	_xActiveDefine.ExtReward = field.NewFloat64(tableName, "ExtReward")
	_xActiveDefine.MinDeposit = field.NewFloat64(tableName, "MinDeposit")
	_xActiveDefine.MaxReward = field.NewFloat64(tableName, "MaxReward")
	_xActiveDefine.ValidRecharge = field.NewFloat64(tableName, "ValidRecharge")
	_xActiveDefine.ValidLiuShui = field.NewFloat64(tableName, "ValidLiuShui")
	_xActiveDefine.TrxPrice = field.NewFloat64(tableName, "TrxPrice")
	_xActiveDefine.Config = field.NewString(tableName, "Config")
	_xActiveDefine.BaseConfig = field.NewString(tableName, "BaseConfig")
	_xActiveDefine.DetailImgLang = field.NewString(tableName, "DetailImgLang")
	_xActiveDefine.IsUseDetail = field.NewInt32(tableName, "IsUseDetail")
	_xActiveDefine.Cats = field.NewString(tableName, "Cats")
	_xActiveDefine.TitleLang = field.NewString(tableName, "TitleLang")
	_xActiveDefine.PcDetailImgLang = field.NewString(tableName, "PcDetailImgLang")
	_xActiveDefine.CustomerURL = field.NewString(tableName, "CustomerUrl")

	_xActiveDefine.fillFieldMap()

	return _xActiveDefine
}

// xActiveDefine 活动列表
type xActiveDefine struct {
	xActiveDefineDo xActiveDefineDo

	ALL             field.Asterisk
	ID              field.Int32   // 活动id
	SellerID        field.Int32   // 运营商
	ChannelID       field.Int32   // 渠道
	ActiveID        field.Int32   // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	Memo            field.String  // 活动说明
	AuditType       field.Int32   // 审核方式 1人工审核 2自动审核
	State           field.Int32   // 状态
	Sort            field.Int32   // 排序
	EffectStartTime field.Int64   // 活动开始时间
	EffectEndTime   field.Int64   // 活动截止时间
	Title           field.String  // 活动名称
	TitleImg        field.String  // 图片
	TitleImgCn      field.String  // 图片中文
	TitleImgEn      field.String  // 图片英文
	TitleImgLang    field.String  // 图片其他语言
	TopImg          field.String  // 置顶图片
	TopImgEn        field.String  // 置顶图片英文
	TopImgLang      field.String  // 置顶图片其他语言
	IsTop           field.Int32   // 是否置顶 1是 2否
	TopSort         field.Int32   // 置顶顺序
	GameType        field.String  // 游戏分类
	MinLiuShui      field.Float64 // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	ExtReward       field.Float64 // 额外奖金 闯关活动才有的配置
	MinDeposit      field.Float64 // 最低存款 救援金 和 首日充次日送 活动才有的配置
	MaxReward       field.Float64 // 最大返还金额 救援金活动才有的配置 首日充次日送最大彩金上限
	ValidRecharge   field.Float64 // 有效会员最低充值
	ValidLiuShui    field.Float64 // 有效会员最低流水
	TrxPrice        field.Float64 // Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          field.String  // 活动配置
	BaseConfig      field.String  // 基础配置
	DetailImgLang   field.String  // 详情图片其他语言
	IsUseDetail     field.Int32   // 是否使用详情图片
	Cats            field.String  // 分类
	TitleLang       field.String  // 多语言标题
	PcDetailImgLang field.String  // pc详情图多语言版本
	CustomerURL     field.String  // 客服url

	fieldMap map[string]field.Expr
}

func (x xActiveDefine) Table(newTableName string) *xActiveDefine {
	x.xActiveDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveDefine) As(alias string) *xActiveDefine {
	x.xActiveDefineDo.DO = *(x.xActiveDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveDefine) updateTableName(table string) *xActiveDefine {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.Memo = field.NewString(table, "Memo")
	x.AuditType = field.NewInt32(table, "AuditType")
	x.State = field.NewInt32(table, "State")
	x.Sort = field.NewInt32(table, "Sort")
	x.EffectStartTime = field.NewInt64(table, "EffectStartTime")
	x.EffectEndTime = field.NewInt64(table, "EffectEndTime")
	x.Title = field.NewString(table, "Title")
	x.TitleImg = field.NewString(table, "TitleImg")
	x.TitleImgCn = field.NewString(table, "TitleImgCn")
	x.TitleImgEn = field.NewString(table, "TitleImgEn")
	x.TitleImgLang = field.NewString(table, "TitleImgLang")
	x.TopImg = field.NewString(table, "TopImg")
	x.TopImgEn = field.NewString(table, "TopImgEn")
	x.TopImgLang = field.NewString(table, "TopImgLang")
	x.IsTop = field.NewInt32(table, "IsTop")
	x.TopSort = field.NewInt32(table, "TopSort")
	x.GameType = field.NewString(table, "GameType")
	x.MinLiuShui = field.NewFloat64(table, "MinLiuShui")
	x.ExtReward = field.NewFloat64(table, "ExtReward")
	x.MinDeposit = field.NewFloat64(table, "MinDeposit")
	x.MaxReward = field.NewFloat64(table, "MaxReward")
	x.ValidRecharge = field.NewFloat64(table, "ValidRecharge")
	x.ValidLiuShui = field.NewFloat64(table, "ValidLiuShui")
	x.TrxPrice = field.NewFloat64(table, "TrxPrice")
	x.Config = field.NewString(table, "Config")
	x.BaseConfig = field.NewString(table, "BaseConfig")
	x.DetailImgLang = field.NewString(table, "DetailImgLang")
	x.IsUseDetail = field.NewInt32(table, "IsUseDetail")
	x.Cats = field.NewString(table, "Cats")
	x.TitleLang = field.NewString(table, "TitleLang")
	x.PcDetailImgLang = field.NewString(table, "PcDetailImgLang")
	x.CustomerURL = field.NewString(table, "CustomerUrl")

	x.fillFieldMap()

	return x
}

func (x *xActiveDefine) WithContext(ctx context.Context) *xActiveDefineDo {
	return x.xActiveDefineDo.WithContext(ctx)
}

func (x xActiveDefine) TableName() string { return x.xActiveDefineDo.TableName() }

func (x xActiveDefine) Alias() string { return x.xActiveDefineDo.Alias() }

func (x xActiveDefine) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveDefineDo.Columns(cols...)
}

func (x *xActiveDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["AuditType"] = x.AuditType
	x.fieldMap["State"] = x.State
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["EffectStartTime"] = x.EffectStartTime
	x.fieldMap["EffectEndTime"] = x.EffectEndTime
	x.fieldMap["Title"] = x.Title
	x.fieldMap["TitleImg"] = x.TitleImg
	x.fieldMap["TitleImgCn"] = x.TitleImgCn
	x.fieldMap["TitleImgEn"] = x.TitleImgEn
	x.fieldMap["TitleImgLang"] = x.TitleImgLang
	x.fieldMap["TopImg"] = x.TopImg
	x.fieldMap["TopImgEn"] = x.TopImgEn
	x.fieldMap["TopImgLang"] = x.TopImgLang
	x.fieldMap["IsTop"] = x.IsTop
	x.fieldMap["TopSort"] = x.TopSort
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["MinLiuShui"] = x.MinLiuShui
	x.fieldMap["ExtReward"] = x.ExtReward
	x.fieldMap["MinDeposit"] = x.MinDeposit
	x.fieldMap["MaxReward"] = x.MaxReward
	x.fieldMap["ValidRecharge"] = x.ValidRecharge
	x.fieldMap["ValidLiuShui"] = x.ValidLiuShui
	x.fieldMap["TrxPrice"] = x.TrxPrice
	x.fieldMap["Config"] = x.Config
	x.fieldMap["BaseConfig"] = x.BaseConfig
	x.fieldMap["DetailImgLang"] = x.DetailImgLang
	x.fieldMap["IsUseDetail"] = x.IsUseDetail
	x.fieldMap["Cats"] = x.Cats
	x.fieldMap["TitleLang"] = x.TitleLang
	x.fieldMap["PcDetailImgLang"] = x.PcDetailImgLang
	x.fieldMap["CustomerUrl"] = x.CustomerURL
}

func (x xActiveDefine) clone(db *gorm.DB) xActiveDefine {
	x.xActiveDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveDefine) replaceDB(db *gorm.DB) xActiveDefine {
	x.xActiveDefineDo.ReplaceDB(db)
	return x
}

type xActiveDefineDo struct{ gen.DO }

func (x xActiveDefineDo) Debug() *xActiveDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveDefineDo) WithContext(ctx context.Context) *xActiveDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveDefineDo) ReadDB() *xActiveDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveDefineDo) WriteDB() *xActiveDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveDefineDo) Session(config *gorm.Session) *xActiveDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveDefineDo) Clauses(conds ...clause.Expression) *xActiveDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveDefineDo) Returning(value interface{}, columns ...string) *xActiveDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveDefineDo) Not(conds ...gen.Condition) *xActiveDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveDefineDo) Or(conds ...gen.Condition) *xActiveDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveDefineDo) Select(conds ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveDefineDo) Where(conds ...gen.Condition) *xActiveDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveDefineDo) Order(conds ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveDefineDo) Distinct(cols ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveDefineDo) Omit(cols ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveDefineDo) Join(table schema.Tabler, on ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveDefineDo) Group(cols ...field.Expr) *xActiveDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveDefineDo) Having(conds ...gen.Condition) *xActiveDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveDefineDo) Limit(limit int) *xActiveDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveDefineDo) Offset(offset int) *xActiveDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveDefineDo) Unscoped() *xActiveDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveDefineDo) Create(values ...*model.XActiveDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveDefineDo) CreateInBatches(values []*model.XActiveDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveDefineDo) Save(values ...*model.XActiveDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveDefineDo) First() (*model.XActiveDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefine), nil
	}
}

func (x xActiveDefineDo) Take() (*model.XActiveDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefine), nil
	}
}

func (x xActiveDefineDo) Last() (*model.XActiveDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefine), nil
	}
}

func (x xActiveDefineDo) Find() ([]*model.XActiveDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveDefine), err
}

func (x xActiveDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveDefine, err error) {
	buf := make([]*model.XActiveDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveDefineDo) FindInBatches(result *[]*model.XActiveDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveDefineDo) Attrs(attrs ...field.AssignExpr) *xActiveDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveDefineDo) Assign(attrs ...field.AssignExpr) *xActiveDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveDefineDo) Joins(fields ...field.RelationField) *xActiveDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveDefineDo) Preload(fields ...field.RelationField) *xActiveDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveDefineDo) FirstOrInit() (*model.XActiveDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefine), nil
	}
}

func (x xActiveDefineDo) FirstOrCreate() (*model.XActiveDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveDefine), nil
	}
}

func (x xActiveDefineDo) FindByPage(offset int, limit int) (result []*model.XActiveDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveDefineDo) Delete(models ...*model.XActiveDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveDefineDo) withDO(do gen.Dao) *xActiveDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
