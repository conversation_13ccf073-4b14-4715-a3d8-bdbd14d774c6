// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserActive = "x_user_active"

// XUserActive 玩家活动数据
type XUserActive struct {
	UserID         int32     `gorm:"column:UserId;primaryKey;comment:玩家ID" json:"UserId"`                               // 玩家ID
	ActiveDefineID int32     `gorm:"column:ActiveDefineId;primaryKey;comment:x_active_define.Id" json:"ActiveDefineId"` // x_active_define.Id
	JoinTime       time.Time `gorm:"column:JoinTime;comment:玩家主动参加活动的时间" json:"JoinTime"`                               // 玩家主动参加活动的时间
	CompletionData string    `gorm:"column:CompletionData;comment:活动达成数据" json:"CompletionData"`                        // 活动达成数据
}

// TableName XUserActive's table name
func (*XUserActive) TableName() string {
	return TableNameXUserActive
}
