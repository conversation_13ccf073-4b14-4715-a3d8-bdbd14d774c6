package customer

import (
	"context"
	"fmt"
	"time"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// EventParams 事件参数结构体
type EventParams struct {
	AccountID int32     `json:"account_id"`
	Date      time.Time `json:"date"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	IP        string    `json:"ip"`
	NickName  string    `json:"nickname"`
}

// BaseController 基础控制器
// 提供与CustomerIO集成的通用功能，用于用户行为追踪和数据分析
type BaseController struct {
	cio *CustomerIOController
}

// NewBaseController 创建BaseController实例
func NewBaseController() *BaseController {
	cio := NewCustomerIOController()
	if cio == nil {
		logs.Error("BaseController 初始化失败: CustomerIOController 创建失败")
		return nil
	}
	return &BaseController{
		cio: cio,
	}
}

// buildBaseParams 构建基础事件参数
func buildBaseParams(u *model.XUser) EventParams {
	// 检查用户是否为空
	if u == nil {
		return EventParams{
			Date: time.Now().UTC(),
		}
	}

	return EventParams{
		AccountID: u.UserID,
		Date:      time.Now().UTC(),
		Email:     u.Email,
		Phone:     u.PhoneNum,
		IP:        u.LoginIP,
		NickName:  u.NickName,
	}
}

// pushEvent 推送事件到CustomerIO（内部通用方法）
func (c *BaseController) pushEvent(customerID string, eventName string, params map[string]interface{}) <-chan error {
	if c.cio == nil {
		logs.Error("pushEvent 失败: CustomerIO 客户端未初始化")
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("CustomerIO 客户端未初始化")
		close(errChan)
		return errChan
	}

	// 添加时间戳
	params["date"] = time.Now().UTC().Format(time.RFC3339Nano)

	// 构建用户属性
	attributes := map[string]interface{}{
		"account_id": customerID,
	}

	// 异步执行推送
	resultChan := c.cio.ExecuteIdentifyAndTrack(customerID, attributes, eventName, params)

	// 在后台处理错误
	go func() {
		if err := <-resultChan; err != nil {
			logs.Error("CustomerIO推送失败 - 事件: %s, 用户: %s, 错误: %v", eventName, customerID, err)
		}
	}()

	return resultChan
}

// PushLoginEvent 推送登录事件
// @param success 登录是否成功
// @param u 用户信息
func (c *BaseController) PushLoginEvent(success bool, u *model.XUser) <-chan error {
	// 检查控制器和用户是否为空
	if c == nil {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("BaseController 未初始化")
		close(errChan)
		return errChan
	}

	if u == nil {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("用户信息为空")
		close(errChan)
		return errChan
	}

	base := buildBaseParams(u)
	params := map[string]interface{}{
		"account_id": base.AccountID,
		"email":      base.Email,
		"ip":         base.IP,
		"phone":      base.Phone,
		"type":       map[bool]string{true: "success_login", false: "failed_login"}[success],
	}

	return c.pushEvent(fmt.Sprint(u.UserID), "login", params)
}

// PushBetEvent 推送投注事件
// @param u 用户信息
// @param gameName 游戏名称
// @param playType 游戏玩法
// @param betAmount 投注金额
// @param currency 投注币种
func (c *BaseController) PushBetEvent(userId int, gameName, playType string, betAmount float64, currency string) <-chan error {
	// 检查控制器和用户是否为空
	if c == nil {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("BaseController 未初始化")
		close(errChan)
		return errChan
	}

	if userId == 0 {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("用户信息为空")
		close(errChan)
		return errChan
	}
	// 获取用户信息
	user, err := server.DaoxHashGame().XUser.WithContext(context.TODO()).
		Where(server.DaoxHashGame().XUser.UserID.Eq(int32(userId))).
		First()
	if err != nil {
		logs.Error("获取用户信息失败:", err)
	}
	base := buildBaseParams(user)
	params := map[string]interface{}{
		"account_id": base.AccountID,
		"email":      base.Email,
		"phone":      base.Phone,
		"game_name":  gameName,
		"play_type":  playType,
		"bet_amount": betAmount,
		"currency":   currency,
		"nickname":   base.NickName,
		"ip":         base.IP,
	}

	return c.pushEvent(fmt.Sprint(userId), "bet", params)
}

// PushRewardEvent 推送派奖事件
// @param u 用户信息
// @param gameName 游戏名称
// @param playType 游戏玩法
// @param betAmount 投注金额
// @param rewardAmount 派奖金额
// @param currency 币种
func (c *BaseController) PushRewardEvent(userId int, gameName, playType string, betAmount, rewardAmount float64, currency string) <-chan error {
	// 检查控制器和用户是否为空
	if c == nil {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("BaseController 未初始化")
		close(errChan)
		return errChan
	}

	if userId == 0 {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("用户信息为空")
		close(errChan)
		return errChan
	}
	// 获取用户信息
	user, err := server.DaoxHashGame().XUser.WithContext(context.TODO()).
		Where(server.DaoxHashGame().XUser.UserID.Eq(int32(userId))).
		First()
	if err != nil {
		logs.Error("获取用户信息失败:", err)
	}
	base := buildBaseParams(user)
	params := map[string]interface{}{
		"account_id":    base.AccountID,
		"email":         base.Email,
		"phone":         base.Phone,
		"game_name":     gameName,
		"play_type":     playType,
		"bet_amount":    betAmount,
		"reward_amount": rewardAmount,
		"currency":      currency,
		"nickname":      base.NickName,
		"ip":            base.IP,
	}

	return c.pushEvent(fmt.Sprint(userId), "reward", params)
}

// PushCustomEvent 推送自定义事件
// @param customerID 用户ID
// @param eventName 事件名称
// @param params 事件参数
func (c *BaseController) PushCustomEvent(customerID string, eventName string, params map[string]interface{}) <-chan error {
	// 检查控制器是否为空
	if c == nil {
		errChan := make(chan error, 1)
		errChan <- fmt.Errorf("BaseController 未初始化")
		close(errChan)
		return errChan
	}

	return c.pushEvent(customerID, eventName, params)
}
