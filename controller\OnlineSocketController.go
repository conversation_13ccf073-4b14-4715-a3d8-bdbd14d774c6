package controller

import (
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/gorilla/websocket"
	"github.com/robfig/cron/v3"
	"github.com/spf13/viper"
	"log"
	"net/http"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type OnlineSocketController struct {
}

var upgrade = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// WebSiteStats 统计访问站点的在线人数
type WebSiteStats struct {
	Online int
}

// PlayStates 用于统计游戏在线人数的结构体
type PlayStates struct {
	Online int
	Brand  []Brand
}

type Brand struct {
	Online int
	Name   string
	Games  []Games
}

type Games struct {
	Online int
	GameId string
}

type OnlineStates struct {
	WebSiteStates WebSiteStats
	PlayStates    PlayStates
}

var (
	clients     = make(map[*websocket.Conn]bool)
	onlineStats = new(OnlineStates)
	broadcast   = make(chan OnlineStates) // 广播在线人数信息
	mutex       sync.Mutex

	// 峰值在线人数
	peakPlayUsers int
	peakWebUsers  int

	// 保存每小时的峰值记录
	//hourlyPeakRecords []map[string]int
)

func (c *OnlineSocketController) Init() {
	go c.handleBroadcast()

	if env := viper.GetString("server.env"); env != "dev" {
		go c.recordOnlineWebUsers()
		go c.recordOnlinePlayUsers()
		go c.recordOnlineBrandUsers()
		go c.recordOnlineGameUsers()
		go c.destroyHistoryData()
	}

	server.Http().GetNoAuth("/api/ws/online", c.handleConnections)
}

func (c *OnlineSocketController) handleConnections(ctx *abugo.AbuHttpContent) {
	ws, err := upgrade.Upgrade(ctx.Gin().Writer, ctx.Gin().Request, nil)
	if err != nil {
		fmt.Println("Upgrade error:", err)
		return
	}
	defer ws.Close()

	// 获取类别参数
	cat := ctx.Query("cat")
	gameId := ctx.Query("gameId")
	brand := ctx.Query("brand")

	// 根据类别将客户端加入相应的组
	mutex.Lock()
	if cat == "" {
		clients[ws] = true
	}

	if cat == "web" || cat == "game" {
		onlineStats.WebSiteStates.Online++
	}

	if cat == "game" {
		onlineStats.PlayStates.Online++
		if brand != "" && gameId != "" {
			brandFound := false
			// 检查是否已经存在该 brand
			for i := range onlineStats.PlayStates.Brand {
				if onlineStats.PlayStates.Brand[i].Name == brand {
					brandFound = true
					onlineStats.PlayStates.Brand[i].Online++
					gameFound := false
					// 检查该 brand 下是否已经存在该 gameId
					for j := range onlineStats.PlayStates.Brand[i].Games {
						if onlineStats.PlayStates.Brand[i].Games[j].GameId == gameId {
							onlineStats.PlayStates.Brand[i].Games[j].Online++
							gameFound = true
							break
						}
					}
					// 如果 gameId 不存在，则添加新游戏
					if !gameFound {
						onlineStats.PlayStates.Brand[i].Games = append(onlineStats.PlayStates.Brand[i].Games, Games{
							GameId: gameId,
							Online: 1,
						})
					}
					break
				}
			}
			// 如果 brand 不存在，则添加新品牌和新游戏
			if !brandFound {
				onlineStats.PlayStates.Brand = append(onlineStats.PlayStates.Brand, Brand{
					Name:   brand,
					Online: 1,
					Games: []Games{
						{
							GameId: gameId,
							Online: 1,
						},
					},
				})
			}
		}
	}

	mutex.Unlock()

	//broadcast <- *c.getOnlineStates()

	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			// 连接断开时，移除客户端
			mutex.Lock()
			if cat == "web" || cat == "game" {
				onlineStats.WebSiteStates.Online--
			}
			if cat == "game" {
				onlineStats.PlayStates.Online--
				if brand != "" && gameId != "" {
					// 查找对应的 brand
					for i := range onlineStats.PlayStates.Brand {
						if onlineStats.PlayStates.Brand[i].Name == brand {
							// 减少品牌的在线人数
							onlineStats.PlayStates.Brand[i].Online--
							// 查找对应的 gameId 并减少该游戏的在线人数
							for j := range onlineStats.PlayStates.Brand[i].Games {
								if onlineStats.PlayStates.Brand[i].Games[j].GameId == gameId {
									onlineStats.PlayStates.Brand[i].Games[j].Online--
									// 如果某个游戏的在线人数变为 0，可以选择删除该游戏（可选逻辑）
									if onlineStats.PlayStates.Brand[i].Games[j].Online == 0 {
										// 删除该游戏
										onlineStats.PlayStates.Brand[i].Games = append(
											onlineStats.PlayStates.Brand[i].Games[:j],
											onlineStats.PlayStates.Brand[i].Games[j+1:]...,
										)
									}
									break
								}
							}
							// 如果某个品牌的在线人数变为 0，也可以选择删除该品牌
							if onlineStats.PlayStates.Brand[i].Online == 0 {
								// 删除该品牌
								onlineStats.PlayStates.Brand = append(
									onlineStats.PlayStates.Brand[:i],
									onlineStats.PlayStates.Brand[i+1:]...,
								)
							}
							break
						}
					}
				}
			}
			mutex.Unlock()

			// 更新并广播在线人数
			//broadcast <- *c.getOnlineStates()
			break
		}
	}
}

// 计算每个类别的在线人数，并更新每小时的峰值
func (c *OnlineSocketController) getOnlineStates() *OnlineStates {
	mutex.Lock()
	defer mutex.Unlock()

	currentPlayUsers := onlineStats.PlayStates.Online
	currentWebUsers := onlineStats.WebSiteStates.Online

	//更新游戏和网站的峰值在线人数
	if currentPlayUsers > peakPlayUsers {
		peakPlayUsers = currentPlayUsers
	}
	if currentWebUsers > peakWebUsers {
		peakWebUsers = currentWebUsers
	}

	return onlineStats
}

//// TODO 通过协程统计每小时峰值在线人数到数据库中
//func (c *OnlineSocketController) trackHourlyPeak() {
//	// 创建 cron 调度器
//	cron := cron.New()
//	// 添加一个每小时执行的任务
//	cron.AddFunc("@hourly", func() {
//		logs.Info("Running hourly peak tracking...")
//		// 锁住，确保数据一致性
//		mutex.Lock()
//
//		// 记录当前小时的峰值
//		hourlyRecord := map[string]int{
//			"game_peak":  peakPlayUsers,
//			"movie_peak": peakWebUsers,
//		}
//		hourlyPeakRecords = append(hourlyPeakRecords, hourlyRecord)
//
//		fmt.Printf("Hourly peak recorded: %v\n", hourlyRecord)
//
//		// 重置峰值
//		peakPlayUsers = 0
//		peakWebUsers = 0
//
//		mutex.Unlock()
//	})
//
//	// 启动 cron 调度器
//	cron.Start()
//
//	// 阻止 cron 提前退出
//	select {}
//}

// 在线人数入库
func (c *OnlineSocketController) recordOnlineWebUsers() {
	//env := viper.GetString("server.env")
	//if env == "dev" {
	//	return
	//}
	cron := cron.New(cron.WithSeconds())
	_, err := cron.AddFunc("*/5 * * * * *", func() {
		currentTime := time.Now()
		//log.Println("站点在线:", onlineStats.WebSiteStates.Online)
		mutex.Lock()
		if onlineStats.WebSiteStates.Online > 0 {
			xOlineWebUser := server.DaoxHashGame().XOnlineWebUser
			err := xOlineWebUser.WithContext(nil).Create(&model.XOnlineWebUser{
				Online:     int32(onlineStats.WebSiteStates.Online),
				CreateTime: currentTime,
			})
			if err != nil {
				log.Println("WebOnline 插入数据失败:", err)
			}
		}

		mutex.Unlock()
	})

	if err != nil {
		log.Println("Cron任务添加失败:", err)
		return
	}

	// 启动 cron 调度器
	cron.Start()

	// 阻止 cron 提前退出
	select {}
}

// 在玩人数入库
func (c *OnlineSocketController) recordOnlinePlayUsers() {
	//env := viper.GetString("server.env")
	//if env == "dev" {
	//	return
	//}
	cron := cron.New(cron.WithSeconds())
	_, err := cron.AddFunc("*/5 * * * * *", func() {
		currentTime := time.Now()
		//log.Println("在玩在线:", onlineStats.PlayStates.Online)
		mutex.Lock()
		if onlineStats.PlayStates.Online > 0 {
			xOnlinePlayUser := server.DaoxHashGame().XOnlinePlayUser
			err := xOnlinePlayUser.WithContext(nil).Create(&model.XOnlinePlayUser{
				Online:     int32(onlineStats.PlayStates.Online),
				CreateTime: currentTime,
			})
			if err != nil {
				log.Println("PlayOnline插入数据失败:", err)
			}
		}

		mutex.Unlock()
	})

	if err != nil {
		log.Println("Cron任务添加失败:", err)
		return
	}

	// 启动 cron 调度器
	cron.Start()

	// 阻止 cron 提前退出
	select {}
}

// 游戏厂商在玩入库
func (c *OnlineSocketController) recordOnlineBrandUsers() {
	//env := viper.GetString("server.env")
	//if env == "dev" {
	//	return
	//}
	cron := cron.New(cron.WithSeconds())
	_, err := cron.AddFunc("*/5 * * * * *", func() {
		currentTime := time.Now()
		mutex.Lock()
		if onlineStats.PlayStates.Brand != nil {
			var data []*model.XOnlineBrandUser
			for i := range onlineStats.PlayStates.Brand {
				//log.Println("厂商:", onlineStats.PlayStates.Brand[i].Name, "在线:", onlineStats.PlayStates.Brand[i].Online)
				data = append(data, &model.XOnlineBrandUser{
					Brand:      onlineStats.PlayStates.Brand[i].Name,
					Online:     int32(onlineStats.PlayStates.Brand[i].Online),
					CreateTime: currentTime,
				})
			}

			xOnlineBrandUser := server.DaoxHashGame().XOnlineBrandUser
			err := xOnlineBrandUser.WithContext(nil).CreateInBatches(data, len(data))
			if err != nil {
				log.Println("BrandOnline插入数据失败:", err)
			}
		}
		mutex.Unlock()
	})

	if err != nil {
		log.Println("Cron任务添加失败:", err)
		return
	}

	// 启动 cron 调度器
	cron.Start()

	// 阻止 cron 提前退出
	select {}
}

// 游戏在玩入库
func (c *OnlineSocketController) recordOnlineGameUsers() {
	//env := viper.GetString("server.env")
	//if env == "dev" {
	//	return
	//}
	cron := cron.New(cron.WithSeconds())
	_, err := cron.AddFunc("*/5 * * * * *", func() {
		currentTime := time.Now()
		mutex.Lock()
		if onlineStats.PlayStates.Brand != nil {
			for i := range onlineStats.PlayStates.Brand {
				if onlineStats.PlayStates.Brand[i].Games != nil {
					var data []*model.XOnlineGameUser
					for j := range onlineStats.PlayStates.Brand[i].Games {
						//log.Println("厂商:", onlineStats.PlayStates.Brand[i].Name, "游戏ID:", onlineStats.PlayStates.Brand[i].Games[j].GameId, "在线:", onlineStats.PlayStates.Brand[i].Games[j].Online)
						data = append(data, &model.XOnlineGameUser{
							Brand:      onlineStats.PlayStates.Brand[i].Name,
							GameID:     onlineStats.PlayStates.Brand[i].Games[j].GameId,
							Online:     int32(onlineStats.PlayStates.Brand[i].Games[j].Online),
							CreateTime: currentTime,
						})
					}
					xOnlineGameUser := server.DaoxHashGame().XOnlineGameUser
					err := xOnlineGameUser.WithContext(nil).CreateInBatches(data, len(data))
					if err != nil {
						log.Println("GameOnline插入数据失败:", err)
					}
				}
			}
		}
		mutex.Unlock()
	})

	if err != nil {
		log.Println("Cron任务添加失败:", err)
		return
	}

	// 启动 cron 调度器
	cron.Start()

	// 阻止 cron 提前退出
	select {}
}

// 定时删除3天前的数据
func (c *OnlineSocketController) destroyHistoryData() {
	cron := cron.New()
	_, err := cron.AddFunc("0 0 * * *", func() {
		threeDaysAgo := carbon.Now().SubDays(3).StartOfDay().StdTime()
		logs.Info("正在删除3天前的在线历史数据")
		go func() {
			xOnlineWebUser := server.DaoxHashGame().XOnlineWebUser
			_, err := xOnlineWebUser.WithContext(nil).Where(xOnlineWebUser.CreateTime.Lt(threeDaysAgo)).Delete()
			if err != nil {
				log.Println(err.Error())
			}
		}()

		go func() {
			xOnlinePlayUser := server.DaoxHashGame().XOnlinePlayUser
			_, err := xOnlinePlayUser.WithContext(nil).Where(xOnlinePlayUser.CreateTime.Lt(threeDaysAgo)).Delete()
			if err != nil {
				log.Println(err.Error())
			}
		}()

		go func() {
			xOnlineBrandUser := server.DaoxHashGame().XOnlineBrandUser
			_, err := xOnlineBrandUser.WithContext(nil).Where(xOnlineBrandUser.CreateTime.Lt(threeDaysAgo)).Delete()
			if err != nil {
				log.Println(err.Error())
			}
		}()

		go func() {
			xOnlineGameUser := server.DaoxHashGame().XOnlineGameUser
			_, err := xOnlineGameUser.WithContext(nil).Where(xOnlineGameUser.CreateTime.Lt(threeDaysAgo)).Delete()
			if err != nil {
				log.Println(err.Error())
			}
		}()
	})
	if err != nil {
		log.Println("Cron任务添加失败:", err)
		return
	}

	// 启动 cron 调度器
	cron.Start()

	// 阻止 cron 提前退出
	select {}
}

// 广播在线人数
func (c *OnlineSocketController) handleBroadcast() {
	cron := cron.New(cron.WithSeconds())
	for {
		_, err := cron.AddFunc("*/3 * * * * *", func() {
			//mutex.Lock()
			for client := range clients {
				// 尝试发送消息
				err := client.WriteJSON(onlineStats)
				if err != nil {
					log.Println("发送消息失败，关闭客户端连接:", err)
					client.Close()
					delete(clients, client)
				}
			}
			//mutex.Unlock()
		})
		if err != nil {
			log.Println("Cron任务添加失败:", err)
			return
		}
		// 启动 cron 调度器
		cron.Start()

		// 阻止 cron 提前退出
		select {}
	}
}
