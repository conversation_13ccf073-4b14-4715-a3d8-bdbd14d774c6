package utils

import (
	"encoding/base64"
	"os"
	"xserver/utils/haxicrypto"
)

/*
用户密码加密流程：
1.客户端使用RSA公钥加密后发送给服务端
2.服务端使用RSA私钥解密，得到密码原文
3.服务端将用户密码原文使用AES重新加密
4.将AES加密后的用户密码存入数据库
*/

// ConvertUserPassword 将RSA加密的用户密码转换成AES加密
func ConvertUserPassword(pwd string) (string, error) {
	if len(pwd) == 0 {
		return "", nil
	}
	prvkey, err := os.ReadFile("./config/rsa_key")
	if err != nil {
		return "", err
	}
	cipher, err := base64.StdEncoding.DecodeString(pwd)
	if err != nil {
		return "", err
	}
	decrypted, err := haxicrypto.RsaDecrypt(prvkey, cipher)
	if err != nil {
		return "", err
	}
	pwd = haxicrypto.AesEncryptCBC(decrypted, nil).Hex()
	return pwd, nil
}

// RsaEncryptUserPassword 将密码原文使用RSA加密
func RsaEncryptUserPassword(pwd string) (string, error) {
	pubkey, err := os.ReadFile("./config/rsa_key.pub")
	if err != nil {
		return "", err
	}
	cipher, err := haxicrypto.RsaEncrypt(pubkey, []byte(pwd))
	if err != nil {
		return "", err
	}
	encrypted := base64.StdEncoding.EncodeToString(cipher)
	return encrypted, nil
}
