// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAmountChangeLog = "x_amount_change_log"

// XAmountChangeLog mapped from table <x_amount_change_log>
type XAmountChangeLog struct {
	ID           int32   `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID       int32   `gorm:"column:UserId;comment:玩家" json:"UserId"`                                            // 玩家
	AmountType   int32   `gorm:"column:AmountType;not null;default:1;comment:余额分类 1=真金，2=Bonus币" json:"AmountType"` // 余额分类 1=真金，2=Bonus币
	BeforeAmount float64 `gorm:"column:BeforeAmount;comment:变化前" json:"BeforeAmount"`                               // 变化前
	Amount       float64 `gorm:"column:Amount;comment:变化值" json:"Amount"`                                           // 变化值
	AfterAmount  float64 `gorm:"column:AfterAmount;comment:变化后" json:"AfterAmount"`                                 // 变化后
	/*
		变化原因
		1充值
		2提款
		3活动送金
		4佣金领取
		5后台增资
		6提款拒绝退款
		7彩票转出
		8彩票转入
		9彩票下注
		10电子转出
		11电子转入
		12棋牌转出
		13棋牌转入
		14小游戏转出
		15小游戏转入
		16彩票返奖
		17vip返水
		18vip升级礼金
		19vip每月礼金
		20能量补给站投注反水新
		21evo下注
		22evo取消投注
		23evo结算
		24evo重新结算
		25充值活动新
		26哈希闯关活动新
		27棋牌闯关活动新
		28电子闯关活动新
		29救援金活动新
		30邀请好友活动新
		31降龙伏虎活动新
	*/
	Reason            int32     `gorm:"column:Reason;comment:变化原因 \n1充值 \n2提款 \n3活动送金 \n4佣金领取 \n5后台增资\n6提款拒绝退款\n7彩票转出\n8彩票转入\n9彩票下注\n10电子转出\n11电子转入\n12棋牌转出\n13棋牌转入\n14小游戏转出\n15小游戏转入\n16彩票返奖\n17vip返水\n18vip升级礼金\n19vip每月礼金\n20能量补给站投注反水新\n21evo下注\n22evo取消投注\n23evo结算\n24evo重新结算\n25充值活动新\n26哈希闯关活动新\n27棋牌闯关活动新\n28电子闯关活动新\n29救援金活动新\n30邀请好友活动新\n31降龙伏虎活动新" json:"Reason"`
	CreateTime        time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:变化时间" json:"CreateTime"` // 变化时间
	Memo              string    `gorm:"column:Memo" json:"Memo"`
	SellerID          int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID         int32     `gorm:"column:ChannelId" json:"ChannelId"`
	BeforeBonusAmount float64   `gorm:"column:BeforeBonusAmount;comment:bonus变化前" json:"BeforeBonusAmount"` // bonus变化前
	BonusAmount       float64   `gorm:"column:BonusAmount;comment:bonus变化值" json:"BonusAmount"`             // bonus变化值
	AfterBonusAmount  float64   `gorm:"column:AfterBonusAmount;comment:bonus变化后" json:"AfterBonusAmount"`   // bonus变化后
}

// TableName XAmountChangeLog's table name
func (*XAmountChangeLog) TableName() string {
	return TableNameXAmountChangeLog
}
