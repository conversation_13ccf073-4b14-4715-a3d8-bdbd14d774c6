package tronapi

import (
	"encoding/json"
	"fmt"
	"io"
)

type Transaction struct{}

func (c *Transaction) Info(hash string) (rsp *TransactionInfoRsp, err error) {
	url := baseUrl + fmt.Sprintf("/transaction-info?hash=%s", hash)
	resp, err := Client().HttpGet(url)
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		return nil, err
	}
	defer resp.Response().Body.Close()
	rsp = new(TransactionInfoRsp)
	err = json.Unmarshal(body, rsp)
	return rsp, err
}

func (c *Transaction) Transfers(start, limit int, address string) (rsp *TransfersRsp, err error) {
	url := baseUrl + fmt.Sprintf("/token_trc20/transfers?start=%d&limit=%d&relatedAddress=%s", start, limit, address)
	resp, err := Client().HttpGet(url)
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		return nil, err
	}
	defer resp.Response().Body.Close()
	rsp = new(TransfersRsp)
	err = json.Unmarshal(body, rsp)
	return rsp, err
}
