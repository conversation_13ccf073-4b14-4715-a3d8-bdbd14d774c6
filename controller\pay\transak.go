package paycontroller

import (
	"errors"
	"fmt"
	"io"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/dgrijalva/jwt-go"
	"github.com/goccy/go-json"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

type TransakRechargeRequest struct {
	Network             string `json:"network" validate:"required"`
	CryptoCurrencyCode  string `json:"cryptoCurrencyCode" validate:"required"`
	FiatCurrency        string `json:"fiatCurrency" validate:"required"`
	FiatAmount          string `json:"fiatAmount"`
	DefaultCryptoAmount string `json:"defaultCryptoAmount"`
}

func (c *PayController) InitTransak() {
	server.Http().PostNoAuth("/api/transak/webhook", c.webhook)
	server.Http().Post("/api/transak/create_order", c.createTransakOrder)
	server.Http().PostNoAuth("/api/transak/countries", c.getTransakCountries)
}

func (c *PayController) createTransakOrder(ctx *abugo.AbuHttpContent) {
	errcode := 0
	rechargeReq := TransakRechargeRequest{FiatAmount: "", DefaultCryptoAmount: ""}
	err := ctx.RequestData(&rechargeReq)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	daoUser := server.DaoxHashGame().XUser
	db := daoUser.WithContext(ctx.Gin())
	user, err := db.Where(daoUser.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}
	// 获取用户充值的钱包地址
	userRechargeAddress := getUserRechargeAddress(rechargeReq, token, user)
	if userRechargeAddress == "" {
		ctx.RespErr(errors.New("wallet address empty"), &errcode)
		return
	}

	// 获取支付通道
	payMethod, _ := server.XDb().Table("x_finance_method").Where("Brand = ?", "transak").Where("SellerId = ?", token.SellerId, "").First()

	if payMethod == nil {
		ctx.RespErr(errors.New("pay method not found"), &errcode)
		return
	}

	config := map[string]interface{}{}
	json.Unmarshal([]byte(payMethod.String("ExtraConfig")), &config)
	//网络定义
	networkFormat := map[string]string{
		"Tron":     "tron",
		"Ethereum": "eth",
	}

	orderData := xgo.H{
		"SellerId":     token.SellerId,
		"ChannelId":    token.ChannelId,
		"UserId":       token.UserId,
		"Symbol":       strings.ToLower(rechargeReq.FiatCurrency),
		"Net":          networkFormat[rechargeReq.Network],
		"State":        3,
		"CSGroup":      user.CSGroup,
		"CSId":         user.CSID,
		"SpecialAgent": user.SpecialAgent,
		"TopAgentId":   user.TopAgentID,
		"ToAddress":    userRechargeAddress,
	}

	var isDefaultCryptoAmount, isFiatAmount string
	if rechargeReq.FiatAmount != "" {
		isFiatAmount = fmt.Sprintf("&fiatAmount=%v", rechargeReq.FiatAmount)
		orderData["Amount"] = rechargeReq.FiatAmount
	}

	if rechargeReq.DefaultCryptoAmount != "" {
		isDefaultCryptoAmount = fmt.Sprintf("&defaultCryptoAmount=%v", rechargeReq.DefaultCryptoAmount)
		orderData["RealAmount"] = rechargeReq.DefaultCryptoAmount
	}

	//生成待支付订单
	orderId, _ := server.Db().Table("x_recharge_transak").Insert(orderData)

	payurl := "https://global.transak.com/"
	transakPayUrl := fmt.Sprintf("%v?apiKey=%v&productsAvailed=true&network=%v&cryptoCurrencyCode=%v&fiatCurrency=%v&walletAddress=%v&disableWalletAddressForm=true&partnerCustomerId=%d&partnerOrderId=%d%v%v", payurl, config["api_key"], rechargeReq.Network, rechargeReq.CryptoCurrencyCode, rechargeReq.FiatCurrency, userRechargeAddress, user.UserID, orderId, isDefaultCryptoAmount, isFiatAmount)

	ctx.RespOK(xgo.H{
		"payurl":  transakPayUrl,
		"orderId": orderId,
	})
}

// 回调
func (c *PayController) webhook(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)

	logs.Info("Transak 钩子数据", string(body))
	// 获取配置信息
	payMethod, _ := server.XDb().Table("x_finance_method").Where("Brand = ?", "transak").Find()
	if payMethod == nil {
		ctx.RespErr(errors.New("pay method not found"), nil)
		return
	}

	config := map[string]interface{}{}

	token := &jwt.Token{}
	err := errors.New("")
	// 遍历支付通道 获取正确的access_token
	payMethod.ForEach(func(item *xgo.XMap) bool {
		json.Unmarshal([]byte(item.String("ExtraConfig")), &config)
		accessToken := config["access_token"].(string)

		// 解析请求体
		//decoder := json.NewDecoder(ctx.Gin().Request.Body)
		var data map[string]interface{}
		if err = json.Unmarshal(body, &data); err != nil {
			return false
		}

		// 获取 JWT token
		tokenString := data["data"].(string)

		// 验证 JWT token
		token, _ = jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			return []byte(accessToken), nil
		})

		// 如果token不为空 跳出循环
		if token != nil {
			return false
		}

		return true
	})

	// 如果循环结束有错误信息
	if err != nil {
		errcode := 0
		ctx.RespErr(err, &errcode)
		return

	}

	// 提取数据并处理
	claims, ok := token.Claims.(jwt.MapClaims)
	jsonData, _ := json.Marshal(claims)
	logs.Info("Transak 钩子数据序列化:", string(jsonData))
	if ok && token.Valid {
		eventId := claims["eventID"].(string)
		webhookData := claims["webhookData"].(map[string]interface{})
		orderId := webhookData["id"].(string)
		walletAddress := webhookData["walletAddress"].(string)
		partnerOrderID := webhookData["partnerOrderId"].(string)

		// 发起了创建订单
		if eventId == "ORDER_CREATED" {
			_, err = server.XDb().Table("x_recharge_transak").Where("Id = ? and State = 3", partnerOrderID).Update(xgo.H{
				"ThirdId": orderId,
			})
			if err != nil {
				errcode := 0
				ctx.RespErr(err, &errcode)
				return
			}
		}

		if eventId == "ORDER_COMPLETED" {
			//获取订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "Id", "=", partnerOrderID, 0)
			where.Add("and", "ToAddress", "=", walletAddress, 0)
			orderData, oErr := server.Db().Table("x_recharge_transak").Select("Id,ToAddress").Where(where).GetOne()
			if oErr != nil {
				errcode := 0
				ctx.RespErr(oErr, &errcode)
				return
			}

			if (*orderData)["ToAddress"] == walletAddress {
				// 更新交易Hash
				transactionHash := webhookData["transactionHash"].(string)
				fiatAmount := webhookData["fiatAmount"].(float64)
				cryptoAmount := webhookData["cryptoAmount"].(float64)
				_, uErr := server.XDb().Table("x_recharge_transak").Where("Id = ? and State = 3", partnerOrderID).Update(xgo.H{
					"Amount":       fiatAmount,
					"TxId":         transactionHash,
					"RealAmount":   cryptoAmount,
					"TransferRate": fiatAmount / cryptoAmount,
					"State":        5,
				})

				if uErr != nil {
					errcode := 0
					ctx.RespErr(uErr, &errcode)
					return
				}

				//c.recharge_result(int(xgo.ToInt(partnerCustomerID)), int(xgo.ToInt(partnerOrderID)), 5)
				logs.Info("Transak 支付成功", partnerOrderID)
			}
		}

	} else {
		errcode := 0
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// 获取国家列表
func (c *PayController) getTransakCountries(ctx *abugo.AbuHttpContent) {

	type Countries struct {
		Country string `json:"country"`
		Symbol  string `json:"symbol"`
	}

	type TransakCountry struct {
		Alpha2             string   `json:"alpha2"`
		Alpha3             string   `json:"alpha3"`
		IsAllowed          bool     `json:"isAllowed"`
		IsLightKycAllowed  bool     `json:"isLightKycAllowed"`
		Name               string   `json:"name"`
		CurrencyCode       string   `json:"currencyCode"`
		SupportedDocuments []string `json:"supportedDocuments"`
	}

	host := ctx.Host()
	token := server.GetToken(ctx)
	_, sellerId := server.GetChannel(ctx, host)
	if token != nil {
		sellerId = token.SellerId
	}

	var countries []Countries

	// 获取支付通道
	payMethod, _ := server.XDb().Table("x_finance_method").Where("Brand = ?", "transak").Where("SellerId = ?", sellerId, "").First()
	if payMethod == nil {
		ctx.RespOK(countries)
		return
	}

	var data map[string][]TransakCountry
	resp, err := req.Get("https://api.transak.com/api/v2/countries")
	defer resp.Response().Body.Close()
	body, _ := io.ReadAll(resp.Response().Body)
	if err != nil {
		return
	}
	json.Unmarshal(body, &data)

	countriesData := data["response"]

	// 需要过滤的国家
	// restrictedCountries := map[string]bool{
	// 	"ABW": true, // Aruba
	// 	"AUS": true, // Australia
	// 	"BES": true, // BES islands (Bonaire, Saba, Statia)
	// 	"BRA": true, // Brazil
	// 	"CAN": true, // Canada
	// 	"CUW": true, // Curaçao
	// 	"FRA": true, // France
	// 	"IRN": true, // Iran
	// 	"IRQ": true, // Iraq
	// 	"NLD": true, // Netherlands
	// 	"ESP": true, // Spain
	// 	"SXM": true, // St. Maarten
	// 	"USA": true, // United States of America
	// 	"GBR": true, // United Kingdom
	// }

	for _, country := range countriesData {
		// 跳过受限制的国家
		// if restrictedCountries[country.Alpha3] {
		// 	continue
		// }

		countries = append(countries, Countries{
			Country: country.Alpha2,
			Symbol:  country.CurrencyCode,
		})
	}

	countries = append(countries, Countries{
		Country: "TH",
		Symbol:  "THB",
	}, Countries{
		Country: "RU",
		Symbol:  "RUB",
	}, Countries{
		Country: "VN",
		Symbol:  "VND",
	}, Countries{
		Country: "UA",
		Symbol:  "UAH",
	})

	//json.Marshal(&countries)

	ctx.RespOK(countries)
}

// 获取用户钱包地址
func getUserRechargeAddress(rechargeReq TransakRechargeRequest, token *server.TokenData, user *model.XUser) (address string) {
	//rechargeReq := TransakRechargeRequest{}

	// 获取Tron用户钱包地址

	if rechargeReq.Network == "Tron" {
		RechargeAddressTron := ""
		if user.RechargeAddressTron != "" {
			RechargeAddressTron = user.RechargeAddressTron
		}
		if len(RechargeAddressTron) == 0 {
			for {
				sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 1 limit 1"
				server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressTron)
				if len(RechargeAddressTron) == 0 {
					break
				}
				sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 1"
				r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressTron)
				RowsAffected, _ := r.RowsAffected()
				if RowsAffected == 1 {
					sql = "update x_user set RechargeAddressTron = ? where UserId = ?"
					server.Db().Conn().Exec(sql, RechargeAddressTron, token.UserId)
					break
				}
			}
		}

		return RechargeAddressTron
	}

	// 获取Eth用户钱包地址

	if rechargeReq.Network == "Ethereum" {
		RechargeAddressEth := ""
		if user.RechargeAddressEth != "" {
			RechargeAddressEth = user.RechargeAddressEth
		}
		if len(RechargeAddressEth) == 0 {
			for {
				sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 2 limit 1"
				server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressEth)
				if len(RechargeAddressEth) == 0 {
					break
				}
				sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 2"
				r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressEth)
				RowsAffected, _ := r.RowsAffected()
				if RowsAffected == 1 {
					sql = "update x_user set RechargeAddressEth = ? where UserId = ?"
					server.Db().Conn().Exec(sql, RechargeAddressEth, token.UserId)
					break
				}
			}
		}

		return RechargeAddressEth
	}

	return ""
}
