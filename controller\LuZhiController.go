package controller

import (
	"encoding/json"
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type LuZhiController struct {
}

func (c *LuZhiController) Init() {
	server.Http().PostNoAuth("/api/luzi/list", c.list)
	//server.Http().PostNoAuth("/api/luzi/get", c.get)
}

func (c *LuZhiController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1 // 默认波场
	}
	rediskey := fmt.Sprintf("%s:%s:blockopen:%d", server.Project(), server.Module(), reqdata.ChainType)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := []map[string]interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.Put("blockdata", jdata)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "ChainType", "=", reqdata.ChainType, 0)
		data, _ := server.Db().Table("x_blockopen").Where(where).OrderBy("id desc").Limit(112).GetList()
		if data != nil {
			server.Redis().SetEx(rediskey, 3, data)
			ctx.Put("blockdata", data)
		}
	}
	ctx.RespOK()
}

func (c *LuZhiController) get(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	rediskey := fmt.Sprintf("%s:%s:blockopen:%d", server.Project(), server.Module(), reqdata.Id)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := map[string]interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.Put("blockdata", jdata)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "Id", "=", reqdata.Id, "")
		data, _ := server.Db().Table("x_blockopen").Where(where).GetOne()
		if data != nil {
			server.Redis().SetEx(rediskey, 10, data)
			ctx.Put("blockdata", data)
		}
	}
	ctx.RespOK()
}
