// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipInfo(db *gorm.DB, opts ...gen.DOOption) xVipInfo {
	_xVipInfo := xVipInfo{}

	_xVipInfo.xVipInfoDo.UseDB(db, opts...)
	_xVipInfo.xVipInfoDo.UseModel(&model.XVipInfo{})

	tableName := _xVipInfo.xVipInfoDo.TableName()
	_xVipInfo.ALL = field.NewAsterisk(tableName)
	_xVipInfo.ID = field.NewInt32(tableName, "Id")
	_xVipInfo.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipInfo.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipInfo.UserID = field.NewInt32(tableName, "UserId")
	_xVipInfo.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xVipInfo.Recharge = field.NewFloat64(tableName, "Recharge")
	_xVipInfo.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xVipInfo.KeepLiuSui = field.NewFloat64(tableName, "KeepLiuSui")
	_xVipInfo.UpgradeTime = field.NewTime(tableName, "UpgradeTime")
	_xVipInfo.UpgradeRewardRecord = field.NewString(tableName, "UpgradeRewardRecord")
	_xVipInfo.Withdraw = field.NewFloat64(tableName, "Withdraw")
	_xVipInfo.CaiJin = field.NewFloat64(tableName, "CaiJin")
	_xVipInfo.CaiJinTrx = field.NewFloat64(tableName, "CaiJinTrx")

	_xVipInfo.fillFieldMap()

	return _xVipInfo
}

type xVipInfo struct {
	xVipInfoDo xVipInfoDo

	ALL                 field.Asterisk
	ID                  field.Int32
	SellerID            field.Int32   // 运营商
	ChannelID           field.Int32   // 渠道商
	UserID              field.Int32   // 玩家id
	VipLevel            field.Int32   // 当前vip等级
	Recharge            field.Float64 // 累计充值
	LiuSui              field.Float64 // 累计流水
	KeepLiuSui          field.Float64 // 保级流水
	UpgradeTime         field.Time    // 升级时间
	UpgradeRewardRecord field.String
	Withdraw            field.Float64 // 累计提现
	CaiJin              field.Float64 // 累计彩金Usdt
	CaiJinTrx           field.Float64 // 累计彩金Trx

	fieldMap map[string]field.Expr
}

func (x xVipInfo) Table(newTableName string) *xVipInfo {
	x.xVipInfoDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipInfo) As(alias string) *xVipInfo {
	x.xVipInfoDo.DO = *(x.xVipInfoDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipInfo) updateTableName(table string) *xVipInfo {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.Recharge = field.NewFloat64(table, "Recharge")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.KeepLiuSui = field.NewFloat64(table, "KeepLiuSui")
	x.UpgradeTime = field.NewTime(table, "UpgradeTime")
	x.UpgradeRewardRecord = field.NewString(table, "UpgradeRewardRecord")
	x.Withdraw = field.NewFloat64(table, "Withdraw")
	x.CaiJin = field.NewFloat64(table, "CaiJin")
	x.CaiJinTrx = field.NewFloat64(table, "CaiJinTrx")

	x.fillFieldMap()

	return x
}

func (x *xVipInfo) WithContext(ctx context.Context) *xVipInfoDo { return x.xVipInfoDo.WithContext(ctx) }

func (x xVipInfo) TableName() string { return x.xVipInfoDo.TableName() }

func (x xVipInfo) Alias() string { return x.xVipInfoDo.Alias() }

func (x xVipInfo) Columns(cols ...field.Expr) gen.Columns { return x.xVipInfoDo.Columns(cols...) }

func (x *xVipInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipInfo) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["Recharge"] = x.Recharge
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["KeepLiuSui"] = x.KeepLiuSui
	x.fieldMap["UpgradeTime"] = x.UpgradeTime
	x.fieldMap["UpgradeRewardRecord"] = x.UpgradeRewardRecord
	x.fieldMap["Withdraw"] = x.Withdraw
	x.fieldMap["CaiJin"] = x.CaiJin
	x.fieldMap["CaiJinTrx"] = x.CaiJinTrx
}

func (x xVipInfo) clone(db *gorm.DB) xVipInfo {
	x.xVipInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipInfo) replaceDB(db *gorm.DB) xVipInfo {
	x.xVipInfoDo.ReplaceDB(db)
	return x
}

type xVipInfoDo struct{ gen.DO }

func (x xVipInfoDo) Debug() *xVipInfoDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipInfoDo) WithContext(ctx context.Context) *xVipInfoDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipInfoDo) ReadDB() *xVipInfoDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipInfoDo) WriteDB() *xVipInfoDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipInfoDo) Session(config *gorm.Session) *xVipInfoDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipInfoDo) Clauses(conds ...clause.Expression) *xVipInfoDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipInfoDo) Returning(value interface{}, columns ...string) *xVipInfoDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipInfoDo) Not(conds ...gen.Condition) *xVipInfoDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipInfoDo) Or(conds ...gen.Condition) *xVipInfoDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipInfoDo) Select(conds ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipInfoDo) Where(conds ...gen.Condition) *xVipInfoDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipInfoDo) Order(conds ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipInfoDo) Distinct(cols ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipInfoDo) Omit(cols ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipInfoDo) Join(table schema.Tabler, on ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipInfoDo) Group(cols ...field.Expr) *xVipInfoDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipInfoDo) Having(conds ...gen.Condition) *xVipInfoDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipInfoDo) Limit(limit int) *xVipInfoDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipInfoDo) Offset(offset int) *xVipInfoDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipInfoDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipInfoDo) Unscoped() *xVipInfoDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipInfoDo) Create(values ...*model.XVipInfo) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipInfoDo) CreateInBatches(values []*model.XVipInfo, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipInfoDo) Save(values ...*model.XVipInfo) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipInfoDo) First() (*model.XVipInfo, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipInfo), nil
	}
}

func (x xVipInfoDo) Take() (*model.XVipInfo, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipInfo), nil
	}
}

func (x xVipInfoDo) Last() (*model.XVipInfo, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipInfo), nil
	}
}

func (x xVipInfoDo) Find() ([]*model.XVipInfo, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipInfo), err
}

func (x xVipInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipInfo, err error) {
	buf := make([]*model.XVipInfo, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipInfoDo) FindInBatches(result *[]*model.XVipInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipInfoDo) Attrs(attrs ...field.AssignExpr) *xVipInfoDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipInfoDo) Assign(attrs ...field.AssignExpr) *xVipInfoDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipInfoDo) Joins(fields ...field.RelationField) *xVipInfoDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipInfoDo) Preload(fields ...field.RelationField) *xVipInfoDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipInfoDo) FirstOrInit() (*model.XVipInfo, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipInfo), nil
	}
}

func (x xVipInfoDo) FirstOrCreate() (*model.XVipInfo, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipInfo), nil
	}
}

func (x xVipInfoDo) FindByPage(offset int, limit int) (result []*model.XVipInfo, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipInfoDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipInfoDo) Delete(models ...*model.XVipInfo) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipInfoDo) withDO(do gen.Dao) *xVipInfoDo {
	x.DO = *do.(*gen.DO)
	return x
}
