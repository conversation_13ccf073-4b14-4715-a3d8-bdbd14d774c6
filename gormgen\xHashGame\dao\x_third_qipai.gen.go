// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdQipai(db *gorm.DB, opts ...gen.DOOption) xThirdQipai {
	_xThirdQipai := xThirdQipai{}

	_xThirdQipai.xThirdQipaiDo.UseDB(db, opts...)
	_xThirdQipai.xThirdQipaiDo.UseModel(&model.XThirdQipai{})

	tableName := _xThirdQipai.xThirdQipaiDo.TableName()
	_xThirdQipai.ALL = field.NewAsterisk(tableName)
	_xThirdQipai.ID = field.NewInt64(tableName, "Id")
	_xThirdQipai.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdQipai.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdQipai.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdQipai.UserID = field.NewInt32(tableName, "UserId")
	_xThirdQipai.Brand = field.NewString(tableName, "Brand")
	_xThirdQipai.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdQipai.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdQipai.GameID = field.NewString(tableName, "GameId")
	_xThirdQipai.GameName = field.NewString(tableName, "GameName")
	_xThirdQipai.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdQipai.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdQipai.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdQipai.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdQipai.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdQipai.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdQipai.Currency = field.NewString(tableName, "Currency")
	_xThirdQipai.RawData = field.NewString(tableName, "RawData")
	_xThirdQipai.State = field.NewInt32(tableName, "State")
	_xThirdQipai.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdQipai.DataState = field.NewInt32(tableName, "DataState")
	_xThirdQipai.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdQipai.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdQipai.CSID = field.NewString(tableName, "CSId")
	_xThirdQipai.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdQipai.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdQipai.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdQipai.GameRst = field.NewString(tableName, "GameRst")
	_xThirdQipai.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdQipai.IP = field.NewString(tableName, "Ip")
	_xThirdQipai.Lang = field.NewString(tableName, "Lang")
	_xThirdQipai.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdQipai.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdQipai.BetType = field.NewInt32(tableName, "BetType")
	_xThirdQipai.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdQipai.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdQipai.fillFieldMap()

	return _xThirdQipai
}

type xThirdQipai struct {
	xThirdQipaiDo xThirdQipaiDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	ThirdRefID     field.String  // 三方备用注单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	BetType        field.Int32   // 下注类型：1=纯真金 2=混合下注 3=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdQipai) Table(newTableName string) *xThirdQipai {
	x.xThirdQipaiDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdQipai) As(alias string) *xThirdQipai {
	x.xThirdQipaiDo.DO = *(x.xThirdQipaiDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdQipai) updateTableName(table string) *xThirdQipai {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdQipai) WithContext(ctx context.Context) *xThirdQipaiDo {
	return x.xThirdQipaiDo.WithContext(ctx)
}

func (x xThirdQipai) TableName() string { return x.xThirdQipaiDo.TableName() }

func (x xThirdQipai) Alias() string { return x.xThirdQipaiDo.Alias() }

func (x xThirdQipai) Columns(cols ...field.Expr) gen.Columns { return x.xThirdQipaiDo.Columns(cols...) }

func (x *xThirdQipai) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdQipai) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdQipai) clone(db *gorm.DB) xThirdQipai {
	x.xThirdQipaiDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdQipai) replaceDB(db *gorm.DB) xThirdQipai {
	x.xThirdQipaiDo.ReplaceDB(db)
	return x
}

type xThirdQipaiDo struct{ gen.DO }

func (x xThirdQipaiDo) Debug() *xThirdQipaiDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdQipaiDo) WithContext(ctx context.Context) *xThirdQipaiDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdQipaiDo) ReadDB() *xThirdQipaiDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdQipaiDo) WriteDB() *xThirdQipaiDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdQipaiDo) Session(config *gorm.Session) *xThirdQipaiDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdQipaiDo) Clauses(conds ...clause.Expression) *xThirdQipaiDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdQipaiDo) Returning(value interface{}, columns ...string) *xThirdQipaiDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdQipaiDo) Not(conds ...gen.Condition) *xThirdQipaiDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdQipaiDo) Or(conds ...gen.Condition) *xThirdQipaiDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdQipaiDo) Select(conds ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdQipaiDo) Where(conds ...gen.Condition) *xThirdQipaiDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdQipaiDo) Order(conds ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdQipaiDo) Distinct(cols ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdQipaiDo) Omit(cols ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdQipaiDo) Join(table schema.Tabler, on ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdQipaiDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdQipaiDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdQipaiDo) Group(cols ...field.Expr) *xThirdQipaiDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdQipaiDo) Having(conds ...gen.Condition) *xThirdQipaiDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdQipaiDo) Limit(limit int) *xThirdQipaiDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdQipaiDo) Offset(offset int) *xThirdQipaiDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdQipaiDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdQipaiDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdQipaiDo) Unscoped() *xThirdQipaiDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdQipaiDo) Create(values ...*model.XThirdQipai) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdQipaiDo) CreateInBatches(values []*model.XThirdQipai, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdQipaiDo) Save(values ...*model.XThirdQipai) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdQipaiDo) First() (*model.XThirdQipai, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQipai), nil
	}
}

func (x xThirdQipaiDo) Take() (*model.XThirdQipai, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQipai), nil
	}
}

func (x xThirdQipaiDo) Last() (*model.XThirdQipai, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQipai), nil
	}
}

func (x xThirdQipaiDo) Find() ([]*model.XThirdQipai, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdQipai), err
}

func (x xThirdQipaiDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdQipai, err error) {
	buf := make([]*model.XThirdQipai, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdQipaiDo) FindInBatches(result *[]*model.XThirdQipai, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdQipaiDo) Attrs(attrs ...field.AssignExpr) *xThirdQipaiDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdQipaiDo) Assign(attrs ...field.AssignExpr) *xThirdQipaiDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdQipaiDo) Joins(fields ...field.RelationField) *xThirdQipaiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdQipaiDo) Preload(fields ...field.RelationField) *xThirdQipaiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdQipaiDo) FirstOrInit() (*model.XThirdQipai, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQipai), nil
	}
}

func (x xThirdQipaiDo) FirstOrCreate() (*model.XThirdQipai, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQipai), nil
	}
}

func (x xThirdQipaiDo) FindByPage(offset int, limit int) (result []*model.XThirdQipai, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdQipaiDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdQipaiDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdQipaiDo) Delete(models ...*model.XThirdQipai) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdQipaiDo) withDO(do gen.Dao) *xThirdQipaiDo {
	x.DO = *do.(*gen.DO)
	return x
}
