package single

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// Prepay 预付款 API URL Prepay
func (l *RSGSingleService) Prepay(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string  `json:"SystemCode"`    // 系統代碼(只限英數)，必填，长度2~20
		WebId         string  `json:"WebId"`         // 站台代碼(只限英數)，必填，长度3~20
		UserId        string  `json:"UserId"`        // 會員惟一識別碼(只限英數)，必填，长度3~20
		GameId        int     `json:"GameId"`        // 遊戲代碼(只限 3001 & 3002)，必填
		Currency      string  `json:"Currency"`      // 幣別代碼(請參照代碼表)，必填，长度2~5
		TransactionId string  `json:"TransactionId"` // 交易惟一識別碼(只限英數、@)，必填，长度8~30
		SessionId     string  `json:"SessionId"`     // 同一交易過程識別碼，必填，长度36
		Amount        float64 `json:"Amount"`        // 預扣金額(小數點兩位) (範圍0.00~9999999999.99)，必填
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
			Amount  float64 `json:"Amount"`  // 会员预扣款金额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 预付款")
	if err != nil {
		logs.Error("RSG_single 预付款 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 预付款 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 预付款 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 预付款 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 预付款 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 检查余额是否足够
	if reqdata.Amount <= 0 {
		logs.Error("RSG_single 预付款 余额不足 userId=", userId, " reqdata.Amount=", reqdata.Amount)
		respdata.ErrorCode = RSG_Code_Balance_Not_Enough
		respdata.ErrorMessage = "金额不正确"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
	}
	thirdId := reqdata.TransactionId
	// 开始预付款事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 预付款 获取用户余额失败 userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		}

		// 获取投注渠道
		//ChannelId := base.GetUserChannelId(ctx, &userBalance)

		// 检查余额是否足够
		if userBalance.Amount < reqdata.Amount {
			logs.Error("RSG_single 预付款 余额不足 userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdata.Amount=", reqdata.Amount)
			respdata.ErrorCode = RSG_Code_Balance_Not_Enough
			respdata.ErrorMessage = "余额不足"
			return e
		}

		// 扣除用户余额
		if reqdata.Amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, reqdata.Amount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", reqdata.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
				e = fmt.Errorf("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 预付款 扣除用户余额失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       -reqdata.Amount,
				AfterAmount:  userBalance.Amount - reqdata.Amount,
				Reason:       utils.BalanceCReasonRSGPrepay,
				Memo:         "RSG 预扣款,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   utils.GetCurrentTime(),
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("RSG_single 预付款 创建账变记录失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			//保存原始订单号，用于后续查单 2=加款 1=扣款
			e := l.AddThirdAmountLog(tx, -reqdata.Amount, 1, userId, reqdata.TransactionId, reqdata.SessionId, amountLog.Memo)
			if e != nil {
				logs.Error("RSG_single 插入三方账变记录失败: err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}

		//// 创建预付款订单
		//rawData, _ := json.Marshal(reqdata)
		//prepayOrder := thirdGameModel.ThirdOrder{
		//	UserId:     userId,
		//	ThirdId:    thirdId,
		//	Brand:      l.brandName,
		//	GameId:     strconv.Itoa(reqdata.GameId),
		//	GameName:   gameName,
		//	BetAmount:  reqdata.Amount,
		//	WinAmount:  0,
		//	DataState:  4, // 预付款状态
		//	RawData:    string(rawData),
		//	CreateTime: utils.GetCurrentTime(),
		//	ThirdTime:  utils.GetCurrentTime(),
		//	SellerId:   userBalance.SellerId,
		//	ChannelId:  ChannelId,
		//}
		//e = tx.Table(tablePrepay).Create(&prepayOrder).Error
		//if e != nil {
		//	logs.Error("RSG_single 预付款 创建预付款订单失败 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
		//	respdata.ErrorCode = RSG_Code_System_Busy
		//	respdata.ErrorMessage = "系统维护中"
		//	return e
		//}

		// 设置响应数据
		respdata.Data.Balance = userBalance.Amount - reqdata.Amount
		balance := math.Floor(respdata.Data.Balance*100) / 100

		amount := math.Floor(reqdata.Amount*100) / 100
		respdata.Data.Balance = balance
		respdata.Data.Amount = amount
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 预付款 事务处理失败 userId=", userId, " err=", err)
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
	if err != nil {
		logs.Error("RSG_single 预付款 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 预付款")
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 预付款 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 预付款 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

/**
 * 记录三方账变和单号用于查询注单金额
 */
func (s *RSGSingleService) AddThirdAmountLog(tx *daogorm.DB, changeAmount float64, reason int, userId int, thirdId string, transactionId string, memo string) error {
	amountLog := thirdGameModel.ThirdAmountLog{
		UserId:        userId,
		Amount:        changeAmount,
		ThirdId:       thirdId,
		TransactionId: transactionId,
		Reason:        reason,
		Memo:          memo,
		Brand:         s.brandName,
		CreateTime:    utils.GetCurrentTime(),
	}
	e := tx.Table("x_third_amount_log").Create(&amountLog).Error
	if e != nil {
		return e
	}
	return nil
}
