package kwai

import (
	"encoding/json"
	"github.com/beego/beego/logs"
	"sync"
	"xserver/server"
)

const apiUrlTpl = "http://www.adsnebula.com/log/common/api"

type Maidian struct {
	Kwai struct {
		BackEnd struct {
			PixelID     string `json:"PIXEL_ID"`
			AccessToken string `json:"ACCESS_TOKEN"`
			IsTest      bool   `json:"IsTest"`
		} `json:"BackEnd"`
		Front struct {
			PixelID string `json:"PIXEL_ID"`
			IsTest  bool   `json:"IsTest"`
		} `json:"Front"`
	} `json:"Kwai"`
}

var mu sync.Mutex

func loadConfig(host string) {
	clients = make(map[string]*Client)
	mu.Lock()
	defer mu.Unlock()
	xChannelHost := server.DaoxHashGame().XChannelHost
	hostConfig, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	if hostConfig == nil {
		logs.Error("暂无Kwai埋点配置！")
		return
	}

	if hostConfig.Maidian == "" {
		logs.Error("暂无Kwai埋点配置")
		return
	}

	var cfg Maidian

	err := json.Unmarshal([]byte(hostConfig.Maidian), &cfg)
	if err != nil {
		logs.Error(err)
		return
	}

	clients[host] = &Client{
		host:        host,
		api:         apiUrlTpl,
		pixelId:     cfg.Kwai.BackEnd.PixelID,
		accessToken: cfg.Kwai.BackEnd.AccessToken,
		isTest:      cfg.Kwai.BackEnd.IsTest,
	}

	return
}
