// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserOnline(db *gorm.DB, opts ...gen.DOOption) xUserOnline {
	_xUserOnline := xUserOnline{}

	_xUserOnline.xUserOnlineDo.UseDB(db, opts...)
	_xUserOnline.xUserOnlineDo.UseModel(&model.XUserOnline{})

	tableName := _xUserOnline.xUserOnlineDo.TableName()
	_xUserOnline.ALL = field.NewAsterisk(tableName)
	_xUserOnline.ID = field.NewInt64(tableName, "Id")
	_xUserOnline.UserID = field.NewInt32(tableName, "UserId")
	_xUserOnline.Day = field.NewTime(tableName, "Day")
	_xUserOnline.Time = field.NewInt32(tableName, "Time")
	_xUserOnline.CreatedAt = field.NewTime(tableName, "CreatedAt")
	_xUserOnline.UpdatedAt = field.NewTime(tableName, "UpdatedAt")

	_xUserOnline.fillFieldMap()

	return _xUserOnline
}

type xUserOnline struct {
	xUserOnlineDo xUserOnlineDo

	ALL       field.Asterisk
	ID        field.Int64
	UserID    field.Int32
	Day       field.Time
	Time      field.Int32 // second
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (x xUserOnline) Table(newTableName string) *xUserOnline {
	x.xUserOnlineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserOnline) As(alias string) *xUserOnline {
	x.xUserOnlineDo.DO = *(x.xUserOnlineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserOnline) updateTableName(table string) *xUserOnline {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.Day = field.NewTime(table, "Day")
	x.Time = field.NewInt32(table, "Time")
	x.CreatedAt = field.NewTime(table, "CreatedAt")
	x.UpdatedAt = field.NewTime(table, "UpdatedAt")

	x.fillFieldMap()

	return x
}

func (x *xUserOnline) WithContext(ctx context.Context) *xUserOnlineDo {
	return x.xUserOnlineDo.WithContext(ctx)
}

func (x xUserOnline) TableName() string { return x.xUserOnlineDo.TableName() }

func (x xUserOnline) Alias() string { return x.xUserOnlineDo.Alias() }

func (x xUserOnline) Columns(cols ...field.Expr) gen.Columns { return x.xUserOnlineDo.Columns(cols...) }

func (x *xUserOnline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserOnline) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Day"] = x.Day
	x.fieldMap["Time"] = x.Time
	x.fieldMap["CreatedAt"] = x.CreatedAt
	x.fieldMap["UpdatedAt"] = x.UpdatedAt
}

func (x xUserOnline) clone(db *gorm.DB) xUserOnline {
	x.xUserOnlineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserOnline) replaceDB(db *gorm.DB) xUserOnline {
	x.xUserOnlineDo.ReplaceDB(db)
	return x
}

type xUserOnlineDo struct{ gen.DO }

func (x xUserOnlineDo) Debug() *xUserOnlineDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserOnlineDo) WithContext(ctx context.Context) *xUserOnlineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserOnlineDo) ReadDB() *xUserOnlineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserOnlineDo) WriteDB() *xUserOnlineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserOnlineDo) Session(config *gorm.Session) *xUserOnlineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserOnlineDo) Clauses(conds ...clause.Expression) *xUserOnlineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserOnlineDo) Returning(value interface{}, columns ...string) *xUserOnlineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserOnlineDo) Not(conds ...gen.Condition) *xUserOnlineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserOnlineDo) Or(conds ...gen.Condition) *xUserOnlineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserOnlineDo) Select(conds ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserOnlineDo) Where(conds ...gen.Condition) *xUserOnlineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserOnlineDo) Order(conds ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserOnlineDo) Distinct(cols ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserOnlineDo) Omit(cols ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserOnlineDo) Join(table schema.Tabler, on ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserOnlineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserOnlineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserOnlineDo) Group(cols ...field.Expr) *xUserOnlineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserOnlineDo) Having(conds ...gen.Condition) *xUserOnlineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserOnlineDo) Limit(limit int) *xUserOnlineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserOnlineDo) Offset(offset int) *xUserOnlineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserOnlineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserOnlineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserOnlineDo) Unscoped() *xUserOnlineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserOnlineDo) Create(values ...*model.XUserOnline) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserOnlineDo) CreateInBatches(values []*model.XUserOnline, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserOnlineDo) Save(values ...*model.XUserOnline) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserOnlineDo) First() (*model.XUserOnline, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnline), nil
	}
}

func (x xUserOnlineDo) Take() (*model.XUserOnline, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnline), nil
	}
}

func (x xUserOnlineDo) Last() (*model.XUserOnline, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnline), nil
	}
}

func (x xUserOnlineDo) Find() ([]*model.XUserOnline, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserOnline), err
}

func (x xUserOnlineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserOnline, err error) {
	buf := make([]*model.XUserOnline, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserOnlineDo) FindInBatches(result *[]*model.XUserOnline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserOnlineDo) Attrs(attrs ...field.AssignExpr) *xUserOnlineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserOnlineDo) Assign(attrs ...field.AssignExpr) *xUserOnlineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserOnlineDo) Joins(fields ...field.RelationField) *xUserOnlineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserOnlineDo) Preload(fields ...field.RelationField) *xUserOnlineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserOnlineDo) FirstOrInit() (*model.XUserOnline, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnline), nil
	}
}

func (x xUserOnlineDo) FirstOrCreate() (*model.XUserOnline, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnline), nil
	}
}

func (x xUserOnlineDo) FindByPage(offset int, limit int) (result []*model.XUserOnline, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserOnlineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserOnlineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserOnlineDo) Delete(models ...*model.XUserOnline) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserOnlineDo) withDO(do gen.Dao) *xUserOnlineDo {
	x.DO = *do.(*gen.DO)
	return x
}
