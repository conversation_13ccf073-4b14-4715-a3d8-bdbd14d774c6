// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXGameBrand(db *gorm.DB, opts ...gen.DOOption) xGameBrand {
	_xGameBrand := xGameBrand{}

	_xGameBrand.xGameBrandDo.UseDB(db, opts...)
	_xGameBrand.xGameBrandDo.UseModel(&model.XGameBrand{})

	tableName := _xGameBrand.xGameBrandDo.TableName()
	_xGameBrand.ALL = field.NewAsterisk(tableName)
	_xGameBrand.GameType = field.NewInt32(tableName, "GameType")
	_xGameBrand.Brand = field.NewString(tableName, "Brand")
	_xGameBrand.BrandName = field.NewString(tableName, "BrandName")
	_xGameBrand.ClientGameType = field.NewInt32(tableName, "ClientGameType")
	_xGameBrand.GameSortExID = field.NewString(tableName, "GameSortExId")
	_xGameBrand.BrandType = field.NewInt32(tableName, "BrandType")
	_xGameBrand.Status = field.NewInt32(tableName, "Status")
	_xGameBrand.CountryList = field.NewString(tableName, "CountryList")
	_xGameBrand.CreateTime = field.NewTime(tableName, "CreateTime")
	_xGameBrand.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xGameBrand.SpecialGames = field.NewString(tableName, "SpecialGames")
	_xGameBrand.DisplayGames = field.NewString(tableName, "DisplayGames")

	_xGameBrand.fillFieldMap()

	return _xGameBrand
}

// xGameBrand 游戏厂商
type xGameBrand struct {
	xGameBrandDo xGameBrandDo

	ALL            field.Asterisk
	GameType       field.Int32  // 游戏大类
	Brand          field.String // 厂商唯一标识
	BrandName      field.String // 厂商名
	ClientGameType field.Int32  // 客户端定义的GameType
	GameSortExID   field.String // GameSortEx(游戏小类排序)中的Id
	BrandType      field.Int32  // 游戏厂商分类 1自营 2三方
	Status         field.Int32  // 状态 1有效 2无效
	CountryList    field.String // 支持的地区（二位字母国家代码，英文逗号分隔）
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间
	SpecialGames   field.String // 特殊跳转到omg的游戏
	DisplayGames   field.String // 如果区域限制了，必须开启的游戏

	fieldMap map[string]field.Expr
}

func (x xGameBrand) Table(newTableName string) *xGameBrand {
	x.xGameBrandDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xGameBrand) As(alias string) *xGameBrand {
	x.xGameBrandDo.DO = *(x.xGameBrandDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xGameBrand) updateTableName(table string) *xGameBrand {
	x.ALL = field.NewAsterisk(table)
	x.GameType = field.NewInt32(table, "GameType")
	x.Brand = field.NewString(table, "Brand")
	x.BrandName = field.NewString(table, "BrandName")
	x.ClientGameType = field.NewInt32(table, "ClientGameType")
	x.GameSortExID = field.NewString(table, "GameSortExId")
	x.BrandType = field.NewInt32(table, "BrandType")
	x.Status = field.NewInt32(table, "Status")
	x.CountryList = field.NewString(table, "CountryList")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.SpecialGames = field.NewString(table, "SpecialGames")
	x.DisplayGames = field.NewString(table, "DisplayGames")

	x.fillFieldMap()

	return x
}

func (x *xGameBrand) WithContext(ctx context.Context) *xGameBrandDo {
	return x.xGameBrandDo.WithContext(ctx)
}

func (x xGameBrand) TableName() string { return x.xGameBrandDo.TableName() }

func (x xGameBrand) Alias() string { return x.xGameBrandDo.Alias() }

func (x xGameBrand) Columns(cols ...field.Expr) gen.Columns { return x.xGameBrandDo.Columns(cols...) }

func (x *xGameBrand) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xGameBrand) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["BrandName"] = x.BrandName
	x.fieldMap["ClientGameType"] = x.ClientGameType
	x.fieldMap["GameSortExId"] = x.GameSortExID
	x.fieldMap["BrandType"] = x.BrandType
	x.fieldMap["Status"] = x.Status
	x.fieldMap["CountryList"] = x.CountryList
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["SpecialGames"] = x.SpecialGames
	x.fieldMap["DisplayGames"] = x.DisplayGames
}

func (x xGameBrand) clone(db *gorm.DB) xGameBrand {
	x.xGameBrandDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xGameBrand) replaceDB(db *gorm.DB) xGameBrand {
	x.xGameBrandDo.ReplaceDB(db)
	return x
}

type xGameBrandDo struct{ gen.DO }

func (x xGameBrandDo) Debug() *xGameBrandDo {
	return x.withDO(x.DO.Debug())
}

func (x xGameBrandDo) WithContext(ctx context.Context) *xGameBrandDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xGameBrandDo) ReadDB() *xGameBrandDo {
	return x.Clauses(dbresolver.Read)
}

func (x xGameBrandDo) WriteDB() *xGameBrandDo {
	return x.Clauses(dbresolver.Write)
}

func (x xGameBrandDo) Session(config *gorm.Session) *xGameBrandDo {
	return x.withDO(x.DO.Session(config))
}

func (x xGameBrandDo) Clauses(conds ...clause.Expression) *xGameBrandDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xGameBrandDo) Returning(value interface{}, columns ...string) *xGameBrandDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xGameBrandDo) Not(conds ...gen.Condition) *xGameBrandDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xGameBrandDo) Or(conds ...gen.Condition) *xGameBrandDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xGameBrandDo) Select(conds ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xGameBrandDo) Where(conds ...gen.Condition) *xGameBrandDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xGameBrandDo) Order(conds ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xGameBrandDo) Distinct(cols ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xGameBrandDo) Omit(cols ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xGameBrandDo) Join(table schema.Tabler, on ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xGameBrandDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xGameBrandDo) RightJoin(table schema.Tabler, on ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xGameBrandDo) Group(cols ...field.Expr) *xGameBrandDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xGameBrandDo) Having(conds ...gen.Condition) *xGameBrandDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xGameBrandDo) Limit(limit int) *xGameBrandDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xGameBrandDo) Offset(offset int) *xGameBrandDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xGameBrandDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xGameBrandDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xGameBrandDo) Unscoped() *xGameBrandDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xGameBrandDo) Create(values ...*model.XGameBrand) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xGameBrandDo) CreateInBatches(values []*model.XGameBrand, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xGameBrandDo) Save(values ...*model.XGameBrand) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xGameBrandDo) First() (*model.XGameBrand, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameBrand), nil
	}
}

func (x xGameBrandDo) Take() (*model.XGameBrand, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameBrand), nil
	}
}

func (x xGameBrandDo) Last() (*model.XGameBrand, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameBrand), nil
	}
}

func (x xGameBrandDo) Find() ([]*model.XGameBrand, error) {
	result, err := x.DO.Find()
	return result.([]*model.XGameBrand), err
}

func (x xGameBrandDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XGameBrand, err error) {
	buf := make([]*model.XGameBrand, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xGameBrandDo) FindInBatches(result *[]*model.XGameBrand, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xGameBrandDo) Attrs(attrs ...field.AssignExpr) *xGameBrandDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xGameBrandDo) Assign(attrs ...field.AssignExpr) *xGameBrandDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xGameBrandDo) Joins(fields ...field.RelationField) *xGameBrandDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xGameBrandDo) Preload(fields ...field.RelationField) *xGameBrandDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xGameBrandDo) FirstOrInit() (*model.XGameBrand, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameBrand), nil
	}
}

func (x xGameBrandDo) FirstOrCreate() (*model.XGameBrand, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameBrand), nil
	}
}

func (x xGameBrandDo) FindByPage(offset int, limit int) (result []*model.XGameBrand, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xGameBrandDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xGameBrandDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xGameBrandDo) Delete(models ...*model.XGameBrand) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xGameBrandDo) withDO(do gen.Dao) *xGameBrandDo {
	x.DO = *do.(*gen.DO)
	return x
}
