// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXConfig(db *gorm.DB, opts ...gen.DOOption) xConfig {
	_xConfig := xConfig{}

	_xConfig.xConfigDo.UseDB(db, opts...)
	_xConfig.xConfigDo.UseModel(&model.XConfig{})

	tableName := _xConfig.xConfigDo.TableName()
	_xConfig.ALL = field.NewAsterisk(tableName)
	_xConfig.ID = field.NewInt32(tableName, "Id")
	_xConfig.SellerID = field.NewInt32(tableName, "SellerId")
	_xConfig.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xConfig.ConfigName = field.NewString(tableName, "ConfigName")
	_xConfig.ConfigValue = field.NewString(tableName, "ConfigValue")
	_xConfig.Remark = field.NewString(tableName, "Remark")
	_xConfig.IsShow = field.NewInt32(tableName, "IsShow")
	_xConfig.EditAble = field.NewInt32(tableName, "EditAble")

	_xConfig.fillFieldMap()

	return _xConfig
}

type xConfig struct {
	xConfigDo xConfigDo

	ALL         field.Asterisk
	ID          field.Int32
	SellerID    field.Int32  // 运营商
	ChannelID   field.Int32  // 渠道商
	ConfigName  field.String // 配置名称
	ConfigValue field.String
	Remark      field.String // 注释
	IsShow      field.Int32
	EditAble    field.Int32

	fieldMap map[string]field.Expr
}

func (x xConfig) Table(newTableName string) *xConfig {
	x.xConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xConfig) As(alias string) *xConfig {
	x.xConfigDo.DO = *(x.xConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xConfig) updateTableName(table string) *xConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ConfigName = field.NewString(table, "ConfigName")
	x.ConfigValue = field.NewString(table, "ConfigValue")
	x.Remark = field.NewString(table, "Remark")
	x.IsShow = field.NewInt32(table, "IsShow")
	x.EditAble = field.NewInt32(table, "EditAble")

	x.fillFieldMap()

	return x
}

func (x *xConfig) WithContext(ctx context.Context) *xConfigDo { return x.xConfigDo.WithContext(ctx) }

func (x xConfig) TableName() string { return x.xConfigDo.TableName() }

func (x xConfig) Alias() string { return x.xConfigDo.Alias() }

func (x xConfig) Columns(cols ...field.Expr) gen.Columns { return x.xConfigDo.Columns(cols...) }

func (x *xConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["ConfigName"] = x.ConfigName
	x.fieldMap["ConfigValue"] = x.ConfigValue
	x.fieldMap["Remark"] = x.Remark
	x.fieldMap["IsShow"] = x.IsShow
	x.fieldMap["EditAble"] = x.EditAble
}

func (x xConfig) clone(db *gorm.DB) xConfig {
	x.xConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xConfig) replaceDB(db *gorm.DB) xConfig {
	x.xConfigDo.ReplaceDB(db)
	return x
}

type xConfigDo struct{ gen.DO }

func (x xConfigDo) Debug() *xConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xConfigDo) WithContext(ctx context.Context) *xConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xConfigDo) ReadDB() *xConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xConfigDo) WriteDB() *xConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xConfigDo) Session(config *gorm.Session) *xConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xConfigDo) Clauses(conds ...clause.Expression) *xConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xConfigDo) Returning(value interface{}, columns ...string) *xConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xConfigDo) Not(conds ...gen.Condition) *xConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xConfigDo) Or(conds ...gen.Condition) *xConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xConfigDo) Select(conds ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xConfigDo) Where(conds ...gen.Condition) *xConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xConfigDo) Order(conds ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xConfigDo) Distinct(cols ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xConfigDo) Omit(cols ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xConfigDo) Join(table schema.Tabler, on ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xConfigDo) Group(cols ...field.Expr) *xConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xConfigDo) Having(conds ...gen.Condition) *xConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xConfigDo) Limit(limit int) *xConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xConfigDo) Offset(offset int) *xConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xConfigDo) Unscoped() *xConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xConfigDo) Create(values ...*model.XConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xConfigDo) CreateInBatches(values []*model.XConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xConfigDo) Save(values ...*model.XConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xConfigDo) First() (*model.XConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XConfig), nil
	}
}

func (x xConfigDo) Take() (*model.XConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XConfig), nil
	}
}

func (x xConfigDo) Last() (*model.XConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XConfig), nil
	}
}

func (x xConfigDo) Find() ([]*model.XConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XConfig), err
}

func (x xConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XConfig, err error) {
	buf := make([]*model.XConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xConfigDo) FindInBatches(result *[]*model.XConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xConfigDo) Attrs(attrs ...field.AssignExpr) *xConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xConfigDo) Assign(attrs ...field.AssignExpr) *xConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xConfigDo) Joins(fields ...field.RelationField) *xConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xConfigDo) Preload(fields ...field.RelationField) *xConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xConfigDo) FirstOrInit() (*model.XConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XConfig), nil
	}
}

func (x xConfigDo) FirstOrCreate() (*model.XConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XConfig), nil
	}
}

func (x xConfigDo) FindByPage(offset int, limit int) (result []*model.XConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xConfigDo) Delete(models ...*model.XConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xConfigDo) withDO(do gen.Dao) *xConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
