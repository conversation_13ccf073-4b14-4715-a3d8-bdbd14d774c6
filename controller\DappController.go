package controller

import (
	"xserver/abugo"
	"xserver/server"
)

type DappController struct {
}

func (c *DappController) Init() {
	gropu := server.Http().NewGroup("/api/dapp")
	{
		gropu.PostNoAuth("/bet", c.bet)
	}
}

func (c *DappController) bet(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int     `validate:"required"`
		Address  string  `validate:"required"`
		Amount   float64 `validate:"required"`
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	sql := "delete from x_dapp_bet where SellerId = ? and ChannelId = ? and Address = ?"
	server.Db().QueryNoResult(sql, SellerId, ChannelId, reqdata.Address)
	sql = "insert into x_dapp_bet values(?,?,?)"
	server.Db().QueryNoResult(sql, SellerId, ChannelId, reqdata.Address)
	ctx.RespOK()
}
