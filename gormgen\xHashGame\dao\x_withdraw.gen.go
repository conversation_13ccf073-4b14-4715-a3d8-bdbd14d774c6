// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXWithdraw(db *gorm.DB, opts ...gen.DOOption) xWithdraw {
	_xWithdraw := xWithdraw{}

	_xWithdraw.xWithdrawDo.UseDB(db, opts...)
	_xWithdraw.xWithdrawDo.UseModel(&model.XWithdraw{})

	tableName := _xWithdraw.xWithdrawDo.TableName()
	_xWithdraw.ALL = field.NewAsterisk(tableName)
	_xWithdraw.ID = field.NewInt32(tableName, "Id")
	_xWithdraw.UserID = field.NewInt32(tableName, "UserId")
	_xWithdraw.SellerID = field.NewInt32(tableName, "SellerId")
	_xWithdraw.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xWithdraw.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xWithdraw.OrderType = field.NewInt32(tableName, "OrderType")
	_xWithdraw.State = field.NewInt32(tableName, "State")
	_xWithdraw.Symbol = field.NewString(tableName, "Symbol")
	_xWithdraw.Net = field.NewString(tableName, "Net")
	_xWithdraw.RepeatCheck = field.NewInt32(tableName, "RepeatCheck")
	_xWithdraw.TotalWinLost = field.NewFloat64(tableName, "TotalWinLost")
	_xWithdraw.WithdrawLiuSui = field.NewFloat64(tableName, "WithdrawLiuSui")
	_xWithdraw.TotalLiuSui = field.NewFloat64(tableName, "TotalLiuSui")
	_xWithdraw.WithwardNeedLiuSui = field.NewInt32(tableName, "WithwardNeedLiuSui")
	_xWithdraw.TotalWithdrawLiuSui = field.NewFloat64(tableName, "TotalWithdrawLiuSui")
	_xWithdraw.OrderCheck = field.NewInt32(tableName, "OrderCheck")
	_xWithdraw.Address = field.NewString(tableName, "Address")
	_xWithdraw.AddressState = field.NewInt32(tableName, "AddressState")
	_xWithdraw.Amount = field.NewFloat64(tableName, "Amount")
	_xWithdraw.OrderFee = field.NewFloat64(tableName, "OrderFee")
	_xWithdraw.AfterAmount = field.NewFloat64(tableName, "AfterAmount")
	_xWithdraw.Txid = field.NewString(tableName, "Txid")
	_xWithdraw.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xWithdraw.AduitTime = field.NewTime(tableName, "AduitTime")
	_xWithdraw.AduitMemo = field.NewString(tableName, "AduitMemo")
	_xWithdraw.CreateTime = field.NewTime(tableName, "CreateTime")
	_xWithdraw.Account = field.NewString(tableName, "Account")
	_xWithdraw.Fee = field.NewFloat64(tableName, "Fee")
	_xWithdraw.HbcOrder = field.NewString(tableName, "HbcOrder")
	_xWithdraw.HbcState = field.NewInt32(tableName, "HbcState")
	_xWithdraw.HbcFinishTime = field.NewTime(tableName, "HbcFinishTime")
	_xWithdraw.Memo = field.NewString(tableName, "Memo")
	_xWithdraw.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xWithdraw.SendAccount = field.NewString(tableName, "SendAccount")
	_xWithdraw.SendTime = field.NewTime(tableName, "SendTime")
	_xWithdraw.LockState = field.NewInt32(tableName, "LockState")
	_xWithdraw.LockUserAccount = field.NewString(tableName, "LockUserAccount")
	_xWithdraw.LockUserID = field.NewInt32(tableName, "LockUserId")
	_xWithdraw.LockTime = field.NewTime(tableName, "LockTime")
	_xWithdraw.HbcMemo = field.NewString(tableName, "HbcMemo")
	_xWithdraw.NetFee = field.NewFloat64(tableName, "NetFee")
	_xWithdraw.CSGroup = field.NewString(tableName, "CSGroup")
	_xWithdraw.CSID = field.NewString(tableName, "CSId")
	_xWithdraw.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xWithdraw.PayType = field.NewInt32(tableName, "PayType")
	_xWithdraw.PayID = field.NewInt32(tableName, "PayId")
	_xWithdraw.PayData = field.NewString(tableName, "PayData")
	_xWithdraw.TransferRate = field.NewFloat64(tableName, "TransferRate")
	_xWithdraw.RealAmount = field.NewFloat64(tableName, "RealAmount")
	_xWithdraw.PayDataEx = field.NewString(tableName, "PayDataEx")
	_xWithdraw.ThirdID = field.NewString(tableName, "ThirdId")
	_xWithdraw.IsFirst = field.NewInt32(tableName, "IsFirst")
	_xWithdraw.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xWithdraw.WithdrawSuccessCount = field.NewInt32(tableName, "WithdrawSuccessCount")
	_xWithdraw.LastWithdrawTime = field.NewTime(tableName, "LastWithdrawTime")
	_xWithdraw.LastWithdrawFinishTime = field.NewTime(tableName, "LastWithdrawFinishTime")
	_xWithdraw.IsTgCounted = field.NewInt32(tableName, "IsTgCounted")
	_xWithdraw.FeeFreeCountPerDay = field.NewInt32(tableName, "FeeFreeCountPerDay")
	_xWithdraw.FeePercent = field.NewFloat64(tableName, "FeePercent")
	_xWithdraw.TodayWithdrawCount = field.NewInt32(tableName, "TodayWithdrawCount")

	_xWithdraw.fillFieldMap()

	return _xWithdraw
}

// xWithdraw 提现列表
type xWithdraw struct {
	xWithdrawDo xWithdrawDo

	ALL                    field.Asterisk
	ID                     field.Int32   // id
	UserID                 field.Int32   // 用户
	SellerID               field.Int32   // 运营商
	ChannelID              field.Int32   // 渠道
	BetChannelID           field.Int32   // 投注渠道
	OrderType              field.Int32   // 提款订单分类 31提款 32上庄提款
	State                  field.Int32   // 状态 0 待审核 1审核拒绝 2审核通过  8拒绝发放 4已发放 5正在出款 6出款完成  7失败退回
	Symbol                 field.String  // 币种
	Net                    field.String  // 区块链网络
	RepeatCheck            field.Int32   // 重复性检测(ip、设备、密码) 1通过 2未通过
	TotalWinLost           field.Float64 // 输赢总额
	WithdrawLiuSui         field.Float64 // 提现流水
	TotalLiuSui            field.Float64 // 当前流水
	WithwardNeedLiuSui     field.Int32   // 提现是否需要流水,1是,2否
	TotalWithdrawLiuSui    field.Float64
	OrderCheck             field.Int32   // 注单检测 1通过 2未通过
	Address                field.String  // 提现地址
	AddressState           field.Int32   // 状态 1已验证 2未验证
	Amount                 field.Float64 // 提现金额
	OrderFee               field.Float64 // 单日免手续费提款次数
	AfterAmount            field.Float64 // 提款后用户余额
	Txid                   field.String
	AuditAccount           field.String  // 审核人
	AduitTime              field.Time    // 审核时间
	AduitMemo              field.String  // 审核备注
	CreateTime             field.Time    // 提现时间
	Account                field.String  // 玩家账号
	Fee                    field.Float64 // 手续费
	HbcOrder               field.String  // hbc订单号
	HbcState               field.Int32   // hbc状态
	HbcFinishTime          field.Time    // hbc提现完成时间
	Memo                   field.String  // 备注
	TopAgentID             field.Int32   // 顶级id
	SendAccount            field.String  // 发放账号
	SendTime               field.Time    // 发放时间
	LockState              field.Int32   // 锁定状态 1锁定 2解锁
	LockUserAccount        field.String  // 锁定账号
	LockUserID             field.Int32   // 锁定账号Id
	LockTime               field.Time    // 锁定时间
	HbcMemo                field.String  // hbc备注
	NetFee                 field.Float64 // 链上费用
	CSGroup                field.String  // 客服组
	CSID                   field.String  // 客服id
	SpecialAgent           field.Int32   // 是否独立代理 1是,2否
	PayType                field.Int32   // 支付类型 1链上提款 2.pix
	PayID                  field.Int32   // 支付id
	PayData                field.String  // 提现额外数据
	TransferRate           field.Float64 // 汇率
	RealAmount             field.Float64 // 到账金额
	PayDataEx              field.String  // 提现额外数据
	ThirdID                field.String  // 三方订单id
	IsFirst                field.Int32   // 是否首次提款 1是 2否
	WithdrawCount          field.Int32   // 提款申请数
	WithdrawSuccessCount   field.Int32   // 提款成功数
	LastWithdrawTime       field.Time    // 上次提款申请时间
	LastWithdrawFinishTime field.Time    // 上次提款申请时间
	IsTgCounted            field.Int32   // 是否tg统计(0未统计 1已统计)
	FeeFreeCountPerDay     field.Int32   // 单日免手续费提款次数
	FeePercent             field.Float64 // 超出后手续费%
	TodayWithdrawCount     field.Int32   // 当日提现次数

	fieldMap map[string]field.Expr
}

func (x xWithdraw) Table(newTableName string) *xWithdraw {
	x.xWithdrawDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xWithdraw) As(alias string) *xWithdraw {
	x.xWithdrawDo.DO = *(x.xWithdrawDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xWithdraw) updateTableName(table string) *xWithdraw {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.OrderType = field.NewInt32(table, "OrderType")
	x.State = field.NewInt32(table, "State")
	x.Symbol = field.NewString(table, "Symbol")
	x.Net = field.NewString(table, "Net")
	x.RepeatCheck = field.NewInt32(table, "RepeatCheck")
	x.TotalWinLost = field.NewFloat64(table, "TotalWinLost")
	x.WithdrawLiuSui = field.NewFloat64(table, "WithdrawLiuSui")
	x.TotalLiuSui = field.NewFloat64(table, "TotalLiuSui")
	x.WithwardNeedLiuSui = field.NewInt32(table, "WithwardNeedLiuSui")
	x.TotalWithdrawLiuSui = field.NewFloat64(table, "TotalWithdrawLiuSui")
	x.OrderCheck = field.NewInt32(table, "OrderCheck")
	x.Address = field.NewString(table, "Address")
	x.AddressState = field.NewInt32(table, "AddressState")
	x.Amount = field.NewFloat64(table, "Amount")
	x.OrderFee = field.NewFloat64(table, "OrderFee")
	x.AfterAmount = field.NewFloat64(table, "AfterAmount")
	x.Txid = field.NewString(table, "Txid")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AduitTime = field.NewTime(table, "AduitTime")
	x.AduitMemo = field.NewString(table, "AduitMemo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Account = field.NewString(table, "Account")
	x.Fee = field.NewFloat64(table, "Fee")
	x.HbcOrder = field.NewString(table, "HbcOrder")
	x.HbcState = field.NewInt32(table, "HbcState")
	x.HbcFinishTime = field.NewTime(table, "HbcFinishTime")
	x.Memo = field.NewString(table, "Memo")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SendAccount = field.NewString(table, "SendAccount")
	x.SendTime = field.NewTime(table, "SendTime")
	x.LockState = field.NewInt32(table, "LockState")
	x.LockUserAccount = field.NewString(table, "LockUserAccount")
	x.LockUserID = field.NewInt32(table, "LockUserId")
	x.LockTime = field.NewTime(table, "LockTime")
	x.HbcMemo = field.NewString(table, "HbcMemo")
	x.NetFee = field.NewFloat64(table, "NetFee")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.PayType = field.NewInt32(table, "PayType")
	x.PayID = field.NewInt32(table, "PayId")
	x.PayData = field.NewString(table, "PayData")
	x.TransferRate = field.NewFloat64(table, "TransferRate")
	x.RealAmount = field.NewFloat64(table, "RealAmount")
	x.PayDataEx = field.NewString(table, "PayDataEx")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.IsFirst = field.NewInt32(table, "IsFirst")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawSuccessCount = field.NewInt32(table, "WithdrawSuccessCount")
	x.LastWithdrawTime = field.NewTime(table, "LastWithdrawTime")
	x.LastWithdrawFinishTime = field.NewTime(table, "LastWithdrawFinishTime")
	x.IsTgCounted = field.NewInt32(table, "IsTgCounted")
	x.FeeFreeCountPerDay = field.NewInt32(table, "FeeFreeCountPerDay")
	x.FeePercent = field.NewFloat64(table, "FeePercent")
	x.TodayWithdrawCount = field.NewInt32(table, "TodayWithdrawCount")

	x.fillFieldMap()

	return x
}

func (x *xWithdraw) WithContext(ctx context.Context) *xWithdrawDo {
	return x.xWithdrawDo.WithContext(ctx)
}

func (x xWithdraw) TableName() string { return x.xWithdrawDo.TableName() }

func (x xWithdraw) Alias() string { return x.xWithdrawDo.Alias() }

func (x xWithdraw) Columns(cols ...field.Expr) gen.Columns { return x.xWithdrawDo.Columns(cols...) }

func (x *xWithdraw) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xWithdraw) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 60)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["OrderType"] = x.OrderType
	x.fieldMap["State"] = x.State
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Net"] = x.Net
	x.fieldMap["RepeatCheck"] = x.RepeatCheck
	x.fieldMap["TotalWinLost"] = x.TotalWinLost
	x.fieldMap["WithdrawLiuSui"] = x.WithdrawLiuSui
	x.fieldMap["TotalLiuSui"] = x.TotalLiuSui
	x.fieldMap["WithwardNeedLiuSui"] = x.WithwardNeedLiuSui
	x.fieldMap["TotalWithdrawLiuSui"] = x.TotalWithdrawLiuSui
	x.fieldMap["OrderCheck"] = x.OrderCheck
	x.fieldMap["Address"] = x.Address
	x.fieldMap["AddressState"] = x.AddressState
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["OrderFee"] = x.OrderFee
	x.fieldMap["AfterAmount"] = x.AfterAmount
	x.fieldMap["Txid"] = x.Txid
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AduitTime"] = x.AduitTime
	x.fieldMap["AduitMemo"] = x.AduitMemo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Account"] = x.Account
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["HbcOrder"] = x.HbcOrder
	x.fieldMap["HbcState"] = x.HbcState
	x.fieldMap["HbcFinishTime"] = x.HbcFinishTime
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SendAccount"] = x.SendAccount
	x.fieldMap["SendTime"] = x.SendTime
	x.fieldMap["LockState"] = x.LockState
	x.fieldMap["LockUserAccount"] = x.LockUserAccount
	x.fieldMap["LockUserId"] = x.LockUserID
	x.fieldMap["LockTime"] = x.LockTime
	x.fieldMap["HbcMemo"] = x.HbcMemo
	x.fieldMap["NetFee"] = x.NetFee
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["PayType"] = x.PayType
	x.fieldMap["PayId"] = x.PayID
	x.fieldMap["PayData"] = x.PayData
	x.fieldMap["TransferRate"] = x.TransferRate
	x.fieldMap["RealAmount"] = x.RealAmount
	x.fieldMap["PayDataEx"] = x.PayDataEx
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["IsFirst"] = x.IsFirst
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawSuccessCount"] = x.WithdrawSuccessCount
	x.fieldMap["LastWithdrawTime"] = x.LastWithdrawTime
	x.fieldMap["LastWithdrawFinishTime"] = x.LastWithdrawFinishTime
	x.fieldMap["IsTgCounted"] = x.IsTgCounted
	x.fieldMap["FeeFreeCountPerDay"] = x.FeeFreeCountPerDay
	x.fieldMap["FeePercent"] = x.FeePercent
	x.fieldMap["TodayWithdrawCount"] = x.TodayWithdrawCount
}

func (x xWithdraw) clone(db *gorm.DB) xWithdraw {
	x.xWithdrawDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xWithdraw) replaceDB(db *gorm.DB) xWithdraw {
	x.xWithdrawDo.ReplaceDB(db)
	return x
}

type xWithdrawDo struct{ gen.DO }

func (x xWithdrawDo) Debug() *xWithdrawDo {
	return x.withDO(x.DO.Debug())
}

func (x xWithdrawDo) WithContext(ctx context.Context) *xWithdrawDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xWithdrawDo) ReadDB() *xWithdrawDo {
	return x.Clauses(dbresolver.Read)
}

func (x xWithdrawDo) WriteDB() *xWithdrawDo {
	return x.Clauses(dbresolver.Write)
}

func (x xWithdrawDo) Session(config *gorm.Session) *xWithdrawDo {
	return x.withDO(x.DO.Session(config))
}

func (x xWithdrawDo) Clauses(conds ...clause.Expression) *xWithdrawDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xWithdrawDo) Returning(value interface{}, columns ...string) *xWithdrawDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xWithdrawDo) Not(conds ...gen.Condition) *xWithdrawDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xWithdrawDo) Or(conds ...gen.Condition) *xWithdrawDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xWithdrawDo) Select(conds ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xWithdrawDo) Where(conds ...gen.Condition) *xWithdrawDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xWithdrawDo) Order(conds ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xWithdrawDo) Distinct(cols ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xWithdrawDo) Omit(cols ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xWithdrawDo) Join(table schema.Tabler, on ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xWithdrawDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xWithdrawDo) RightJoin(table schema.Tabler, on ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xWithdrawDo) Group(cols ...field.Expr) *xWithdrawDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xWithdrawDo) Having(conds ...gen.Condition) *xWithdrawDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xWithdrawDo) Limit(limit int) *xWithdrawDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xWithdrawDo) Offset(offset int) *xWithdrawDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xWithdrawDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xWithdrawDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xWithdrawDo) Unscoped() *xWithdrawDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xWithdrawDo) Create(values ...*model.XWithdraw) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xWithdrawDo) CreateInBatches(values []*model.XWithdraw, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xWithdrawDo) Save(values ...*model.XWithdraw) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xWithdrawDo) First() (*model.XWithdraw, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdraw), nil
	}
}

func (x xWithdrawDo) Take() (*model.XWithdraw, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdraw), nil
	}
}

func (x xWithdrawDo) Last() (*model.XWithdraw, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdraw), nil
	}
}

func (x xWithdrawDo) Find() ([]*model.XWithdraw, error) {
	result, err := x.DO.Find()
	return result.([]*model.XWithdraw), err
}

func (x xWithdrawDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XWithdraw, err error) {
	buf := make([]*model.XWithdraw, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xWithdrawDo) FindInBatches(result *[]*model.XWithdraw, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xWithdrawDo) Attrs(attrs ...field.AssignExpr) *xWithdrawDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xWithdrawDo) Assign(attrs ...field.AssignExpr) *xWithdrawDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xWithdrawDo) Joins(fields ...field.RelationField) *xWithdrawDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xWithdrawDo) Preload(fields ...field.RelationField) *xWithdrawDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xWithdrawDo) FirstOrInit() (*model.XWithdraw, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdraw), nil
	}
}

func (x xWithdrawDo) FirstOrCreate() (*model.XWithdraw, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdraw), nil
	}
}

func (x xWithdrawDo) FindByPage(offset int, limit int) (result []*model.XWithdraw, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xWithdrawDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xWithdrawDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xWithdrawDo) Delete(models ...*model.XWithdraw) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xWithdrawDo) withDO(do gen.Dao) *xWithdrawDo {
	x.DO = *do.(*gen.DO)
	return x
}
