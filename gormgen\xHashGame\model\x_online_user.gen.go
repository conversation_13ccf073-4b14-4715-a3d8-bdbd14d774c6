// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOnlineUser = "x_online_user"

// XOnlineUser mapped from table <x_online_user>
type XOnlineUser struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Type       int32     `gorm:"column:Type;not null;comment:1:访问站点的在线 2:玩游戏的在线 3:游戏厂商在线" json:"Type"`                // 1:访问站点的在线 2:玩游戏的在线 3:游戏厂商在线
	TypeName   string    `gorm:"column:TypeName;not null;comment:在线类型" json:"TypeName"`                               // 在线类型
	Online     int32     `gorm:"column:Online;not null;comment:在线人数" json:"Online"`                                   // 在线人数
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"CreateTime"` // 记录时间
}

// TableName XOnlineUser's table name
func (*XOnlineUser) TableName() string {
	return TableNameXOnlineUser
}
