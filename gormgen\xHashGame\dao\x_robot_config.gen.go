// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotConfig(db *gorm.DB, opts ...gen.DOOption) xRobotConfig {
	_xRobotConfig := xRobotConfig{}

	_xRobotConfig.xRobotConfigDo.UseDB(db, opts...)
	_xRobotConfig.xRobotConfigDo.UseModel(&model.XRobotConfig{})

	tableName := _xRobotConfig.xRobotConfigDo.TableName()
	_xRobotConfig.ALL = field.NewAsterisk(tableName)
	_xRobotConfig.ID = field.NewInt64(tableName, "id")
	_xRobotConfig.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotConfig.SellerName = field.NewString(tableName, "seller_name")
	_xRobotConfig.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotConfig.ChannelName = field.NewString(tableName, "channel_name")
	_xRobotConfig.Name = field.NewString(tableName, "name")
	_xRobotConfig.Token = field.NewString(tableName, "token")
	_xRobotConfig.GameURL = field.NewString(tableName, "game_url")
	_xRobotConfig.IsEnable = field.NewInt32(tableName, "is_enable")
	_xRobotConfig.GroupURL = field.NewString(tableName, "group_url")
	_xRobotConfig.GroupChatID = field.NewInt64(tableName, "group_chat_id")
	_xRobotConfig.GroupRobotName = field.NewString(tableName, "group_robot_name")
	_xRobotConfig.GroupRobotToken = field.NewString(tableName, "group_robot_token")
	_xRobotConfig.IsOpenGroupRobot = field.NewInt32(tableName, "is_open_group_robot")
	_xRobotConfig.IsOpenGift = field.NewInt32(tableName, "is_open_gift")
	_xRobotConfig.IsIPRestriction = field.NewInt32(tableName, "is_ip_restriction")
	_xRobotConfig.IsDeviceRestriction = field.NewInt32(tableName, "is_device_restriction")
	_xRobotConfig.IsWalletRestriction = field.NewInt32(tableName, "is_wallet_restriction")
	_xRobotConfig.IPWhitelist = field.NewString(tableName, "ip_whitelist")
	_xRobotConfig.GiftAmount = field.NewInt32(tableName, "gift_amount")
	_xRobotConfig.GiftCurrency = field.NewString(tableName, "gift_currency")
	_xRobotConfig.RobotType = field.NewInt32(tableName, "robot_type")
	_xRobotConfig.CommonReply = field.NewString(tableName, "common_reply")
	_xRobotConfig.MissionData = field.NewString(tableName, "mission_data")
	_xRobotConfig.MessageData = field.NewString(tableName, "message_data")
	_xRobotConfig.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotConfig.UpdateTime = field.NewTime(tableName, "update_time")
	_xRobotConfig.IsDel = field.NewInt32(tableName, "is_del")

	_xRobotConfig.fillFieldMap()

	return _xRobotConfig
}

type xRobotConfig struct {
	xRobotConfigDo xRobotConfigDo

	ALL                 field.Asterisk
	ID                  field.Int64  // pk
	SellerID            field.Int32  // 运营商id
	SellerName          field.String // 运营名称
	ChannelID           field.Int32  // 渠道id
	ChannelName         field.String // 渠道名称
	Name                field.String // 机器人Username
	Token               field.String // 机器人token
	GameURL             field.String // 游戏链接
	IsEnable            field.Int32  // 是否启用 (1:是 2:否)
	GroupURL            field.String // tg群组链接/频道链接
	GroupChatID         field.Int64  // tg用户在汇报群内的ChatId
	GroupRobotName      field.String // 群组机器人名称
	GroupRobotToken     field.String // 群组机器人token
	IsOpenGroupRobot    field.Int32  // 是否开启入群机器人 0:关闭，1:开启
	IsOpenGift          field.Int32  // 是否开启体验金(1:是 2:否)
	IsIPRestriction     field.Int32  // 是否开启IP限制(1:是 2:否)
	IsDeviceRestriction field.Int32  // 是否开启设备限制(1:是 2:否)
	IsWalletRestriction field.Int32  // 是否开启钱包地址关联限制(1:是 2:否)
	IPWhitelist         field.String // ip白名单(英文逗号分割)
	GiftAmount          field.Int32  // 彩金数量
	GiftCurrency        field.String // 彩金货币
	RobotType           field.Int32  // 机器人类型(1:引客机器人 2:广告机器人)
	CommonReply         field.String // 通用回复模板
	MissionData         field.String // 关联活动模板配置
	MessageData         field.String // 关联消息回复
	CreateTime          field.Time   // 创建时间
	UpdateTime          field.Time   // 修改时间
	IsDel               field.Int32  // 软删除

	fieldMap map[string]field.Expr
}

func (x xRobotConfig) Table(newTableName string) *xRobotConfig {
	x.xRobotConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotConfig) As(alias string) *xRobotConfig {
	x.xRobotConfigDo.DO = *(x.xRobotConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotConfig) updateTableName(table string) *xRobotConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.SellerName = field.NewString(table, "seller_name")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.ChannelName = field.NewString(table, "channel_name")
	x.Name = field.NewString(table, "name")
	x.Token = field.NewString(table, "token")
	x.GameURL = field.NewString(table, "game_url")
	x.IsEnable = field.NewInt32(table, "is_enable")
	x.GroupURL = field.NewString(table, "group_url")
	x.GroupChatID = field.NewInt64(table, "group_chat_id")
	x.GroupRobotName = field.NewString(table, "group_robot_name")
	x.GroupRobotToken = field.NewString(table, "group_robot_token")
	x.IsOpenGroupRobot = field.NewInt32(table, "is_open_group_robot")
	x.IsOpenGift = field.NewInt32(table, "is_open_gift")
	x.IsIPRestriction = field.NewInt32(table, "is_ip_restriction")
	x.IsDeviceRestriction = field.NewInt32(table, "is_device_restriction")
	x.IsWalletRestriction = field.NewInt32(table, "is_wallet_restriction")
	x.IPWhitelist = field.NewString(table, "ip_whitelist")
	x.GiftAmount = field.NewInt32(table, "gift_amount")
	x.GiftCurrency = field.NewString(table, "gift_currency")
	x.RobotType = field.NewInt32(table, "robot_type")
	x.CommonReply = field.NewString(table, "common_reply")
	x.MissionData = field.NewString(table, "mission_data")
	x.MessageData = field.NewString(table, "message_data")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")
	x.IsDel = field.NewInt32(table, "is_del")

	x.fillFieldMap()

	return x
}

func (x *xRobotConfig) WithContext(ctx context.Context) *xRobotConfigDo {
	return x.xRobotConfigDo.WithContext(ctx)
}

func (x xRobotConfig) TableName() string { return x.xRobotConfigDo.TableName() }

func (x xRobotConfig) Alias() string { return x.xRobotConfigDo.Alias() }

func (x xRobotConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotConfigDo.Columns(cols...)
}

func (x *xRobotConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 28)
	x.fieldMap["id"] = x.ID
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["seller_name"] = x.SellerName
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["channel_name"] = x.ChannelName
	x.fieldMap["name"] = x.Name
	x.fieldMap["token"] = x.Token
	x.fieldMap["game_url"] = x.GameURL
	x.fieldMap["is_enable"] = x.IsEnable
	x.fieldMap["group_url"] = x.GroupURL
	x.fieldMap["group_chat_id"] = x.GroupChatID
	x.fieldMap["group_robot_name"] = x.GroupRobotName
	x.fieldMap["group_robot_token"] = x.GroupRobotToken
	x.fieldMap["is_open_group_robot"] = x.IsOpenGroupRobot
	x.fieldMap["is_open_gift"] = x.IsOpenGift
	x.fieldMap["is_ip_restriction"] = x.IsIPRestriction
	x.fieldMap["is_device_restriction"] = x.IsDeviceRestriction
	x.fieldMap["is_wallet_restriction"] = x.IsWalletRestriction
	x.fieldMap["ip_whitelist"] = x.IPWhitelist
	x.fieldMap["gift_amount"] = x.GiftAmount
	x.fieldMap["gift_currency"] = x.GiftCurrency
	x.fieldMap["robot_type"] = x.RobotType
	x.fieldMap["common_reply"] = x.CommonReply
	x.fieldMap["mission_data"] = x.MissionData
	x.fieldMap["message_data"] = x.MessageData
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
	x.fieldMap["is_del"] = x.IsDel
}

func (x xRobotConfig) clone(db *gorm.DB) xRobotConfig {
	x.xRobotConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotConfig) replaceDB(db *gorm.DB) xRobotConfig {
	x.xRobotConfigDo.ReplaceDB(db)
	return x
}

type xRobotConfigDo struct{ gen.DO }

func (x xRobotConfigDo) Debug() *xRobotConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotConfigDo) WithContext(ctx context.Context) *xRobotConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotConfigDo) ReadDB() *xRobotConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotConfigDo) WriteDB() *xRobotConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotConfigDo) Session(config *gorm.Session) *xRobotConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotConfigDo) Clauses(conds ...clause.Expression) *xRobotConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotConfigDo) Returning(value interface{}, columns ...string) *xRobotConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotConfigDo) Not(conds ...gen.Condition) *xRobotConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotConfigDo) Or(conds ...gen.Condition) *xRobotConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotConfigDo) Select(conds ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotConfigDo) Where(conds ...gen.Condition) *xRobotConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotConfigDo) Order(conds ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotConfigDo) Distinct(cols ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotConfigDo) Omit(cols ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotConfigDo) Join(table schema.Tabler, on ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotConfigDo) Group(cols ...field.Expr) *xRobotConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotConfigDo) Having(conds ...gen.Condition) *xRobotConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotConfigDo) Limit(limit int) *xRobotConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotConfigDo) Offset(offset int) *xRobotConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotConfigDo) Unscoped() *xRobotConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotConfigDo) Create(values ...*model.XRobotConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotConfigDo) CreateInBatches(values []*model.XRobotConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotConfigDo) Save(values ...*model.XRobotConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotConfigDo) First() (*model.XRobotConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotConfig), nil
	}
}

func (x xRobotConfigDo) Take() (*model.XRobotConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotConfig), nil
	}
}

func (x xRobotConfigDo) Last() (*model.XRobotConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotConfig), nil
	}
}

func (x xRobotConfigDo) Find() ([]*model.XRobotConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotConfig), err
}

func (x xRobotConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotConfig, err error) {
	buf := make([]*model.XRobotConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotConfigDo) FindInBatches(result *[]*model.XRobotConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotConfigDo) Attrs(attrs ...field.AssignExpr) *xRobotConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotConfigDo) Assign(attrs ...field.AssignExpr) *xRobotConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotConfigDo) Joins(fields ...field.RelationField) *xRobotConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotConfigDo) Preload(fields ...field.RelationField) *xRobotConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotConfigDo) FirstOrInit() (*model.XRobotConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotConfig), nil
	}
}

func (x xRobotConfigDo) FirstOrCreate() (*model.XRobotConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotConfig), nil
	}
}

func (x xRobotConfigDo) FindByPage(offset int, limit int) (result []*model.XRobotConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotConfigDo) Delete(models ...*model.XRobotConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotConfigDo) withDO(do gen.Dao) *xRobotConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
