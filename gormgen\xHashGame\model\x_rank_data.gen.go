// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRankDatum = "x_rank_data"

// XRankDatum mapped from table <x_rank_data>
type XRankDatum struct {
	SellerID int32 `gorm:"column:SellerId;primaryKey;comment:运营商" json:"SellerId"` // 运营商
	/*
		1 中奖日排行,2 中奖周排行,3 中奖月排行,4 中奖实时排行
		11 下注日排行,12 下注周排行,13 下注月排行,14 下注实时排行 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行
	*/
	RankType     int32     `gorm:"column:RankType;primaryKey;comment:1 中奖日排行,2 中奖周排行,3 中奖月排行,4 中奖实时排行 \n11 下注日排行,12 下注周排行,13 下注月排行,14 下注实时排行 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行" json:"RankType"`
	Rank         int32     `gorm:"column:Rank;primaryKey;comment:名次" json:"Rank"`                                    // 名次
	GameType     int32     `gorm:"column:GameType;not null;comment:1哈希游戏 100三方游戏" json:"GameType"`                   // 1哈希游戏 100三方游戏
	Symbol       string    `gorm:"column:Symbol;not null;comment:币种" json:"Symbol"`                                  // 币种
	UserID       string    `gorm:"column:UserId;comment:玩家id" json:"UserId"`                                         // 玩家id
	VipLevel     int32     `gorm:"column:VipLevel;comment:Vip等级" json:"VipLevel"`                                    // Vip等级
	ChainType    int32     `gorm:"column:ChainType;not null;default:1;comment:网链分类 1trc 2erc 3bsc" json:"ChainType"` // 网链分类 1trc 2erc 3bsc
	Brand        string    `gorm:"column:Brand;comment:游戏厂商" json:"Brand"`                                           // 游戏厂商
	GameID       string    `gorm:"column:GameId;comment:游戏id" json:"GameId"`                                         // 游戏id
	Address      string    `gorm:"column:Address;comment:投注地址" json:"Address"`                                       // 投注地址
	Amount       float64   `gorm:"column:Amount;default:0.000000" json:"Amount"`
	RewardRate   float64   `gorm:"column:RewardRate;default:0.000000" json:"RewardRate"`
	RewardAmount float64   `gorm:"column:RewardAmount;default:0.000000" json:"RewardAmount"`
	BetTime      time.Time `gorm:"column:BetTime;comment:下注时间" json:"BetTime"`                                          // 下注时间
	RewardTime   time.Time `gorm:"column:RewardTime;comment:结算时间" json:"RewardTime"`                                    // 结算时间
	CreateTime   time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
}

// TableName XRankDatum's table name
func (*XRankDatum) TableName() string {
	return TableNameXRankDatum
}
