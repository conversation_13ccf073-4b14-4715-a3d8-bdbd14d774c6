// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserDailly = "x_user_dailly"

// XUserDailly mapped from table <x_user_dailly>
type XUserDailly struct {
	ChannelID                   int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	UserID                      int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	RecordDate                  time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	SellerID                    int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	BetTrx                      float64   `gorm:"column:BetTrx;default:0.000000;comment:Trx下注" json:"BetTrx"`                                                      // Trx下注
	BonusBetTrx                 float64   `gorm:"column:BonusBetTrx;default:0.000000;comment:Bonus-Trx下注" json:"BonusBetTrx"`                                      // Bonus-Trx下注
	RewardTrx                   float64   `gorm:"column:RewardTrx;default:0.000000;comment:trx返奖" json:"RewardTrx"`                                                // trx返奖
	BonusRewardTrx              float64   `gorm:"column:BonusRewardTrx;default:0.000000;comment:Bonus-trx返奖" json:"BonusRewardTrx"`                                // Bonus-trx返奖
	LiuSuiTrx                   float64   `gorm:"column:LiuSuiTrx;default:0.000000;comment:trx流水" json:"LiuSuiTrx"`                                                // trx流水
	BetUsdt                     float64   `gorm:"column:BetUsdt;default:0.000000;comment:usdt下注" json:"BetUsdt"`                                                   // usdt下注
	BonusBetUsdt                float64   `gorm:"column:BonusBetUsdt;default:0.000000;comment:Bonus-usdt下注" json:"BonusBetUsdt"`                                   // Bonus-usdt下注
	RewardUsdt                  float64   `gorm:"column:RewardUsdt;default:0.000000;comment:usdt返奖" json:"RewardUsdt"`                                             // usdt返奖
	BonusRewardUsdt             float64   `gorm:"column:BonusRewardUsdt;default:0.000000;comment:Bonus-usdt返奖" json:"BonusRewardUsdt"`                             // Bonus-usdt返奖
	LiuSuiUsdt                  float64   `gorm:"column:LiuSuiUsdt;default:0.000000;comment:usdt流水" json:"LiuSuiUsdt"`                                             // usdt流水
	HaXiRouletteBetTrx          float64   `gorm:"column:HaXiRouletteBetTrx;default:0.000000;comment:哈希轮盘下注Trx" json:"HaXiRouletteBetTrx"`                          // 哈希轮盘下注Trx
	HaXiRouletteBonusBetTrx     float64   `gorm:"column:HaXiRouletteBonusBetTrx;default:0.000000;comment:哈希轮盘下注Bonus-Trx" json:"HaXiRouletteBonusBetTrx"`          // 哈希轮盘下注Bonus-Trx
	HaXiRouletteRewardTrx       float64   `gorm:"column:HaXiRouletteRewardTrx;default:0.000000;comment:哈希轮盘返奖Trx" json:"HaXiRouletteRewardTrx"`                    // 哈希轮盘返奖Trx
	HaXiRouletteBonusRewardTrx  float64   `gorm:"column:HaXiRouletteBonusRewardTrx;default:0.000000;comment:哈希轮盘返奖Bonus-Trx" json:"HaXiRouletteBonusRewardTrx"`    // 哈希轮盘返奖Bonus-Trx
	HaXiRouletteLiuSuiTrx       float64   `gorm:"column:HaXiRouletteLiuSuiTrx;default:0.000000;comment:哈希轮盘流水Trx" json:"HaXiRouletteLiuSuiTrx"`                    // 哈希轮盘流水Trx
	HaXiRouletteBetUsdt         float64   `gorm:"column:HaXiRouletteBetUsdt;default:0.000000;comment:哈希轮盘下注Usdt" json:"HaXiRouletteBetUsdt"`                       // 哈希轮盘下注Usdt
	HaXiRouletteBonusBetUsdt    float64   `gorm:"column:HaXiRouletteBonusBetUsdt;default:0.000000;comment:哈希轮盘下注Bonus-Usdt" json:"HaXiRouletteBonusBetUsdt"`       // 哈希轮盘下注Bonus-Usdt
	HaXiRouletteRewardUsdt      float64   `gorm:"column:HaXiRouletteRewardUsdt;default:0.000000;comment:哈希轮盘返奖Usdt" json:"HaXiRouletteRewardUsdt"`                 // 哈希轮盘返奖Usdt
	HaXiRouletteBonusRewardUsdt float64   `gorm:"column:HaXiRouletteBonusRewardUsdt;default:0.000000;comment:哈希轮盘返奖Bonus-Usdt" json:"HaXiRouletteBonusRewardUsdt"` // 哈希轮盘返奖Bonus-Usdt
	HaXiRouletteLiuSuiUsdt      float64   `gorm:"column:HaXiRouletteLiuSuiUsdt;default:0.000000;comment:哈希轮盘流水Usdt" json:"HaXiRouletteLiuSuiUsdt"`                 // 哈希轮盘流水Usdt
	LiuSuiLottery               float64   `gorm:"column:LiuSuiLottery;default:0.000000;comment:彩票流水" json:"LiuSuiLottery"`                                         // 彩票流水
	LiuSuiLowLottery            float64   `gorm:"column:LiuSuiLowLottery;default:0.000000;comment:低频彩流水" json:"LiuSuiLowLottery"`                                  // 低频彩流水
	LiuSuiLiuHeLottery          float64   `gorm:"column:LiuSuiLiuHeLottery;default:0.000000;comment:六合彩流水" json:"LiuSuiLiuHeLottery"`                              // 六合彩流水
	LiuSuiQiPai                 float64   `gorm:"column:LiuSuiQiPai;default:0.000000;comment:棋牌流水" json:"LiuSuiQiPai"`                                             // 棋牌流水
	LiuSuiDianZhi               float64   `gorm:"column:LiuSuiDianZhi;default:0.000000;comment:电子流水" json:"LiuSuiDianZhi"`                                         // 电子流水
	LiuSuiXiaoYouXi             float64   `gorm:"column:LiuSuiXiaoYouXi;default:0.000000;comment:小游戏流水" json:"LiuSuiXiaoYouXi"`                                    // 小游戏流水
	LiuSuiCryptoMarket          float64   `gorm:"column:LiuSuiCryptoMarket;default:0.000000;comment:低频彩流水" json:"LiuSuiCryptoMarket"`                              // 低频彩流水
	LiuSuiLive                  float64   `gorm:"column:LiuSuiLive;default:0.000000;comment:真人流水" json:"LiuSuiLive"`                                               // 真人流水
	LiuSuiSport                 float64   `gorm:"column:LiuSuiSport;default:0.000000;comment:体育流水" json:"LiuSuiSport"`                                             // 体育流水
	LiuSuiTexas                 float64   `gorm:"column:LiuSuiTexas;default:0.000000;comment:德州流水" json:"LiuSuiTexas"`                                             // 德州流水
	FineLiuSuiTrx               float64   `gorm:"column:FineLiuSuiTrx;default:0.000000;comment:扣除流水trx" json:"FineLiuSuiTrx"`                                      // 扣除流水trx
	FineLiuSuiUsdt              float64   `gorm:"column:FineLiuSuiUsdt;default:0.000000;comment:扣除流水Usdt" json:"FineLiuSuiUsdt"`                                   // 扣除流水Usdt
	FineAccount                 string    `gorm:"column:FineAccount;comment:扣除流水操作人" json:"FineAccount"`                                                           // 扣除流水操作人
	FineTime                    time.Time `gorm:"column:FineTime;comment:扣除流水时间" json:"FineTime"`                                                                  // 扣除流水时间
	FineMemo                    string    `gorm:"column:FineMemo" json:"FineMemo"`
	BetCountTrx                 int32     `gorm:"column:BetCountTrx" json:"BetCountTrx"`
	BetCountUsdt                int32     `gorm:"column:BetCountUsdt" json:"BetCountUsdt"`
	TotalWinLoss                float64   `gorm:"column:TotalWinLoss;default:0.000000;comment:今日输赢总额(包含所有游戏) 赢为正 输为负" json:"TotalWinLoss"` // 今日输赢总额(包含所有游戏) 赢为正 输为负
	TotalLiuSui                 float64   `gorm:"column:TotalLiuSui;default:0.000000;comment:今日总流水usdt+trx" json:"TotalLiuSui"`            // 今日总流水usdt+trx
	TrxRate                     float64   `gorm:"column:TrxRate;default:0.000000;comment:今日trx汇率" json:"TrxRate"`                          // 今日trx汇率
	TotalCaiJin                 float64   `gorm:"column:TotalCaiJin;default:0.000000;comment:今日赠送彩金总额Usdt" json:"TotalCaiJin"`             // 今日赠送彩金总额Usdt
	TotalCaiJinTrx              float64   `gorm:"column:TotalCaiJinTrx;default:0.000000;comment:今日赠送彩金总额Trx" json:"TotalCaiJinTrx"`        // 今日赠送彩金总额Trx
}

// TableName XUserDailly's table name
func (*XUserDailly) TableName() string {
	return TableNameXUserDailly
}
