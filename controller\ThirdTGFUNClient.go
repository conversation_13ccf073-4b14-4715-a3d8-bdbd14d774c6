package controller

import (
	"bytes"
	"crypto/aes"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	daogorm "gorm.io/gorm"

	"github.com/beego/beego/logs"
	"github.com/spf13/cast"
)

// TGFUNClient 是TGFUN游戏API客户端
type TGFUNClient struct {
	apiURL        string // API基础URL
	appID         string // 应用ID
	secret        string // 应用密钥
	aesKey        string // AES加密密钥
	currency      string // 默认货币类型
	gameUUID      string // 游戏UUID
	accessToken   string // 访问令牌
	tokenExpireAt int64  // 令牌过期时间

	thirdController *ThirdController // 第三方控制器引用
}

// TGFUN API响应结构
type TGFUNResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// TGFUN Token响应结构
type TGFUNTokenData struct {
	Result      string `json:"result"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
}

// TGFUN 余额响应结构
type TGFUNBalanceData struct {
	Result   string `json:"result"`
	PlayerID string `json:"playerID"`
	Balance  string `json:"balance"`
	Currency string `json:"currency"`
}

// TGFUN 转账响应结构
type TGFUNTransferData struct {
	Result    string `json:"result"`
	Balance   string `json:"balance,omitempty"`
	OutAmount string `json:"outAmount,omitempty"`
	TransID   int64  `json:"transID"`
}

// TGFUN 获取游戏URL响应结构
type TGFUNGameURLData struct {
	Result string `json:"result"`
	URL    string `json:"url"`
}

// NewTGFUNClient 创建一个新的TGFUN客户端
func NewTGFUNClient(config map[string]string, controller *ThirdController) *TGFUNClient {
	client := &TGFUNClient{
		apiURL:          cast.ToString(config["api_url"]),
		appID:           cast.ToString(config["app_id"]),
		secret:          cast.ToString(config["secret"]),
		aesKey:          cast.ToString(config["aes_key"]),
		currency:        cast.ToString(config["currency"]),
		thirdController: controller,
	}
	return client
}

// refreshToken 刷新访问令牌
func (c *TGFUNClient) refreshToken() error {
	// 如果token未过期，则不刷新
	if c.accessToken != "" && c.tokenExpireAt > time.Now().Unix() {
		return nil
	}

	// 构造请求参数
	params := map[string]string{
		"appid":  c.appID,
		"secret": c.secret,
	}

	// 发送请求获取token
	url := fmt.Sprintf("%s/openapi/v3/game/token", c.apiURL)

	// 将参数转为JSON
	jsonData, err := json.Marshal(params)
	if err != nil {
		logs.Error("tgfun_refresh_token json marshal error:", err)
		return err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logs.Error("tgfun_refresh_token create request error:", err)
		return err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("tgfun_refresh_token request error:", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("tgfun_refresh_token read response error:", err)
		return err
	}

	// 解析响应
	var response TGFUNResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		logs.Error("tgfun_refresh_token parse response error:", err)
		return err
	}

	// 检查响应状态
	if response.Code != "000000" {
		logs.Error("tgfun_refresh_token failed, code:", response.Code, "message:", response.Message)
		return fmt.Errorf("获取token失败: %s", response.Message)
	}

	// 解密数据，使用公共方法
	encryptedData := cast.ToString(response.Data)
	decryptedData, err := c.DecryptData(encryptedData)
	if err != nil {
		logs.Error("tgfun_refresh_token decrypt data error:", err)
		return err
	}

	// 解析解密后的数据
	var tokenData TGFUNTokenData
	err = json.Unmarshal([]byte(decryptedData), &tokenData)
	if err != nil {
		logs.Error("tgfun_refresh_token parse decrypted data error:", err)
		return err
	}

	// 保存token信息
	c.accessToken = tokenData.AccessToken

	// 如果ExpiresIn为-1，则设置为长期有效（1年）
	if tokenData.ExpiresIn == -1 {
		c.tokenExpireAt = time.Now().Unix() + 365*24*60*60
	} else {
		c.tokenExpireAt = time.Now().Unix() + tokenData.ExpiresIn
	}

	logs.Info("tgfun_refresh_token success, token:", c.accessToken)
	return nil
}

// encryptAES 使用AES加密数据（ECB模式）
func (c *TGFUNClient) encryptAES(plaintext string) (string, error) {
	// 创建AES加密器
	block, err := aes.NewCipher([]byte(c.aesKey))
	if err != nil {
		return "", err
	}

	// 对明文进行填充
	plainBytes := []byte(plaintext)
	blockSize := block.BlockSize()
	padding := blockSize - len(plainBytes)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	plainBytes = append(plainBytes, padtext...)

	// 加密
	ciphertext := make([]byte, len(plainBytes))

	// ECB模式加密
	for bs, be := 0, blockSize; bs < len(plainBytes); bs, be = bs+blockSize, be+blockSize {
		block.Encrypt(ciphertext[bs:be], plainBytes[bs:be])
	}

	// 返回十六进制编码的密文
	return hex.EncodeToString(ciphertext), nil
}

// decryptAES 使用AES解密数据（ECB模式）
func (c *TGFUNClient) decryptAES(ciphertext string) (string, error) {
	// 解码十六进制密文
	cipherBytes, err := hex.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建AES解密器
	block, err := aes.NewCipher([]byte(c.aesKey))
	if err != nil {
		return "", err
	}

	// 解密
	plaintext := make([]byte, len(cipherBytes))
	blockSize := block.BlockSize()

	// ECB模式解密
	for bs, be := 0, blockSize; bs < len(cipherBytes); bs, be = bs+blockSize, be+blockSize {
		block.Decrypt(plaintext[bs:be], cipherBytes[bs:be])
	}

	// 去除填充
	padding := int(plaintext[len(plaintext)-1])
	if padding < 1 || padding > blockSize {
		return "", fmt.Errorf("invalid padding size: %d", padding)
	}

	// 检查填充是否有效
	for i := len(plaintext) - padding; i < len(plaintext); i++ {
		if plaintext[i] != byte(padding) {
			return "", fmt.Errorf("invalid padding")
		}
	}

	// 返回去除填充后的明文
	return string(plaintext[:len(plaintext)-padding]), nil
}

// tgfunHttpPost 发送HTTP POST请求到TGFUN API
func (c *TGFUNClient) tgfunHttpPost(endpoint string, data interface{}) (interface{}, error) {
	// 刷新token
	err := c.refreshToken()
	if err != nil {
		return nil, err
	}

	// 将数据转为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		logs.Error("tgfun_http_post json marshal error:", err)
		return nil, err
	}

	// 加密数据，使用公共方法
	encryptedData, err := c.EncryptData(string(jsonData))
	if err != nil {
		logs.Error("tgfun_http_post encrypt data error:", err)
		return nil, err
	}

	// 构造请求体
	requestData := map[string]string{
		"data": encryptedData,
	}

	// 将请求体转为JSON
	requestBody, err := json.Marshal(requestData)
	if err != nil {
		logs.Error("tgfun_http_post json marshal request body error:", err)
		return nil, err
	}

	// 构造请求URL
	url := fmt.Sprintf("%s/openapi/v3/game/%s", c.apiURL, endpoint)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		logs.Error("tgfun_http_post create request error:", err)
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", c.accessToken)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("tgfun_http_post request error:", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("tgfun_http_post read response error:", err)
		return nil, err
	}

	// 解析响应
	var response TGFUNResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		logs.Error("tgfun_http_post parse response error:", err)
		return nil, err
	}

	// 检查响应状态
	if response.Code != "000000" {
		logs.Error("tgfun_http_post failed, code:", response.Code, "message:", response.Message)
		return nil, fmt.Errorf("请求失败: %s", response.Message)
	}

	// 解密数据，使用公共方法
	encryptedResponseData := cast.ToString(response.Data)
	decryptedData, err := c.DecryptData(encryptedResponseData)
	if err != nil {
		logs.Error("tgfun_http_post decrypt data error:", err)
		return nil, err
	}
	logs.Info("tfun API请求URL:", url, " req=", string(jsonData), " res=", decryptedData)
	// 解析解密后的数据
	var result interface{}
	err = json.Unmarshal([]byte(decryptedData), &result)
	if err != nil {
		logs.Error("tgfun_http_post parse decrypted data error:", err)
		return nil, err
	}

	return result, nil
}

// TransferDeposit 从平台转出（对应API文档中的"玩家游戏上分"），返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *TGFUNClient) TransferDeposit(userId int, amount float64, orderId string) (int, error) {
	logs.Info("tgfun_transfer_deposit 参数 userId:", userId, "amount:", amount, "orderId:", orderId)

	// 构造请求参数
	params := map[string]interface{}{
		"payID":    orderId,
		"playerID": fmt.Sprintf("%d", userId),
		"coin":     fmt.Sprintf("%f", amount),
		"currency": c.currency,
	}

	// 发送上分请求
	result, err := c.tgfunHttpPost("transfer/in", params)
	if err != nil {
		logs.Error("tgfun_transfer_deposit request failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("转账失败: %v", err) // 网络错误或其它错误
	}

	// 将结果转换为map
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		logs.Error("tgfun_transfer_deposit invalid result type")
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("转账失败: 响应格式错误") // 网络错误或其它错误
	}

	// 解析响应
	transferData := TGFUNTransferData{
		Result:  cast.ToString(resultMap["result"]),
		Balance: cast.ToString(resultMap["balance"]),
		TransID: cast.ToInt64(resultMap["transID"]),
	}

	// 检查结果
	if transferData.Result != "SUCCESS" {
		logs.Error("tgfun_transfer_deposit failed, result:", transferData.Result)
		return TRANSFER_STATUS_FAILED, fmt.Errorf("转账失败: 结果为 %s", transferData.Result) // 失败
	}

	logs.Info("tgfun_transfer_deposit success, balance:", transferData.Balance, "transID:", transferData.TransID)
	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

// TransferWithdraw 转入到平台（对应API文档中的"游戏玩家下分"），返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *TGFUNClient) TransferWithdraw(userId int, amount float64, orderId string) (int, error) {
	logs.Info("tgfun_transfer_withdraw 参数 userId:", userId, "amount:", amount, "orderId:", orderId)

	// 构造请求参数
	params := map[string]interface{}{
		"payID":    orderId,
		"playerID": fmt.Sprintf("%d", userId),
		"coin":     fmt.Sprintf("%f", amount),
		"outall":   "false",
		"currency": c.currency,
	}

	// 如果金额为0，则设置outall为true，表示全部下分
	if amount == 0 {
		params["outall"] = "true"
	}

	// 发送下分请求
	result, err := c.tgfunHttpPost("transfer/out", params)
	if err != nil {
		logs.Error("tgfun_transfer_withdraw request failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("转账失败: %v", err) // 网络错误或其它错误
	}

	// 将结果转换为map
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		logs.Error("tgfun_transfer_withdraw invalid result type")
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("转账失败: 响应格式错误") // 网络错误或其它错误
	}

	// 解析响应
	transferData := TGFUNTransferData{
		Result:    cast.ToString(resultMap["result"]),
		OutAmount: cast.ToString(resultMap["outAmount"]),
		Balance:   cast.ToString(resultMap["balance"]),
		TransID:   cast.ToInt64(resultMap["transID"]),
	}

	// 检查结果
	if transferData.Result != "SUCCESS" {
		logs.Error("tgfun_transfer_withdraw failed, result:", transferData.Result)
		return TRANSFER_STATUS_FAILED, fmt.Errorf("转账失败: 结果为 %s", transferData.Result) // 失败
	}

	logs.Info("tgfun_transfer_withdraw success, outAmount:", transferData.OutAmount, "balance:", transferData.Balance, "transID:", transferData.TransID)
	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

// GetBalance 获取用户在平台上的余额
func (c *TGFUNClient) GetBalance(userId int) (float64, error) {
	logs.Info("tgfun_get_balance 参数 userId:", userId)

	// 构造请求参数
	params := map[string]interface{}{
		"playerID": fmt.Sprintf("%d", userId),
	}

	// 发送查询余额请求
	result, err := c.tgfunHttpPost("player/balance", params)
	if err != nil {
		logs.Error("tgfun_get_balance request failed:", err)
		return 0, fmt.Errorf("查询余额失败: %v", err)
	}

	// 将结果转换为map
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		logs.Error("tgfun_get_balance invalid result type")
		return 0, fmt.Errorf("查询余额失败: 响应格式错误")
	}

	// 解析响应
	balanceData := TGFUNBalanceData{
		Result:   cast.ToString(resultMap["result"]),
		PlayerID: cast.ToString(resultMap["playerID"]),
		Balance:  cast.ToString(resultMap["balance"]),
		Currency: cast.ToString(resultMap["currency"]),
	}

	// 检查结果
	if balanceData.Result != "SUCCESS" {
		logs.Error("tgfun_get_balance failed, result:", balanceData.Result)
		return 0, fmt.Errorf("查询余额失败: 结果为 %s", balanceData.Result)
	}

	// 转换余额为float64
	balance, err := strconv.ParseFloat(balanceData.Balance, 64)
	if err != nil {
		logs.Error("tgfun_get_balance parse balance error:", err)
		return 0, fmt.Errorf("解析余额失败: %v", err)
	}

	logs.Info("tgfun_get_balance success, balance:", balance)
	return balance, nil
}

// GetLoginUrl 获取登录网址（对应API文档中的"玩家注册"和"获取游戏地址"组合实现）
func (c *TGFUNClient) GetLoginUrl(ctx *abugo.AbuHttpContent) {
	// 获取游戏类型和ID
	type RequestData struct {
		GameId   string `validate:"required"`
		LangCode string `validate:"required"`
		Ip       string `json:"ip"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("tgfun登录失败")
		ctx.RespErr(err, &errcode)
		return
	}

	// 获取用户token
	token := server.GetToken(ctx)

	//三方全局并发锁-
	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	//tgfun单独锁 确保2秒中只有一次登录请求
	rediskey2 := fmt.Sprintf("%v:%v:third_tgfun_%v", server.Project(), server.Module(), token.UserId)
	lck2 := server.Redis().SetNxString(rediskey2, "1", 2)
	if lck2 != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	//defer server.Redis().Del(rediskey)

	// 验证用户
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	user, _ := server.Db().Table("x_user").Select("State").Where(where).GetOne()
	if user == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	state := abugo.GetInt64FromInterface((*user)["State"])
	if state != 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	brand := "tgfun"
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", brand, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error(brand, " 登录游戏 查询游戏错误 userId=", token.UserId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error(brand, " 登录游戏 游戏不可用 userId=", token.UserId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	// 初始化时获取一次token
	c.refreshToken()

	// 同步余额
	err = c.thirdController.sync_amountex(token.UserId, TRANSFER_PLATFORM_TGFUN)
	if err != nil {
		logs.Error("tgfun_login 同步余额失败", err)
	}

	// 第一步：注册玩家账号
	// 获取用户信息
	userInfo, _ := server.Db().Table("x_user").Select("NickName").Where(where).GetOne()
	if userInfo == nil {
		ctx.RespErrString(true, &errcode, "获取用户信息失败")
		return
	}
	nickName := cast.ToString((*userInfo)["NickName"])
	if nickName == "" {
		nickName = fmt.Sprintf("Player%d", token.UserId)
	}

	// 构造注册参数
	registerParams := map[string]interface{}{
		"playerID":   fmt.Sprintf("%d", token.UserId),
		"playerName": nickName,
		"currency":   c.currency,
	}

	// 发送注册请求 三方缺少检查用户是否已经注册接口 故每次调用接口先尝试一遍注册
	_, err = c.tgfunHttpPost("create/player", registerParams)
	if err != nil {
		// 如果是玩家已注册的错误，使用Info级别日志，否则使用Warning
		if strings.Contains(err.Error(), "was registered") {
			logs.Info("tgfun_login player already registered:")
		} else {
			logs.Warning("tgfun_login register player failed:", err)
		}
		// 注册失败可能是因为玩家已存在，继续尝试获取游戏地址
	}

	// 第二步：获取游戏地址
	// 构造请求参数
	gameParams := map[string]interface{}{
		"playerID": fmt.Sprintf("%d", token.UserId),
		"gameUUID": reqdata.GameId,
		"gameLang": reqdata.LangCode,
	}

	// 发送获取游戏地址请求
	result, err := c.tgfunHttpPost("create/url", gameParams)
	if err != nil {
		logs.Error("tgfun_login get game url failed:", err)

		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	// 将结果转换为map
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		logs.Error("tgfun_login invalid result type")
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	// 解析响应
	gameURLData := TGFUNGameURLData{
		Result: cast.ToString(resultMap["result"]),
		URL:    cast.ToString(resultMap["url"]),
	}

	// 检查结果
	if gameURLData.Result != "SUCCESS" {
		logs.Error("tgfun_login failed, result:", gameURLData.Result)
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	if gameURLData.URL == "" {
		logs.Error("tgfun_login no game URL returned")
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	// 登录成功后执行转账
	err = c.thirdController.third_transfer_out(TRANSFER_PLATFORM_TGFUN, token.UserId, "")
	if err != nil {
		logs.Error("tgfun_login 同步余额失败", err)
		ctx.RespErrString(true, &errcode, "进入失败 "+err.Error())
		return
	}

	// 返回游戏URL
	ctx.Put("url", gameURLData.URL)
	ctx.RespOK()
}

// QueryOrder 查询订单状态，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *TGFUNClient) QueryOrder(orderId string, userId int) (int, error) {

	txType := "查询订单"
	logs.Info(fmt.Sprintf("[tgfun%s] 开始处理 - 用户ID: %d, 订单号: %s", txType, userId, orderId))

	// 参数验证
	if orderId == "" {
		logs.Error(fmt.Sprintf("[tgfun%s] 参数错误 - 用户ID: %d, 订单号不能为空", txType, userId))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("订单号不能为空") // 失败
	}

	// 构造请求参数，根据API文档
	params := map[string]interface{}{
		"page":     1,
		"size":     10,
		"type":     9,       // 查询全部类型的记录（同时含上分和下分记录）
		"payID":    orderId, // 使用保存的交易ID
		"playerID": fmt.Sprintf("%d", userId),
	}

	// 发送查询请求，根据API文档应该是transfer/record/list接口
	result, err := c.tgfunHttpPost("transfer/record/list", params)
	if err != nil {
		logs.Error(fmt.Sprintf("[tgfun%s] 请求失败 - 用户ID: %d, 订单号: %s, 错误: %v", txType, userId, orderId, err))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("查询订单失败: %v", err) // 网络错误或其它错误
	}

	// 解析响应
	response, ok := result.(map[string]interface{})
	if !ok {
		logs.Error(fmt.Sprintf("[tgfun%s] 响应格式错误 - 用户ID: %d, 订单号: %s", txType, userId, orderId))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("响应格式错误")
	}

	// 检查响应码
	//code, _ := response["code"].(string)
	//if code != "000000" {
	//	message, _ := response["message"].(string)
	//	logs.Error(fmt.Sprintf("[tgfun%s] 查询失败 - 用户ID: %d, 订单号: %s, 错误码: %s, 错误信息: %s",
	//		txType, userId, orderId, code, message))
	//	return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("查询失败: %s - %s", code, message)
	//}

	// 获取数据
	total, _ := response["data"].(string)
	if !ok {
		logs.Error(fmt.Sprintf("[tgfun%s] 数据格式错误 - 用户ID: %d, 订单号: %s", txType, userId, orderId))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("数据格式错误")
	}

	// 获取内容
	content, ok := response["content"].([]interface{})
	if !ok || len(content) == 0 {
		logs.Warning(fmt.Sprintf("[tgfun%s] 未找到订单记录 - 用户ID: %d, 订单号: %s", txType, userId, orderId))
		return TRANSFER_STATUS_FAILED, fmt.Errorf("未找到订单记录")
	}

	// 检查第一条记录
	record, ok := content[0].(map[string]interface{})
	if !ok {
		logs.Error(fmt.Sprintf("[tgfun%s] 记录格式错误 - 用户ID: %d, 订单号: %s", txType, userId, orderId))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("记录格式错误")
	}

	// 获取记录信息
	payID, _ := record["payID"].(string)
	transferType, _ := record["transferType"].(float64)
	transferAmount, _ := record["transferAmount"].(string)
	transferBalance, _ := record["transferBalance"].(string)
	transferTime, _ := record["transferTime"].(string)

	// 记录查询结果
	logs.Info(fmt.Sprintf("[tgfun%s] 查询成功 - 用户ID: %d, 订单号: %s, 交易ID: %s, 类型: %.0f, 金额: %s, 余额: %s, 时间: %s, 记录数: %s",
		txType, userId, orderId, payID, transferType, transferAmount, transferBalance, transferTime, total))

	// 订单存在且成功
	return TRANSFER_STATUS_SUCCESS, nil
}

// EncryptData 公共方法，用于加密数据
func (c *TGFUNClient) EncryptData(plaintext string) (string, error) {
	return c.encryptAES(plaintext)
}

// DecryptData 公共方法，用于解密数据
func (c *TGFUNClient) DecryptData(ciphertext string) (string, error) {
	return c.decryptAES(ciphertext)
}
