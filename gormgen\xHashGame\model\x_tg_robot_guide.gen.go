// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRobotGuide = "x_tg_robot_guide"

// XTgRobotGuide mapped from table <x_tg_robot_guide>
type XTgRobotGuide struct {
	ID                  int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID            int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                                                                       // 运营商id
	ChannelID           int32     `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`                                                                                      // 渠道id
	TgRobotUserName     string    `gorm:"column:TgRobotUserName;not null;comment:机器人Username" json:"TgRobotUserName"`                                                          // 机器人Username
	TgRobotToken        string    `gorm:"column:TgRobotToken;not null;comment:机器人token" json:"TgRobotToken"`                                                                   // 机器人token
	TgAccountID         int32     `gorm:"column:TgAccountId;comment:x_tg_account.Id" json:"TgAccountId"`                                                                       // x_tg_account.Id
	KefuTgUserName      string    `gorm:"column:KefuTgUserName;not null;comment:客服tg号" json:"KefuTgUserName"`                                                                  // 客服tg号
	RegGift             float64   `gorm:"column:RegGift;default:0.000000;comment:注册彩金(废弃)" json:"RegGift"`                                                                     // 注册彩金(废弃)
	GameURL             string    `gorm:"column:GameUrl;comment:游戏链接" json:"GameUrl"`                                                                                          // 游戏链接
	Welcome             string    `gorm:"column:Welcome;comment:欢迎文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"Welcome"`                       // 欢迎文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	Welcome2            string    `gorm:"column:Welcome2;comment:欢迎文案2 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"Welcome2"`                    // 欢迎文案2 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	BindMsg             string    `gorm:"column:BindMsg;comment:绑定钱包地址提示文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"BindMsg"`                 // 绑定钱包地址提示文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	GuideMsg            string    `gorm:"column:GuideMsg;comment:未识别消息的指引文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"GuideMsg"`               // 未识别消息的指引文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	FirstChargeActive   string    `gorm:"column:FirstChargeActive;comment:首充活动文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"FirstChargeActive"` // 首充活动文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	FuLiMsg             string    `gorm:"column:FuLiMsg;comment:福利文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"FuLiMsg"`                       // 福利文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	LianxiKefuMsg       string    `gorm:"column:LianxiKefuMsg;comment:联系客服文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}" json:"LianxiKefuMsg"`         // 联系客服文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	LianxiKefuInterval  int32     `gorm:"column:LianxiKefuInterval;comment:联系客服间隔时间（注册经过多少分钟未申请体验金的用户，机器人主动发送客服联系方式）" json:"LianxiKefuInterval"`                               // 联系客服间隔时间（注册经过多少分钟未申请体验金的用户，机器人主动发送客服联系方式）
	CommonReply         string    `gorm:"column:CommonReply;comment:通用回复模板" json:"CommonReply"`                                                                                // 通用回复模板
	IsEnable            int32     `gorm:"column:IsEnable;comment:是否启用 (1:是 2:否)" json:"IsEnable"`                                                                              // 是否启用 (1:是 2:否)
	H5KefuPopText       string    `gorm:"column:H5KefuPopText;comment:H5客服弹窗文案" json:"H5KefuPopText"`                                                                          // H5客服弹窗文案
	H5KefuLink          string    `gorm:"column:H5KefuLink;comment:H5客服弹窗按钮链接" json:"H5KefuLink"`                                                                              // H5客服弹窗按钮链接
	GroupChatID         int64     `gorm:"column:GroupChatId;comment:tg用户在汇报群内的ChatId" json:"GroupChatId"`                                                                      // tg用户在汇报群内的ChatId
	IsTiyanjin          int32     `gorm:"column:IsTiyanjin;default:1;comment:是否开启体验金(1:是 2:否)" json:"IsTiyanjin"`                                                              // 是否开启体验金(1:是 2:否)
	IsIPRestriction     int32     `gorm:"column:IsIPRestriction;default:1;comment:是否开启IP限制(1:是 2:否)" json:"IsIPRestriction"`                                                   // 是否开启IP限制(1:是 2:否)
	IsDeviceRestriction int32     `gorm:"column:IsDeviceRestriction;default:2;comment:是否开启设备限制(1:是 2:否)" json:"IsDeviceRestriction"`                                           // 是否开启设备限制(1:是 2:否)
	IsWalletRestriction int32     `gorm:"column:IsWalletRestriction;default:2;comment:是否开启钱包地址关联限制(1:是 2:否)" json:"IsWalletRestriction"`                                       // 是否开启钱包地址关联限制(1:是 2:否)
	IsAotoTyj2          int32     `gorm:"column:IsAotoTyj2;default:2;comment:是否自动发放第二笔体验金(1:是 2:否)" json:"IsAotoTyj2"`                                                         // 是否自动发放第二笔体验金(1:是 2:否)
	IPWhitelist         string    `gorm:"column:IPWhitelist;comment:ip白名单(英文逗号分割)" json:"IPWhitelist"`                                                                         // ip白名单(英文逗号分割)
	TgRobotType         int32     `gorm:"column:TgRobotType;default:1;comment:机器人类型(1:引客机器人 2:英文接待机器人)" json:"TgRobotType"`                                                    // 机器人类型(1:引客机器人 2:英文接待机器人)
	TrxGiftAmount       float64   `gorm:"column:TrxGiftAmount;comment:TRX赠送金额" json:"TrxGiftAmount"`                                                                           // TRX赠送金额
	UsdtGiftAmount      float64   `gorm:"column:UsdtGiftAmount;comment:USDT赠送金额" json:"UsdtGiftAmount"`                                                                        // USDT赠送金额
	CreatedAt           time.Time `gorm:"column:CreatedAt;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreatedAt"`                                                   // 创建时间
	UpdatedAt           time.Time `gorm:"column:UpdatedAt;default:CURRENT_TIMESTAMP;comment:修改时间" json:"UpdatedAt"`                                                            // 修改时间
	TaskGroup           string    `gorm:"column:TaskGroup;comment:关注频道链接" json:"TaskGroup"`                                                                                    // 关注频道链接
}

// TableName XTgRobotGuide's table name
func (*XTgRobotGuide) TableName() string {
	return TableNameXTgRobotGuide
}
