package model

import (
	"time"
)

// StationMessageTemplate 站内信模板表
type StationMessageTemplate struct {
	ID          int64             `gorm:"column:Id;primaryKey;autoIncrement" json:"Id"`
	Alias       string            `gorm:"column:Alias" json:"Alias"`             // 模板别名
	Type        string            `gorm:"column:Type" json:"Type"`               // 发送类型 1:领取彩金提醒 2:活动参与提醒 等
	PushType    int               `gorm:"column:PushType" json:"PushType"`       // 推送类型：0-系统消息，1-手动消息
	Status      int               `gorm:"column:Status" json:"Status"`           // 状态：0-禁用，1-启用
	IsPopup     int               `gorm:"column:IsPopup" json:"IsPopup"`         // 是否弹窗：0-否，1-是
	IsTimed     int               `gorm:"column:IsTimed" json:"IsTimed"`         // 是否定时发送：0-否，1-是
	TimedAt     *time.Time        `gorm:"column:TimedAt" json:"TimedAt"`         // 定时发送时间
	SellerType  int               `gorm:"column:SellerType" json:"SellerType"`   // 运营商类型：1-指定运营商，2-指定顶级代理ID 0全平台 系统消息可不传该字段
	SellerIds   string            `gorm:"column:SellerIds" json:"SellerIds"`     // 运营商ID列表，逗号分隔
	ChannelIds  string            `gorm:"column:ChannelIds" json:"ChannelIds"`   // 渠道ID列表，逗号分隔
	VipLevels   string            `gorm:"column:VipLevels" json:"VipLevels"`     // VIP等级列表，逗号分隔
	UserIds     string            `gorm:"column:UserIds" json:"UserIds"`         // 用户ID列表，逗号分隔
	UserLabels  string            `gorm:"column:UserLabels" json:"UserLabels"`   // 用户标签列表，逗号分隔
	TopAgentIds string            `gorm:"column:TopAgentIds" json:"TopAgentIds"` // 顶级代理ID列表，逗号分隔
	CreatedAt   time.Time         `gorm:"column:CreatedAt" json:"CreatedAt"`     // 创建时间
	UpdatedAt   time.Time         `gorm:"column:UpdatedAt" json:"UpdatedAt"`     // 更新时间
	Contents    []TemplateContent `gorm:"-" json:"Contents,omitempty"`           // 模板内容（非数据库字段）
}

// TableName 返回表名
func (StationMessageTemplate) TableName() string {
	return "x_message_template"
}

// TemplateContent 站内信模板内容表
type TemplateContent struct {
	ID         int64     `gorm:"column:Id;primaryKey;autoIncrement" json:"Id"`
	TemplateID int64     `gorm:"column:TemplateId" json:"TemplateId"` // 站内信模板ID
	Lang       string    `gorm:"column:Lang" json:"Lang"`             // 语言代码
	Title      string    `gorm:"column:Title" json:"Title"`           // 站内信标题
	Content    string    `gorm:"column:Content" json:"Content"`       // 站内信内容
	CreatedAt  time.Time `gorm:"column:CreatedAt" json:"CreatedAt"`   // 创建时间
	UpdatedAt  time.Time `gorm:"column:UpdatedAt" json:"updatedAt"`   // 更新时间
}

// TableName 返回表名
func (TemplateContent) TableName() string {
	return "x_message_template_content"
}

// MessageRecord 站内信发送记录表
type MessageRecord struct {
	ID          int64      `gorm:"column:Id;primaryKey;autoIncrement" json:"id"`
	TemplateID  int64      `gorm:"column:TemplateId" json:"templateId"`         // 站内信模板ID
	UserID      int64      `gorm:"column:UserId" json:"userId"`                 // 用户ID
	SellerID    int64      `gorm:"column:SellerId" json:"sellerId"`             // 运营商ID
	ChannelID   int64      `gorm:"column:ChannelId" json:"channelId"`           // 渠道ID
	Type        string     `gorm:"column:Type" json:"type"`                     // 发送类型
	Title       string     `gorm:"column:Title" json:"title"`                   // 站内信标题
	Content     string     `gorm:"column:Content" json:"content"`               // 站内信内容
	TitleLang   string     `gorm:"column:TitleLang" json:"titleLang"`           // 多语言标题JSON
	ContentLang string     `gorm:"column:ContentLang" json:"contentLang"`       // 多语言内容JSON
	IsRead      int        `gorm:"column:IsRead" json:"isRead"`                 // 是否已读：0-未读，1-已读
	ReadAt      *time.Time `gorm:"column:ReadAt" json:"readAt"`                 // 阅读时间
	SentAt      time.Time  `gorm:"column:SentAt" json:"sentAt"`                 // 发送时间
	IsDeleted   int        `gorm:"column:IsDeleted;default:0" json:"isDeleted"` // 是否删除：0-未删除，1-已删除
	DeletedAt   *time.Time `gorm:"column:DeletedAt" json:"deletedAt"`           // 删除时间
}

// TableName 返回表名
func (MessageRecord) TableName() string {
	return "x_message_record"
}
