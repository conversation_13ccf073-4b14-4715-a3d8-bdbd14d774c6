// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOnlineUser(db *gorm.DB, opts ...gen.DOOption) xOnlineUser {
	_xOnlineUser := xOnlineUser{}

	_xOnlineUser.xOnlineUserDo.UseDB(db, opts...)
	_xOnlineUser.xOnlineUserDo.UseModel(&model.XOnlineUser{})

	tableName := _xOnlineUser.xOnlineUserDo.TableName()
	_xOnlineUser.ALL = field.NewAsterisk(tableName)
	_xOnlineUser.ID = field.NewInt32(tableName, "Id")
	_xOnlineUser.Type = field.NewInt32(tableName, "Type")
	_xOnlineUser.TypeName = field.NewString(tableName, "TypeName")
	_xOnlineUser.Online = field.NewInt32(tableName, "Online")
	_xOnlineUser.CreateTime = field.NewTime(tableName, "CreateTime")

	_xOnlineUser.fillFieldMap()

	return _xOnlineUser
}

type xOnlineUser struct {
	xOnlineUserDo xOnlineUserDo

	ALL        field.Asterisk
	ID         field.Int32
	Type       field.Int32  // 1:访问站点的在线 2:玩游戏的在线 3:游戏厂商在线
	TypeName   field.String // 在线类型
	Online     field.Int32  // 在线人数
	CreateTime field.Time   // 记录时间

	fieldMap map[string]field.Expr
}

func (x xOnlineUser) Table(newTableName string) *xOnlineUser {
	x.xOnlineUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOnlineUser) As(alias string) *xOnlineUser {
	x.xOnlineUserDo.DO = *(x.xOnlineUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOnlineUser) updateTableName(table string) *xOnlineUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Type = field.NewInt32(table, "Type")
	x.TypeName = field.NewString(table, "TypeName")
	x.Online = field.NewInt32(table, "Online")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xOnlineUser) WithContext(ctx context.Context) *xOnlineUserDo {
	return x.xOnlineUserDo.WithContext(ctx)
}

func (x xOnlineUser) TableName() string { return x.xOnlineUserDo.TableName() }

func (x xOnlineUser) Alias() string { return x.xOnlineUserDo.Alias() }

func (x xOnlineUser) Columns(cols ...field.Expr) gen.Columns { return x.xOnlineUserDo.Columns(cols...) }

func (x *xOnlineUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOnlineUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 5)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Type"] = x.Type
	x.fieldMap["TypeName"] = x.TypeName
	x.fieldMap["Online"] = x.Online
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xOnlineUser) clone(db *gorm.DB) xOnlineUser {
	x.xOnlineUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOnlineUser) replaceDB(db *gorm.DB) xOnlineUser {
	x.xOnlineUserDo.ReplaceDB(db)
	return x
}

type xOnlineUserDo struct{ gen.DO }

func (x xOnlineUserDo) Debug() *xOnlineUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xOnlineUserDo) WithContext(ctx context.Context) *xOnlineUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOnlineUserDo) ReadDB() *xOnlineUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOnlineUserDo) WriteDB() *xOnlineUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOnlineUserDo) Session(config *gorm.Session) *xOnlineUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOnlineUserDo) Clauses(conds ...clause.Expression) *xOnlineUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOnlineUserDo) Returning(value interface{}, columns ...string) *xOnlineUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOnlineUserDo) Not(conds ...gen.Condition) *xOnlineUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOnlineUserDo) Or(conds ...gen.Condition) *xOnlineUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOnlineUserDo) Select(conds ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOnlineUserDo) Where(conds ...gen.Condition) *xOnlineUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOnlineUserDo) Order(conds ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOnlineUserDo) Distinct(cols ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOnlineUserDo) Omit(cols ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOnlineUserDo) Join(table schema.Tabler, on ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOnlineUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOnlineUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOnlineUserDo) Group(cols ...field.Expr) *xOnlineUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOnlineUserDo) Having(conds ...gen.Condition) *xOnlineUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOnlineUserDo) Limit(limit int) *xOnlineUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOnlineUserDo) Offset(offset int) *xOnlineUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOnlineUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOnlineUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOnlineUserDo) Unscoped() *xOnlineUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOnlineUserDo) Create(values ...*model.XOnlineUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOnlineUserDo) CreateInBatches(values []*model.XOnlineUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOnlineUserDo) Save(values ...*model.XOnlineUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOnlineUserDo) First() (*model.XOnlineUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineUser), nil
	}
}

func (x xOnlineUserDo) Take() (*model.XOnlineUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineUser), nil
	}
}

func (x xOnlineUserDo) Last() (*model.XOnlineUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineUser), nil
	}
}

func (x xOnlineUserDo) Find() ([]*model.XOnlineUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOnlineUser), err
}

func (x xOnlineUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOnlineUser, err error) {
	buf := make([]*model.XOnlineUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOnlineUserDo) FindInBatches(result *[]*model.XOnlineUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOnlineUserDo) Attrs(attrs ...field.AssignExpr) *xOnlineUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOnlineUserDo) Assign(attrs ...field.AssignExpr) *xOnlineUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOnlineUserDo) Joins(fields ...field.RelationField) *xOnlineUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOnlineUserDo) Preload(fields ...field.RelationField) *xOnlineUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOnlineUserDo) FirstOrInit() (*model.XOnlineUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineUser), nil
	}
}

func (x xOnlineUserDo) FirstOrCreate() (*model.XOnlineUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineUser), nil
	}
}

func (x xOnlineUserDo) FindByPage(offset int, limit int) (result []*model.XOnlineUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOnlineUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOnlineUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOnlineUserDo) Delete(models ...*model.XOnlineUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOnlineUserDo) withDO(do gen.Dao) *xOnlineUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
