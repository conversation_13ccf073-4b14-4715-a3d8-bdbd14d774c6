package httpc

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"xserver/controller/third/single/base"
)

const (
	FORM_DATA     = 1                                   //x-www-form-urlencoded
	JSON_DATA     = 2                                   //json请求
	URL_ENCODE    = 3                                   //x-www-form-urlencoded  //query parameters and form values
	POST_METHOD_1 = "application/x-www-form-urlencoded" //"user=123&a=123"
	POST_METHOD_2 = "application/json;charset=UTF-8"
)

type DoRequest struct {
	UrlPath    string            //路径
	RespType   int               //返回 0=正常的map  1=字符串  map[data][res]
	Param      []byte            //请求数据
	Header     map[string]string //请求头
	Cookies    map[string]string //请求cookie
	PostMethod int8              //1=x-www-form-urlencoded 2=json请求 3=x-www-form-urlencoded  //query parameters and form values
}

func (this *DoRequest) DoPost() (body map[string]interface{}, err error) {
	client := &http.Client{}
	// 修改 后
	paramStr := string(this.Param)
	if len(paramStr) > 1000 {
		paramStr = paramStr[:1000] + "... (已截断，总长度:" + strconv.Itoa(len(paramStr)) + ")"
	}
	logs.Info("DoPost请求:", this.UrlPath, paramStr)
	//postForm请求
	if this.PostMethod == URL_ENCODE {
		postData := url.Values{}
		var tmp map[string]any
		err = base.MyJson.Unmarshal(this.Param, &tmp)
		if err != nil {
			logs.Error("postParam:解析出错:", this.UrlPath, err)
			return nil, errors.New("postParam:解析出错")
		}
		for k, v := range tmp {
			postData.Set(k, fmt.Sprint(v))
		}
		this.Param = []byte(postData.Encode())

	}

	payload := strings.NewReader(string(this.Param))
	//创建POST请求参数
	req, err := http.NewRequest(http.MethodPost, this.UrlPath, payload)
	if err != nil {
		logs.Error("创建POST请求出错:", this.UrlPath, err)
		return nil, err
	}
	//urlencode请求
	if this.PostMethod == FORM_DATA || this.PostMethod == URL_ENCODE {
		req.Header.Add("content-type", POST_METHOD_1)
	}
	//json流请求
	if this.PostMethod == JSON_DATA {
		req.Header.Add("content-type", POST_METHOD_2)
	}

	//设置头部信息
	if len(this.Header) > 0 {
		for k, v := range this.Header {
			req.Header.Add(k, v)
		}
	}
	//设置cookie
	if len(this.Cookies) > 0 {
		for k, v := range this.Cookies {
			c := &http.Cookie{
				Name:  k,
				Value: v,
			}
			req.AddCookie(c)
		}
	}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("发生POST请求出错:", this.UrlPath, "|", err, "|", req)
		return
	}
	defer resp.Body.Close()
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("返回POST请求出错:", this.UrlPath, "|", err, "|", req, "|", string(b))
		return
	}
	if this.RespType == 1 {
		body = make(map[string]interface{})
		body["data"] = string(b)
		return
	}
	body = map[string]interface{}{}
	err = json.Unmarshal(b, &body)
	if err != nil {
		logs.Error("解析返回POST请求出错:", this.UrlPath, "|", err, "|", req, "|", string(b))
		return
	}
	return
}

func (this *DoRequest) DoPostBytes() (body []byte, err error) {
	client := &http.Client{}
	// 修改后
	paramStr := string(this.Param)
	if len(paramStr) > 2500 {
		logs.Info("DoPost请求:", this.UrlPath, paramStr[:2500]+"... (已截断，总长度:"+strconv.Itoa(len(paramStr))+")")
	} else {
		logs.Info("DoPost请求:", this.UrlPath, paramStr)
	}
	//postForm请求
	if this.PostMethod == URL_ENCODE {
		postData := url.Values{}
		var tmp map[string]any
		err = json.Unmarshal(this.Param, &tmp)
		if err != nil {
			logs.Error("postParam:解析出错:", this.UrlPath, err)
			return nil, errors.New("postParam:解析出错")
		}
		for k, v := range tmp {
			postData.Set(k, fmt.Sprint(v))
		}
		this.Param = []byte(postData.Encode())

	}

	payload := strings.NewReader(string(this.Param))
	//创建POST请求参数
	req, err := http.NewRequest(http.MethodPost, this.UrlPath, payload)
	if err != nil {
		logs.Error("创建POST请求出错:", this.UrlPath, err)
		return nil, err
	}
	//urlencode请求
	if this.PostMethod == FORM_DATA || this.PostMethod == URL_ENCODE {
		req.Header.Add("content-type", POST_METHOD_1)
	}
	//json流请求
	if this.PostMethod == JSON_DATA {
		req.Header.Add("content-type", POST_METHOD_2)
	}

	//设置头部信息
	if len(this.Header) > 0 {
		for k, v := range this.Header {
			req.Header.Add(k, v)
		}
	}
	//设置cookie
	if len(this.Cookies) > 0 {
		for k, v := range this.Cookies {
			c := &http.Cookie{
				Name:  k,
				Value: v,
			}
			req.AddCookie(c)
		}
	}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("发生POST请求出错:", this.UrlPath, "|", err, "|", req)
		return
	}
	defer resp.Body.Close()
	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("返回POST请求出错:", this.UrlPath, "|", err, "|", req, "|", string(body))
		return
	}

	return
}

func (this *DoRequest) DoGet() (body []byte, err error) {
	client := &http.Client{}

	//创建Get请求参数
	req, _ := http.NewRequest(http.MethodGet, this.UrlPath, nil)
	//设置请求参数
	if this.Param != nil {
		var tmp map[string]string
		err = json.Unmarshal(this.Param, &tmp)
		if err != nil {
			return nil, errors.New("Param:解析出错")
		}
		param := req.URL.Query()
		for k, v := range tmp {
			param.Add(k, v)
		}
		//url encode
		req.URL.RawQuery = param.Encode()
	}

	req.Header.Add("content-type", POST_METHOD_1)
	//设置头部信息
	if len(this.Header) > 0 {
		for k, v := range this.Header {
			req.Header.Add(k, v)
		}
	}
	//设置cookie
	if len(this.Cookies) > 0 {
		for k, v := range this.Cookies {
			c := &http.Cookie{
				Name:  k,
				Value: v,
			}
			req.AddCookie(c)
		}
	}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	body, err = ioutil.ReadAll(resp.Body)
	if resp.StatusCode == 200 {
		return body, nil
	}
	return body, err
}
