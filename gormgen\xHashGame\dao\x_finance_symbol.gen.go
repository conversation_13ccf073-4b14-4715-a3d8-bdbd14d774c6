// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXFinanceSymbol(db *gorm.DB, opts ...gen.DOOption) xFinanceSymbol {
	_xFinanceSymbol := xFinanceSymbol{}

	_xFinanceSymbol.xFinanceSymbolDo.UseDB(db, opts...)
	_xFinanceSymbol.xFinanceSymbolDo.UseModel(&model.XFinanceSymbol{})

	tableName := _xFinanceSymbol.xFinanceSymbolDo.TableName()
	_xFinanceSymbol.ALL = field.NewAsterisk(tableName)
	_xFinanceSymbol.ID = field.NewInt32(tableName, "Id")
	_xFinanceSymbol.SellerID = field.NewInt32(tableName, "SellerId")
	_xFinanceSymbol.PSymbol = field.NewString(tableName, "PSymbol")
	_xFinanceSymbol.Symbol = field.NewString(tableName, "Symbol")
	_xFinanceSymbol.Country = field.NewString(tableName, "Country")
	_xFinanceSymbol.FType = field.NewInt32(tableName, "FType")
	_xFinanceSymbol.RateSource = field.NewInt32(tableName, "RateSource")
	_xFinanceSymbol.RechargeRate = field.NewFloat64(tableName, "RechargeRate")
	_xFinanceSymbol.RechargeRateEx = field.NewFloat64(tableName, "RechargeRateEx")
	_xFinanceSymbol.RechargeFixType = field.NewInt32(tableName, "RechargeFixType")
	_xFinanceSymbol.RechargeFix = field.NewFloat64(tableName, "RechargeFix")
	_xFinanceSymbol.WithwardRate = field.NewFloat64(tableName, "WithwardRate")
	_xFinanceSymbol.WithwardRateEx = field.NewFloat64(tableName, "WithwardRateEx")
	_xFinanceSymbol.WithwardFixType = field.NewInt32(tableName, "WithwardFixType")
	_xFinanceSymbol.WithwardFix = field.NewFloat64(tableName, "WithwardFix")
	_xFinanceSymbol.State = field.NewInt32(tableName, "State")
	_xFinanceSymbol.UpdateTime = field.NewTime(tableName, "UpdateTime")
	_xFinanceSymbol.UpdateAccount = field.NewString(tableName, "UpdateAccount")
	_xFinanceSymbol.RateUpdateTime = field.NewTime(tableName, "RateUpdateTime")
	_xFinanceSymbol.RateUpdateAccount = field.NewString(tableName, "RateUpdateAccount")
	_xFinanceSymbol.AutoState = field.NewInt32(tableName, "AutoState")
	_xFinanceSymbol.Icon = field.NewString(tableName, "Icon")
	_xFinanceSymbol.BuyPrice = field.NewFloat64(tableName, "BuyPrice")
	_xFinanceSymbol.SellPrice = field.NewFloat64(tableName, "SellPrice")
	_xFinanceSymbol.Sort = field.NewInt32(tableName, "Sort")
	_xFinanceSymbol.RechargeMin = field.NewFloat64(tableName, "RechargeMin")
	_xFinanceSymbol.RechargeMax = field.NewFloat64(tableName, "RechargeMax")
	_xFinanceSymbol.WithwardMin = field.NewFloat64(tableName, "WithwardMin")
	_xFinanceSymbol.WithwardMax = field.NewFloat64(tableName, "WithwardMax")
	_xFinanceSymbol.ShowIndex = field.NewInt32(tableName, "ShowIndex")
	_xFinanceSymbol.NetJSON = field.NewString(tableName, "NetJson")
	_xFinanceSymbol.ColdWallet = field.NewString(tableName, "ColdWallet")

	_xFinanceSymbol.fillFieldMap()

	return _xFinanceSymbol
}

// xFinanceSymbol 充提币种
type xFinanceSymbol struct {
	xFinanceSymbolDo xFinanceSymbolDo

	ALL               field.Asterisk
	ID                field.Int32
	SellerID          field.Int32  // 运营商Id
	PSymbol           field.String // 平台币
	Symbol            field.String // 币种
	Country           field.String
	FType             field.Int32   // 类型 1法币 2加密货币
	RateSource        field.Int32   // 汇率来源 1币安
	RechargeRate      field.Float64 // 自动时时充值汇率
	RechargeRateEx    field.Float64 // 手动设置充值汇率
	RechargeFixType   field.Int32   // 充值汇率偏差类型 1固定值,2百分比
	RechargeFix       field.Float64 // 充值汇率偏差
	WithwardRate      field.Float64 // 自动时时提现汇率
	WithwardRateEx    field.Float64 // 手动设置提现汇率
	WithwardFixType   field.Int32   // 提现汇率偏差类型 1固定值,2百分比
	WithwardFix       field.Float64 // 提现汇率偏差
	State             field.Int32   // 状态,1启用,2禁用
	UpdateTime        field.Time    // 修改时间
	UpdateAccount     field.String  // 修改人
	RateUpdateTime    field.Time
	RateUpdateAccount field.String
	AutoState         field.Int32 // 是否开启自动同步 1开启,2关闭
	Icon              field.String
	BuyPrice          field.Float64 // 充值汇率
	SellPrice         field.Float64 // 提款汇率
	Sort              field.Int32   // 排序,数字越大越靠前
	RechargeMin       field.Float64 // 充值最小金额
	RechargeMax       field.Float64 // 充值最大金额
	WithwardMin       field.Float64 // 提现最小金额
	WithwardMax       field.Float64 // 提现最大金额
	ShowIndex         field.Int32   // 是否显示在首页 1:显示 2:不显示
	NetJSON           field.String  // 网络协议
	ColdWallet        field.String  // 冷钱包

	fieldMap map[string]field.Expr
}

func (x xFinanceSymbol) Table(newTableName string) *xFinanceSymbol {
	x.xFinanceSymbolDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xFinanceSymbol) As(alias string) *xFinanceSymbol {
	x.xFinanceSymbolDo.DO = *(x.xFinanceSymbolDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xFinanceSymbol) updateTableName(table string) *xFinanceSymbol {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.PSymbol = field.NewString(table, "PSymbol")
	x.Symbol = field.NewString(table, "Symbol")
	x.Country = field.NewString(table, "Country")
	x.FType = field.NewInt32(table, "FType")
	x.RateSource = field.NewInt32(table, "RateSource")
	x.RechargeRate = field.NewFloat64(table, "RechargeRate")
	x.RechargeRateEx = field.NewFloat64(table, "RechargeRateEx")
	x.RechargeFixType = field.NewInt32(table, "RechargeFixType")
	x.RechargeFix = field.NewFloat64(table, "RechargeFix")
	x.WithwardRate = field.NewFloat64(table, "WithwardRate")
	x.WithwardRateEx = field.NewFloat64(table, "WithwardRateEx")
	x.WithwardFixType = field.NewInt32(table, "WithwardFixType")
	x.WithwardFix = field.NewFloat64(table, "WithwardFix")
	x.State = field.NewInt32(table, "State")
	x.UpdateTime = field.NewTime(table, "UpdateTime")
	x.UpdateAccount = field.NewString(table, "UpdateAccount")
	x.RateUpdateTime = field.NewTime(table, "RateUpdateTime")
	x.RateUpdateAccount = field.NewString(table, "RateUpdateAccount")
	x.AutoState = field.NewInt32(table, "AutoState")
	x.Icon = field.NewString(table, "Icon")
	x.BuyPrice = field.NewFloat64(table, "BuyPrice")
	x.SellPrice = field.NewFloat64(table, "SellPrice")
	x.Sort = field.NewInt32(table, "Sort")
	x.RechargeMin = field.NewFloat64(table, "RechargeMin")
	x.RechargeMax = field.NewFloat64(table, "RechargeMax")
	x.WithwardMin = field.NewFloat64(table, "WithwardMin")
	x.WithwardMax = field.NewFloat64(table, "WithwardMax")
	x.ShowIndex = field.NewInt32(table, "ShowIndex")
	x.NetJSON = field.NewString(table, "NetJson")
	x.ColdWallet = field.NewString(table, "ColdWallet")

	x.fillFieldMap()

	return x
}

func (x *xFinanceSymbol) WithContext(ctx context.Context) *xFinanceSymbolDo {
	return x.xFinanceSymbolDo.WithContext(ctx)
}

func (x xFinanceSymbol) TableName() string { return x.xFinanceSymbolDo.TableName() }

func (x xFinanceSymbol) Alias() string { return x.xFinanceSymbolDo.Alias() }

func (x xFinanceSymbol) Columns(cols ...field.Expr) gen.Columns {
	return x.xFinanceSymbolDo.Columns(cols...)
}

func (x *xFinanceSymbol) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xFinanceSymbol) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 32)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["PSymbol"] = x.PSymbol
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Country"] = x.Country
	x.fieldMap["FType"] = x.FType
	x.fieldMap["RateSource"] = x.RateSource
	x.fieldMap["RechargeRate"] = x.RechargeRate
	x.fieldMap["RechargeRateEx"] = x.RechargeRateEx
	x.fieldMap["RechargeFixType"] = x.RechargeFixType
	x.fieldMap["RechargeFix"] = x.RechargeFix
	x.fieldMap["WithwardRate"] = x.WithwardRate
	x.fieldMap["WithwardRateEx"] = x.WithwardRateEx
	x.fieldMap["WithwardFixType"] = x.WithwardFixType
	x.fieldMap["WithwardFix"] = x.WithwardFix
	x.fieldMap["State"] = x.State
	x.fieldMap["UpdateTime"] = x.UpdateTime
	x.fieldMap["UpdateAccount"] = x.UpdateAccount
	x.fieldMap["RateUpdateTime"] = x.RateUpdateTime
	x.fieldMap["RateUpdateAccount"] = x.RateUpdateAccount
	x.fieldMap["AutoState"] = x.AutoState
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["BuyPrice"] = x.BuyPrice
	x.fieldMap["SellPrice"] = x.SellPrice
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["RechargeMin"] = x.RechargeMin
	x.fieldMap["RechargeMax"] = x.RechargeMax
	x.fieldMap["WithwardMin"] = x.WithwardMin
	x.fieldMap["WithwardMax"] = x.WithwardMax
	x.fieldMap["ShowIndex"] = x.ShowIndex
	x.fieldMap["NetJson"] = x.NetJSON
	x.fieldMap["ColdWallet"] = x.ColdWallet
}

func (x xFinanceSymbol) clone(db *gorm.DB) xFinanceSymbol {
	x.xFinanceSymbolDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xFinanceSymbol) replaceDB(db *gorm.DB) xFinanceSymbol {
	x.xFinanceSymbolDo.ReplaceDB(db)
	return x
}

type xFinanceSymbolDo struct{ gen.DO }

func (x xFinanceSymbolDo) Debug() *xFinanceSymbolDo {
	return x.withDO(x.DO.Debug())
}

func (x xFinanceSymbolDo) WithContext(ctx context.Context) *xFinanceSymbolDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xFinanceSymbolDo) ReadDB() *xFinanceSymbolDo {
	return x.Clauses(dbresolver.Read)
}

func (x xFinanceSymbolDo) WriteDB() *xFinanceSymbolDo {
	return x.Clauses(dbresolver.Write)
}

func (x xFinanceSymbolDo) Session(config *gorm.Session) *xFinanceSymbolDo {
	return x.withDO(x.DO.Session(config))
}

func (x xFinanceSymbolDo) Clauses(conds ...clause.Expression) *xFinanceSymbolDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xFinanceSymbolDo) Returning(value interface{}, columns ...string) *xFinanceSymbolDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xFinanceSymbolDo) Not(conds ...gen.Condition) *xFinanceSymbolDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xFinanceSymbolDo) Or(conds ...gen.Condition) *xFinanceSymbolDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xFinanceSymbolDo) Select(conds ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xFinanceSymbolDo) Where(conds ...gen.Condition) *xFinanceSymbolDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xFinanceSymbolDo) Order(conds ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xFinanceSymbolDo) Distinct(cols ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xFinanceSymbolDo) Omit(cols ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xFinanceSymbolDo) Join(table schema.Tabler, on ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xFinanceSymbolDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xFinanceSymbolDo) RightJoin(table schema.Tabler, on ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xFinanceSymbolDo) Group(cols ...field.Expr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xFinanceSymbolDo) Having(conds ...gen.Condition) *xFinanceSymbolDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xFinanceSymbolDo) Limit(limit int) *xFinanceSymbolDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xFinanceSymbolDo) Offset(offset int) *xFinanceSymbolDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xFinanceSymbolDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xFinanceSymbolDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xFinanceSymbolDo) Unscoped() *xFinanceSymbolDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xFinanceSymbolDo) Create(values ...*model.XFinanceSymbol) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xFinanceSymbolDo) CreateInBatches(values []*model.XFinanceSymbol, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xFinanceSymbolDo) Save(values ...*model.XFinanceSymbol) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xFinanceSymbolDo) First() (*model.XFinanceSymbol, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceSymbol), nil
	}
}

func (x xFinanceSymbolDo) Take() (*model.XFinanceSymbol, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceSymbol), nil
	}
}

func (x xFinanceSymbolDo) Last() (*model.XFinanceSymbol, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceSymbol), nil
	}
}

func (x xFinanceSymbolDo) Find() ([]*model.XFinanceSymbol, error) {
	result, err := x.DO.Find()
	return result.([]*model.XFinanceSymbol), err
}

func (x xFinanceSymbolDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XFinanceSymbol, err error) {
	buf := make([]*model.XFinanceSymbol, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xFinanceSymbolDo) FindInBatches(result *[]*model.XFinanceSymbol, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xFinanceSymbolDo) Attrs(attrs ...field.AssignExpr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xFinanceSymbolDo) Assign(attrs ...field.AssignExpr) *xFinanceSymbolDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xFinanceSymbolDo) Joins(fields ...field.RelationField) *xFinanceSymbolDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xFinanceSymbolDo) Preload(fields ...field.RelationField) *xFinanceSymbolDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xFinanceSymbolDo) FirstOrInit() (*model.XFinanceSymbol, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceSymbol), nil
	}
}

func (x xFinanceSymbolDo) FirstOrCreate() (*model.XFinanceSymbol, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceSymbol), nil
	}
}

func (x xFinanceSymbolDo) FindByPage(offset int, limit int) (result []*model.XFinanceSymbol, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xFinanceSymbolDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xFinanceSymbolDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xFinanceSymbolDo) Delete(models ...*model.XFinanceSymbol) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xFinanceSymbolDo) withDO(do gen.Dao) *xFinanceSymbolDo {
	x.DO = *do.(*gen.DO)
	return x
}
