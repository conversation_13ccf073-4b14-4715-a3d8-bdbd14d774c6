package third

import (
	"errors"
	"fmt"
	"strconv"
	"sync/atomic"
	"xserver/abugo"

	"github.com/beego/beego/logs"
	"gorm.io/gorm"

	"xserver/server"
)

// UserLostStatus 用户输光状态检查
type UserLostStatus struct {
	// 当前并发数量
	currentRequests int32
}

// 获取用户余额的缓存键
func (c *UserLostStatus) getUserAmountCacheKey(userId int) string {
	return fmt.Sprintf("%s:%s:user:amount:%d", server.Project(), server.Module(), userId)
}

// CheckAndUpdateLostAllOnceStatus 检查并更新用户是否曾经输光一次的状态
func (c *UserLostStatus) CheckAndUpdateLostAllOnceStatus(userId int) (bool, error) {
	// 尝试从缓存获取用户余额
	cacheKey := c.getUserAmountCacheKey(userId)
	rValueInterface := server.Redis().Get(cacheKey)
	amountStr := abugo.GetStringFromInterface(rValueInterface)

	// 如果缓存命中，直接使用缓存值
	if amountStr != "" {
		// 尝试将字符串转换为浮点数
		amount, err := strconv.ParseFloat(amountStr, 64)
		//logs.Info("从缓存中查询到用户余额userId=", userId, " amount=", amount)
		if err != nil {
			logs.Error("用户余额缓存转换失败 userId=", userId, " err=", err.Error())
			// 缓存值转换失败，继续查询数据库
		} else {
			// 如果余额小于1，则更新x_user_more表状态为1
			if amount < 1 {
				return c.UpdateUserLostStatus(userId, 1)
			}
			return true, nil
		}
	}

	//logs.Info("没有从缓存中查询到用户余额userId=", userId)

	// 缓存未命中，需要查询数据库
	// 检查当前并发数量，如果超过70个，则跳过查询
	current := atomic.LoadInt32(&c.currentRequests)
	if current >= 70 {
		logs.Warn("用户余额检查并发已超过70，跳过数据库查询 userId=", userId)
		return true, nil
	}

	// 原子操作增加并发计数
	atomic.AddInt32(&c.currentRequests, 1)
	defer atomic.AddInt32(&c.currentRequests, -1) // 确保在函数结束时减少计数

	// 查询数据库
	db := server.Db().GormDao()
	var user struct {
		Amount float64 `gorm:"column:Amount"`
	}

	err := db.Table("x_user").
		Select("Amount").
		Where("UserId = ?", userId).
		First(&user).Error

	if err != nil {
		logs.Error("查询用户余额失败 userId=", userId, " err=", err.Error())
		return false, err
	}

	// 缓存查询结果，设置10秒过期时间
	amountCacheStr := fmt.Sprintf("%v", user.Amount)
	if e := server.Redis().SetStringEx(cacheKey, 10, amountCacheStr); e != nil {
		logs.Error("缓存用户余额失败 userId=", userId, " err=", e.Error())
	}

	// 如果余额小于1，则更新x_user_more表状态为1
	if user.Amount < 1 {
		return c.UpdateUserLostStatus(userId, 1)
	}

	return true, nil
}

// UpdateUserLostStatus 修改用户输光状态 0 初始状态 1 输光状态
func (c *UserLostStatus) UpdateUserLostStatus(userId int, status int) (bool, error) {
	db := server.Db().GormDao()

	userMore := struct {
		UserId        int `gorm:"column:UserId;primaryKey"`
		IsLostAllOnce int `gorm:"column:IsLostAllOnce"`
	}{
		UserId:        userId,
		IsLostAllOnce: status,
	}

	// FirstOrCreate 会自动处理记录不存在的情况
	result := db.Table("x_user_more").
		Where("UserId = ?", userId).
		FirstOrCreate(&userMore)

	if result.Error != nil {
		logs.Error("更新用户输光状态失败 userId=", userId, " status=", status, " err=", result.Error.Error())
		return false, result.Error
	}
	//logs.Info("创建记录成功 userId=", userId, " result=", result)

	// 如果记录已存在但状态不同，则更新状态
	if result.RowsAffected == 0 && userMore.IsLostAllOnce != status {
		result = db.Table("x_user_more").
			Where("UserId = ?", userId).
			Update("IsLostAllOnce", status)
		//logs.Info("记录已存在但状态不同 更新状态 userId=", userId, " status=", status)
		if result.Error != nil {
			logs.Error("更新用户输光状态失败 userId=", userId, " status=", status, " err=", result.Error.Error())
			return false, result.Error
		}
	}

	logs.Info("更新用户输光状态成功 userId=", userId, " status=", status)
	return true, nil
}

// HasLostAllOnce 判断用户是否曾经输光过
func (c *UserLostStatus) HasLostAllOnce(userId int) (bool, error) {
	// 查询用户在x_user_more表中的IsLostAllOnce状态
	var userMore struct {
		IsLostAllOnce int `gorm:"column:IsLostAllOnce"`
	}

	err := server.Db().GormDao().Table("x_user_more").
		Select("IsLostAllOnce").
		Where("UserId = ?", userId).
		First(&userMore).Error

	// 处理用户不存在的情况
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 用户不存在，表示未输光过
		return false, nil
	} else if err != nil {
		// 查询出错
		logs.Error("查询用户输光状态失败 userId=", userId, " err=", err.Error())
		return false, err
	}

	// 返回用户是否输光过
	return userMore.IsLostAllOnce == 1, nil
}
