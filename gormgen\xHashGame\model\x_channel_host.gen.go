// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXChannelHost = "x_channel_host"

// XChannelHost mapped from table <x_channel_host>
type XChannelHost struct {
	ID                 int32     `gorm:"column:Id;not null" json:"Id"`
	Host               string    `gorm:"column:Host;primaryKey" json:"Host"`
	ChannelID          int32     `gorm:"column:ChannelId" json:"ChannelId"`
	State              int32     `gorm:"column:State;comment:1启用 2禁用" json:"State"` // 1启用 2禁用
	CreateTime         time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	GameSort           string    `gorm:"column:GameSort" json:"GameSort"`
	GameSortEx         string    `gorm:"column:GameSortEx" json:"GameSortEx"`
	AgentCode          string    `gorm:"column:AgentCode" json:"AgentCode"`
	GameSortNew        string    `gorm:"column:GameSortNew;comment:新游戏大类排序" json:"GameSortNew"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 新游戏大类排序
	HostTagID          int32     `gorm:"column:HostTagId;comment:x_host_tag.id" json:"HostTagId"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // x_host_tag.id
	WithdrawNeedActive int32     `gorm:"column:WithdrawNeedActive;default:1;comment:提款是否需要激活钱包 1激活 2不需要激活" json:"WithdrawNeedActive"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // 提款是否需要激活钱包 1激活 2不需要激活
	CustomService      string    `gorm:"column:CustomService;comment:独立客服" json:"CustomService"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 独立客服
	CustomServiceState int32     `gorm:"column:CustomServiceState;default:2;comment:独立客服开关 1-开启 2关闭" json:"CustomServiceState"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 独立客服开关 1-开启 2关闭
	TJ51ID             string    `gorm:"column:TJ51Id;comment:51统计ID" json:"TJ51Id"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    // 51统计ID
	SocialLinks        string    `gorm:"column:SocialLinks;comment:社媒链接" json:"SocialLinks"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 社媒链接
	Maidian            string    `gorm:"column:Maidian;comment:埋点统计" json:"Maidian"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    // 埋点统计
	IsAIEnable         int32     `gorm:"column:IsAIEnable;default:2;comment:是否开启AI客服（1:开启 2:关闭）" json:"IsAIEnable"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     // 是否开启AI客服（1:开启 2:关闭）
	SportTarget        string    `gorm:"column:SportTarget;comment:体育跳转" json:"SportTarget"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 体育跳转
	IsTyjEnable        int32     `gorm:"column:IsTyjEnable;default:2;comment:体验金开关 1:开 2:关" json:"IsTyjEnable"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 体验金开关 1:开 2:关
	TgAuthRobot        string    `gorm:"column:TgAuthRobot;comment:Tg授权机器人" json:"TgAuthRobot"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // Tg授权机器人
	Webclip            string    `gorm:"column:Webclip;comment:webclip描述文件" json:"Webclip"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             // webclip描述文件
	CountryList        string    `gorm:"column:CountryList;not null;default:AE,AF,AZ,BD,BH,BN,BT,CN,CY,HK,ID,IL,IN,IO,IQ,IR,JO,JP,KG,KH,KP,KR,KW,KZ,LA,LB,LK,MM,MN,MO,MV,MY,NP,OM,PH,PK,PS,QA,RU,SA,SY,TH,TJ,TL,TM,TW,UZ,VN,YE,AD,AL,AM,AT,AX,BA,BE,BG,BY,CH,CZ,DE,DK,EE,FI,FO,FR,GB,GE,GG,GI,GR,HR,HU,IE,IM,IS,IT,JE,LI,LT,LU,LV,MC,MD,ME,MK,MT,NL,NO,PL,PT,RO,RS,SE,SI,SJ,SK,SM,TR,UA,VA,AG,AI,AW,BB,BL,BM,BQ,BS,BZ,CA,CR,CU,CW,DM,DO,GD,GL,GP,GT,HN,HT,JM,KN,KY,LC,MF,MQ,MS,MX,NI,PA,PM,PR,SV,SX,TC,TT,US,VC,VG,VI,AR,BO,BR,CL,CO,EC,FK,GF,GS,GY,PE,PY,SR,UY,VE,AO,BF,BI,BJ,BW,CD,CF,CG,CI,CM,CV,DJ,DZ,EG,EH,ER,ET,GA,GH,GM,GN,GQ,GW,KE,KM,LR,LS,LY,MA,MG,ML,MR,MU,MW,MZ,NA,NE,NG,RE,RW,SC,SD,SH,SL,SN,SO,SS,ST,SZ,TD,TG,TN,TZ,UG,YT,ZA,ZM,ZW,AQ,BV,TF,AS,AU,CC,CK,CX,FJ,FM,GU,HM,KI,MH,MP,NC,NF,NR,NU,NZ,PF,PG,PN,PW,SB,TK,TO,TV,UM,VU,WF,WS,SG;comment:支持的地区（二位字母国家代码，英文逗号分隔）" json:"CountryList"` // 支持的地区（二位字母国家代码，英文逗号分隔）
	LoginRegisterType  string    `gorm:"column:LoginRegisterType;comment:登录注册类型" json:"LoginRegisterType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              // 登录注册类型
}

// TableName XChannelHost's table name
func (*XChannelHost) TableName() string {
	return TableNameXChannelHost
}
