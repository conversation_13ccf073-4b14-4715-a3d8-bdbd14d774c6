package model

import (
	"github.com/shopspring/decimal"
	"xserver/utils"
)

type Charge struct {
	Id         int             `json:"Id" gorm:"column:Id"`
	UserId     int             `json:"UserId" gorm:"column:UserId"`
	Sellerld   int             `json:"Sellerld" gorm:"column:Sellerld"`
	ChannelId  int             `json:"ChannelId" gorm:"column:ChannelId"`
	OrderId    string          `json:"OrderId" gorm:"column:OrderId"`
	Amount     decimal.Decimal `json:"Amount" gorm:"column:Amount"`
	TxId       string          `json:"TxId" gorm:"column:TxId"`
	RealAmount decimal.Decimal `json:"RealAmount" gorm:"column:RealAmount"`
	State      int             `json:"State" gorm:"column:State"`
}

type ActiveDefine struct {
	Id              int             `json:"Id" gorm:"column:Id"`                           //id
	SellerId        int             `json:"SellerId" gorm:"column:SellerId"`               //代理id
	ChannelId       int             `json:"ChannelId" gorm:"column:ChannelId"`             //活动渠道id
	ActiveId        int             `json:"ActiveId" gorm:"column:ActiveId"`               //活动id
	Memo            string          `json:"memo" gorm:"column:Memo"`                       //活动说明
	AuditType       int             `json:"AuditType" gorm:"column:AuditType"`             //审核方式 1人工审核 2自动审核
	State           int             `json:"State" gorm:"column:State"`                     //状态
	Sort            int             `json:"Sort" gorm:"column:Sort"`                       //排序权重
	EffectStartTime int64           `json:"EffectStartTime" gorm:"column:EffectStartTime"` //活动开始时间
	EffectEndTime   int64           `json:"EffectEndTime" gorm:"column:EffectEndTime"`     //活动结束时间
	Title           string          `json:"Title" gorm:"column:Title"`                     //活动名称
	TitleImg        string          `json:"TitleImg" gorm:"column:TitleImg"`               //图片
	MinLiuShui      decimal.Decimal `json:"MinLiuShui" gorm:"column:MinLiuShui"`           //提现最低流水百分比
	ExtReward       decimal.Decimal `json:"ExtReward" gorm:"column:ExtReward"`             //额外奖金 闯关活动才有
	MinDeposit      decimal.Decimal `json:"MinDeposit" gorm:"column:MinDeposit"`           //最低存款
	MaxReward       decimal.Decimal `json:"MaxReward" gorm:"column:MaxReward"`             //最大返还金额
	ValidRecharge   decimal.Decimal `json:"ValidRecharge" gorm:"column:ValidRecharge"`     //有效会员最低充值
	ValidLiuShui    decimal.Decimal `json:"ValidLiuShui" gorm:"column:ValidLiuShui"`       //有效会员最低流水
	TrxPrice        decimal.Decimal `json:"TrxPrice" gorm:"column:TrxPrice"`               //Trx按多少倍计算下注价格(同后台配置VipTrxPrice)
	Config          string          `json:"Config" gorm:"column:Config"`                   //不同活动区别化的配置
	BaseConfig      string          `json:"BaseConfig" gorm:"column:BaseConfig"`           //基础配置
	GameType        string          `json:"GameType" gorm:"column:GameType"`               //游戏分类
}

type ActiveDefineOld struct {
	ActiveDefine
	UpdateDate utils.MyString `json:"UpdateDate" gorm:"column:UpdateDate"` //最后更改时间
}

type ActiveInfo struct {
	Id          int             `json:"Id" gorm:"column:Id"`                   //id
	SellerId    int             `json:"SellerId" gorm:"column:SellerId"`       //代理id
	ChannelId   int             `json:"ChannelId" gorm:"column:ChannelId"`     //活动渠道id
	ActiveId    int             `json:"ActiveId" gorm:"column:ActiveId"`       //活动id
	Level       int             `json:"Level" gorm:"column:Level"`             //活动等级(如果是游戏投注返水 此字段为游戏ID  1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery)
	LimitValue  decimal.Decimal `json:"LimitValue" gorm:"column:LimitValue"`   //限制值(有的是数值,有的是个数)
	RewardValue decimal.Decimal `json:"RewardValue" gorm:"column:RewardValue"` //返奖值(有的是直接数值有的是比例)
	RewardJson  string          `json:"RewardJson" gorm:"RewardJson"`          //降龙伏虎活动配置细节
}

type ActiveInfoOld struct {
	ActiveInfo
	UpdateDate utils.MyString `json:"UpdateDate" gorm:"column:UpdateDate"` //最后更改时间
}

type UserInfo struct {
	Id                       int             `json:"Id" gorm:"column:Id"`                                             //id
	UserId                   int             `json:"UserId" gorm:"column:UserId"`                                     //用户id
	SellerId                 int             `json:"SellerId" gorm:"column:SellerId"`                                 //代理id
	ChannelId                int             `json:"ChannelId" gorm:"column:ChannelId"`                               //活动渠道id
	Account                  string          `json:"Account" gorm:"column:Account"`                                   //账号
	Password                 string          `json:"Password" gorm:"column:Password"`                                 //密码
	NickName                 string          `json:"NickName" gorm:"column:NickName"`                                 //昵称
	Token                    string          `json:"Token" gorm:"column:Token"`                                       //登陆token
	Agents                   string          `json:"Agents" gorm:"column:Agents"`                                     //json数组,所有上级id,下标0是直属代理,越往后,代理等级越高
	TopAgentId               int             `json:"TopAgentId" gorm:"column:TopAgentId"`                             //顶级代理id
	AgentId                  int             `json:"AgentId" gorm:"column:AgentId"`                                   //直属代理
	RegisterIp               string          `json:"RegisterIp" gorm:"column:RegisterIp"`                             //注册ip
	RegisterTime             utils.MyString  `json:"RegisterTime" gorm:"column:RegisterTime;type:datetime"`           //注册时间
	Address                  string          `json:"Address" gorm:"column:Address"`                                   //地址
	Email                    string          `json:"Email" gorm:"column:Email"`                                       //邮件地址
	PhoneNum                 string          `json:"PhoneNum" gorm:"column:PhoneNum"`                                 //电话号码
	IsTopAgent               int             `json:"IsTopAgent" gorm:"column:IsTopAgent"`                             //是否顶级代理1 是 2不是
	BetTrx                   decimal.Decimal `json:"BetTrx" gorm:"column:BetTrx"`                                     //trx下注
	RewardTrx                decimal.Decimal `json:"RewardTrx" gorm:"column:RewardTrx"`                               //trx反奖
	LiuSuiTrx                decimal.Decimal `json:"LiuSuiTrx" gorm:"column:LiuSuiTrx"`                               //trx流水
	BetUsdt                  decimal.Decimal `json:"BetUsdt" gorm:"column:BetUsdt"`                                   //usdt下注
	RewardUsdt               decimal.Decimal `json:"RewardUsdt" gorm:"column:RewardUsdt"`                             //usdt反奖
	LiuSuiUsdt               decimal.Decimal `json:"LiuSuiUsdt" gorm:"column:LiuSuiUsdt"`                             //usdt流水
	IsAgent                  int             `json:"IsAgent" gorm:"column:IsAgent"`                                   //是否代理 1 是 2 不是
	LoginTime                utils.MyString  `json:"LoginTime" gorm:"column:LoginTime"`                               //登陆时间
	LoginIp                  string          `json:"LoginIp" gorm:"column:LoginIp"`                                   //登陆ip
	State                    int             `json:"State" gorm:"column:State"`                                       //状态 1启用 2禁用
	FineLiuSuiTrx            decimal.Decimal `json:"FineLiuSuiTrx" gorm:"column:FineLiuSuiTrx"`                       //扣除流水trx
	FineLiuSuiUsdt           decimal.Decimal `json:"FineLiuSuiUsdt" gorm:"column:FineLiuSuiUsdt"`                     //扣除流水usdt
	LastGameInfo             string          `json:"LastGameInfo" gorm:"column:LastGameInfo"`                         //最后玩的游戏
	FenCheng                 decimal.Decimal `json:"FenCheng" gorm:"column:FenCheng"`                                 //分成比例
	AgentCode                string          `json:"AgentCode" gorm:"column:AgentCode"`                               //注册邀请码
	TgChatId                 int64           `json:"TgChatId" gorm:"column:TgChatId"`                                 //电报机器人chat id
	TgUserName               string          `json:"TgUserName" gorm:"column:TgUserName"`                             //电报机器人用户名
	VerifyState              int             `json:"VerifyState" gorm:"column:VerifyState"`                           //地址是否已经被验证
	VerifyAmount             int             `json:"VerifyAmount" gorm:"column:VerifyAmount"`                         //地址验证转账金额
	VerifyTime               utils.MyString  `json:"VerifyTime" gorm:"column:VerifyTime"`                             //地址验证时间
	Amount                   decimal.Decimal `json:"Amount" gorm:"column:Amount"`                                     //账户余额usdt
	LockedAmount             decimal.Decimal `json:"LockedAmount" gorm:"column:LockedAmount"`                         //锁定账户余额
	LastGameInfo6            string          `json:"LastGameInfo6" gorm:"column:LastGameInfo6"`                       //最后一局快3
	LastGameInfo7            string          `json:"LastGameInfo7" gorm:"column:LastGameInfo7"`                       //最后一局pk10
	GameFee                  string          `json:"GameFee" gorm:"column:GameFee"`                                   //个人游戏费率
	VipAmount                decimal.Decimal `json:"VipAmount" gorm:"column:VipAmount"`                               //vip累计金额
	WinAudit                 string          `json:"WinAudit" gorm:"column:WinAudit"`                                 //连赢审核记录
	MaxBet                   string          `json:"MaxBet" gorm:"column:MaxBet"`                                     //单笔最大下注记录
	RechargeAddressTron      string          `json:"RechargeAddressTron" gorm:"column:RechargeAddressTron"`           //充值地址
	WalletPassword           string          `json:"WalletPassword" gorm:"column:WalletPassword"`                     //提现密码
	MaxBetTime               utils.MyString  `json:"MaxBetTime" gorm:"column:MaxBetTime"`                             //降赔重置时间
	JpType                   int             `json:"JpType" gorm:"column:JpType"`                                     //降赔类型
	LastGameInfoEx           string          `json:"LastGameInfoEx" gorm:"column:LastGameInfoEx"`                     //最后玩的游戏
	WithdrawLiuSui           decimal.Decimal `json:"WithdrawLiuSui" gorm:"column:WithdrawLiuSui"`                     //提现流水
	TotalLiuSui              decimal.Decimal `json:"TotalLiuSui" gorm:"column:TotalLiuSui"`                           //当前流水
	RechargeAddressEth       string          `json:"RechargeAddressEth" gorm:"column:RechargeAddressEth"`             //充值地址
	HeadId                   string          `json:"HeadId" gorm:"column:HeadId"`                                     //头像id
	Gender                   string          `json:"Gender" gorm:"column:Gender"`                                     //性别
	DeliveryAddress          string          `json:"DeliveryAddress" gorm:"column:DeliveryAddress"`                   //收货地址
	Birthday                 utils.MyString  `json:"Birthday" gorm:"column:Birthday"`                                 //生日
	RealName                 string          `json:"RealName" gorm:"column:RealName"`                                 //真实姓名
	IsTest                   int             `json:"IsTest" gorm:"column:IsTest"`                                     //是否测试账号 1是 2 不是
	UpdatePasswordTime       utils.MyString  `json:"UpdatePasswordTime" gorm:"column:UpdatePasswordTime"`             //最后一次修改密码时间
	UpdateWalletPasswordTime utils.MyString  `json:"UpdateWalletPasswordTime" gorm:"column:UpdateWalletPasswordTime"` //最后一次修改资金密码时间
	AuditAmount              decimal.Decimal `json:"AuditAmount" gorm:"column:AuditAmount"`                           //审核金额，反奖超过此金额需要审核
	WinJiangPeiMax           string          `json:"WinJiangPeiMax" gorm:"column:WinJiangPeiMax"`                     //降赔最大金额
	IsPanda                  int             `json:"IsPanda" gorm:"column:IsPanda"`                                   //是否量化用户 1是 2 不是
	BlackMaker               string          `json:"BlackMaker" gorm:"column:BlackMaker"`                             //区块黑名单
	TgName                   string          `json:"TgName" gorm:"column:TgName"`                                     //注册tg
	CsGroup                  string          `json:"CsGroup" gorm:"column:CsGroup"`                                   //客服团队
	CsId                     string          `json:"CsId" gorm:"column:CsId"`                                         //客服工号
	RegGift                  int             `json:"RegGift" gorm:"column:RegGift"`                                   //体验金状态 1无体验金,2可以领取体验金,3已经领取trx体验金,4可以领取u体验金,5已经领取u体验金
	IgnoreWinJiangPei        int             `json:"IgnoreWinJiangPei" gorm:"column:IgnoreWinJiangPei"`               //忽略盈利降赔,1是,2否
	BetCount                 int             `json:"BetCount" gorm:"column:BetCount"`                                 //投注次数
	FirstBetTime             utils.MyString  `json:"FirstBetTime" gorm:"column:FirstBetTime"`                         //首次投注时间
	LastBetTime              utils.MyString  `json:"LastBetTime" gorm:"column:LastBetTime"`                           //最后投注时间
	FirstRechargeTime        utils.MyString  `json:"FirstRechargeTime" gorm:"column:FirstRechargeTime"`               //首次充值时间
	LastRechargeTime         utils.MyString  `json:"LastRechargeTime" gorm:"column:LastRechargeTime"`                 //最后充值时间
	RegUrl                   string          `json:"RegUrl" gorm:"column:RegUrl"`                                     //注册域名
	Memo                     string          `json:"Memo" gorm:"column:Memo"`                                         //备注
	KeFuTgName               string          `json:"KeFuTgName" gorm:"column:KeFuTgName"`                             //客服tg
	Tag                      string          `json:"Tag" gorm:"column:Tag"`                                           //标签
	CaiJingTrx               decimal.Decimal `json:"CaiJingTrx" gorm:"column:CaiJingTrx"`                             //累计彩金trx
	CaiJingUsdt              decimal.Decimal `json:"CaiJingUsdt" gorm:"column:CaiJingUsdt"`                           //累计彩金usdt
	RechargeAmount           decimal.Decimal `json:"RechargeAmount" gorm:"column:RechargeAmount"`                     //累计充值
	WithdrawAmount           decimal.Decimal `json:"WithdrawAmount" gorm:"column:WithdrawAmount"`                     //累计提款
	CSBindTime               utils.MyString  `json:"CSBindTime" gorm:"column:CSBindTime"`                             //客服绑定时间
	ThirdId                  float64         `json:"ThirdId" gorm:"column:ThirdId"`                                   //三方id
	SpecialAgent             float64         `json:"SpecialAgent" gorm:"column:SpecialAgent"`                         //是否是独立代理 1 是,2不是
	LastAddAmount            decimal.Decimal `json:"LastAddAmount" gorm:"column:LastAddAmount"`                       //最后一次后台增值金额

}

//type ActiveAwardAuditBase struct {
//	SellerId      int             `json:"SellerId" gorm:"column:SellerId"`           //代理id
//	ChannelId     int             `json:"ChannelId" gorm:"column:ChannelId"`         //渠道商id
//	UserId        int             `json:"UserId" gorm:"column:UserId"`               //用户id
//	ActiveId      int             `json:"ActiveId" gorm:"column:ActiveId"`           //活动id
//	ActiveLevel   int             `json:"ActiveLevel" gorm:"column:ActiveLevel"`     //活动等级
//	ActiveMemo    string          `json:"ActiveMemo" gorm:"column:ActiveMemo"`       //活动额外说明
//	RecordDate    string          `json:"RecordDate" gorm:"column:RecordDate"`       //日期
//	Amount        decimal.Decimal `json:"Amount" gorm:"column:Amount"`               //活动送金
//	AuditState    int             `json:"AuditState" gorm:"column:AuditState"`       //审核状态 1待审核,2审核拒绝,3审核通过,4自动通过
//	AuditAccount  string          `json:"AuditAccount" gorm:"column:AuditAccount"`   //审核账号
//	AuditTime     *utils.MyString `json:"AuditTime" gorm:"column:AuditTime"`         //审核时间
//	AuditMemo     string          `json:"AuditMemo" gorm:"column:AuditMemo"`         //审核备注
//	CreateTime    utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`       //创建时间
//	OrderId       int             `json:"OrderId" gorm:"column:OrderId"`             //活动1 注单Id
//	AmountTrx     decimal.Decimal `json:"AmountTrx" gorm:"column:AmountTrx"`         //活动1 送金的trx
//	GasFee        decimal.Decimal `json:"GasFee" gorm:"column:GasFee"`               //活动1 派奖上链gas费
//	TotalRecharge decimal.Decimal `json:"TotalRecharge" gorm:"column:TotalRecharge"` //活动2 6 当日充值金额
//	LiuShui       decimal.Decimal `json:"LiuShui" gorm:"column:LiuShui"`             //流水有效投注
//	Address       string          `json:"Address" gorm:"column:Address"`             //活动1 玩家地址
//	TxId          string          `json:"TxId" gorm:"column:TxId"`                   //活动1 交易哈希
//	NetWinLoss    decimal.Decimal `json:"NetWinLoss" gorm:"column:NetWinLoss"`       //活动6 当日净亏损
//	LimitLiuShui  decimal.Decimal `json:"LimitLiuShui" gorm:"-:all"`                 //活动6 当日净亏损
//}

type AddActiveAwardAudit struct {
	//gorm.Model
	//ActiveAwardAuditBase
	//gorm.Model
	SellerId      int             `json:"SellerId" gorm:"column:SellerId"`           //代理id
	ChannelId     int             `json:"ChannelId" gorm:"column:ChannelId"`         //渠道商id
	UserId        int             `json:"UserId" gorm:"column:UserId"`               //用户id
	ActiveId      int             `json:"ActiveId" gorm:"column:ActiveId"`           //活动id
	ActiveLevel   int             `json:"ActiveLevel" gorm:"column:ActiveLevel"`     //活动等级
	ActiveMemo    string          `json:"ActiveMemo" gorm:"column:ActiveMemo"`       //活动额外说明
	RecordDate    string          `json:"RecordDate" gorm:"column:RecordDate"`       //日期
	Amount        decimal.Decimal `json:"Amount" gorm:"column:Amount"`               //活动送金
	AuditState    int             `json:"AuditState" gorm:"column:AuditState"`       //审核状态 1待审核,2审核拒绝,3审核通过,4自动通过
	AuditAccount  string          `json:"AuditAccount" gorm:"column:AuditAccount"`   //审核账号
	AuditTime     *utils.MyString `json:"AuditTime" gorm:"column:AuditTime"`         //审核时间
	AuditMemo     string          `json:"AuditMemo" gorm:"column:AuditMemo"`         //审核备注
	CreateTime    utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`       //创建时间
	OrderId       int             `json:"OrderId" gorm:"column:OrderId"`             //活动1 注单Id
	AmountTrx     decimal.Decimal `json:"AmountTrx" gorm:"column:AmountTrx"`         //活动1 送金的trx
	GasFee        decimal.Decimal `json:"GasFee" gorm:"column:GasFee"`               //活动1 派奖上链gas费
	TotalRecharge decimal.Decimal `json:"TotalRecharge" gorm:"column:TotalRecharge"` //活动2 6 当日充值金额
	LiuShui       decimal.Decimal `json:"LiuShui" gorm:"column:LiuShui"`             //流水有效投注
	Address       string          `json:"Address" gorm:"column:Address"`             //活动1 玩家地址
	TxId          string          `json:"TxId" gorm:"column:TxId"`                   //活动1 交易哈希
	NetWinLoss    decimal.Decimal `json:"NetWinLoss" gorm:"column:NetWinLoss"`       //活动6 当日净亏损
	LimitLiuShui  decimal.Decimal `json:"LimitLiuShui" gorm:"-:all"`                 //活动6 当日净亏损
}

type ActiveAwardAudit struct {
	*AddActiveAwardAudit
	Id int `json:"Id" gorm:"column:Id"` //id
}

type Recharge struct {
	Id           int             `json:"Id" gorm:"column:Id"`                     //id
	UserId       int             `json:"UserId" gorm:"column:UserId"`             //玩家id
	SellerId     int             `json:"SellerId" gorm:"column:SellerId"`         //运营商
	ChannelId    int             `json:"ChannelId" gorm:"column:ChannelId"`       //渠道商
	OrderId      string          `json:"OrderId" gorm:"column:OrderId"`           //hbc订单id
	Symbol       string          `json:"Symbol" gorm:"column:Symbol"`             //币种
	Amount       decimal.Decimal `json:"Amount" gorm:"column:Amount"`             //金额
	Net          string          `json:"Net" gorm:"column:Net"`                   //
	ToAddress    string          `json:"ToAddress" gorm:"column:ToAddress"`       //收款地址
	TxId         string          `json:"TxId" gorm:"column:TxId"`                 //交易哈希
	RealAmount   decimal.Decimal `json:"RealAmount" gorm:"column:RealAmount"`     //到账金额
	TransferRate decimal.Decimal `json:"TransferRate" gorm:"column:TransferRate"` //汇率
	CreateTime   utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`     //到账时间
	Memo         string          `json:"Memo" gorm:"column:Memo"`                 //备注
	TopAgentId   int             `json:"TopAgentId" gorm:"column:TopAgentId"`     //顶级代理id
	State        int             `json:"State" gorm:"column:State"`               //状态 1未找到玩家 2小于最低充值额 5充值成功
	NetFee       decimal.Decimal `json:"NetFee" gorm:"column:NetFee"`             //
	CSGroup      string          `json:"CSGroup" gorm:"column:CSGroup"`           //
	CSId         string          `json:"CSId" gorm:"column:CSId"`                 //
	AdJustState  int             `json:"AdJustState" gorm:"column:AdJustState"`   //
}

type VipDailly struct {
	Id                    int             `json:"Id" gorm:"column:Id"`                                       //id
	RecordDate            string          `json:"RecordDate" gorm:"column:RecordDate"`                       //
	SellerId              int             `json:"SellerId" gorm:"column:SellerId"`                           //
	ChannelId             int             `json:"ChannelId" gorm:"column:ChannelId"`                         //
	UserId                int             `json:"UserId" gorm:"column:UserId"`                               //
	LiuSui                decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`                               //
	LiuSuiHaXi            decimal.Decimal `json:"LiuSuiHaXi" gorm:"column:LiuSuiHaXi"`                       //
	LiuSuiLottery         decimal.Decimal `json:"LiuSuiLottery" gorm:"column:LiuSuiLottery"`                 //
	LiuSuiQiPai           decimal.Decimal `json:"LiuSuiQiPai" gorm:"column:LiuSuiQiPai"`                     //
	LiuSuiDianZhi         decimal.Decimal `json:"LiuSuiDianZhi" gorm:"column:LiuSuiDianZhi"`                 //
	LiuSuiXiaoYouXi       decimal.Decimal `json:"LiuSuiXiaoYouXi" gorm:"column:LiuSuiXiaoYouXi"`             //
	LiuSuiLive            decimal.Decimal `json:"LiuSuiLive" gorm:"column:LiuSuiLive"`                       //
	LiuSuiSport           decimal.Decimal `json:"LiuSuiSport" gorm:"column:LiuSuiSport"`                     //
	LiuSuiTexas           decimal.Decimal `json:"LiuSuiTexas" gorm:"column:LiuSuiTexas"`                     //
	State                 decimal.Decimal `json:"State" gorm:"column:State"`                                 //状态 1 待发放,2已发放
	RewardAmount          decimal.Decimal `json:"RewardAmount" gorm:"column:RewardAmount"`                   //
	RewardAmountHaXi      decimal.Decimal `json:"RewardAmountHaXi" gorm:"column:RewardAmountHaXi"`           //
	RewardAmountLottery   decimal.Decimal `json:"RewardAmountLottery" gorm:"column:RewardAmountLottery"`     //
	RewardAmountQiPai     decimal.Decimal `json:"RewardAmountQiPai" gorm:"column:RewardAmountQiPai"`         //
	RewardAmountDianZhi   decimal.Decimal `json:"RewardAmountDianZhi" gorm:"column:RewardAmountDianZhi"`     //
	RewardAmountXiaoYouXi decimal.Decimal `json:"RewardAmountXiaoYouXi" gorm:"column:RewardAmountXiaoYouXi"` //
	RewardAmountLive      decimal.Decimal `json:"RewardAmountLive" gorm:"column:RewardAmountLive"`           //
	RewardAmountSport     decimal.Decimal `json:"RewardAmountSport" gorm:"column:RewardAmountSport"`         //
	RewardAmountTexas     decimal.Decimal `json:"RewardAmountTexas" gorm:"column:RewardAmountTexas"`         //
}

type ActiveReward struct {
	Id           int             `json:"Id" gorm:"column:Id"`                     //id
	SellerId     int             `json:"SellerId" gorm:"column:SellerId"`         //
	ChannelId    int             `json:"ChannelId" gorm:"column:ChannelId"`       //
	UserId       int             `json:"UserId" gorm:"column:UserId"`             //
	State        int             `json:"State" gorm:"column:State"`               //状态 1待审核,2审核拒绝,3审核通过,4自动通过
	ActiveId     int             `json:"ActiveId" gorm:"column:ActiveId"`         //活动id
	ActiveName   string          `json:"ActiveName" gorm:"column:ActiveName"`     //活动名
	Amount       decimal.Decimal `json:"Amount" gorm:"column:Amount"`             //活动送金
	AuditAccount string          `json:"AuditAccount" gorm:"column:AuditAccount"` //审核账号
	AuditTime    utils.MyString  `json:"AuditTime" gorm:"column:AuditTime"`       //审核时间
	AuditMemo    string          `json:"AuditMemo" gorm:"column:AuditMemo"`       //审核备注
	CreateTime   utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`     //申请时间
	TopAgentId   int             `json:"TopAgentId" gorm:"column:TopAgentId"`     //顶级代理id
}

type AmountChargeLog struct {
	Id           int             `json:"Id" gorm:"column:Id"`                     //id
	UserId       int             `json:"UserId" gorm:"column:UserId"`             //id
	BeforeAmount decimal.Decimal `json:"BeforeAmount" gorm:"column:BeforeAmount"` //变化前余额
	Amount       decimal.Decimal `json:"Amount" gorm:"column:Amount"`             //变化值
	AfterAmount  decimal.Decimal `json:"AfterAmount" gorm:"column:AfterAmount"`   //变化后余额
	Reason       int             `json:"Reason" gorm:"column:Reason"`             //变化原因
	CreateTime   utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"`     //变化时间
	Memo         string          `json:"Memo" gorm:"column:Memo"`                 //备注
	SellerId     int             `json:"SellerId" gorm:"column:SellerId"`         //
	ChannelId    int             `json:"ChannelId" gorm:"column:ChannelId"`       //
}

type VipInfo struct {
	Id                  int             `json:"Id" gorm:"column:Id"`                                   //id
	SellerId            int             `json:"SellerId" gorm:"column:SellerId"`                       //SellerId
	ChannelId           int             `json:"ChannelId" gorm:"column:ChannelId"`                     //
	UserId              int             `json:"UserId" gorm:"column:UserId"`                           //UserId
	VipLevel            int             `json:"VipLevel" gorm:"column:VipLevel"`                       //当前vip等级
	Recharge            decimal.Decimal `json:"Recharge" gorm:"column:Recharge"`                       //累计充值
	LiuSui              decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`                           //累计流水
	KeepLiuSui          decimal.Decimal `json:"KeepLiuSui" gorm:"column:KeepLiuSui"`                   //保级流水
	UpgradeTime         utils.MyString  `json:"UpgradeTime" gorm:"column:UpgradeTime"`                 //升级时间
	UpgradeRewardRecord string          `json:"UpgradeRewardRecord" gorm:"column:UpgradeRewardRecord"` //升级时间
	Withdraw            decimal.Decimal `json:"Withdraw" gorm:"column:Withdraw"`                       //累计提现
	CaiJin              decimal.Decimal `json:"CaiJin" gorm:"column:CaiJin"`                           //累计彩金usdt
	CaiJinTrc           decimal.Decimal `json:"CaiJinTrc" gorm:"column:CaiJinTrc"`                     //累计彩金trx
}

type CaiJinDetail struct {
	Id         int             `json:"Id" gorm:"column:Id"`                 //id
	UserId     int             `json:"UserId" gorm:"column:UserId"`         //UserId
	SType      string          `json:"SType" gorm:"column:SType"`           //SType
	Symbol     string          `json:"Symbol" gorm:"column:Symbol"`         //Symbol
	Amount     decimal.Decimal `json:"Amount" gorm:"column:Amount"`         //Amount
	CSGroup    string          `json:"CSGroup" gorm:"column:CSGroup"`       //CSGroup
	CSId       string          `json:"CSId" gorm:"column:CSId"`             //CSId
	CreateTime utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"` //
	TopAgentId int             `json:"TopAgentId" gorm:"column:TopAgentId"` //
}

type UserDailly struct {
	//Id             int             `json:"Id" gorm:"column:Id"`                         //id
	UserId         int             `json:"UserId" gorm:"column:UserId"`                 //UserId
	ChannelId      int             `json:"ChannelId" gorm:"column:ChannelId"`           //
	SellerId       int             `json:"SellerId" gorm:"column:SellerId"`             //SellerId
	RecordDate     string          `json:"RecordDate" gorm:"column:RecordDate"`         //
	BetTrx         decimal.Decimal `json:"BetTrx" gorm:"column:BetTrx"`                 //trx 下注
	RewardTrx      decimal.Decimal `json:"RewardTrx" gorm:"column:RewardTrx"`           //trx 反奖
	LiuSuiTrx      decimal.Decimal `json:"LiuSuiTrx" gorm:"column:LiuSuiTrx"`           //trx 流水
	BetUsdt        decimal.Decimal `json:"BetUsdt" gorm:"column:BetUsdt"`               //usdt 下注
	RewardUsdt     decimal.Decimal `json:"RewardUsdt" gorm:"column:RewardUsdt"`         //usdt 反奖
	LiuSuiUsdt     decimal.Decimal `json:"LiuSuiUsdt" gorm:"column:LiuSuiUsdt"`         //usdt 流水
	FineLiuSuiTrx  decimal.Decimal `json:"FineLiuSuiTrx" gorm:"column:FineLiuSuiTrx"`   //扣除流水trx
	FineLiuSuiUsdt decimal.Decimal `json:"FineLiuSuiUsdt" gorm:"column:FineLiuSuiUsdt"` //扣除流水usdt
	FineAccount    string          `json:"FineAccount" gorm:"column:FineAccount"`       //扣除流水操作人
	FineTime       *utils.MyString `json:"FineTime" gorm:"column:FineTime"`             //扣除流水时间
	FineMemo       string          `json:"FineMemo" gorm:"column:FineMemo"`             //扣除流水备注
	BetCountTrx    decimal.Decimal `json:"BetCountTrx" gorm:"column:BetCountTrx"`       //
	BetCountUsdt   decimal.Decimal `json:"BetCountUsdt" gorm:"column:BetCountUsdt"`     //
	TotalWinLoss   decimal.Decimal `json:"TotalWinLoss" gorm:"column:TotalWinLoss"`     //今日输赢总额(包含所有游戏) 赢为正 输为负
	TotalCaiJin    decimal.Decimal `json:"TotalCaiJin" gorm:"column:TotalCaiJin"`       //今日赠送usdt彩金总额
	TotalCaiJinTrx decimal.Decimal `json:"TotalCaiJinTrx" gorm:"column:TotalCaiJinTrx"` //今日赠送trx彩金总额
}

type ThirdDianZi struct {
	Id         int             `json:"Id" gorm:"column:Id"`                 //id
	UserId     int             `json:"UserId" gorm:"column:UserId"`         //UserId
	ChannelId  int             `json:"ChannelId" gorm:"column:ChannelId"`   //
	SellerId   int             `json:"SellerId" gorm:"column:SellerId"`     //SellerId
	Brand      string          `json:"Brand" gorm:"column:Brand"`           //三方品牌
	ThirdId    string          `json:"ThirdId" gorm:"column:ThirdId"`       //三方订单号
	GameId     string          `json:"GameId" gorm:"column:GameId"`         //游戏id
	GameName   string          `json:"GameName" gorm:"column:GameName"`     //游戏名称
	BetAmount  decimal.Decimal `json:"BetAmount" gorm:"column:BetAmount"`   //下注金额
	WinAmount  decimal.Decimal `json:"WinAmount" gorm:"column:WinAmount"`   //派奖金额
	ValidBet   decimal.Decimal `json:"ValidBet" gorm:"column:ValidBet"`     //有效投注
	ThirdTime  utils.MyString  `json:"ThirdTime" gorm:"column:ThirdTime"`   //有效投注
	Currency   string          `json:"Currency" gorm:"column:Currency"`     //币种
	RawData    string          `json:"RawData" gorm:"column:RawData"`       //三方原始数据
	State      int             `json:"State" gorm:"column:State"`           //状态
	Fee        decimal.Decimal `json:"Fee" gorm:"column:Fee"`               //状态
	DataState  int             `json:"DataState" gorm:"column:DataState"`   //数据状态
	CreateTime utils.MyString  `json:"CreateTime" gorm:"column:CreateTime"` //同步时间
	CSGroup    string          `json:"CSGroup" gorm:"column:CSGroup"`       //
	CSId       string          `json:"CSId" gorm:"column:CSId"`             //
	TopAgentId int             `json:"TopAgentId" gorm:"column:TopAgentId"` //
}

type ACiRiConfig struct {
	MinAwardLiuShui float32 `json:"MinAwardLiuShui"` //配置
}
