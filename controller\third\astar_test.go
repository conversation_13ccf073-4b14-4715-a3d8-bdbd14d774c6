package third

import (
	"testing"
)

func TestAstar(t *testing.T) {
	// srv := NewAstarSwSrvice("", "", "", "", "")
	// userId := 123456
	// token := srv.userId2token(userId)
	// fmt.Println(token)
	// if token == "" {
	// 	t.Error()
	// }
	// token = url.QueryEscape(token)
	// fmt.Println(token)
	// userId2 := srv.token2UserId(token)
	// fmt.Println(userId2)
	// if userId != userId2 {
	// 	t.Error()
	// }

	// userId2 = srv.token2UserId(`%2FhnTue3RfbPMWaJqixlinXkIPNuqS8kGPK%2BeUdFaZSZi%2Fge1aqCpRS1hdhWX8APE`)
	// fmt.Println(userId2)

}
