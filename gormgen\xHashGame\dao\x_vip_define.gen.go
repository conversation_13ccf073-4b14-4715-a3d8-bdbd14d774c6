// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipDefine(db *gorm.DB, opts ...gen.DOOption) xVipDefine {
	_xVipDefine := xVipDefine{}

	_xVipDefine.xVipDefineDo.UseDB(db, opts...)
	_xVipDefine.xVipDefineDo.UseModel(&model.XVipDefine{})

	tableName := _xVipDefine.xVipDefineDo.TableName()
	_xVipDefine.ALL = field.NewAsterisk(tableName)
	_xVipDefine.ID = field.NewInt32(tableName, "Id")
	_xVipDefine.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipDefine.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipDefine.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xVipDefine.Recharge = field.NewInt32(tableName, "Recharge")
	_xVipDefine.LiuSui = field.NewInt32(tableName, "LiuSui")
	_xVipDefine.KeepLiuSui = field.NewInt32(tableName, "KeepLiuSui")
	_xVipDefine.State = field.NewInt32(tableName, "State")
	_xVipDefine.UpgradeReward = field.NewFloat64(tableName, "UpgradeReward")
	_xVipDefine.MonthlyReward = field.NewFloat64(tableName, "MonthlyReward")
	_xVipDefine.MonthlyLiuSui = field.NewFloat64(tableName, "MonthlyLiuSui")
	_xVipDefine.RewardRateHaXi = field.NewFloat64(tableName, "RewardRateHaXi")
	_xVipDefine.RewardRateHaXiRoulette = field.NewFloat64(tableName, "RewardRateHaXiRoulette")
	_xVipDefine.RewardRateLottery = field.NewFloat64(tableName, "RewardRateLottery")
	_xVipDefine.RewardRateLowLottery = field.NewFloat64(tableName, "RewardRateLowLottery")
	_xVipDefine.RewardRateQiPai = field.NewFloat64(tableName, "RewardRateQiPai")
	_xVipDefine.RewardRateDianZhi = field.NewFloat64(tableName, "RewardRateDianZhi")
	_xVipDefine.RewardRateXiaoYouXi = field.NewFloat64(tableName, "RewardRateXiaoYouXi")
	_xVipDefine.RewardRateCryptoMarket = field.NewFloat64(tableName, "RewardRateCryptoMarket")
	_xVipDefine.RewardRateLive = field.NewFloat64(tableName, "RewardRateLive")
	_xVipDefine.RewardRateSport = field.NewFloat64(tableName, "RewardRateSport")
	_xVipDefine.RewardRateTexas = field.NewFloat64(tableName, "RewardRateTexas")
	_xVipDefine.WeeklyReward = field.NewFloat64(tableName, "WeeklyReward")
	_xVipDefine.WeeklyLiuSui = field.NewFloat64(tableName, "WeeklyLiuSui")

	_xVipDefine.fillFieldMap()

	return _xVipDefine
}

type xVipDefine struct {
	xVipDefineDo xVipDefineDo

	ALL                    field.Asterisk
	ID                     field.Int32
	SellerID               field.Int32
	ChannelID              field.Int32
	VipLevel               field.Int32   // vip等级
	Recharge               field.Int32   // 累计存款
	LiuSui                 field.Int32   // 累计流水
	KeepLiuSui             field.Int32   // 保级流水
	State                  field.Int32   // 状态 1启用,2禁用
	UpgradeReward          field.Float64 // 升级礼金
	MonthlyReward          field.Float64 // 每月礼金
	MonthlyLiuSui          field.Float64 // 每月礼金流水
	RewardRateHaXi         field.Float64 // 哈希返点
	RewardRateHaXiRoulette field.Float64 // 哈希轮盘返点
	RewardRateLottery      field.Float64 // 彩票返点
	RewardRateLowLottery   field.Float64 // 低频彩返点
	RewardRateQiPai        field.Float64 // 棋牌返点
	RewardRateDianZhi      field.Float64 // 电子返点
	RewardRateXiaoYouXi    field.Float64 // 小游戏返点
	RewardRateCryptoMarket field.Float64 // 加密市场返点
	RewardRateLive         field.Float64 // 真人返点
	RewardRateSport        field.Float64 // 体育返点
	RewardRateTexas        field.Float64 // 德州返点
	WeeklyReward           field.Float64 // 每周礼金
	WeeklyLiuSui           field.Float64 // 每周礼金流水

	fieldMap map[string]field.Expr
}

func (x xVipDefine) Table(newTableName string) *xVipDefine {
	x.xVipDefineDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipDefine) As(alias string) *xVipDefine {
	x.xVipDefineDo.DO = *(x.xVipDefineDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipDefine) updateTableName(table string) *xVipDefine {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.Recharge = field.NewInt32(table, "Recharge")
	x.LiuSui = field.NewInt32(table, "LiuSui")
	x.KeepLiuSui = field.NewInt32(table, "KeepLiuSui")
	x.State = field.NewInt32(table, "State")
	x.UpgradeReward = field.NewFloat64(table, "UpgradeReward")
	x.MonthlyReward = field.NewFloat64(table, "MonthlyReward")
	x.MonthlyLiuSui = field.NewFloat64(table, "MonthlyLiuSui")
	x.RewardRateHaXi = field.NewFloat64(table, "RewardRateHaXi")
	x.RewardRateHaXiRoulette = field.NewFloat64(table, "RewardRateHaXiRoulette")
	x.RewardRateLottery = field.NewFloat64(table, "RewardRateLottery")
	x.RewardRateLowLottery = field.NewFloat64(table, "RewardRateLowLottery")
	x.RewardRateQiPai = field.NewFloat64(table, "RewardRateQiPai")
	x.RewardRateDianZhi = field.NewFloat64(table, "RewardRateDianZhi")
	x.RewardRateXiaoYouXi = field.NewFloat64(table, "RewardRateXiaoYouXi")
	x.RewardRateCryptoMarket = field.NewFloat64(table, "RewardRateCryptoMarket")
	x.RewardRateLive = field.NewFloat64(table, "RewardRateLive")
	x.RewardRateSport = field.NewFloat64(table, "RewardRateSport")
	x.RewardRateTexas = field.NewFloat64(table, "RewardRateTexas")
	x.WeeklyReward = field.NewFloat64(table, "WeeklyReward")
	x.WeeklyLiuSui = field.NewFloat64(table, "WeeklyLiuSui")

	x.fillFieldMap()

	return x
}

func (x *xVipDefine) WithContext(ctx context.Context) *xVipDefineDo {
	return x.xVipDefineDo.WithContext(ctx)
}

func (x xVipDefine) TableName() string { return x.xVipDefineDo.TableName() }

func (x xVipDefine) Alias() string { return x.xVipDefineDo.Alias() }

func (x xVipDefine) Columns(cols ...field.Expr) gen.Columns { return x.xVipDefineDo.Columns(cols...) }

func (x *xVipDefine) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipDefine) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 24)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["Recharge"] = x.Recharge
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["KeepLiuSui"] = x.KeepLiuSui
	x.fieldMap["State"] = x.State
	x.fieldMap["UpgradeReward"] = x.UpgradeReward
	x.fieldMap["MonthlyReward"] = x.MonthlyReward
	x.fieldMap["MonthlyLiuSui"] = x.MonthlyLiuSui
	x.fieldMap["RewardRateHaXi"] = x.RewardRateHaXi
	x.fieldMap["RewardRateHaXiRoulette"] = x.RewardRateHaXiRoulette
	x.fieldMap["RewardRateLottery"] = x.RewardRateLottery
	x.fieldMap["RewardRateLowLottery"] = x.RewardRateLowLottery
	x.fieldMap["RewardRateQiPai"] = x.RewardRateQiPai
	x.fieldMap["RewardRateDianZhi"] = x.RewardRateDianZhi
	x.fieldMap["RewardRateXiaoYouXi"] = x.RewardRateXiaoYouXi
	x.fieldMap["RewardRateCryptoMarket"] = x.RewardRateCryptoMarket
	x.fieldMap["RewardRateLive"] = x.RewardRateLive
	x.fieldMap["RewardRateSport"] = x.RewardRateSport
	x.fieldMap["RewardRateTexas"] = x.RewardRateTexas
	x.fieldMap["WeeklyReward"] = x.WeeklyReward
	x.fieldMap["WeeklyLiuSui"] = x.WeeklyLiuSui
}

func (x xVipDefine) clone(db *gorm.DB) xVipDefine {
	x.xVipDefineDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipDefine) replaceDB(db *gorm.DB) xVipDefine {
	x.xVipDefineDo.ReplaceDB(db)
	return x
}

type xVipDefineDo struct{ gen.DO }

func (x xVipDefineDo) Debug() *xVipDefineDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipDefineDo) WithContext(ctx context.Context) *xVipDefineDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipDefineDo) ReadDB() *xVipDefineDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipDefineDo) WriteDB() *xVipDefineDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipDefineDo) Session(config *gorm.Session) *xVipDefineDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipDefineDo) Clauses(conds ...clause.Expression) *xVipDefineDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipDefineDo) Returning(value interface{}, columns ...string) *xVipDefineDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipDefineDo) Not(conds ...gen.Condition) *xVipDefineDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipDefineDo) Or(conds ...gen.Condition) *xVipDefineDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipDefineDo) Select(conds ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipDefineDo) Where(conds ...gen.Condition) *xVipDefineDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipDefineDo) Order(conds ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipDefineDo) Distinct(cols ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipDefineDo) Omit(cols ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipDefineDo) Join(table schema.Tabler, on ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipDefineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipDefineDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipDefineDo) Group(cols ...field.Expr) *xVipDefineDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipDefineDo) Having(conds ...gen.Condition) *xVipDefineDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipDefineDo) Limit(limit int) *xVipDefineDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipDefineDo) Offset(offset int) *xVipDefineDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipDefineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipDefineDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipDefineDo) Unscoped() *xVipDefineDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipDefineDo) Create(values ...*model.XVipDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipDefineDo) CreateInBatches(values []*model.XVipDefine, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipDefineDo) Save(values ...*model.XVipDefine) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipDefineDo) First() (*model.XVipDefine, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDefine), nil
	}
}

func (x xVipDefineDo) Take() (*model.XVipDefine, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDefine), nil
	}
}

func (x xVipDefineDo) Last() (*model.XVipDefine, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDefine), nil
	}
}

func (x xVipDefineDo) Find() ([]*model.XVipDefine, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipDefine), err
}

func (x xVipDefineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipDefine, err error) {
	buf := make([]*model.XVipDefine, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipDefineDo) FindInBatches(result *[]*model.XVipDefine, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipDefineDo) Attrs(attrs ...field.AssignExpr) *xVipDefineDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipDefineDo) Assign(attrs ...field.AssignExpr) *xVipDefineDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipDefineDo) Joins(fields ...field.RelationField) *xVipDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipDefineDo) Preload(fields ...field.RelationField) *xVipDefineDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipDefineDo) FirstOrInit() (*model.XVipDefine, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDefine), nil
	}
}

func (x xVipDefineDo) FirstOrCreate() (*model.XVipDefine, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDefine), nil
	}
}

func (x xVipDefineDo) FindByPage(offset int, limit int) (result []*model.XVipDefine, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipDefineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipDefineDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipDefineDo) Delete(models ...*model.XVipDefine) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipDefineDo) withDO(do gen.Dao) *xVipDefineDo {
	x.DO = *do.(*gen.DO)
	return x
}
