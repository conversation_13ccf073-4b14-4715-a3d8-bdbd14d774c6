// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentDailly = "x_agent_dailly"

// XAgentDailly mapped from table <x_agent_dailly>
type XAgentDailly struct {
	ID                             int32     `gorm:"column:Id;not null" json:"Id"`
	UserID                         int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	ChannelID                      int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	Address                        string    `gorm:"column:Address" json:"Address"`
	RecordDate                     time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	SellerID                       int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	BetTrx                         float64   `gorm:"column:BetTrx;default:0.000000;comment:Trx下注" json:"BetTrx"`                          // Trx下注
	BetUsdt                        float64   `gorm:"column:BetUsdt;default:0.000000;comment:usdt下注" json:"BetUsdt"`                       // usdt下注
	RewardTrx                      float64   `gorm:"column:RewardTrx;default:0.000000;comment:trx返奖" json:"RewardTrx"`                    // trx返奖
	RewardUsdt                     float64   `gorm:"column:RewardUsdt;default:0.000000;comment:usdt返奖" json:"RewardUsdt"`                 // usdt返奖
	LiuSuiTrx                      float64   `gorm:"column:LiuSuiTrx;default:0.000000;comment:trx流水" json:"LiuSuiTrx"`                    // trx流水
	LiuSuiUsdt                     float64   `gorm:"column:LiuSuiUsdt;default:0.000000;comment:usdt流水" json:"LiuSuiUsdt"`                 // usdt流水
	DictBetTrx                     float64   `gorm:"column:DictBetTrx;default:0.000000;comment:Trx下注" json:"DictBetTrx"`                  // Trx下注
	DictBetUsdt                    float64   `gorm:"column:DictBetUsdt;default:0.000000;comment:usdt下注" json:"DictBetUsdt"`               // usdt下注
	DictLiuSuiUsdt                 float64   `gorm:"column:DictLiuSuiUsdt;default:0.000000;comment:usdt流水" json:"DictLiuSuiUsdt"`         // usdt流水
	DictLiuSuiTrx                  float64   `gorm:"column:DictLiuSuiTrx;default:0.000000;comment:trx流水" json:"DictLiuSuiTrx"`            // trx流水
	DictRewardTrx                  float64   `gorm:"column:DictRewardTrx;default:0.000000;comment:trx返奖" json:"DictRewardTrx"`            // trx返奖
	DictRewardUsdt                 float64   `gorm:"column:DictRewardUsdt;default:0.000000;comment:usdt返奖" json:"DictRewardUsdt"`         // usdt返奖
	TotalCommissionTrx             float64   `gorm:"column:TotalCommissionTrx;default:0.000000;comment:当日产生佣金" json:"TotalCommissionTrx"` // 当日产生佣金
	TotalCommissionUsdt            float64   `gorm:"column:TotalCommissionUsdt;default:0.000000" json:"TotalCommissionUsdt"`
	GetedCommissionTrx             float64   `gorm:"column:GetedCommissionTrx;default:0.000000;comment:当日领取佣金" json:"GetedCommissionTrx"` // 当日领取佣金
	GetedCommissionUsdt            float64   `gorm:"column:GetedCommissionUsdt;default:0.000000" json:"GetedCommissionUsdt"`
	FineCommissionTrx              float64   `gorm:"column:FineCommissionTrx;default:0.000000;comment:当日罚没佣金" json:"FineCommissionTrx"` // 当日罚没佣金
	FineCommissionUsdt             float64   `gorm:"column:FineCommissionUsdt;default:0.000000" json:"FineCommissionUsdt"`
	NewChildCount                  int32     `gorm:"column:NewChildCount;comment:当日新增下级" json:"NewChildCount"`           // 当日新增下级
	DictNewChildCount              int32     `gorm:"column:DictNewChildCount;comment:当日新增直属下级" json:"DictNewChildCount"` // 当日新增直属下级
	SelfBetTrx                     float64   `gorm:"column:SelfBetTrx;default:0.000000" json:"SelfBetTrx"`
	SelfBetUsdt                    float64   `gorm:"column:SelfBetUsdt;default:0.000000" json:"SelfBetUsdt"`
	SelfLiuSuiTrx                  float64   `gorm:"column:SelfLiuSuiTrx;default:0.000000" json:"SelfLiuSuiTrx"`
	SelfLiuSuiUsdt                 float64   `gorm:"column:SelfLiuSuiUsdt;default:0.000000" json:"SelfLiuSuiUsdt"`
	SelfRewardTrx                  float64   `gorm:"column:SelfRewardTrx;default:0.000000" json:"SelfRewardTrx"`
	SelfRewardUsdt                 float64   `gorm:"column:SelfRewardUsdt;default:0.000000" json:"SelfRewardUsdt"`
	NewLiuSuiTrx                   float64   `gorm:"column:NewLiuSuiTrx;default:0.000000;comment:新版代理trx业绩,包含直属的业绩" json:"NewLiuSuiTrx"`   // 新版代理trx业绩,包含直属的业绩
	NewDictLiuSuiTrx               float64   `gorm:"column:NewDictLiuSuiTrx;default:0.000000;comment:新版代理trx直属业绩" json:"NewDictLiuSuiTrx"` // 新版代理trx直属业绩
	NewLiuSuiHaXiRouletteTrx       float64   `gorm:"column:NewLiuSuiHaXiRouletteTrx;default:0.000000" json:"NewLiuSuiHaXiRouletteTrx"`
	NewLiuSuiHaXiRouletteDictTrx   float64   `gorm:"column:NewLiuSuiHaXiRouletteDictTrx;default:0.000000" json:"NewLiuSuiHaXiRouletteDictTrx"`
	NewLiuSui                      float64   `gorm:"column:NewLiuSui;default:0.000000;comment:新版代理业绩,包含直属业绩" json:"NewLiuSui"`              // 新版代理业绩,包含直属业绩
	NewLiuSuiDict                  float64   `gorm:"column:NewLiuSuiDict;default:0.000000;comment:新版代理直属业绩" json:"NewLiuSuiDict"`           // 新版代理直属业绩
	NewLiuSuiHaXi                  float64   `gorm:"column:NewLiuSuiHaXi;default:0.000000;comment:新版代理哈希业绩,包含直属" json:"NewLiuSuiHaXi"`      // 新版代理哈希业绩,包含直属
	NewLiuSuiHaXiDict              float64   `gorm:"column:NewLiuSuiHaXiDict;default:0.000000;comment:新版代理哈希直属业绩" json:"NewLiuSuiHaXiDict"` // 新版代理哈希直属业绩
	NewLiuSuiHaXiRoulette          float64   `gorm:"column:NewLiuSuiHaXiRoulette;default:0.000000" json:"NewLiuSuiHaXiRoulette"`
	NewLiuSuiHaXiRouletteDict      float64   `gorm:"column:NewLiuSuiHaXiRouletteDict;default:0.000000" json:"NewLiuSuiHaXiRouletteDict"`
	NewLiuSuiLottery               float64   `gorm:"column:NewLiuSuiLottery;default:0.000000" json:"NewLiuSuiLottery"`
	NewLiuSuiLotteryDict           float64   `gorm:"column:NewLiuSuiLotteryDict;default:0.000000" json:"NewLiuSuiLotteryDict"`
	NewLiuSuiLowLottery            float64   `gorm:"column:NewLiuSuiLowLottery;default:0.000000" json:"NewLiuSuiLowLottery"`
	NewLiuSuiLowLotteryDict        float64   `gorm:"column:NewLiuSuiLowLotteryDict;default:0.000000" json:"NewLiuSuiLowLotteryDict"`
	NewLiuSuiLiuHeLottery          float64   `gorm:"column:NewLiuSuiLiuHeLottery;default:0.000000" json:"NewLiuSuiLiuHeLottery"`
	NewLiuSuiLiuHeLotteryDict      float64   `gorm:"column:NewLiuSuiLiuHeLotteryDict;default:0.000000" json:"NewLiuSuiLiuHeLotteryDict"`
	NewLiuSuiQiPai                 float64   `gorm:"column:NewLiuSuiQiPai;default:0.000000" json:"NewLiuSuiQiPai"`
	NewLiuSuiQiPaiDict             float64   `gorm:"column:NewLiuSuiQiPaiDict;default:0.000000" json:"NewLiuSuiQiPaiDict"`
	NewLiuSuiDianZhi               float64   `gorm:"column:NewLiuSuiDianZhi;default:0.000000" json:"NewLiuSuiDianZhi"`
	NewLiuSuiDianZhiDict           float64   `gorm:"column:NewLiuSuiDianZhiDict;default:0.000000" json:"NewLiuSuiDianZhiDict"`
	NewLiuSuiXiaoYouXi             float64   `gorm:"column:NewLiuSuiXiaoYouXi;default:0.000000" json:"NewLiuSuiXiaoYouXi"`
	NewLiuSuiXiaoYouXiDict         float64   `gorm:"column:NewLiuSuiXiaoYouXiDict;default:0.000000" json:"NewLiuSuiXiaoYouXiDict"`
	NewLiuSuiCryptoMarket          float64   `gorm:"column:NewLiuSuiCryptoMarket;default:0.000000" json:"NewLiuSuiCryptoMarket"`
	NewLiuSuiCryptoMarketDict      float64   `gorm:"column:NewLiuSuiCryptoMarketDict;default:0.000000" json:"NewLiuSuiCryptoMarketDict"`
	NewLiuSuiLive                  float64   `gorm:"column:NewLiuSuiLive;default:0.000000" json:"NewLiuSuiLive"`
	NewLiuSuiLiveDict              float64   `gorm:"column:NewLiuSuiLiveDict;default:0.000000" json:"NewLiuSuiLiveDict"`
	NewLiuSuiSport                 float64   `gorm:"column:NewLiuSuiSport;default:0.000000" json:"NewLiuSuiSport"`
	NewLiuSuiSportDict             float64   `gorm:"column:NewLiuSuiSportDict;default:0.000000" json:"NewLiuSuiSportDict"`
	NewLiuSuiTexas                 float64   `gorm:"column:NewLiuSuiTexas;default:0.000000" json:"NewLiuSuiTexas"`
	NewLiuSuiTexasDict             float64   `gorm:"column:NewLiuSuiTexasDict;default:0.000000" json:"NewLiuSuiTexasDict"`
	NewCommissionTrx               float64   `gorm:"column:NewCommissionTrx;default:0.000000;comment:佣金trx" json:"NewCommissionTrx"` // 佣金trx
	NewCommission                  float64   `gorm:"column:NewCommission;default:0.000000;comment:佣金" json:"NewCommission"`          // 佣金
	NewCommissionHaXi              float64   `gorm:"column:NewCommissionHaXi;default:0.000000" json:"NewCommissionHaXi"`
	NewCommissionHaXiRoulette      float64   `gorm:"column:NewCommissionHaXiRoulette;default:0.000000" json:"NewCommissionHaXiRoulette"`
	NewCommissionLottery           float64   `gorm:"column:NewCommissionLottery;default:0.000000" json:"NewCommissionLottery"`
	NewCommissionLowLottery        float64   `gorm:"column:NewCommissionLowLottery;default:0.000000" json:"NewCommissionLowLottery"`
	NewCommissionLiuHeLottery      float64   `gorm:"column:NewCommissionLiuHeLottery;default:0.000000" json:"NewCommissionLiuHeLottery"`
	NewCommissionQiPai             float64   `gorm:"column:NewCommissionQiPai;default:0.000000" json:"NewCommissionQiPai"`
	NewCommissionDianZhi           float64   `gorm:"column:NewCommissionDianZhi;default:0.000000" json:"NewCommissionDianZhi"`
	NewCommissionXiaoYouXi         float64   `gorm:"column:NewCommissionXiaoYouXi;default:0.000000" json:"NewCommissionXiaoYouXi"`
	NewCommissionCryptoMarket      float64   `gorm:"column:NewCommissionCryptoMarket;default:0.000000" json:"NewCommissionCryptoMarket"`
	NewCommissionLive              float64   `gorm:"column:NewCommissionLive;default:0.000000" json:"NewCommissionLive"`
	NewCommissionSport             float64   `gorm:"column:NewCommissionSport;default:0.000000" json:"NewCommissionSport"`
	NewCommissionTexas             float64   `gorm:"column:NewCommissionTexas;default:0.000000" json:"NewCommissionTexas"`
	NewStateTrx                    int32     `gorm:"column:NewStateTrx;default:1;comment:1当天还没结算完,2结算完未领取 ,3已领取" json:"NewStateTrx"` // 1当天还没结算完,2结算完未领取 ,3已领取
	NewState                       int32     `gorm:"column:NewState;default:1;comment:1当天还没结算完,2结算完未领取 ,3已领取" json:"NewState"`       // 1当天还没结算完,2结算完未领取 ,3已领取
	LastUpdateTime                 time.Time `gorm:"column:LastUpdateTime" json:"LastUpdateTime"`
	NewSelfLiuSuiTrx               float64   `gorm:"column:NewSelfLiuSuiTrx;default:0.000000" json:"NewSelfLiuSuiTrx"`
	NewSelfLiuSuiHaXi              float64   `gorm:"column:NewSelfLiuSuiHaXi;default:0.000000" json:"NewSelfLiuSuiHaXi"`
	NewSelfLiuSuiHaXiRouletteTrx   float64   `gorm:"column:NewSelfLiuSuiHaXiRouletteTrx;default:0.000000" json:"NewSelfLiuSuiHaXiRouletteTrx"`
	NewSelfLiuSuiHaXiRoulette      float64   `gorm:"column:NewSelfLiuSuiHaXiRoulette;default:0.000000" json:"NewSelfLiuSuiHaXiRoulette"`
	NewSelfLiuSuiLottery           float64   `gorm:"column:NewSelfLiuSuiLottery;default:0.000000" json:"NewSelfLiuSuiLottery"`
	NewSelfLiuSuiLowLottery        float64   `gorm:"column:NewSelfLiuSuiLowLottery;default:0.000000" json:"NewSelfLiuSuiLowLottery"`
	NewSelfLiuSuiLiuHeLottery      float64   `gorm:"column:NewSelfLiuSuiLiuHeLottery;default:0.000000" json:"NewSelfLiuSuiLiuHeLottery"`
	NewSelfLiuSuiQiPai             float64   `gorm:"column:NewSelfLiuSuiQiPai;default:0.000000" json:"NewSelfLiuSuiQiPai"`
	NewSelfLiuSuiDianZhi           float64   `gorm:"column:NewSelfLiuSuiDianZhi;default:0.000000" json:"NewSelfLiuSuiDianZhi"`
	NewSelfLiuSuiXiaoYouXi         float64   `gorm:"column:NewSelfLiuSuiXiaoYouXi;default:0.000000" json:"NewSelfLiuSuiXiaoYouXi"`
	NewSelfLiuSuiCryptoMarket      float64   `gorm:"column:NewSelfLiuSuiCryptoMarket;default:0.000000" json:"NewSelfLiuSuiCryptoMarket"`
	NewSelfLiuSuiLive              float64   `gorm:"column:NewSelfLiuSuiLive;default:0.000000" json:"NewSelfLiuSuiLive"`
	NewSelfLiuSuiSport             float64   `gorm:"column:NewSelfLiuSuiSport;default:0.000000" json:"NewSelfLiuSuiSport"`
	NewSelfLiuSuiTexas             float64   `gorm:"column:NewSelfLiuSuiTexas;default:0.000000" json:"NewSelfLiuSuiTexas"`
	TmpCommissionTrx               float64   `gorm:"column:TmpCommissionTrx;default:0.000000" json:"TmpCommissionTrx"`
	TmpCommission                  float64   `gorm:"column:TmpCommission;default:0.000000" json:"TmpCommission"`
	LiuSuiHashTrxT1                float64   `gorm:"column:LiuSuiHashTrx_t1;default:0.000000" json:"LiuSuiHashTrx_t1"`
	LiuSuiHashUsdtT1               float64   `gorm:"column:LiuSuiHashUsdt_t1;default:0.000000" json:"LiuSuiHashUsdt_t1"`
	LiuSuiHaXiRouletteTrxT1        float64   `gorm:"column:LiuSuiHaXiRouletteTrx_t1;default:0.000000" json:"LiuSuiHaXiRouletteTrx_t1"`
	LiuSuiHaXiRouletteUsdtT1       float64   `gorm:"column:LiuSuiHaXiRouletteUsdt_t1;default:0.000000" json:"LiuSuiHaXiRouletteUsdt_t1"`
	LiuSuiDianZhiUsdtT1            float64   `gorm:"column:LiuSuiDianZhiUsdt_t1;default:0.000000" json:"LiuSuiDianZhiUsdt_t1"`
	LiuSuiQiPaiUsdtT1              float64   `gorm:"column:LiuSuiQiPaiUsdt_t1;default:0.000000" json:"LiuSuiQiPaiUsdt_t1"`
	LiuSuiLiveUsdtT1               float64   `gorm:"column:LiuSuiLiveUsdt_t1;default:0.000000" json:"LiuSuiLiveUsdt_t1"`
	LiuSuiChainUsdtT1              float64   `gorm:"column:LiuSuiChainUsdt_t1;default:0.000000" json:"LiuSuiChainUsdt_t1"`
	LiuSuiCryptoMarketUsdtT1       float64   `gorm:"column:LiuSuiCryptoMarketUsdt_t1;default:0.000000" json:"LiuSuiCryptoMarketUsdt_t1"`
	LiuSuiLotteryUsdtT1            float64   `gorm:"column:LiuSuiLotteryUsdt_t1;default:0.000000" json:"LiuSuiLotteryUsdt_t1"`
	LiuSuiLowLotteryUsdtT1         float64   `gorm:"column:LiuSuiLowLotteryUsdt_t1;default:0.000000" json:"LiuSuiLowLotteryUsdt_t1"`
	LiuSuiLiuHeLotteryUsdtT1       float64   `gorm:"column:LiuSuiLiuHeLotteryUsdt_t1;default:0.000000" json:"LiuSuiLiuHeLotteryUsdt_t1"`
	LiuSuiTexasUsdtT1              float64   `gorm:"column:LiuSuiTexasUsdt_t1;default:0.000000" json:"LiuSuiTexasUsdt_t1"`
	SelfLiuSuiHashTrxT1            float64   `gorm:"column:SelfLiuSuiHashTrx_t1;default:0.000000" json:"SelfLiuSuiHashTrx_t1"`
	SelfLiuSuiHashUsdtT1           float64   `gorm:"column:SelfLiuSuiHashUsdt_t1;default:0.000000" json:"SelfLiuSuiHashUsdt_t1"`
	SelfLiuSuiHaXiRouletteTrxT1    float64   `gorm:"column:SelfLiuSuiHaXiRouletteTrx_t1;default:0.000000" json:"SelfLiuSuiHaXiRouletteTrx_t1"`
	SelfLiuSuiHaXiRouletteUsdtT1   float64   `gorm:"column:SelfLiuSuiHaXiRouletteUsdt_t1;default:0.000000" json:"SelfLiuSuiHaXiRouletteUsdt_t1"`
	SelfLiuSuiDianZhiUsdtT1        float64   `gorm:"column:SelfLiuSuiDianZhiUsdt_t1;default:0.000000" json:"SelfLiuSuiDianZhiUsdt_t1"`
	SelfLiuSuiQiPaiUsdtT1          float64   `gorm:"column:SelfLiuSuiQiPaiUsdt_t1;default:0.000000" json:"SelfLiuSuiQiPaiUsdt_t1"`
	SelfLiuSuiLiveUsdtT1           float64   `gorm:"column:SelfLiuSuiLiveUsdt_t1;default:0.000000" json:"SelfLiuSuiLiveUsdt_t1"`
	SelfLiuSuiChainUsdtT1          float64   `gorm:"column:SelfLiuSuiChainUsdt_t1;default:0.000000" json:"SelfLiuSuiChainUsdt_t1"`
	SelfLiuSuiCryptoMarketUsdtT1   float64   `gorm:"column:SelfLiuSuiCryptoMarketUsdt_t1;default:0.000000" json:"SelfLiuSuiCryptoMarketUsdt_t1"`
	SelfLiuSuiLotteryUsdtT1        float64   `gorm:"column:SelfLiuSuiLotteryUsdt_t1;default:0.000000" json:"SelfLiuSuiLotteryUsdt_t1"`
	SelfLiuSuiLowLotteryUsdtT1     float64   `gorm:"column:SelfLiuSuiLowLotteryUsdt_t1;default:0.000000" json:"SelfLiuSuiLowLotteryUsdt_t1"`
	SelfLiuSuiLiuHeLotteryUsdtT1   float64   `gorm:"column:SelfLiuSuiLiuHeLotteryUsdt_t1;default:0.000000" json:"SelfLiuSuiLiuHeLotteryUsdt_t1"`
	LiuSuiSportUsdtT1              float64   `gorm:"column:LiuSuiSportUsdt_t1;default:0.000000" json:"LiuSuiSportUsdt_t1"`
	SelfLiuSuiSportUsdtT1          float64   `gorm:"column:SelfLiuSuiSportUsdt_t1;default:0.000000" json:"SelfLiuSuiSportUsdt_t1"`
	SelfLiuSuiTexasUsdtT1          float64   `gorm:"column:SelfLiuSuiTexasUsdt_t1;default:0.000000" json:"SelfLiuSuiTexasUsdt_t1"`
	DirectLiuSuiHashTrxT1          float64   `gorm:"column:DirectLiuSuiHashTrx_t1;default:0.000000" json:"DirectLiuSuiHashTrx_t1"`
	DirectLiuSuiHashUsdtT1         float64   `gorm:"column:DirectLiuSuiHashUsdt_t1;default:0.000000" json:"DirectLiuSuiHashUsdt_t1"`
	DirectLiuSuiHaXiRouletteTrxT1  float64   `gorm:"column:DirectLiuSuiHaXiRouletteTrx_t1;default:0.000000" json:"DirectLiuSuiHaXiRouletteTrx_t1"`
	DirectLiuSuiHaXiRouletteUsdtT1 float64   `gorm:"column:DirectLiuSuiHaXiRouletteUsdt_t1;default:0.000000" json:"DirectLiuSuiHaXiRouletteUsdt_t1"`
	DirectLiuSuiDianZhiUsdtT1      float64   `gorm:"column:DirectLiuSuiDianZhiUsdt_t1;default:0.000000" json:"DirectLiuSuiDianZhiUsdt_t1"`
	DirectLiuSuiQiPaiUsdtT1        float64   `gorm:"column:DirectLiuSuiQiPaiUsdt_t1;default:0.000000" json:"DirectLiuSuiQiPaiUsdt_t1"`
	DirectLiuSuiLiveUsdtT1         float64   `gorm:"column:DirectLiuSuiLiveUsdt_t1;default:0.000000" json:"DirectLiuSuiLiveUsdt_t1"`
	DirectLiuSuiChainUsdtT1        float64   `gorm:"column:DirectLiuSuiChainUsdt_t1;default:0.000000" json:"DirectLiuSuiChainUsdt_t1"`
	DirectLiuSuiCryptoMarketUsdtT1 float64   `gorm:"column:DirectLiuSuiCryptoMarketUsdt_t1;default:0.000000" json:"DirectLiuSuiCryptoMarketUsdt_t1"`
	DirectLiuSuiLotteryUsdtT1      float64   `gorm:"column:DirectLiuSuiLotteryUsdt_t1;default:0.000000" json:"DirectLiuSuiLotteryUsdt_t1"`
	DirectLiuSuiLowLotteryUsdtT1   float64   `gorm:"column:DirectLiuSuiLowLotteryUsdt_t1;default:0.000000" json:"DirectLiuSuiLowLotteryUsdt_t1"`
	DirectLiuSuiLiuHeLotteryUsdtT1 float64   `gorm:"column:DirectLiuSuiLiuHeLotteryUsdt_t1;default:0.000000" json:"DirectLiuSuiLiuHeLotteryUsdt_t1"`
	DirectLiuSuiSportUsdtT1        float64   `gorm:"column:DirectLiuSuiSportUsdt_t1;default:0.000000" json:"DirectLiuSuiSportUsdt_t1"`
	DirectLiuSuiTexasUsdtT1        float64   `gorm:"column:DirectLiuSuiTexasUsdt_t1;default:0.000000" json:"DirectLiuSuiTexasUsdt_t1"`
	SelfCommissionUsdtT1           float64   `gorm:"column:SelfCommissionUsdt_t1;default:0.000000" json:"SelfCommissionUsdt_t1"`
	DirectCommissionUsdtT1         float64   `gorm:"column:DirectCommissionUsdt_t1;default:0.000000" json:"DirectCommissionUsdt_t1"`
	TeamCommissionUsdtT1           float64   `gorm:"column:TeamCommissionUsdt_t1;default:0.000000" json:"TeamCommissionUsdt_t1"`
	SelfCommissionTrxT1            float64   `gorm:"column:SelfCommissionTrx_t1;default:0.000000" json:"SelfCommissionTrx_t1"`
	DirectCommissionTrxT1          float64   `gorm:"column:DirectCommissionTrx_t1;default:0.000000" json:"DirectCommissionTrx_t1"`
	TeamCommissionTrxT1            float64   `gorm:"column:TeamCommissionTrx_t1;default:0.000000" json:"TeamCommissionTrx_t1"`
	TotalCommissionTrxT1           float64   `gorm:"column:TotalCommissionTrx_t1;default:0.000000;comment:独立代理—历史总佣金" json:"TotalCommissionTrx_t1"`            // 独立代理—历史总佣金
	FineCommissionTrxT1            float64   `gorm:"column:FineCommissionTrx_t1;default:0.000000;comment:独立代理-审核拒绝,罚没佣金" json:"FineCommissionTrx_t1"`          // 独立代理-审核拒绝,罚没佣金
	AvailableCommissionTrxT1       float64   `gorm:"column:AvailableCommissionTrx_t1;default:0.000000;comment:独立代理-可领取佣金trx" json:"AvailableCommissionTrx_t1"` // 独立代理-可领取佣金trx
	BackCommissionTrxT1            float64   `gorm:"column:BackCommissionTrx_t1;default:0.000000;comment:独立代理-退回佣金" json:"BackCommissionTrx_t1"`               // 独立代理-退回佣金
	GetedCommissionTrxT1           float64   `gorm:"column:GetedCommissionTrx_t1;default:0.000000;comment:独立代理-已领取佣金" json:"GetedCommissionTrx_t1"`            // 独立代理-已领取佣金
	TotalCommissionUsdtT1          float64   `gorm:"column:TotalCommissionUsdt_t1;default:0.000000" json:"TotalCommissionUsdt_t1"`
	FineCommissionUsdtT1           float64   `gorm:"column:FineCommissionUsdt_t1;default:0.000000" json:"FineCommissionUsdt_t1"`
	AvailableCommissionUsdtT1      float64   `gorm:"column:AvailableCommissionUsdt_t1;default:0.000000" json:"AvailableCommissionUsdt_t1"`
	BackCommissionUsdtT1           float64   `gorm:"column:BackCommissionUsdt_t1;default:0.000000" json:"BackCommissionUsdt_t1"`
	GetedCommissionUsdtT1          float64   `gorm:"column:GetedCommissionUsdt_t1;default:0.000000" json:"GetedCommissionUsdt_t1"`
}

// TableName XAgentDailly's table name
func (*XAgentDailly) TableName() string {
	return TableNameXAgentDailly
}
