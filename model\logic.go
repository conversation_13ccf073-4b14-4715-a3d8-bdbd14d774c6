package model

import (
	"database/sql/driver"
	"fmt"
	"github.com/shopspring/decimal"
	"time"
)

type QUser struct {
	Id        int `json:"Id" gorm:"column:Id"`               //id
	UserId    int `json:"UserId" gorm:"column:UserId"`       //用户id
	IsTest    int `json:"IsTest" gorm:"column:IsTest"`       //是否测试账号 1是 2 不是
	SellerId  int `json:"SellerId" gorm:"column:SellerId"`   //代理id
	ChannelId int `json:"ChannelId" gorm:"column:ChannelId"` //活动渠道id
}

type BetReport struct {
	BetSum decimal.Decimal `json:"BetSum" gorm:"column:BetSum"` //id
}

type VipWeek struct {
	UserId          int             `json:"UserId" gorm:"column:UserId"`                   //
	LiuSui          decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`                   //
	LiuSuiHaXi      decimal.Decimal `json:"LiuSuiHaXi" gorm:"column:LiuSuiHaXi"`           //
	LiuSuiLottery   decimal.Decimal `json:"LiuSuiLottery" gorm:"column:LiuSuiLottery"`     //
	LiuSuiQiPai     decimal.Decimal `json:"LiuSuiQiPai" gorm:"column:LiuSuiQiPai"`         //
	LiuSuiDianZhi   decimal.Decimal `json:"LiuSuiDianZhi" gorm:"column:LiuSuiDianZhi"`     //
	LiuSuiXiaoYouXi decimal.Decimal `json:"LiuSuiXiaoYouXi" gorm:"column:LiuSuiXiaoYouXi"` //
	LiuSuiLive      decimal.Decimal `json:"LiuSuiLive" gorm:"column:LiuSuiLive"`           //
	LiuSuiSport     decimal.Decimal `json:"LiuSuiSport" gorm:"column:LiuSuiSport"`         //
	LiuSuiTexas     decimal.Decimal `json:"LiuSuiTexas" gorm:"column:LiuSuiTexas"`         //
}

type Time time.Time

const (
	timeFormart = "2006-01-02 15:04:05"
	zone        = "Asia/Shanghai"
)

// UnmarshalJSON implements json unmarshal interface.
func (t *Time) UnmarshalJSON(data []byte) (err error) {
	now, err := time.ParseInLocation(`"`+timeFormart+`"`, string(data), time.Local)
	*t = Time(now)
	return
}

// MarshalJSON implements json marshal interface.
func (t Time) MarshalJSON() ([]byte, error) {
	b := make([]byte, 0, len(timeFormart)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, timeFormart)
	b = append(b, '"')
	return b, nil
}

func (t Time) String() string {
	return time.Time(t).Format(timeFormart)
}

func (t Time) local() time.Time {
	loc, _ := time.LoadLocation(zone)
	return time.Time(t).In(loc)
}

// Value ...
func (t Time) Value() (driver.Value, error) {
	var zeroTime time.Time
	var ti = time.Time(t)
	if ti.UnixNano() == zeroTime.UnixNano() {
		return nil, nil
	}
	return ti, nil
}

// Scan valueof time.Time 注意是指针类型 method
func (t *Time) Scan(v interface{}) error {
	value, ok := v.(time.Time)
	if ok {
		*t = Time(value)
		return nil
	}
	return fmt.Errorf("can not convert %v to timestamp", v)
}

// UnmarshalJSON implements json unmarshal interface.
//func (t *MyString) UnmarshalJSON(data []byte) (err error) {
//	now, err := time.ParseInLocation(`"`+timeFormart+`"`, string(data), time.Local)
//	*t = Time(now)
//	return
//}

// MarshalJSON implements json marshal interface.
//func (t MyString) MarshalJSON() ([]byte, error) {
//	b := make([]byte, 0, len(timeFormart)+2)
//	b = append(b, '"')
//	b = time.Time(t).AppendFormat(b, timeFormart)
//	b = append(b, '"')
//	return b, nil
//}

//func (t MyString) String() string {
//	return time.Time(t).Format(timeFormart)
//}

//func (t MyString) local() time.Time {
//	loc, _ := time.LoadLocation(zone)
//	return time.Time(t).In(loc)
//}

//// Value ...
//func (t MyString) Value() (driver.Value, error) {
//	var zeroTime time.Time
//	var ti = time.Time(t)
//	if ti.UnixNano() == zeroTime.UnixNano() {
//		return nil, nil
//	}
//	return ti, nil
//}
//
//// Scan valueof time.Time 注意是指针类型 method
//func (t *MyString) Scan(v interface{}) error {
//	value, ok := v.(time.Time)
//	if ok {
//		*t = Time(value)
//		return nil
//	}
//	return fmt.Errorf("can not convert %v to timestamp", v)
//}

//func (t *Time) Scan(v interface{}) error {
//	value, ok := v.(string)
//	if ok {
//		*t = string(value)
//		return nil
//	}
//	return fmt.Errorf("can not convert %v to timestamp", v)
//}

//func Unix(sec int64, nsec int64) LocalTime {
//	if nsec < 0 || nsec >= 1e9 {
//		n := nsec / 1e9
//		sec += n
//		nsec -= n * 1e9
//		if nsec < 0 {
//			nsec += 1e9
//			sec--
//		}
//	}
//	return localTime(sec, int32(nsec))
//}
//
//func CreateLocalTimeByTime(t time.Time) LocalTime {
//	LocalTime{t}
//}
