// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashStat/model"
)

func newXTgRobotStatHour(db *gorm.DB, opts ...gen.DOOption) xTgRobotStatHour {
	_xTgRobotStatHour := xTgRobotStatHour{}

	_xTgRobotStatHour.xTgRobotStatHourDo.UseDB(db, opts...)
	_xTgRobotStatHour.xTgRobotStatHourDo.UseModel(&model.XTgRobotStatHour{})

	tableName := _xTgRobotStatHour.xTgRobotStatHourDo.TableName()
	_xTgRobotStatHour.ALL = field.NewAsterisk(tableName)
	_xTgRobotStatHour.RecordTime = field.NewTime(tableName, "RecordTime")
	_xTgRobotStatHour.RobotID = field.NewInt64(tableName, "RobotId")
	_xTgRobotStatHour.TgChatID = field.NewInt64(tableName, "TgChatId")
	_xTgRobotStatHour.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRobotStatHour.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgRobotStatHour.StartCount = field.NewInt32(tableName, "StartCount")
	_xTgRobotStatHour.IsReg = field.NewInt32(tableName, "IsReg")
	_xTgRobotStatHour.IsGetUSDT = field.NewInt32(tableName, "IsGetUSDT")
	_xTgRobotStatHour.IsGetTRX = field.NewInt32(tableName, "IsGetTRX")
	_xTgRobotStatHour.IsActivation = field.NewInt32(tableName, "IsActivation")
	_xTgRobotStatHour.IsInResourceDb = field.NewInt32(tableName, "IsInResourceDb")
	_xTgRobotStatHour.Memo = field.NewString(tableName, "Memo")
	_xTgRobotStatHour.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRobotStatHour.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgRobotStatHour.fillFieldMap()

	return _xTgRobotStatHour
}

// xTgRobotStatHour 机器人小时统计
type xTgRobotStatHour struct {
	xTgRobotStatHourDo xTgRobotStatHourDo

	ALL            field.Asterisk
	RecordTime     field.Time   // 时间(精确到小时)
	RobotID        field.Int64  // 机器人Id
	TgChatID       field.Int64  //  tg用户Id
	SellerID       field.Int32  // 运营商id
	ChannelID      field.Int32  // 渠道id
	StartCount     field.Int32  // Start次数
	IsReg          field.Int32  // 是否注册
	IsGetUSDT      field.Int32  // 是否领取U
	IsGetTRX       field.Int32  // 是否领取trx
	IsActivation   field.Int32  // 是否激活地址
	IsInResourceDb field.Int32  // 是否在库Start
	Memo           field.String // 备注
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTgRobotStatHour) Table(newTableName string) *xTgRobotStatHour {
	x.xTgRobotStatHourDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRobotStatHour) As(alias string) *xTgRobotStatHour {
	x.xTgRobotStatHourDo.DO = *(x.xTgRobotStatHourDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRobotStatHour) updateTableName(table string) *xTgRobotStatHour {
	x.ALL = field.NewAsterisk(table)
	x.RecordTime = field.NewTime(table, "RecordTime")
	x.RobotID = field.NewInt64(table, "RobotId")
	x.TgChatID = field.NewInt64(table, "TgChatId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.StartCount = field.NewInt32(table, "StartCount")
	x.IsReg = field.NewInt32(table, "IsReg")
	x.IsGetUSDT = field.NewInt32(table, "IsGetUSDT")
	x.IsGetTRX = field.NewInt32(table, "IsGetTRX")
	x.IsActivation = field.NewInt32(table, "IsActivation")
	x.IsInResourceDb = field.NewInt32(table, "IsInResourceDb")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRobotStatHour) WithContext(ctx context.Context) *xTgRobotStatHourDo {
	return x.xTgRobotStatHourDo.WithContext(ctx)
}

func (x xTgRobotStatHour) TableName() string { return x.xTgRobotStatHourDo.TableName() }

func (x xTgRobotStatHour) Alias() string { return x.xTgRobotStatHourDo.Alias() }

func (x xTgRobotStatHour) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRobotStatHourDo.Columns(cols...)
}

func (x *xTgRobotStatHour) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRobotStatHour) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["RecordTime"] = x.RecordTime
	x.fieldMap["RobotId"] = x.RobotID
	x.fieldMap["TgChatId"] = x.TgChatID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["StartCount"] = x.StartCount
	x.fieldMap["IsReg"] = x.IsReg
	x.fieldMap["IsGetUSDT"] = x.IsGetUSDT
	x.fieldMap["IsGetTRX"] = x.IsGetTRX
	x.fieldMap["IsActivation"] = x.IsActivation
	x.fieldMap["IsInResourceDb"] = x.IsInResourceDb
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgRobotStatHour) clone(db *gorm.DB) xTgRobotStatHour {
	x.xTgRobotStatHourDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRobotStatHour) replaceDB(db *gorm.DB) xTgRobotStatHour {
	x.xTgRobotStatHourDo.ReplaceDB(db)
	return x
}

type xTgRobotStatHourDo struct{ gen.DO }

func (x xTgRobotStatHourDo) Debug() *xTgRobotStatHourDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRobotStatHourDo) WithContext(ctx context.Context) *xTgRobotStatHourDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRobotStatHourDo) ReadDB() *xTgRobotStatHourDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRobotStatHourDo) WriteDB() *xTgRobotStatHourDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRobotStatHourDo) Session(config *gorm.Session) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRobotStatHourDo) Clauses(conds ...clause.Expression) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRobotStatHourDo) Returning(value interface{}, columns ...string) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRobotStatHourDo) Not(conds ...gen.Condition) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRobotStatHourDo) Or(conds ...gen.Condition) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRobotStatHourDo) Select(conds ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRobotStatHourDo) Where(conds ...gen.Condition) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRobotStatHourDo) Order(conds ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRobotStatHourDo) Distinct(cols ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRobotStatHourDo) Omit(cols ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRobotStatHourDo) Join(table schema.Tabler, on ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRobotStatHourDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRobotStatHourDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRobotStatHourDo) Group(cols ...field.Expr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRobotStatHourDo) Having(conds ...gen.Condition) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRobotStatHourDo) Limit(limit int) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRobotStatHourDo) Offset(offset int) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRobotStatHourDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRobotStatHourDo) Unscoped() *xTgRobotStatHourDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRobotStatHourDo) Create(values ...*model.XTgRobotStatHour) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRobotStatHourDo) CreateInBatches(values []*model.XTgRobotStatHour, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRobotStatHourDo) Save(values ...*model.XTgRobotStatHour) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRobotStatHourDo) First() (*model.XTgRobotStatHour, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatHour), nil
	}
}

func (x xTgRobotStatHourDo) Take() (*model.XTgRobotStatHour, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatHour), nil
	}
}

func (x xTgRobotStatHourDo) Last() (*model.XTgRobotStatHour, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatHour), nil
	}
}

func (x xTgRobotStatHourDo) Find() ([]*model.XTgRobotStatHour, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRobotStatHour), err
}

func (x xTgRobotStatHourDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRobotStatHour, err error) {
	buf := make([]*model.XTgRobotStatHour, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRobotStatHourDo) FindInBatches(result *[]*model.XTgRobotStatHour, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRobotStatHourDo) Attrs(attrs ...field.AssignExpr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRobotStatHourDo) Assign(attrs ...field.AssignExpr) *xTgRobotStatHourDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRobotStatHourDo) Joins(fields ...field.RelationField) *xTgRobotStatHourDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRobotStatHourDo) Preload(fields ...field.RelationField) *xTgRobotStatHourDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRobotStatHourDo) FirstOrInit() (*model.XTgRobotStatHour, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatHour), nil
	}
}

func (x xTgRobotStatHourDo) FirstOrCreate() (*model.XTgRobotStatHour, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatHour), nil
	}
}

func (x xTgRobotStatHourDo) FindByPage(offset int, limit int) (result []*model.XTgRobotStatHour, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRobotStatHourDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRobotStatHourDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRobotStatHourDo) Delete(models ...*model.XTgRobotStatHour) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRobotStatHourDo) withDO(do gen.Dao) *xTgRobotStatHourDo {
	x.DO = *do.(*gen.DO)
	return x
}
