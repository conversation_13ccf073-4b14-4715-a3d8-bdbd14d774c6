// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotConfig = "x_robot_config"

// XRobotConfig mapped from table <x_robot_config>
type XRobotConfig struct {
	ID                  int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                                      // pk
	SellerID            int32     `gorm:"column:seller_id;comment:运营商id" json:"seller_id"`                                                   // 运营商id
	SellerName          string    `gorm:"column:seller_name;comment:运营名称" json:"seller_name"`                                                // 运营名称
	ChannelID           int32     `gorm:"column:channel_id;comment:渠道id" json:"channel_id"`                                                  // 渠道id
	ChannelName         string    `gorm:"column:channel_name;comment:渠道名称" json:"channel_name"`                                              // 渠道名称
	Name                string    `gorm:"column:name;not null;comment:机器人Username" json:"name"`                                              // 机器人Username
	Token               string    `gorm:"column:token;not null;comment:机器人token" json:"token"`                                               // 机器人token
	GameURL             string    `gorm:"column:game_url;comment:游戏链接" json:"game_url"`                                                      // 游戏链接
	IsEnable            int32     `gorm:"column:is_enable;comment:是否启用 (1:是 2:否)" json:"is_enable"`                                          // 是否启用 (1:是 2:否)
	GroupURL            string    `gorm:"column:group_url;comment:tg群组链接/频道链接" json:"group_url"`                                             // tg群组链接/频道链接
	GroupChatID         int64     `gorm:"column:group_chat_id;comment:tg用户在汇报群内的ChatId" json:"group_chat_id"`                                // tg用户在汇报群内的ChatId
	GroupRobotName      string    `gorm:"column:group_robot_name;comment:群组机器人名称" json:"group_robot_name"`                                   // 群组机器人名称
	GroupRobotToken     string    `gorm:"column:group_robot_token;comment:群组机器人token" json:"group_robot_token"`                              // 群组机器人token
	IsOpenGroupRobot    int32     `gorm:"column:is_open_group_robot;comment:是否开启入群机器人 0:关闭，1:开启" json:"is_open_group_robot"`                 // 是否开启入群机器人 0:关闭，1:开启
	IsOpenGift          int32     `gorm:"column:is_open_gift;default:1;comment:是否开启体验金(1:是 2:否)" json:"is_open_gift"`                        // 是否开启体验金(1:是 2:否)
	IsIPRestriction     int32     `gorm:"column:is_ip_restriction;default:1;comment:是否开启IP限制(1:是 2:否)" json:"is_ip_restriction"`             // 是否开启IP限制(1:是 2:否)
	IsDeviceRestriction int32     `gorm:"column:is_device_restriction;default:2;comment:是否开启设备限制(1:是 2:否)" json:"is_device_restriction"`     // 是否开启设备限制(1:是 2:否)
	IsWalletRestriction int32     `gorm:"column:is_wallet_restriction;default:2;comment:是否开启钱包地址关联限制(1:是 2:否)" json:"is_wallet_restriction"` // 是否开启钱包地址关联限制(1:是 2:否)
	IPWhitelist         string    `gorm:"column:ip_whitelist;comment:ip白名单(英文逗号分割)" json:"ip_whitelist"`                                     // ip白名单(英文逗号分割)
	GiftAmount          int32     `gorm:"column:gift_amount;comment:彩金数量" json:"gift_amount"`                                                // 彩金数量
	GiftCurrency        string    `gorm:"column:gift_currency;comment:彩金货币" json:"gift_currency"`                                            // 彩金货币
	RobotType           int32     `gorm:"column:robot_type;default:1;comment:机器人类型(1:引客机器人 2:广告机器人)" json:"robot_type"`                      // 机器人类型(1:引客机器人 2:广告机器人)
	CommonReply         string    `gorm:"column:common_reply;comment:通用回复模板" json:"common_reply"`                                            // 通用回复模板
	MissionData         string    `gorm:"column:mission_data;comment:关联活动模板配置" json:"mission_data"`                                          // 关联活动模板配置
	MessageData         string    `gorm:"column:message_data;comment:关联消息回复" json:"message_data"`                                            // 关联消息回复
	CreateTime          time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`             // 创建时间
	UpdateTime          time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:修改时间" json:"update_time"`                      // 修改时间
	IsDel               int32     `gorm:"column:is_del;comment:软删除" json:"is_del"`                                                           // 软删除
}

// TableName XRobotConfig's table name
func (*XRobotConfig) TableName() string {
	return TableNameXRobotConfig
}
