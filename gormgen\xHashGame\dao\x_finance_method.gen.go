// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXFinanceMethod(db *gorm.DB, opts ...gen.DOOption) xFinanceMethod {
	_xFinanceMethod := xFinanceMethod{}

	_xFinanceMethod.xFinanceMethodDo.UseDB(db, opts...)
	_xFinanceMethod.xFinanceMethodDo.UseModel(&model.XFinanceMethod{})

	tableName := _xFinanceMethod.xFinanceMethodDo.TableName()
	_xFinanceMethod.ALL = field.NewAsterisk(tableName)
	_xFinanceMethod.ID = field.NewInt32(tableName, "Id")
	_xFinanceMethod.SellerID = field.NewInt32(tableName, "SellerId")
	_xFinanceMethod.Brand = field.NewString(tableName, "Brand")
	_xFinanceMethod.Name = field.NewString(tableName, "Name")
	_xFinanceMethod.Symbol = field.NewString(tableName, "Symbol")
	_xFinanceMethod.IsRecharge = field.NewInt32(tableName, "IsRecharge")
	_xFinanceMethod.IsWithdraw = field.NewInt32(tableName, "IsWithdraw")
	_xFinanceMethod.MinRecharge = field.NewFloat64(tableName, "MinRecharge")
	_xFinanceMethod.MaxRecharge = field.NewFloat64(tableName, "MaxRecharge")
	_xFinanceMethod.MinWithdraw = field.NewFloat64(tableName, "MinWithdraw")
	_xFinanceMethod.MaxWithdraw = field.NewFloat64(tableName, "MaxWithdraw")
	_xFinanceMethod.RechargeAmountOptions = field.NewString(tableName, "RechargeAmountOptions")
	_xFinanceMethod.WithdrawAmountOptions = field.NewString(tableName, "WithdrawAmountOptions")
	_xFinanceMethod.IsInputRechargeAmount = field.NewInt32(tableName, "IsInputRechargeAmount")
	_xFinanceMethod.IsInputWithdrawAmount = field.NewInt32(tableName, "IsInputWithdrawAmount")
	_xFinanceMethod.State = field.NewInt32(tableName, "State")
	_xFinanceMethod.APIInfo = field.NewString(tableName, "ApiInfo")
	_xFinanceMethod.CreateTime = field.NewTime(tableName, "CreateTime")
	_xFinanceMethod.RechargeFreeRate = field.NewFloat64(tableName, "RechargeFreeRate")
	_xFinanceMethod.WithdrawFreeRate = field.NewFloat64(tableName, "WithdrawFreeRate")
	_xFinanceMethod.RechargeDaillyLimit = field.NewFloat64(tableName, "RechargeDaillyLimit")
	_xFinanceMethod.WithdrawDaillyLimit = field.NewFloat64(tableName, "WithdrawDaillyLimit")
	_xFinanceMethod.RechargeDailly = field.NewFloat64(tableName, "RechargeDailly")
	_xFinanceMethod.WithdrawDailly = field.NewFloat64(tableName, "WithdrawDailly")
	_xFinanceMethod.ExtraConfig = field.NewString(tableName, "ExtraConfig")
	_xFinanceMethod.Icon = field.NewString(tableName, "Icon")
	_xFinanceMethod.MKey = field.NewString(tableName, "MKey")
	_xFinanceMethod.Sort = field.NewInt32(tableName, "Sort")
	_xFinanceMethod.Country = field.NewString(tableName, "Country")
	_xFinanceMethod.Banks = field.NewString(tableName, "Banks")
	_xFinanceMethod.WithdrawBanks = field.NewString(tableName, "WithdrawBanks")
	_xFinanceMethod.IsBankRecharge = field.NewInt32(tableName, "IsBankRecharge")
	_xFinanceMethod.HasBankNoRecharge = field.NewInt32(tableName, "HasBankNoRecharge")
	_xFinanceMethod.RechargeWayType = field.NewInt32(tableName, "RechargeWayType")
	_xFinanceMethod.WithdrawWayType = field.NewInt32(tableName, "WithdrawWayType")
	_xFinanceMethod.PayType = field.NewString(tableName, "PayType")
	_xFinanceMethod.WPayType = field.NewString(tableName, "WPayType")
	_xFinanceMethod.BankType = field.NewString(tableName, "BankType")
	_xFinanceMethod.PoundSign = field.NewString(tableName, "PoundSign")
	_xFinanceMethod.IdentifyType = field.NewString(tableName, "IdentifyType")
	_xFinanceMethod.Ftype = field.NewInt32(tableName, "Ftype")
	_xFinanceMethod.Rtype = field.NewInt32(tableName, "Rtype")
	_xFinanceMethod.Hv = field.NewInt32(tableName, "Hv")
	_xFinanceMethod.JumpType = field.NewInt32(tableName, "JumpType")

	_xFinanceMethod.fillFieldMap()

	return _xFinanceMethod
}

// xFinanceMethod 充提方法
type xFinanceMethod struct {
	xFinanceMethodDo xFinanceMethodDo

	ALL                   field.Asterisk
	ID                    field.Int32
	SellerID              field.Int32
	Brand                 field.String  // 支付商家  1.区块链TRON,2区块链ETH,3易币付,4PIX
	Name                  field.String  // 支代付名称
	Symbol                field.String  // 支持币种
	IsRecharge            field.Int32   // 是否支持充值 1支持,2不支持
	IsWithdraw            field.Int32   // 是否支持提款 1支持,2不支持
	MinRecharge           field.Float64 // 充值最低金额 -1无限制
	MaxRecharge           field.Float64 // 充值最大金额 -1 无限制
	MinWithdraw           field.Float64 // 最小提现金额 -1 无限制
	MaxWithdraw           field.Float64 // 最大提现金额 -1 无限制
	RechargeAmountOptions field.String  // 充值快速金额
	WithdrawAmountOptions field.String  // 提现快速金额
	IsInputRechargeAmount field.Int32   // 是否支持自定义输入充值金额
	IsInputWithdrawAmount field.Int32   // 是否支持自定义输入提款金额
	State                 field.Int32   // 状态 1启用 禁用
	APIInfo               field.String  // api信息
	CreateTime            field.Time
	RechargeFreeRate      field.Float64 // 充值手续费率
	WithdrawFreeRate      field.Float64 // 提现手续费率
	RechargeDaillyLimit   field.Float64 // 充值日上线
	WithdrawDaillyLimit   field.Float64 // 提款日上线
	RechargeDailly        field.Float64 // 今日已充值金额
	WithdrawDailly        field.Float64 // 今日已提现金额
	ExtraConfig           field.String
	Icon                  field.String
	MKey                  field.String
	Sort                  field.Int32 // 排序,数字越大越靠前
	Country               field.String
	Banks                 field.String // 银行编码 名称
	WithdrawBanks         field.String // 代付银行
	IsBankRecharge        field.Int32  // 充值是否银行卡方式 0:否 1:是
	HasBankNoRecharge     field.Int32  // 充值是否需要传银行卡号 0:否 1:是
	/*
		充值方式
		1-默认
		2-需要传姓名
		3-需要传银行编码
		4-需要传姓名和银行卡号
		5-需要传姓名和银行名称
		6-需要传完整的银行信息(姓名;银行名称;编码;卡号)
		7-需要传银行编码和姓名
	*/
	RechargeWayType field.Int32
	/*
		提现方式
		1-银行信息(RealName,BankName,BankCode,BankNo)
		2-银行信息(RealName,BankName,BankNo)
		3-手机号 (PhoneNum)
		4-PIX (CPFNo,PIXType,PIXAccount)
		5-银行信息(BankCode)
		6-印度银行信息(BankName, RealName,BankNo,IFSC,BrankType)
		7-银行信息(BankCode, RealName,BankNo,BrankType)
		8-银行信息+身份信息(BankCode, RealName,BankNo,BrankType,IdentifyType,IdentifyNum)
	*/
	WithdrawWayType field.Int32
	PayType         field.String // 支付类型
	WPayType        field.String // 代付通道编码
	BankType        field.String // 银行类型
	PoundSign       field.String // 币种符号
	IdentifyType    field.String // 证件类型
	Ftype           field.Int32  // 币种 1:法币 2:加密货币
	Rtype           field.Int32  // 支付类型 1:加密货币 2:线上支付 3:全球法币 4:信用卡 5:银行卡
	Hv              field.Int32  // 权重必须大于1
	JumpType        field.Int32  // 跳转类型 1：内嵌 2:新窗口

	fieldMap map[string]field.Expr
}

func (x xFinanceMethod) Table(newTableName string) *xFinanceMethod {
	x.xFinanceMethodDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xFinanceMethod) As(alias string) *xFinanceMethod {
	x.xFinanceMethodDo.DO = *(x.xFinanceMethodDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xFinanceMethod) updateTableName(table string) *xFinanceMethod {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.Brand = field.NewString(table, "Brand")
	x.Name = field.NewString(table, "Name")
	x.Symbol = field.NewString(table, "Symbol")
	x.IsRecharge = field.NewInt32(table, "IsRecharge")
	x.IsWithdraw = field.NewInt32(table, "IsWithdraw")
	x.MinRecharge = field.NewFloat64(table, "MinRecharge")
	x.MaxRecharge = field.NewFloat64(table, "MaxRecharge")
	x.MinWithdraw = field.NewFloat64(table, "MinWithdraw")
	x.MaxWithdraw = field.NewFloat64(table, "MaxWithdraw")
	x.RechargeAmountOptions = field.NewString(table, "RechargeAmountOptions")
	x.WithdrawAmountOptions = field.NewString(table, "WithdrawAmountOptions")
	x.IsInputRechargeAmount = field.NewInt32(table, "IsInputRechargeAmount")
	x.IsInputWithdrawAmount = field.NewInt32(table, "IsInputWithdrawAmount")
	x.State = field.NewInt32(table, "State")
	x.APIInfo = field.NewString(table, "ApiInfo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.RechargeFreeRate = field.NewFloat64(table, "RechargeFreeRate")
	x.WithdrawFreeRate = field.NewFloat64(table, "WithdrawFreeRate")
	x.RechargeDaillyLimit = field.NewFloat64(table, "RechargeDaillyLimit")
	x.WithdrawDaillyLimit = field.NewFloat64(table, "WithdrawDaillyLimit")
	x.RechargeDailly = field.NewFloat64(table, "RechargeDailly")
	x.WithdrawDailly = field.NewFloat64(table, "WithdrawDailly")
	x.ExtraConfig = field.NewString(table, "ExtraConfig")
	x.Icon = field.NewString(table, "Icon")
	x.MKey = field.NewString(table, "MKey")
	x.Sort = field.NewInt32(table, "Sort")
	x.Country = field.NewString(table, "Country")
	x.Banks = field.NewString(table, "Banks")
	x.WithdrawBanks = field.NewString(table, "WithdrawBanks")
	x.IsBankRecharge = field.NewInt32(table, "IsBankRecharge")
	x.HasBankNoRecharge = field.NewInt32(table, "HasBankNoRecharge")
	x.RechargeWayType = field.NewInt32(table, "RechargeWayType")
	x.WithdrawWayType = field.NewInt32(table, "WithdrawWayType")
	x.PayType = field.NewString(table, "PayType")
	x.WPayType = field.NewString(table, "WPayType")
	x.BankType = field.NewString(table, "BankType")
	x.PoundSign = field.NewString(table, "PoundSign")
	x.IdentifyType = field.NewString(table, "IdentifyType")
	x.Ftype = field.NewInt32(table, "Ftype")
	x.Rtype = field.NewInt32(table, "Rtype")
	x.Hv = field.NewInt32(table, "Hv")
	x.JumpType = field.NewInt32(table, "JumpType")

	x.fillFieldMap()

	return x
}

func (x *xFinanceMethod) WithContext(ctx context.Context) *xFinanceMethodDo {
	return x.xFinanceMethodDo.WithContext(ctx)
}

func (x xFinanceMethod) TableName() string { return x.xFinanceMethodDo.TableName() }

func (x xFinanceMethod) Alias() string { return x.xFinanceMethodDo.Alias() }

func (x xFinanceMethod) Columns(cols ...field.Expr) gen.Columns {
	return x.xFinanceMethodDo.Columns(cols...)
}

func (x *xFinanceMethod) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xFinanceMethod) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 44)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["Name"] = x.Name
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["IsRecharge"] = x.IsRecharge
	x.fieldMap["IsWithdraw"] = x.IsWithdraw
	x.fieldMap["MinRecharge"] = x.MinRecharge
	x.fieldMap["MaxRecharge"] = x.MaxRecharge
	x.fieldMap["MinWithdraw"] = x.MinWithdraw
	x.fieldMap["MaxWithdraw"] = x.MaxWithdraw
	x.fieldMap["RechargeAmountOptions"] = x.RechargeAmountOptions
	x.fieldMap["WithdrawAmountOptions"] = x.WithdrawAmountOptions
	x.fieldMap["IsInputRechargeAmount"] = x.IsInputRechargeAmount
	x.fieldMap["IsInputWithdrawAmount"] = x.IsInputWithdrawAmount
	x.fieldMap["State"] = x.State
	x.fieldMap["ApiInfo"] = x.APIInfo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["RechargeFreeRate"] = x.RechargeFreeRate
	x.fieldMap["WithdrawFreeRate"] = x.WithdrawFreeRate
	x.fieldMap["RechargeDaillyLimit"] = x.RechargeDaillyLimit
	x.fieldMap["WithdrawDaillyLimit"] = x.WithdrawDaillyLimit
	x.fieldMap["RechargeDailly"] = x.RechargeDailly
	x.fieldMap["WithdrawDailly"] = x.WithdrawDailly
	x.fieldMap["ExtraConfig"] = x.ExtraConfig
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["MKey"] = x.MKey
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["Country"] = x.Country
	x.fieldMap["Banks"] = x.Banks
	x.fieldMap["WithdrawBanks"] = x.WithdrawBanks
	x.fieldMap["IsBankRecharge"] = x.IsBankRecharge
	x.fieldMap["HasBankNoRecharge"] = x.HasBankNoRecharge
	x.fieldMap["RechargeWayType"] = x.RechargeWayType
	x.fieldMap["WithdrawWayType"] = x.WithdrawWayType
	x.fieldMap["PayType"] = x.PayType
	x.fieldMap["WPayType"] = x.WPayType
	x.fieldMap["BankType"] = x.BankType
	x.fieldMap["PoundSign"] = x.PoundSign
	x.fieldMap["IdentifyType"] = x.IdentifyType
	x.fieldMap["Ftype"] = x.Ftype
	x.fieldMap["Rtype"] = x.Rtype
	x.fieldMap["Hv"] = x.Hv
	x.fieldMap["JumpType"] = x.JumpType
}

func (x xFinanceMethod) clone(db *gorm.DB) xFinanceMethod {
	x.xFinanceMethodDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xFinanceMethod) replaceDB(db *gorm.DB) xFinanceMethod {
	x.xFinanceMethodDo.ReplaceDB(db)
	return x
}

type xFinanceMethodDo struct{ gen.DO }

func (x xFinanceMethodDo) Debug() *xFinanceMethodDo {
	return x.withDO(x.DO.Debug())
}

func (x xFinanceMethodDo) WithContext(ctx context.Context) *xFinanceMethodDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xFinanceMethodDo) ReadDB() *xFinanceMethodDo {
	return x.Clauses(dbresolver.Read)
}

func (x xFinanceMethodDo) WriteDB() *xFinanceMethodDo {
	return x.Clauses(dbresolver.Write)
}

func (x xFinanceMethodDo) Session(config *gorm.Session) *xFinanceMethodDo {
	return x.withDO(x.DO.Session(config))
}

func (x xFinanceMethodDo) Clauses(conds ...clause.Expression) *xFinanceMethodDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xFinanceMethodDo) Returning(value interface{}, columns ...string) *xFinanceMethodDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xFinanceMethodDo) Not(conds ...gen.Condition) *xFinanceMethodDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xFinanceMethodDo) Or(conds ...gen.Condition) *xFinanceMethodDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xFinanceMethodDo) Select(conds ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xFinanceMethodDo) Where(conds ...gen.Condition) *xFinanceMethodDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xFinanceMethodDo) Order(conds ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xFinanceMethodDo) Distinct(cols ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xFinanceMethodDo) Omit(cols ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xFinanceMethodDo) Join(table schema.Tabler, on ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xFinanceMethodDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xFinanceMethodDo) RightJoin(table schema.Tabler, on ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xFinanceMethodDo) Group(cols ...field.Expr) *xFinanceMethodDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xFinanceMethodDo) Having(conds ...gen.Condition) *xFinanceMethodDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xFinanceMethodDo) Limit(limit int) *xFinanceMethodDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xFinanceMethodDo) Offset(offset int) *xFinanceMethodDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xFinanceMethodDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xFinanceMethodDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xFinanceMethodDo) Unscoped() *xFinanceMethodDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xFinanceMethodDo) Create(values ...*model.XFinanceMethod) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xFinanceMethodDo) CreateInBatches(values []*model.XFinanceMethod, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xFinanceMethodDo) Save(values ...*model.XFinanceMethod) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xFinanceMethodDo) First() (*model.XFinanceMethod, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceMethod), nil
	}
}

func (x xFinanceMethodDo) Take() (*model.XFinanceMethod, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceMethod), nil
	}
}

func (x xFinanceMethodDo) Last() (*model.XFinanceMethod, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceMethod), nil
	}
}

func (x xFinanceMethodDo) Find() ([]*model.XFinanceMethod, error) {
	result, err := x.DO.Find()
	return result.([]*model.XFinanceMethod), err
}

func (x xFinanceMethodDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XFinanceMethod, err error) {
	buf := make([]*model.XFinanceMethod, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xFinanceMethodDo) FindInBatches(result *[]*model.XFinanceMethod, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xFinanceMethodDo) Attrs(attrs ...field.AssignExpr) *xFinanceMethodDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xFinanceMethodDo) Assign(attrs ...field.AssignExpr) *xFinanceMethodDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xFinanceMethodDo) Joins(fields ...field.RelationField) *xFinanceMethodDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xFinanceMethodDo) Preload(fields ...field.RelationField) *xFinanceMethodDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xFinanceMethodDo) FirstOrInit() (*model.XFinanceMethod, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceMethod), nil
	}
}

func (x xFinanceMethodDo) FirstOrCreate() (*model.XFinanceMethod, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XFinanceMethod), nil
	}
}

func (x xFinanceMethodDo) FindByPage(offset int, limit int) (result []*model.XFinanceMethod, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xFinanceMethodDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xFinanceMethodDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xFinanceMethodDo) Delete(models ...*model.XFinanceMethod) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xFinanceMethodDo) withDO(do gen.Dao) *xFinanceMethodDo {
	x.DO = *do.(*gen.DO)
	return x
}
