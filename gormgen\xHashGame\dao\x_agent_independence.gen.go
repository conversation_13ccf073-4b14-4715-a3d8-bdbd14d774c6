// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentIndependence(db *gorm.DB, opts ...gen.DOOption) xAgentIndependence {
	_xAgentIndependence := xAgentIndependence{}

	_xAgentIndependence.xAgentIndependenceDo.UseDB(db, opts...)
	_xAgentIndependence.xAgentIndependenceDo.UseModel(&model.XAgentIndependence{})

	tableName := _xAgentIndependence.xAgentIndependenceDo.TableName()
	_xAgentIndependence.ALL = field.NewAsterisk(tableName)
	_xAgentIndependence.ID = field.NewInt32(tableName, "Id")
	_xAgentIndependence.CommissionID = field.NewInt32(tableName, "CommissionId")
	_xAgentIndependence.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentIndependence.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentIndependence.UserID = field.NewInt32(tableName, "UserId")
	_xAgentIndependence.Host = field.NewString(tableName, "Host")
	_xAgentIndependence.CustomeService = field.NewString(tableName, "CustomeService")
	_xAgentIndependence.IsSelfHost = field.NewInt32(tableName, "IsSelfHost")
	_xAgentIndependence.IsDuiHuan = field.NewInt32(tableName, "IsDuiHuan")
	_xAgentIndependence.IsSelfBackOffice = field.NewInt32(tableName, "IsSelfBackOffice")
	_xAgentIndependence.IsSelfTgBot = field.NewInt32(tableName, "IsSelfTgBot")
	_xAgentIndependence.IsSelfActive = field.NewInt32(tableName, "IsSelfActive")
	_xAgentIndependence.IsSelfMajorGameOrder = field.NewInt32(tableName, "IsSelfMajorGameOrder")
	_xAgentIndependence.IsSelfMinorGameOrder = field.NewInt32(tableName, "IsSelfMinorGameOrder")
	_xAgentIndependence.IsSelfAddress = field.NewInt32(tableName, "IsSelfAddress")
	_xAgentIndependence.IsSelfCustomService = field.NewInt32(tableName, "IsSelfCustomService")
	_xAgentIndependence.MajorGameOrder = field.NewString(tableName, "MajorGameOrder")
	_xAgentIndependence.MinorGameOrder = field.NewString(tableName, "MinorGameOrder")
	_xAgentIndependence.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentIndependence.CommissionRate = field.NewFloat64(tableName, "CommissionRate")
	_xAgentIndependence.Memo = field.NewString(tableName, "Memo")
	_xAgentIndependence.TgInfo = field.NewString(tableName, "TgInfo")
	_xAgentIndependence.ActiveInfo = field.NewString(tableName, "ActiveInfo")
	_xAgentIndependence.BackOffaceInfo = field.NewString(tableName, "BackOffaceInfo")
	_xAgentIndependence.AgentCode = field.NewString(tableName, "AgentCode")
	_xAgentIndependence.State = field.NewInt32(tableName, "State")
	_xAgentIndependence.Icon = field.NewString(tableName, "Icon")
	_xAgentIndependence.AgentName = field.NewString(tableName, "AgentName")
	_xAgentIndependence.ShowName = field.NewString(tableName, "ShowName")
	_xAgentIndependence.Logo = field.NewString(tableName, "Logo")
	_xAgentIndependence.IsSuper = field.NewInt32(tableName, "IsSuper")
	_xAgentIndependence.AgentUseID = field.NewInt32(tableName, "AgentUseId")
	_xAgentIndependence.MajorGameOrderNew = field.NewString(tableName, "MajorGameOrderNew")
	_xAgentIndependence.IsSelfMajorGameOrderNew = field.NewInt32(tableName, "IsSelfMajorGameOrderNew")
	_xAgentIndependence.Logo2 = field.NewString(tableName, "Logo2")
	_xAgentIndependence.PromotionHost = field.NewString(tableName, "PromotionHost")
	_xAgentIndependence.IosIcon = field.NewString(tableName, "IosIcon")

	_xAgentIndependence.fillFieldMap()

	return _xAgentIndependence
}

type xAgentIndependence struct {
	xAgentIndependenceDo xAgentIndependenceDo

	ALL                     field.Asterisk
	ID                      field.Int32   // id
	CommissionID            field.Int32   // 佣金方案ID
	SellerID                field.Int32   // 运营商
	ChannelID               field.Int32   // 渠道
	UserID                  field.Int32   // 代理id
	Host                    field.String  // 代理域名
	CustomeService          field.String  // 自己的客服
	IsSelfHost              field.Int32   // 是否独立域名
	IsDuiHuan               field.Int32   // 是否开启兑换
	IsSelfBackOffice        field.Int32   // 是否独立后台
	IsSelfTgBot             field.Int32   // 是否独立机器人
	IsSelfActive            field.Int32   // 是否独立活动
	IsSelfMajorGameOrder    field.Int32   // 是否自定义游戏主类
	IsSelfMinorGameOrder    field.Int32   // 是否自定义游戏小类
	IsSelfAddress           field.Int32   // 是否独立地址
	IsSelfCustomService     field.Int32   // 是否独立客服
	MajorGameOrder          field.String  // 游戏大类排序
	MinorGameOrder          field.String  // 游戏小类排序
	CreateTime              field.Time    // 创建时间
	CommissionRate          field.Float64 // 返佣比例
	Memo                    field.String  // 备注
	TgInfo                  field.String  // 独立机器人
	ActiveInfo              field.String  // 独立活动
	BackOffaceInfo          field.String  // 独立后台信息
	AgentCode               field.String  // 代理邀请码
	State                   field.Int32   // 状态 1启用,2禁用
	Icon                    field.String  // 网页标签icon
	AgentName               field.String  // 代理名称(后台显示用)
	ShowName                field.String  // 网页标签显示名称
	Logo                    field.String  // 前端页面显示的logo
	IsSuper                 field.Int32   // 每日零点是否盈利清理
	AgentUseID              field.Int32
	MajorGameOrderNew       field.String // 新游戏大类排序
	IsSelfMajorGameOrderNew field.Int32  // 新版分类是否开启1:开启2:关闭
	Logo2                   field.String // Logo2
	PromotionHost           field.String // 推广域名
	IosIcon                 field.String // 标签ICON

	fieldMap map[string]field.Expr
}

func (x xAgentIndependence) Table(newTableName string) *xAgentIndependence {
	x.xAgentIndependenceDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentIndependence) As(alias string) *xAgentIndependence {
	x.xAgentIndependenceDo.DO = *(x.xAgentIndependenceDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentIndependence) updateTableName(table string) *xAgentIndependence {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.CommissionID = field.NewInt32(table, "CommissionId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Host = field.NewString(table, "Host")
	x.CustomeService = field.NewString(table, "CustomeService")
	x.IsSelfHost = field.NewInt32(table, "IsSelfHost")
	x.IsDuiHuan = field.NewInt32(table, "IsDuiHuan")
	x.IsSelfBackOffice = field.NewInt32(table, "IsSelfBackOffice")
	x.IsSelfTgBot = field.NewInt32(table, "IsSelfTgBot")
	x.IsSelfActive = field.NewInt32(table, "IsSelfActive")
	x.IsSelfMajorGameOrder = field.NewInt32(table, "IsSelfMajorGameOrder")
	x.IsSelfMinorGameOrder = field.NewInt32(table, "IsSelfMinorGameOrder")
	x.IsSelfAddress = field.NewInt32(table, "IsSelfAddress")
	x.IsSelfCustomService = field.NewInt32(table, "IsSelfCustomService")
	x.MajorGameOrder = field.NewString(table, "MajorGameOrder")
	x.MinorGameOrder = field.NewString(table, "MinorGameOrder")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CommissionRate = field.NewFloat64(table, "CommissionRate")
	x.Memo = field.NewString(table, "Memo")
	x.TgInfo = field.NewString(table, "TgInfo")
	x.ActiveInfo = field.NewString(table, "ActiveInfo")
	x.BackOffaceInfo = field.NewString(table, "BackOffaceInfo")
	x.AgentCode = field.NewString(table, "AgentCode")
	x.State = field.NewInt32(table, "State")
	x.Icon = field.NewString(table, "Icon")
	x.AgentName = field.NewString(table, "AgentName")
	x.ShowName = field.NewString(table, "ShowName")
	x.Logo = field.NewString(table, "Logo")
	x.IsSuper = field.NewInt32(table, "IsSuper")
	x.AgentUseID = field.NewInt32(table, "AgentUseId")
	x.MajorGameOrderNew = field.NewString(table, "MajorGameOrderNew")
	x.IsSelfMajorGameOrderNew = field.NewInt32(table, "IsSelfMajorGameOrderNew")
	x.Logo2 = field.NewString(table, "Logo2")
	x.PromotionHost = field.NewString(table, "PromotionHost")
	x.IosIcon = field.NewString(table, "IosIcon")

	x.fillFieldMap()

	return x
}

func (x *xAgentIndependence) WithContext(ctx context.Context) *xAgentIndependenceDo {
	return x.xAgentIndependenceDo.WithContext(ctx)
}

func (x xAgentIndependence) TableName() string { return x.xAgentIndependenceDo.TableName() }

func (x xAgentIndependence) Alias() string { return x.xAgentIndependenceDo.Alias() }

func (x xAgentIndependence) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentIndependenceDo.Columns(cols...)
}

func (x *xAgentIndependence) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentIndependence) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 37)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["CommissionId"] = x.CommissionID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Host"] = x.Host
	x.fieldMap["CustomeService"] = x.CustomeService
	x.fieldMap["IsSelfHost"] = x.IsSelfHost
	x.fieldMap["IsDuiHuan"] = x.IsDuiHuan
	x.fieldMap["IsSelfBackOffice"] = x.IsSelfBackOffice
	x.fieldMap["IsSelfTgBot"] = x.IsSelfTgBot
	x.fieldMap["IsSelfActive"] = x.IsSelfActive
	x.fieldMap["IsSelfMajorGameOrder"] = x.IsSelfMajorGameOrder
	x.fieldMap["IsSelfMinorGameOrder"] = x.IsSelfMinorGameOrder
	x.fieldMap["IsSelfAddress"] = x.IsSelfAddress
	x.fieldMap["IsSelfCustomService"] = x.IsSelfCustomService
	x.fieldMap["MajorGameOrder"] = x.MajorGameOrder
	x.fieldMap["MinorGameOrder"] = x.MinorGameOrder
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CommissionRate"] = x.CommissionRate
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["TgInfo"] = x.TgInfo
	x.fieldMap["ActiveInfo"] = x.ActiveInfo
	x.fieldMap["BackOffaceInfo"] = x.BackOffaceInfo
	x.fieldMap["AgentCode"] = x.AgentCode
	x.fieldMap["State"] = x.State
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["AgentName"] = x.AgentName
	x.fieldMap["ShowName"] = x.ShowName
	x.fieldMap["Logo"] = x.Logo
	x.fieldMap["IsSuper"] = x.IsSuper
	x.fieldMap["AgentUseId"] = x.AgentUseID
	x.fieldMap["MajorGameOrderNew"] = x.MajorGameOrderNew
	x.fieldMap["IsSelfMajorGameOrderNew"] = x.IsSelfMajorGameOrderNew
	x.fieldMap["Logo2"] = x.Logo2
	x.fieldMap["PromotionHost"] = x.PromotionHost
	x.fieldMap["IosIcon"] = x.IosIcon
}

func (x xAgentIndependence) clone(db *gorm.DB) xAgentIndependence {
	x.xAgentIndependenceDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentIndependence) replaceDB(db *gorm.DB) xAgentIndependence {
	x.xAgentIndependenceDo.ReplaceDB(db)
	return x
}

type xAgentIndependenceDo struct{ gen.DO }

func (x xAgentIndependenceDo) Debug() *xAgentIndependenceDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentIndependenceDo) WithContext(ctx context.Context) *xAgentIndependenceDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentIndependenceDo) ReadDB() *xAgentIndependenceDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentIndependenceDo) WriteDB() *xAgentIndependenceDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentIndependenceDo) Session(config *gorm.Session) *xAgentIndependenceDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentIndependenceDo) Clauses(conds ...clause.Expression) *xAgentIndependenceDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentIndependenceDo) Returning(value interface{}, columns ...string) *xAgentIndependenceDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentIndependenceDo) Not(conds ...gen.Condition) *xAgentIndependenceDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentIndependenceDo) Or(conds ...gen.Condition) *xAgentIndependenceDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentIndependenceDo) Select(conds ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentIndependenceDo) Where(conds ...gen.Condition) *xAgentIndependenceDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentIndependenceDo) Order(conds ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentIndependenceDo) Distinct(cols ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentIndependenceDo) Omit(cols ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentIndependenceDo) Join(table schema.Tabler, on ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentIndependenceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentIndependenceDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentIndependenceDo) Group(cols ...field.Expr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentIndependenceDo) Having(conds ...gen.Condition) *xAgentIndependenceDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentIndependenceDo) Limit(limit int) *xAgentIndependenceDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentIndependenceDo) Offset(offset int) *xAgentIndependenceDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentIndependenceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentIndependenceDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentIndependenceDo) Unscoped() *xAgentIndependenceDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentIndependenceDo) Create(values ...*model.XAgentIndependence) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentIndependenceDo) CreateInBatches(values []*model.XAgentIndependence, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentIndependenceDo) Save(values ...*model.XAgentIndependence) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentIndependenceDo) First() (*model.XAgentIndependence, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependence), nil
	}
}

func (x xAgentIndependenceDo) Take() (*model.XAgentIndependence, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependence), nil
	}
}

func (x xAgentIndependenceDo) Last() (*model.XAgentIndependence, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependence), nil
	}
}

func (x xAgentIndependenceDo) Find() ([]*model.XAgentIndependence, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentIndependence), err
}

func (x xAgentIndependenceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentIndependence, err error) {
	buf := make([]*model.XAgentIndependence, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentIndependenceDo) FindInBatches(result *[]*model.XAgentIndependence, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentIndependenceDo) Attrs(attrs ...field.AssignExpr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentIndependenceDo) Assign(attrs ...field.AssignExpr) *xAgentIndependenceDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentIndependenceDo) Joins(fields ...field.RelationField) *xAgentIndependenceDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentIndependenceDo) Preload(fields ...field.RelationField) *xAgentIndependenceDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentIndependenceDo) FirstOrInit() (*model.XAgentIndependence, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependence), nil
	}
}

func (x xAgentIndependenceDo) FirstOrCreate() (*model.XAgentIndependence, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependence), nil
	}
}

func (x xAgentIndependenceDo) FindByPage(offset int, limit int) (result []*model.XAgentIndependence, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentIndependenceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentIndependenceDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentIndependenceDo) Delete(models ...*model.XAgentIndependence) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentIndependenceDo) withDO(do gen.Dao) *xAgentIndependenceDo {
	x.DO = *do.(*gen.DO)
	return x
}
