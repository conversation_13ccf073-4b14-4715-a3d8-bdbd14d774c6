// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCommissionConfig(db *gorm.DB, opts ...gen.DOOption) xAgentCommissionConfig {
	_xAgentCommissionConfig := xAgentCommissionConfig{}

	_xAgentCommissionConfig.xAgentCommissionConfigDo.UseDB(db, opts...)
	_xAgentCommissionConfig.xAgentCommissionConfigDo.UseModel(&model.XAgentCommissionConfig{})

	tableName := _xAgentCommissionConfig.xAgentCommissionConfigDo.TableName()
	_xAgentCommissionConfig.ALL = field.NewAsterisk(tableName)
	_xAgentCommissionConfig.ID = field.NewInt32(tableName, "Id")
	_xAgentCommissionConfig.Name = field.NewString(tableName, "Name")
	_xAgentCommissionConfig.ModelID = field.NewInt32(tableName, "ModelId")
	_xAgentCommissionConfig.Data = field.NewString(tableName, "Data")
	_xAgentCommissionConfig.State = field.NewInt32(tableName, "State")
	_xAgentCommissionConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCommissionConfig.CreateAccount = field.NewString(tableName, "CreateAccount")
	_xAgentCommissionConfig.Memo = field.NewString(tableName, "Memo")
	_xAgentCommissionConfig.UseCount = field.NewInt32(tableName, "UseCount")

	_xAgentCommissionConfig.fillFieldMap()

	return _xAgentCommissionConfig
}

type xAgentCommissionConfig struct {
	xAgentCommissionConfigDo xAgentCommissionConfigDo

	ALL           field.Asterisk
	ID            field.Int32  // id
	Name          field.String // 方案名称
	ModelID       field.Int32  // 模式Id
	Data          field.String // 方案数据
	State         field.Int32  // 1启用,2禁用
	CreateTime    field.Time
	CreateAccount field.String
	Memo          field.String // 备注
	UseCount      field.Int32

	fieldMap map[string]field.Expr
}

func (x xAgentCommissionConfig) Table(newTableName string) *xAgentCommissionConfig {
	x.xAgentCommissionConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCommissionConfig) As(alias string) *xAgentCommissionConfig {
	x.xAgentCommissionConfigDo.DO = *(x.xAgentCommissionConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCommissionConfig) updateTableName(table string) *xAgentCommissionConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Name = field.NewString(table, "Name")
	x.ModelID = field.NewInt32(table, "ModelId")
	x.Data = field.NewString(table, "Data")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CreateAccount = field.NewString(table, "CreateAccount")
	x.Memo = field.NewString(table, "Memo")
	x.UseCount = field.NewInt32(table, "UseCount")

	x.fillFieldMap()

	return x
}

func (x *xAgentCommissionConfig) WithContext(ctx context.Context) *xAgentCommissionConfigDo {
	return x.xAgentCommissionConfigDo.WithContext(ctx)
}

func (x xAgentCommissionConfig) TableName() string { return x.xAgentCommissionConfigDo.TableName() }

func (x xAgentCommissionConfig) Alias() string { return x.xAgentCommissionConfigDo.Alias() }

func (x xAgentCommissionConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCommissionConfigDo.Columns(cols...)
}

func (x *xAgentCommissionConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCommissionConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Name"] = x.Name
	x.fieldMap["ModelId"] = x.ModelID
	x.fieldMap["Data"] = x.Data
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CreateAccount"] = x.CreateAccount
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["UseCount"] = x.UseCount
}

func (x xAgentCommissionConfig) clone(db *gorm.DB) xAgentCommissionConfig {
	x.xAgentCommissionConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCommissionConfig) replaceDB(db *gorm.DB) xAgentCommissionConfig {
	x.xAgentCommissionConfigDo.ReplaceDB(db)
	return x
}

type xAgentCommissionConfigDo struct{ gen.DO }

func (x xAgentCommissionConfigDo) Debug() *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCommissionConfigDo) WithContext(ctx context.Context) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCommissionConfigDo) ReadDB() *xAgentCommissionConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCommissionConfigDo) WriteDB() *xAgentCommissionConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCommissionConfigDo) Session(config *gorm.Session) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCommissionConfigDo) Clauses(conds ...clause.Expression) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCommissionConfigDo) Returning(value interface{}, columns ...string) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCommissionConfigDo) Not(conds ...gen.Condition) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCommissionConfigDo) Or(conds ...gen.Condition) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCommissionConfigDo) Select(conds ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCommissionConfigDo) Where(conds ...gen.Condition) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCommissionConfigDo) Order(conds ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCommissionConfigDo) Distinct(cols ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCommissionConfigDo) Omit(cols ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCommissionConfigDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCommissionConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCommissionConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCommissionConfigDo) Group(cols ...field.Expr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCommissionConfigDo) Having(conds ...gen.Condition) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCommissionConfigDo) Limit(limit int) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCommissionConfigDo) Offset(offset int) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCommissionConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCommissionConfigDo) Unscoped() *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCommissionConfigDo) Create(values ...*model.XAgentCommissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCommissionConfigDo) CreateInBatches(values []*model.XAgentCommissionConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCommissionConfigDo) Save(values ...*model.XAgentCommissionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCommissionConfigDo) First() (*model.XAgentCommissionConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionConfig), nil
	}
}

func (x xAgentCommissionConfigDo) Take() (*model.XAgentCommissionConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionConfig), nil
	}
}

func (x xAgentCommissionConfigDo) Last() (*model.XAgentCommissionConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionConfig), nil
	}
}

func (x xAgentCommissionConfigDo) Find() ([]*model.XAgentCommissionConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCommissionConfig), err
}

func (x xAgentCommissionConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCommissionConfig, err error) {
	buf := make([]*model.XAgentCommissionConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCommissionConfigDo) FindInBatches(result *[]*model.XAgentCommissionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCommissionConfigDo) Attrs(attrs ...field.AssignExpr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCommissionConfigDo) Assign(attrs ...field.AssignExpr) *xAgentCommissionConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCommissionConfigDo) Joins(fields ...field.RelationField) *xAgentCommissionConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCommissionConfigDo) Preload(fields ...field.RelationField) *xAgentCommissionConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCommissionConfigDo) FirstOrInit() (*model.XAgentCommissionConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionConfig), nil
	}
}

func (x xAgentCommissionConfigDo) FirstOrCreate() (*model.XAgentCommissionConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionConfig), nil
	}
}

func (x xAgentCommissionConfigDo) FindByPage(offset int, limit int) (result []*model.XAgentCommissionConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCommissionConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCommissionConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCommissionConfigDo) Delete(models ...*model.XAgentCommissionConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCommissionConfigDo) withDO(do gen.Dao) *xAgentCommissionConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
