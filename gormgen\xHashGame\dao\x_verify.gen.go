// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVerify(db *gorm.DB, opts ...gen.DOOption) xVerify {
	_xVerify := xVerify{}

	_xVerify.xVerifyDo.UseDB(db, opts...)
	_xVerify.xVerifyDo.UseModel(&model.XVerify{})

	tableName := _xVerify.xVerifyDo.TableName()
	_xVerify.ALL = field.NewAsterisk(tableName)
	_xVerify.SellerID = field.NewInt32(tableName, "SellerId")
	_xVerify.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVerify.Account = field.NewString(tableName, "Account")
	_xVerify.UseType = field.NewInt32(tableName, "UseType")
	_xVerify.VerifyCode = field.NewString(tableName, "VerifyCode")
	_xVerify.CreateTime = field.NewTime(tableName, "CreateTime")

	_xVerify.fillFieldMap()

	return _xVerify
}

type xVerify struct {
	xVerifyDo xVerifyDo

	ALL        field.Asterisk
	SellerID   field.Int32  // 运营商
	ChannelID  field.Int32  // 渠道
	Account    field.String // 账号
	UseType    field.Int32  // 使用途径 1注册 2登录
	VerifyCode field.String // 验证码
	CreateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xVerify) Table(newTableName string) *xVerify {
	x.xVerifyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVerify) As(alias string) *xVerify {
	x.xVerifyDo.DO = *(x.xVerifyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVerify) updateTableName(table string) *xVerify {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Account = field.NewString(table, "Account")
	x.UseType = field.NewInt32(table, "UseType")
	x.VerifyCode = field.NewString(table, "VerifyCode")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xVerify) WithContext(ctx context.Context) *xVerifyDo { return x.xVerifyDo.WithContext(ctx) }

func (x xVerify) TableName() string { return x.xVerifyDo.TableName() }

func (x xVerify) Alias() string { return x.xVerifyDo.Alias() }

func (x xVerify) Columns(cols ...field.Expr) gen.Columns { return x.xVerifyDo.Columns(cols...) }

func (x *xVerify) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVerify) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Account"] = x.Account
	x.fieldMap["UseType"] = x.UseType
	x.fieldMap["VerifyCode"] = x.VerifyCode
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xVerify) clone(db *gorm.DB) xVerify {
	x.xVerifyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVerify) replaceDB(db *gorm.DB) xVerify {
	x.xVerifyDo.ReplaceDB(db)
	return x
}

type xVerifyDo struct{ gen.DO }

func (x xVerifyDo) Debug() *xVerifyDo {
	return x.withDO(x.DO.Debug())
}

func (x xVerifyDo) WithContext(ctx context.Context) *xVerifyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVerifyDo) ReadDB() *xVerifyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVerifyDo) WriteDB() *xVerifyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVerifyDo) Session(config *gorm.Session) *xVerifyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVerifyDo) Clauses(conds ...clause.Expression) *xVerifyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVerifyDo) Returning(value interface{}, columns ...string) *xVerifyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVerifyDo) Not(conds ...gen.Condition) *xVerifyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVerifyDo) Or(conds ...gen.Condition) *xVerifyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVerifyDo) Select(conds ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVerifyDo) Where(conds ...gen.Condition) *xVerifyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVerifyDo) Order(conds ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVerifyDo) Distinct(cols ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVerifyDo) Omit(cols ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVerifyDo) Join(table schema.Tabler, on ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVerifyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVerifyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVerifyDo) Group(cols ...field.Expr) *xVerifyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVerifyDo) Having(conds ...gen.Condition) *xVerifyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVerifyDo) Limit(limit int) *xVerifyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVerifyDo) Offset(offset int) *xVerifyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVerifyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVerifyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVerifyDo) Unscoped() *xVerifyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVerifyDo) Create(values ...*model.XVerify) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVerifyDo) CreateInBatches(values []*model.XVerify, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVerifyDo) Save(values ...*model.XVerify) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVerifyDo) First() (*model.XVerify, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVerify), nil
	}
}

func (x xVerifyDo) Take() (*model.XVerify, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVerify), nil
	}
}

func (x xVerifyDo) Last() (*model.XVerify, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVerify), nil
	}
}

func (x xVerifyDo) Find() ([]*model.XVerify, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVerify), err
}

func (x xVerifyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVerify, err error) {
	buf := make([]*model.XVerify, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVerifyDo) FindInBatches(result *[]*model.XVerify, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVerifyDo) Attrs(attrs ...field.AssignExpr) *xVerifyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVerifyDo) Assign(attrs ...field.AssignExpr) *xVerifyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVerifyDo) Joins(fields ...field.RelationField) *xVerifyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVerifyDo) Preload(fields ...field.RelationField) *xVerifyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVerifyDo) FirstOrInit() (*model.XVerify, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVerify), nil
	}
}

func (x xVerifyDo) FirstOrCreate() (*model.XVerify, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVerify), nil
	}
}

func (x xVerifyDo) FindByPage(offset int, limit int) (result []*model.XVerify, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVerifyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVerifyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVerifyDo) Delete(models ...*model.XVerify) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVerifyDo) withDO(do gen.Dao) *xVerifyDo {
	x.DO = *do.(*gen.DO)
	return x
}
