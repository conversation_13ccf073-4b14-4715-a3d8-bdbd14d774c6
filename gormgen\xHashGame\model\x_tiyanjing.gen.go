// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTiyanjing = "x_tiyanjing"

// XTiyanjing mapped from table <x_tiyanjing>
type XTiyanjing struct {
	ID                   int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增Id" json:"Id"` // 自增Id
	SellerID             int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                    // 运营商
	ChannelID            int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                   // 渠道
	UserID               int32     `gorm:"column:UserId" json:"UserId"`
	Address              string    `gorm:"column:Address;comment:地址" json:"Address"`                                               // 地址
	CSGroup              string    `gorm:"column:CSGroup;comment:客服团队" json:"CSGroup"`                                             // 客服团队
	CSID                 string    `gorm:"column:CSId;comment:客服工号" json:"CSId"`                                                   // 客服工号
	TgName               string    `gorm:"column:TgName;comment:玩家tg" json:"TgName"`                                               // 玩家tg
	IPCount              int32     `gorm:"column:IpCount;comment:同ip注册人数" json:"IpCount"`                                          // 同ip注册人数
	LoginIPCount         int32     `gorm:"column:LoginIpCount;comment:同ip登录人数" json:"LoginIpCount"`                                // 同ip登录人数
	PwdCount             int32     `gorm:"column:PwdCount;comment:同密码注册人数" json:"PwdCount"`                                        // 同密码注册人数
	AddressCount         int32     `gorm:"column:AddressCount;comment:钱包地址相同人数" json:"AddressCount"`                               // 钱包地址相同人数
	Symbol               string    `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                                 // 币种
	Amount               float64   `gorm:"column:Amount;comment:金额" json:"Amount"`                                                 // 金额
	Games                string    `gorm:"column:Games;comment:玩过哪些游戏" json:"Games"`                                               // 玩过哪些游戏
	State                int32     `gorm:"column:State;default:1;comment:状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成" json:"State"` // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
	Memo                 string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                     // 备注
	KeFuTgName           string    `gorm:"column:KeFuTgName;comment:客服tg" json:"KeFuTgName"`                                       // 客服tg
	BetCountTransfer     string    `gorm:"column:BetCountTransfer;comment:转账下注次数" json:"BetCountTransfer"`                         // 转账下注次数
	BetAmountTransfer    string    `gorm:"column:BetAmountTransfer;comment:转账下注金额" json:"BetAmountTransfer"`                       // 转账下注金额
	RewardAmountTransfer string    `gorm:"column:RewardAmountTransfer;comment:转账返奖金额" json:"RewardAmountTransfer"`                 // 转账返奖金额
	BetCountYue          int32     `gorm:"column:BetCountYue;comment:余额下注次数" json:"BetCountYue"`                                   // 余额下注次数
	BetAmountYue         float64   `gorm:"column:BetAmountYue;comment:余额下注金额" json:"BetAmountYue"`                                 // 余额下注金额
	RewardAmountYue      float64   `gorm:"column:RewardAmountYue;comment:余额返奖金额" json:"RewardAmountYue"`                           // 余额返奖金额
	BetCountThird        int32     `gorm:"column:BetCountThird;comment:三方下注次数" json:"BetCountThird"`                               // 三方下注次数
	BetAmountThird       float64   `gorm:"column:BetAmountThird;comment:三方下注金额" json:"BetAmountThird"`                             // 三方下注金额
	RewardAmountThird    float64   `gorm:"column:RewardAmountThird;comment:三方返奖金额" json:"RewardAmountThird"`                       // 三方返奖金额
	CreateTime           time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:申请时间" json:"CreateTime"`             // 申请时间
	HbcOrder             string    `gorm:"column:HbcOrder;comment:hbc订单" json:"HbcOrder"`                                          // hbc订单
	HbcState             int32     `gorm:"column:HbcState;comment:hbc状态" json:"HbcState"`                                          // hbc状态
	LoginTime            time.Time `gorm:"column:LoginTime;comment:最后登录时间" json:"LoginTime"`                                       // 最后登录时间
	BetTime              time.Time `gorm:"column:BetTime;comment:最后投注时间" json:"BetTime"`                                           // 最后投注时间
	AuditAccount         string    `gorm:"column:AuditAccount;comment:审核人" json:"AuditAccount"`                                    // 审核人
	AuditTime            time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                         // 审核时间
	SendAccount          string    `gorm:"column:SendAccount;comment:发放人" json:"SendAccount"`                                      // 发放人
	SendTime             time.Time `gorm:"column:SendTime;comment:发放时间" json:"SendTime"`                                           // 发放时间
	Account              string    `gorm:"column:Account;comment:玩家账号" json:"Account"`                                             // 玩家账号
	RegisterTime         time.Time `gorm:"column:RegisterTime;comment:玩家注册时间" json:"RegisterTime"`                                 // 玩家注册时间
	TxID                 string    `gorm:"column:TxId;comment:交易id" json:"TxId"`                                                   // 交易id
	ApplyType            int32     `gorm:"column:ApplyType;default:1;comment:申请分类 1用户申请 2客服申请" json:"ApplyType"`                   // 申请分类 1用户申请 2客服申请
	OperUserAccount      string    `gorm:"column:OperUserAccount;comment:操作员账号" json:"OperUserAccount"`                            // 操作员账号
	OperUserID           int32     `gorm:"column:OperUserId;comment:操作员账号Id" json:"OperUserId"`                                    // 操作员账号Id
	UpdateTime           time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`    // 更新时间
}

// TableName XTiyanjing's table name
func (*XTiyanjing) TableName() string {
	return TableNameXTiyanjing
}
