package single

import (
	"encoding/json"
	"errors"
	"fmt"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"io"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
)

// NewFbService FB体育初始化入口
/**
错误码地址：
https://doc.newsportspro.com/apidoc_data.html#exception
https://doc.newsportspro.com/h5_pc_doc.html#%E6%AD%A5%E9%AA%A4%E4%BA%8C%E8%8E%B7%E5%8F%82-url-%E6%8B%BC%E6%8E%A5%E8%A7%84%E5%88%99
https://doc.newsportspro.com/single_wallet.html
单数据推送： 跟订单相关，推送跟普通订单相关的消息
cashOut数据推送：就是有提前结算订单接单，结算时调用的流水推送，是除了下单扣款之外的所有资金变化都会调用
注意：fb体育三方未鉴权是裸掉接口，需要把FB体育IP加白确保接口调用安全
*/
func NewFbService(temp map[string]string, fc func(int) error) *FbService {
	games := map[string]string{
		"1":    "足球",
		"2":    "冰球",
		"3":    "篮球",
		"4":    "橄榄球",
		"5":    "网球",
		"6":    "美国足球",
		"7":    "棒球",
		"8":    "手球",
		"10":   "地板球",
		"12":   "高尔夫球",
		"13":   "排球",
		"14":   "玩板球",
		"15":   "乒乓球",
		"16":   "斯诺克台球",
		"17":   "五人制足球",
		"18":   "综合格斗",
		"19":   "拳击",
		"20":   "飞镖",
		"21":   "草地滚球",
		"24":   "水球",
		"25":   "自行车",
		"47":   "羽毛球",
		"51":   "沙滩排球",
		"92":   "F1赛车",
		"93":   "特殊投注",
		"94":   "赛车",
		"95":   "摩托车赛 （支持玩法：冠军）",
		"100":  "奥林匹克",
		"101":  "亚运会 （支持玩法：冠军）",
		"164":  "刀塔2",
		"165":  "LOL英雄联盟",
		"177":  "电子足球",
		"178":  "电子篮球",
		"179":  "反恐精英",
		"180":  "王者荣耀",
		"1001": "虚拟足球 （支持玩法：普通）",
		"1020": "虚拟赛马 （支持玩法：普通）",
		"1021": "虚拟赛狗 （支持玩法：普通）",
		"1022": "虚拟沙地摩托车 （支持玩法：普通）",
		"1023": "虚拟摩托车 （支持玩法：普通）",
	}

	currencyEn := map[string]string{
		"1":   "CNY",  // 人民币
		"2":   "USD",  // 美元
		"3":   "EUR",  // 欧元
		"4":   "GBP",  // 英镑
		"5":   "HKD",  // 港元
		"6":   "TWD",  // 台币
		"7":   "MYR",  // 马币
		"8":   "SGD",  // 新加坡元
		"9":   "THB",  // 泰铢
		"10":  "VND",  // 越南盾
		"11":  "KRW",  // 韩元
		"12":  "JPY",  // 日元
		"13":  "PHP",  // 菲律宾比索
		"14":  "IDR",  // 印尼盾
		"15":  "INR",  // 印度卢比
		"16":  "AUD",  // 澳元
		"17":  "MMK",  // 缅甸元
		"18":  "COP",  // 哥伦比亚比索
		"19":  "TZS",  // 坦桑尼亚先令
		"20":  "NGN",  // 尼日利亚奈拉
		"21":  "ZMW",  // 赞比亚克瓦查
		"22":  "BRL",  // 巴西雷亚尔
		"23":  "MXN",  // 墨西哥比索
		"24":  "RUB",  // 俄罗斯卢布
		"25":  "EGP",  // 埃及镑
		"26":  "PEN",  // 秘鲁新索尔
		"27":  "BOB",  // 玻利维亚诺
		"28":  "PKR",  // 巴基斯坦卢比
		"29":  "BDT",  // 孟加拉塔卡
		"30":  "UGX",  // 乌干达先令
		"31":  "ETB",  // 埃塞俄比亚比尔
		"32":  "ZAR",  // 南非兰特
		"33":  "HNL",  // 洪都拉斯伦皮拉
		"34":  "CDF",  // 刚果法郎
		"35":  "TRY",  // 土耳其里拉
		"36":  "IRR",  // 伊朗里亚尔
		"37":  "GHS",  // 加纳塞地
		"38":  "KES",  // 肯尼亚先令
		"39":  "ILS",  // 以色列新谢克尔
		"40":  "CLP",  // 智利比索
		"41":  "KZT",  // 哈萨克斯坦坚戈
		"42":  "KGS",  // 吉尔吉斯斯坦索姆
		"43":  "TJS",  // 塔吉克斯坦索莫尼
		"44":  "TMT",  // 土库曼斯坦马纳特
		"45":  "UZS",  // 乌兹别克索姆
		"46":  "LAK",  // 老挝基普
		"47":  "BND",  // 文莱元
		"48":  "CAD",  // 加拿大元
		"49":  "NZD",  // 新西兰元
		"50":  "PLN",  // 波兰兹罗提
		"51":  "ARS",  // 阿根廷比索
		"52":  "KHR",  // 柬埔寨瑞尔
		"53":  "NIO",  // 尼加拉瓜科多巴
		"54":  "ZWL",  // 津巴布韦元
		"55":  "IMP",  // 曼岛镑
		"56":  "LKR",  // 斯里兰卡卢比
		"200": "USDT", // 泰达币
		"201": "BTC",  // 比特币
	}

	gamesEn := map[string]string{
		"Soccer":           "足球",
		"Basketball":       "篮球",
		"Outright":         "冠军",
		"Tennis":           "网球",
		"Americanfootball": "美式橄榄球",
		"Cricket":          "板球",
		"Handball":         "手球",
		"Icehockey":        "冰球",
		"Snooker":          "斯诺克",
		"Volleyball":       "排球",
		"Badminton":        "羽毛球",
		"Multiple Parlay":  "过关",
		"Esport":           "电子竞技",
	}
	return &FbService{
		url:        temp["url"],
		merchantId: temp["merchant_id"], //商户号
		key:        temp["key"],         //密钥
		currency:   temp["currency"],
		//platform:              temp["platform"],
		brandName:             "fb", //FB体育
		currencys:             currencyEn,
		games:                 games,
		games_en:              gamesEn,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

// FB体育transferType 转账类型
const (
	FB_TRANSFER_Bet                         = "BET"                            // 押注
	FB_TRANSFER_Win                         = "WIN"                            // 派彩
	FB_TRANSFER_Refund                      = "REFUND"                         // 退款 订单撤单
	FB_TRANSFER_Cashout                     = "CASHOUT"                        // 提前结算
	FB_TRANSFER_CancelDeduct                = "CANCEL_DEDUCT"                  // 订单取消补扣
	FB_TRANSFER_CancelReturn                = "CANCEL_RETURN"                  // 订单取消返还
	FB_TRANSFER_SettlementRollbackDeduct    = "SETTLEMENT_ROLLBACK_DEDUCT"     // 结算回滚补扣
	FB_TRANSFER_CashoutCancelDeduct         = "CASHOUT_CANCEL_DEDUCT"          // 提前结算订单取消补扣
	FB_TRANSFER_CashoutCancelReturn         = "CASHOUT_CANCEL_RETURN"          // 提前结算订单取消返还
	FB_TRANSFER_CashoutCancelRollbackDeduct = "CASHOUT_CANCEL_ROLLBACK_DEDUCT" // 提前结算取消回滚补扣
	FB_TRANSFER_CashoutCancelRollbackReturn = "CASHOUT_CANCEL_ROLLBACK_RETURN" // 提前结算取消回滚返还
)

type FbService struct {
	url        string
	merchantId string
	brandName  string
	key        string
	currency   string
	//platform              string
	currencys             map[string]string
	games                 map[string]string
	games_en              map[string]string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

var cacheKeyFb = "cacheKeyFb:"

// 用户登录
func (l *FbService) login(userId string, platform string) (body []byte, err error) {
	type RequestData struct {
		MerchantUserId string `json:"merchantUserId"` // 渠道用户id，40位字符串，不能为空
		PlatForm       string `json:"platForm"`       // 平台类型，pc，h5, mobile，不能为空
		Ip             string `json:"ip,omitempty"`   // 客户端用户ip地址，尽可能提供，用于风控
	}

	reqData := RequestData{
		MerchantUserId: userId,
		PlatForm:       platform,
		Ip:             "",
	}

	urlReq := l.url + "/fb/data/api/v2/token/get"
	reqBytes, _ := json.Marshal(reqData)
	timestamp := time.Now().Unix()
	sign, err := generateSignature(reqBytes, l.merchantId, timestamp, l.key)
	if err != nil {
		logs.Error("fb login 生成签名出错", err)
		return nil, fmt.Errorf("生成签名出错: %v", err)
	}

	header := map[string]string{
		"sign":       sign,
		"timestamp":  fmt.Sprintf("%d", timestamp),
		"merchantId": l.merchantId,
	}

	logs.Debug("fb login receive:%s, ", string(reqBytes))
	httpclient := httpc.DoRequest{
		UrlPath:    urlReq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}

	data, err := httpclient.DoPostBytes()
	if err != nil {
		logs.Error("fb login:", err)
		return nil, err
	}
	//logs.Info("[fb] login ==>", string(data))
	return data, nil
}

// 响应给第三方结构体
// Balance float64 `json:"balance"`
type fbResp struct {
	Message string `json:"message"` // 描述信息
	Code    int    `json:"code"`    // 返回码
}

// HotRecords 赛事结构体
type HotRecords struct {
	Nsg []struct {
		Pe  int   `json:"pe"`
		Tyg int   `json:"tyg"`
		Sc  []int `json:"sc"`
	} `json:"nsg"`
	Mg []struct {
		Mty int `json:"mty"`
		Pe  int `json:"pe"`
		Mks []struct {
			Op []struct {
				Na  string  `json:"na"`
				Nm  string  `json:"nm"`
				Tid int     `json:"tid"`
				Ty  int     `json:"ty"`
				Od  float64 `json:"od"`
				Bod float64 `json:"bod"`
				Odt int     `json:"odt"`
				Otc int     `json:"otc"`
				Li  string  `json:"li"`
				ID  int     `json:"id"`
				SS  int     `json:"ss"`
			} `json:"op"`
			A   int    `json:"a"`
			Mbl int    `json:"mbl"`
			L   string `json:"l"`
			ID  int    `json:"id"`
			SS  int    `json:"ss"`
		} `json:"mks"`
		Tps []string `json:"tps"`
		Nm  string   `json:"nm"`
		Tms int      `json:"tms"`
	} `json:"mg"`
	Lg struct {
		Mt   int    `json:"mt"`
		Na   string `json:"na"`
		ID   int    `json:"id"`
		Or   int    `json:"or"`
		Lurl string `json:"lurl"`
		Sid  int    `json:"sid"` //运动种类id
		Rnm  string `json:"rnm"`
		Rlg  string `json:"rlg"`
		Hot  bool   `json:"hot"` //是否热门
		Slid int    `json:"slid"`
	} `json:"lg"`
	Ts []struct {
		Na   string `json:"na"`
		ID   int    `json:"id"`
		Lurl string `json:"lurl"`
	} `json:"ts"`
	Mc struct {
		S   int  `json:"s"`
		Pe  int  `json:"pe"`
		R   bool `json:"r"`
		Tp  int  `json:"tp"`
		Itd int  `json:"itd"`
	} `json:"mc"`
	ID  int      `json:"id"`
	Bt  int      `json:"bt"`
	Ms  int      `json:"ms"`
	Fid int      `json:"fid"`
	Fmt int      `json:"fmt"`
	Ss  int      `json:"ss"`
	Ne  int      `json:"ne"`
	As  []string `json:"as"`
	Sid int      `json:"sid"`
	Ssi int      `json:"ssi"`
	Mp  string   `json:"mp"`
	Smt int      `json:"smt"`
	Ty  int      `json:"ty"`
	Ye  string   `json:"ye"`
	Nm  string   `json:"nm"`
	Sb  struct {
		Ihs int    `json:"ihs"`
		Ias int    `json:"ias"`
		Rp  string `json:"rp"`
		Rd  int    `json:"rd"`
		Ry  int    `json:"ry"`
		Sv  string `json:"sv"`
		Srr int    `json:"srr"`
		Bs  int    `json:"bs"`
		Bb  int    `json:"bb"`
		Bo  int    `json:"bo"`
		Bbs string `json:"bbs"`
		Hhs int    `json:"hhs"`
		Has int    `json:"has"`
		C   int    `json:"c"`
		Cd  int    `json:"cd"`
	} `json:"sb"`
	Pl int `json:"pl"`
}

// ResponseHotEvents 赛事响应数据
type ResponseHotEvents struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	PageTotal int    `json:"pageTotal"`
	Code      int    `json:"code"`
	Data      struct {
		Current int          `json:"current"`
		Size    int          `json:"size"`
		Total   int          `json:"total"`
		Records []HotRecords `json:"records"`
	} `json:"data"`
}

type RequestHotData struct {
	Lang    string `json:"lang"`    // 语言代号，不携带默认返回英文内容
	Current int    `json:"current"` // 当前页码
	SportId int    `json:"SportId"` // 运动类型
}

// 定义分页查询函数
func (l *FbService) FetchHotEvents(ctx *abugo.AbuHttpContent, reqData RequestHotData, number int) (*[]HotRecords, error) {
	var filteredRecords []HotRecords

	// 热门赛事请求
	type ReqData struct {
		Current      int    `json:"current"`      // 当前页码
		SportId      int    `json:"sportId"`      // 运动类型
		LanguageType string `json:"languageType"` // 语言
		Size         int    `json:"size"`         // 每页大小
		Type         string `json:"type"`         // 类型 0:全部赛事
	}

	reqData_ := ReqData{
		LanguageType: reqData.Lang,    // 语言
		Current:      reqData.Current, // 当前页
		SportId:      reqData.SportId, // 运动类型数组，足球、篮球、橄榄球、板球、综合格斗
		Size:         50,              // 每页50条记录
		Type:         "0",
	}

	urlReq := l.url + "/fb/data/api/v2/match/page"
	//v1/match/getList
	reqBytes, _ := json.Marshal(reqData_)
	timestamp := time.Now().Unix()
	sign, err := generateSignature(reqBytes, l.merchantId, int64(timestamp), l.key)
	if err != nil {
		logs.Error("fb体育 拉取热门赛事 生成签名出错", err)
		return nil, err
	}

	header := map[string]string{
		"sign":       sign,
		"timestamp":  fmt.Sprintf("%d", timestamp),
		"merchantId": l.merchantId,
	}

	//logs.Info("fb 拉取热门赛事请求参数 reqData:", string(reqBytes), header)
	httpclient := httpc.DoRequest{
		UrlPath:    urlReq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}

	data, err := httpclient.DoPostBytes()
	if err != nil {
		logs.Error("fb体育 拉取热门赛事出错，err", err)
		return nil, err
	}

	//logs.Info("[fb]  拉取热门赛事返回数据 ==>", string(data))

	resData := ResponseHotEvents{}
	err = json.Unmarshal(data, &resData)
	if err != nil {
		logs.Error("fb体育 拉取热门赛事失败:", resData.Message)
		return nil, err
	}
	code := resData.Code
	if code != 0 { //9003禁止投注
		logs.Error("fb体育 拉取热门赛事失败:", resData.Message)
		return nil, errors.New(resData.Message)
	}

	// 使用map来存储已经存在的记录，以便快速检查重复性
	existingRecords := make(map[int]struct{})
	// 只取热门数据，最多取5条
	for _, record := range resData.Data.Records {
		if record.Lg.Hot && len(filteredRecords) < number {
			if _, exists := existingRecords[record.ID]; !exists {
				filteredRecords = append(filteredRecords, record)
				existingRecords[record.ID] = struct{}{}
			}
		}
	}

	// 如果filteredRecords不足5条，并且resData.Data.Records的记录数小于5，填充剩余数据
	if len(filteredRecords) < number {
		for _, record := range resData.Data.Records {
			if len(filteredRecords) >= number {
				break
			}
			if _, exists := existingRecords[record.ID]; !exists {
				filteredRecords = append(filteredRecords, record)
				existingRecords[record.ID] = struct{}{}
			}
		}
	}

	//logs.Info("[fb]  拉取热门赛事[SportId=%v]返回数据长度 ==>%d", reqData.SportId, len(filteredRecords))
	return &filteredRecords, nil
}

// GetHotEvents 获取热门赛事
func (l *FbService) GetHotEvents(ctx *abugo.AbuHttpContent) {

	errCode := 0
	reqData := RequestHotData{}
	err := ctx.RequestData(&reqData)
	if err != nil {
		logs.Error("fb体育 拉取热门赛事，参数解析失败：", err)
		ctx.RespErrString(true, &errCode, "获取热门赛事失败")
		return
	}

	// 缓存key
	rKey := fmt.Sprintf("%s:%s:fb:GetHotEvents:%s", server.Project(), server.Module(), reqData.Lang)
	rValueInterface := server.Redis().Get(rKey)
	rValue := abugo.GetStringFromInterface(rValueInterface)
	if rValue != "" {
		// 已经有缓存 直接返回
		//logs.Info("fb体育 拉取热门赛事，从缓存中返回数据")
		ctx.RespOK(rValue)
		return
	}

	reqData.Current = 1
	reqData.SportId = 3 //篮球
	records, err := l.FetchHotEvents(ctx, reqData, 3)
	if err != nil {
		//logs.Error("fb体育 拉取[篮球]热门赛事失败:", err.Error())
		ctx.RespErrString(true, &errCode, "获取热门赛事失败")
		return
	}

	var filteredRecords []HotRecords //返回的热门赛事总记录
	for _, record := range *records {
		filteredRecords = append(filteredRecords, record)
	}

	//reqData.Current = 1
	//reqData.SportId = 4 //4 橄榄球
	//records, err = l.FetchHotEvents(ctx, reqData, 3)
	//if err != nil {
	//	logs.Error("fb体育 拉取[橄榄球]热门赛事失败:", err)
	//}
	//for _, record := range *records {
	//	filteredRecords = append(filteredRecords, record)
	//}

	reqData.Current = 1
	reqData.SportId = 14 //14 板球
	records, err = l.FetchHotEvents(ctx, reqData, 3)
	if err != nil {
		logs.Error("fb体育拉取[板球]热门赛事失败:", err)
	}
	for _, record := range *records {
		filteredRecords = append(filteredRecords, record)
	}

	reqData.Current = 1
	reqData.SportId = 18 //18 综合格斗
	records, err = l.FetchHotEvents(ctx, reqData, 3)
	if err != nil {
		logs.Error("fb体育 拉取[综合格斗]热门赛事失败:", err)
	}
	for _, record := range *records {
		filteredRecords = append(filteredRecords, record)
	}

	var number int = 12 - len(filteredRecords)
	reqData.Current = 1
	reqData.SportId = 1 //1 足球
	records, err = l.FetchHotEvents(ctx, reqData, number)
	if err != nil {
		logs.Error("fb体育 拉取[足球]热门赛事失败:", err)
	}
	for _, record := range *records {
		filteredRecords = append(filteredRecords, record)
	}

	// 注单详情
	reqDataByte, _ := json.Marshal(filteredRecords)
	defer func() {
		if e := server.Redis().SetStringEx(rKey, 5, string(reqDataByte)); e != nil {
			logs.Error("[ERROR][FB] GetHotEvents Redis SetStringEx err=", e.Error())
		}

		//logs.Info("[fb] GetHotEvents 热门赛事放入缓存,热门赛事数量=", len(filteredRecords))
	}()
	ctx.RespOK(string(reqDataByte))
	return
}

// 对请求体 JSON 字符串中的属性按升序排序
func sortRequestBodyJSON(jsonData []byte) ([]byte, error) {
	var data map[string]interface{}
	err := json.Unmarshal(jsonData, &data)
	if err != nil {
		return nil, fmt.Errorf("JSON解码出错: %v", err)
	}

	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sortedData := make(map[string]interface{})
	for _, k := range keys {
		sortedData[k] = data[k]
	}

	sortedJSON, err := json.Marshal(sortedData)
	if err != nil {
		return nil, fmt.Errorf("JSON编码出错: %v", err)
	}

	return sortedJSON, nil
}

// 生成签名
func generateSignature(reqBytes []byte, merchantId string, timestamp int64, key string) (string, error) {
	sortedReqJSON, err := sortRequestBodyJSON(reqBytes)
	if err != nil {
		return "", err
	}
	stringSign := string(sortedReqJSON) + "." + merchantId + "." + fmt.Sprintf("%d", timestamp) + "." + key
	sign := base.MD5(stringSign)
	return sign, nil
}

// Register 用户注册
func (l *FbService) register(username string, userId int) (body []byte, err error) {

	type RequestData struct {
		MerchantUserId string `json:"merchantUserId"`
		CurrencyIds    []int  `json:"currencyIds"`
		//OddsLevel      int    `json:"oddsLevel"`
	}

	currency, _ := strconv.Atoi(l.currency) //默认币种2 usd
	reqData := RequestData{
		MerchantUserId: username,
		CurrencyIds:    []int{currency}, //币种
		//OddsLevel: 1,
	}

	urlReq := l.url + "/fb/data/api/v2/new/user/create"
	reqBytes, _ := json.Marshal(reqData)
	timestamp := time.Now().Unix()
	sign, err := generateSignature(reqBytes, l.merchantId, timestamp, l.key)
	if err != nil {
		logs.Error("fb register 生成签名出错", err)
		return nil, fmt.Errorf("生成签名出错: %v", err)
	}

	header := map[string]string{
		"sign":       sign,
		"timestamp":  fmt.Sprintf("%d", timestamp),
		"merchantId": l.merchantId,
	}

	logs.Debug("fb register receive:%s, ", string(reqBytes))
	httpclient := httpc.DoRequest{
		UrlPath:    urlReq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}
	data, err := httpclient.DoPostBytes()
	if err != nil {
		logs.Error("fb register:", err)
		return nil, err
	}
	logs.Info("[fb] register ==>", string(data))
	return data, nil
}

// ServerInfo 包含服务器地址信息的结构体定义
type ServerInfo struct {
	APIServerAddress         string `json:"apiServerAddress"`         // App接口服务地址
	APIEmbeddedServerAddress string `json:"apiEmbeddedServerAddress"` // App内嵌网页地址
	PushServerAddress        string `json:"pushServerAddress"`        // 推送服务地址赔率服务地址
	PCAddress                string `json:"pcAddress"`                // PC投注网站地址
	H5Address                string `json:"h5Address"`                // H5投注网站地址
	VirtualAddress           string `json:"virtualAddress"`           // 虚拟体育投注网站地址
	VirtualMatchVideoAddress string `json:"virtualMatchVideoAddress"` // 虚拟赛事视频地址
}

// Domain 包含域名信息的结构体定义
type Domain struct {
	Type    int      `json:"type"`    // 域名类型，参见枚举：domain_type_enum
	Domains []string `json:"domains"` // 域名集合
}

// TokenData 包含token响应数据的结构体定义
type TokenData struct {
	Token        string     `json:"token"`        // 用户鉴权token，用于客户端鉴权
	ServerInfo   ServerInfo `json:"serverInfo"`   // 服务器地址信息
	Domains      []Domain   `json:"domains"`      // 全部服务器地址信息
	ThemeBgColor string     `json:"themeBgColor"` // 主题背景色
	ThemeFgColor string     `json:"themeFgColor"` // 主题前景色
	UserId       int        `json:"userId"`       // FB用户ID
}

// TokenResponse token接口返回结构体
type TokenResponse struct {
	Success bool      `json:"success"` // 是否成功
	Message string    `json:"message"` // 描述信息
	Data    TokenData `json:"data"`    // 业务数据
	Code    int       `json:"code"`    // 返回码
}

// CreateResponse 创建用户接口返回响应结构体
type CreateResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 描述信息
	Data    int    `json:"data"`    // 业务数据
	Code    int    `json:"code"`    // 返回码
}

// LoginRequestData 登录结构体
type LoginRequestData struct {
	Lang     string
	Platform interface{} `json:"Platform"` // Can be string or number
	GameId   string      `json:"GameId,omitempty"`
	SellerId interface{} `json:"SellerId,omitempty"`
}

// 获取FB体育登录URL链接网址
func generateFBUrl(resData TokenResponse, token *server.TokenData, reqdata LoginRequestData, currency string, platform string) string {
	serverInfo := resData.Data.ServerInfo
	fbToken := resData.Data.Token

	// 根据平台类型选择不同的服务 器地址
	var serverAddress string
	if platform == "pc" {
		serverAddress = serverInfo.PCAddress
	} else {
		serverAddress = serverInfo.H5Address
	}

	nickname := token.Account
	apiSrc := serverInfo.APIServerAddress
	pushSrc := serverInfo.PushServerAddress
	virtualSrc := serverInfo.VirtualAddress
	platformName := "FB体育"
	icoUrl := "https://fsports.co/favicon.ico"
	handicap := "1"
	language := reqdata.Lang //"CMN"
	color := "dark"
	cid := currency //币种Id
	themeBg := "6D060D"
	//tutorialPop := "1"
	noType := "2"
	hideBalance := "1"
	themeText := `{"h5FgColor":"333333","pcFgColor":"888888"}`
	//拼接FB体育URL
	URL_SRC := fmt.Sprintf("%s/index.html#/?token=%s&nickname=%s&apiSrc=%s&virtualSrc=%s&pushSrc=%s&platformName=%s&icoUrl=%s&handicap=%s&language=%s&color=%s&currencyId=%s&themeBg=%s&themeText=%s&type=1&sportId=1&controlMenu=1&noType=%s&hideBalance=%s", serverAddress, fbToken, nickname, apiSrc, virtualSrc, pushSrc, platformName, icoUrl, handicap, language, color, cid, themeBg, themeText, noType, hideBalance)

	return URL_SRC
}

// FbLoginAndRegister FB体育登录
func (l *FbService) FbLoginAndRegister(ctx *abugo.AbuHttpContent) {

	//userIP := ctx.GetIp()
	//ot := getRegion(userIP)

	errCode := 0
	reqData := LoginRequestData{}
	err := ctx.RequestData(&reqData)
	if err != nil {
		logs.Error("fb体育登录，参数解析失败：", err)
		ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
		return
	}

	// 处理平台类型，pc，h5, mobile
	platform := "h5" // 默认为h5

	// 根据请求中的Platform参数确定平台类型
	switch v := reqData.Platform.(type) {
	case string:
		if strings.ToLower(v) == "pc" {
			platform = "pc"
		} else {
			platform = "h5"
		}
	case float64:
		// 数字类型的Platform，不是pc，所以使用默认的h5
		platform = "h5"
	default:
		// 其他情况使用默认的h5
		platform = "h5"
	}

	token := server.GetToken(ctx)
	if err, errCode = base.IsLoginByUserId(cacheKeyFb, token.UserId); err != nil { //判断用户状态是否正常
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}
	logs.Info("[fb] 体育登录，userId=", token.UserId)
	userId := strconv.Itoa(token.UserId)

	login, err := l.login(userId, platform) //先执行登录如果登录失败则注册用户
	if err != nil {
		logs.Error("fb体育登录失败:", err)
		ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
		return
	}
	resData := TokenResponse{}
	err = json.Unmarshal(login, &resData)
	code := resData.Code
	if code == 9003 { //9003禁止投注
		logs.Error("fb体育登录失败:", resData.Message)
		ctx.RespErrString(true, &errCode, "进入失败,该账号禁止投注,请稍后再试")
		return
	}

	if code == 0 { // 0:成功 9002:会员不存在
		//拼接FB体育URL
		url := generateFBUrl(resData, token, reqData, l.currency, platform)
		ctx.Put("url", url) //返回的游戏页面链接
		ctx.RespOK()
		logs.Info("[fb] 体育登录登录成功，userId=", token.UserId)
		return
	} else if code == 9002 { //如果用户不存在则注册用户
		logs.Info("[fb] 体育登录,用户不存在注册用户，userId=", token.UserId)
		register, err := l.register(userId, token.UserId)
		if err != nil {
			logs.Error("fb体育注册用户失败:userId=", userId, err)
			ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
			return
		}
		resCreateData := CreateResponse{}
		err = json.Unmarshal(register, &resCreateData)
		if resData.Code == 0 || resData.Code == 9002 { //0:成功 9002:会员已存在 重新执行登录操作
			login, err := l.login(strconv.Itoa(token.UserId), platform)
			if err != nil {
				logs.Error("fb体育用户登录失败:userId=", userId, err)
				ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
				return
			}

			err = json.Unmarshal(login, &resData)
			if !resData.Success {
				logs.Error("fb体育登录失败:", resData.Message)
				ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
				return
			}
			//拼接FB体育URL
			url := generateFBUrl(resData, token, reqData, l.currency, platform)
			ctx.Put("url", url) //返回的游戏页面链接
			ctx.RespOK()
			logs.Info("[fb] 体育登录登录成功，userId=", token.UserId)
			return

		} else {
			ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
			return
		}

	} else {
		logs.Error("fb体育登录失败:", resData.Message)
		ctx.RespErrString(true, &errCode, "进入失败,请稍后再试")
		return
	}
}

// GetBalance 余额请求
func (l *FbService) GetBalance(ctx *abugo.AbuHttpContent) {

	// BalanceRequest 包含余额查询请求的结构体定义
	type RequestData struct {
		MerchantUserId string `json:"merchantUserId"` // 渠道用户ID (必填)
		MerchantId     string `json:"merchantId"`     // 商户ID (必填)
		CurrencyId     int    `json:"currencyId"`     // 币种ID
	}

	// BalanceData 包含余额数据的结构体定义
	type BalanceData struct {
		Balance    string `json:"balance"`    // 用户钱包余额，支持2位小数
		CurrencyId int    `json:"currencyId"` // 币种id，参见枚举：currency
	}

	type BalanceResponse struct {
		Message string        `json:"message" comment:"描述信息"`
		Code    int           `json:"code"` // 返回码
		Success bool          `json:"success" comment:"是否成功"`
		Data    []BalanceData `json:"data" comment:"数据"`
	}

	reqData := RequestData{}
	resData := fbResp{}

	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		resData.Code = 201
		resData.Message = "数据解析失败！"
		ctx.RespJson(resData)
		logs.Error("数据解析失败:", err.Error())
		return
	}

	logs.Debug("fb GetBalance api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		resData.Code = 203
		resData.Message = "参数错误！"
		ctx.RespJson(resData)
		logs.Error("fb GetBalance 数据解析错误", err.Error())
		return
	}

	authToken := reqData.MerchantId
	if !l.checkToken(authToken) {
		resData.Code = 1
		resData.Message = "密钥不正确！"
		ctx.RespJson(resData)
		logs.Error("fb 密钥不正确")
		return
	}

	userId_ := reqData.MerchantUserId
	userId, _ := strconv.Atoi(userId_)
	_, balance, err := base.GetUserById(userId)
	if err != nil {
		resData.Code = 203
		resData.Message = "查询用户余额失败！"
		ctx.RespJson(resData)
		logs.Error("fb GetBalance 查询用户余额失败", err.Error())
		return
	}

	currency, _ := strconv.Atoi(l.currency) //默认币种2 usd
	balanceResp := BalanceResponse{
		Message: "",
		Code:    0,
		Success: true,
		Data: []BalanceData{
			{Balance: fmt.Sprintf("%.2f", balance), CurrencyId: currency},
		},
	}

	ctx.RespJson(balanceResp)
}

// Bet 下注
// 下单时会调用此接口进行扣款，返回支付状态，返回码：0 成功，1失败，6 处理异常，9 余额不足
// order_pay
func (l *FbService) Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		TransactionId   string  `json:"transactionId"`   // 交易流水ID，全服唯一
		UserId          string  `json:"userId"`          // FB用户ID
		MerchantId      string  `json:"merchantId"`      // 渠道ID
		MerchantUserId  string  `json:"merchantUserId"`  // 渠道用户ID
		BusinessId      string  `json:"businessId"`      // 业务ID，即订单ID
		TransactionType string  `json:"transactionType"` // 交易类型 OUT 转出，IN 转入
		TransferType    string  `json:"transferType"`    // 转账类型 , see enum: transfer_type_enum
		CurrencyId      int     `json:"currencyId"`      // 币种ID , see enum: currency
		Amount          float64 `json:"amount"`          // 流水金额
		Status          int     `json:"status"`          // 转账状态, 0 取消，1成功; see enum: transfer_status_enum
		RelatedId       string  `json:"relatedId"`       // 三方数据关联ID，可为空，下单时三方带的订单ID; 可选
		ThirdRemark     string  `json:"thirdRemark"`     // 三方商户下单使用的备注，可为空，下单时三方带的备注; 可选
	}

	reqData := RequestData{}
	resData := fbResp{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	logs.Debug("fb Bet api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		resData.Code = 1
		resData.Message = "参数错误！"
		ctx.RespJson(resData)
		logs.Error("fb Bet 参数错误", err.Error())
		return
	}

	authToken := reqData.MerchantId
	if !l.checkToken(authToken) {
		resData.Code = 1
		resData.Message = "密钥不正确！"
		ctx.RespJson(resData)
		logs.Error("fb 密钥不正确")
		return
	}

	//记录下注结果 fb体育通过注单上报接口上报投注内容，下注时无投注内容
	_betCtx := "" //l.getReqOrderInfo(string(reqDataByte))
	userId_ := reqData.MerchantUserId
	userId, _ := strconv.Atoi(userId_)

	//三方来源的数据整理
	var (
		betAmount = reqData.Amount
		thirdId   = reqData.BusinessId
		thirdTime = utils.GetCurrentTime()
	)

	betAmount = math.Abs(betAmount) //三方返回的的Amount是负数，需要取绝对值变正数
	gameCode := ""                  //通过注单上报接口获取游戏类型
	gameName := ""                  //
	//币种转换
	currency := l.currencys[strconv.Itoa(reqData.CurrencyId)]

	tablePre := "x_third_sport_pre_order"

	logs.Info("fb 下注开始执行事务", " thirdId=", thirdId, " userId=", thirdId, " betAmount=", betAmount)
	// 开始投注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("fb Bet 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				resData.Code = 6
				resData.Message = "会员不存在"
			} else {
				resData.Code = 6
				resData.Message = "查询用户信息失败"
			}
			return e
		}
		if userBalance.State != 1 {
			resData.Code = 6
			resData.Message = "该账号禁止进入"
			e = errors.New("该账号禁止进入")
			logs.Error("fb Bet 该账号禁止进入 thirdId=", thirdId, " userId=", userId)
			return e
		}

		// 查询注单是否存在
		order := thirdGameModel.ThirdSportOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e == nil {
			logs.Error("fb Bet 注单已存在 thirdId=", thirdId, " order=", order)
			resData.Code = 1
			resData.Message = "注单已存在"
			e = errors.New("注单已存在")
			return e
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("fb Bet 查询注单失败 thirdId=", thirdId, " err=", e.Error())
			resData.Code = 6
			resData.Message = "查询注单失败"
			return e
		}

		// 下注金额大于用户余额
		if userBalance.Amount < 0 || userBalance.Amount < betAmount {
			logs.Error("fb Bet 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " betAmount=", betAmount)
			e = errors.New("玩家余额不足")
			resData.Code = 9
			resData.Message = "玩家余额不足"
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, betAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("fb Bet 扣款失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			resData.Code = 6
			resData.Message = "扣款失败！"
			return e
		}

		//获取投注渠道
		BetChannelId := base.GetUserChannelId(ctx, &userBalance)
		order = thirdGameModel.ThirdSportOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: BetChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gameCode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     currency, //FB体育需要转换为真实的币种
			BetCtx:       _betCtx,
			State:        1,
			DataState:    -1,
			BetCtxType:   3,
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("fb Bet 创建订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			resData.Code = 6
			resData.Message = "创建订单失败！"
			return e
		}
		//操作成功
		afterBalance := userBalance.Amount - betAmount
		//创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       0 - betAmount,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceFB_Bet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("fb Bet 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			resData.Code = 6
			resData.Message = "创建账变失败！"
			return e
		}
		resData.Code = 0
		ctx.RespJson(resData)
		logs.Info("[fb] Bet 下注成功 thirdId=", thirdId, " userId=", userId)
		return nil
	})
	//
	////推送下注事件到 CustomerIO
	if l.thirdGamePush != nil {
		l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, currency)
	}
	//没有发生错误则发送账变通知
	if err == nil {
		// 发送余额变动通知
		go func(notifyUserId int) {
			v := l
			if v == nil || v.RefreshUserAmountFunc == nil {
				return
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][fb] Debit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					return
				}
			}
		}(userId)
	}
}

// SyncOrders 订单数据推送
/**
 * 订单数据推送： 跟订单相关，推送跟普通订单相关的消息
 * cashOut数据推送：就是有提前结算订单接单，结算时调用的流水推送，是除了下单扣款之外的所有资金变化都会调用
 * 先推送订单数据，在推送流水数据
 * 更新订单状态变化 只修改订单状态和更新中将金额，账变在流水数据推送接口处理
 * settle_amount 是正常结算的派奖额，validSettleAmount 包含提前结算的有效派奖额。
 * settleAmount和cashoutPayout默认都是空，有改动才会变成对对应的值，如果订单已结算或者已取消，默认为空可处理成0
 */
func (l *FbService) SyncOrders(ctx *abugo.AbuHttpContent) {

	reqData := PushOrderData{}
	resData := fbResp{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("fb SyncOrders 订单数据推送 读取请求消息体错误 err=", err.Error())
		resData.Code = 1
		resData.Message = "读取请求消息体错误！"
		ctx.RespJson(resData)
		return
	}

	bodyBytes := string(reqDataByte)
	logs.Info("fb SyncOrders 订单数据推送 api receive:%s, ", bodyBytes)
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		resData.Code = 1
		resData.Message = "序列化参数错误错误！"
		ctx.RespJson(resData)
		logs.Error("fb SyncOrders 参数错误", err.Error())
		return
	}

	authToken := reqData.MerchantId
	if !l.checkToken(authToken) {
		logs.Error("fb 密钥不正确")
		resData.Code = 1
		resData.Message = "密钥不正确！"
		ctx.RespJson(resData)
		return
	}

	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	thirdTime := utils.GetCurrentTime()
	thirdId := reqData.Id //订单ID
	logs.Info("[fb] SyncOrders 开始处理订单，thirdId=", thirdId)

	//只有结算状态才需要派奖操作
	orderStatus := reqData.OrderStatus
	settleAmount, _ := strconv.ParseFloat(reqData.SettleAmount, 64)
	cashOutPayoutStake, _ := strconv.ParseFloat(reqData.CashOutPayoutStake, 64)
	rollBackCount := reqData.RollBackCount //回滚次数
	winAmount := 0.0                       //用户赢的钱
	seriesType := reqData.SeriesType       //0 单关、1 串关
	gameCode := ""
	gameName := ""

	if seriesType == 1 { //串关一笔订单对应多场赛事，拼接赛事体育类型
		uniqueSportTypeNames := make(map[string]struct{})
		var uniqueSportIds []string
		for _, detail := range reqData.BetList {
			var sportId_ = detail.SportId
			var sportId = strconv.Itoa(sportId_)
			if _, ok := uniqueSportTypeNames[sportId]; !ok {
				uniqueSportTypeNames[sportId] = struct{}{}
				uniqueSportIds = append(uniqueSportIds, sportId)
			}
		}
		var uniqueSportNames []string
		for _, detail := range uniqueSportIds {
			var sportName = l.games[detail]
			uniqueSportNames = append(uniqueSportNames, sportName)
		}
		// 去除重复值后的 TicketDetail.SportTypeName 值
		// 将 uniqueNames 切片重新转换为用逗号分隔的字符串
		uniqueSportTypeNamesString := strings.Join(uniqueSportNames, ",")
		uniqueSportIdsString := strings.Join(uniqueSportIds, ",")
		gameCode = uniqueSportIdsString //"串关"
		gameName = "串关:" + uniqueSportTypeNamesString
	} else { //单关 一笔订单对应一场赛事
		var sportId_ = reqData.BetList[0].SportId
		var sportId = strconv.Itoa(sportId_)
		gameCode = sportId          //运动id
		gameName = l.games[sportId] //运动名称
	}

	thirdRefId := thirdId + strconv.Itoa(reqData.Version) //判断流水号是否已被处理过，如果被处理过直接返回之前的响应数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, thirdRefId, ctx.Gin().Request.URL.String())
	if duplicate {
		logs.Error("fb SyncOrders 检测到重复请求 thirdId=", thirdId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			resData.Code = 1
			resData.Message = "重复请求，thirdRefId=" + thirdRefId
			ctx.RespJson(resData)
		}
		return
	}

	//记录请求号用于判断是否重复请求
	defer func() {
		base.AddRequestDB(thirdRefId, bodyBytes, resData, resData.Code, l.brandName, ctx.Gin().Request.URL.String())
	}()
	//游戏结果转中文
	gameBetZh := l.record2String(reqData)
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		order := thirdGameModel.ThirdSportOrder{}
		e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("fb SyncOrders 订单不存在 thirdId=", thirdId, " e=", e.Error())
			resData.Code = 1
			resData.Message = "订单不存在！"
			ctx.RespJson(resData)
			return e
		}
		//0:未确认 （是订单的初始状态，马上会被更新为其他状态）
		//1:确认中（待确认）
		//2:已拒单（投注失败）
		//3:已取消（取消）包含 提前结算取消返还、订单取消返还、提前结算取消回滚补扣、提前结算取消回滚返还、订单取消补扣
		//4:已接单（未结算-投注成功）
		//5:已结算（已结算-投注成功）
		switch orderStatus { //其它状态不需要派奖只用更新订单投注数据
		case 2, 3: //修改订单状态为撤单，包含两种情况 1：已下注的订单撤单 2：已派奖的订单撤单
			logs.Info("fb 订单撤单 thirdId=", thirdId, ",DataState=", order.DataState, "ResettleState=", order.ResettleState)
			if order.DataState == -2 {
				logs.Error("fb SyncOrders 订单状态不正确,订单已经被处理, thirdId=", thirdId, " state=", order.DataState)
				resData.Code = 1
				resData.Message = "订单已经被处理！"
				ctx.RespJson(resData)
				return errors.New("订单状态不正确")
			}

			//更新订单表
			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
				"WinAmount": 0,
				"BetAmount": 0,
				"ValidBet":  0,
				"DataState": -2,
				"GameId":    gameCode,
				"GameName":  gameName,
				"RawData":   reqDataByte,
				"ThirdTime": thirdTime,
				"BetCtx":    gameBetZh,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("saba SyncOrders 更新订单失败 thirdId=", thirdId, " e=", e.Error())
				resData.Code = 1
				resData.Message = "更新订单失败！"
				ctx.RespJson(resData)
				return e
			}
			logs.Info("fb 结束更新订单数据 thirdId=", thirdId, "更新订单状态state=", -2)

		case 5: //已结算需要派奖
			winAmount = settleAmount + cashOutPayoutStake //用户赢的钱
			logs.Info("fb 开始派奖 thirdId=", thirdId, ",DataState=", order.DataState, "ResettleState=", order.ResettleState)
			if order.DataState != -1 && order.DataState != 1 { //订单可以多次结算 -1：待结算 1：已结算 -2：撤单
				//返回成功，否则三方系统会一直调用
				logs.Error("fb SyncOrders 订单状态不正确 thirdId=", thirdId, " state=", order.DataState)
				resData.Code = 1
				resData.Message = "订单已经被处理！"
				ctx.RespJson(resData)
				return errors.New("订单状态不正确")
			}
			userId := order.UserId
			validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值

			//更新订单表
			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
				"WinAmount":      winAmount,
				"ValidBet":       validBet,
				"DataState":      1,
				"ThirdTime":      thirdTime,
				"GameRst":        reqDataByte,
				"BetCtx":         gameBetZh,
				"ResettleNumber": rollBackCount, //orderStatus=4会上报回滚次数 即：重新结算次数
				"ResettleTime":   thirdTime,
				"SettleTime":     utils.TimestampToLocal(reqData.SettleTime),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("fb SyncOrders 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				resData.Code = 1
				resData.Message = "更新订单失败！"
				ctx.RespJson(resData)
				return e
			}
			if order.DataState == -1 && order.ResettleState == 0 { //订单状态为0 并且未重新结算 说明正式订单表不存在数据 则创建订单
				order.WinAmount = winAmount
				order.ValidBet = validBet
				order.RawData = bodyBytes
				order.DataState = 1
				order.ThirdTime = thirdTime
				order.GameRst = bodyBytes
				order.BetCtx = gameBetZh
				order.BetCtxType = 3
				order.GameId = gameCode
				order.GameName = gameName
				order.BetTime = utils.TimestampToLocal(reqData.CreateTime)
				order.BetLocalTime = utils.TimestampToLocal(reqData.CreateTime)
				order.SettleTime = utils.TimestampToLocal(reqData.SettleTime)
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("fb SyncOrders 创建正式订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					resData.Code = 1
					resData.Message = "创建订单失败！"
					ctx.RespJson(resData)
					return e
				}
			} else {
				//更新统计表
				//问题：重新结算后用户提款了怎么办？是否修改账变？统计报表是实时结算即DataState = 1就会返佣
				resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"WinAmount":  winAmount,
					"ValidBet":   validBet,
					"ThirdTime":  thirdTime,
					"GameRst":    bodyBytes,
					"BetCtx":     gameBetZh,
					"SettleTime": utils.TimestampToLocal(reqData.SettleTime),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("fb Resettle 更新统计表失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					resData.Code = 901
					resData.Message = "创建订单失败！"
					ctx.RespJson(resData)
					return e
				}
			}

			// 推送派奖事件通知
			if l.thirdGamePush != nil {
				//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, order.BetAmount, winAmount, l.currency)
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				l.thirdGamePush.PushRewardEvent(6, l.brandName, thirdId)
			}

		default: //其它状态更新订单数据,无需更新订单状态
			logs.Info("fb 开始更新订单数据 thirdId=", thirdId, ", orderStatus=", orderStatus)
			//更新订单表
			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
				"WinAmount":      winAmount,
				"GameId":         gameCode,
				"GameName":       gameName,
				"RawData":        reqDataByte,
				"ThirdTime":      thirdTime,
				"ResettleNumber": rollBackCount, //orderStatus=4会上报回滚次数 即：重新结算次数
				"BetCtx":         gameBetZh,
				"BetTime":        utils.TimestampToLocal(reqData.CreateTime),
				"BetLocalTime":   utils.TimestampToLocal(reqData.CreateTime),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("saba SyncOrders 更新订单失败 thirdId=", thirdId, " e=", e.Error())
				resData.Code = 1
				resData.Message = "更新订单失败！"
				ctx.RespJson(resData)
				return e
			}
			logs.Info("fb 结束更新订单数据 thirdId=", thirdId, ", orderStatus=", orderStatus)
		}
		resData.Code = 0
		ctx.RespJson(resData)
		return nil
	})

	logs.Info("[fb] SyncOrders 订单数据处理成功,thirdId=", thirdId)
}

// CashOutOrders 提前结算订单数据推送
/**
 * 如果是多次，只是部分提前结算走/fb/callback/sync_cashout
 * 剩下的正常结算并派彩的话是走/fb/callback/sync_orders、/fb/callback/sync_transaction
 * 提前结算取  cashOutPayoutStake-stakeAmount 作为盈利
 * 又有提前结算又有正常的 盈利就是  cashOutPayoutStake+settleAmount-stakeAmount
 * 不是提前结算就取  settleAmount-stakeAmount  作为盈利
 * 提前结算只会单笔
 */
func (l *FbService) CashOutOrders(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Id                     string  `json:"id"`                     // 提前结算订单ID
		OrderId                string  `json:"orderId"`                // 订单ID
		UserId                 string  `json:"userId"`                 // 会员ID
		MerchantId             string  `json:"merchantId"`             // 渠道ID
		MerchantUserId         string  `json:"merchantUserId"`         // 渠道用户ID
		WalletType             int     `json:"walletType"`             // 钱包类型
		Currency               int     `json:"currency"`               // 币种ID
		ExchangeRate           float64 `json:"exchangeRate"`           // 汇率
		CashoutTime            string  `json:"cashoutTime"`            // 提前结算发起时间
		BetTime                string  `json:"betTime"`                // 订单下注时间
		SettleTime             string  `json:"settleTime"`             // 结算时间
		CreateTime             string  `json:"createTime"`             // 接单时间
		CancelTime             string  `json:"cancelTime"`             // 取消时间
		CashOutStake           float64 `json:"cashOutStake"`           // 提前结算本金
		OrderStatus            int     `json:"orderStatus"`            // 订单状态
		CashOutPayoutStake     float64 `json:"cashOutPayoutStake"`     // 提前结算派奖额
		AcceptOddsChange       bool    `json:"acceptOddsChange"`       // 是否接受赔率变动
		SeriesType             int     `json:"seriesType"`             // 串关类型
		BetType                string  `json:"betType"`                // 投注类型
		OrderStakeAmount       float64 `json:"orderStakeAmount"`       // 订单投注额
		Ip                     string  `json:"ip"`                     // 提前结算时的IP
		Remark                 string  `json:"remark"`                 // 备注
		CancelReasonCode       int     `json:"cancelReasonCode"`       // 取消提前结算原因枚举
		CancelCashOutAmountTo  float64 `json:"cancelCashOutAmountTo"`  // 取消提前结算订单时修正数额
		UnitCashOutPayoutStake float64 `json:"unitCashOutPayoutStake"` // cashOut单位询价派奖额
		Device                 string  `json:"device"`                 // 设备类型（PC、h5、mobile)
		Version                int     `json:"version"`                // 版本号
		LastModifyTime         string  `json:"lastModifyTime"`         // 最后更新时间
	}

	reqData := RequestData{}
	resData := fbResp{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("fb CashOutOrders 订单数据推送 读取请求消息体错误 err=", err.Error())
		resData.Code = 1
		resData.Message = "读取请求消息体错误！"
		ctx.RespJson(resData)
		return
	}

	bodyBytes := string(reqDataByte)
	logs.Info("fb CashOutOrders 提前结算数据推送 api receive:%s, ", bodyBytes)
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		logs.Error("fb CashOutOrders 参数错误", err.Error())
		resData.Code = 1
		resData.Message = "序列化参数错误错误！"
		ctx.RespJson(resData)
		return
	}

	authToken := reqData.MerchantId
	if !l.checkToken(authToken) {
		logs.Error("fb 密钥不正确")
		resData.Code = 1
		resData.Message = "密钥不正确！"
		ctx.RespJson(resData)
		return
	}

	tablePre := "x_third_sport_pre_order"
	table := "x_third_sport"
	thirdTime := utils.GetCurrentTime()
	thirdId := reqData.Id              //订单ID
	orderStatus := reqData.OrderStatus //订单状态
	cashOutPayoutStake := reqData.CashOutPayoutStake
	winAmount := 0.0
	switch orderStatus { //(0:创建 1:确认中 2:已拒绝 3:取消 4:已确认 5:已结算 )
	case 4:
	case 5: //提前结算订单状态只会是4或5
		winAmount = cashOutPayoutStake //用户赢的钱
	default:
		// 处理未知订单状态
		resData.Code = 1
		resData.Message = "订单状态不正确！"
		ctx.RespJson(resData)
		return
	}

	thirdRefId := thirdId + strconv.Itoa(reqData.Version) //三方反馈订单Id+版本号构成唯一请求号，判断流水号是否已被处理过，如果被处理过直接返回之前的响应数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, thirdRefId, ctx.Gin().Request.URL.String())
	if duplicate {
		logs.Error("fb CashOutOrders 检测到重复请求 thirdId=", thirdId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			resData.Code = 1
			resData.Message = "重复请求，thirdRefId=" + thirdRefId
			ctx.RespJson(resData)
		}
		return
	}
	//记录请求号用于判断是否重复请求
	defer func() {
		base.AddRequestDB(thirdRefId, bodyBytes, resData, resData.Code, l.brandName, ctx.Gin().Request.URL.String())
	}()
	logs.Info("[fb] CashOutOrders 开始处理提前结算订单，thirdId=", thirdId)
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		order := thirdGameModel.ThirdSportOrder{}
		e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("fb CashOutOrders 订单不存在 thirdId=", thirdId, " e=", e.Error())
			resData.Code = 1
			resData.Message = "订单不存在！"
			ctx.RespJson(resData)
			return e
		}

		logs.Info("fb 开始结算订单 thirdId=", thirdId)
		if order.DataState != -1 { //订单状态不正确
			logs.Error("fb CashOutOrders 订单状态不正确不是待结算 thirdId=", thirdId, " order=", order)
			resData.Code = 1
			resData.Message = "订单状态不正确！"
			ctx.RespJson(resData)
			return errors.New("订单状态不正确")
		}
		userId := order.UserId
		validBet := base.GetValidBet(order.BetAmount, winAmount) // 有效下注取输赢绝对值

		//更新订单表
		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":     winAmount,
			"ValidBet":      validBet,
			"DataState":     1,
			"ThirdTime":     thirdTime,
			"GameRst":       reqDataByte,
			"ResettleState": 0,
			"ResettleTime":  thirdTime,
			"BetTime":       utils.TimestampToLocal(reqData.CreateTime),
			"BetLocalTime":  utils.TimestampToLocal(reqData.CreateTime),
			"SettleTime":    utils.TimestampToLocal(reqData.SettleTime),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("fb CashOutOrders 更新订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			resData.Code = 1
			resData.Message = "更新订单失败！"
			ctx.RespJson(resData)
			return e
		}
		order.WinAmount = winAmount
		order.ValidBet = validBet
		order.RawData = bodyBytes
		order.DataState = 1
		order.ThirdTime = thirdTime
		order.GameRst = bodyBytes
		order.BetCtxType = 3
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("fb CashOutOrders 创建正式订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			resData.Code = 1
			resData.Message = "更新正式订单失败！"
			ctx.RespJson(resData)
			return e
		}
		logs.Info("fb 结束提前结算订单 thirdId=", thirdId)
		resData.Code = 0
		ctx.RespJson(resData)
		return nil
	})
}

// ConfirmBet 确认投注
/**
  只会出现单笔
  ConfirmBet只會有加錢，不會有扣錢的操作
*/
func (l *FbService) ConfirmBet(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		TransactionId   string  `json:"transactionId" validate:"required"`   // 交易流水ID，全服唯一
		UserId          string  `json:"userId" validate:"required"`          // FB用户ID
		MerchantId      string  `json:"merchantId" validate:"required"`      // 渠道ID
		MerchantUserId  string  `json:"merchantUserId" validate:"required"`  // 渠道用户ID
		BusinessId      string  `json:"businessId" validate:"required"`      // 业务ID，即订单ID
		TransactionType string  `json:"transactionType" validate:"required"` // 交易类型 OUT 转出，IN 转入
		TransferType    string  `json:"transferType" validate:"required"`    // 转账类型
		CurrencyId      int     `json:"currencyId" validate:"required"`      // 币种ID
		Amount          float64 `json:"amount" validate:"required"`          // 流水金额
		Status          int     `json:"status" validate:"required"`          // 转账状态, 0 取消，1成功
		RelatedId       string  `json:"relatedId,omitempty"`                 // 三方数据关联ID，可为空，下单时三方带的订单ID
		ThirdRemark     string  `json:"thirdRemark,omitempty"`               // 三方商户下单使用的备注，可为空，下单时三方带的备注
	}

	reqData := RequestData{}
	resData := fbResp{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Debug("fb configBet 查看渠道支付状态 api receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqData)
	if err != nil {
		resData.Code = 1
		resData.Message = "序列化参数错误错误！"
		ctx.RespJson(resData)
		logs.Error("fb configBet 参数错误", err.Error())
		return
	}

	authToken := reqData.MerchantId
	if !l.checkToken(authToken) {
		resData.Code = 1
		resData.Message = "密钥不正确！"
		ctx.RespJson(resData)
		logs.Error("fb 密钥不正确")
		return
	}

	thirdId := reqData.BusinessId
	//查询订单是否存在
	order := base.GetOrder(thirdId, l.brandName, "x_third_sport_pre_order")
	if order == nil {
		resData.Code = 1
		resData.Message = "订单不存在！"
		ctx.RespJson(resData)
		logs.Error("fb configBet 订单不存在 thirdId=", thirdId, " userId=", reqData.UserId)
		return
	}
	resData.Code = 0
	ctx.RespJson(resData)
	logs.Info("[fb] configBet 确认订单成功,thirdId=", thirdId, " userId=", reqData.UserId)
}

type SyncTransactionRequest struct {
	TransactionId   string  `json:"transactionId"`   // 交易流水ID，全服唯一
	UserId          string  `json:"userId"`          // FB用户ID
	MerchantId      string  `json:"merchantId"`      // 渠道ID
	MerchantUserId  string  `json:"merchantUserId"`  // 渠道用户ID
	BusinessId      string  `json:"businessId"`      // 业务ID，即订单ID
	TransactionType string  `json:"transactionType"` // 交易类型 OUT 转出，IN 转入
	TransferType    string  `json:"transferType"`    // 转账类型
	CurrencyId      int     `json:"currencyId"`      // 币种ID
	Amount          float64 `json:"amount"`          // 流水金额
	Status          int     `json:"status"`          // 转账状态, 0 取消，1成功
	RelatedId       string  `json:"relatedId"`       // 三方数据关联ID，可为空，下单时三方带的订单ID
	ThirdRemark     string  `json:"thirdRemark"`     // 三方商户下单使用的备注，可为空，下单时三方带的备注
}

type FbTransaction struct {
	TransactionId string `json:"transactionId"`
}

// SyncTransaction  流水数据推送
/**
 * 订单推送：只要有订单发生变化，比如接单成功，结算，取消，回滚，选项变化等，都会调用
 * 流水推送：是除了下单扣款之外的所有资金变化都会调用。
 * 订单取消补扣：注单有派彩，然后注单因赛事取消，而需要扣回之前的派彩
 * 订单取消返还：注单结算为输，然后注单因赛事取消，之前输的金额需要返回给用户
 * 流水数据推送,返回推送状态,当全部流水处理失败时返回code=1，其他情况返回code=0
 * status=1，就是流水里的数据让加多少贵司就加多少，让减多少就减多少；
 * status=0，就判断是否存在流水ID一样，并且贵司处理成功的流水，如果有，
 *  就取消这笔流水，至于怎么去取消，贵司自己业务决定，意思就是回退这笔流水。如果贵司没有处理过相同流水Id的数据，就忽略
 */
/**
CANCEL_DEDUCT 订单结算后赢了钱，取消结算订单，补扣差价 OUT
CANCEL_RETURN 订单结算后输了钱，取消结算订单，返回差价   IN
SETTLEMENT_ROLLBACK_DEDUCT是订单结算赢了后，订单回滚成未结算时，扣除差价  OUT
CASHOUT_CANCEL_DEDUCT 订单提前结算后赢了钱，取消提前结算订单，补扣差价   OUT
CASHOUT_CANCEL_RETURN 订单提前结算后输了钱，取消提前结算订单，返回差价  IN
CASHOUT_CANCEL_ROLLBACK_DEDUCT   提前结算取消回滚补扣  就是订单取消回滚对应要扣的钱  OUT
CASHOUT_CANCEL_ROLLBACK_RETURN    提前结算取消回滚返还   订单取消返还, 就是订单回滚取消对应要加的钱   IN
Deduct 就是扣款，Return 就是加款
*/
func (l *FbService) SyncTransaction(ctx *abugo.AbuHttpContent) {
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("fb syncTransaction api receive:%s, ", string(reqDataByte))
	var trxResults []SyncTransactionRequest
	type ResponseData struct { //响应结构体
		Message string          `json:"message"` // 描述信息
		Code    int             `json:"code"`    // 返回码
		Data    []FbTransaction `json:"data"`
	}
	responseData := ResponseData{}

	err = json.Unmarshal(reqDataByte, &trxResults)
	if err != nil {
		responseData.Code = 203
		responseData.Message = "序列化参数错误错误！"
		ctx.RespJson(responseData)
		logs.Error("fb cancelBet 参数错误", err.Error())
		return
	}

	var failures []string     //失败注单
	var success []string      //成功注单
	var retry []FbTransaction //需要重试的注单 数据库操作失败的需要三方重试
	tablePre := "x_third_sport_pre_order"
	logs.Info("[fb] syncTransaction 开始处理注单，注单总数：", len(trxResults))
	//流水数据延时1.5秒执行 收到流水数据应该晚于订单推送数据时间 否则订单状态已经提前修改，导致流水处理判断订单状态 不正确 报错
	time.After(1500 * time.Millisecond)
	for _, trxResult := range trxResults { //批量订单处理
		//transactionType: out 是客户需要扣款，IN为客户须加钱,注意 当transferType=BET，是支付失败，不是拒单
		//担心因为异常原因导致的扣款失败（实际上已经扣款成功，但是我们拿到的结果是失败）。贵司收到这个流水，
		//需要判断这个流水id是否扣款成功，如果扣款成功，则自行进行退款处理
		transferType := trxResult.TransferType
		status := trxResult.Status //转账状态,0 取消，1成功；当需要取消对应的下单支付交易时，此字段为0，其他都是1
		transactionType := trxResult.TransactionType
		thirdTime := utils.GetCurrentTime()
		thirdId := trxResult.BusinessId //订单Id
		amount := trxResult.Amount      //三方返回的的Amount有时是负数 有时是正数
		//status=1，就是流水里的数据让加多少贵司就加多少，让减多少就减多少；
		//status=0，就判断是否存在流水ID一样，并且贵司处理成功的流水，需要回退。
		if status == 0 { //三方反馈根据0和非0状态来判断需要执行的操作，0=撤单操作需要加款
			logs.Info("撤单操作，thirdId=", thirdId)
			amount = math.Abs(trxResult.Amount) //三方返回的的Amount是负数，需要取绝对值变正数，以加款
		} else { //status非0根据transactionType类型来判断加款还是扣款
			if transactionType == "OUT" { //需要扣款
				amount = math.Abs(trxResult.Amount)
				amount = -amount //负数扣款 正数加款
			} else if transactionType == "IN" { //需要加款
				amount = math.Abs(trxResult.Amount)
			}
		}

		if amount == 0 {
			logs.Error("订单处理失败，订单金额不能为0，thirdId=", thirdId)
			failures = append(failures, thirdId)
			continue
		}

		//1.不会出现第一次win第二次cashout
		//2.会出现第一次cashout第二次win，参考部分提前结算功能
		//3.第一次cashout之后，注单状态不是已结算
		var isSettle bool = false // 是否重新结算 重新结算注单需要记录结算记录
		var settleMemo = "重新结算"   // 记录重新结算的备注
		var oldState []int        // 注单原来的状态，改为数组
		var newState int          // 当前状态
		var tranType int          // 账变类型

		switch transferType {
		case "BET":
			oldState = append(oldState, -1) // 押注
			newState = -2                   // 撤单
			tranType = utils.BalanceFB_Refund
		case "WIN":
			oldState = append(oldState, -1, 1) // 派彩 会出现部分派彩部分 提前结算的情况 原始订单状态是-1或者1
			newState = 1
			tranType = utils.BalanceFB_Win
		case "REFUND":
			// 退款 订单撤单 原始订单状态。加上-2原因：会出现先收到【订单数据推送】后收到【流水数据】此时订单状态是【-2】，会导致退款时判断订单不是【-1】 拒退款
			oldState = append(oldState, -1, -2)
			newState = -2 // 撤单
			tranType = utils.BalanceFB_Refund
		case "CASHOUT":
			oldState = append(oldState, -1) // 提前结算
			newState = 1
			tranType = utils.BalanceFB_Cashout
		case "CANCEL_DEDUCT":
			oldState = append(oldState, 1) // 订单取消补扣 只有派彩后才会发生订单取消补扣
			newState = 1
			tranType = utils.BalanceFB_CancelDeduct
			settleMemo = "订单取消补扣"
		case "CANCEL_RETURN":
			oldState = append(oldState, -1, 1) //注单结算为输，然后注单因赛事取消，之前输的金额需要返回给用户 原始订单状态是-1或者1
			newState = -2
			tranType = utils.BalanceFB_CancelReturn
			settleMemo = "订单取消返还"
		case "SETTLEMENT_ROLLBACK_DEDUCT":
			oldState = append(oldState, 1) // 结算回滚补扣
			newState = 1
			tranType = utils.BalanceFB_SettlementRollbackDeduct
			settleMemo = "结算回滚补扣"
		case "CASHOUT_CANCEL_DEDUCT":
			oldState = append(oldState, 1) // 提前结算订单取消补扣
			newState = 1
			tranType = utils.BalanceFB_CashoutCancelDeduct
			settleMemo = "提前结算订单取消补扣"
		case "CASHOUT_CANCEL_RETURN":
			oldState = append(oldState, 1) // 提前结算订单取消返还
			newState = 1
			tranType = utils.BalanceFB_CashoutCancelReturn
			settleMemo = "提前结算订单取消返还"
		case "CASHOUT_CANCEL_ROLLBACK_DEDUCT":
			oldState = append(oldState, 1) // 提前结算取消回滚补扣
			newState = 1
			tranType = utils.BalanceFB_CashoutCancelRollbackDeduct
			settleMemo = "提前结算取消回滚补扣"
		case "CASHOUT_CANCEL_ROLLBACK_RETURN":
			oldState = append(oldState, 1) // 提前结算取消回滚返还
			newState = 1
			tranType = utils.BalanceFB_CashoutCancelRollbackReturn
			settleMemo = "提前结算取消回滚返还"
		default:
			// 处理未知的转账类型
			if err != nil {
				logs.Error("处理订单发生错误,未知的转账类型，thirdId=", thirdId, "transferType=", transferType)
			}
			continue
		}
		//保存订单详情
		_resultCtx, _ := json.Marshal(trxResult)
		resultCtx := string(_resultCtx)
		thirdRefId := trxResult.TransactionId //保留三方唯一请求号用来判断是否是重复请求，如果是重复请求则返回上次响应
		duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, thirdRefId, ctx.Gin().Request.URL.String())
		if err != nil {
			continue
		}
		if duplicate { //判断流水号是否已被处理过，如果被处理过直接返回之前的响应数据
			logs.Error("fb sync_transaction 检测到重复请求 thirdRefId=", thirdRefId, "duplicateResult=", duplicateResult, "err=", err)
			if len(trxResults) == 1 { //如果只有一条订单流水直接返回 之前的响应数据
				if duplicateResult != nil && err == nil {
					ctx.RespJson(duplicateResult)
				} else {
					responseData.Message = "重复请求，thirdRefId=" + thirdRefId
					ctx.RespJson(responseData)
				}
				return
			} else { //如果是多条流水数据 中断本次处理后 继续执行
				continue
			}
		}

		//开启事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			//查询订单是否存在 订单不存在或者订单已完成 直接返回成功信息给三方 避免三方重复调用接口
			order := thirdGameModel.ThirdSportOrder{}
			e := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
			if e != nil {
				logs.Error("fb sync_transaction 订单不存在 thirdId=", thirdId, " e=", e.Error())
				retry = append(retry, FbTransaction{TransactionId: thirdId})
				return e
			}
			if order.DataState == 1 { // 二次结算 二次结算流程：结算—未结算—结算
				isSettle = true //订单是否结算过
			}
			if !containsInt(oldState, order.DataState) {
				logs.Error("fb sync_transaction 订单已处理或订单状态不正确 thirdId=", thirdId, " state=", order.DataState, " newState=", newState)
				failures = append(failures, thirdId)
				// 订单已经处理或订单状态不正确跳过
				return e
			}

			userId := order.UserId
			logs.Info("fb sync_transaction  开始处理流水 userId=", userId, " thirdId=", thirdId, " transferType=", transferType, " orderState=", order.DataState, " amount=", amount)
			userBalance := thirdGameModel.UserBalance{} //锁定用户余额
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("fb sync_transaction  查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				retry = append(retry, FbTransaction{TransactionId: thirdId})
				return e
			}
			if amount != 0 { //是否需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount+?", amount), //正数加款 负数减款
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 { //amount=0 则 RowsAffected<=0
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("fb sync_transaction 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					retry = append(retry, FbTransaction{TransactionId: thirdId})
					return e
				}
			}
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       amount,
				AfterAmount:  userBalance.Amount + amount,
				Reason:       tranType,
				Memo:         l.brandName + " " + transferType + ",thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("fb sync_transaction 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				retry = append(retry, FbTransaction{TransactionId: thirdId})
				return e
			}

			if isSettle {
				//重新结算增加记录日志
				logs.Info("fb sync_transaction 重新结算处理 userId=", userId, " thirdId=", thirdId, " state=", order.DataState)
				state := 0      //状态 0-重新结算1-撤销结算
				changeType := 0 // 0增加 1减少
				if amount <= 0 {
					changeType = 1
				}
				//修改订单状态
				resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
					"ResettleState":  1, //0：不是重新结算 1：是重新结算
					"ResettleNumber": daogorm.Expr("ResettleNumber+?", 1),
				})
				e = resultTmp.Error
				if e != nil {
					logs.Error("fb sync_transaction 更新订单状态失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
					retry = append(retry, FbTransaction{TransactionId: thirdId})
					return e
				}

				e = base.AddSportResettleLog(tx, order, thirdTime, resultCtx, amount, changeType, state, thirdId, thirdTime, "", settleMemo)
			}

			success = append(success, thirdId)
			// 发送余额变动通知
			go func(notifyUserId int) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(notifyUserId)
					if tmpErr != nil {
						logs.Error("fb sync_transaction 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("fb sync_transaction 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(userId)

			logs.Info("[fb] sync_transaction 处理流水数据成功 thirdId=", thirdId)
			//记录请求号用于判断是否重复请求
			base.AddRequestDB(thirdRefId, resultCtx, responseData, responseData.Code, l.brandName, ctx.Gin().Request.URL.String())
			return nil
		})
	}
	logs.Info("[fb] sync_transaction 结束处理流水数据，注单总数：", len(trxResults), "操作成功的注单数:", len(success), success, "操作失败的注单:", failures, "需要重试的注单:", retry)

	/*
	 批量取消时，失败的订单需要记录通知三方重试
	*/
	if len(retry) > 0 { //将失败单号返回给三方
		logs.Info("[fb] sync_transaction 存在失败的注单流水数据，失败的thirdId=", retry)
	} else {
		logs.Info("[fb] sync_transaction 流水数据全部处理成功")
	}
	responseData.Code = 0
	responseData.Data = retry
	ctx.RespJson(responseData)
}

// 判断状态是否存在
func containsInt(arr []int, target int) bool {
	for _, a := range arr {
		if a == target {
			return true
		}
	}
	return false
}

// HealthCheck 心跳检测/*
func (l *FbService) HealthCheck(ctx *abugo.AbuHttpContent) {

	reqDataByte, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Debug("fb healthCheck api receive:%s, ", string(reqDataByte))
	resData := fbResp{}
	resData.Code = 0
	ctx.RespJson(resData)
	logs.Info("[fb] healthCheck 心跳检测成功")
}

func (l *FbService) getBetCtxFromRowData(_rawData string) (_betCtx string) {
	// 拼接下注内容 开始
	_betCtx = _rawData
	// sport 游戏项目
	_betCtx = strings.ReplaceAll(_betCtx, "\"sport\"", "\"sport游戏项目\"")
	return
}

//func (l *FbService) formatStrTimestamp(timestampStr string) string {
//	if timestampStr == "" {
//		return ""
//	}
//	// 将字符串转换为int64
//	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
//	if err != nil {
//		return ""
//	}
//	// 将毫秒时间戳转换为UTC时间
//	t := time.UnixMilli(timestamp).UTC()
//	// 格式化为字符串
//	return t.Format("2006-01-02 15:04:05")
//}

// 验证token
func (l *FbService) checkToken(authToken string) bool {
	//注意：FB体育三方未传密钥裸掉接口，待以后FB接口传了密钥在完善代码，暂时不验证密钥
	// 去除两个字符串中的空格后再比较
	authTokenTrim := strings.TrimSpace(authToken)
	merchantIdTrim := strings.TrimSpace(l.merchantId)

	if authTokenTrim == merchantIdTrim {
		return true
	}
	return false
}

func (s *FbService) record2String(data PushOrderData) string {
	// 获取订单状态描述
	getOrderStatus := func(status int) string {
		switch status {
		case 0:
			return "未确认"
		case 1:
			return "确认中"
		case 2:
			return "已拒单"
		case 3:
			return "已取消"
		case 4:
			return "已接单"
		case 5:
			return "已结算"
		default:
			return "未知状态"
		}
	}

	// 获取赛事类型描述
	getMatchType := func(matchType int) string {
		switch matchType {
		case 1:
			return "冠军"
		case 2:
			return "常规体育"
		case 3:
			return "电竞"
		case 4:
			return "虚拟体育"
		default:
			return "未知类型"
		}
	}

	// 获取结算结果描述
	getSettleResult := func(result int) string {
		switch result {
		case 0:
			return "无结果"
		case 2:
			return "和"
		case 3:
			return "输"
		case 4:
			return "赢"
		case 5:
			return "赢半"
		case 6:
			return "输半"
		case 7:
			return "取消"
		default:
			return "未知结果"
		}
	}

	var sb strings.Builder
	sb.WriteString("{")

	// 基本信息
	sb.WriteString(fmt.Sprintf("\"订单号\":\"%v\",", data.Id))
	sb.WriteString(fmt.Sprintf("\"用户ID\":\"%v\",", data.MerchantUserId))
	sb.WriteString(fmt.Sprintf("\"订单状态\":\"%v\",", getOrderStatus(data.OrderStatus)))
	sb.WriteString(fmt.Sprintf("\"串关类型\":%v,", data.SeriesType))
	sb.WriteString(fmt.Sprintf("\"投注类型\":\"%v\",", data.BetType))
	sb.WriteString(fmt.Sprintf("\"串关数\":%v,", data.AllUp))
	sb.WriteString(fmt.Sprintf("\"投注金额\":\"%v\",", data.StakeAmount))
	sb.WriteString(fmt.Sprintf("\"实际投注金额\":\"%v\",", data.LiabilityStake))
	sb.WriteString(fmt.Sprintf("\"回滚次数\":%v,", data.RollBackCount))
	sb.WriteString(fmt.Sprintf("\"结算金额\":\"%v\",", data.SettleAmount))
	sb.WriteString(fmt.Sprintf("\"下注时间\":\"%v\"", utils.TimestampToLocal(data.CreateTime)))

	if data.SettleTime != "" {
		sb.WriteString(fmt.Sprintf(",\"结算时间\":\"%v\"", utils.TimestampToLocal(data.SettleTime)))
	}

	if data.OrderStatus == 2 && data.RejectReasonStr != "" {
		sb.WriteString(fmt.Sprintf(",\"拒单原因\":\"%v\"", data.RejectReasonStr))
	}

	// 投注列表
	if len(data.BetList) > 0 {
		sb.WriteString(",\"投注列表\":[")
		for i, bet := range data.BetList {
			if i > 0 {
				sb.WriteString(",")
			}
			sb.WriteString("{")
			sb.WriteString(fmt.Sprintf("\"比赛\":\"%v\",", bet.MatchName))
			sb.WriteString(fmt.Sprintf("\"类型\":\"%v\",", getMatchType(bet.MatchType)))
			sb.WriteString(fmt.Sprintf("\"玩法\":\"%v\",", bet.MarketName))
			sb.WriteString(fmt.Sprintf("\"选项\":\"%v\",", bet.OptionName))

			if bet.Handicap != "" {
				sb.WriteString(fmt.Sprintf("\"盘口\":\"%v\",", bet.Handicap))
			}

			sb.WriteString(fmt.Sprintf("\"赔率\":\"%v\",", bet.Odds))
			sb.WriteString(fmt.Sprintf("\"投注额\":\"%v\"", bet.BetAmount))

			if bet.BetScore != "" {
				sb.WriteString(fmt.Sprintf(",\"比分\":\"%v\"", bet.BetScore))
			}

			if bet.SettleTime != "" {
				sb.WriteString(fmt.Sprintf(",\"结果\":\"%v\"", getSettleResult(bet.SettleResult)))
				sb.WriteString(fmt.Sprintf(",\"结算额\":\"%v\"", bet.SettleAmount))
			}

			if bet.CancelReason != "" {
				sb.WriteString(fmt.Sprintf(",\"取消原因\":\"%v\"", bet.CancelReason))
			}

			sb.WriteString("}")
		}
		sb.WriteString("]")
	}

	// 提前结算信息
	if data.CashOutCount > 0 {
		sb.WriteString(",\"提前结算\":{")
		sb.WriteString(fmt.Sprintf("\"次数\":%d,", data.CashOutCount))
		sb.WriteString(fmt.Sprintf("\"总本金\":\"%s\",", data.CashOutTotalStake))
		sb.WriteString(fmt.Sprintf("\"名义总本金\":\"%s\",", data.LiabilityCashoutStake))
		sb.WriteString(fmt.Sprintf("\"总派奖额\":\"%s\"", data.CashOutPayoutStake))
		sb.WriteString("}")
	}

	sb.WriteString("}")
	return sb.String()
}

type PushOrderData struct {
	Id                    string `json:"id"`                    // 订单号
	RejectReason          int    `json:"rejectReason"`          // 拒单原因码
	RejectReasonStr       string `json:"rejectReasonStr"`       // 拒单原因
	UserId                string `json:"userId"`                // FB平台用户ID
	MerchantId            string `json:"merchantId"`            // 渠道ID
	MerchantUserId        string `json:"merchantUserId"`        // 渠道用户ID
	Currency              int    `json:"currency"`              // 币种
	ExchangeRate          string `json:"exchangeRate"`          // 汇率快照
	SeriesType            int    `json:"seriesType"`            // 关次类型，单关或者串关
	BetType               string `json:"betType"`               // 投注类型
	AllUp                 int    `json:"allUp"`                 // 总关数
	AllUpAlive            int    `json:"allUpAlive"`            // 存活关数
	StakeAmount           string `json:"stakeAmount"`           // 投注额(本金)
	LiabilityStake        string `json:"liabilityStake"`        // 名义投注额(名义本金)
	SettleAmount          string `json:"settleAmount"`          // 结算派奖金额
	OrderStatus           int    `json:"orderStatus"`           // 订单状态(0:创建 1:确认中 2:已拒绝 3:取消 4:已确认 5:已结算 ) , see enum: order_status
	PayStatus             int    `json:"payStatus"`             // 付款状态(弃用)
	OddsChange            int    `json:"oddsChange"`            // 是否接受赔率变更
	Device                string `json:"device"`                // 设备类型
	Ip                    string `json:"ip"`                    // 投注IP地址
	SettleTime            string `json:"settleTime"`            // 订单结算时间
	CreateTime            string `json:"createTime"`            // 订单创建时间
	ModifyTime            string `json:"modifyTime"`            // 订单确认时间
	CancelTime            string `json:"cancelTime"`            // 订单取消时间
	ThirdRemark           string `json:"thirdRemark"`           // 第三方备注
	RelatedId             string `json:"relatedId"`             // 三方关联ID
	MaxWinAmount          string `json:"maxWinAmount"`          // 最大可赢金额
	LoseAmount            string `json:"loseAmount"`            // 最大赔付金额
	RollBackCount         int    `json:"rollBackCount"`         // 回滚次数
	ItemCount             int    `json:"itemCount"`             // 选项数
	SeriesValue           int    `json:"seriesValue"`           // 串几关
	BetNum                int    `json:"betNum"`                // 子单数
	CashOutTotalStake     string `json:"cashOutTotalStake"`     // 提前结算总本金
	LiabilityCashoutStake string `json:"liabilityCashoutStake"` // 提前结算名义总本金
	CashOutPayoutStake    string `json:"cashOutPayoutStake"`    // 提前结算总派奖额
	ReserveId             string `json:"reserveId"`             // 预约订单单号
	CashOutCount          int    `json:"cashOutCount"`          // 提前结算次数
	UnitStake             string `json:"unitStake"`             // 每单金额(混合串关时用到)
	MaxStake              string `json:"maxStake"`              // 最大投注额
	ReserveVersion        int    `json:"reserveVersion"`        // 预约订单版本号
	BetList               []struct {
		Id              string `json:"id"`         // 注单ID
		OrderId         string `json:"orderId"`    // 订单ID
		SportId         int    `json:"sportId"`    // 运动ID
		MatchId         string `json:"matchId"`    // 比赛ID
		MatchName       string `json:"matchName"`  // 比赛名称
		Period          int    `json:"period"`     // 阶段ID
		MarketId        string `json:"marketId"`   // 玩法ID
		MarketType      int    `json:"marketType"` // 玩法类型
		OptionType      int    `json:"option"`
		OptionId        string `json:"optionId"`        // 选项ID
		OptionName      string `json:"optionName"`      // 选项名称
		Handicap        string `json:"handicap"`        // 盘口
		Odds            string `json:"odds"`            // 赔率
		BetAmount       string `json:"betAmount"`       // 投注额
		SettleAmount    string `json:"settleAmount"`    // 结算金额
		WinAmount       string `json:"winAmount"`       // 赢取金额
		Status          int    `json:"status"`          // 注单状态
		Source          int    `json:"source"`          // 下单来源
		OrderCreateTime string `json:"orderCreateTime"` // 注单创建时间
		SettleTime      string `json:"settleTime"`      // 注单结算时间
		RollBackCount   int    `json:"rollBackCount"`   // 回滚次数
		IsRolling       int    `json:"isRolling"`       // 是否滚盘
		MarketName      string `json:"marketName"`      //玩法名称
		SettleStatus    int    `json:"settleStatus"`    //赛事类型, see enum: match_type
		SettleResult    int    `json:"settleResult"`    //结算结果 , see enum: outcome
		BetScore        string `json:"betScore"`        //下注当时比分
		MatchType       int    `json:"matchType"`       //赛事类型, see enum: match_type
		CancelReason    string `json:"cancelReason"`    //取消原因 , see enum: order_cancel_reason

	} `json:"betList"`
	Version int `json:"version"` // 版本号
}
