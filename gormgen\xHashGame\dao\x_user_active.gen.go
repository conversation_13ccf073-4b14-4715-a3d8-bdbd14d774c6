// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserActive(db *gorm.DB, opts ...gen.DOOption) xUserActive {
	_xUserActive := xUserActive{}

	_xUserActive.xUserActiveDo.UseDB(db, opts...)
	_xUserActive.xUserActiveDo.UseModel(&model.XUserActive{})

	tableName := _xUserActive.xUserActiveDo.TableName()
	_xUserActive.ALL = field.NewAsterisk(tableName)
	_xUserActive.UserID = field.NewInt32(tableName, "UserId")
	_xUserActive.ActiveDefineID = field.NewInt32(tableName, "ActiveDefineId")
	_xUserActive.JoinTime = field.NewTime(tableName, "JoinTime")
	_xUserActive.CompletionData = field.NewString(tableName, "CompletionData")

	_xUserActive.fillFieldMap()

	return _xUserActive
}

// xUserActive 玩家活动数据
type xUserActive struct {
	xUserActiveDo xUserActiveDo

	ALL            field.Asterisk
	UserID         field.Int32  // 玩家ID
	ActiveDefineID field.Int32  // x_active_define.Id
	JoinTime       field.Time   // 玩家主动参加活动的时间
	CompletionData field.String // 活动达成数据

	fieldMap map[string]field.Expr
}

func (x xUserActive) Table(newTableName string) *xUserActive {
	x.xUserActiveDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserActive) As(alias string) *xUserActive {
	x.xUserActiveDo.DO = *(x.xUserActiveDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserActive) updateTableName(table string) *xUserActive {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.ActiveDefineID = field.NewInt32(table, "ActiveDefineId")
	x.JoinTime = field.NewTime(table, "JoinTime")
	x.CompletionData = field.NewString(table, "CompletionData")

	x.fillFieldMap()

	return x
}

func (x *xUserActive) WithContext(ctx context.Context) *xUserActiveDo {
	return x.xUserActiveDo.WithContext(ctx)
}

func (x xUserActive) TableName() string { return x.xUserActiveDo.TableName() }

func (x xUserActive) Alias() string { return x.xUserActiveDo.Alias() }

func (x xUserActive) Columns(cols ...field.Expr) gen.Columns { return x.xUserActiveDo.Columns(cols...) }

func (x *xUserActive) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserActive) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 4)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ActiveDefineId"] = x.ActiveDefineID
	x.fieldMap["JoinTime"] = x.JoinTime
	x.fieldMap["CompletionData"] = x.CompletionData
}

func (x xUserActive) clone(db *gorm.DB) xUserActive {
	x.xUserActiveDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserActive) replaceDB(db *gorm.DB) xUserActive {
	x.xUserActiveDo.ReplaceDB(db)
	return x
}

type xUserActiveDo struct{ gen.DO }

func (x xUserActiveDo) Debug() *xUserActiveDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserActiveDo) WithContext(ctx context.Context) *xUserActiveDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserActiveDo) ReadDB() *xUserActiveDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserActiveDo) WriteDB() *xUserActiveDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserActiveDo) Session(config *gorm.Session) *xUserActiveDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserActiveDo) Clauses(conds ...clause.Expression) *xUserActiveDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserActiveDo) Returning(value interface{}, columns ...string) *xUserActiveDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserActiveDo) Not(conds ...gen.Condition) *xUserActiveDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserActiveDo) Or(conds ...gen.Condition) *xUserActiveDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserActiveDo) Select(conds ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserActiveDo) Where(conds ...gen.Condition) *xUserActiveDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserActiveDo) Order(conds ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserActiveDo) Distinct(cols ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserActiveDo) Omit(cols ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserActiveDo) Join(table schema.Tabler, on ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserActiveDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserActiveDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserActiveDo) Group(cols ...field.Expr) *xUserActiveDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserActiveDo) Having(conds ...gen.Condition) *xUserActiveDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserActiveDo) Limit(limit int) *xUserActiveDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserActiveDo) Offset(offset int) *xUserActiveDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserActiveDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserActiveDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserActiveDo) Unscoped() *xUserActiveDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserActiveDo) Create(values ...*model.XUserActive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserActiveDo) CreateInBatches(values []*model.XUserActive, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserActiveDo) Save(values ...*model.XUserActive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserActiveDo) First() (*model.XUserActive, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserActive), nil
	}
}

func (x xUserActiveDo) Take() (*model.XUserActive, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserActive), nil
	}
}

func (x xUserActiveDo) Last() (*model.XUserActive, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserActive), nil
	}
}

func (x xUserActiveDo) Find() ([]*model.XUserActive, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserActive), err
}

func (x xUserActiveDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserActive, err error) {
	buf := make([]*model.XUserActive, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserActiveDo) FindInBatches(result *[]*model.XUserActive, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserActiveDo) Attrs(attrs ...field.AssignExpr) *xUserActiveDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserActiveDo) Assign(attrs ...field.AssignExpr) *xUserActiveDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserActiveDo) Joins(fields ...field.RelationField) *xUserActiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserActiveDo) Preload(fields ...field.RelationField) *xUserActiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserActiveDo) FirstOrInit() (*model.XUserActive, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserActive), nil
	}
}

func (x xUserActiveDo) FirstOrCreate() (*model.XUserActive, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserActive), nil
	}
}

func (x xUserActiveDo) FindByPage(offset int, limit int) (result []*model.XUserActive, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserActiveDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserActiveDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserActiveDo) Delete(models ...*model.XUserActive) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserActiveDo) withDO(do gen.Dao) *xUserActiveDo {
	x.DO = *do.(*gen.DO)
	return x
}
