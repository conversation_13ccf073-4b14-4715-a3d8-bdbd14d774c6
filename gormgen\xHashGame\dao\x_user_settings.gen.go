// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserSetting(db *gorm.DB, opts ...gen.DOOption) xUserSetting {
	_xUserSetting := xUserSetting{}

	_xUserSetting.xUserSettingDo.UseDB(db, opts...)
	_xUserSetting.xUserSettingDo.UseModel(&model.XUserSetting{})

	tableName := _xUserSetting.xUserSettingDo.TableName()
	_xUserSetting.ALL = field.NewAsterisk(tableName)
	_xUserSetting.UserID = field.NewInt32(tableName, "UserId")
	_xUserSetting.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserSetting.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserSetting.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xUserSetting.AgentID = field.NewInt32(tableName, "AgentId")
	_xUserSetting.ChipList = field.NewString(tableName, "ChipList")
	_xUserSetting.Memo = field.NewString(tableName, "Memo")
	_xUserSetting.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserSetting.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserSetting.fillFieldMap()

	return _xUserSetting
}

// xUserSetting 用户设置
type xUserSetting struct {
	xUserSettingDo xUserSettingDo

	ALL        field.Asterisk
	UserID     field.Int32  // 用户id
	SellerID   field.Int32  // 运营商id
	ChannelID  field.Int32  // 渠道id
	TopAgentID field.Int32  // 顶级代理
	AgentID    field.Int32  // 代理id
	ChipList   field.String // 筹码
	Memo       field.String
	CreateTime field.Time // 创建时间
	UpdateTime field.Time // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserSetting) Table(newTableName string) *xUserSetting {
	x.xUserSettingDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserSetting) As(alias string) *xUserSetting {
	x.xUserSettingDo.DO = *(x.xUserSettingDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserSetting) updateTableName(table string) *xUserSetting {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.ChipList = field.NewString(table, "ChipList")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserSetting) WithContext(ctx context.Context) *xUserSettingDo {
	return x.xUserSettingDo.WithContext(ctx)
}

func (x xUserSetting) TableName() string { return x.xUserSettingDo.TableName() }

func (x xUserSetting) Alias() string { return x.xUserSettingDo.Alias() }

func (x xUserSetting) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserSettingDo.Columns(cols...)
}

func (x *xUserSetting) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserSetting) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["ChipList"] = x.ChipList
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserSetting) clone(db *gorm.DB) xUserSetting {
	x.xUserSettingDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserSetting) replaceDB(db *gorm.DB) xUserSetting {
	x.xUserSettingDo.ReplaceDB(db)
	return x
}

type xUserSettingDo struct{ gen.DO }

func (x xUserSettingDo) Debug() *xUserSettingDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserSettingDo) WithContext(ctx context.Context) *xUserSettingDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserSettingDo) ReadDB() *xUserSettingDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserSettingDo) WriteDB() *xUserSettingDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserSettingDo) Session(config *gorm.Session) *xUserSettingDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserSettingDo) Clauses(conds ...clause.Expression) *xUserSettingDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserSettingDo) Returning(value interface{}, columns ...string) *xUserSettingDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserSettingDo) Not(conds ...gen.Condition) *xUserSettingDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserSettingDo) Or(conds ...gen.Condition) *xUserSettingDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserSettingDo) Select(conds ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserSettingDo) Where(conds ...gen.Condition) *xUserSettingDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserSettingDo) Order(conds ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserSettingDo) Distinct(cols ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserSettingDo) Omit(cols ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserSettingDo) Join(table schema.Tabler, on ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserSettingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserSettingDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserSettingDo) Group(cols ...field.Expr) *xUserSettingDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserSettingDo) Having(conds ...gen.Condition) *xUserSettingDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserSettingDo) Limit(limit int) *xUserSettingDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserSettingDo) Offset(offset int) *xUserSettingDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserSettingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserSettingDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserSettingDo) Unscoped() *xUserSettingDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserSettingDo) Create(values ...*model.XUserSetting) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserSettingDo) CreateInBatches(values []*model.XUserSetting, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserSettingDo) Save(values ...*model.XUserSetting) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserSettingDo) First() (*model.XUserSetting, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserSetting), nil
	}
}

func (x xUserSettingDo) Take() (*model.XUserSetting, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserSetting), nil
	}
}

func (x xUserSettingDo) Last() (*model.XUserSetting, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserSetting), nil
	}
}

func (x xUserSettingDo) Find() ([]*model.XUserSetting, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserSetting), err
}

func (x xUserSettingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserSetting, err error) {
	buf := make([]*model.XUserSetting, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserSettingDo) FindInBatches(result *[]*model.XUserSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserSettingDo) Attrs(attrs ...field.AssignExpr) *xUserSettingDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserSettingDo) Assign(attrs ...field.AssignExpr) *xUserSettingDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserSettingDo) Joins(fields ...field.RelationField) *xUserSettingDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserSettingDo) Preload(fields ...field.RelationField) *xUserSettingDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserSettingDo) FirstOrInit() (*model.XUserSetting, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserSetting), nil
	}
}

func (x xUserSettingDo) FirstOrCreate() (*model.XUserSetting, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserSetting), nil
	}
}

func (x xUserSettingDo) FindByPage(offset int, limit int) (result []*model.XUserSetting, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserSettingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserSettingDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserSettingDo) Delete(models ...*model.XUserSetting) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserSettingDo) withDO(do gen.Dao) *xUserSettingDo {
	x.DO = *do.(*gen.DO)
	return x
}
