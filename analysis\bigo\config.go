package bigo

import (
	"encoding/json"
	"github.com/beego/beego/logs"
	"sync"
	"xserver/server"
)

const apiUrlTpl = "https://api.bytegle.site/bigoad/trackingevent"

type Maidian struct {
	Bigo struct {
		BackEnd struct {
			PixelID string `json:"PIXEL_ID"`
		} `json:"BackEnd"`
		Front struct {
			PixelID string `json:"PIXEL_ID"`
		} `json:"Front"`
	} `json:"Bigo"`
}

var mu sync.Mutex

func loadConfig(host string) {
	mu.Lock()
	defer mu.Unlock()
	clients = make(map[string]*Client)

	xChannelHost := server.DaoxHashGame().XChannelHost
	hostConfig, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	if hostConfig == nil {
		logs.Error("暂无Bigo埋点配置！")
		return
	}

	if hostConfig.Maidian == "" {
		logs.Error("暂无Bigo埋点配置")
		return
	}

	var cfg Maidian
	err := json.Unmarshal([]byte(hostConfig.Maidian), &cfg)
	if err != nil {
		logs.Error(err)
		return
	}

	clients[host] = &Client{
		host:    host,
		api:     apiUrlTpl,
		pixelId: cfg.Bigo.BackEnd.PixelID,
	}

	return
}
