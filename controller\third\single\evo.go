package single

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/oschwald/geoip2-golang"
	"math"
	"net"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var tzUTC8 = time.FixedZone("utc+8", 8*3600)

type EvoConfig struct {
	url                   string
	merchant              string
	currency              string
	key                   string
	token                 string
	id                    string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}
type EVOT map[string]*EvoConfig

func NewEvoLogic(params map[string]interface{}, fc func(int) error) map[string]*EvoConfig {

	respconfig := make(map[string]*EvoConfig, 0)
	for k := range params {
		temp := params[k].(map[string]interface{})
		respconfig[k] = &EvoConfig{
			url:                   temp["url"].(string),
			merchant:              temp["merchant"].(string),
			currency:              temp["currency"].(string),
			key:                   temp["key"].(string),
			token:                 temp["token"].(string),
			id:                    temp["id"].(string),
			brandName:             "evo",
			RefreshUserAmountFunc: fc,
			thirdGamePush:         base.NewThirdGamePush(),
		}
	}
	return respconfig
}

const cacheKey = "cacheKeyEVO:"

func (l EVOT) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Lang    string
		TableId string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)

	if err, errcode = base.IsLoginByUserId(cacheKey, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	//ip
	strip := ctx.GetIp()
	if net.ParseIP(ctx.GetIp()) == nil {
		strip = "************"
	}

	//国家
	country := ctx.Gin().Request.Header.Get("country")

	eur := "AT,BE,CY,EE,FI,FR,DE,GR,IE,IT,LV,LT,LU,MT,NL,PT,SK,SI,ES"
	if country != "" && strings.Contains(eur, country) {
		country = "EU"
	}
	logs.Info("EVO 获取到的国家：", country, "token.UserId=", token.UserId)
	if country == "" {
		//ctx.RespErrString(true, &errcode, "您的区域暂不支持")
		//return
		country = "CN" //给默认国家
		logs.Info("EVO 给默认国家：", country, "token.UserId=", token.UserId)
	}

	//姓名
	strid := fmt.Sprintf("%d", token.UserId)

	//语言
	if reqdata.Lang == "" {
		reqdata.Lang = "zh"
	}

	//游戏类型
	category := "roulette"
	// 获取游戏类型
	if reqdata.TableId != "" {
		// 从数据库中获取
		where := abugo.AbuDbWhere{}
		where.Add("and", "GameId", "=", reqdata.TableId, nil)
		gameData, _ := server.Db().Table("x_game_list").Where(where).GetOne()
		if gameData != nil {
			category = (*gameData)["ThirdGameType"].(string)
		}
	}

	//桌子
	tableid := ""
	if reqdata.TableId != "" {
		tableid = fmt.Sprintf(`"id":"%s"`, reqdata.TableId)
	}

	//sessionId
	sessionId := base.UserId2token(cacheKey, token.UserId)

	//本地测试 暂时将国家写死 这是以前的代码 线上环境也有这句 先注释 2024-11-1

	//countryName := l.getIPContinent(strip)
	//logs.Info("EVO countryName", countryName)

	//正式环境默认中国
	currency := "CN"
	if strings.EqualFold(country, "IN") { //如果是印度IP走INR线路
		country = "IN"
		currency = "INR"
	} else {
		currency = "CN" //正式环境默认是人民币
		country = "CN"  //正式环境默认是中国
	}

	la, ok := l[currency]
	if !ok {
		ctx.RespErrString(true, &errcode, "您的区域暂不支持")
		return
	}
	logs.Info("EVO getcurrency", currency, "la.currency=", la.currency)

	groupId := ""
	//系统限红
	if la.id != "" {
		groupId = fmt.Sprintf(`"id":"%s",`, la.id)
	}
	//做限红处理
	where := abugo.AbuDbWhere{}
	where.Add("and", "user_id", "=", token.UserId, nil)
	where.Add("and", "state", "=", 1, nil)
	limitData, err := server.Db().Table("x_third_config_limit").Where(where).GetOne()
	if limitData != nil {
		groupId = fmt.Sprintf(`"id":"%s",`, (*limitData)["third_limit_id"])
	}
	querydata := fmt.Sprintf(`{
        "uuid": "%s",
        "player": {
            "id": "%d",
            "update": true,
            "firstName": "%s",
            "lastName": "%s",
            "country": "%s",
            "nickname": "%s",
            "language": "%s",
            "currency": "%s",
            "session": {
                "id": "%s",
                "ip": "%s"
            },
            "group": {
                %s
                "action": "assign"
            }
        },
        "config": {
            "game": {
                "category": "%s",
                "interface": "view1",
                "table": {
                    %s
                }
            },
            "channel": {
                "wrapped": false,
                "mobile": false
            }
        }
    }`, abugo.GetUuid(), token.UserId, strid[:5], strid[5:], country, strid, reqdata.Lang, la.currency, sessionId, strip, groupId, category, tableid)
	url := fmt.Sprintf("%s/ua/v1/%s/%s", la.url, la.key, la.token)
	httpClient := httpc.DoRequest{
		UrlPath:    url,
		Param:      []byte(querydata),
		PostMethod: httpc.JSON_DATA,
	}
	data, err := httpClient.DoPost()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if _, ok := data["errors"]; ok {
		logs.Error("evo_http_post body error:", data["errors"])
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	if data != nil {
		ctx.Put("url", data["entry"])
	}
	ctx.RespOK()

}

//验证token

func (l EVOT) CheckToken(authToken string) bool {
	for k := range l {
		if l[k].token == authToken {
			return true
		}
	}
	return false
}

//OK  成功
//TEMPORARY_ERROR  可重试的错误  游戏服务器暂时出现问题。
//INVALID_TOKEN_ID  致命错误    赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//INVALID_SID  致命错误         赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//ACCOUNT_LOCKED               赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//FATAL_ERROR_CLOSE_USER_SESSION  赌场出了问题。用户身份验证失败或您的会话可能已过期，请关闭浏览器并重试
//UNKNOWN_ERROR  	请联系客户支持寻求帮助。
//INVALID_PARAMETER 	请联系客户支持寻求帮助。
//BET_DOES_NOT_EXIST 	请联系客户支持寻求帮助。
//BET_ALREADY_EXIST     投注已存在于第三方系统中。
//BET_ALREADY_SETTLED 	投注已在第三方系统中结算。
//INSUFFICIENT_FUNDS 	您没有足够的资金来进行此投注。。

type req2 struct {
	UserId  string `json:"userId"`
	Sid     string `json:"sid"`
	Channel struct {
		Type string `json:"type"`
	} `json:"channel"`
	Uuid string `json:"uuid"`
}

// 检查请求
func (l EVOT) Check(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status string `json:"status"`
		Sid    string `json:"sid"`
		Uuid   string `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	logs.Error("CheckCheckCheck", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		return
	}
	req := req2{}
	_ = ctx.Gin().ShouldBindJSON(&req)
	id := req.Sid
	logs.Error("CheckCheckCheck", ctx.Gin().Request.URL, authToken, id, req)
	userId := base.Token2UserId(cacheKey, id)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Sid = abugo.GetUuid()
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	res.Status = "OK"
	res.Sid = base.UserId2token(cacheKey, userId)
	res.Uuid = abugo.GetUuid()
	ctx.RespJson(res)
	return
}

// 余额请求
func (l EVOT) Balance(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status  string  `json:"status"`
		Balance float64 `json:"balance"`
		Uuid    string  `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	if !l.CheckToken(authToken) {
		return
	}
	logs.Error("BalanceBalanceBalance", ctx.Gin().Request.URL, authToken)
	req := req2{}
	_ = ctx.Gin().ShouldBindJSON(&req)
	id := req.Sid
	logs.Error("BalanceBalanceBalanceBalance", ctx.Gin().Request.URL, authToken, id, req)
	userId := base.Token2UserId(cacheKey, id)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	_, balance, err := base.GetUserBalance(userId, cacheKey)
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	balance2 := float64(int(balance*100)) / 100
	res.Status = "OK"
	res.Balance = balance2
	res.Uuid = abugo.GetUuid()
	ctx.RespJson(res)
	return
}

type request struct {
	Uuid     string `json:"uuid"`
	Sid      string `json:"sid"`
	UserId   string `json:"userId"`
	Currency string `json:"currency"`
	Game     struct {
		Id      string `json:"id"`
		Type    string `json:"type"`
		Details struct {
			Table struct {
				Id  string `json:"id"`
				Vid string `json:"vid"`
			} `json:"table"`
		} `json:"details"`
	} `json:"game"`
	Transaction struct {
		Id     string  `json:"id"`
		RefId  string  `json:"refId"`
		Amount float64 `json:"amount"`
	} `json:"transaction"`
}

func (l EVOT) Debit(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status  string  `json:"status"`
		Balance float64 `json:"balance"`
		Uuid    string  `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	logs.Error("DebitDebitDebit", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		return
	}

	req := request{}
	err := ctx.Gin().ShouldBindJSON(&req)

	reqDataByte, _ := json.Marshal(req)
	logs.Debug("EVO Debit api receive:%s, ", string(reqDataByte))

	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	userId := base.Token2UserId(cacheKey, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKey)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	//三方来源的数据整理
	var (
		betAmount = req.Transaction.Amount
		thirdId   = req.Game.Id
		gamecode  = req.Game.Details.Table.Id
		thirdTime = time.Now()
	)

	if betAmount > balance2 {
		res.Status = "INSUFFICIENT_FUNDS"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	//查询订单是否存在
	betTran, isExist := base.OrderIsExist(thirdId, "evo", "x_third_live_pre_order")
	if isExist {
		res.Status = "BET_ALREADY_EXIST"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	//开启事务
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		ressql, err2 := tx.Tx.Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmount, userId, betAmount)
		row, err3 := ressql.RowsAffected()
		if err2 != nil || err3 != nil || row < 1 {
			logs.Debug("evo 下注 修改x_user失败了:  id = ", thirdId, err2, err3)
			res.Status = "INSUFFICIENT_FUNDS"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)

			return errors.New("修改x_user失败了")
		}
		afterBalance := balance2 - betAmount
		if betTran != nil {
			betId := abugo.GetInt64FromInterface((*betTran)["Id"])
			_, err = tx.Tx.Exec(`update x_third_live_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmount, betId)
			if err != nil {
				logs.Debug("evo 下注 修改x_third_live_pre_order修改失败了:  id = ", thirdId, err)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = balance2
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return err
			}
		} else {
			order := xgo.H{
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
				"BetChannelId": ChannelId,
				"UserId":       userId,
				"Brand":        "evo",
				"ThirdId":      thirdId,
				"GameId":       gamecode,
				"GameName":     gamecode,
				"BetAmount":    betAmount,
				"WinAmount":    0,
				"ValidBet":     0,
				"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
				"Currency":     req.Currency,
				"RawData":      string(reqDataByte),
				"DataState":    -1,
			}
			_, err = tx.Table("x_third_live_pre_order").Insert(order)
			if err != nil {
				logs.Debug("evo 下注 修改x_third_live_pre_order新增失败了:  id = ", thirdId, err)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = balance2
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return err
			}
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance2,
			"Amount":       0 - betAmount,
			"AfterAmount":  balance2 - betAmount,
			"Reason":       utils.BalanceCReasonEVOBet,
			"Memo":         "evo bet,thirdId:" + thirdId,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Debug("evo 下注 新增x_amount_change_log失败了:  id = ", thirdId, err)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}
		res.Status = "OK"
		res.Balance = afterBalance
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		for _, v := range l {
			if v == nil || v.RefreshUserAmountFunc == nil {
				continue
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][EVOT] Debit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					break
				}
			}
		}
	}(userId)
}

// 结算
func (l EVOT) Credit(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status  string  `json:"status"`
		Balance float64 `json:"balance"`
		Uuid    string  `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	logs.Error("CreditCreditCredit", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		return
	}

	req := request{}
	err := ctx.Gin().ShouldBindJSON(&req)

	reqDataByte, _ := json.Marshal(req)
	logs.Debug("EVO Credit api receive:%s, ", string(reqDataByte))

	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKey, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKey)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	//三方来源的数据整理
	var (
		amount   = req.Transaction.Amount
		thirdId  = req.Game.Id
		gamecode = req.Game.Details.Table.Id
		// thirdTime = time.Now()
	)

	//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", "evo", nil)
	where.Add("and", "DataState", "=", -1, nil)
	betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		res.Status = "BET_DOES_NOT_EXIST"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	logs.Error("evo 订单数据：", *betTran)

	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	validBet := math.Abs(amount - betAmount)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}
	//开启事务
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		//将下注订单移动至结算订单表
		//修改成已经结算了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_live_pre_order set
		WinAmount = ?,
		GameName = ?,
		RawData = ?,
		GameId = ?,
		ValidBet = ?,
		DataState = 1
		where Id = ?`,
			amount,
			"evo",
			string(reqDataByte),
			gamecode,
			validBet,
			betId,
		)
		if err != nil {
			logs.Debug("evo 结算 修改成已经结算了 x_third_live_pre_order 失败了:  id = ", thirdId, err)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}
		//移动至统计表
		tmp := (*betTran)
		delete(tmp, "Id")
		delete(tmp, "CreateTime")

		// 获取游戏名称
		gameListDao := server.DaoxHashGame().XGameList
		gameListDb := gameListDao.WithContext(nil)
		gameInfos, _ := gameListDb.Where(gameListDao.GameID.Eq(gamecode)).Where(gameListDao.Brand.Eq("evo")).First()
		tmp["GameName"] = "evo"
		if gameInfos != nil {
			tmp["GameName"] = gameInfos.Name
		}

		tmp["DataState"] = 1
		tmp["WinAmount"] = amount
		tmp["RawData"] = string(reqDataByte)
		tmp["GameId"] = gamecode
		tmp["ValidBet"] = validBet
		_, err = tx.Table("x_third_live").Insert(tmp)
		if err != nil {
			logs.Debug("evo 结算 移动至统计表 x_third_live_pre_order 失败了:  id = ", thirdId, err)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}
		//处理结算
		if amount > 0 {
			//win
			ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			row, err3 := ressql.RowsAffected()
			if err2 != nil || err3 != nil || row < 1 {
				logs.Debug("evo 结算 处理结算 x_user 失败了:  id = ", thirdId, err2, err3)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = balance2
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return errors.New("修改x_user失败了")
			}
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance2,
				"Amount":       amount,
				"AfterAmount":  balance2 + amount,
				"Reason":       utils.BalanceCReasonEVOSettle,
				"Memo":         "evo settle,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Debug("evo 结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
				res.Status = "TEMPORARY_ERROR"
				res.Balance = balance2
				res.Uuid = abugo.GetUuid()
				ctx.RespJson(res)
				return err
			}
		}
		logs.Debug("evo sw :", "結算成功", thirdId)
		res.Status = "OK"
		res.Balance = balance2 + amount
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		for _, v := range l {

			if v == nil || v.RefreshUserAmountFunc == nil {
				continue
			}
			// 推送奖励事件通知
			if v.thirdGamePush != nil {
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				v.thirdGamePush.PushRewardEvent(5, v.brandName, thirdId)
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][EVOT] Credit 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					break
				}
			}
		}
	}(userId)
}

// 取消请求
func (l EVOT) Cancel(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Status  string  `json:"status"`
		Balance float64 `json:"balance"`
		Uuid    string  `json:"uuid"`
	}
	res := resp{}
	authToken := ctx.Query("authToken")
	logs.Error("CancelCancelCancel", ctx.Gin().Request.URL, authToken)
	if !l.CheckToken(authToken) {
		return
	}

	req := request{}
	err := ctx.Gin().ShouldBindJSON(&req)

	reqDataByte, _ := json.Marshal(req)
	logs.Info("EVO Cancel api receive:%s, ", string(reqDataByte))

	if err != nil {
		res.Status = "INVALID_PARAMETER"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKey, req.Sid)
	if userId == -1 {
		res.Status = "INVALID_SID"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKey)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		res.Status = "TEMPORARY_ERROR"
		res.Balance = 0
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}

	//三方来源的数据整理
	var (
		amount  = req.Transaction.Amount
		thirdId = req.Game.Id
	)

	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", "evo", nil)
	betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
	if betTran == nil {
		//订单不存在
		logs.Error("evo 订单不存在:  id = ", thirdId)
		res.Status = "BET_DOES_NOT_EXIST"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	if dataState == -2 {
		//已经取消了
		logs.Error("evo 订单已经取消了:  id = ", thirdId, " userId=", userId, " betAmunt=", betAmount, " cancelAmunt=", amount)
		res.Status = "UNKNOWN_ERROR"
		res.Balance = balance2
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)
		return
	}
	//开启事务
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		//修改成取消了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_live_pre_order set
		GameName = ?,
		RawData = ?,
		DataState = -2
		where Id = ?`,
			"evo",
			string(reqDataByte),
			betId,
		)
		if err != nil {
			logs.Info("evo 订单取消 修改x_third_live_pre_order状态失败了:  id = ", thirdId, err)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}

		ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
		row, err3 := ressql.RowsAffected()
		if err2 != nil || err3 != nil || row < 1 {
			logs.Error("evo 订单取消 操作金币上分失败了:  id = ", thirdId, err2, err3)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return errors.New("修改x_user失败了")
		}

		// 定义一个变量来记录Memo 内容
		var memoText string
		// 只有当amount和betAmount不相等时，才在Memo中记录这两个值
		if amount != betAmount {
			memoText = "evo cancel,betAmt:" + fmt.Sprint(betAmount) + ",cancAmt:" + fmt.Sprint(amount) + ",thirdId:" + thirdId
		} else {
			memoText = "evo cancel,thirdId:" + thirdId
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance2,
			"Amount":       amount,
			"AfterAmount":  balance2 + amount,
			"Reason":       utils.BalanceCReasonEVOCancel,
			"Memo":         memoText,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Error("evo 订单取消 操作金币日志失败了:  id = ", thirdId, err)
			res.Status = "TEMPORARY_ERROR"
			res.Balance = balance2
			res.Uuid = abugo.GetUuid()
			ctx.RespJson(res)
			return err
		}
		logs.Info("evo 订单取消成功了:  id = ", thirdId, " userId=", userId, "  betAmunt=", betAmount, " cancelAmunt=", amount)
		res.Status = "OK"
		res.Balance = balance2 + amount
		res.Uuid = abugo.GetUuid()
		ctx.RespJson(res)

		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		for _, v := range l {
			if v == nil || v.RefreshUserAmountFunc == nil {
				continue
			}
			if v.RefreshUserAmountFunc != nil {
				tmpErr := v.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][EVOT] Cancel 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				} else {
					break
				}
			}
		}
	}(userId)
}

func (l EVOT) GameList(ctx *abugo.AbuHttpContent) {
	// 获取游戏列表
	type EvoGameList struct {
		Data []struct {
			TableName           string `json:"Table Name"`
			TableID             string `json:"Table ID"`
			DirectLaunchTableID string `json:"Direct Launch Table ID"`
			GameType            string `json:"Game Type"`
			BetLimit            struct {
				Usd struct {
					Symbol string  `json:"symbol"`
					Min    float64 `json:"min"`
					Max    int     `json:"max"`
				} `json:"USD"`
			} `json:"Bet Limit"`
		} `json:"data"`
	}

	type req struct {
		Host string `json:"host"`
	}

	reqdata := req{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, nil, err.Error())
		return

	}

	key := l["CN"].key
	token := l["CN"].token

	// base64(key:token)
	auth := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", key, token)))
	url := fmt.Sprintf("%s/api/lobby/v1/%s/tablelist", reqdata.Host, key)

	// Get请求
	httpClient := httpc.DoRequest{
		UrlPath:    url,
		PostMethod: httpc.JSON_DATA,
		Header:     map[string]string{"Authorization": fmt.Sprintf("Basic %s", auth)},
	}

	data, err := httpClient.DoGet()

	if err != nil {
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	//logs.Info("evo GameList data:", string(data))

	gameList := EvoGameList{}
	json.Unmarshal(data, &gameList)

	//// 获取游戏类别过滤掉重复的数据
	//var gameTypes []string
	//uniqueGameTypes := make(map[string]bool)
	//for _, v := range gameList.Data {
	//	if _, ok := uniqueGameTypes[v.GameType]; !ok {
	//		uniqueGameTypes[v.GameType] = true
	//		gameTypes = append(gameTypes, v.GameType)
	//	}
	//}

	thirdCatToSubType := map[string]string{
		"baccarat":                "BAC",
		"blackjack":               "BJ",
		"rng-topcard":             "OTHER",
		"roulette":                "ROU",
		"stockmarket":             "OTHER",
		"crazypachinko":           "OTHER",
		"rng-dealnodeal":          "OTHER",
		"rng-baccarat":            "BAC",
		"dragontiger":             "DT",
		"rng-dragontiger":         "DT",
		"cashorcrash":             "OTHER",
		"videopoker":              "OTHER",
		"lightningscalablebj":     "OTHER",
		"holdem":                  "OTHER",
		"rng-roulette":            "ROU",
		"dhp":                     "OTHER",
		"powerball":               "OTHER",
		"monopoly":                "OTHER",
		"funkytime":               "OTHER",
		"andarbahar":              "OTHER",
		"moneywheel":              "OTHER",
		"gonzotreasuremap":        "OTHER",
		"crazytime":               "OTHER",
		"teenpatti":               "OTHER",
		"craps":                   "OTHER",
		"monopolybigballer":       "OTHER",
		"bacbo":                   "BAC",
		"sidebetcity":             "OTHER",
		"trp":                     "OTHER",
		"rng-lightningscalablebj": "OTHER",
		"csp":                     "OTHER",
		"rng-videopoker":          "OTHER",
		"instantroulette":         "ROU",
		"dealnodeal":              "OTHER",
		"rng-blackjack":           "BJ",
		"megaball":                "OTHER",
		"lightningdice":           "OTHER",
		"gonzotreasurehunt":       "OTHER",
		"thb":                     "OTHER",
		"rng-megaball":            "OTHER",
		"topdice":                 "OTHER",
		"classicfreebet":          "OTHER",
		"eth":                     "OTHER",
		"deadoralivesaloon":       "OTHER",
		"crazycoinflip":           "OTHER",
		"lightninglotto":          "OTHER",
		"rng-lightninglotto":      "OTHER",
		"scalableblackjack":       "OTHER",
		"topcard":                 "OTHER",
		"sicbo":                   "SB",
		"rng-sicbo":               "SB",
		"powerscalableblackjack":  "BJ",
		"americanroulette":        "ROU",
		"rng-moneywheel":          "OTHER",
		"extrachilliepicspins":    "OTHER",
		"rng-craps":               "OTHER",
		"fantan":                  "OTHER",
		"reddoorroulette":         "ROU",
		"freebet":                 "OTHER",
		"rng-american-roulette":   "ROU",
	}

	//logs.Info("evo GameList gameTypes:", gameTypes)
	//ctx.Put("gameTypes", gameTypes)
	// 插入数据到数据库
	for _, v := range gameList.Data {

		// 获取游戏类型
		subType, ok := thirdCatToSubType[v.GameType]
		if !ok {
			subType = "OTHER"
		}

		// 插入数据
		_, err := server.Db().Table("x_game_list").Insert(xgo.H{
			"GameId":        v.TableID,
			"Name":          v.TableName,
			"EName":         v.TableName,
			"Brand":         "evo",
			"State":         1,
			"OpenState":     1,
			"GameSubType":   subType,
			"ThirdGameType": v.GameType,
		})

		if err != nil {
			logs.Info("evo GameList insert error:", err)
		}

		logs.Info("evo GameList insert success:", v.TableName)
	}

	ctx.RespOK()

}

// 根据IP传对应的币种
func (l EVOT) getIPContinent(ipStr string) string {
	// 打开 MaxMind 数据库文件
	db, err := geoip2.Open("./config/GeoLite2-Country.mmdb")
	if err != nil {
		logs.Error("geoip2.Open", err)
		return ""
	}
	defer db.Close()

	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		logs.Error("Invalid IP address format")
		return ""
	}

	// 查询 IP 地址的国家和洲信息
	record, err := db.Country(ip)
	if err != nil {
		logs.Error("db.Country(ip)", err)
		return ""
	}

	// 返回国家信息
	return record.Country.Names["zh-CN"]
}
