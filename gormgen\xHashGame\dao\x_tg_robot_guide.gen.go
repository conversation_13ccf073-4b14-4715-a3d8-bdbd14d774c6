// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTgRobotGuide(db *gorm.DB, opts ...gen.DOOption) xTgRobotGuide {
	_xTgRobotGuide := xTgRobotGuide{}

	_xTgRobotGuide.xTgRobotGuideDo.UseDB(db, opts...)
	_xTgRobotGuide.xTgRobotGuideDo.UseModel(&model.XTgRobotGuide{})

	tableName := _xTgRobotGuide.xTgRobotGuideDo.TableName()
	_xTgRobotGuide.ALL = field.NewAsterisk(tableName)
	_xTgRobotGuide.ID = field.NewInt64(tableName, "Id")
	_xTgRobotGuide.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRobotGuide.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgRobotGuide.TgRobotUserName = field.NewString(tableName, "TgRobotUserName")
	_xTgRobotGuide.TgRobotToken = field.NewString(tableName, "TgRobotToken")
	_xTgRobotGuide.TgAccountID = field.NewInt32(tableName, "TgAccountId")
	_xTgRobotGuide.KefuTgUserName = field.NewString(tableName, "KefuTgUserName")
	_xTgRobotGuide.RegGift = field.NewFloat64(tableName, "RegGift")
	_xTgRobotGuide.GameURL = field.NewString(tableName, "GameUrl")
	_xTgRobotGuide.Welcome = field.NewString(tableName, "Welcome")
	_xTgRobotGuide.Welcome2 = field.NewString(tableName, "Welcome2")
	_xTgRobotGuide.BindMsg = field.NewString(tableName, "BindMsg")
	_xTgRobotGuide.GuideMsg = field.NewString(tableName, "GuideMsg")
	_xTgRobotGuide.FirstChargeActive = field.NewString(tableName, "FirstChargeActive")
	_xTgRobotGuide.FuLiMsg = field.NewString(tableName, "FuLiMsg")
	_xTgRobotGuide.LianxiKefuMsg = field.NewString(tableName, "LianxiKefuMsg")
	_xTgRobotGuide.LianxiKefuInterval = field.NewInt32(tableName, "LianxiKefuInterval")
	_xTgRobotGuide.CommonReply = field.NewString(tableName, "CommonReply")
	_xTgRobotGuide.IsEnable = field.NewInt32(tableName, "IsEnable")
	_xTgRobotGuide.H5KefuPopText = field.NewString(tableName, "H5KefuPopText")
	_xTgRobotGuide.H5KefuLink = field.NewString(tableName, "H5KefuLink")
	_xTgRobotGuide.GroupChatID = field.NewInt64(tableName, "GroupChatId")
	_xTgRobotGuide.IsTiyanjin = field.NewInt32(tableName, "IsTiyanjin")
	_xTgRobotGuide.IsIPRestriction = field.NewInt32(tableName, "IsIPRestriction")
	_xTgRobotGuide.IsDeviceRestriction = field.NewInt32(tableName, "IsDeviceRestriction")
	_xTgRobotGuide.IsWalletRestriction = field.NewInt32(tableName, "IsWalletRestriction")
	_xTgRobotGuide.IsAotoTyj2 = field.NewInt32(tableName, "IsAotoTyj2")
	_xTgRobotGuide.IPWhitelist = field.NewString(tableName, "IPWhitelist")
	_xTgRobotGuide.TgRobotType = field.NewInt32(tableName, "TgRobotType")
	_xTgRobotGuide.TrxGiftAmount = field.NewFloat64(tableName, "TrxGiftAmount")
	_xTgRobotGuide.UsdtGiftAmount = field.NewFloat64(tableName, "UsdtGiftAmount")
	_xTgRobotGuide.CreatedAt = field.NewTime(tableName, "CreatedAt")
	_xTgRobotGuide.UpdatedAt = field.NewTime(tableName, "UpdatedAt")
	_xTgRobotGuide.TaskGroup = field.NewString(tableName, "TaskGroup")

	_xTgRobotGuide.fillFieldMap()

	return _xTgRobotGuide
}

type xTgRobotGuide struct {
	xTgRobotGuideDo xTgRobotGuideDo

	ALL                 field.Asterisk
	ID                  field.Int64
	SellerID            field.Int32   // 运营商id
	ChannelID           field.Int32   // 渠道id
	TgRobotUserName     field.String  // 机器人Username
	TgRobotToken        field.String  // 机器人token
	TgAccountID         field.Int32   // x_tg_account.Id
	KefuTgUserName      field.String  // 客服tg号
	RegGift             field.Float64 // 注册彩金(废弃)
	GameURL             field.String  // 游戏链接
	Welcome             field.String  // 欢迎文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	Welcome2            field.String  // 欢迎文案2 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	BindMsg             field.String  // 绑定钱包地址提示文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	GuideMsg            field.String  // 未识别消息的指引文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	FirstChargeActive   field.String  // 首充活动文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	FuLiMsg             field.String  // 福利文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	LianxiKefuMsg       field.String  // 联系客服文案 {"type":"文案类型：1:文案 2：图片+文案 3：视频+文案","url":"图片链接或视频链接","text":"文案"}
	LianxiKefuInterval  field.Int32   // 联系客服间隔时间（注册经过多少分钟未申请体验金的用户，机器人主动发送客服联系方式）
	CommonReply         field.String  // 通用回复模板
	IsEnable            field.Int32   // 是否启用 (1:是 2:否)
	H5KefuPopText       field.String  // H5客服弹窗文案
	H5KefuLink          field.String  // H5客服弹窗按钮链接
	GroupChatID         field.Int64   // tg用户在汇报群内的ChatId
	IsTiyanjin          field.Int32   // 是否开启体验金(1:是 2:否)
	IsIPRestriction     field.Int32   // 是否开启IP限制(1:是 2:否)
	IsDeviceRestriction field.Int32   // 是否开启设备限制(1:是 2:否)
	IsWalletRestriction field.Int32   // 是否开启钱包地址关联限制(1:是 2:否)
	IsAotoTyj2          field.Int32   // 是否自动发放第二笔体验金(1:是 2:否)
	IPWhitelist         field.String  // ip白名单(英文逗号分割)
	TgRobotType         field.Int32   // 机器人类型(1:引客机器人 2:英文接待机器人)
	TrxGiftAmount       field.Float64 // TRX赠送金额
	UsdtGiftAmount      field.Float64 // USDT赠送金额
	CreatedAt           field.Time    // 创建时间
	UpdatedAt           field.Time    // 修改时间
	TaskGroup           field.String  // 关注频道链接

	fieldMap map[string]field.Expr
}

func (x xTgRobotGuide) Table(newTableName string) *xTgRobotGuide {
	x.xTgRobotGuideDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRobotGuide) As(alias string) *xTgRobotGuide {
	x.xTgRobotGuideDo.DO = *(x.xTgRobotGuideDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRobotGuide) updateTableName(table string) *xTgRobotGuide {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.TgRobotUserName = field.NewString(table, "TgRobotUserName")
	x.TgRobotToken = field.NewString(table, "TgRobotToken")
	x.TgAccountID = field.NewInt32(table, "TgAccountId")
	x.KefuTgUserName = field.NewString(table, "KefuTgUserName")
	x.RegGift = field.NewFloat64(table, "RegGift")
	x.GameURL = field.NewString(table, "GameUrl")
	x.Welcome = field.NewString(table, "Welcome")
	x.Welcome2 = field.NewString(table, "Welcome2")
	x.BindMsg = field.NewString(table, "BindMsg")
	x.GuideMsg = field.NewString(table, "GuideMsg")
	x.FirstChargeActive = field.NewString(table, "FirstChargeActive")
	x.FuLiMsg = field.NewString(table, "FuLiMsg")
	x.LianxiKefuMsg = field.NewString(table, "LianxiKefuMsg")
	x.LianxiKefuInterval = field.NewInt32(table, "LianxiKefuInterval")
	x.CommonReply = field.NewString(table, "CommonReply")
	x.IsEnable = field.NewInt32(table, "IsEnable")
	x.H5KefuPopText = field.NewString(table, "H5KefuPopText")
	x.H5KefuLink = field.NewString(table, "H5KefuLink")
	x.GroupChatID = field.NewInt64(table, "GroupChatId")
	x.IsTiyanjin = field.NewInt32(table, "IsTiyanjin")
	x.IsIPRestriction = field.NewInt32(table, "IsIPRestriction")
	x.IsDeviceRestriction = field.NewInt32(table, "IsDeviceRestriction")
	x.IsWalletRestriction = field.NewInt32(table, "IsWalletRestriction")
	x.IsAotoTyj2 = field.NewInt32(table, "IsAotoTyj2")
	x.IPWhitelist = field.NewString(table, "IPWhitelist")
	x.TgRobotType = field.NewInt32(table, "TgRobotType")
	x.TrxGiftAmount = field.NewFloat64(table, "TrxGiftAmount")
	x.UsdtGiftAmount = field.NewFloat64(table, "UsdtGiftAmount")
	x.CreatedAt = field.NewTime(table, "CreatedAt")
	x.UpdatedAt = field.NewTime(table, "UpdatedAt")
	x.TaskGroup = field.NewString(table, "TaskGroup")

	x.fillFieldMap()

	return x
}

func (x *xTgRobotGuide) WithContext(ctx context.Context) *xTgRobotGuideDo {
	return x.xTgRobotGuideDo.WithContext(ctx)
}

func (x xTgRobotGuide) TableName() string { return x.xTgRobotGuideDo.TableName() }

func (x xTgRobotGuide) Alias() string { return x.xTgRobotGuideDo.Alias() }

func (x xTgRobotGuide) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRobotGuideDo.Columns(cols...)
}

func (x *xTgRobotGuide) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRobotGuide) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 34)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["TgRobotUserName"] = x.TgRobotUserName
	x.fieldMap["TgRobotToken"] = x.TgRobotToken
	x.fieldMap["TgAccountId"] = x.TgAccountID
	x.fieldMap["KefuTgUserName"] = x.KefuTgUserName
	x.fieldMap["RegGift"] = x.RegGift
	x.fieldMap["GameUrl"] = x.GameURL
	x.fieldMap["Welcome"] = x.Welcome
	x.fieldMap["Welcome2"] = x.Welcome2
	x.fieldMap["BindMsg"] = x.BindMsg
	x.fieldMap["GuideMsg"] = x.GuideMsg
	x.fieldMap["FirstChargeActive"] = x.FirstChargeActive
	x.fieldMap["FuLiMsg"] = x.FuLiMsg
	x.fieldMap["LianxiKefuMsg"] = x.LianxiKefuMsg
	x.fieldMap["LianxiKefuInterval"] = x.LianxiKefuInterval
	x.fieldMap["CommonReply"] = x.CommonReply
	x.fieldMap["IsEnable"] = x.IsEnable
	x.fieldMap["H5KefuPopText"] = x.H5KefuPopText
	x.fieldMap["H5KefuLink"] = x.H5KefuLink
	x.fieldMap["GroupChatId"] = x.GroupChatID
	x.fieldMap["IsTiyanjin"] = x.IsTiyanjin
	x.fieldMap["IsIPRestriction"] = x.IsIPRestriction
	x.fieldMap["IsDeviceRestriction"] = x.IsDeviceRestriction
	x.fieldMap["IsWalletRestriction"] = x.IsWalletRestriction
	x.fieldMap["IsAotoTyj2"] = x.IsAotoTyj2
	x.fieldMap["IPWhitelist"] = x.IPWhitelist
	x.fieldMap["TgRobotType"] = x.TgRobotType
	x.fieldMap["TrxGiftAmount"] = x.TrxGiftAmount
	x.fieldMap["UsdtGiftAmount"] = x.UsdtGiftAmount
	x.fieldMap["CreatedAt"] = x.CreatedAt
	x.fieldMap["UpdatedAt"] = x.UpdatedAt
	x.fieldMap["TaskGroup"] = x.TaskGroup
}

func (x xTgRobotGuide) clone(db *gorm.DB) xTgRobotGuide {
	x.xTgRobotGuideDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRobotGuide) replaceDB(db *gorm.DB) xTgRobotGuide {
	x.xTgRobotGuideDo.ReplaceDB(db)
	return x
}

type xTgRobotGuideDo struct{ gen.DO }

func (x xTgRobotGuideDo) Debug() *xTgRobotGuideDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRobotGuideDo) WithContext(ctx context.Context) *xTgRobotGuideDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRobotGuideDo) ReadDB() *xTgRobotGuideDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRobotGuideDo) WriteDB() *xTgRobotGuideDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRobotGuideDo) Session(config *gorm.Session) *xTgRobotGuideDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRobotGuideDo) Clauses(conds ...clause.Expression) *xTgRobotGuideDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRobotGuideDo) Returning(value interface{}, columns ...string) *xTgRobotGuideDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRobotGuideDo) Not(conds ...gen.Condition) *xTgRobotGuideDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRobotGuideDo) Or(conds ...gen.Condition) *xTgRobotGuideDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRobotGuideDo) Select(conds ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRobotGuideDo) Where(conds ...gen.Condition) *xTgRobotGuideDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRobotGuideDo) Order(conds ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRobotGuideDo) Distinct(cols ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRobotGuideDo) Omit(cols ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRobotGuideDo) Join(table schema.Tabler, on ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRobotGuideDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRobotGuideDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRobotGuideDo) Group(cols ...field.Expr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRobotGuideDo) Having(conds ...gen.Condition) *xTgRobotGuideDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRobotGuideDo) Limit(limit int) *xTgRobotGuideDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRobotGuideDo) Offset(offset int) *xTgRobotGuideDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRobotGuideDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRobotGuideDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRobotGuideDo) Unscoped() *xTgRobotGuideDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRobotGuideDo) Create(values ...*model.XTgRobotGuide) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRobotGuideDo) CreateInBatches(values []*model.XTgRobotGuide, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRobotGuideDo) Save(values ...*model.XTgRobotGuide) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRobotGuideDo) First() (*model.XTgRobotGuide, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotGuide), nil
	}
}

func (x xTgRobotGuideDo) Take() (*model.XTgRobotGuide, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotGuide), nil
	}
}

func (x xTgRobotGuideDo) Last() (*model.XTgRobotGuide, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotGuide), nil
	}
}

func (x xTgRobotGuideDo) Find() ([]*model.XTgRobotGuide, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRobotGuide), err
}

func (x xTgRobotGuideDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRobotGuide, err error) {
	buf := make([]*model.XTgRobotGuide, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRobotGuideDo) FindInBatches(result *[]*model.XTgRobotGuide, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRobotGuideDo) Attrs(attrs ...field.AssignExpr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRobotGuideDo) Assign(attrs ...field.AssignExpr) *xTgRobotGuideDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRobotGuideDo) Joins(fields ...field.RelationField) *xTgRobotGuideDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRobotGuideDo) Preload(fields ...field.RelationField) *xTgRobotGuideDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRobotGuideDo) FirstOrInit() (*model.XTgRobotGuide, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotGuide), nil
	}
}

func (x xTgRobotGuideDo) FirstOrCreate() (*model.XTgRobotGuide, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotGuide), nil
	}
}

func (x xTgRobotGuideDo) FindByPage(offset int, limit int) (result []*model.XTgRobotGuide, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRobotGuideDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRobotGuideDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRobotGuideDo) Delete(models ...*model.XTgRobotGuide) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRobotGuideDo) withDO(do gen.Dao) *xTgRobotGuideDo {
	x.DO = *do.(*gen.DO)
	return x
}
