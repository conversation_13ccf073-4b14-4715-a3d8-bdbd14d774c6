package utils

import (
	"github.com/gin-gonic/gin"
	"sync"
	"time"
)

type TokenBucket struct {
	Capacity  int64      // 桶的容量
	Rate      float64    // 令牌放入速率
	Tokens    float64    // 当前令牌数量
	LastToken time.Time  // 上一次放令牌的时间
	Mtx       sync.Mutex // 互斥锁
}

func (tb *TokenBucket) Allow() bool {
	tb.Mtx.Lock()
	defer tb.Mtx.Unlock()
	now := time.Now()
	// 计算需要放的令牌数量
	tb.Tokens = tb.Tokens + tb.Rate*now.Sub(tb.LastToken).Seconds()
	if tb.Tokens > float64(tb.Capacity) {
		tb.Tokens = float64(tb.Capacity)
	}
	// 判断是否允许请求
	if tb.Tokens >= 1 {
		tb.Tokens--
		tb.LastToken = now
		return true
	} else {
		return false
	}
}

// LimitHandler 请求限制
func LimitHandler(maxConn int) gin.HandlerFunc {
	tb := &TokenBucket{
		Capacity:  int64(maxConn),
		Rate:      1.0,
		Tokens:    0,
		LastToken: time.Now(),
	}
	return func(c *gin.Context) {
		if !tb.Allow() {
			c.JSON(200, gin.H{
				"code": 0,
				"msg":  "Too many request",
				"data": "",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
