// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRobotStatHour = "x_tg_robot_stat_hour"

// XTgRobotStatHour 机器人小时统计
type XTgRobotStatHour struct {
	RecordTime     time.Time `gorm:"column:RecordTime;primaryKey;comment:时间(精确到小时)" json:"RecordTime"`                    // 时间(精确到小时)
	RobotID        int64     `gorm:"column:RobotId;primaryKey;comment:机器人Id" json:"RobotId"`                              // 机器人Id
	TgChatID       int64     `gorm:"column:TgChatId;primaryKey;comment: tg用户Id" json:"TgChatId"`                          //  tg用户Id
	SellerID       int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	ChannelID      int32     `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`                                      // 渠道id
	StartCount     int32     `gorm:"column:StartCount;not null;comment:Start次数" json:"StartCount"`                        // Start次数
	IsReg          int32     `gorm:"column:IsReg;not null;comment:是否注册" json:"IsReg"`                                     // 是否注册
	IsGetUSDT      int32     `gorm:"column:IsGetUSDT;not null;comment:是否领取U" json:"IsGetUSDT"`                            // 是否领取U
	IsGetTRX       int32     `gorm:"column:IsGetTRX;not null;comment:是否领取trx" json:"IsGetTRX"`                            // 是否领取trx
	IsActivation   int32     `gorm:"column:IsActivation;not null;comment:是否激活地址" json:"IsActivation"`                     // 是否激活地址
	IsInResourceDb int32     `gorm:"column:IsInResourceDb;comment:是否在库Start" json:"IsInResourceDb"`                       // 是否在库Start
	Memo           string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	CreateTime     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTgRobotStatHour's table name
func (*XTgRobotStatHour) TableName() string {
	return TableNameXTgRobotStatHour
}
