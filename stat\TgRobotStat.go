package stat

import (
	"context"
	"github.com/beego/beego/logs"
	"time"
	"xserver/gormgen/xHashStat/dao"
	"xserver/gormgen/xHashStat/model"
	"xserver/server"
)

type tgRobotStat struct{}

var TgRobotStat = new(tgRobotStat)

type ActionType int32

const (
	ActionType_Start            ActionType = iota + 1 // 未注册用户Start
	ActionType_Register                               // 注册成功
	ActionType_GetUSDT                                // 领取USDT
	ActionType_GetTRX                                 // 领取TRX
	ActionType_Activation                             // 激活地址
	ActionType_Login                                  // 登录
	ActionType_Recharge                               // 充值成功
	ActionType_Withdraw                               // 提款成功
	ActionType_ConcatCs                               // 联系客服
	ActionType_FirstchargeClick                       // 点击首存按钮
	ActionType_FailWithIP                             // 同IP领取体验金失败
	ActionType_FailWithDeviceId                       // 同设备ID领取体验金失败
	ActionType_FailWithAddress                        // 关联地址领取体验金失败
)

func (s *tgRobotStat) AddLogAction(ctx context.Context, sellerId int32, channelId int32, robotId int64, tgChatId int64, at ActionType, memo string, IsInResourceDb bool, actionTime time.Time) (err error) {
	err = server.DaoxHashStat().Transaction(func(tx *dao.Query) error {
		var isIn int32
		if IsInResourceDb {
			isIn = 1
		}
		err := tx.XTgRobotLogAction.WithContext(ctx).Create(&model.XTgRobotLogAction{
			RobotID:        robotId,
			TgChatID:       tgChatId,
			SellerID:       sellerId,
			ChannelID:      channelId,
			ActionType:     int32(at),
			Memo:           memo,
			IsInResourceDb: isIn,
		})
		if err != nil {
			return err
		}

		if actionTime.IsZero() {
			actionTime = time.Now()
		}
		if at == ActionType_Start || at == ActionType_Register || at == ActionType_GetUSDT || at == ActionType_GetTRX || at == ActionType_Activation {
			// 小时统计
			actionHour := time.Date(actionTime.Year(), actionTime.Month(), actionTime.Day(), actionTime.Hour(), 0, 0, 0, actionTime.Location())
			xStatH, err := tx.XTgRobotStatHour.WithContext(ctx).
				Where(tx.XTgRobotStatHour.RecordTime.Eq(actionHour)).
				Where(tx.XTgRobotStatHour.RobotID.Eq(robotId)).
				Where(tx.XTgRobotStatHour.TgChatID.Eq(tgChatId)).
				Where(tx.XTgRobotStatHour.SellerID.Eq(sellerId)).
				Where(tx.XTgRobotStatHour.ChannelID.Eq(channelId)).
				FirstOrCreate()
			if err != nil {
				return err
			}
			if at == ActionType_Start {
				xStatH.StartCount = 1
				xStatH.IsInResourceDb = isIn
			} else if at == ActionType_Register {
				xStatH.IsReg = 1
			} else if at == ActionType_GetUSDT {
				xStatH.IsGetUSDT = 1
			} else if at == ActionType_GetTRX {
				xStatH.IsGetTRX = 1
			} else if at == ActionType_Activation {
				xStatH.IsActivation = 1
			}
			_, err = tx.XTgRobotStatHour.WithContext(ctx).
				Where(tx.XTgRobotStatHour.RecordTime.Eq(actionHour)).
				Where(tx.XTgRobotStatHour.RobotID.Eq(robotId)).
				Where(tx.XTgRobotStatHour.TgChatID.Eq(tgChatId)).
				Where(tx.XTgRobotStatHour.SellerID.Eq(sellerId)).
				Where(tx.XTgRobotStatHour.ChannelID.Eq(channelId)).
				Updates(xStatH)
			if err != nil {
				return err
			}
		}

		var SellerName, ChannelName, RobotUsername string
		daoSeller := server.DaoxHashGame().XSeller
		xSeller, err := daoSeller.WithContext(ctx).Where(daoSeller.SellerID.Eq(sellerId)).First()
		if err == nil {
			SellerName = xSeller.SellerName
		}
		daoChannel := server.DaoxHashGame().XChannel
		xChannel, err := daoChannel.WithContext(ctx).Where(daoChannel.ChannelID.Eq(channelId)).First()
		if err == nil {
			ChannelName = xChannel.ChannelName
		}
		daoBot := server.DaoxHashGame().XTgRobotGuide
		xBot, err := daoBot.WithContext(ctx).Where(daoBot.ID.Eq(robotId)).First()
		if err == nil {
			RobotUsername = xBot.TgRobotUserName
		}

		// 日统计
		actionDay := time.Date(actionTime.Year(), actionTime.Month(), actionTime.Day(), 0, 0, 0, 0, actionTime.Location())
		xStatD, err := tx.XTgRobotStatDate.WithContext(ctx).
			Where(tx.XTgRobotStatDate.RecordDate.Eq(actionDay)).
			Where(tx.XTgRobotStatDate.RobotID.Eq(robotId)).
			Where(tx.XTgRobotStatDate.TgChatID.Eq(tgChatId)).
			Where(tx.XTgRobotStatDate.ActionType.Eq(int32(at))).
			Where(tx.XTgRobotStatDate.SellerID.Eq(sellerId)).
			Where(tx.XTgRobotStatDate.ChannelID.Eq(channelId)).
			FirstOrCreate()
		if err != nil {
			return err
		}
		xStatD.ActionCount += 1
		xStatD.IsInResourceDb = isIn
		xStatD.SellerName = SellerName
		xStatD.ChannelName = ChannelName
		xStatD.RobotUsername = RobotUsername
		_, err = tx.XTgRobotStatDate.WithContext(ctx).
			Where(tx.XTgRobotStatDate.RecordDate.Eq(actionDay)).
			Where(tx.XTgRobotStatDate.RobotID.Eq(robotId)).
			Where(tx.XTgRobotStatDate.TgChatID.Eq(tgChatId)).
			Where(tx.XTgRobotStatDate.ActionType.Eq(int32(at))).
			Where(tx.XTgRobotStatDate.SellerID.Eq(sellerId)).
			Where(tx.XTgRobotStatDate.ChannelID.Eq(channelId)).
			Updates(xStatD)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logs.Error("TG统计错误：", err)
	}
	return err
}
