// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveReward(db *gorm.DB, opts ...gen.DOOption) xActiveReward {
	_xActiveReward := xActiveReward{}

	_xActiveReward.xActiveRewardDo.UseDB(db, opts...)
	_xActiveReward.xActiveRewardDo.UseModel(&model.XActiveReward{})

	tableName := _xActiveReward.xActiveRewardDo.TableName()
	_xActiveReward.ALL = field.NewAsterisk(tableName)
	_xActiveReward.ID = field.NewInt32(tableName, "Id")
	_xActiveReward.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveReward.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xActiveReward.UserID = field.NewInt32(tableName, "UserId")
	_xActiveReward.State = field.NewInt32(tableName, "State")
	_xActiveReward.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveReward.ActiveName = field.NewString(tableName, "ActiveName")
	_xActiveReward.Amount = field.NewFloat64(tableName, "Amount")
	_xActiveReward.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xActiveReward.AuditTime = field.NewTime(tableName, "AuditTime")
	_xActiveReward.AuditMemo = field.NewString(tableName, "AuditMemo")
	_xActiveReward.CreateTime = field.NewTime(tableName, "CreateTime")
	_xActiveReward.TopAgentID = field.NewInt32(tableName, "TopAgentId")

	_xActiveReward.fillFieldMap()

	return _xActiveReward
}

// xActiveReward 活动发放表
type xActiveReward struct {
	xActiveRewardDo xActiveRewardDo

	ALL          field.Asterisk
	ID           field.Int32   // id
	SellerID     field.Int32   // 运营商
	ChannelID    field.Int32   // 渠道
	UserID       field.Int32   //  玩家Id
	State        field.Int32   // 状态 1待审核,2审核拒绝,3审核通过,4自动通过
	ActiveID     field.Int32   // 活动Id 为了兼容新活动 新活动对应的固定ID 10001能量补给站 10002充值任务 10003哈希闯关 10004棋牌闯关 10005电子闯关 10006救援金 10007邀请好友 10008VIP返水
	ActiveName   field.String  // 活动名称
	Amount       field.Float64 // 活动送金
	AuditAccount field.String  // 审核账号
	AuditTime    field.Time    // 审核时间
	AuditMemo    field.String  // 审核备注
	CreateTime   field.Time    // 申请时间
	TopAgentID   field.Int32   // 顶级代理Id

	fieldMap map[string]field.Expr
}

func (x xActiveReward) Table(newTableName string) *xActiveReward {
	x.xActiveRewardDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveReward) As(alias string) *xActiveReward {
	x.xActiveRewardDo.DO = *(x.xActiveRewardDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveReward) updateTableName(table string) *xActiveReward {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.State = field.NewInt32(table, "State")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.ActiveName = field.NewString(table, "ActiveName")
	x.Amount = field.NewFloat64(table, "Amount")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AuditTime = field.NewTime(table, "AuditTime")
	x.AuditMemo = field.NewString(table, "AuditMemo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")

	x.fillFieldMap()

	return x
}

func (x *xActiveReward) WithContext(ctx context.Context) *xActiveRewardDo {
	return x.xActiveRewardDo.WithContext(ctx)
}

func (x xActiveReward) TableName() string { return x.xActiveRewardDo.TableName() }

func (x xActiveReward) Alias() string { return x.xActiveRewardDo.Alias() }

func (x xActiveReward) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveRewardDo.Columns(cols...)
}

func (x *xActiveReward) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveReward) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["State"] = x.State
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["ActiveName"] = x.ActiveName
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AuditTime"] = x.AuditTime
	x.fieldMap["AuditMemo"] = x.AuditMemo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["TopAgentId"] = x.TopAgentID
}

func (x xActiveReward) clone(db *gorm.DB) xActiveReward {
	x.xActiveRewardDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveReward) replaceDB(db *gorm.DB) xActiveReward {
	x.xActiveRewardDo.ReplaceDB(db)
	return x
}

type xActiveRewardDo struct{ gen.DO }

func (x xActiveRewardDo) Debug() *xActiveRewardDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveRewardDo) WithContext(ctx context.Context) *xActiveRewardDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveRewardDo) ReadDB() *xActiveRewardDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveRewardDo) WriteDB() *xActiveRewardDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveRewardDo) Session(config *gorm.Session) *xActiveRewardDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveRewardDo) Clauses(conds ...clause.Expression) *xActiveRewardDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveRewardDo) Returning(value interface{}, columns ...string) *xActiveRewardDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveRewardDo) Not(conds ...gen.Condition) *xActiveRewardDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveRewardDo) Or(conds ...gen.Condition) *xActiveRewardDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveRewardDo) Select(conds ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveRewardDo) Where(conds ...gen.Condition) *xActiveRewardDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveRewardDo) Order(conds ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveRewardDo) Distinct(cols ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveRewardDo) Omit(cols ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveRewardDo) Join(table schema.Tabler, on ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveRewardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveRewardDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveRewardDo) Group(cols ...field.Expr) *xActiveRewardDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveRewardDo) Having(conds ...gen.Condition) *xActiveRewardDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveRewardDo) Limit(limit int) *xActiveRewardDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveRewardDo) Offset(offset int) *xActiveRewardDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveRewardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveRewardDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveRewardDo) Unscoped() *xActiveRewardDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveRewardDo) Create(values ...*model.XActiveReward) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveRewardDo) CreateInBatches(values []*model.XActiveReward, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveRewardDo) Save(values ...*model.XActiveReward) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveRewardDo) First() (*model.XActiveReward, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveReward), nil
	}
}

func (x xActiveRewardDo) Take() (*model.XActiveReward, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveReward), nil
	}
}

func (x xActiveRewardDo) Last() (*model.XActiveReward, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveReward), nil
	}
}

func (x xActiveRewardDo) Find() ([]*model.XActiveReward, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveReward), err
}

func (x xActiveRewardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveReward, err error) {
	buf := make([]*model.XActiveReward, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveRewardDo) FindInBatches(result *[]*model.XActiveReward, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveRewardDo) Attrs(attrs ...field.AssignExpr) *xActiveRewardDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveRewardDo) Assign(attrs ...field.AssignExpr) *xActiveRewardDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveRewardDo) Joins(fields ...field.RelationField) *xActiveRewardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveRewardDo) Preload(fields ...field.RelationField) *xActiveRewardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveRewardDo) FirstOrInit() (*model.XActiveReward, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveReward), nil
	}
}

func (x xActiveRewardDo) FirstOrCreate() (*model.XActiveReward, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveReward), nil
	}
}

func (x xActiveRewardDo) FindByPage(offset int, limit int) (result []*model.XActiveReward, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveRewardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveRewardDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveRewardDo) Delete(models ...*model.XActiveReward) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveRewardDo) withDO(do gen.Dao) *xActiveRewardDo {
	x.DO = *do.(*gen.DO)
	return x
}
