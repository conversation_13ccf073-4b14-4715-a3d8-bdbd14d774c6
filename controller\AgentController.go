package controller

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/spf13/viper"
)

type AgentController struct {
}

func (c *AgentController) Init() {
	gropu := server.Http().NewGroup("/api/agent")
	{
		gropu.Post("/info", c.info)
		gropu.Post("/info_detail", c.info_detail)
		gropu.Post("/set_save", c.set_save)
		gropu.Post("/get_commission", c.get_commission)
		gropu.Post("/commission_record", c.commission_record)
		gropu.Post("/limit_fencheng", c.limit_fencheng)
		gropu.Post("/get_codes", c.get_codes)
		gropu.Post("/modify_code", c.modify_code)
		gropu.Post("/get_fencheng_calcdata", c.get_fencheng_calcdata)
		gropu.Post("/agent_code_register", c.agent_code_register)
		gropu.Post("/agent_code_betcount", c.agent_code_betcount)
		gropu.Post("/dict_children", c.dict_children)
		gropu.Post("/new_code", c.new_code)

		// 香港六合彩玩法
		gropu.Post("/lottery_play", c.agent_game_lottery_play)
		// 香港六合彩98代理client设置分成
		gropu.Post("/set_xg_fencheng_t1", c.set_xg_fencheng_t1)
		// 香港六合彩98我的链接修改分成
		gropu.Post("/update_xg_code_t1", c.update_xg_code_t1)

		gropu.Post("/info_new", c.info_new)
		gropu.Post("/liusui_new", c.liusui_new)
		gropu.Post("/child_search", c.child_search)
		//gropu.Post("/define_new", c.define_new)
		gropu.PostByNoAuthMayUserToken("/define_new", c.define_new)
		gropu.Post("/get_commission_new", c.get_commission_new)
		gropu.Post("/commission_record_new", c.commission_record_new)

		gropu.Post("/info_t1", c.info_t1)
		gropu.Post("/get_commission_mine_t1", c.get_commission_mine_t1)
		gropu.Post("/get_code_t1", c.get_code_t1)
		gropu.Post("/create_code_t1", c.create_code_t1)
		gropu.Post("/delete_code_t1", c.delete_code_t1)
		gropu.Post("/update_code_t1", c.update_code_t1)
		gropu.Post("/dict_child_search_t1", c.dict_child_search_t1)
		gropu.Post("/child_search_t1", c.child_search_t1)
		gropu.Post("/set_fencheng_t1", c.set_fencheng_t1)
		gropu.Post("/get_commission_new_t1", c.get_commission_new_t1)
		gropu.Post("/commission_record_new_t1", c.commission_record_new_t1)
		gropu.Post("/set_agentnickname_t1", c.set_agentnickname_t1)

	}
}

func (c *AgentController) info(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	type ReturnData struct {
		AgentId              int     `gorm:"column:AgentId"`
		ChildCount           int     `gorm:"column:ChildCount"`
		DictChildCount       int     `gorm:"column:DictChildCount"`
		CanGetCommissionTrx  float32 `gorm:"column:CanGetCommissionTrx"`
		CanGetCommissionUsdt float32 `gorm:"column:CanGetCommissionUsdt"`

		NewChildCount       int     `gorm:"column:NewChildCount"`
		DictNewChildCount   int     `gorm:"column:DictNewChildCount"`
		LiuSuiTrx           float32 `gorm:"column:LiuSuiTrx"`
		LiuSuiUsdt          float32 `gorm:"column:LiuSuiUsdt"`
		DictLiuSuiTrx       float32 `gorm:"column:DictLiuSuiTrx"`
		DictLiuSuiUsdt      float32 `gorm:"column:DictLiuSuiUsdt"`
		TotalCommissionTrx  float32 `gorm:"column:TotalCommissionTrx"`
		TotalCommissionUsdt float32 `gorm:"column:TotalCommissionUsdt"`
		FenCheng            float64 `gorm:"column:FenCheng"`
		IsTopAgent          int     `gorm:"column:IsTopAgent"`
		IsAgent             int     `gorm:"column:IsAgent"`
	}
	token := server.GetToken(ctx)
	data := ReturnData{}
	{
		sql := `SELECT AgentId,IsTopAgent,FenCheng,IsAgent from  x_user where UserId = ?`
		dbresult, err := server.Db().Conn().Query(sql, token.UserId)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if dbresult.Next() {
			abugo.GetDbResult(dbresult, &data)
		}
		if data.IsTopAgent == 1 {
			data.FenCheng = 1
		}
		dbresult.Close()
	}
	{
		sql := `SELECT ChildCount,DictChildCount,CanGetCommissionTrx,CanGetCommissionUsdt from  x_agent where UserId = ?`
		dbresult, err := server.Db().Conn().Query(sql, token.UserId)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if dbresult.Next() {
			abugo.GetDbResult(dbresult, &data)
		}
		dbresult.Close()
	}
	{
		sql := `SELECT NewChildCount,DictNewChildCount,LiuSuiTrx,LiuSuiUsdt,DictLiuSuiTrx,DictLiuSuiUsdt,TotalCommissionTrx,TotalCommissionUsdt from  x_agent_dailly where UserId = ? and RecordDate = ?`
		today := time.Now().Format("2006-01-02")
		dbresult, err := server.Db().Conn().Query(sql, token.UserId, today)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if dbresult.Next() {
			abugo.GetDbResult(dbresult, &data)
		}
		dbresult.Close()
	}
	data.CanGetCommissionTrx = data.CanGetCommissionTrx - data.TotalCommissionTrx
	data.CanGetCommissionUsdt = data.CanGetCommissionUsdt - data.TotalCommissionUsdt
	if data.IsTopAgent == 1 || data.IsAgent != 1 {
		data.FenCheng = 1
	}
	ctx.RespOK(data)
}
func (c *AgentController) info_detail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		StartTime int64
		EndTime   int64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	type DetailReturnData struct {
		LiuSuiTrx           float64 `gorm:"column:LiuSuiTrx"`
		LiuSuiUsdt          float64 `gorm:"column:LiuSuiUsdt"`
		DictLiuSuiTrx       float64 `gorm:"column:DictLiuSuiTrx"`
		DictLiuSuiUsdt      float64 `gorm:"column:DictLiuSuiUsdt"`
		GetedCommissionTrx  float64 `gorm:"column:GetedCommissionTrx"`
		GetedCommissionUsdt float64 `gorm:"column:GetedCommissionUsdt"`
		TotalCommissionTrx  float64 `gorm:"column:CanGetCommissionTrx"`
		TotalCommissionUsdt float64 `gorm:"column:CanGetCommissionUsdt"`
	}
	token := server.GetToken(ctx)
	detail_data := DetailReturnData{}
	StartTime := ""
	EndTime := ""
	now := time.Now()
	if reqdata.StartTime == 0 {
		StartTime = now.Format("2006-01-02")
	} else {
		StartTime = abugo.TimeStampToLocalDate(reqdata.StartTime)
	}
	if reqdata.EndTime == 0 {
		m, _ := time.ParseDuration("24h")
		EndTime = now.Add(m).Format("2006-01-02")
	} else {
		reqdata.EndTime += 86400000
		EndTime = abugo.TimeStampToLocalDate(reqdata.EndTime)
	}
	{
		sql := `SELECT sum(LiuSuiTrx) as LiuSuiTrx,
		sum(LiuSuiUsdt) as	LiuSuiUsdt,
		sum(DictLiuSuiTrx) as	DictLiuSuiTrx,
		sum(DictLiuSuiUsdt) as	DictLiuSuiUsdt,
		sum(GetedCommissionTrx) as	GetedCommissionTrx,
		sum(GetedCommissionUsdt) as	GetedCommissionUsdt,
		sum(TotalCommissionTrx) as	TotalCommissionTrx,
		sum(TotalCommissionUsdt) as	TotalCommissionUsdt
		from  x_agent_dailly where UserId = ? and RecordDate >= ? and RecordDate < ?`
		dbresult, err := server.Db().Conn().Query(sql, token.UserId, StartTime, EndTime)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if dbresult.Next() {
			abugo.GetDbResult(dbresult, &detail_data)
		}
		dbresult.Close()
	}
	type DictReturnData struct {
		UserId         int     `gorm:"column:UserId"`
		FenCheng       float64 `gorm:"column:FenCheng"`
		LiuSuiTrx      float64 `gorm:"column:LiuSuiTrx"`
		LiuSuiUsdt     float64 `gorm:"column:LiuSuiUsdt"`
		SelfLiuSuiTrx  float64 `gorm:"column:DictLiuSuiTrx"`
		SelfLiuSuiUsdt float64 `gorm:"column:DictLiuSuiUsdt"`
	}
	dict_data := []DictReturnData{}
	{
		sql := `SELECT x_agent_child.Child AS UserId
				FROM x_agent_child
				RIGHT  JOIN x_agent ON x_agent.UserId = x_agent_child.Child
				WHERE x_agent_child.UserId = ? and ChildLevel = 0`
		dbresult, err := server.Db().Conn().Query(sql, token.UserId)
		if ctx.RespErr(err, &errcode) {
			return
		}
		for dbresult.Next() {
			d := DictReturnData{}
			abugo.GetDbResult(dbresult, &d)
			dict_data = append(dict_data, d)
		}
		dbresult.Close()
		for i := 0; i < len(dict_data); i++ {
			{
				sql := `select sum(LiuSuiTrx) as LiuSuiTrx,sum(LiuSuiUsdt) as LiuSuiUsdt,
				sum(SelfLiuSuiTrx) as SelfLiuSuiTrx,
				sum(SelfLiuSuiUsdt) as SelfLiuSuiUsdt
				 from x_agent_dailly where UserId = ? and RecordDate >= ? and RecordDate <?  `
				dbresult, err := server.Db().Conn().Query(sql, dict_data[i].UserId, StartTime, EndTime)
				if ctx.RespErr(err, &errcode) {
					return
				}
				if dbresult.Next() {
					abugo.GetDbResult(dbresult, &dict_data[i])
				}
				dbresult.Close()
			}
			{
				sql := `select FenCheng from x_user where UserId = ? `
				dbresult, err := server.Db().Conn().Query(sql, dict_data[i].UserId)
				if ctx.RespErr(err, &errcode) {
					return
				}
				if dbresult.Next() {
					abugo.GetDbResult(dbresult, &dict_data[i])
				}
				dbresult.Close()
			}
		}
	}
	ctx.Put("detail", detail_data)
	ctx.Put("dict", dict_data)
	ctx.RespOK()
}
func (c *AgentController) set_save(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		UserId   int `validate:"required"`
		FenCheng float64
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	MinFenCheng := server.GetConfigFloat(token.SellerId, 0, "MinFenCheng")
	if ctx.RespErrString(reqdata.FenCheng < MinFenCheng, &errcode, "分成比例不可小于最小限制") {
		return
	}
	MaxFenCheng := server.GetConfigFloat(token.SellerId, 0, "MaxFenCheng")
	if ctx.RespErrString(reqdata.FenCheng > MaxFenCheng, &errcode, "分成比例不可大于最大限制") {
		return
	}
	presult, err := server.Db().CallProcedure("x_admin_set_fencheng", reqdata.UserId, reqdata.FenCheng)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(*&presult) {
		return
	}
	ctx.RespOK()
}
func (c *AgentController) get_commission(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	date := abugo.GetLocalDate()
	date += " 00:30:00"

	loc, _ := time.LoadLocation("Local")
	tx, _ := time.ParseInLocation("2006-01-02 15:04:05", date, loc)
	if time.Now().Before(tx) {
		ctx.RespErrString(true, &errcode, "每日00:30:00以后才可领取佣金")
		return
	}

	token := server.GetToken(ctx)
	presult, err := server.Db().CallProcedure("x_admin_get_commission", token.UserId, reqdata.Symbol)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}
func (c *AgentController) commission_record(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	fields := "Id,UserId,SellerId,State,Amount,CreateTime,Symbol"
	_, presult := server.Db().Table("x_commission_audit").Select(fields).Where(where).OrderBy("id desc").PageData(1, 100000)
	if presult == nil {
		ctx.RespOK(map[string]interface{}{})
	}
	result := *presult
	for i := 0; i < len(result); i++ {
		result[i]["CreateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["CreateTime"]))
	}
	ctx.RespOK(result)
}
func (c *AgentController) limit_fencheng(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	data := map[string]interface{}{}
	data["Min"] = server.GetConfigFloat(token.SellerId, 0, "MinFenCheng")
	data["Max"] = server.GetConfigFloat(token.SellerId, 0, "MaxFenCheng")
	ctx.RespOK(data)
}
func (c *AgentController) new_code(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		FenCheng float64 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(reqdata.FenCheng < 0, &errcode, "参数不正确") {
		return
	}
	MinFenCheng := server.GetConfigFloat(token.SellerId, 0, "MinFenCheng")
	if ctx.RespErrString(reqdata.FenCheng < MinFenCheng, &errcode, "分成比例不可小于最小限制") {
		return
	}
	MaxFenCheng := server.GetConfigFloat(token.SellerId, 0, "MaxFenCheng")
	if ctx.RespErrString(reqdata.FenCheng > MaxFenCheng, &errcode, "分成比例不可大于最大限制") {
		return
	}
	presult, err := server.Db().CallProcedure("x_api_new_agent_code", token.UserId, reqdata.FenCheng)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	ctx.RespOK()
}
func (c *AgentController) get_codes(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	presult, err := server.Db().Table("x_agent_code").Where(where).OrderBy("id asc").GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK(*presult)
}
func (c *AgentController) modify_code(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		AgentCode string  `validate:"required"`
		FenCheng  float64 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(reqdata.FenCheng < 0, &errcode, "参数不正确") {
		return
	}
	MinFenCheng := server.GetConfigFloat(token.SellerId, 0, "MinFenCheng")
	if ctx.RespErrString(reqdata.FenCheng < MinFenCheng, &errcode, "分成比例不可小于最小限制") {
		return
	}
	MaxFenCheng := server.GetConfigFloat(token.SellerId, 0, "MaxFenCheng")
	if ctx.RespErrString(reqdata.FenCheng > MaxFenCheng, &errcode, "分成比例不可大于最大限制") {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	where.Add("and", "AgentCode", "=", reqdata.AgentCode, 0)
	server.Db().Table("x_agent_code").Where(where).Update(map[string]interface{}{
		"FenCheng": reqdata.FenCheng,
	})
	ctx.RespOK()
}
func (c *AgentController) get_fencheng_calcdata(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		UserId int
		Host   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, _ := server.GetChannel(ctx, reqdata.Host)
	gamewhere := abugo.AbuDbWhere{}
	gamewhere.Add("and", "State", "=", 1, 0)
	gamewhere.Add("and", "ChannelId", "=", ChannelId, 0)
	presult, err := server.Db().Table("x_game").Select("GameId,RoomLevel,FenChengRate").Where(gamewhere).GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}
	result := *presult
	token := server.GetToken(ctx)
	if reqdata.UserId == 0 {
		reqdata.UserId = token.UserId
	}
	agentswhere := abugo.AbuDbWhere{}
	agentswhere.Add("and", "UserId", "=", reqdata.UserId, 0)
	pagents, err := server.Db().Table("x_user").Select("Agents").Where(agentswhere).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(pagents == nil, &errcode, "读取玩家错误") {
		return
	}
	stragents := ""
	if (*pagents)["Agents"] != nil {
		stragents = abugo.GetStringFromInterface((*pagents)["Agents"])
	}
	if len(stragents) == 0 {
		stragents = "[]"
	}
	agents := []interface{}{}
	json.Unmarshal([]byte(stragents), &agents)
	var topagent int64
	if len(agents) > 0 {
		topagent = int64(abugo.GetFloat64FromInterface(agents[len(agents)-1]))
	} else {
		topagent = int64(reqdata.UserId)
	}
	if topagent > 0 {
		topagentwhere := abugo.AbuDbWhere{}
		topagentwhere.Add("and", "UserId", "=", topagent, 0)
		topagentwhere.Add("and", "State", "=", 1, 0)
		presult, err = server.Db().Table("x_game_agent_fencheng").Select("GameId,RoomLevel,FenChengRate").Where(topagentwhere).GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		for i := 0; i < len(*presult); i++ {
			for j := 0; j < len(result); j++ {
				if (*presult)[i]["GameId"] == result[j]["GameId"] && (*presult)[i]["RoomLevel"] == result[j]["RoomLevel"] {
					result[j]["FenChengRate"] = (*presult)[i]["FenChengRate"]
				}
			}
		}
	}
	ctx.Put("game", result)
	var fencheng []float64
	newagents := []int{}
	if len(agents) > 0 {
		for i := len(agents) - 1; i >= 0; i-- {
			newagents = append(newagents, int(abugo.GetFloat64FromInterface(agents[i])))
		}
	}
	newagents = append(newagents, reqdata.UserId)
	fencheng = append(fencheng, 1)
	for i := 1; i < len(newagents); i++ {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", newagents[i], 0)
		pfencheng, err := server.Db().Table("x_user").Select("FenCheng").Where(where).GetOne()
		if err == nil && pfencheng != nil {
			fencheng = append(fencheng, abugo.GetFloat64FromInterface((*pfencheng)["FenCheng"]))
		}
	}
	ctx.Put("fencheng", fencheng)
	ctx.RespOK()
}
func (c *AgentController) agent_code_register(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		AgentCode int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	sql := "select UserId,Address,RegisterTime from x_user where AgentCode = ? order by RegisterTime desc"
	presult, err := server.Db().Query(sql, []interface{}{reqdata.AgentCode})
	if ctx.RespErr(err, &errcode) {
		return
	}
	for k := range *presult {
		(*presult)[k]["RegisterTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface((*presult)[k]["RegisterTime"]))
	}
	ctx.RespOK(*presult)
}
func (c *AgentController) agent_code_betcount(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		AgentCode int `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	sql := "select UserId,Address,RegisterTime from x_user where AgentCode = ? and (BetTrx > 0 or BetUsdt > 0) order by RegisterTime desc"
	presult, err := server.Db().Query(sql, []interface{}{reqdata.AgentCode})
	if ctx.RespErr(err, &errcode) {
		return
	}
	for k := range *presult {
		(*presult)[k]["RegisterTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface((*presult)[k]["RegisterTime"]))
	}
	ctx.RespOK(*presult)
}

func (c *AgentController) dict_children(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	token := server.GetToken(ctx)
	sql := "select UserId,Address,RegisterTime,VerifyState,VerifyAmount,AgentCode from x_user where AgentId = ?"
	presult, err := server.Db().Query(sql, []interface{}{token.UserId})
	if ctx.RespErr(err, &errcode) {
		return
	}
	for k := range *presult {
		(*presult)[k]["RegisterTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface((*presult)[k]["RegisterTime"]))
	}
	ctx.RespOK(*presult)
}

func (c *AgentController) info_new(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	data, _ := server.Db().CallProcedure("x_api_agent_get_info", token.UserId, reqdata.Symbol)
	ctx.RespOK(data)
}

func (c *AgentController) liusui_new(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Days      int //0 今天,1昨天
		QueryType int //0所有,1直属,2代理
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	date := abugo.TimeStampToLocalDate(time.Now().Unix()*1000 - int64(reqdata.Days)*86400000)
	sel := `select UserId,NewLiuSuiTrx,
NewDictLiuSuiTrx,
NewLiuSui,
NewLiuSuiDict,
NewLiuSuiHaXi,
NewLiuSuiHaXiDict,
NewLiuSuiLottery,
NewLiuSuiLotteryDict,
NewLiuSuiQiPai,
NewLiuSuiQiPaiDict,
NewLiuSuiDianZhi,
NewLiuSuiDianZhiDict,
NewLiuSuiXiaoYouXi,
NewLiuSuiXiaoYouXiDict,
NewLiuSuiLive,
NewLiuSuiLiveDict,
NewLiuSuiSport,
NewLiuSuiSportDict,
NewLiuSuiTexas,
NewLiuSuiTexasDict,
NewLiuSuiLowLottery,
NewLiuSuiLowLotteryDict,
NewLiuSuiCryptoMarket,
NewLiuSuiCryptoMarketDict,
NewLiuSuiLiuHeLottery,
NewLiuSuiLiuHeLotteryDict,
NewLiuSuiHaXiRoulette,
NewLiuSuiHaXiRouletteDict,
NewLiuSuiHaXiRouletteTrx,
NewLiuSuiHaXiRouletteDictTrx from x_agent_dailly`
	sel2 := `select UserId,NewSelfLiuSuiTrx,
NewSelfLiuSuiHaXi,
NewSelfLiuSuiLottery,
NewSelfLiuSuiQiPai,
NewSelfLiuSuiDianZhi,
NewSelfLiuSuiXiaoYouXi,
NewSelfLiuSuiLive,
NewSelfLiuSuiSport,
NewSelfLiuSuiTexas,
NewSelfLiuSuiLowLottery,
NewSelfLiuSuiCryptoMarket,
NewSelfLiuSuiLiuHeLottery,
NewSelfLiuSuiHaXiRoulette,
NewSelfLiuSuiHaXiRouletteTrx from x_agent_dailly`
	if reqdata.QueryType == 0 {
		sql := sel + ` where UserId = ? and RecordDate = ?`
		data, _ := server.Db().Query(sql, []interface{}{token.UserId, date})
		if len(*data) == 0 {
			*data = append(*data, gin.H{"UserId": token.UserId, "NewLiuSuiTrx": 0,
				"NewDictLiuSuiTrx":             0,
				"NewLiuSui":                    0,
				"NewLiuSuiDict":                0,
				"NewLiuSuiHaXi":                0,
				"NewLiuSuiHaXiDict":            0,
				"NewLiuSuiLottery":             0,
				"NewLiuSuiLotteryDict":         0,
				"NewLiuSuiQiPai":               0,
				"NewLiuSuiQiPaiDict":           0,
				"NewLiuSuiDianZhi":             0,
				"NewLiuSuiDianZhiDict":         0,
				"NewLiuSuiXiaoYouXi":           0,
				"NewLiuSuiXiaoYouXiDict":       0,
				"NewLiuSuiLive":                0,
				"NewLiuSuiLiveDict":            0,
				"NewLiuSuiSport":               0,
				"NewLiuSuiSportDict":           0,
				"NewLiuSuiTexas":               0,
				"NewLiuSuiTexasDict":           0,
				"NewLiuSuiLowLottery":          0,
				"NewLiuSuiLowLotteryDict":      0,
				"NewLiuSuiCryptoMarket":        0,
				"NewLiuSuiCryptoMarketDict":    0,
				"NewLiuSuiLiuHeLottery":        0,
				"NewLiuSuiLiuHeLotteryDict":    0,
				"NewLiuSuiHaXiRoulette":        0,
				"NewLiuSuiHaXiRouletteDict":    0,
				"NewLiuSuiHaXiRouletteTrx":     0,
				"NewLiuSuiHaXiRouletteDictTrx": 0,
			})
		}
		ctx.RespOK((*data)[0])
	} else if reqdata.QueryType == 1 {
		sql := sel2 + ` where UserId in(SELECT UserId FROM x_user WHERE AgentId = ?) and RecordDate = ?`
		data, _ := server.Db().Query(sql, []interface{}{token.UserId, date})
		ctx.RespOK(data)
	} else if reqdata.QueryType == 2 {
		sql := sel2 + ` where UserId in(SELECT Child FROM x_agent_child WHERE UserId = ? and ChildLevel > 0) and RecordDate = ?`
		data, _ := server.Db().Query(sql, []interface{}{token.UserId, date})
		ctx.RespOK(data)
	}
}

func (c *AgentController) child_search(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		QueryType int //1我的玩家,2代理管家,3下级查询
		ChildId   int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if reqdata.QueryType == 1 {
		sel := `UserId,NewLiuSuiTrx,
NewDictLiuSuiTrx,
NewLiuSui,
NewLiuSuiDict`
		sql := fmt.Sprintf("SELECT %s FROM x_agent_dailly WHERE UserId IN(SELECT UserId FROM x_user WHERE AgentId = ?) AND RecordDate = DATE_FORMAT(NOW(),'%%Y-%%m-%%d')", sel)
		data, _ := server.Db().Query(sql, []interface{}{token.UserId})
		users, _ := server.Db().Query("SELECT UserId FROM x_user WHERE AgentId = ?", []interface{}{token.UserId})
		var usermap map[int]int = make(map[int]int)
		for i := 0; i < len(*data); i++ {
			uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
			usermap[uid] = 1
		}
		for i := 0; i < len(*users); i++ {
			uid := int(abugo.GetInt64FromInterface((*users)[i]["UserId"]))
			_, ok := usermap[uid]
			if !ok {
				*data = append(*data, gin.H{"UserId": uid, "NewLiuSuiTrx": 0, "NewDictLiuSuiTrx": 0, "NewLiuSui": 0, "NewLiuSuiDict": 0})
			}
		}
		if reqdata.ChildId != 0 {
			for i := 0; i < len(*data); i++ {
				uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
				if uid == reqdata.ChildId {
					ctx.RespOK([]interface{}{(*data)[i]})
					return
				}
			}
			ctx.RespOK([]interface{}{})
		} else {
			ctx.RespOK(data)
		}
	} else if reqdata.QueryType == 2 {
		sel := `UserId,NewLiuSuiTrx,
NewDictLiuSuiTrx,
NewLiuSui,
NewLiuSuiDict`
		sql := fmt.Sprintf("SELECT %s FROM x_agent_dailly WHERE UserId IN(SELECT Child FROM x_agent_child WHERE UserId = ? and ChildLevel > 0) AND RecordDate = DATE_FORMAT(NOW(),'%%Y-%%m-%%d')", sel)
		data, _ := server.Db().Query(sql, []interface{}{token.UserId})
		users, _ := server.Db().Query("SELECT Child FROM x_agent_child WHERE UserId = ? and ChildLevel > 0", []interface{}{token.UserId})
		var usermap map[int]int = make(map[int]int)
		for i := 0; i < len(*data); i++ {
			uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
			usermap[uid] = 1
		}
		for i := 0; i < len(*users); i++ {
			uid := int(abugo.GetInt64FromInterface((*users)[i]["Child"]))
			_, ok := usermap[uid]
			if !ok {
				*data = append(*data, gin.H{"UserId": uid, "NewLiuSuiTrx": 0, "NewDictLiuSuiTrx": 0, "NewLiuSui": 0, "NewLiuSuiDict": 0})
			}
		}
		if reqdata.ChildId != 0 {
			for i := 0; i < len(*data); i++ {
				uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
				if uid == reqdata.ChildId {
					ctx.RespOK([]interface{}{(*data)[i]})
					return
				}
			}
			ctx.RespOK([]interface{}{})
		} else {
			ctx.RespOK(data)
		}
	} else if reqdata.QueryType == 3 {
		sel := `UserId,ChildCount`
		sql := fmt.Sprintf("SELECT %s FROM x_agent WHERE UserId IN(SELECT Child FROM x_agent_child WHERE UserId = ? )", sel)
		data, _ := server.Db().Query(sql, []interface{}{token.UserId})
		users, _ := server.Db().Query("SELECT Child FROM x_agent_child WHERE UserId = ?", []interface{}{token.UserId})
		var usermap map[int]int = make(map[int]int)
		for i := 0; i < len(*data); i++ {
			uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
			usermap[uid] = 1
		}
		for i := 0; i < len(*users); i++ {
			uid := int(abugo.GetInt64FromInterface((*users)[i]["Child"]))
			_, ok := usermap[uid]
			if !ok {
				*data = append(*data, gin.H{"UserId": uid, "ChildCount": 0})
			}
		}
		if reqdata.ChildId != 0 {
			for i := 0; i < len(*data); i++ {
				uid := int(abugo.GetInt64FromInterface((*data)[i]["UserId"]))
				if uid == reqdata.ChildId {
					ctx.RespOK([]interface{}{(*data)[i]})
					return
				}
			}
			ctx.RespOK([]interface{}{})
		} else {
			ctx.RespOK(data)
		}
	}
}

//func (c *AgentController) define_new(ctx *abugo.AbuHttpContent) {
//	token := server.GetToken(ctx)
//	if token.ChannelId == 0 {
//		where := abugo.AbuDbWhere{}
//		where.Add("and", "UserId", "=", token.UserId, 0)
//		userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
//		if userdata != nil {
//			token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
//		}
//	}
//	where := abugo.AbuDbWhere{}
//	where.Add("and", "ChannelId", "=", token.ChannelId, "")
//	data, _ := server.Db().Table("x_agent_define").Where(where).OrderBy("AgentLevel asc").GetList()
//	ctx.RespOK(data)
//}

func (c *AgentController) define_new(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	type GameDefine struct {
		CatId  int64
		LiuSui float64
		Reward float64
	}
	type XgGameDefineArr []GameDefine
	if token != nil {
		if token.ChannelId == 0 {
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, 0)
			userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
			if userdata != nil {
				token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
			}
		}
		where := abugo.AbuDbWhere{}
		where.Add("and", "ChannelId", "=", token.ChannelId, "")
		data, _ := server.Db().Table("x_agent_define").Where(where).OrderBy("AgentLevel asc").GetList()
		// 香港六合彩返佣配置
		where.Add("and", "SellerId", "=", token.SellerId, "")
		rows, _ := server.Db().Table("x_agent_game_define").Where(where).OrderBy("AgentLevel asc").GetList()
		for i := 0; i < len(*data); i++ {
			gameDefineArr := XgGameDefineArr{}
			for j := 0; j < len(*rows); j++ {
				gameDefine := (*rows)[j]
				dataAgentLevel := (*data)[i]["AgentLevel"].(int64)
				if dataAgentLevel == gameDefine["AgentLevel"].(int64) {
					var tempGameDefine GameDefine
					tempGameDefine.CatId = gameDefine["CatId"].(int64)
					tempGameDefine.LiuSui = gameDefine["LiuSui"].(float64)
					tempGameDefine.Reward = gameDefine["Reward"].(float64)
					gameDefineArr = append(gameDefineArr, tempGameDefine)
				}
			}
			(*data)[i]["XgGameDefineArr"] = gameDefineArr
		}
		ctx.RespOK(data)
	} else {
		errcode := 0
		reqdata := struct {
			SellerId int `validate:"required"`
			Host     string
		}{}
		err := ctx.RequestData(&reqdata)
		if ctx.RespErr(err, &errcode) {
			return
		}

		ChannelId, _ := server.GetChannel(ctx, reqdata.Host)
		where := abugo.AbuDbWhere{}
		where.Add("and", "ChannelId", "=", ChannelId, "")
		data, _ := server.Db().Table("x_agent_define").Where(where).OrderBy("AgentLevel asc").GetList()
		// 香港六合彩返佣配置
		where.Add("and", "SellerId", "=", reqdata.SellerId, "")
		rows, _ := server.Db().Table("x_agent_game_define").Where(where).OrderBy("AgentLevel asc").GetList()
		for i := 0; i < len(*data); i++ {
			gameDefineArr := XgGameDefineArr{}
			for j := 0; j < len(*rows); j++ {
				gameDefine := (*rows)[j]
				dataAgentLevel := (*data)[i]["AgentLevel"].(int64)
				if dataAgentLevel == gameDefine["AgentLevel"].(int64) {
					var tempGameDefine GameDefine
					tempGameDefine.CatId = gameDefine["CatId"].(int64)
					tempGameDefine.LiuSui = gameDefine["LiuSui"].(float64)
					tempGameDefine.Reward = gameDefine["Reward"].(float64)
					gameDefineArr = append(gameDefineArr, tempGameDefine)
				}
			}
			(*data)[i]["XgGameDefineArr"] = gameDefineArr
		}
		ctx.RespOK(data)
	}
}

func (c *AgentController) get_commission_new(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	date := abugo.GetLocalDate()
	date += " 02:00:00"

	loc, _ := time.LoadLocation("Local")
	tx, _ := time.ParseInLocation("2006-01-02 15:04:05", date, loc)
	if time.Now().Before(tx) {
		ctx.RespErrString(true, &errcode, "每日02:00:00以后才可领取佣金")
		return
	}
	token := server.GetToken(ctx)
	presult, err := server.Db().CallProcedure("x_admin_get_commission_new", token.UserId, reqdata.Symbol)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	if (*presult)["Id"] != nil {
		env := "测试服,"
		if !server.Debug() {
			env = "正式服,"
		}
		msg := fmt.Sprintf(`%v新的佣金领取订单,请立即审核
编号: %v
金额: %v
时间: %v`, env, (*presult)["Id"], (*presult)["Amount"], (*presult)["NowTime"])
		req.Post(viper.GetString("tgbotapi")+"/sendmsg", msg)
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}

func (c *AgentController) commission_record_new(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	fields := "Id,Symbol,UserId,State,Amount,CreateTime,Symbol"
	_, presult := server.Db().Table("x_commission_audit").Select(fields).Where(where).OrderBy("id desc").PageData(1, 100000)
	if presult == nil {
		ctx.RespOK(map[string]interface{}{})
	}
	result := *presult
	for i := 0; i < len(result); i++ {
		result[i]["CreateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["CreateTime"]))
	}
	ctx.RespOK(result)
}

// 获取香港彩玩法
func (c *AgentController) agent_game_lottery_play(ctx *abugo.AbuHttpContent) {
	defer recover()
	type LotteryPlay struct {
		Id    int64
		PName string
	}
	type ResponseData struct {
		lotteryPlay []LotteryPlay
	}
	responseData := ResponseData{}
	query := `select Id, PName from x_liuhecai_map GROUP BY id, PName`
	params := make([]interface{}, 0)
	rows, err := server.Db().Query(query, params)
	if err != nil {
		panic(err)
	}
	//var respData ResponseData
	for i := 0; i < len(*rows); i++ {
		play := (*rows)[i]
		var lotteryPlay LotteryPlay
		idStr := fmt.Sprintf("%v", play["Id"])
		lotteryPlay.Id, err = strconv.ParseInt(idStr, 10, 64)
		lotteryPlay.PName = fmt.Sprintf("%v", play["PName"])
		responseData.lotteryPlay = append(responseData.lotteryPlay, lotteryPlay)
		//server.Db().GormDao().Table("x_liuhecai_map").Select("Id, PName").Group("Id")
	}
	ctx.Put("", responseData.lotteryPlay)
	ctx.RespOK()
}
