// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXCustomThird(db *gorm.DB, opts ...gen.DOOption) xCustomThird {
	_xCustomThird := xCustomThird{}

	_xCustomThird.xCustomThirdDo.UseDB(db, opts...)
	_xCustomThird.xCustomThirdDo.UseModel(&model.XCustomThird{})

	tableName := _xCustomThird.xCustomThirdDo.TableName()
	_xCustomThird.ALL = field.NewAsterisk(tableName)
	_xCustomThird.ID = field.NewInt32(tableName, "Id")
	_xCustomThird.RecordDate = field.NewTime(tableName, "RecordDate")
	_xCustomThird.UserID = field.NewInt32(tableName, "UserId")
	_xCustomThird.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xCustomThird.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xCustomThird.SellerID = field.NewInt32(tableName, "SellerId")
	_xCustomThird.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xCustomThird.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xCustomThird.ThirdType = field.NewInt32(tableName, "ThirdType")
	_xCustomThird.Brand = field.NewString(tableName, "Brand")
	_xCustomThird.Symbol = field.NewString(tableName, "Symbol")
	_xCustomThird.BetCount = field.NewInt32(tableName, "BetCount")
	_xCustomThird.WinCount = field.NewInt32(tableName, "WinCount")
	_xCustomThird.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xCustomThird.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xCustomThird.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xCustomThird.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")
	_xCustomThird.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xCustomThird.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xCustomThird.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xCustomThird.Fee = field.NewFloat64(tableName, "Fee")
	_xCustomThird.IsFirst = field.NewInt32(tableName, "IsFirst")
	_xCustomThird.CSGroup = field.NewString(tableName, "CSGroup")
	_xCustomThird.GameName = field.NewString(tableName, "GameName")
	_xCustomThird.GameID = field.NewString(tableName, "GameId")

	_xCustomThird.fillFieldMap()

	return _xCustomThird
}

type xCustomThird struct {
	xCustomThirdDo xCustomThirdDo

	ALL          field.Asterisk
	ID           field.Int32 // 自增id
	RecordDate   field.Time  // 记录时间
	UserID       field.Int32 // 玩家id
	SpecialAgent field.Int32 // 玩家来源 1独立代理,2公司官网
	TopAgentID   field.Int32 // 顶级id
	SellerID     field.Int32 // 运营商
	ChannelID    field.Int32 // 渠道
	BetChannelID field.Int32 // 下注渠道Id
	/*
		三方类型
		1 lottery
		2 dianzhi
		3 qipai
		4 xiaoyouxi
		5 live
		6 sport

	*/
	ThirdType      field.Int32
	Brand          field.String  // 三方品牌
	Symbol         field.String  // 币种
	BetCount       field.Int32   // 投注次数
	WinCount       field.Int32   // 赢次数
	BetAmount      field.Float64 // 投注金额
	BonusBetAmount field.Float64 // Bonus投注金额
	WinAmount      field.Float64 // 返奖金额
	BonusWinAmount field.Float64 // Bonus返奖金额
	LiuSui         field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	Fee            field.Float64 // 手续费
	IsFirst        field.Int32   // 是否首次参与 1是,2否
	CSGroup        field.String
	GameName       field.String
	GameID         field.String // 游戏Id

	fieldMap map[string]field.Expr
}

func (x xCustomThird) Table(newTableName string) *xCustomThird {
	x.xCustomThirdDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xCustomThird) As(alias string) *xCustomThird {
	x.xCustomThirdDo.DO = *(x.xCustomThirdDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xCustomThird) updateTableName(table string) *xCustomThird {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.UserID = field.NewInt32(table, "UserId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.ThirdType = field.NewInt32(table, "ThirdType")
	x.Brand = field.NewString(table, "Brand")
	x.Symbol = field.NewString(table, "Symbol")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.WinCount = field.NewInt32(table, "WinCount")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.Fee = field.NewFloat64(table, "Fee")
	x.IsFirst = field.NewInt32(table, "IsFirst")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.GameName = field.NewString(table, "GameName")
	x.GameID = field.NewString(table, "GameId")

	x.fillFieldMap()

	return x
}

func (x *xCustomThird) WithContext(ctx context.Context) *xCustomThirdDo {
	return x.xCustomThirdDo.WithContext(ctx)
}

func (x xCustomThird) TableName() string { return x.xCustomThirdDo.TableName() }

func (x xCustomThird) Alias() string { return x.xCustomThirdDo.Alias() }

func (x xCustomThird) Columns(cols ...field.Expr) gen.Columns {
	return x.xCustomThirdDo.Columns(cols...)
}

func (x *xCustomThird) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xCustomThird) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 25)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["ThirdType"] = x.ThirdType
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["WinCount"] = x.WinCount
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["IsFirst"] = x.IsFirst
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["GameId"] = x.GameID
}

func (x xCustomThird) clone(db *gorm.DB) xCustomThird {
	x.xCustomThirdDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xCustomThird) replaceDB(db *gorm.DB) xCustomThird {
	x.xCustomThirdDo.ReplaceDB(db)
	return x
}

type xCustomThirdDo struct{ gen.DO }

func (x xCustomThirdDo) Debug() *xCustomThirdDo {
	return x.withDO(x.DO.Debug())
}

func (x xCustomThirdDo) WithContext(ctx context.Context) *xCustomThirdDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xCustomThirdDo) ReadDB() *xCustomThirdDo {
	return x.Clauses(dbresolver.Read)
}

func (x xCustomThirdDo) WriteDB() *xCustomThirdDo {
	return x.Clauses(dbresolver.Write)
}

func (x xCustomThirdDo) Session(config *gorm.Session) *xCustomThirdDo {
	return x.withDO(x.DO.Session(config))
}

func (x xCustomThirdDo) Clauses(conds ...clause.Expression) *xCustomThirdDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xCustomThirdDo) Returning(value interface{}, columns ...string) *xCustomThirdDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xCustomThirdDo) Not(conds ...gen.Condition) *xCustomThirdDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xCustomThirdDo) Or(conds ...gen.Condition) *xCustomThirdDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xCustomThirdDo) Select(conds ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xCustomThirdDo) Where(conds ...gen.Condition) *xCustomThirdDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xCustomThirdDo) Order(conds ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xCustomThirdDo) Distinct(cols ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xCustomThirdDo) Omit(cols ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xCustomThirdDo) Join(table schema.Tabler, on ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xCustomThirdDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xCustomThirdDo) RightJoin(table schema.Tabler, on ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xCustomThirdDo) Group(cols ...field.Expr) *xCustomThirdDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xCustomThirdDo) Having(conds ...gen.Condition) *xCustomThirdDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xCustomThirdDo) Limit(limit int) *xCustomThirdDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xCustomThirdDo) Offset(offset int) *xCustomThirdDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xCustomThirdDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xCustomThirdDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xCustomThirdDo) Unscoped() *xCustomThirdDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xCustomThirdDo) Create(values ...*model.XCustomThird) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xCustomThirdDo) CreateInBatches(values []*model.XCustomThird, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xCustomThirdDo) Save(values ...*model.XCustomThird) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xCustomThirdDo) First() (*model.XCustomThird, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomThird), nil
	}
}

func (x xCustomThirdDo) Take() (*model.XCustomThird, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomThird), nil
	}
}

func (x xCustomThirdDo) Last() (*model.XCustomThird, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomThird), nil
	}
}

func (x xCustomThirdDo) Find() ([]*model.XCustomThird, error) {
	result, err := x.DO.Find()
	return result.([]*model.XCustomThird), err
}

func (x xCustomThirdDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XCustomThird, err error) {
	buf := make([]*model.XCustomThird, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xCustomThirdDo) FindInBatches(result *[]*model.XCustomThird, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xCustomThirdDo) Attrs(attrs ...field.AssignExpr) *xCustomThirdDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xCustomThirdDo) Assign(attrs ...field.AssignExpr) *xCustomThirdDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xCustomThirdDo) Joins(fields ...field.RelationField) *xCustomThirdDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xCustomThirdDo) Preload(fields ...field.RelationField) *xCustomThirdDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xCustomThirdDo) FirstOrInit() (*model.XCustomThird, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomThird), nil
	}
}

func (x xCustomThirdDo) FirstOrCreate() (*model.XCustomThird, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCustomThird), nil
	}
}

func (x xCustomThirdDo) FindByPage(offset int, limit int) (result []*model.XCustomThird, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xCustomThirdDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xCustomThirdDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xCustomThirdDo) Delete(models ...*model.XCustomThird) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xCustomThirdDo) withDO(do gen.Dao) *xCustomThirdDo {
	x.DO = *do.(*gen.DO)
	return x
}
