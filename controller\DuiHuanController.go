package controller

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"xserver/abugo"
	"xserver/server"
)

type DuiHuanController struct {
}

func (c *DuiHuanController) Init() {
	group := server.Http().NewGroup("/api/duihuan")
	{
		group.PostNoAuth("/info", c.info)
		group.PostNoAuth("/calc", c.calc)
	}
}

func (c *DuiHuanController) info(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	data := map[string]interface{}{}
	_, SellerId := server.GetChannel(ctx, "")
	rediskey := fmt.Sprintf("%s:%s:duihuan:%d", server.Project(), server.Module(), SellerId)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		json.Unmarshal(redisdata.([]byte), &data)
		ctx.RespOK(data)
	} else {
		data["Address"] = server.GetConfigString(SellerId, 0, "DuiHuanAddress")
		data["ExchangeRateFixed"] = server.GetConfigString(SellerId, 0, "ExchangeRateFixed")
		data["TrxPrice"] = server.GetConfigString(SellerId, 0, "TrxPrice")
		data["DuiHuanMinTrx"] = server.GetConfigString(SellerId, 0, "DuiHuanMinTrx")
		data["DuiHuanMaxTrx"] = server.GetConfigString(SellerId, 0, "DuiHuanMaxTrx")
		data["DuiHuanMinUsdt"] = server.GetConfigString(SellerId, 0, "DuiHuanMinUsdt")
		data["DuiHuanMaxUsdt"] = server.GetConfigString(SellerId, 0, "DuiHuanMaxUsdt")
		ctx.RespOK(data)
		server.Redis().SetEx(rediskey, 10, data)
	}
}

func (c *DuiHuanController) calc(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int     `validate:"required"` //运营商
		DType    int     `validate:"required"` //兑换类型 // 1 trx->usdt  2 usdt->trx
		Amount   float64 `validate:"required"` //兑换数量
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, "")
	data := map[string]interface{}{}
	rediskey := fmt.Sprintf("%s:%s:duihuan:%d", server.Project(), server.Module(), SellerId)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		json.Unmarshal(redisdata.([]byte), &data)
	} else {
		data["Address"] = server.GetConfigString(SellerId, 0, "DuiHuanAddress")
		data["ExchangeRateFixed"] = server.GetConfigString(SellerId, 0, "ExchangeRateFixed")
		data["TrxPrice"] = server.GetConfigString(SellerId, 0, "TrxPrice")
		data["DuiHuanMinTrx"] = server.GetConfigString(SellerId, 0, "DuiHuanMinTrx")
		data["DuiHuanMaxTrx"] = server.GetConfigString(SellerId, 0, "DuiHuanMaxTrx")
		data["DuiHuanMinUsdt"] = server.GetConfigString(SellerId, 0, "DuiHuanMinUsdt")
		data["DuiHuanMaxUsdt"] = server.GetConfigString(SellerId, 0, "DuiHuanMaxUsdt")
		server.Redis().SetEx(rediskey, 10, data)
	}
	DuiHuanMinTrx, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["DuiHuanMinTrx"]), 64)
	DuiHuanMaxTrx, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["DuiHuanMaxTrx"]), 64)
	DuiHuanMinUsdt, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["DuiHuanMinUsdt"]), 64)
	DuiHuanMaxUsdt, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["DuiHuanMaxUsdt"]), 64)
	ExchangeRateFixed, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["ExchangeRateFixed"]), 64)
	TrxPrice, _ := strconv.ParseFloat(abugo.GetStringFromInterface(data["TrxPrice"]), 64)
	if ctx.RespErrString(reqdata.DType == 1 && reqdata.Amount < DuiHuanMinTrx, &errcode, "金额小于最小兑换数量") {
		return
	}
	if ctx.RespErrString(reqdata.DType == 1 && reqdata.Amount > DuiHuanMaxTrx, &errcode, "金额大于最大兑换数量") {
		return
	}
	if ctx.RespErrString(reqdata.DType == 2 && reqdata.Amount < DuiHuanMinUsdt, &errcode, "金额小于最小兑换数量") {
		return
	}
	if ctx.RespErrString(reqdata.DType == 2 && reqdata.Amount > DuiHuanMaxUsdt, &errcode, "金额大于最大兑换数量") {
		return
	}
	RealPrice := TrxPrice
	BackAmount := 0.0
	if reqdata.DType == 1 {
		RealPrice -= (RealPrice * ExchangeRateFixed)
		BackAmount = RealPrice * reqdata.Amount
	}
	if reqdata.DType == 2 {
		RealPrice += (RealPrice * ExchangeRateFixed)
		BackAmount = reqdata.Amount / RealPrice
	}
	BackAmount = math.Floor(BackAmount*10000000) / 10000000
	ctx.RespOK(BackAmount)
}
