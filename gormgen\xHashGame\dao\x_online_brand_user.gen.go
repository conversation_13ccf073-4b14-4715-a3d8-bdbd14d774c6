// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOnlineBrandUser(db *gorm.DB, opts ...gen.DOOption) xOnlineBrandUser {
	_xOnlineBrandUser := xOnlineBrandUser{}

	_xOnlineBrandUser.xOnlineBrandUserDo.UseDB(db, opts...)
	_xOnlineBrandUser.xOnlineBrandUserDo.UseModel(&model.XOnlineBrandUser{})

	tableName := _xOnlineBrandUser.xOnlineBrandUserDo.TableName()
	_xOnlineBrandUser.ALL = field.NewAsterisk(tableName)
	_xOnlineBrandUser.ID = field.NewInt32(tableName, "Id")
	_xOnlineBrandUser.Online = field.NewInt32(tableName, "Online")
	_xOnlineBrandUser.Brand = field.NewString(tableName, "Brand")
	_xOnlineBrandUser.CreateTime = field.NewTime(tableName, "CreateTime")

	_xOnlineBrandUser.fillFieldMap()

	return _xOnlineBrandUser
}

type xOnlineBrandUser struct {
	xOnlineBrandUserDo xOnlineBrandUserDo

	ALL        field.Asterisk
	ID         field.Int32
	Online     field.Int32  // 在线人数
	Brand      field.String // 厂商名称
	CreateTime field.Time   // 记录时间

	fieldMap map[string]field.Expr
}

func (x xOnlineBrandUser) Table(newTableName string) *xOnlineBrandUser {
	x.xOnlineBrandUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOnlineBrandUser) As(alias string) *xOnlineBrandUser {
	x.xOnlineBrandUserDo.DO = *(x.xOnlineBrandUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOnlineBrandUser) updateTableName(table string) *xOnlineBrandUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Online = field.NewInt32(table, "Online")
	x.Brand = field.NewString(table, "Brand")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xOnlineBrandUser) WithContext(ctx context.Context) *xOnlineBrandUserDo {
	return x.xOnlineBrandUserDo.WithContext(ctx)
}

func (x xOnlineBrandUser) TableName() string { return x.xOnlineBrandUserDo.TableName() }

func (x xOnlineBrandUser) Alias() string { return x.xOnlineBrandUserDo.Alias() }

func (x xOnlineBrandUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xOnlineBrandUserDo.Columns(cols...)
}

func (x *xOnlineBrandUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOnlineBrandUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 4)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Online"] = x.Online
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xOnlineBrandUser) clone(db *gorm.DB) xOnlineBrandUser {
	x.xOnlineBrandUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOnlineBrandUser) replaceDB(db *gorm.DB) xOnlineBrandUser {
	x.xOnlineBrandUserDo.ReplaceDB(db)
	return x
}

type xOnlineBrandUserDo struct{ gen.DO }

func (x xOnlineBrandUserDo) Debug() *xOnlineBrandUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xOnlineBrandUserDo) WithContext(ctx context.Context) *xOnlineBrandUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOnlineBrandUserDo) ReadDB() *xOnlineBrandUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOnlineBrandUserDo) WriteDB() *xOnlineBrandUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOnlineBrandUserDo) Session(config *gorm.Session) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOnlineBrandUserDo) Clauses(conds ...clause.Expression) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOnlineBrandUserDo) Returning(value interface{}, columns ...string) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOnlineBrandUserDo) Not(conds ...gen.Condition) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOnlineBrandUserDo) Or(conds ...gen.Condition) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOnlineBrandUserDo) Select(conds ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOnlineBrandUserDo) Where(conds ...gen.Condition) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOnlineBrandUserDo) Order(conds ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOnlineBrandUserDo) Distinct(cols ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOnlineBrandUserDo) Omit(cols ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOnlineBrandUserDo) Join(table schema.Tabler, on ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOnlineBrandUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOnlineBrandUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOnlineBrandUserDo) Group(cols ...field.Expr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOnlineBrandUserDo) Having(conds ...gen.Condition) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOnlineBrandUserDo) Limit(limit int) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOnlineBrandUserDo) Offset(offset int) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOnlineBrandUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOnlineBrandUserDo) Unscoped() *xOnlineBrandUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOnlineBrandUserDo) Create(values ...*model.XOnlineBrandUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOnlineBrandUserDo) CreateInBatches(values []*model.XOnlineBrandUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOnlineBrandUserDo) Save(values ...*model.XOnlineBrandUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOnlineBrandUserDo) First() (*model.XOnlineBrandUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineBrandUser), nil
	}
}

func (x xOnlineBrandUserDo) Take() (*model.XOnlineBrandUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineBrandUser), nil
	}
}

func (x xOnlineBrandUserDo) Last() (*model.XOnlineBrandUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineBrandUser), nil
	}
}

func (x xOnlineBrandUserDo) Find() ([]*model.XOnlineBrandUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOnlineBrandUser), err
}

func (x xOnlineBrandUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOnlineBrandUser, err error) {
	buf := make([]*model.XOnlineBrandUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOnlineBrandUserDo) FindInBatches(result *[]*model.XOnlineBrandUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOnlineBrandUserDo) Attrs(attrs ...field.AssignExpr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOnlineBrandUserDo) Assign(attrs ...field.AssignExpr) *xOnlineBrandUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOnlineBrandUserDo) Joins(fields ...field.RelationField) *xOnlineBrandUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOnlineBrandUserDo) Preload(fields ...field.RelationField) *xOnlineBrandUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOnlineBrandUserDo) FirstOrInit() (*model.XOnlineBrandUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineBrandUser), nil
	}
}

func (x xOnlineBrandUserDo) FirstOrCreate() (*model.XOnlineBrandUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineBrandUser), nil
	}
}

func (x xOnlineBrandUserDo) FindByPage(offset int, limit int) (result []*model.XOnlineBrandUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOnlineBrandUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOnlineBrandUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOnlineBrandUserDo) Delete(models ...*model.XOnlineBrandUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOnlineBrandUserDo) withDO(do gen.Dao) *xOnlineBrandUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
