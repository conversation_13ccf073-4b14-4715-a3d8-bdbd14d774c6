package paycontroller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

// Pay24 是24pay支付服务的实例
var Pay24 = new(pay24)

// pay24 结构体定义
type pay24 struct {
	Base
}

// Init 初始化函数
// 初始化服务器的HTTP接口，注册充值和提现回调接口
func (c *pay24) Init() {
	// 注册充值回调接口
	server.Http().PostNoAuth("/api/wxrecharge", c.rechargeCallback)
	// 注册提现回调接口
	server.Http().PostNoAuth("/api/wxwithward", c.withdrawCallback)
}

// httpPost 发送HTTP POST请求到24pay接口
// @param cfg 配置信息
// @param path API路径
// @param data 请求数据
// @return 响应数据和错误信息
func (c *pay24) httpPost(cfg map[string]interface{}, path string, data xgo.H) (xgo.H, error) {
	url := cfg["url"].(string) + path
	bytes, _ := json.Marshal(data)
	header := req.Header{
		"Content-Type":  "application/json;charset=UTF-8",
		"Authorization": cfg["id"].(string),
		"Sign":          xgo.Md5(string(bytes) + cfg["id"].(string) + cfg["key"].(string)),
	}

	// 发送请求
	resp, err := req.Post(url, header, strings.NewReader(string(bytes)))
	if err != nil {
		logs.Error("pay24 httpPost:", err)
		return nil, err
	}
	defer resp.Response().Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("pay24 httpPost:", err)
		return nil, err
	}

	// 记录请求和响应日志
	databytes, _ := json.Marshal(data)
	logs.Debug("pay24 httpPost:", url, "|", string(databytes), "|", string(body))

	// 解析响应JSON
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("pay24 httpPost:", err)
		return nil, err
	}

	// 检查响应状态码
	code := xgo.ToInt(jdata["Code"])
	if code != 0 {
		return nil, errors.New(jdata["Msg"].(string))
	}

	return jdata, nil
}

// withdrawCallback 处理提现回调
func (c *pay24) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 获取请求头和请求体
	headers := ctx.Gin().Request.Header
	body, _ := io.ReadAll(ctx.Gin().Request.Body)

	// 获取订单号并处理测试前缀
	orderid := headers.Get("Merchantorderno")
	orderid = strings.Replace(orderid, "TEST", "", -1)

	// 转换订单号为整数
	orderidFormat, err := strconv.ParseInt(orderid, 10, 64)
	if err != nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 订单号无效:", orderid, err)
		ctx.Gin().String(500, "订单号无效")
		return
	}

	// 查询提现订单
	dao := server.DaoxHashGame().XWithdraw
	db := dao.WithContext(context.TODO())
	orderdata, err := db.Where(dao.ID.Eq(int32(orderidFormat))).First()

	// 处理订单查询错误
	if err != nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 查询订单错误:", orderid, err)
		ctx.Gin().String(500, "订单号无效")
		return
	}

	// 检查订单是否存在
	if orderdata == nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 订单不存在:", orderid)
		ctx.Gin().String(500, "订单不存在")
		return
	}

	// 获取支付方式信息
	PayId := orderdata.PayID
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", PayId).First()
	if err != nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 获取支付方式失败:", PayId, err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 检查支付方式是否存在
	if paymethod == nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 支付方式不存在:", PayId)
		ctx.Gin().String(500, "支付方式不存在")
		return
	}

	// 解析支付配置
	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)

	// 验证签名
	strbody := string(body)
	s := strbody + headers.Get("Merchantno") + jcfg["key"].(string)
	sign := xgo.Md5(s)

	if sign != headers.Get("Sign") {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 签名错误:", sign, headers.Get("Sign"))
		ctx.Gin().String(500, "签名错误")
		return
	}

	// 检查订单状态
	if orderdata.State != 5 {
		logs.Error("pay24 提现回调 订单已处理:", orderid)
		ctx.Gin().String(200, "success")
		return
	}

	// 解析请求体JSON
	jbody := map[string]interface{}{}
	err = json.Unmarshal(body, &jbody)
	if err != nil {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 JSON解析错误:", err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 验证订单号
	if jbody["MerchantOrderNo"] != orderid {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 订单号不匹配:", jbody["MerchantOrderNo"], orderid)
		ctx.Gin().String(500, "订单号错误")
		return
	}

	// 验证金额
	MerchantPayAmount := xgo.ToFloat(jbody["MerchantPayAmount"])
	if MerchantPayAmount-orderdata.RealAmount > 0.0001 {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
		logs.Error("pay24 提现回调 金额不匹配:", MerchantPayAmount, orderdata.RealAmount)
		ctx.Gin().String(500, "金额错误")
		return
	}

	// 处理提现结果
	Status := xgo.ToInt(jbody["Status"])
	if Status == 1 {
		c.withdrawCallbackHandel(int(orderidFormat), 6) // 成功
	} else {
		c.withdrawCallbackHandel(int(orderidFormat), 7) // 失败
	}

	ctx.Gin().String(200, "success")
}

// rechargeCallback 处理充值回调
func (c *pay24) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 获取请求头信息
	headers := ctx.Gin().Request.Header
	logs.Info("pay24 充值回调 headers:", headers)

	// 获取订单号
	orderid := headers.Get("Merchantorderno")

	// 查询订单信息
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", orderid, "")
	orderdata, err := server.Db().Table("x_recharge").Where(where).GetOne()

	// 处理订单查询错误
	if err != nil {
		logs.Error("pay24 充值回调 查询订单错误:", err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 检查订单是否存在
	if orderdata == nil {
		logs.Error("pay24 充值回调 订单不存在:", orderid)
		ctx.Gin().String(500, "订单不存在")
		return
	}

	// 检查订单是否已处理
	if xgo.ToInt((*orderdata)["State"]) == 5 {
		logs.Info("pay24 充值回调 订单已处理:", orderid)
		ctx.Gin().String(200, "success")
		return
	}

	// 从订单数据中获取用户ID
	userID := int(xgo.ToInt((*orderdata)["UserId"]))
	user, err := c.getUser(userID)
	if err != nil {
		logs.Error("pay24 充值回调 获取用户信息失败:", err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 获取支付方式信息
	PayId := int(xgo.ToInt((*orderdata)["PayId"]))
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", PayId).First()
	if err != nil {
		logs.Error("pay24 充值回调 获取支付方式失败:", err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 检查支付方式是否存在
	if paymethod == nil {
		logs.Error("pay24 充值回调 支付方式不存在:", PayId)
		ctx.Gin().String(500, "支付方式不存在")
		return
	}

	// 解析支付配置
	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)

	// 读取请求体
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	strbody := string(body)

	// 验证签名
	s := strbody + headers.Get("Merchantno") + jcfg["key"].(string)
	sign := xgo.Md5(s)
	logs.Info("pay24 充值回调 签名数据:", s)
	logs.Info("pay24 充值回调 计算签名:", sign, "请求签名:", headers.Get("Sign"))

	if sign != headers.Get("Sign") {
		logs.Error("pay24 充值回调 签名错误")
		ctx.Gin().String(500, "签名错误")
		return
	}

	// 解析请求体JSON
	jbody := map[string]interface{}{}
	err = json.Unmarshal(body, &jbody)
	if err != nil {
		logs.Error("pay24 充值回调 JSON解析错误:", err)
		ctx.Gin().String(500, err.Error())
		return
	}

	// 验证订单号
	if jbody["MerchantOrderNo"] != orderid {
		logs.Error("pay24 充值回调 订单号不匹配:", jbody["MerchantOrderNo"], orderid)
		ctx.Gin().String(500, "订单号错误")
		return
	}

	// 验证金额
	MerchantPayAmount := xgo.ToFloat(jbody["MerchantPayAmount"])
	payorderAmount := (*orderdata)["Amount"].(float64)
	if MerchantPayAmount-payorderAmount > 0.0001 {
		logs.Error("pay24 充值回调 金额不匹配:", MerchantPayAmount, payorderAmount)
		ctx.Gin().String(500, "金额错误")
		return
	}
	c.rechargeCallbackHandel(int(user.UserID), int(xgo.ToInt(orderid)), 5)
	ctx.Gin().String(200, "success")
}

// Recharge 充值接口
// 处理充值请求，创建订单，调用支付接口
func (c *pay24) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0 // 初始化错误码

	// 获取支付方式
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode) // 支付方式不存在，返回错误
		return
	}

	// 获取支付方式配置
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)

	// 获取用户信息
	token := server.GetToken(ctx)
	userData, err := server.XDb().Table("x_user").Where("UserId = ?", token.UserId).First()
	if err != nil || userData == nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode) // 用户不存在，返回错误
		return
	}

	// 获取汇率
	rate, err := c.getWithdrawRate(req.Symbol)
	if err != nil {
		ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
		return
	}

	// 创建充值订单
	c.createRechargeOrder(ctx, cfg, rate, userData, specialAgent, token, req, payMethodData)
}

// createRechargeOrder 创建充值订单
// 处理充值请求的具体实现，创建订单并调用支付接口
func (c *pay24) createRechargeOrder(ctx *abugo.AbuHttpContent, jcfg map[string]interface{}, rate float64,
	userdata *xgo.XMap, SpecialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap) {

	server.XDb().Transaction(func(tx *xgo.XTx) error {
		errcode := 0
		symbol := strings.ToLower(reqdata.Symbol)
		paytype := 2
		if symbol != "brl" {
			paytype = 7
		}
		// 添加运营商26的特殊处理
		var amount float64
		currentRate := rate
		if token.SellerId == 26 {
			currentRate = 0
			amount = reqdata.Amount // 不进行汇率转换
		} else {
			amount = reqdata.Amount / rate
		}
		token := server.GetToken(ctx) // 获取用户信息
		user, err := c.getUser(token.UserId)
		if err != nil {
			ctx.RespErr(errors.New("获取用户信息失败"), &errcode)
			return errors.New("获取用户信息失败")
		}
		OrderId, err := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":     user.SellerID,
			"ChannelId":    user.ChannelID,
			"UserId":       user.UserID,
			"Symbol":       reqdata.Symbol,
			"PayId":        reqdata.MethodId,
			"PayType":      paytype,
			"Amount":       reqdata.Amount,
			"RealAmount":   amount,
			"TransferRate": currentRate,
			"State":        3,
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": SpecialAgent,
			"TopAgentId":   user.TopAgentID,
			"OrderType":    reqdata.OrderType,
		})

		if err != nil {
			ctx.RespErr(errors.New("创建订单失败"), &errcode)
			return errors.New("创建订单失败")
		}

		param := xgo.H{
			"MerchantNotifyUrl": jcfg["cburl"].(string) + "/api/wxrecharge",
			"MerchantOrderNo":   OrderId,
			"MerchantPayAmount": reqdata.Amount,
			"MerchantReturnUrl": jcfg["cburl"],
		}

		if symbol == "brl" {
			// 巴西
			param["MerchantPayType"] = "PIX"
			param["MerchantUid"] = fmt.Sprint(token.UserId)
			param["MerchantUIP"] = ctx.GetIp()
		} else if symbol == "hkd" {
			// 香港
			if reqdata.PayType != "FPS" && reqdata.PayType != "FPS_EN" && reqdata.PayType != "FPS_QR" && reqdata.PayType != "HK_BANK" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}
			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName
		} else if symbol == "php" {
			// 菲律宾
			if reqdata.PayType != "GCASH_DIRECT" && reqdata.PayType != "GCASH_QR" && reqdata.PayType != "MAYA_DIRECT" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}
			param["MerchantPayType"] = reqdata.PayType
		} else if symbol == "sgd" {
			// 新加坡
			if reqdata.BankCode == "" {
				ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
				return errors.New("银行代码不能为空")
			}
			param["MerchantPayType"] = reqdata.PayType
			param["BankCode"] = reqdata.BankCode
		} else if symbol == "thb" {
			// 泰国
			if reqdata.PayType != "TRUE_MONEY" && reqdata.PayType != "BANK_QR" && reqdata.PayType != "THBCT" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}

			if reqdata.BankNo == "" {
				ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
				return errors.New("银行卡号不能为空")
			}

			if reqdata.PayType == "BANK_QR" {
				param["BankCardNo"] = reqdata.BankNo
				param["BankCode"] = reqdata.BankCode
				param["BankCardHolder"] = reqdata.RealName
			}

			param["MerchantPayType"] = reqdata.PayType
		} else if symbol == "jpy" {
			// 日本
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}
			if reqdata.BankName == "" {
				ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
				return errors.New("银行名称不能为空")
			}
			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName
			param["BankName"] = reqdata.BankName
		} else if symbol == "krw" {
			// 韩国
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}

			if reqdata.BankName == "" {
				ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
				return errors.New("银行名称不能为空")
			}

			if reqdata.BankCode == "" {
				ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
				return errors.New("银行代码不能为空")
			}

			if reqdata.BankNo == "" {
				ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
				return errors.New("银行卡号不能为空")
			}
			param["MerchantPayType"] = reqdata.PayType
			param["BankCardHolder"] = reqdata.RealName
			param["BankName"] = reqdata.BankName
			param["BankCode"] = reqdata.BankCode
			param["BankCardNo"] = reqdata.BankNo
			param["IDNumber"] = user.UserID
		} else if symbol == "myr" {
			// 马来
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}
			if reqdata.PayType != "MYBCT" && reqdata.PayType != "MYDuitNow" && reqdata.PayType != "PayPin" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}
			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName
		} else if symbol == "idr" {
			// 印尼   MANDIRI PERMATA BNI BRI QRIS
			if reqdata.PayType != "MANDIRI" && reqdata.PayType != "PERMATA" && reqdata.PayType != "QRIS" && reqdata.PayType != "BNI" && reqdata.PayType != "BRI" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}
			param["MerchantPayType"] = reqdata.PayType // 支付类型
			param["Name"] = reqdata.RealName           // 姓名
			param["PhoneNumber"] = reqdata.PhoneNum    // 手机号
		} else if symbol == "inr" {
			// 印度
			if reqdata.PayType != "INUPI" && reqdata.PayType != "INBCT" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}

			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = rand.Intn(100000)
		} else if symbol == "mxn" {
			// 墨西哥
			if reqdata.PayType != "SPEI" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}

			param["MerchantPayType"] = reqdata.PayType
			param["MerchantUid"] = rand.Intn(10000)
			param["MerchantUIP"] = "127.0.0.1"
			param["Remark"] = "goods"
		} else if symbol == "khr" {
			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName
		} else if symbol == "rub" {
			// 俄罗斯支付
			if reqdata.PayType != "RUBBCT" && reqdata.PayType != "RUBQR" && reqdata.PayType != "RUBSBER" {
				ctx.RespErr(errors.New("支付类型不正确"), &errcode)
				return errors.New("支付类型不正确")
			}

			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return errors.New("姓名不能为空")
			}

			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName

			// 如果是银行卡支付，需要提供银行卡信息
			if reqdata.PayType == "RUBBCT" {
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return errors.New("银行卡号不能为空")
				}

				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return errors.New("银行名称不能为空")
				}

				param["BankCardNo"] = reqdata.BankNo
				param["BankName"] = reqdata.BankName
			}

			// 添加用户IP和用户ID
			param["MerchantUid"] = fmt.Sprint(token.UserId)
			param["MerchantUIP"] = ctx.GetIp()
		} else if symbol == "vnd" {
			param["MerchantPayType"] = reqdata.PayType
			param["Name"] = reqdata.RealName
		} else if symbol == "bdt" {
			param["MerchantPayType"] = reqdata.PayType
			param["WalletCode"] = reqdata.BankCode
		} else {
			param["MerchantPayType"] = reqdata.PayType
		}

		data, err := c.httpPost(jcfg, "/api/pay/v3/create", param)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return err
		}

		data = data["Data"].(map[string]interface{})
		data["PayId"] = reqdata.MethodId
		data["Brand"] = paymethod.String("Brand")
		data["Name"] = paymethod.String("Name")
		bytes, _ := json.Marshal(data)
		tx.Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(bytes),
			"ThirdId": data["PlatformOrderNo"],
		})
		ctx.RespOK(xgo.H{
			"payurl":  data["MobilePayUrl"],
			"orderId": OrderId,
		})
		return nil
	})
}
