package single

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"

	//"math"
	"net/http"
	//"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	//daogormclause "gorm.io/gorm/clause"
)

// DG真人单一钱包类
// DG真人接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

type DGSingleService struct {
	apiDomain             string            // api基础接口
	agent                 string            // 代理账号
	apiKey                string            //
	currency              string            //币种
	winLimit              float64           //盈利金额限制(<1表示不限制)
	brandName             string            //厂商标识
	games                 map[string]string //游戏类型
	gameTableTypes        map[int]string    //游戏表类型
	RefreshUserAmountFunc func(int) error   // 余额更新回调
}

func NewDGSingleService(params map[string]string, fc func(int) error) *DGSingleService {

	//游戏类型
	games := map[string]string{
		"dgh_live":        "DGH 真人",
		"baccarat":        "百家乐",
		"dragon_tiger":    "龙虎",
		"roulette":        "轮盘",
		"sic_bo":          "骰子游戏",
		"slot":            "老虎机",
		"sport":           "体育",
		"fishing":         "捕鱼",
		"bull_bull":       "牛牛",
		"win_three_cards": "炸金花",
		"gof_baccarat":    "财神百家乐",
		"gof_roulette":    "财神轮盘",
		"gof_sic_bo":      "财神骰宝",
		"fantan":          "番摊",
		"lucky_wheel":     "彩虹幸运轮",
		"mini_game":       "迷你游戏",
		"color_game":      "色彩游戏",
		"one_lucky_9":     "唯一幸运9",
		"hi_lo":           "hi lo",
		"dropBall":        "DropBall",
	}

	//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
	gameTableTypes := map[int]string{
		1: "x_third_dianzi",
		2: "x_third_qipai",
		3: "x_third_quwei",
		4: "x_third_lottery",
		5: "x_third_live",
		6: "x_third_sport",
	}

	winLimit, err := strconv.ParseFloat(params["winLimit"], 64)
	if err != nil {
		// 如果转换失败，输出错误信息
		fmt.Println("转换失败:", err)
	}

	return &DGSingleService{
		apiDomain:             params["api_domain"], //API地址
		agent:                 params["agent"],
		apiKey:                params["apiKey"],
		currency:              params["currency"],
		winLimit:              winLimit,
		brandName:             "dg",
		games:                 games,
		gameTableTypes:        gameTableTypes,
		RefreshUserAmountFunc: fc,
	}
}

const cacheKeyDG = "cacheKeyDG:"

// DG返回错误码
const (
	DG_Code_Success                 = 0   // 成功
	DG_Code_Fail_Not_Enough_Balance = 120 // 	    余额不足
	DG_Code_Fail_Illegal_Parameter  = 1   // 	参数错误
	DG_Code_Fail_Operation_Failed   = 98  // 	    操作失败
	DG_Code_Fail_User_Not_Exist     = 114 // 	    会员不存在
	DG_Code_Fail_Channel_Not_Exist  = 301 //渠道不存在
	DG_Code_Fail_Signature_Error    = 2   // 	    签名验证失败
	DG_Code_Fail_System_Error       = 500 //系统异常
)

// DGSupportLang 支持的语言
// 客户端语言(cn:中文简体,tw:中文繁体en:英文th:泰语ko:韩语vi:越南语id:印尼语pt:葡萄牙语es:西班牙语)

func (l *DGSingleService) Login(ctx *abugo.AbuHttpContent) {

	// Limit 表示用户的限红区间，包含最小值和最大值
	type Limit struct {
		Min int `json:"min"` // 最小限额
		Max int `json:"max"` // 最大限额
	}

	// LoginP 表示登录请求的参数
	type RequestLoginParamsData struct {
		UserName     string  `json:"username"`     // 会员账号 (例如: "user123")
		Password     string  `json:"password"`     // 会员密码 (MD5 后的 32 位字符串)
		CurrencyName string  `json:"currencyName"` // 币种名称 (例如: "USD")
		WinLimit     float64 `json:"winLimit"`     // 盈利金额限制 (<1 表示不限制)
		Area         string  `json:"area"`         // 登录地区 (例如: "US")
		Domains      string  `json:"domains"`      // SDK 接入标识 (1 表示 SDK 接入)
		Language     string  `json:"language"`     // 客户端语言 (例如: "en" 为英语)
		LimitGroup   string  `json:"limitGroup"`   // 限红组 (文档请联系提供方获取)
		Limits       []Limit `json:"limits"`       // 限红区间 (最多支持 8 组)
	}

	// ResponseData 表示接口返回的响应结构
	type ResponseLoginData struct {
		CodeId     int      `json:"codeId"`     // 错误码
		Msg        string   `json:"msg"`        // 错误信息
		Token      string   `json:"token"`      // 会员进入游戏 token
		Domains    string   `json:"domains"`    // SDK 接入需要的端数据
		List       []string `json:"list"`       // 各端游戏地址 [PC, WAP, SDK]
		LimitGroup string   `json:"limitGroup"` // 限红组，文档请联系我方获取
		Limits     []Limit  `json:"limits"`     // 限红区间
	}

	type RequestData struct {
		Language    string `json:"language"`    // 语言
		RedirectUrl string `json:"redirectUrl"` // 返回商户地址
		GameId      string `json:"gameId"`
		Platform    int    `json:"platform"` // 用户设备类型，表示用户访问的设备类型，可以是 0- Desktop、1- Mobile、2-SDK 等，选填
	}
	errcode := 0
	reqdata := RequestData{
		Platform: 1,
	}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("DG_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("DG_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyDG, userId); err != nil {
		logs.Error("DG_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			//ctx.RespErrString(true, &errcode, "游戏code不存在!")
			//return
		}
		logs.Error("DG_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试2")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("DG_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("DG_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试4")
		return
	}

	loginLang := reqdata.Language
	loginHomeUrl := reqdata.RedirectUrl
	loginName := strconv.Itoa(userId)
	//loginIp:=ctx.GetIp()

	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	// 创建特殊用户ID集合
	specialUserIds := map[int]bool{
		********: true,
		********: true,
	}

	// 设置默认限额
	minLimit := 5
	maxLimit := 5000

	// 检查是否为特殊用户ID
	if specialUserIds[userId] {
		minLimit = 1
		maxLimit = 20000
	}

	reqParams := RequestLoginParamsData{
		UserName:     loginName,
		Password:     Md5Encrypt(loginName),
		CurrencyName: l.currency,
		WinLimit:     l.winLimit,
		//LimitGroup: "B",
		//B
		Language: loginLang,
		Limits: []Limit{
			{
				Min: minLimit,
				Max: maxLimit,
			},
		},
	}

	reqdataLoginBytes, _ := json.Marshal(reqParams)
	payload := bytes.NewReader(reqdataLoginBytes)
	logs.Info("DG_single 登录游戏 开始 userId=", userId, " reqdata=", string(reqdataLoginBytes))
	url := fmt.Sprintf("%s/v2/wallet/login", l.apiDomain)
	client := &http.Client{}

	signature := Md5Encrypt(l.agent + l.apiKey + timestamp)
	logs.Info(url)
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	req.Header.Add("agent", l.agent)
	req.Header.Add("sign", signature)
	req.Header.Add("time", timestamp)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("DG_single 登录游戏 请求错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "网络错误,请稍后再试6")
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("DG_single 登录游戏 读取响应错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}
	logs.Info("DG_single 登录游戏 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respData := ResponseLoginData{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		logs.Error("DG_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试8")
		return
	}

	if respData.CodeId != 0 {
		logs.Error("DG_single 登录游戏 登录失败 userId=", userId, " 错误码=", respData.CodeId, " Message=", respData.Msg)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试9")
		return
	}

	ctx.RespOK(respData.List[reqdata.Platform] + "&showapp=off" + "&tableId=" + reqdata.GameId + "&backUrl=" + loginHomeUrl)
}

func (l *DGSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	// Member 结构体对应 "member" 对象
	type Member struct {
		UserName string  `json:"username"`
		Balance  float64 `json:"balance"`
	}

	type RequestData struct {
		Token  string `json:"token"` //md5(agent+apiKey)
		Member Member `json:"member"`
	}

	type ResponseData struct {
		CodeId int    `json:"codeId"` //md5(agent+apiKey)
		Member Member `json:"member"`
	}
	respdata := ResponseData{
		CodeId: DG_Code_Success,
	}
	// 获取路径参数 :agentName
	agentName := ctx.Gin().Param("agentName")
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("DG_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	logs.Info("DG_single 获取玩家余额 Request.Body=", string(bodyBytes), "agentName=", agentName)

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("DG_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	if agentName != l.agent {
		logs.Error("DG_single 获取玩家余额 非法的渠道Id l.agent=", l.agent, " reqdata.agentName=", agentName)
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	signKey := agentName + l.apiKey
	callbackSign := Md5Encrypt(signKey)
	if !strings.EqualFold(reqdata.Token, callbackSign) {
		logs.Error("DG_single 下注确认 签名错误 reqdata.Signature=", reqdata.Token, " getCallbackSign=", callbackSign)
		respdata.CodeId = DG_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.Member.UserName)
	if err != nil {
		logs.Error("DG_single 下注确认 会员账号错误 reqdata.Member.UserName=", reqdata.Member.UserName, " err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("DG_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.CodeId = DG_Code_Fail_User_Not_Exist
		} else {
			respdata.CodeId = DG_Code_Fail_User_Not_Exist
		}
		ctx.RespJson(respdata)
		return
	}
	// 给Data赋值
	respdata.Member.UserName = strconv.Itoa(userBalance.UserId)
	respdata.Member.Balance = userBalance.Amount
	ctx.RespJson(respdata)
	logs.Info("DG_single 获取玩家余额 响应成功 respdata=", respdata)
}

type DGBetDetail struct {
	Tie  int `json:"tie"` // Tie bet amount
	Info struct {
		Table int `json:"table"` // Table number for the info field
	} `json:"info"`
	Player int `json:"player"` // Player bet amount
}

type DGDetail struct {
	Id           int64   `json:"id"`           // 注单号
	TableId      int     `json:"tableId"`      // 表号
	ShoeId       int     `json:"shoeId"`       // 鞋号
	PlayId       int     `json:"playId"`       // 游戏局号
	LobbyId      int     `json:"lobbyId"`      // 大厅号
	GameType     int     `json:"gameType"`     // 游戏类型
	GameId       int     `json:"gameId"`       // 游戏ID
	BetTime      string  `json:"betTime"`      // 下注时间
	BetPoints    float64 `json:"betPoints"`    // 下注金额
	BetDetail    string  `json:"betDetail"`    // 下注详细信息
	IP           string  `json:"ip"`           // 用户IP地址
	Ext          string  `json:"ext"`          // 扩展信息
	IsRevocation int     `json:"isRevocation"` // 是否撤销
	CurrencyId   int     `json:"currencyId"`   // 币种ID
	DeviceType   int     `json:"deviceType"`   // 设备类型

	WinOrLoss    float64 `json:"winOrLoss"`    // 派彩
	AvailableBet float64 `json:"availableBet"` // 有效投注

}

type WalletTransferMemberParam struct {
	UserName string  `json:"username"` // 会员账号([a-zA-Z0-9@#_]{3,40}), 格式要求为字母数字和特定符号，长度在3到40之间
	Amount   float64 `json:"amount"`   // 转账金额(>0表示转入DG，<0表示转出DG)
	Balance  float64 `json:"balance"`  // 账户余额, 表示当前用户账户中的余额，类型为double
}

type DGRequestData struct {
	Token        string                    `json:"token"`    // md5(agent + apiKey)
	Data         string                    `json:"data"`     // 转账流水号(唯一，勿重复处理)
	TicketId     int64                     `json:"ticketId"` // 注单号, 一局有多次扣款记录
	Type         int32                     `json:"type"`     // 转账类型(1:下注 2:派彩 3:补单 5:红包 6:小费)
	DetailString string                    `json:"detail"`
	Detail       DGDetail                  `json:"detail2"` // 注单内容(transfers仅派彩时有，为本单所有有效的扣款记录)
	Member       WalletTransferMemberParam `json:"member"`  // 账户信息
}

// 如果是请求过checkTransfer或者inform接口的流水号，直接拒接处理返回失败
func (l *DGSingleService) Transfer(ctx *abugo.AbuHttpContent) {

	type ResponseData struct {
		CodeId int32                     `json:"codeId"` // 错误码(参考文档定义), 错误的类型，使用整数表示
		Member WalletTransferMemberParam `json:"member"` // 会员信息，包含用户名、余额和转账金额
	}
	respdata := ResponseData{
		CodeId: DG_Code_Success,
	}

	// 获取路径参数 :agentName
	agentName := ctx.Gin().Param("agentName")

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("DG_single Transfer 读取请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	logs.Info("DG_single Transfer Request.Body=", string(bodyBytes), "reqdata.agentName=", agentName)

	reqdata := DGRequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("DG_single Transfer 解析请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	// 解析 `detail` 字段（需要再次反序列化嵌套的 detail 字符串）
	var detail DGDetail
	err = json.Unmarshal([]byte(reqdata.DetailString), &detail)
	if err != nil {
		logs.Error("DG_single Transfer 解析请求消息体DetailString错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	reqdata.Detail = detail

	if agentName != l.agent {
		logs.Error("DG_single Transfer 非法的渠道Id l.agent=", l.agent, " reqdata.agentName=", agentName)
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	signKey := agentName + l.apiKey
	callbackSign := Md5Encrypt(signKey)
	if !strings.EqualFold(reqdata.Token, callbackSign) {
		logs.Error("DG_single Transfer 签名错误 reqdata.Signature=", reqdata.Token, " getCallbackSign=", callbackSign)
		respdata.CodeId = DG_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.Member.UserName)
	if err != nil {
		logs.Error("DG_single Transfer 会员账号错误 reqdata.Member.UserName=", reqdata.Member.UserName, " err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	gameId := strconv.Itoa(reqdata.Detail.TableId)
	if reqdata.Type == 5 || reqdata.Type == 6 {
		gameId = strconv.Itoa(reqdata.Detail.GameId)
	}

	gameTableType := ""
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("DG_single Transfer 获取游戏列表失败 ticketId=", reqdata.TicketId, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
			gameTableType = l.gameTableTypes[gameList.GameType]
		}
	}

	logs.Info(gameName)

	//判断是否是重复请求数据
	urlMethod := "inform"
	// 判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, reqdata.Data, urlMethod)
	if err != nil {
		respdata.CodeId = DG_Code_Fail_System_Error
		ctx.RespJson(respdata)
		logs.Error(l.brandName, " Transfer 检测是否重复请求 发生错误 thirdId=", reqdata.Data, " err=", err)
		return
	}
	if duplicate {
		logs.Error(l.brandName, " Transfer 检测到重复请求 thirdId=", reqdata.Data)
		if duplicateResult != nil {
			ctx.RespJson(duplicateResult)
		} else {
			respdata.CodeId = DG_Code_Fail_System_Error
			ctx.RespJson(respdata)
		}
		return
	}

	// 记录请求响应日志
	defer func() {
		respCode := 0
		if respdata.CodeId != DG_Code_Success {
			respCode = 1
		}
		base.AddRequestDB(reqdata.Data, string(bodyBytes), respdata, respCode, l.brandName, urlMethod)
	}()

	totalBetAmount := reqdata.Detail.BetPoints
	tablePre := gameTableType + "_pre_order"
	table := gameTableType

	thirdId := strconv.FormatInt(reqdata.Detail.Id, 10)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	//转账类型(1:下注 2:派彩 3:补单 5:红包 6:小费)
	if reqdata.Type == 1 {
		logs.Info("DG_single 下注")
		if totalBetAmount < 0 {
			logs.Error("DG_single 下注确认 下注金额不能为负数 data=", reqdata.Data, " TotalBetAmount=", totalBetAmount, " BetTime=", reqdata.Detail.BetTime)
			respdata.CodeId = DG_Code_Fail_Illegal_Parameter
			ctx.RespJson(respdata)
			return
		}
		// 开始下注事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single 下注确认 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if errors.Is(e, daogorm.ErrRecordNotFound) {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			} else {

			}
			if totalBetAmount > userBalance.Amount {
				e = errors.New("余额不足")
				respdata.CodeId = DG_Code_Fail_Not_Enough_Balance
				return e
			}

			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

			// 下注金额大于用户余额
			if reqdata.Detail.BetPoints > userBalance.Amount {
				logs.Error("DG_single 下注确认 会员余额不足下注金额  data=", reqdata.Data, " TicketId=", reqdata.TicketId, " BetMoney=", totalBetAmount, " userBalance.Amount=", userBalance.Amount)
				return nil
			}
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    totalBetAmount,
				WinAmount:    0,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    -1, //未开奖
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
					logs.Error("DG_single 下注确认 订单已存在  thirdId=", thirdId, " order=", order, " error=", e)
					return e
				}
				logs.Error("DG_single 下注确认 创建订单失败 thirdId=", thirdId, " order=", order, " error=", e)
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, totalBetAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", totalBetAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && totalBetAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("DG_single 下注确认 扣款失败  userId=", userId, " thirdId=", thirdId, " v.BetAmount=", totalBetAmount, " err=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       -totalBetAmount,
				AfterAmount:  userBalance.Amount - totalBetAmount,
				Reason:       utils.BalanceCReasonDGBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single 下注确认 创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Amount = -totalBetAmount
			respdata.Member.Balance = userBalance.Amount
			return nil
		})

		if err != nil {
			logs.Error("DG_single 下注确认 事务处理失败 err=", err)
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}
		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single 下注确认 响应成功 respdata=", respdata)
		return
		return
	} else if reqdata.Type == 2 {
		logs.Info("DG_single 派彩")
		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single 派彩 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			} else {

			}

			winAmount := reqdata.Detail.WinOrLoss
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("DG_single 派彩 订单不存在 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					return e
				}
				logs.Error("DG_single 派彩 查询订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			betCtx := l.DGGameRecord2Str(reqdata, strconv.Itoa(userId), gameName)
			if order.DataState != -1 {
				e = errors.New("订单已结算")
				logs.Error("DG_single 派彩 订单已结算 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(winAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState":  1,
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  winAmount,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single 派彩 更新订单状态失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			order.DataState = 1
			order.ThirdTime = thirdTime
			order.ValidBet = validBet
			order.WinAmount = winAmount
			order.BetCtx = betCtx
			order.GameRst = betCtx
			order.BetCtxType = 3
			order.RawData = string(bodyBytes)
			order.Id = 0
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("DG_single 派彩 创建正式表订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single 派彩 加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonDGSettle,
				Memo:         l.brandName + " settle,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single 派彩 创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single 派彩 事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single 派彩 响应成功 respdata=", respdata)
		return

	} else if reqdata.Type == 3 {
		logs.Info("DG_single 改单")
		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single 改单 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			} else {

			}

			winAmount := reqdata.Detail.WinOrLoss
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("DG_single 改单 订单不存在 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					return e
				}
				logs.Error("DG_single 改单 查询订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			betCtx := l.DGGameRecord2Str(reqdata, strconv.Itoa(userId), gameName)
			if order.DataState != 1 {
				e = errors.New("订单不是已结算状态，不能重新结算")
				logs.Error("DG_single 改单 订单不是已结算状态 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(winAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState":  1,
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  winAmount,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single 改单 更新临时订单状态失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"ThirdTime":  thirdTime,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single 改单 更新正式订单状态失败  thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single 改单 加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonDGEditOrder,
				Memo:         l.brandName + " settle,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single 改单 创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single 改单 事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] 改单 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] 改单 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single 改单 响应成功 respdata=", respdata)
		return
	} else if reqdata.Type == 5 || reqdata.Type == 6 {
		typeName := "红包"
		reasonType := utils.BalanceCReasonDGRedpack
		if reqdata.Type == 6 {
			typeName = "小费"
			reasonType = utils.BalanceCReasonDGTip
		}

		logs.Info("DG_single " + typeName)
		// 开始红包小费事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single "+typeName+" 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			}
			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    0,
				WinAmount:    reqdata.Member.Amount,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    1,
				CreateTime:   thirdTime,
			}
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
					logs.Error("DG_single "+typeName+"  订单已存在  thirdId=", thirdId, " order=", order, " error=", e)
					return e
				}
				logs.Error("DG_single "+typeName+"  创建订单失败 thirdId=", thirdId, " order=", order, " error=", e)
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}

			winAmount := reqdata.Member.Amount

			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single "+typeName+"  加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       reasonType,
				Memo:         l.brandName + " " + typeName + ",thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single "+typeName+"  创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single "+typeName+"  事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] "+typeName+" 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] " + typeName + " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)
		ctx.RespJson(respdata)
		logs.Info("DG_single "+typeName+" 响应成功 respdata=", respdata)
		return
	} else {
		logs.Error("DG_single  Transfer 非法的TransferType TransactionId=", reqdata.TicketId, " TransferType=", reqdata.Type)
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
}

// 检查并完成处理
// type in（1，6）回滚转账，type in（2，3，5）继续完成转账
// 注意：本接口与转账接口应该对流水号做全局锁，确保请求过本接口的流水号再请求转账接口不会再处理
func (l *DGSingleService) Inform(ctx *abugo.AbuHttpContent) {

	type ResponseData struct {
		CodeId int32                     `json:"codeId"` // 错误码(参考文档定义), 错误的类型，使用整数表示
		Member WalletTransferMemberParam `json:"member"` // 会员信息，包含用户名、余额和转账金额
	}
	respdata := ResponseData{
		CodeId: DG_Code_Success,
	}

	// 获取路径参数 :agentName
	agentName := ctx.Gin().Param("agentName")

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("DG_single Transfer 读取请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	logs.Info("DG_single Transfer Request.Body=", string(bodyBytes), "reqdata.agentName=", agentName)

	reqdata := DGRequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("DG_single Transfer 解析请求消息体错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	// 解析 `detail` 字段（需要再次反序列化嵌套的 detail 字符串）
	var detail DGDetail
	err = json.Unmarshal([]byte(reqdata.DetailString), &detail)
	if err != nil {
		logs.Error("DG_single Transfer 解析请求消息体DetailString错误 err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
	reqdata.Detail = detail

	if agentName != l.agent {
		logs.Error("DG_single Transfer 非法的渠道Id l.agent=", l.agent, " reqdata.agentName=", agentName)
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	signKey := agentName + l.apiKey
	callbackSign := Md5Encrypt(signKey)
	if !strings.EqualFold(reqdata.Token, callbackSign) {
		logs.Error("DG_single Transfer 签名错误 reqdata.Signature=", reqdata.Token, " getCallbackSign=", callbackSign)
		respdata.CodeId = DG_Code_Fail_Signature_Error
		ctx.RespJson(respdata)
		return
	}

	userId, err := strconv.Atoi(reqdata.Member.UserName)
	if err != nil {
		logs.Error("DG_single Transfer 会员账号错误 reqdata.Member.UserName=", reqdata.Member.UserName, " err=", err.Error())
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}

	gameId := strconv.Itoa(reqdata.Detail.TableId)
	if reqdata.Type == 5 || reqdata.Type == 6 {
		gameId = strconv.Itoa(reqdata.Detail.GameId)
	}

	gameTableType := ""
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("DG_single Transfer 获取游戏列表失败 ticketId=", reqdata.TicketId, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
			gameTableType = l.gameTableTypes[gameList.GameType]
		}
	}

	logs.Info(gameName)

	//判断是否是重复请求数据
	urlMethod := "inform"
	// 判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, reqdata.Data, urlMethod)
	if err != nil {
		respdata.CodeId = DG_Code_Fail_System_Error
		ctx.RespJson(respdata)
		logs.Error(l.brandName, " Inform 检测是否重复请求 发生错误 thirdId=", reqdata.Data, " err=", err)
		return
	}
	if duplicate {
		logs.Error(l.brandName, " Inform 检测到重复请求 thirdId=", reqdata.Data)
		if duplicateResult != nil {
			ctx.RespJson(duplicateResult)
		} else {
			respdata.CodeId = DG_Code_Fail_System_Error
			ctx.RespJson(respdata)
		}
		return
	}

	// 记录请求响应日志
	defer func() {
		respCode := 0
		if respdata.CodeId != DG_Code_Success {
			respCode = 1
		}
		base.AddRequestDB(reqdata.Data, string(bodyBytes), respdata, respCode, l.brandName, urlMethod)
	}()

	tablePre := gameTableType + "_pre_order"
	table := gameTableType

	thirdId := strconv.FormatInt(reqdata.Detail.Id, 10)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

	//转账类型(1:下注 2:派彩 3:补单 5:红包 6:小费)
	// type in（1，6）回滚转账，type in（2，3，5）继续完成转账
	if reqdata.Type == 1 || reqdata.Type == 6 {
		typeName := "inform 下注取消"
		reasonType := utils.BalanceCReasonDGRedpack
		if reqdata.Type == 6 {
			typeName = "inform 退回小费"
			reasonType = utils.BalanceCReasonDGTip
		}

		logs.Info("DG_single " + typeName)

		// 开始下注事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single "+typeName+" 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if errors.Is(e, daogorm.ErrRecordNotFound) {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			}

			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("DG_single  "+typeName+"  订单不存在 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					return e
				}
				logs.Error("DG_single  "+typeName+" 查询订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			if order.DataState != -1 {
				e = errors.New("订单已结算")
				logs.Error("DG_single   "+typeName+" 订单已结算 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			backAmount := reqdata.Detail.BetPoints
			if backAmount != order.BetAmount {
				e = errors.New("取消金额与下注金额不一致")
				logs.Error("DG_single   "+typeName+" 取消金额与下注金额不一致 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"RawData":   string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single   "+typeName+"  更新订单状态失败 thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = WE_Code_Fail_System_Error
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", backAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && backAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("DG_single   "+typeName+" 加款失败 thirdId=", thirdId, " userId=", userId, " RefundMoney=", backAmount, " err=", e.Error())
				respdata.CodeId = WE_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       backAmount,
				AfterAmount:  userBalance.Amount + backAmount,
				Reason:       reasonType,
				Memo:         l.brandName + " cancel,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single   "+typeName+" 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = WE_Code_Fail_System_Error
				return e
			}
			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = backAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single "+typeName+" 事务处理失败 err=", err)
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] "+typeName+" 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DGLive_single] " + typeName + " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}
		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single "+typeName+" 响应成功 respdata=", respdata)
		return
	} else if reqdata.Type == 2 {
		logs.Info("DG_single inform 派彩")
		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single inform 派彩 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			} else {

			}

			winAmount := reqdata.Detail.WinOrLoss
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("DG_single inform 派彩 订单不存在 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					return e
				}
				logs.Error("DG_single inform 派彩 查询订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			betCtx := l.DGGameRecord2Str(reqdata, strconv.Itoa(userId), gameName)
			if order.DataState != -1 {
				e = errors.New("订单已结算")
				logs.Error("DG_single  inform 派彩 订单已结算 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(winAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState":  1,
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  winAmount,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single inform 派彩 更新订单状态失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			order.DataState = 1
			order.ThirdTime = thirdTime
			order.ValidBet = validBet
			order.WinAmount = winAmount
			order.BetCtx = betCtx
			order.GameRst = betCtx
			order.BetCtxType = 3
			order.RawData = string(bodyBytes)
			order.Id = 0
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("DG_single inform 派彩 创建正式表订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single inform 派彩 加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonDGSettle,
				Memo:         l.brandName + " settle,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single inform 派彩 创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single inform 派彩 事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single inform 派彩 响应成功 respdata=", respdata)
		return

	} else if reqdata.Type == 3 {
		logs.Info("DG_single inform 改单")
		// 开始派彩事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single inform 改单 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			} else {

			}

			winAmount := reqdata.Detail.WinOrLoss
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("DG_single inform 改单 订单不存在 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					return e
				}
				logs.Error("DG_single inform 改单 查询订单失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			betCtx := l.DGGameRecord2Str(reqdata, strconv.Itoa(userId), gameName)
			if order.DataState != 1 {
				e = errors.New("订单不是已结算状态，不能重新结算")
				logs.Error("DG_single 改单 订单不是已结算状态 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}
			// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(winAmount - order.BetAmount)
			if validBet > math.Abs(order.BetAmount) {
				validBet = math.Abs(order.BetAmount)
			}
			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState":  1,
				"ThirdTime":  thirdTime,
				"ValidBet":   validBet,
				"WinAmount":  winAmount,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single inform 改单 更新临时订单状态失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}
			e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"ThirdTime":  thirdTime,
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
				"RawData":    string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("DG_single inform 改单 更新正式订单状态失败  thirdId=", thirdId, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single inform 改单 加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonDGEditOrder,
				Memo:         l.brandName + " settle,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single inform 改单 创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single inform 改单 事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] 改单 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] 改单 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)

		ctx.RespJson(respdata)
		logs.Info("DG_single inform 改单 响应成功 respdata=", respdata)
		return
	} else if reqdata.Type == 5 {
		typeName := "inform 红包"
		reasonType := utils.BalanceCReasonDGRedpack

		logs.Info("DG_single " + typeName)
		// 开始红包小费事务
		err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
			var e error
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
			if e != nil {
				logs.Error("DG_single "+typeName+" 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				} else {
					respdata.CodeId = DG_Code_Fail_User_Not_Exist
				}
				return e
			}
			ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    0,
				WinAmount:    reqdata.Member.Amount,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    1, //未开奖
				CreateTime:   thirdTime,
			}
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				if errors.Is(e, daogorm.ErrDuplicatedKey) { // 如果注单已存在则跳过
					logs.Error("DG_single "+typeName+"  订单已存在  thirdId=", thirdId, " order=", order, " error=", e)
					return e
				}
				logs.Error("DG_single "+typeName+"  创建订单失败 thirdId=", thirdId, " order=", order, " error=", e)
				respdata.CodeId = DG_Code_Fail_Operation_Failed
				return e
			}

			winAmount := reqdata.Member.Amount

			if winAmount != 0 { //金额不=0才需要更新用户余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount + ?", winAmount),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
					e = errors.New("更新条数0")
				}
				if e != nil {
					logs.Error("DG_single "+typeName+"  加扣款失败 TicketId=", reqdata.TicketId, " userId=", userId, " thirdId=", thirdId, " winAmount", winAmount, " err=", e.Error())
					respdata.CodeId = DG_Code_Fail_System_Error
					return e
				}
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       reasonType,
				Memo:         l.brandName + " " + typeName + ",thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("DG_single "+typeName+"  创建账变记录失败 TicketId=", reqdata.TicketId, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.CodeId = DG_Code_Fail_System_Error
				return e
			}

			respdata.Member.Balance = userBalance.Amount
			respdata.Member.Amount = winAmount
			return nil
		})

		if err != nil {
			logs.Error("DG_single "+typeName+"  事务处理失败 TicketId=", reqdata.TicketId, " thirdId=", reqdata.Data, " err=", err.Error())
			ctx.RespJson(respdata)
			return
		} else {
			// 发送余额变动通知
			go func(notifyUserId int64) {
				if l.RefreshUserAmountFunc != nil {
					tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
					if tmpErr != nil {
						logs.Error("[ERROR][DG_single] "+typeName+" 发送余额变动通知错误", notifyUserId, tmpErr.Error())
					}
				} else {
					logs.Error("[ERROR][DG_single] " + typeName + " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				}
			}(int64(userId))
		}

		respdata.Member.UserName = strconv.Itoa(userId)
		ctx.RespJson(respdata)
		logs.Info("DG_single "+typeName+" 响应成功 respdata=", respdata)
		return
	} else {
		logs.Error("DG_single  Transfer 非法的TransferType TransactionId=", reqdata.TicketId, " TransferType=", reqdata.Type)
		respdata.CodeId = DG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respdata)
		return
	}
}

func (l *DGSingleService) DGGameRecord2Str(data DGRequestData, userId string, gameName string) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单编号\":\"%d\",", data.Detail.Id))
	sb.WriteString(fmt.Sprintf("\"玩家账号\":\"%s\",", data.Member.UserName))
	sb.WriteString(fmt.Sprintf("\"投注额\":%g,", data.Detail.BetPoints))
	sb.WriteString(fmt.Sprintf("\"有效投注额\":%g,", data.Detail.AvailableBet))
	sb.WriteString(fmt.Sprintf("\"返奖金额\":%g,", data.Detail.WinOrLoss))
	sb.WriteString(fmt.Sprintf("\"输赢金额\":%g,", data.Detail.WinOrLoss-data.Detail.BetPoints))
	sb.WriteString(fmt.Sprintf("\"游戏名\":\"%s\"", gameName))
	sb.WriteString("}")
	res = sb.String()
	return
}

// 生成DG token
func (l *DGSingleService) getTokenByUser(userId int, account string) (token string) {
	type DGTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyDG + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyDG + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("DG_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("DG_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "DG_" + uuid.NewString()
	tokendata := DGTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyDG + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("DG_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("DG_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *DGSingleService) getUserByToken(token string) (userId int, account string) {
	type DGTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyDG + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := DGTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("DG_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyDG + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("DG_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("DG_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("DG_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// Md5Encrypt 函数用于封装 MD5 加密过程
func Md5Encrypt(data string) string {
	// 创建一个 MD5 哈希对象
	hash := md5.New()

	// 将数据写入哈希对象
	hash.Write([]byte(data))

	// 获取最终的哈希值
	hashBytes := hash.Sum(nil)

	// 将字节切片转换为十六进制字符串并返回
	return hex.EncodeToString(hashBytes)
}
