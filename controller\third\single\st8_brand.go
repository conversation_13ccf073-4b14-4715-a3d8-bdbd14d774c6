package single

import (
	"strings"
)

// Buyin 处理非游戏相关的扣款（如活动或比赛入场费）

// GetProviderCode 根据开发商名称返回对应的代码
func (s *ST8Service) getProviderCode(providerName string) string {
	// 将输入转换为小写以实现大小写不敏感的匹配
	searchName := strings.ToLower(strings.TrimSpace(providerName))
	providerMap := map[string]string{
		"evolution gaming":      "evo",
		"red tiger":             "rtg",
		"netent":                "ntn",
		"spribe":                "spb",
		"ezugi":                 "ezg",
		"betsolutions":          "btsl",
		"charismatic":           "crc",
		"pragmatic play":        "pgp",
		"pragmatic play live":   "ppl",
		"habanero":              "hbn",
		"gamzix":                "gmz",
		"peter & sons":          "pas",
		"jade rabbit studio":    "jdr",
		"naga games":            "ngg",
		"onegame":               "onegame",
		"nolimit city":          "nlc",
		"big time gaming":       "btg",
		"pgsoft":                "pgs",
		"mancala gaming":        "mnc",
		"3 oaks gaming":         "oak",
		"kingmidas":             "kng",
		"ae sexy":               "aes",
		"spinomenal":            "spn",
		"smartsoft gaming":      "sms",
		"tvbet":                 "tvb",
		"fantasma games":        "fnt",
		"voltent":               "wzd",
		"iconic21":              "btr",
		"fugaso":                "fgs",
		"slotmill":              "sml",
		"jdb":                   "jdb",
		"tom horn gaming":       "tmh",
		"endorphina":            "end",
		"netgaming":             "ntg",
		"hacksaw gaming":        "hcw",
		"bgaming":               "bgm",
		"gamebeat":              "gmb",
		"gaming corps":          "gcs",
		"avatarux":              "aux",
		"blueprint gaming":      "blg",
		"netgame entertainment": "nge",
		"thunderkick":           "tdk",
		"bf games":              "bfg",
		"tada gaming":           "tad",
		"booming games":         "bmg",
		"amigo gaming":          "ago",
		"raw igaming":           "raw",
		"leander games":         "lea",
		"cq9":                   "cqc",
		"atmosfera":             "atm",
		"novomatic":             "nvm",
		"winfinity":             "win",
		"onlyplay":              "onl",
		"darwin gaming":         "dwg",
		"playson":               "ply",
		"yolted":                "ylt",
		"imoon games":           "imn",
		"inout games":           "iog",
		"backseat gaming":       "bcs",
		"octoplay":              "oct",
		"spinoro":               "spi",
		"phoenix 7":             "phx",
		"bullshark games":       "bls",
		"spinlogic gaming":      "slg",
		"nownow gaming":         "now",
		"elysium studios":       "els",
		"swintt games":          "swg",
		"swintt premium":        "swp",
		"evoplay entertainment": "eve",
		"fa chai":               "fch",
		"vivo gaming":           "vio",
		"mascot gaming":         "mas",
		"playzia":               "plz",
		"trusty studios":        "trs",
		"slotopia":              "slo",
		"kitsune studios":       "kit",
		//第二批
		"play'n go":              "png",
		"quickspin":              "qps",
		"yggdrasil":              "ygg",
		"northern lights gaming": "nrl",
		"rabcat gambling":        "rbc",
		"4theplayer":             "ftp",
		"reflex gaming":          "rfg",
		"reelplay":               "rpl",
		"bang bang games":        "bbg",
		"hot rise games":         "hrg",
		"bulletproof games":      "bpg",
		"reel life games":        "rlg",
		"gamevy":                 "gmv",
		"jelly":                  "jly",
		"boomerang studios":      "brg",
		"playtech":               "plt",
		"playtech live":          "pltl",
		"relax gaming":           "reg",
		"jili games":             "jil",
		"royal gaming":           "roy",
		"aura gaming":            "aug",
		"push gaming":            "psh",
		"rtg slots":              "rts",
		"booongo":                "bng",
		"print studios":          "pnt",
		"qubit games":            "qbt",
		"hungry bear gaming":     "hbg",
		"win fast games":         "wfg",
		"1spin4win":              "osp",
	}

	// 返回开发商代码，如果未找到则返回空字符串
	return providerMap[searchName]
}

// GetGameDeveloperName 根据开发商代码获取开发商名称
func (s *ST8Service) getGameDeveloperName(code string) string {
	developers := map[string]string{
		"evo":     "Evolution Gaming",
		"rtg":     "Red Tiger",
		"ntn":     "NetEnt",
		"spb":     "Spribe",
		"ezg":     "Ezugi",
		"btsl":    "Betsolutions",
		"crc":     "Charismatic",
		"pgp":     "Pragmatic Play",
		"ppl":     "Pragmatic Play Live",
		"hbn":     "Habanero",
		"gmz":     "Gamzix",
		"pas":     "Peter & Sons",
		"jdr":     "Jade Rabbit Studio",
		"ngg":     "Naga Games",
		"onegame": "OneGame",
		"nlc":     "Nolimit City",
		"btg":     "Big Time Gaming",
		"pgs":     "PGSoft",
		"mnc":     "Mancala Gaming",
		"oak":     "3 Oaks Gaming",
		"kng":     "Kingmidas",
		"aes":     "AE Sexy",
		"spn":     "Spinomenal",
		"sms":     "Smartsoft Gaming",
		"tvb":     "TVBet",
		"fnt":     "Fantasma Games",
		"wzd":     "VoltEnt",
		"btr":     "ICONIC21",
		"fgs":     "Fugaso",
		"sml":     "Slotmill",
		"jdb":     "JDB",
		"tmh":     "Tom Horn Gaming",
		"end":     "Endorphina",
		"ntg":     "NetGaming",
		"hcw":     "Hacksaw Gaming",
		"bgm":     "BGaming",
		"gmb":     "Gamebeat",
		"gcs":     "Gaming Corps",
		"aux":     "AvatarUX",
		"blg":     "Blueprint Gaming",
		"nge":     "NetGame Entertainment",
		"tdk":     "Thunderkick",
		"bfg":     "BF Games",
		"tad":     "TaDa Gaming",
		"bmg":     "Booming Games",
		"ago":     "Amigo Gaming",
		"raw":     "Raw iGaming",
		"lea":     "Leander Games",
		"cqc":     "CQ9",
		"atm":     "Atmosfera",
		"nvm":     "Novomatic",
		"win":     "Winfinity",
		"onl":     "Onlyplay",
		"dwg":     "Darwin Gaming",
		"ply":     "Playson",
		"ylt":     "Yolted",
		"imn":     "iMoon Games",
		"iog":     "InOut Games",
		"bcs":     "Backseat Gaming",
		"oct":     "Octoplay",
		"spi":     "SpinOro",
		"phx":     "PHOENIX 7",
		"bls":     "Bullshark Games",
		"slg":     "Spinlogic Gaming",
		"now":     "NowNow Gaming",
		"els":     "Elysium Studios",
		"swg":     "Swintt Games",
		"swp":     "Swintt Premium",
		"eve":     "Evoplay Entertainment",
		"fch":     "Fa Chai",
		"vio":     "Vivo Gaming",
		"mas":     "Mascot Gaming",
		"plz":     "Playzia",
		"trs":     "Trusty Studios",
		"slo":     "Slotopia",
		"kit":     "Kitsune Studios",
		//第二批
		"rpl": "ReelPlay",
		"rlg": "Reel Life Games",
		"plt": "Playtech",
		"reg": "Relax Gaming",
		"rts": "RTG Slots",
		"qbt": "Qubit Games",
		"osp": "1Spin4Win",
		"nrl": "Northern Lights Gaming",
		"rfg": "Reflex Gaming",
		"hrg": "Hot Rise Games",
		"bpg": "Bulletproof Games",
		"gmv": "GameVy",
		"jly": "Jelly",
		"hbg": "Hungry Bear Gaming",
		"png": "Play'n Go",
		"ygg": "Yggdrasil",
		"bbg": "Bang Bang Games",
		"brg": "Boomerang Studios",
		"jil": "Jili Games",
		"psh": "Push Gaming",
		"pnt": "Print Studios",
		"qps": "Quickspin",
		"ftp": "4ThePlayer",
		"bng": "Booongo",
		"wfg": "Win Fast Games",
		"rbc": "Rabcat Gambling",
	}

	if name, exists := developers[code]; exists {
		brandName := s.brandName + strings.ReplaceAll(name, " ", "_")
		brandName = strings.ReplaceAll(brandName, "'", "")
		return brandName
	}
	return code // 如果找不到对应的开发商名称，返回原始代码
}

const K_ST8CHANGETYPEBET int = 1
const K_ST8CHANGETYPESETTLE int = 2
const K_ST8CHANGETYPEROLLBACK int = 3
const K_ST8CHANGETYPEBuyin int = 4
const K_ST8CHANGETYPEPayout int = 5

// 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-hub真人 6-sport体育
// 返回账变类型
func (l *ST8Service) getSt8GoldChangeType(t int, brand string) (goldType int) {

	// 定义账变类型映射

	changeTypeMap := map[string]map[int]int{
		"HS8_evo": {
			K_ST8CHANGETYPEBET:      EvolutionGamingBet,
			K_ST8CHANGETYPESETTLE:   EvolutionGamingSettle,
			K_ST8CHANGETYPEROLLBACK: EvolutionGamingRollback,
			K_ST8CHANGETYPEBuyin:    EvolutionGamingDeduct,
			K_ST8CHANGETYPEPayout:   EvolutionGamingAdd,
		},
		"HS8_rtg": {
			K_ST8CHANGETYPEBET:      RedTigerBet,
			K_ST8CHANGETYPESETTLE:   RedTigerSettle,
			K_ST8CHANGETYPEROLLBACK: RedTigerRollback,
			K_ST8CHANGETYPEBuyin:    RedTigerDeduct,
			K_ST8CHANGETYPEPayout:   RedTigerAdd,
		},
		"HS8_ntn": {
			K_ST8CHANGETYPEBET:      NetEntBet,
			K_ST8CHANGETYPESETTLE:   NetEntSettle,
			K_ST8CHANGETYPEROLLBACK: NetEntRollback,
			K_ST8CHANGETYPEBuyin:    NetEntDeduct,
			K_ST8CHANGETYPEPayout:   NetEntAdd,
		},
		"HS8_spb": {
			K_ST8CHANGETYPEBET:      SpribeBet,
			K_ST8CHANGETYPESETTLE:   SpribeSettle,
			K_ST8CHANGETYPEROLLBACK: SpribeRollback,
			K_ST8CHANGETYPEBuyin:    SpribeDeduct,
			K_ST8CHANGETYPEPayout:   SpribeAdd,
		},
		"HS8_ezg": {
			K_ST8CHANGETYPEBET:      EzugiBet,
			K_ST8CHANGETYPESETTLE:   EzugiSettle,
			K_ST8CHANGETYPEROLLBACK: EzugiRollback,
			K_ST8CHANGETYPEBuyin:    EzugiDeduct,
			K_ST8CHANGETYPEPayout:   EzugiAdd,
		},
		"HS8_btsl": {
			K_ST8CHANGETYPEBET:      BetsolutionsBet,
			K_ST8CHANGETYPESETTLE:   BetsolutionsSettle,
			K_ST8CHANGETYPEROLLBACK: BetsolutionsRollback,
			K_ST8CHANGETYPEBuyin:    BetsolutionsDeduct,
			K_ST8CHANGETYPEPayout:   BetsolutionsAdd,
		},
		"HS8_crc": {
			K_ST8CHANGETYPEBET:      CharismaticBet,
			K_ST8CHANGETYPESETTLE:   CharismaticSettle,
			K_ST8CHANGETYPEROLLBACK: CharismaticRollback,
			K_ST8CHANGETYPEBuyin:    CharismaticDeduct,
			K_ST8CHANGETYPEPayout:   CharismaticAdd,
		},
		"HS8_pgp": {
			K_ST8CHANGETYPEBET:      PragmaticPlayBet,
			K_ST8CHANGETYPESETTLE:   PragmaticPlaySettle,
			K_ST8CHANGETYPEROLLBACK: PragmaticPlayRollback,
			K_ST8CHANGETYPEBuyin:    PragmaticPlayDeduct,
			K_ST8CHANGETYPEPayout:   PragmaticPlayAdd,
		},
		"HS8_ppl": {
			K_ST8CHANGETYPEBET:      PragmaticPlayLiveBet,
			K_ST8CHANGETYPESETTLE:   PragmaticPlayLiveSettle,
			K_ST8CHANGETYPEROLLBACK: PragmaticPlayLiveRollback,
			K_ST8CHANGETYPEBuyin:    PragmaticPlayLiveDeduct,
			K_ST8CHANGETYPEPayout:   PragmaticPlayLiveAdd,
		},
		"HS8_hbn": {
			K_ST8CHANGETYPEBET:      HabaneroBet,
			K_ST8CHANGETYPESETTLE:   HabaneroSettle,
			K_ST8CHANGETYPEROLLBACK: HabaneroRollback,
			K_ST8CHANGETYPEBuyin:    HabaneroDeduct,
			K_ST8CHANGETYPEPayout:   HabaneroAdd,
		},
		"HS8_gmz": {
			K_ST8CHANGETYPEBET:      GamzixBet,
			K_ST8CHANGETYPESETTLE:   GamzixSettle,
			K_ST8CHANGETYPEROLLBACK: GamzixRollback,
			K_ST8CHANGETYPEBuyin:    GamzixDeduct,
			K_ST8CHANGETYPEPayout:   GamzixAdd,
		},
		"HS8_pas": {
			K_ST8CHANGETYPEBET:      PeterAndSonsBet,
			K_ST8CHANGETYPESETTLE:   PeterAndSonsSettle,
			K_ST8CHANGETYPEROLLBACK: PeterAndSonsRollback,
			K_ST8CHANGETYPEBuyin:    PeterAndSonsDeduct,
			K_ST8CHANGETYPEPayout:   PeterAndSonsAdd,
		},
		"HS8_jdr": {
			K_ST8CHANGETYPEBET:      JadeRabbitStudioBet,
			K_ST8CHANGETYPESETTLE:   JadeRabbitStudioSettle,
			K_ST8CHANGETYPEROLLBACK: JadeRabbitStudioRollback,
			K_ST8CHANGETYPEBuyin:    JadeRabbitStudioDeduct,
			K_ST8CHANGETYPEPayout:   JadeRabbitStudioAdd,
		},
		"HS8_ngg": {
			K_ST8CHANGETYPEBET:      NagaGamesBet,
			K_ST8CHANGETYPESETTLE:   NagaGamesSettle,
			K_ST8CHANGETYPEROLLBACK: NagaGamesRollback,
			K_ST8CHANGETYPEBuyin:    NagaGamesDeduct,
			K_ST8CHANGETYPEPayout:   NagaGamesAdd,
		},
		"HS8_onegame": {
			K_ST8CHANGETYPEBET:      OneGameBet,
			K_ST8CHANGETYPESETTLE:   OneGameSettle,
			K_ST8CHANGETYPEROLLBACK: OneGameRollback,
			K_ST8CHANGETYPEBuyin:    OneGameDeduct,
			K_ST8CHANGETYPEPayout:   OneGameAdd,
		},
		"HS8_nlc": {
			K_ST8CHANGETYPEBET:      NolimitCityBet,
			K_ST8CHANGETYPESETTLE:   NolimitCitySettle,
			K_ST8CHANGETYPEROLLBACK: NolimitCityRollback,
			K_ST8CHANGETYPEBuyin:    NolimitCityDeduct,
			K_ST8CHANGETYPEPayout:   NolimitCityAdd,
		},
		"HS8_btg": {
			K_ST8CHANGETYPEBET:      BigTimeGamingBet,
			K_ST8CHANGETYPESETTLE:   BigTimeGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BigTimeGamingRollback,
			K_ST8CHANGETYPEBuyin:    BigTimeGamingDeduct,
			K_ST8CHANGETYPEPayout:   BigTimeGamingAdd,
		},
		"HS8_pgs": {
			K_ST8CHANGETYPEBET:      PGSoftBet,
			K_ST8CHANGETYPESETTLE:   PGSoftSettle,
			K_ST8CHANGETYPEROLLBACK: PGSoftRollback,
			K_ST8CHANGETYPEBuyin:    PGSoftDeduct,
			K_ST8CHANGETYPEPayout:   PGSoftAdd,
		},
		"HS8_mnc": {
			K_ST8CHANGETYPEBET:      MancalaGamingBet,
			K_ST8CHANGETYPESETTLE:   MancalaGamingSettle,
			K_ST8CHANGETYPEROLLBACK: MancalaGamingRollback,
			K_ST8CHANGETYPEBuyin:    MancalaGamingDeduct,
			K_ST8CHANGETYPEPayout:   MancalaGamingAdd,
		},
		"HS8_oak": {
			K_ST8CHANGETYPEBET:      ThreeOaksGamingBet,
			K_ST8CHANGETYPESETTLE:   ThreeOaksGamingSettle,
			K_ST8CHANGETYPEROLLBACK: ThreeOaksGamingRollback,
			K_ST8CHANGETYPEBuyin:    ThreeOaksGamingDeduct,
			K_ST8CHANGETYPEPayout:   ThreeOaksGamingAdd,
		},
		"HS8_kng": {
			K_ST8CHANGETYPEBET:      KingmidasBet,
			K_ST8CHANGETYPESETTLE:   KingmidasSettle,
			K_ST8CHANGETYPEROLLBACK: KingmidasRollback,
			K_ST8CHANGETYPEBuyin:    KingmidasDeduct,
			K_ST8CHANGETYPEPayout:   KingmidasAdd,
		},
		"HS8_aes": {
			K_ST8CHANGETYPEBET:      AESexyBet,
			K_ST8CHANGETYPESETTLE:   AESexySettle,
			K_ST8CHANGETYPEROLLBACK: AESexyRollback,
			K_ST8CHANGETYPEBuyin:    AESexyDeduct,
			K_ST8CHANGETYPEPayout:   AESexyAdd,
		},
		"HS8_spn": {
			K_ST8CHANGETYPEBET:      SpinomenalBet,
			K_ST8CHANGETYPESETTLE:   SpinomenalSettle,
			K_ST8CHANGETYPEROLLBACK: SpinomenalRollback,
			K_ST8CHANGETYPEBuyin:    SpinomenalDeduct,
			K_ST8CHANGETYPEPayout:   SpinomenalAdd,
		},
		"HS8_sms": {
			K_ST8CHANGETYPEBET:      SmartsoftGamingBet,
			K_ST8CHANGETYPESETTLE:   SmartsoftGamingSettle,
			K_ST8CHANGETYPEROLLBACK: SmartsoftGamingRollback,
			K_ST8CHANGETYPEBuyin:    SmartsoftGamingDeduct,
			K_ST8CHANGETYPEPayout:   SmartsoftGamingAdd,
		},
		"HS8_tvb": {
			K_ST8CHANGETYPEBET:      TVBetBet,
			K_ST8CHANGETYPESETTLE:   TVBetSettle,
			K_ST8CHANGETYPEROLLBACK: TVBetRollback,
			K_ST8CHANGETYPEBuyin:    TVBetDeduct,
			K_ST8CHANGETYPEPayout:   TVBetAdd,
		},
		"HS8_fnt": {
			K_ST8CHANGETYPEBET:      FantasmaGamesBet,
			K_ST8CHANGETYPESETTLE:   FantasmaGamesSettle,
			K_ST8CHANGETYPEROLLBACK: FantasmaGamesRollback,
			K_ST8CHANGETYPEBuyin:    FantasmaGamesDeduct,
			K_ST8CHANGETYPEPayout:   FantasmaGamesAdd,
		},
		"HS8_wzd": {
			K_ST8CHANGETYPEBET:      VoltEntBet,
			K_ST8CHANGETYPESETTLE:   VoltEntSettle,
			K_ST8CHANGETYPEROLLBACK: VoltEntRollback,
			K_ST8CHANGETYPEBuyin:    VoltEntDeduct,
			K_ST8CHANGETYPEPayout:   VoltEntAdd,
		},
		"HS8_btr": {
			K_ST8CHANGETYPEBET:      ICONIC21Bet,
			K_ST8CHANGETYPESETTLE:   ICONIC21Settle,
			K_ST8CHANGETYPEROLLBACK: ICONIC21Rollback,
			K_ST8CHANGETYPEBuyin:    ICONIC21Deduct,
			K_ST8CHANGETYPEPayout:   ICONIC21Add,
		},
		"HS8_fgs": {
			K_ST8CHANGETYPEBET:      FugasoBet,
			K_ST8CHANGETYPESETTLE:   FugasoSettle,
			K_ST8CHANGETYPEROLLBACK: FugasoRollback,
			K_ST8CHANGETYPEBuyin:    FugasoDeduct,
			K_ST8CHANGETYPEPayout:   FugasoAdd,
		},
		"HS8_sml": {
			K_ST8CHANGETYPEBET:      SlotmillBet,
			K_ST8CHANGETYPESETTLE:   SlotmillSettle,
			K_ST8CHANGETYPEROLLBACK: SlotmillRollback,
			K_ST8CHANGETYPEBuyin:    SlotmillDeduct,
			K_ST8CHANGETYPEPayout:   SlotmillAdd,
		},
		"HS8_jdb": {
			K_ST8CHANGETYPEBET:      JDBBet,
			K_ST8CHANGETYPESETTLE:   JDBSettle,
			K_ST8CHANGETYPEROLLBACK: JDBRollback,
			K_ST8CHANGETYPEBuyin:    JDBDeduct,
			K_ST8CHANGETYPEPayout:   JDBAdd,
		},
		"HS8_tmh": {
			K_ST8CHANGETYPEBET:      TomHornGamingBet,
			K_ST8CHANGETYPESETTLE:   TomHornGamingSettle,
			K_ST8CHANGETYPEROLLBACK: TomHornGamingRollback,
			K_ST8CHANGETYPEBuyin:    TomHornGamingDeduct,
			K_ST8CHANGETYPEPayout:   TomHornGamingAdd,
		},
		"HS8_end": {
			K_ST8CHANGETYPEBET:      EndorphinaBet,
			K_ST8CHANGETYPESETTLE:   EndorphinaSettle,
			K_ST8CHANGETYPEROLLBACK: EndorphinaRollback,
			K_ST8CHANGETYPEBuyin:    EndorphinaDeduct,
			K_ST8CHANGETYPEPayout:   EndorphinaAdd,
		},
		"HS8_ntg": {
			K_ST8CHANGETYPEBET:      NetGamingBet,
			K_ST8CHANGETYPESETTLE:   NetGamingSettle,
			K_ST8CHANGETYPEROLLBACK: NetGamingRollback,
			K_ST8CHANGETYPEBuyin:    NetGamingDeduct,
			K_ST8CHANGETYPEPayout:   NetGamingAdd,
		},
		"HS8_hcw": {
			K_ST8CHANGETYPEBET:      HacksawGamingBet,
			K_ST8CHANGETYPESETTLE:   HacksawGamingSettle,
			K_ST8CHANGETYPEROLLBACK: HacksawGamingRollback,
			K_ST8CHANGETYPEBuyin:    HacksawGamingDeduct,
			K_ST8CHANGETYPEPayout:   HacksawGamingAdd,
		},
		"HS8_bgm": {
			K_ST8CHANGETYPEBET:      BGamingBet,
			K_ST8CHANGETYPESETTLE:   BGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BGamingRollback,
			K_ST8CHANGETYPEBuyin:    BGamingDeduct,
			K_ST8CHANGETYPEPayout:   BGamingAdd,
		},
		"HS8_gmb": {
			K_ST8CHANGETYPEBET:      GamebeatBet,
			K_ST8CHANGETYPESETTLE:   GamebeatSettle,
			K_ST8CHANGETYPEROLLBACK: GamebeatRollback,
			K_ST8CHANGETYPEBuyin:    GamebeatDeduct,
			K_ST8CHANGETYPEPayout:   GamebeatAdd,
		},
		"HS8_gcs": {
			K_ST8CHANGETYPEBET:      GamingCorpsBet,
			K_ST8CHANGETYPESETTLE:   GamingCorpsSettle,
			K_ST8CHANGETYPEROLLBACK: GamingCorpsRollback,
			K_ST8CHANGETYPEBuyin:    GamingCorpsDeduct,
			K_ST8CHANGETYPEPayout:   GamingCorpsAdd,
		},
		"HS8_aux": {
			K_ST8CHANGETYPEBET:      AvatarUXBet,
			K_ST8CHANGETYPESETTLE:   AvatarUXSettle,
			K_ST8CHANGETYPEROLLBACK: AvatarUXRollback,
			K_ST8CHANGETYPEBuyin:    AvatarUXDeduct,
			K_ST8CHANGETYPEPayout:   AvatarUXAdd,
		},
		"HS8_blg": {
			K_ST8CHANGETYPEBET:      BlueprintGamingBet,
			K_ST8CHANGETYPESETTLE:   BlueprintGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BlueprintGamingRollback,
			K_ST8CHANGETYPEBuyin:    BlueprintGamingDeduct,
			K_ST8CHANGETYPEPayout:   BlueprintGamingAdd,
		},
		"HS8_nge": {
			K_ST8CHANGETYPEBET:      NetGameEntertainmentBet,
			K_ST8CHANGETYPESETTLE:   NetGameEntertainmentSettle,
			K_ST8CHANGETYPEROLLBACK: NetGameEntertainmentRollback,
			K_ST8CHANGETYPEBuyin:    NetGameEntertainmentDeduct,
			K_ST8CHANGETYPEPayout:   NetGameEntertainmentAdd,
		},
		"HS8_tdk": {
			K_ST8CHANGETYPEBET:      ThunderkickBet,
			K_ST8CHANGETYPESETTLE:   ThunderkickSettle,
			K_ST8CHANGETYPEROLLBACK: ThunderkickRollback,
			K_ST8CHANGETYPEBuyin:    ThunderkickDeduct,
			K_ST8CHANGETYPEPayout:   ThunderkickAdd,
		},
		"HS8_bfg": {
			K_ST8CHANGETYPEBET:      BFGamesBet,
			K_ST8CHANGETYPESETTLE:   BFGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BFGamesRollback,
			K_ST8CHANGETYPEBuyin:    BFGamesDeduct,
			K_ST8CHANGETYPEPayout:   BFGamesAdd,
		},
		"HS8_tad": {
			K_ST8CHANGETYPEBET:      TaDaGamingBet,
			K_ST8CHANGETYPESETTLE:   TaDaGamingSettle,
			K_ST8CHANGETYPEROLLBACK: TaDaGamingRollback,
			K_ST8CHANGETYPEBuyin:    TaDaGamingDeduct,
			K_ST8CHANGETYPEPayout:   TaDaGamingAdd,
		},
		"HS8_bmg": {
			K_ST8CHANGETYPEBET:      BoomingGamesBet,
			K_ST8CHANGETYPESETTLE:   BoomingGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BoomingGamesRollback,
			K_ST8CHANGETYPEBuyin:    BoomingGamesDeduct,
			K_ST8CHANGETYPEPayout:   BoomingGamesAdd,
		},
		"HS8_ago": {
			K_ST8CHANGETYPEBET:      AmigoGamingBet,
			K_ST8CHANGETYPESETTLE:   AmigoGamingSettle,
			K_ST8CHANGETYPEROLLBACK: AmigoGamingRollback,
			K_ST8CHANGETYPEBuyin:    AmigoGamingDeduct,
			K_ST8CHANGETYPEPayout:   AmigoGamingAdd,
		},
		"HS8_raw": {
			K_ST8CHANGETYPEBET:      RawIGamingBet,
			K_ST8CHANGETYPESETTLE:   RawIGamingSettle,
			K_ST8CHANGETYPEROLLBACK: RawIGamingRollback,
			K_ST8CHANGETYPEBuyin:    RawIGamingDeduct,
			K_ST8CHANGETYPEPayout:   RawIGamingAdd,
		},
		"HS8_lea": {
			K_ST8CHANGETYPEBET:      LeanderGamesBet,
			K_ST8CHANGETYPESETTLE:   LeanderGamesSettle,
			K_ST8CHANGETYPEROLLBACK: LeanderGamesRollback,
			K_ST8CHANGETYPEBuyin:    LeanderGamesDeduct,
			K_ST8CHANGETYPEPayout:   LeanderGamesAdd,
		},
		"HS8_cqc": {
			K_ST8CHANGETYPEBET:      CQ9Bet,
			K_ST8CHANGETYPESETTLE:   CQ9Settle,
			K_ST8CHANGETYPEROLLBACK: CQ9Rollback,
			K_ST8CHANGETYPEBuyin:    CQ9Deduct,
			K_ST8CHANGETYPEPayout:   CQ9Add,
		},
		"HS8_atm": {
			K_ST8CHANGETYPEBET:      AtmosferaBet,
			K_ST8CHANGETYPESETTLE:   AtmosferaSettle,
			K_ST8CHANGETYPEROLLBACK: AtmosferaRollback,
			K_ST8CHANGETYPEBuyin:    AtmosferaDeduct,
			K_ST8CHANGETYPEPayout:   AtmosferaAdd,
		},
		"HS8_nvm": {
			K_ST8CHANGETYPEBET:      NovomaticBet,
			K_ST8CHANGETYPESETTLE:   NovomaticSettle,
			K_ST8CHANGETYPEROLLBACK: NovomaticRollback,
			K_ST8CHANGETYPEBuyin:    NovomaticDeduct,
			K_ST8CHANGETYPEPayout:   NovomaticAdd,
		},
		"HS8_win": {
			K_ST8CHANGETYPEBET:      WinfinityBet,
			K_ST8CHANGETYPESETTLE:   WinfinitySettle,
			K_ST8CHANGETYPEROLLBACK: WinfinityRollback,
			K_ST8CHANGETYPEBuyin:    WinfinityDeduct,
			K_ST8CHANGETYPEPayout:   WinfinityAdd,
		},
		"HS8_onl": {
			K_ST8CHANGETYPEBET:      OnlyplayBet,
			K_ST8CHANGETYPESETTLE:   OnlyplaySettle,
			K_ST8CHANGETYPEROLLBACK: OnlyplayRollback,
			K_ST8CHANGETYPEBuyin:    OnlyplayDeduct,
			K_ST8CHANGETYPEPayout:   OnlyplayAdd,
		},
		"HS8_dwg": {
			K_ST8CHANGETYPEBET:      DarwinGamingBet,
			K_ST8CHANGETYPESETTLE:   DarwinGamingSettle,
			K_ST8CHANGETYPEROLLBACK: DarwinGamingRollback,
			K_ST8CHANGETYPEBuyin:    DarwinGamingDeduct,
			K_ST8CHANGETYPEPayout:   DarwinGamingAdd,
		},
		"HS8_ply": {
			K_ST8CHANGETYPEBET:      PlaysonBet,
			K_ST8CHANGETYPESETTLE:   PlaysonSettle,
			K_ST8CHANGETYPEROLLBACK: PlaysonRollback,
			K_ST8CHANGETYPEBuyin:    PlaysonDeduct,
			K_ST8CHANGETYPEPayout:   PlaysonAdd,
		},
		"HS8_ylt": {
			K_ST8CHANGETYPEBET:      YoltedBet,
			K_ST8CHANGETYPESETTLE:   YoltedSettle,
			K_ST8CHANGETYPEROLLBACK: YoltedRollback,
			K_ST8CHANGETYPEBuyin:    YoltedDeduct,
			K_ST8CHANGETYPEPayout:   YoltedAdd,
		},
		"HS8_imn": {
			K_ST8CHANGETYPEBET:      IMoonGamesBet,
			K_ST8CHANGETYPESETTLE:   IMoonGamesSettle,
			K_ST8CHANGETYPEROLLBACK: IMoonGamesRollback,
			K_ST8CHANGETYPEBuyin:    IMoonGamesDeduct,
			K_ST8CHANGETYPEPayout:   IMoonGamesAdd,
		},
		"HS8_iog": {
			K_ST8CHANGETYPEBET:      InOutGamesBet,
			K_ST8CHANGETYPESETTLE:   InOutGamesSettle,
			K_ST8CHANGETYPEROLLBACK: InOutGamesRollback,
			K_ST8CHANGETYPEBuyin:    InOutGamesDeduct,
			K_ST8CHANGETYPEPayout:   InOutGamesAdd,
		},
		"HS8_bcs": {
			K_ST8CHANGETYPEBET:      BackseatGamingBet,
			K_ST8CHANGETYPESETTLE:   BackseatGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BackseatGamingRollback,
			K_ST8CHANGETYPEBuyin:    BackseatGamingDeduct,
			K_ST8CHANGETYPEPayout:   BackseatGamingAdd,
		},
		"HS8_oct": {
			K_ST8CHANGETYPEBET:      OctoplayBet,
			K_ST8CHANGETYPESETTLE:   OctoplaySettle,
			K_ST8CHANGETYPEROLLBACK: OctoplayRollback,
			K_ST8CHANGETYPEBuyin:    OctoplayDeduct,
			K_ST8CHANGETYPEPayout:   OctoplayAdd,
		}, "HS8_spi": {
			K_ST8CHANGETYPEBET:      SpinOroBet,
			K_ST8CHANGETYPESETTLE:   SpinOroSettle,
			K_ST8CHANGETYPEROLLBACK: SpinOroRollback,
			K_ST8CHANGETYPEBuyin:    SpinOroDeduct,
			K_ST8CHANGETYPEPayout:   SpinOroAdd,
		},
		"HS8_phx": {
			K_ST8CHANGETYPEBET:      PHOENIX7Bet,
			K_ST8CHANGETYPESETTLE:   PHOENIX7Settle,
			K_ST8CHANGETYPEROLLBACK: PHOENIX7Rollback,
			K_ST8CHANGETYPEBuyin:    PHOENIX7Deduct,
			K_ST8CHANGETYPEPayout:   PHOENIX7Add,
		},
		"HS8_bls": {
			K_ST8CHANGETYPEBET:      BullsharkGamesBet,
			K_ST8CHANGETYPESETTLE:   BullsharkGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BullsharkGamesRollback,
			K_ST8CHANGETYPEBuyin:    BullsharkGamesDeduct,
			K_ST8CHANGETYPEPayout:   BullsharkGamesAdd,
		},
		"HS8_slg": {
			K_ST8CHANGETYPEBET:      SpinlogicGamingBet,
			K_ST8CHANGETYPESETTLE:   SpinlogicGamingSettle,
			K_ST8CHANGETYPEROLLBACK: SpinlogicGamingRollback,
			K_ST8CHANGETYPEBuyin:    SpinlogicGamingDeduct,
			K_ST8CHANGETYPEPayout:   SpinlogicGamingAdd,
		},
		"HS8_now": {
			K_ST8CHANGETYPEBET:      NowNowGamingBet,
			K_ST8CHANGETYPESETTLE:   NowNowGamingSettle,
			K_ST8CHANGETYPEROLLBACK: NowNowGamingRollback,
			K_ST8CHANGETYPEBuyin:    NowNowGamingDeduct,
			K_ST8CHANGETYPEPayout:   NowNowGamingAdd,
		},
		"HS8_els": {
			K_ST8CHANGETYPEBET:      ElysiumStudiosBet,
			K_ST8CHANGETYPESETTLE:   ElysiumStudiosSettle,
			K_ST8CHANGETYPEROLLBACK: ElysiumStudiosRollback,
			K_ST8CHANGETYPEBuyin:    ElysiumStudiosDeduct,
			K_ST8CHANGETYPEPayout:   ElysiumStudiosAdd,
		},
		"HS8_swg": {
			K_ST8CHANGETYPEBET:      SwinttGamesBet,
			K_ST8CHANGETYPESETTLE:   SwinttGamesSettle,
			K_ST8CHANGETYPEROLLBACK: SwinttGamesRollback,
			K_ST8CHANGETYPEBuyin:    SwinttGamesDeduct,
			K_ST8CHANGETYPEPayout:   SwinttGamesAdd,
		},
		"HS8_swp": {
			K_ST8CHANGETYPEBET:      SwinttPremiumBet,
			K_ST8CHANGETYPESETTLE:   SwinttPremiumSettle,
			K_ST8CHANGETYPEROLLBACK: SwinttPremiumRollback,
			K_ST8CHANGETYPEBuyin:    SwinttPremiumDeduct,
			K_ST8CHANGETYPEPayout:   SwinttPremiumAdd,
		},
		"HS8_eve": {
			K_ST8CHANGETYPEBET:      EvoplayEntertainmentBet,
			K_ST8CHANGETYPESETTLE:   EvoplayEntertainmentSettle,
			K_ST8CHANGETYPEROLLBACK: EvoplayEntertainmentRollback,
			K_ST8CHANGETYPEBuyin:    EvoplayEntertainmentDeduct,
			K_ST8CHANGETYPEPayout:   EvoplayEntertainmentAdd,
		},
		"HS8_fch": {
			K_ST8CHANGETYPEBET:      FaChaiBet,
			K_ST8CHANGETYPESETTLE:   FaChaiSettle,
			K_ST8CHANGETYPEROLLBACK: FaChaiRollback,
			K_ST8CHANGETYPEBuyin:    FaChaiDeduct,
			K_ST8CHANGETYPEPayout:   FaChaiAdd,
		},
		"HS8_vio": {
			K_ST8CHANGETYPEBET:      VivoGamingBet,
			K_ST8CHANGETYPESETTLE:   VivoGamingSettle,
			K_ST8CHANGETYPEROLLBACK: VivoGamingRollback,
			K_ST8CHANGETYPEBuyin:    VivoGamingDeduct,
			K_ST8CHANGETYPEPayout:   VivoGamingAdd,
		},
		"HS8_mas": {
			K_ST8CHANGETYPEBET:      MascotGamingBet,
			K_ST8CHANGETYPESETTLE:   MascotGamingSettle,
			K_ST8CHANGETYPEROLLBACK: MascotGamingRollback,
			K_ST8CHANGETYPEBuyin:    MascotGamingDeduct,
			K_ST8CHANGETYPEPayout:   MascotGamingAdd,
		},
		"HS8_plz": {
			K_ST8CHANGETYPEBET:      PlayziaBet,
			K_ST8CHANGETYPESETTLE:   PlayziaSettle,
			K_ST8CHANGETYPEROLLBACK: PlayziaRollback,
			K_ST8CHANGETYPEBuyin:    PlayziaDeduct,
			K_ST8CHANGETYPEPayout:   PlayziaAdd,
		},
		"HS8_trs": {
			K_ST8CHANGETYPEBET:      TrustyStudiosBet,
			K_ST8CHANGETYPESETTLE:   TrustyStudiosSettle,
			K_ST8CHANGETYPEROLLBACK: TrustyStudiosRollback,
			K_ST8CHANGETYPEBuyin:    TrustyStudiosDeduct,
			K_ST8CHANGETYPEPayout:   TrustyStudiosAdd,
		},
		"HS8_slo": {
			K_ST8CHANGETYPEBET:      SlotopiaBet,
			K_ST8CHANGETYPESETTLE:   SlotopiaSettle,
			K_ST8CHANGETYPEROLLBACK: SlotopiaRollback,
			K_ST8CHANGETYPEBuyin:    SlotopiaDeduct,
			K_ST8CHANGETYPEPayout:   SlotopiaAdd,
		},
		"HS8_kit": {
			K_ST8CHANGETYPEBET:      KitsuneStudiosBet,
			K_ST8CHANGETYPESETTLE:   KitsuneStudiosSettle,
			K_ST8CHANGETYPEROLLBACK: KitsuneStudiosRollback,
			K_ST8CHANGETYPEBuyin:    KitsuneStudiosDeduct,
			K_ST8CHANGETYPEPayout:   KitsuneStudiosAdd,
		},
		"HS8_rpl": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8ReelPlayBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8ReelPlaySettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8ReelPlayRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8ReelPlayBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8ReelPlayPayout,
		},
		"HS8_rlg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8ReelLifeGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8ReelLifeGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8ReelLifeGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8ReelLifeGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8ReelLifeGamesPayout,
		},
		"HS8_plt": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8PlaytechBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8PlaytechSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8PlaytechRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8PlaytechBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8PlaytechPayout,
		},
		"HS8_reg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8RelaxGamingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8RelaxGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8RelaxGamingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8RelaxGamingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8RelaxGamingPayout,
		},
		"HS8_rts": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8RTGSlotsBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8RTGSlotsSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8RTGSlotsRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8RTGSlotsBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8RTGSlotsPayout,
		},
		"HS8_qbt": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8QubitGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8QubitGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8QubitGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8QubitGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8QubitGamesPayout,
		},
		"HS8_osp": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS81Spin4WinBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS81Spin4WinSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS81Spin4WinRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS81Spin4WinBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS81Spin4WinPayout,
		},
		"HS8_nrl": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8NorthernLightsGamingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8NorthernLightsGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8NorthernLightsGamingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8NorthernLightsGamingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8NorthernLightsGamingPayout,
		},
		"HS8_rfg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8ReflexGamingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8ReflexGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8ReflexGamingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8ReflexGamingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8ReflexGamingPayout,
		},
		"HS8_hrg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8HotRiseGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8HotRiseGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8HotRiseGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8HotRiseGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8HotRiseGamesPayout,
		},
		"HS8_bpg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8BulletproofGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8BulletproofGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8BulletproofGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8BulletproofGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8BulletproofGamesPayout,
		},
		"HS8_gmv": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8GameVyBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8GameVySettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8GameVyRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8GameVyBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8GameVyPayout,
		},
		"HS8_jly": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8JellyBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8JellySettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8JellyRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8JellyBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8JellyPayout,
		},
		"HS8_hbg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8HungryBearGamingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8HungryBearGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8HungryBearGamingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8HungryBearGamingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8HungryBearGamingPayout,
		},
		"HS8_png": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8PlaynGoBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8PlaynGoSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8PlaynGoRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8PlaynGoBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8PlaynGoPayout,
		},
		"HS8_ygg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8YggdrasilBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8YggdrasilSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8YggdrasilRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8YggdrasilBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8YggdrasilPayout,
		},
		"HS8_bbg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8BangBangGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8BangBangGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8BangBangGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8BangBangGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8BangBangGamesPayout,
		},
		"HS8_brg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8BoomerangStudiosBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8BoomerangStudiosSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8BoomerangStudiosRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8BoomerangStudiosBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8BoomerangStudiosPayout,
		},
		"HS8_jil": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8JiliGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8JiliGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8JiliGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8JiliGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8JiliGamesPayout,
		},
		"HS8_psh": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8PushGamingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8PushGamingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8PushGamingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8PushGamingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8PushGamingPayout,
		},
		"HS8_pnt": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8PrintStudiosBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8PrintStudiosSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8PrintStudiosRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8PrintStudiosBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8PrintStudiosPayout,
		},
		"HS8_qps": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8QuickspinBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8QuickspinSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8QuickspinRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8QuickspinBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8QuickspinPayout,
		},
		"HS8_ftp": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS84ThePlayerBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS84ThePlayerSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS84ThePlayerRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS84ThePlayerBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS84ThePlayerPayout,
		},
		"HS8_bng": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8BoongoBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8BoongoSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8BoongoRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8BoongoBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8BoongoPayout,
		},
		"HS8_wfg": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8WinFastGamesBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8WinFastGamesSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8WinFastGamesRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8WinFastGamesBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8WinFastGamesPayout,
		},
		"HS8_rbc": {
			K_ST8CHANGETYPEBET:      BalanceCReasonHS8RabcatGamblingBet,
			K_ST8CHANGETYPESETTLE:   BalanceCReasonHS8RabcatGamblingSettle,
			K_ST8CHANGETYPEROLLBACK: BalanceCReasonHS8RabcatGamblingRollback,
			K_ST8CHANGETYPEBuyin:    BalanceCReasonHS8RabcatGamblingBuyin,
			K_ST8CHANGETYPEPayout:   BalanceCReasonHS8RabcatGamblingPayout,
		},
	}

	// 获取对应品牌的账变类型映射
	if brandTypes, exists := changeTypeMap[brand]; exists {
		// 获取对应操作类型的账变类型
		if changeType, ok := brandTypes[t]; ok {
			goldType = changeType
		}
	}
	return
}
