server:
  env: dev
  debug: true
  snowflakenode: 1 #分布式id生成器节点
  project: x_hash #系统名称
  module: clientapi #模块名称
  dbprefix: x_ #数据库前缀
  local: false
  http:
    http:
      port: 4533
  token:
    host: ************* #*************
    port: 19879 #6379
    db: 6
    password: dT&@h3ID^scKCahF #Ho9mpyeqaILEOfjM
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
    lifetime: 2592000 #token过期时长
  redis:
    host: ************* #*************
    port: 19879 #6379
    db: 5
    password: dT&@h3ID^scKCahF #Ho9mpyeqaILEOfjM
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
  credis:
    host: ************* #*************
    port: 19879 #6379
    db: 5
    password: dT&@h3ID^scKCahF #Ho9mpyeqaILEOfjM
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
  db:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_game
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: true
  dbStat:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_stat
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: true
image:
  url: https://hash98-pro.s3.ap-northeast-1.amazonaws.com
chat:
  urlx: http://testplant.wsx1205.com
  appid: sc911747328180412444337201808189
  appsecret: ZcE7ebUdp8tv84fG1Ab4rpawLxhMssMOqNQB57vzTtyy4C0ZHkCLkDQZVRrObCPA

lotteryapi: #哈希彩票
  url1x: https://indiaapi.x55lajj3.com
  url2x: https://indiaapi.wsx1304.com
  key: 482f132ad1fd47e4b4ec2806848571eb

qipaiapi1: #棋牌
  urlx: http://lxqpapi.dyysfjq.com
  key: 3a6a7610983dd0d329e992b98a4d9f6f
  company: 1990687_chessapilixin
  theme: S007
  agent: 1990702

qipaiapi2: #电子
  urlx: http://lxqpapi.dyysfjq.com
  key: ee9187bfea2255cfc44f36f40bf21e85
  company: 1990687_chessapilxwfgfg
  theme: S007
  agent: 1990704

sms:
  url: http://sms.nbfmg.com/api/nation/method?
  path: cmd=SendVerificationCode&signature=%s&user=%s&mobile_Phone=%s&message=%s
  account: msg_hx
  key: IANmYkfeoEyslODgN1Re

cn_sms:
  url: http://sms.nbfmg.com/api/ktchinese/method?
  path: cmd=SendVerificationCode&signature=%s&user=%s&mobile_Phone=%s&message=%s
  account: msg_hx_cn
  key: 8ciQ601loVVa1WyAQNiP

email:
  url: https://api.onbuka.com/v3/email/sendEmail
  apikey: sqyR559f
  secret: GvGw8SPg
  appid: c2jrMNTa
  from_email_address: <EMAIL>

xiaoyouxi: #小游戏
  urlx: https://open-api.t1games.app
  merchant_code: t7rNxhYAnZ7WR3dRr2I2s
  secure_key: kZiTSIsx1f9PgVtN3zcwV
  sign_key: SIZ709a23SVu3R1YnZFjk

hashmakerapi: http://127.0.0.1:1524
hashmakerethapi: http://127.0.0.1:4536
hashmakerbscapi: http://127.0.0.1:4537

yibi:
  url: https://test-ybf-wallet-api.dcops.cc/wallet-api
  key: 3L58A
  aeskey: Pcs5JyekJz2ywPWp
  md5key: g2PgyZz65ByZ8erP
wm:
  urlx: http://*************:1100 #https://wmwb-001.wmapi88.com
  vendor: a232lxphp
  signature: e5d966662d8cc7923fca7eb5559596fc

evo:
  url: http://*************:1100 #https://api.luckylivegames.com
  urladmin: http://*************:1100
  merchant: 196017
  currency: THB
  key: f77srvzj7wcvszql
  token: af7ed977b3369a809d76423c0a9e377d

easybet:
  urlx: http://*************:1100 #http://qa1.api.sport.nqsf9emow.com:27799  http://*************:1100
  platid: 10082
  key: a4xANJhWzf7ojn0FM21X

pg:
  urlx: http://*************:1100 #https://api.pg-bo.me/external
  gameurl: https://m.pgsoft-games.com
  operator_token: bb0b34ac6ca6eb6a999a713c680da394
  secret_key: b935579936bcb9b9898df3dd9ca61521
  currency: PHP

pp:
  urlx: https://api.prerelease-env.biz
  key: ok_hk
  secret_key: testKey
  currency: THB

ag:
  agent: EM2_AGIN
  gci_url: https://gci.qkj2081.com/forwardGame.do
  gi_url: https://gi.qaz2031.com/doBusiness.do
  deskey: jedDjTVE
  md5key: h2ZnAvEzFkgv
  currency: INR

astar:
  url: https://api.astar666.com
  channel_id: channel_id
  secure_key: rRAdU8iI1XfaWmff
  game_url: https://astar111.com
  report_url: https://api.astar66.com
  vendor_token: 072727fb-6c99-4a77-b1f2-8945067fd403
  currency: CNY

kaiyuan:
  url: https://wc1-api.uaphl791.com
  agent_id: 73560
  aeskey: 6522CE824C5D2061
  md5key: 4D146AE71F560A8B
  currency: CNY

easybetsp:
  url: http://qa1.api.sport.nqsf9emow.com:27799
  plat_id: 10082
  secure_key: a4xANJhWzf7ojn0FM21X
  game_id: 1
  game_name: Soccer

aws:
  email:
    access_key_id: AKIA2UC3B6TVDVGZ5PW
    secret_access_key: EXddrUm3kkJ5vhNtLGR3f8AroOM6YVO0OPcRBKbm

adminapi: http://127.0.0.1:4534
adminapi_readonly: http://127.0.0.1:4634
tgservice: http://127.0.0.1:4514
sms_debug: 100
tronscangoapi: http://*************:8810
lucky_admin: https://lucky-admin.zzyyjsk.com
tgbotapi: https://tgbot.flea8525.vip
websocketapi: http://127.0.0.1:4533/api/ws,https://clientview5.allgame.top/api/ws #当部署多个client时 配置多个余额变动通知地址,","号隔开http://127.0.0.1:4533/api/ws,http://xx.22