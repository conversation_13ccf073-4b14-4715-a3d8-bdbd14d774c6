// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipWeekly(db *gorm.DB, opts ...gen.DOOption) xVipWeekly {
	_xVipWeekly := xVipWeekly{}

	_xVipWeekly.xVipWeeklyDo.UseDB(db, opts...)
	_xVipWeekly.xVipWeeklyDo.UseModel(&model.XVipWeekly{})

	tableName := _xVipWeekly.xVipWeeklyDo.TableName()
	_xVipWeekly.ALL = field.NewAsterisk(tableName)
	_xVipWeekly.ID = field.NewInt32(tableName, "Id")
	_xVipWeekly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xVipWeekly.RecordStartDate = field.NewTime(tableName, "RecordStartDate")
	_xVipWeekly.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipWeekly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipWeekly.UserID = field.NewInt32(tableName, "UserId")
	_xVipWeekly.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xVipWeekly.State = field.NewInt32(tableName, "State")
	_xVipWeekly.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xVipWeekly.GetTime = field.NewTime(tableName, "GetTime")

	_xVipWeekly.fillFieldMap()

	return _xVipWeekly
}

type xVipWeekly struct {
	xVipWeeklyDo xVipWeeklyDo

	ALL             field.Asterisk
	ID              field.Int32
	RecordDate      field.Time
	RecordStartDate field.Time // 记录开始日期
	SellerID        field.Int32
	ChannelID       field.Int32
	UserID          field.Int32
	VipLevel        field.Int32   // vip等级
	State           field.Int32   // 状态 1 未领取 2已领取
	RewardAmount    field.Float64 // 领取金额
	GetTime         field.Time    // 领取时间

	fieldMap map[string]field.Expr
}

func (x xVipWeekly) Table(newTableName string) *xVipWeekly {
	x.xVipWeeklyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipWeekly) As(alias string) *xVipWeekly {
	x.xVipWeeklyDo.DO = *(x.xVipWeeklyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipWeekly) updateTableName(table string) *xVipWeekly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.RecordStartDate = field.NewTime(table, "RecordStartDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.State = field.NewInt32(table, "State")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.GetTime = field.NewTime(table, "GetTime")

	x.fillFieldMap()

	return x
}

func (x *xVipWeekly) WithContext(ctx context.Context) *xVipWeeklyDo {
	return x.xVipWeeklyDo.WithContext(ctx)
}

func (x xVipWeekly) TableName() string { return x.xVipWeeklyDo.TableName() }

func (x xVipWeekly) Alias() string { return x.xVipWeeklyDo.Alias() }

func (x xVipWeekly) Columns(cols ...field.Expr) gen.Columns { return x.xVipWeeklyDo.Columns(cols...) }

func (x *xVipWeekly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipWeekly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["RecordStartDate"] = x.RecordStartDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["State"] = x.State
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["GetTime"] = x.GetTime
}

func (x xVipWeekly) clone(db *gorm.DB) xVipWeekly {
	x.xVipWeeklyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipWeekly) replaceDB(db *gorm.DB) xVipWeekly {
	x.xVipWeeklyDo.ReplaceDB(db)
	return x
}

type xVipWeeklyDo struct{ gen.DO }

func (x xVipWeeklyDo) Debug() *xVipWeeklyDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipWeeklyDo) WithContext(ctx context.Context) *xVipWeeklyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipWeeklyDo) ReadDB() *xVipWeeklyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipWeeklyDo) WriteDB() *xVipWeeklyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipWeeklyDo) Session(config *gorm.Session) *xVipWeeklyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipWeeklyDo) Clauses(conds ...clause.Expression) *xVipWeeklyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipWeeklyDo) Returning(value interface{}, columns ...string) *xVipWeeklyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipWeeklyDo) Not(conds ...gen.Condition) *xVipWeeklyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipWeeklyDo) Or(conds ...gen.Condition) *xVipWeeklyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipWeeklyDo) Select(conds ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipWeeklyDo) Where(conds ...gen.Condition) *xVipWeeklyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipWeeklyDo) Order(conds ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipWeeklyDo) Distinct(cols ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipWeeklyDo) Omit(cols ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipWeeklyDo) Join(table schema.Tabler, on ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipWeeklyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipWeeklyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipWeeklyDo) Group(cols ...field.Expr) *xVipWeeklyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipWeeklyDo) Having(conds ...gen.Condition) *xVipWeeklyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipWeeklyDo) Limit(limit int) *xVipWeeklyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipWeeklyDo) Offset(offset int) *xVipWeeklyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipWeeklyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipWeeklyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipWeeklyDo) Unscoped() *xVipWeeklyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipWeeklyDo) Create(values ...*model.XVipWeekly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipWeeklyDo) CreateInBatches(values []*model.XVipWeekly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipWeeklyDo) Save(values ...*model.XVipWeekly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipWeeklyDo) First() (*model.XVipWeekly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipWeekly), nil
	}
}

func (x xVipWeeklyDo) Take() (*model.XVipWeekly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipWeekly), nil
	}
}

func (x xVipWeeklyDo) Last() (*model.XVipWeekly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipWeekly), nil
	}
}

func (x xVipWeeklyDo) Find() ([]*model.XVipWeekly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipWeekly), err
}

func (x xVipWeeklyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipWeekly, err error) {
	buf := make([]*model.XVipWeekly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipWeeklyDo) FindInBatches(result *[]*model.XVipWeekly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipWeeklyDo) Attrs(attrs ...field.AssignExpr) *xVipWeeklyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipWeeklyDo) Assign(attrs ...field.AssignExpr) *xVipWeeklyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipWeeklyDo) Joins(fields ...field.RelationField) *xVipWeeklyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipWeeklyDo) Preload(fields ...field.RelationField) *xVipWeeklyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipWeeklyDo) FirstOrInit() (*model.XVipWeekly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipWeekly), nil
	}
}

func (x xVipWeeklyDo) FirstOrCreate() (*model.XVipWeekly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipWeekly), nil
	}
}

func (x xVipWeeklyDo) FindByPage(offset int, limit int) (result []*model.XVipWeekly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipWeeklyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipWeeklyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipWeeklyDo) Delete(models ...*model.XVipWeekly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipWeeklyDo) withDO(do gen.Dao) *xVipWeeklyDo {
	x.DO = *do.(*gen.DO)
	return x
}
