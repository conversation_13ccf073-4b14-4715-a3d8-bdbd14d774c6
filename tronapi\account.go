package tronapi

import (
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"io"
	"time"
)

type Account struct{}

func (c *Account) AccountV2(address string) (rsp *AccountV2Rsp, err error) {
	url := baseUrl + fmt.Sprintf("/accountv2?address=%s", address)
	resp, err := Client().HttpGet(url)
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		return nil, err
	}
	defer resp.Response().Body.Close()
	rsp = new(AccountV2Rsp)
	err = json.Unmarshal(body, rsp)
	return rsp, err
}

func (c *Account) Tokens(address string) (rsp *TokensRsp, err error) {
	url := baseUrl + fmt.Sprintf("/account/tokens?address=%s", address)
	resp, err := Client().HttpGet(url)
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		return nil, err
	}
	defer resp.Response().Body.Close()
	rsp = new(TokensRsp)
	err = json.Unmarshal(body, rsp)
	return rsp, err
}

func (c *Account) GetCreatTime(address string) (creatAt time.Time, err error) {
	creatAt = time.UnixMilli(0)
	rsp, err := c.AccountV2(address)
	if err != nil {
		return creatAt, err
	}
	creatAt = time.UnixMilli(rsp.DateCreated)
	return creatAt, nil
}

func (c *Account) GetTotalBalanceInUSD(address string) (total decimal.Decimal, err error) {
	total = decimal.New(0, 0)
	rsp, err := c.Tokens(address)
	if err != nil {
		return total, err
	}
	for _, data := range rsp.Data {
		//if data.TokenType == "trc10" && data.TokenName == "trx" {
		//	total = total.Add(data.Quantity.Mul(data.TokenPriceInUsd))
		//} else if data.TokenType == "trc20" && data.TokenName == "Tether USD" {
		//	total = total.Add(data.Quantity.Mul(data.TokenPriceInUsd))
		//}
		total = total.Add(data.Quantity.Mul(data.TokenPriceInUsd))
	}
	return total, nil
}
