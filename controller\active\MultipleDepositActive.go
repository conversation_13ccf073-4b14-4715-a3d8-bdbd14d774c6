package active

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third"
	xHashModel "xserver/gormgen/xHashGame/model"
	"xserver/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// MultipleDepositGift 处理用户复充活动
func MultipleDepositGift(activeId int, level int, ctx *abugo.AbuHttpContent) (int, error) {
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	// 用户表相关
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 不再使用用户每日统计表

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 用户钱包表相关
	userWalletTb := server.DaoxHashGame().XUserWallet
	userWalletDb := server.DaoxHashGame().XUserWallet.WithContext(context.Background())

	defer recover() // 捕获可能的panic异常
	// 获取当前时间
	now := carbon.Parse(carbon.Now().String()).StdTime()

	// 第一步：获取活动定义，验证活动有效性
	activeDefine, err := GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("MultipleDepositGift getADefineInfo err", err)
		return utils.ActiveINotDefine, errors.New("活动不存在")
	}

	// 检查活动状态是否开启
	if activeDefine.State != utils.ActiveStateOpen {
		return utils.ActiveINotDefine, errors.New("活动已关闭")
	}

	// 校验活动时间范围
	if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
		startTime := time.UnixMilli(activeDefine.EffectStartTime)
		endTime := time.UnixMilli(activeDefine.EffectEndTime)
		if now.Before(startTime) || now.After(endTime) {
			return utils.ActiveINotDefine, errors.New("不在活动时间内")
		}
	}

	// 第二步：检查用户是否已申请过该活动
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
		Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("MultipleDepositGift activeRewardAudit err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}
	if activeRewardAudit != nil {
		// 用户已经申请过该活动
		return utils.ActiveIAlreadyApply, errors.New("已领取过该活动奖励")
	}

	// 第三步：解析活动配置
	// 解析活动档位配置
	var configData []model.FirstDepositGiftConfig
	err = json.Unmarshal([]byte(activeDefine.Config), &configData)
	if err != nil {
		return utils.ActiveIParamsIllegal, errors.New("活动配置有误")
	}

	// 解析活动基础配置
	var baseData model.FirstDepositGiftBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
	if err != nil {
		return utils.ActiveIParamsIllegal, errors.New("活动配置有误")
	}

	// 根据level参数选择对应的档位配置
	var selectData model.FirstDepositGiftConfig
	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
		}
	}
	if selectData.ID == 0 {
		// 未找到对应档位
		return utils.ActiveIParamsIllegal, errors.New("参数错误")
	}

	// 第四步：获取用户信息并验证用户资格
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.Email).
		Where(userTb.UserID.Eq(int32(token.UserId))).
		Where(userTb.SellerID.Eq(int32(sellerId))).
		Where(userTb.ChannelID.Eq(int32(channelId))).
		First()
	if err != nil {
		logs.Error("MultipleDepositGift user err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 检查是否为测试账号
	if user.IsTest == 1 {
		return utils.ActiveIIsTest, errors.New("该玩家禁止领取")
	}

	// 检查邮箱绑定要求
	if baseData.IsBindEmail && user.Email == "" {
		return utils.ActiveINocondition, errors.New("请先绑定邮箱")
	}

	// 检查钱包地址有效性
	if baseData.IsValidWallet {
		// 查询用户钱包地址

		// 查询用户是否有已验证的钱包地址
		userWallet, err := userWalletDb.
			Where(userWalletTb.UserID.Eq(user.UserID)).
			Where(userWalletTb.SellerID.Eq(int32(sellerId))).
			Where(userWalletTb.ChannelID.Eq(int32(channelId))).
			Where(userWalletTb.State.Eq(1)). // 状态 1已验证 2未验证
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("MultipleDepositGift query wallet address err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if userWallet == nil || userWallet.Address == "" {
			return utils.ActiveINocondition, errors.New("请先绑定有效的钱包地址")
		}
	}

	// 计算注册时间和有效时间范围
	registerTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).StartOfDay().String()).StdTime()
	endFindTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay)).StartOfDay().String()).StdTime()

	// 检查是否活动期间内注册的账号
	if baseData.IsDuringReg {
		// 检查用户注册时间是否在活动期间内
		activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
		activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
		if user.RegisterTime.Before(activityStartTime) || user.RegisterTime.After(activityEndTime) {
			return utils.ActiveINocondition, errors.New("仅限活动期间内注册的账号参与")
		}
	}

	// 计算活动领取截止时间
	receiveTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay + baseData.ReceiveDay)).StartOfDay().String()).StdTime()
	if now.After(receiveTime) {
		// 已超过领取时间
		return utils.ActiveINocondition, errors.New("已过了领取时间")
	}

	// 获取用户IP
	userIPInfo, err := userDb.Select(userTb.LoginIP).
		Where(userTb.UserID.Eq(user.UserID)).
		Where(userTb.SellerID.Eq(int32(sellerId))).
		Where(userTb.ChannelID.Eq(int32(channelId))).
		First()
	if err != nil {
		logs.Error("MultipleDepositGift query user IP err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}
	userIP := userIPInfo.LoginIP

	// 检查IP限制
	if userIP != "" {
		// 检查IP是否在黑名单中
		if baseData.BlockedIPList != "" {
			blockedIPs := strings.Split(baseData.BlockedIPList, ",")
			for _, blockedIP := range blockedIPs {
				if blockedIP == userIP {
					return utils.ActiveINocondition, errors.New("您的IP被限制参与此活动")
				}
			}
		}

		// 检查同IP最大领取次数
		if baseData.MaxIPAttempts > 0 {
			var ipAttemptCount int64
			// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
			ipAttemptCount, err = activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
				Where(activeRewardAuditTb.UserIP.Eq(userIP)).
				Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
				Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
				Count()
			if err != nil {
				logs.Error("MultipleDepositGift query IP attempt count err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}

			if int(ipAttemptCount) >= baseData.MaxIPAttempts {
				return utils.ActiveINocondition, errors.New("您的IP已达到最大领取次数")
			}
		}
	}

	// 获取用户设备ID，用于后续保存到活动审核表
	userDeviceInfo, err := userDb.Select(userTb.LoginDeviceID).
		Where(userTb.UserID.Eq(user.UserID)).
		Where(userTb.SellerID.Eq(int32(sellerId))).
		Where(userTb.ChannelID.Eq(int32(channelId))).
		First()
	if err != nil {
		logs.Error("MultipleDepositGift query device ID err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}
	deviceID := userDeviceInfo.LoginDeviceID

	// 检查同设备号是否可参与
	if !baseData.IsDeviceLimit && deviceID != "" {
		var deviceAttemptCount int64
		// 通过DeviceID字段查询，不限制活动ID，检查所有活动
		// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
		deviceAttemptCount, err = activeRewardAuditDb.
			Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
			Where(activeRewardAuditTb.DeviceID.Eq(deviceID)).
			Where(activeRewardAuditTb.ActiveID.In(utils.MultipleDepositGift)). // 只检查复充活动
			Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
			Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
			Count()
		if err != nil {
			logs.Error("MultipleDepositGift query device attempt count err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if deviceAttemptCount > 0 {
			return utils.ActiveINocondition, errors.New("同一设备只能领取一次")
		}

		// 记录日志，方便调试
		logs.Info("MultipleDepositGift 设备ID验证: 设备ID=%s, 活动ID=%d, 设备已领取次数=%d",
			deviceID, activeId, deviceAttemptCount)
	}

	// 检查同ID最大领取次数
	if baseData.MaxIDAttempts > 0 {
		var idAttemptCount int64
		// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
		idAttemptCount, err = activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
			Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
			Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
			Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
			Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
			Count()
		if err != nil {
			logs.Error("MultipleDepositGift query ID attempt count err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if int(idAttemptCount) >= baseData.MaxIDAttempts {
			return utils.ActiveINocondition, errors.New("您已达到最大领取次数")
		}
	}

	// 第五步：查询用户流水数据
	// 使用封装的函数计算用户总流水
	// 获取投注限制参数
	minBetAmount := baseData.MinBetAmount
	maxBetAmount := baseData.MaxBetAmount

	// 如果配置了最小或最大投注限制，则排除超出限制的流水
	isExcludeBetLimit := !minBetAmount.IsZero() || !maxBetAmount.IsZero()

	// 如果配置了投注限制，则在日志中记录
	if isExcludeBetLimit {
		logs.Info("MultipleDepositGift 配置了投注限制: minBetAmount=%v, maxBetAmount=%v, 活动方式=%v",
			minBetAmount, maxBetAmount, baseData.ActivityMethod)
	}

	// 无论是前置模式还是后置模式，都需要计算用户总流水
	// 前置模式：用于记录用户当前流水，方便后续审核
	// 后置模式：用于检查用户流水是否达到要求
	total, err := CalculateUserTotalFlow(user, registerTime, endFindTime, minBetAmount, maxBetAmount, isExcludeBetLimit)
	if err != nil {
		logs.Error("MultipleDepositGift CalculateUserTotalFlow err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 第六步：查询用户所有充值记录
	recharges, err := rechargeDb.
		Where(rechargeTb.UserID.Eq(int32(token.UserId))).
		Where(rechargeTb.SellerID.Eq(int32(sellerId))).
		Where(rechargeTb.ChannelID.Eq(int32(channelId))).
		Where(rechargeTb.State.Eq(5)). // 状态为成功
		Where(rechargeTb.CreateTime.Between(registerTime, endFindTime)).
		Order(rechargeTb.CreateTime).
		Find()
	if err != nil {
		logs.Error("MultipleDepositGift recharge err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 检查充值次数是否足够
	rechargeCount := baseData.RechargeCount

	if len(recharges) < int(rechargeCount) {
		return utils.ActiveINocondition, fmt.Errorf("请完成第%d次充值", rechargeCount)
	}

	// 获取第N次充值记录
	recharge := recharges[rechargeCount-1]

	// 检查用户在复充成功后是否有投注记录
	hasBetAfterRecharge, err := CheckUserMultipleDepositAfterRecharge(
		int(token.UserId), sellerId, channelId,
		recharge.CreateTime, now)
	if err != nil {
		logs.Error("MultipleDepositGift CheckUserMultipleDepositAfterRecharge err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	if hasBetAfterRecharge {
		return utils.ActiveINocondition, errors.New("对不起,因您在申请活动前已经投注,已不符合复充活动要求")
	}

	// 检查用户是否完成第几次充值
	hasEnoughDeposits, actualCount, err := CheckUserMultipleDepositCount(
		int32(sellerId), int32(channelId), int32(activeId), int32(user.UserID), baseData.RechargeCount)
	if err != nil {
		logs.Error("MultipleDepositGift CheckUserMultipleDepositCount err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	if !hasEnoughDeposits {
		return utils.ActiveINocondition, fmt.Errorf("请完成第%d次充值,当前充值次数:%d", baseData.RechargeCount, actualCount)
	}

	logs.Info("MultipleDepositGift 充值次数检查通过: 用户ID=%d, 要求次数=%d, 实际次数=%d",
		token.UserId, baseData.RechargeCount, actualCount)

	// 检查用户是否参与复充活动
	hasMultipleDepositActive := CheckUserMultipleDepositActive(int32(token.UserId))
	if hasMultipleDepositActive {
		return utils.ActiveIAlreadyApply, errors.New("您已参与过复充活动")
	}

	logs.Info("MultipleDepositGift 复充活动参与检查通过: 用户ID=%d, 未参与过复充活动", token.UserId)

	// 第七步：验证充值金额和流水是否满足要求
	rechargetDecimal := decimal.NewFromFloat(recharge.RealAmount)

	// 检查首充金额是否达到要求
	firstChargeUstdLimit := selectData.FirstChargeUstdLimit
	rechargetEqual := rechargetDecimal.GreaterThanOrEqual(firstChargeUstdLimit)
	if !rechargetEqual {
		// 首充金额不满足要求
		return utils.ActiveINocondition, errors.New("充值金额不足")
	}

	// 根据活动方式处理
	// 如果 ActivityMethod 不是 1（前置模式），则默认使用 2（后置模式）
	// 定义一个变量用于存储总流水要求，以便后续保存到MinLiuShui字段
	var totalRequiredLiushui decimal.Decimal

	if baseData.ActivityMethod == 1 {
		logs.Info("MultipleDepositGift 前置模式，不检查流水，但会记录当前流水，用于后续提现检查")

		// 计算流水要求，用于后续提现检查
		var requiredLiushui decimal.Decimal
		switch baseData.BetType {
		case 1: // 真金流水
			// 使用真金流水倍数计算
			requiredLiushui = rechargetDecimal.Mul(selectData.LiushuiMultiple)
		case 2: // 彩金流水
			// 计算赠送金额（彩金）
			giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
			giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
			giveLimit := selectData.GiveLimit                                          // 奖励上限

			// 如果计算的奖励超过上限，则使用上限值
			finalGiveAmount := giveAmount
			if giveAmount.GreaterThan(giveLimit) {
				finalGiveAmount = giveLimit
			}

			// 彩金流水 = 赠送金额 * 彩金流水倍数
			requiredLiushui = finalGiveAmount.Mul(selectData.BonusMultiple)

			logs.Info("MultipleDepositGift 前置模式彩金流水计算: 充值金额=%v, 赠送比例=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v",
				rechargetDecimal, selectData.GiveProportion, finalGiveAmount, selectData.BonusMultiple, requiredLiushui)
		case 3: // 彩金+真金流水
			// 计算赠送金额
			giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
			giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
			giveLimit := selectData.GiveLimit                                          // 奖励上限

			// 如果计算的奖励超过上限，则使用上限值
			finalGiveAmount := giveAmount
			if giveAmount.GreaterThan(giveLimit) {
				finalGiveAmount = giveLimit
			}

			// 真金流水 = 充值金额 * 真金流水倍数
			realMoneyFlow := rechargetDecimal.Mul(selectData.LiushuiMultiple)
			// 彩金流水 = 赠送金额 * 彩金流水倍数
			bonusFlow := finalGiveAmount.Mul(selectData.BonusMultiple)
			// 总流水要求 = 真金流水 + 彩金流水
			requiredLiushui = realMoneyFlow.Add(bonusFlow)

			logs.Info("MultipleDepositGift 前置模式彩金+真金流水计算: 充值金额=%v, 真金流水倍数=%v, 真金流水=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v, 总流水要求=%v",
				rechargetDecimal, selectData.LiushuiMultiple, realMoneyFlow, finalGiveAmount, selectData.BonusMultiple, bonusFlow, requiredLiushui)
		}

		// 记录流水要求，方便调试
		logs.Info("MultipleDepositGift 前置模式流水要求: %s, 当前流水: %s", requiredLiushui.StringFixed(2), total.StringFixed(2))

		// 保存总流水要求
		totalRequiredLiushui = requiredLiushui
	} else {
		// 根据打码条件计算流水要求，先打流水再领奖励，检查流水是否已经达到要求
		var requiredLiushui decimal.Decimal
		switch baseData.BetType {
		case 1: // 真金流水
			// 使用真金流水倍数计算
			requiredLiushui = rechargetDecimal.Mul(selectData.LiushuiMultiple)
		case 2: // 彩金流水
			// 计算赠送金额（彩金）
			giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
			giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
			giveLimit := selectData.GiveLimit                                          // 奖励上限

			// 如果计算的奖励超过上限，则使用上限值
			finalGiveAmount := giveAmount
			if giveAmount.GreaterThan(giveLimit) {
				finalGiveAmount = giveLimit
			}

			// 彩金流水 = 赠送金额 * 彩金流水倍数
			requiredLiushui = finalGiveAmount.Mul(selectData.BonusMultiple)

			logs.Info("MultipleDepositGift 后置模式彩金流水计算: 充值金额=%v, 赠送比例=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v",
				rechargetDecimal, selectData.GiveProportion, finalGiveAmount, selectData.BonusMultiple, requiredLiushui)
		case 3: // 彩金+真金流水
			// 计算赠送金额
			giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
			giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
			giveLimit := selectData.GiveLimit                                          // 奖励上限

			// 如果计算的奖励超过上限，则使用上限值
			finalGiveAmount := giveAmount
			if giveAmount.GreaterThan(giveLimit) {
				finalGiveAmount = giveLimit
			}

			// 真金流水 = 充值金额 * 真金流水倍数
			realMoneyFlow := rechargetDecimal.Mul(selectData.LiushuiMultiple)
			// 彩金流水 = 赠送金额 * 彩金流水倍数
			bonusFlow := finalGiveAmount.Mul(selectData.BonusMultiple)
			// 总流水要求 = 真金流水 + 彩金流水
			requiredLiushui = realMoneyFlow.Add(bonusFlow)

			logs.Info("MultipleDepositGift 后置模式彩金+真金流水计算: 充值金额=%v, 真金流水倍数=%v, 真金流水=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v, 总流水要求=%v",
				rechargetDecimal, selectData.LiushuiMultiple, realMoneyFlow, finalGiveAmount, selectData.BonusMultiple, bonusFlow, requiredLiushui)
		}

		// 保存总流水要求
		totalRequiredLiushui = requiredLiushui

		// 检查流水是否达到要求
		totalEqual := total.GreaterThanOrEqual(requiredLiushui)
		if !totalEqual {
			// 计算差额并保留小数点后2位
			diff := requiredLiushui.Sub(total)
			// 使用Round(2)进行四舍五入，然后使用StringFixed(2)格式化为2位小数
			roundedDiff := diff.Round(2)
			logs.Info("MultipleDepositGift 流水不足计算: 需要流水=%s, 当前流水=%s, 差额=%s, 四舍五入后=%s",
				requiredLiushui.StringFixed(2), total.StringFixed(2), diff.StringFixed(2), roundedDiff.StringFixed(2))
			return utils.ActiveINocondition, errors.New("流水不足，还需要" + roundedDiff.StringFixed(2))
		}
	}

	// 第八步：计算奖励金额
	var realAmount float64
	giveProportion := selectData.GiveProportion
	percentage := decimal.NewFromInt32(100)
	giveProportion = giveProportion.Div(percentage)       // 转换为小数比例
	giveLimit := selectData.GiveLimit                     // 奖励上限
	amountDecimal := rechargetDecimal.Mul(giveProportion) // 按比例计算奖励
	equal := amountDecimal.GreaterThanOrEqual(giveLimit)
	var withdrawLiuSuiAdd float64

	// 初始计算奖励金额
	var rewardDecimal decimal.Decimal
	if equal {
		rewardDecimal = giveLimit
	} else {
		rewardDecimal = amountDecimal
	}

	// 检查总派发和每日派发限制
	if baseData.TotalRewardLimit.GreaterThan(decimal.Zero) || baseData.DailyRewardLimit.GreaterThan(decimal.Zero) {
		// 定义变量，用于存储每日和总派发的检查结果
		var dailyCheckPassed, totalCheckPassed bool

		// 默认设置为通过，如果不需要检查则直接通过
		dailyCheckPassed = true
		totalCheckPassed = true

		// 1. 先检查当日领取条件
		if baseData.DailyRewardLimit.GreaterThan(decimal.Zero) {
			today := time.Now().Format("2006-01-02")
			type DailyRewardResult struct {
				DailyAmount float64
			}
			var dailyRewardResult DailyRewardResult
			// 使用Gen的方式查询，通过自定义条件表达式处理DATE函数
			todayStart, _ := time.Parse("2006-01-02", today)
			todayEnd := todayStart.Add(24 * time.Hour)

			// 首先查询所有状态的记录，了解数据库中的实际情况
			type AllRecordsResultSQL struct {
				TotalAmount sql.NullFloat64
				Count       sql.NullInt64
			}
			var allRecordsResultSQL AllRecordsResultSQL
			err = activeRewardAuditDb.Select(
				activeRewardAuditTb.Amount.Sum().As("TotalAmount"),
				activeRewardAuditTb.ID.Count().As("Count"),
			).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
				Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
				Scan(&allRecordsResultSQL)

			if err != nil {
				logs.Error("MultipleDepositGift query all records err", err)
			} else {
				var totalAmount float64
				var count int64
				if allRecordsResultSQL.TotalAmount.Valid {
					totalAmount = allRecordsResultSQL.TotalAmount.Float64
				}
				if allRecordsResultSQL.Count.Valid {
					count = allRecordsResultSQL.Count.Int64
				}
				logs.Info("MultipleDepositGift 所有记录查询结果: 活动ID=%v, 总金额=%v, 记录数=%v",
					activeId, totalAmount, count)
			}

			// 查询各种状态的记录数量
			type StateCountResult struct {
				AuditState  int32
				Count       int64
				TotalAmount float64
			}
			var stateCountResults []StateCountResult
			err = activeRewardAuditDb.Select(
				activeRewardAuditTb.AuditState,
				activeRewardAuditTb.ID.Count().As("Count"),
				activeRewardAuditTb.Amount.Sum().As("TotalAmount"),
			).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
				Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
				Group(activeRewardAuditTb.AuditState).
				Scan(&stateCountResults)

			if err != nil {
				logs.Error("MultipleDepositGift query state count err", err)
			} else {
				for _, result := range stateCountResults {
					logs.Info("MultipleDepositGift 状态统计: 活动ID=%v, 状态=%v, 记录数=%v, 总金额=%v",
						activeId, result.AuditState, result.Count, result.TotalAmount)
				}
			}

			// 查询今日已审核通过的记录，使用AuditTime字段
			type SumResult struct {
				DailyAmount sql.NullFloat64
			}
			var auditTimeSumResult SumResult
			err = activeRewardAuditDb.Select(activeRewardAuditTb.Amount.Sum().As("DailyAmount")).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
				Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
				Where(activeRewardAuditTb.AuditState.In(3, 4)).
				Where(activeRewardAuditTb.AuditTime.Between(todayStart, todayEnd)).
				Scan(&auditTimeSumResult)

			// 记录更详细的日志，方便调试
			logs.Info("MultipleDepositGift 每日派发限制查询: 活动ID=%v, 日期范围=%v至%v",
				activeId, todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

			if err != nil {
				logs.Error("MultipleDepositGift query daily reward amount err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}

			// 处理NULL值
			if auditTimeSumResult.DailyAmount.Valid {
				dailyRewardResult.DailyAmount = auditTimeSumResult.DailyAmount.Float64
			} else {
				dailyRewardResult.DailyAmount = 0
			}

			logs.Info("MultipleDepositGift 今日已审核通过记录: 活动ID=%v, 总金额=%v",
				activeId, dailyRewardResult.DailyAmount)

			dailyRewardAmount := decimal.NewFromFloat(dailyRewardResult.DailyAmount)

			// 计算每日可领取奖励的额度
			remainingDailyAmount := baseData.DailyRewardLimit.Sub(dailyRewardAmount)

			// 记录日志，方便调试
			logs.Info("MultipleDepositGift 每日派发限制检查: 今日已派发=%s, 本次预计派发=%s, 每日限制=%s, 剩余可派发=%s",
				dailyRewardAmount.StringFixed(2), rewardDecimal.StringFixed(2),
				baseData.DailyRewardLimit.StringFixed(2), remainingDailyAmount.StringFixed(2))

			// 检查原始奖励是否超过每日限制
			if rewardDecimal.GreaterThan(baseData.DailyRewardLimit) {
				// 如果原始奖励超过每日限制，不发放奖励
				logs.Warn("MultipleDepositGift 原始奖励超过每日限制: 应奖励=%s, 每日限制=%s",
					rewardDecimal.StringFixed(2), baseData.DailyRewardLimit.StringFixed(2))
				dailyCheckPassed = false
			} else if rewardDecimal.GreaterThan(remainingDailyAmount) {
				// 如果奖励金额大于剩余可派发金额，则不发放奖励
				logs.Warn("MultipleDepositGift 今日活动奖金额度不足: 应奖励=%s, 剩余可派发=%s, 每日限制=%s",
					rewardDecimal.StringFixed(2), remainingDailyAmount.StringFixed(2), baseData.DailyRewardLimit.StringFixed(2))
				dailyCheckPassed = false
			}
		}

		// 2. 再检查总领取条件
		if baseData.TotalRewardLimit.GreaterThan(decimal.Zero) {
			type TotalRewardResult struct {
				TotalAmount float64
			}
			var totalRewardResult TotalRewardResult
			// 只包含审核通过和自动通过的记录
			err = activeRewardAuditDb.Select(activeRewardAuditTb.Amount.Sum().As("TotalAmount")).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(int32(sellerId))).
				Where(activeRewardAuditTb.ChannelID.Eq(int32(channelId))).
				Where(activeRewardAuditTb.AuditState.In(3, 4)). // 只包含审核通过和自动通过的记录
				Scan(&totalRewardResult)
			if err != nil {
				logs.Error("MultipleDepositGift query total reward amount err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}
			totalRewardAmount := decimal.NewFromFloat(totalRewardResult.TotalAmount)

			// 计算总可领取奖励的额度
			remainingTotalAmount := baseData.TotalRewardLimit.Sub(totalRewardAmount)

			// 记录日志，方便调试
			logs.Info("MultipleDepositGift 总派发限制检查: 总礼金已派发=%s, 本次预计派发=%s, 总限制=%s, 剩余可派发=%s",
				totalRewardAmount.StringFixed(2), rewardDecimal.StringFixed(2),
				baseData.TotalRewardLimit.StringFixed(2), remainingTotalAmount.StringFixed(2))

			// 检查原始奖励是否超过总限制
			if rewardDecimal.GreaterThan(baseData.TotalRewardLimit) {
				// 如果原始奖励超过总限制，不发放奖励
				logs.Warn("MultipleDepositGift 原始奖励超过总限制: 应奖励=%s, 总限制=%s",
					rewardDecimal.StringFixed(2), baseData.TotalRewardLimit.StringFixed(2))
				totalCheckPassed = false
			} else if totalRewardAmount.Add(rewardDecimal).GreaterThan(baseData.TotalRewardLimit) {
				// 计算剩余可派发金额
				remainingAmount := baseData.TotalRewardLimit.Sub(totalRewardAmount)

				// 如果剩余金额小于原始奖励，则不发放奖励
				if remainingAmount.LessThan(rewardDecimal) {
					logs.Warn("MultipleDepositGift 活动奖金额度不足: 应奖励=%s, 剩余可派发=%s",
						rewardDecimal.StringFixed(2), remainingAmount.StringFixed(2))
					totalCheckPassed = false
				}
			}
		}

		// 3. 只有两个条件都满足才能发放奖励
		if !dailyCheckPassed {
			return utils.ActiveINocondition, errors.New("今日礼金额度不足")
		}

		if !totalCheckPassed {
			return utils.ActiveINocondition, errors.New("礼金额度已满")
		}
	}

	// 使用调整后的奖励金额和计算出的总流水要求
	// 使用计算出的总流水要求，而不是activeDefine.MinLiuShui
	withdrawLiuSuiAdd, _ = totalRequiredLiushui.Float64()
	realAmount, _ = rewardDecimal.Float64()

	// 第九步：准备保存活动数据
	configStr, _ := json.Marshal(configData)

	// 确保baseData中包含IsCalcActiveWager和RewardWalletType信息
	// 这样SaveActiveData函数可以通过解析baseData来获取这些信息
	baseConfigStr, _ := json.Marshal(baseData)
	totalLiushui, _ := total.Float64()
	firstRecharge, _ := rechargetDecimal.Float64()

	// 根据RewardWalletType设置奖励账户类型
	balanceCReason := utils.BalanceCReasonFirstDepositGift
	if baseData.RewardWalletType == 1 {
		// 如果是彩金账户，使用彩金相关的BalanceCReason
		balanceCReason = utils.BalanceCReasonFirstDepositGift // 这里可以替换为彩金账户对应的BalanceCReason
	}
	remark := fmt.Sprintf("第%v次充值活动", baseData.RechargeCount)
	// 构建活动数据保存信息
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         int(activeDefine.AuditType), // 审核类型
		SellerID:          int32(sellerId),             // 运营商ID
		ChannelID:         int32(channelId),            // 渠道ID
		ActiveId:          activeId,                    // 活动ID
		Level:             level,                       // 活动等级
		RealAmount:        realAmount,                  // 实际奖励金额
		TotalLiushui:      totalLiushui,                // 总流水
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,           // 提现流水增加值
		ActiveName:        activeDefine.Title,          // 活动名称
		ActiveMemo:        remark,                      // 活动备注
		BalanceCReason:    balanceCReason,              // 余额变更原因
		ConfigStr:         configStr,                   // 配置字符串
		BastConfigStr:     baseConfigStr,               // 基础配置字符串
		FirstRecharge:     firstRecharge,               // 首充金额
		TotalRecharge:     0.0,                         // 总充值金额
		MinLiuShui:        totalRequiredLiushui,        // 总流水要求
		UserIP:            userIP,                      // 用户IP
		DeviceID:          deviceID,                    // 设备ID
	}

	// 第十步：保存活动数据并发放奖励
	err = SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("MultipleDepositGift saveActiveData err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 根据审核类型返回不同的提示信息
	if saveActiveDataInfo.AuditType == utils.ActiveAuditRenGong {
		logs.Info("MultipleDepositGift 人工审核模式: 用户ID=%d, 活动ID=%d, 申请已提交等待审核", user.UserID, activeId)
		// 第十一步：向 x_user_reduce 表写入数据 计算需要扣减的流水
	} else {
		logs.Info("MultipleDepositGift 自动审核模式: 用户ID=%d, 活动ID=%d, 奖励已自动发放", user.UserID, activeId)
		// 第十一步：向 x_user_reduce 表写入数据 计算需要扣减的流水
	}
	reduceLiuShui := totalRequiredLiushui
	currentTime := time.Now()

	if baseData.IsCalcActiveWager {
		userReduceTb := server.DaoxHashGame().XUserReduce
		userReduceDb := server.DaoxHashGame().XUserReduce.WithContext(context.Background())

		// 查询用户是否已有记录
		userReduce, err := userReduceDb.Where(userReduceTb.UserID.Eq(int32(token.UserId))).First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("MultipleDepositGift query x_user_reduce err", err)
			// 不影响主流程，继续返回成功
		} else {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 用户不存在记录，创建新记录
				// 将 decimal.Decimal 类型转换为 float64 类型
				reduceLiuShuiFloat, _ := reduceLiuShui.Float64()
				newUserReduce := xHashModel.XUserReduce{
					UserID:            int32(token.UserId),
					ReduceLiuShui:     reduceLiuShuiFloat,
					RealReduceLiuShui: 0, // 使用 0 代替 decimal.Zero
					CreateTime:        currentTime,
					UpdateTime:        currentTime,
				}

				err = userReduceDb.Create(&newUserReduce)
				if err != nil {
					logs.Error("MultipleDepositGift create x_user_reduce err", err)
					// 不影响主流程，继续返回成功
				}
			} else {
				// 用户已有记录，更新记录
				// 将 decimal.Decimal 类型转换为 float64 类型
				reduceLiuShuiFloat, _ := reduceLiuShui.Float64()
				// 直接相加 float64 类型的值
				updatedReduceLiuShui := userReduce.ReduceLiuShui + reduceLiuShuiFloat

				_, err = userReduceDb.Where(userReduceTb.UserID.Eq(int32(token.UserId))).
					Updates(map[string]interface{}{
						"ReduceLiuShui": updatedReduceLiuShui,
						"UpdateTime":    currentTime,
					})

				if err != nil {
					logs.Error("MultipleDepositGift update x_user_reduce err", err)
					// 不影响主流程，继续返回成功
				}
			}
		}
	}

	// 复充活动领取成功后，更新用户输光状态为初始状态
	userLostStatus := &third.UserLostStatus{}
	_, err = userLostStatus.UpdateUserLostStatus(int(user.UserID), 0) // 0=初始状态
	if err != nil {
		logs.Error("MultipleDepositGift UpdateUserLostStatus err", err)
		// 不影响主流程，继续返回成功
	} else {
		logs.Info("MultipleDepositGift 复充活动领取成功，已更新用户输光状态为初始状态: 用户ID=%d", user.UserID)
	}

	// 处理成功，根据审核类型返回不同的成功提示
	if saveActiveDataInfo.AuditType == utils.ActiveAuditRenGong {
		return utils.ActiveISuccess, errors.New("申请已提交，请等待审核")
	} else {
		return utils.ActiveISuccess, nil
	}
}

// CheckUserMultipleDepositAfterRecharge 检查用户在复充成功后是否有投注记录
func CheckUserMultipleDepositAfterRecharge(userId, sellerId, channelId int, rechargeTime, endTime time.Time) (bool, error) {
	// 1. 检查哈希游戏投注记录 (x_order表)
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())

	orderCount, err := orderDb.Where(orderTb.UserID.Eq(int32(userId))).
		Where(orderTb.SellerID.Eq(int32(sellerId))).
		Where(orderTb.ChannelID.Eq(int32(channelId))).
		Where(orderTb.CreateTime.Gt(rechargeTime)).
		Where(orderTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_order err", err)
		return false, err
	}

	if orderCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现哈希游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, orderCount, rechargeTime)
		return true, nil
	}

	// 2. 检查电子游戏投注记录 (x_third_dianzhi表)
	dianzhiTb := server.DaoxHashGame().XThirdDianzhi
	dianzhiDb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())

	dianzhiCount, err := dianzhiDb.Where(dianzhiTb.UserID.Eq(int32(userId))).
		Where(dianzhiTb.SellerID.Eq(int32(sellerId))).
		Where(dianzhiTb.ChannelID.Eq(int32(channelId))).
		Where(dianzhiTb.CreateTime.Gt(rechargeTime)).
		Where(dianzhiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_dianzhi err", err)
		return false, err
	}

	if dianzhiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现电子游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, dianzhiCount, rechargeTime)
		return true, nil
	}

	// 3. 检查真人游戏投注记录 (x_third_live表)
	liveTb := server.DaoxHashGame().XThirdLive
	liveDb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())

	liveCount, err := liveDb.Where(liveTb.UserID.Eq(int32(userId))).
		Where(liveTb.SellerID.Eq(int32(sellerId))).
		Where(liveTb.ChannelID.Eq(int32(channelId))).
		Where(liveTb.CreateTime.Gt(rechargeTime)).
		Where(liveTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_live err", err)
		return false, err
	}

	if liveCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现真人游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, liveCount, rechargeTime)
		return true, nil
	}

	// 4. 检查彩票游戏投注记录 (x_third_lottery表)
	lotteryTb := server.DaoxHashGame().XThirdLottery
	lotteryDb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())

	lotteryCount, err := lotteryDb.Where(lotteryTb.UserID.Eq(int32(userId))).
		Where(lotteryTb.SellerID.Eq(int32(sellerId))).
		Where(lotteryTb.ChannelID.Eq(int32(channelId))).
		Where(lotteryTb.CreateTime.Gt(rechargeTime)).
		Where(lotteryTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_lottery err", err)
		return false, err
	}

	if lotteryCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现彩票游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, lotteryCount, rechargeTime)
		return true, nil
	}

	// 5. 检查棋牌游戏投注记录 (x_third_qipai表)
	qipaiTb := server.DaoxHashGame().XThirdQipai
	qipaiDb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())

	qipaiCount, err := qipaiDb.Where(qipaiTb.UserID.Eq(int32(userId))).
		Where(qipaiTb.SellerID.Eq(int32(sellerId))).
		Where(qipaiTb.ChannelID.Eq(int32(channelId))).
		Where(qipaiTb.CreateTime.Gt(rechargeTime)).
		Where(qipaiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_qipai err", err)
		return false, err
	}

	if qipaiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现棋牌游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, qipaiCount, rechargeTime)
		return true, nil
	}

	// 6. 检查趣味游戏投注记录 (x_third_quwei表)
	quweiTb := server.DaoxHashGame().XThirdQuwei
	quweiDb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())

	quweiCount, err := quweiDb.Where(quweiTb.UserID.Eq(int32(userId))).
		Where(quweiTb.SellerID.Eq(int32(sellerId))).
		Where(quweiTb.ChannelID.Eq(int32(channelId))).
		Where(quweiTb.CreateTime.Gt(rechargeTime)).
		Where(quweiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_quwei err", err)
		return false, err
	}

	if quweiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现趣味游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, quweiCount, rechargeTime)
		return true, nil
	}

	// 7. 检查体育游戏投注记录 (x_third_sport表)
	sportTb := server.DaoxHashGame().XThirdSport
	sportDb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())

	sportCount, err := sportDb.Where(sportTb.UserID.Eq(int32(userId))).
		Where(sportTb.SellerID.Eq(int32(sellerId))).
		Where(sportTb.ChannelID.Eq(int32(channelId))).
		Where(sportTb.CreateTime.Gt(rechargeTime)).
		Where(sportTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_sport err", err)
		return false, err
	}

	if sportCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现体育游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, sportCount, rechargeTime)
		return true, nil
	}

	// 如果所有游戏类型都没有投注记录,返回false
	logs.Info("CheckUserMultipleDepositAfterRecharge 未发现任何投注记录: 用户ID=%d, 充值时间=%v", userId, rechargeTime)
	return false, nil
}

// CheckUserMultipleDepositCount 检查用户是否完成复充活动要求的充值次数
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//
// 返回:
//   - bool: 是否完成指定次数充值
//   - int: 实际充值次数
//   - error: 错误信息
func CheckUserMultipleDepositCount(sellerId, channelId, activeId, userId, rechargeCount int32) (bool, int, error) {
	// 记录详细的参数信息，用于调试
	logs.Info("CheckUserMultipleDepositCount 函数调用参数: sellerId=%d, channelId=%d, activeId=%d, userId=%d, rechargeCount=%d",
		sellerId, channelId, activeId, userId, rechargeCount)

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 查询用户充值次数
	count, err := rechargeDb.Where(rechargeTb.UserID.Eq(userId)).
		Where(rechargeTb.SellerID.Eq(sellerId)).
		Where(rechargeTb.ChannelID.Eq(channelId)).
		Where(rechargeTb.State.Eq(5)). // 状态为成功
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositCount query error:", err)
		return false, 0, err
	}

	actualCount := int(count)
	hasEnoughDeposits := actualCount >= int(rechargeCount)

	logs.Info("CheckUserMultipleDepositCount 充值次数检查: 用户ID=%d, 活动ID=%d, 要求次数=%d, 实际次数=%d, 满足要求=%v",
		userId, activeId, rechargeCount, actualCount, hasEnoughDeposits)

	return hasEnoughDeposits, actualCount, nil
}

// CheckUserMultipleDepositActive 检查用户是否参与复充活动（只需传用户ID）
func CheckUserMultipleDepositActive(userId int32) bool {
	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 查询用户是否有复充活动记录
	count, err := activeRewardAuditDb.Where(activeRewardAuditTb.UserID.Eq(userId)).
		Where(activeRewardAuditTb.ActiveID.In(utils.MultipleDepositGift)). // 复充活动ID列表
		Where(activeRewardAuditTb.AuditState.Eq(4)).                       // 检查自动通过状态的记录
		Count()

	if err != nil {
		// 查询出错，记录日志并返回 false
		logs.Error("CheckUserMultipleDepositActive query error:", err)
		return false
	}

	// 返回是否有复充活动参与记录
	return count > 0
}

// MultipleDepositEligibilityResult 复充活动资格检查结果
type MultipleDepositEligibilityResult struct {
	IsEligible bool   // 是否符合资格
	ErrorCode  int    // 错误码
	ErrorMsg   string // 错误信息
}

// CheckMultipleDepositEnable 检查复充活动基本领取资格（封装方法，供外部使用）
// 参数:
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//   - userId: 用户ID
//
// 返回:
//   - MultipleDepositEligibilityResult: 检查结果
func CheckMultipleDepositEnable(sellerId, channelId, activeId, userId int32) MultipleDepositEligibilityResult {
	// 用户表相关
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 用户钱包表相关
	userWalletTb := server.DaoxHashGame().XUserWallet
	userWalletDb := server.DaoxHashGame().XUserWallet.WithContext(context.Background())

	// 获取当前时间
	now := time.Now()

	// 第一步：获取活动定义,验证活动有效性
	activeDefine, err := GetActiveDefine(sellerId, channelId, activeId)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable GetActiveDefine err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINotDefine,
			ErrorMsg:   "活动不存在或已关闭",
		}
	}

	// 检查活动状态是否开启
	if activeDefine.State != utils.ActiveStateOpen {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINotDefine,
			ErrorMsg:   "活动不存在或已关闭",
		}
	}

	// 校验活动时间范围
	if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
		startTime := time.UnixMilli(activeDefine.EffectStartTime)
		endTime := time.UnixMilli(activeDefine.EffectEndTime)
		if now.Before(startTime) || now.After(endTime) {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINotDefine,
				ErrorMsg:   "活动已过期,您可以继续游戏",
			}
		}
	}

	// 第二步：检查用户是否已申请过该活动
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(activeId)).
		Where(activeRewardAuditTb.UserID.Eq(userId)).
		Where(activeRewardAuditTb.SellerID.Eq(sellerId)).
		Where(activeRewardAuditTb.ChannelID.Eq(channelId)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("CheckMultipleDepositEnable activeRewardAudit err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}
	if activeRewardAudit != nil {
		// 用户已经申请过该活动
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIAlreadyApply,
			ErrorMsg:   "您已领取过该活动奖励",
		}
	}

	// 第三步：解析活动配置
	// 解析活动档位配置
	var configData []model.FirstDepositGiftConfig
	err = json.Unmarshal([]byte(activeDefine.Config), &configData)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable 活动配置异常:", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIParamsIllegal,
			ErrorMsg:   err.Error(),
		}
	}

	// 解析活动基础配置
	var baseData model.FirstDepositGiftBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable 活动配置异常:", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIParamsIllegal,
			ErrorMsg:   err.Error(),
		}
	}

	// 第四步：获取用户信息并验证用户资格
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.Email).
		Where(userTb.UserID.Eq(userId)).
		Where(userTb.SellerID.Eq(sellerId)).
		Where(userTb.ChannelID.Eq(channelId)).
		First()
	if err != nil {
		logs.Error("CheckMultipleDepositEnable user err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}

	// 检查是否为测试账号
	if user.IsTest == 1 {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIIsTest,
			ErrorMsg:   "该玩家禁止领取",
		}
	}

	// 检查邮箱绑定要求
	if baseData.IsBindEmail && user.Email == "" {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   "您未绑定邮箱,请绑定邮箱",
		}
	}

	// 检查钱包地址有效性
	if baseData.IsValidWallet {
		// 查询用户是否有已验证的钱包地址
		userWallet, err := userWalletDb.
			Where(userWalletTb.UserID.Eq(user.UserID)).
			Where(userWalletTb.SellerID.Eq(sellerId)).
			Where(userWalletTb.ChannelID.Eq(channelId)).
			Where(userWalletTb.State.Eq(1)). // 状态 1已验证 2未验证
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("CheckMultipleDepositEnable query wallet address err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if userWallet == nil || userWallet.Address == "" {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   "您未激活钱包地址,请激活钱包地址",
			}
		}
	}

	// 计算注册时间和有效时间范围
	registerTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).StartOfDay().String()).StdTime()
	endFindTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay)).StartOfDay().String()).StdTime()

	// 检查是否活动期间内注册的账号
	if baseData.IsDuringReg {
		// 检查用户注册时间是否在活动期间内
		activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
		activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
		if user.RegisterTime.Before(activityStartTime) || user.RegisterTime.After(activityEndTime) {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   "您的账号不在活动时间内注册",
			}
		}
	}

	// 计算活动领取截止时间
	receiveTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay + baseData.ReceiveDay)).StartOfDay().String()).StdTime()
	if now.After(receiveTime) {
		// 已超过领取时间
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   "当前活动领取时间已过期",
		}
	}

	// 第五步：检查用户充值记录
	recharges, err := rechargeDb.Where(rechargeTb.UserID.Eq(userId)).
		Where(rechargeTb.SellerID.Eq(sellerId)).
		Where(rechargeTb.ChannelID.Eq(channelId)).
		Where(rechargeTb.State.Eq(5)). // 状态为成功
		Where(rechargeTb.CreateTime.Between(registerTime, endFindTime)).
		Order(rechargeTb.CreateTime).
		Find()

	if err != nil {
		logs.Error("CheckMultipleDepositEnable recharge err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}

	rechargeCount := len(recharges)
	if rechargeCount < int(baseData.RechargeCount) {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   fmt.Sprintf("请完成第%d次充值,当前充值次数:%d", baseData.RechargeCount, rechargeCount),
		}
	}

	// 获取第N次充值记录
	recharge := recharges[rechargeCount-1]

	// 第六步：检查用户在复充成功后是否有投注记录
	hasBetAfterRecharge, err := CheckUserMultipleDepositAfterRecharge(
		int(userId), int(sellerId), int(channelId),
		recharge.CreateTime, now)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable CheckUserMultipleDepositAfterRecharge err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}

	if hasBetAfterRecharge {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   "对不起,因您在申请活动前已经投注,已不符合复充活动要求",
		}
	}

	// 第七步：检查用户是否完成第几次充值
	hasEnoughDeposits, actualCount, err := CheckUserMultipleDepositCount(sellerId, channelId, activeId, userId, baseData.RechargeCount)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable CheckUserMultipleDepositCount err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}

	if !hasEnoughDeposits {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   fmt.Sprintf("请完成第%d次充值,当前充值次数:%d", baseData.RechargeCount, actualCount),
		}
	}

	// 第八步：检查用户是否参与复充活动
	hasMultipleDepositActive := CheckUserMultipleDepositActive(userId)
	if hasMultipleDepositActive {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIAlreadyApply,
			ErrorMsg:   "您已参与过复充活动",
		}
	}

	// 如果所有检查都通过，返回成功
	return MultipleDepositEligibilityResult{
		IsEligible: true,
		ErrorCode:  utils.ActiveISuccess,
		ErrorMsg:   "",
	}
}
