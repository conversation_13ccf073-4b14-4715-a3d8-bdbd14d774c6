package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	daogorm "gorm.io/gorm"
	"math"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"
)

type PPConfig struct {
	url                   string
	name                  string
	secureLogin           string
	key                   string //Secret Key
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
	baseThirdService      *base.BaseThirdService // 用于处理混合下注逻辑
}

func NewPPLogic(params map[string]string, fc func(int) error) *PPConfig {
	brandName := "pp"
	return &PPConfig{
		url:                   params["url"],
		name:                  params["name"],
		secureLogin:           params["secureLogin"],
		key:                   params["key"],
		currency:              params["currency"],
		brandName:             brandName,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
		baseThirdService:      base.NewBaseThirdService(brandName, params["currency"], base.GameTypeDianzi), // 电子游戏类型
	}
}

const cacheKeyPP = "cacheKeyPP:"

// 注意：此文件中直接使用SharedTzUTC8变量

func (l *PPConfig) createHash(querydata map[string]any) string {
	str := base.SortMap(querydata, "&", "=", "asc") + l.key
	md5s := base.MD5(str)
	logs.Error("pp签名前字符", str, "|pp签名后md5", md5s)
	return md5s
}

func (l *PPConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId string `validate:"required"`
		Lang   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyPP, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	//sessionId
	sessionId := base.UserId2token(cacheKeyPP, token.UserId)

	querydata := map[string]any{
		"secureLogin":      l.secureLogin,
		"symbol":           reqdata.GameId,
		"language":         reqdata.Lang,
		"token":            sessionId,
		"externalPlayerId": fmt.Sprint(token.UserId),
		"currency":         l.currency,
	}

	querydata["hash"] = l.createHash(querydata)

	url := fmt.Sprintf("%s/IntegrationService/v3/http/CasinoGameAPI/game/url/", l.url)
	reqBytes, _ := json.Marshal(querydata)

	httpclient := httpc.DoRequest{
		UrlPath:    url,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("PP API request failed:", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试") // "Ошибка входа, пожалуйста, попробуйте позже"
		return
	}
	if errors, ok := data["error"]; ok {
		if errors != "0" {
			logs.Error("pp_http_post body error:", data)
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
	}
	if data != nil {
		ctx.Put("url", data["gameURL"])
	}
	ctx.RespOK()

}

// 验证token
func (l *PPConfig) CheckToken(querydata map[string]any) bool {
	hash := querydata["hash"]
	delete(querydata, "hash")
	myHash := l.createHash(querydata)
	logs.Error("pp三方给出的签名:", hash, "|我方签名", myHash)
	return myHash == hash
}

// 0 Success.    成功
// 1 余额不足。该错误应在投注请求的响应中返回。
// 2 未找到玩家或已注销。如果无法找到玩家或在娱乐场运营商处登出，则应在Pragmatic Play 发送的任何请求的响应中返回。
// 3 不允许投注。当玩家不被允许玩特定游戏时，无论如何都应该返回。例如，因为特殊奖金。
// 4 由于令牌无效、未找到或过期，玩家身份验证失败。
// 5 无效的哈希码。如果哈希码验证失败，则应在Pragmatic  Play 发送的任何请求的响应中返回。
// 6 玩家被冻结。如果玩家帐户被禁止或冻结，娱乐场运营商将在响应任何请求时返回此错误。
// 7 请求参数错误，请检查post参数。
// 8 游戏未找到或已禁用。如果由于某种原因游戏无法进行，则应在投注请求时返回此错误。即使游戏被禁用，包含获胜金额的投注结果请求也应按预期处理。
// 50 已达到投注限额。该准则与受监管的市场相关。
// 100 内部服务器错误(需要重试)。如果娱乐场运营商的系统存在内部问题并且目前无法处理请求并且运营商逻辑需要重试请求，则娱乐场运营商将返回此错误代码。请求将遵循协调流程
// 120 内部服务器错误(不需要重试)。如果娱乐场运营商的系统存在内部问题并且无法处理请求，并且运营商逻辑不需要重试请求，则娱乐场运营商将返回此错误代码。请求不会遵循对帐流程
// 130 EndRound 处理时出现内部服务器错误。如果娱乐场运营商的系统存在内部问题并且无法处理EndRound 请求，并且运营商逻辑需要重试该请求，则娱乐场运营商将返回此错误代码。此错误代码仅适用于Endround 方法，不适用于其他方法
// 210 现实检查警告
// 310 玩家的赌注超出了他的赌注限制。如果玩家的限制已更改，并且投注超出新的限制级别，则应返回。游戏客户端会显示正确的错误信息，并要求玩家重新打开游戏。游戏重新开放后，将应用新的投注限额。该错误与发送玩家下注限额以响应身份验证请求的运营商相关。

// Auth
func (l *PPConfig) Auth(ctx *abugo.AbuHttpContent) {
	type resp struct {
		UserId      string  `json:"userId"`
		Currency    string  `json:"currency"`
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}
	req := gin.H{
		"hash":       ctx.Gin().PostForm("hash"),
		"token":      ctx.Gin().PostForm("token"),
		"providerId": ctx.Gin().PostForm("providerId"),
		"gameId":     ctx.Gin().PostForm("gameId"),
		"ipAddress":  ctx.Gin().PostForm("ipAddress"),
	}
	logs.Error("pp三方AuthAuth req:", req)
	//验证签名
	if !l.CheckToken(req) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	//获取用户id
	token := fmt.Sprint(req["token"])
	logs.Error("AuthAuthAuthAuth", ctx.Gin().Request.URL, req)
	userId := base.Token2UserId(cacheKeyPP, token)
	if userId == -1 {
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	// 使用 BaseThirdService 的 GetUserBalance 方法获取用户余额
	userBalance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}

	// 计算总余额（真金 + Bonus币）
	totalBalance := l.baseThirdService.GetTotalBalance(userBalance)

	// 保留两位小数
	balance2 := float64(int(totalBalance*100)) / 100
	authResp.Error = 0
	authResp.Description = "成功"
	authResp.Currency = l.currency
	authResp.UserId = fmt.Sprint(userId)
	authResp.Cash = balance2
	authResp.Bonus = 0
	ctx.RespJson(authResp)
	return
}

// Balance
func (l *PPConfig) Balance(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Currency    string  `json:"currency"`
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}
	req := gin.H{
		"hash":       ctx.Gin().PostForm("hash"),
		"providerId": ctx.Gin().PostForm("providerId"),
		"userId":     ctx.Gin().PostForm("userId"),
		"token":      ctx.Gin().PostForm("token"),
	}
	logs.Error("pp三方Balance req:", req)
	//验证签名
	if !l.CheckToken(req) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}

	//获取玩家余额
	userId, _ := strconv.Atoi(fmt.Sprint(req["userId"]))

	// 使用 BaseThirdService 的 GetUserBalance 方法获取用户余额
	userBalance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}

	// 计算总余额（真金 + Bonus币）
	totalBalance := l.baseThirdService.GetTotalBalance(userBalance)

	// 保留两位小数
	balance2 := float64(int(totalBalance*100)) / 100
	authResp.Error = 0
	authResp.Description = "成功"
	authResp.Currency = l.currency
	authResp.Cash = balance2
	authResp.Bonus = 0
	ctx.RespJson(authResp)
	return
}

type betReq struct {
	Hash                string `form:"hash" json:"hash"`
	ProviderId          string `form:"providerId" json:"providerId"`
	UserId              string `form:"userId" json:"userId"`
	GameId              string `form:"gameId" json:"gameId"`
	RoundId             *int64 `form:"roundId" json:"roundId"`
	Amount              string `form:"amount" json:"amount"`
	Reference           string `form:"reference" json:"reference"`
	Timestamp           int64  `form:"timestamp" json:"timestamp"`
	RoundDetails        string `form:"roundDetails" json:"roundDetails"`
	BonusCode           string `form:"bonusCode" json:"bonusCode"`
	Platform            string `form:"platform" json:"platform"`
	Language            string `form:"language" json:"language"`
	JackpotContribution string `form:"jackpotContribution" json:"jackpotContribution"`
	JackpotId           *int64 `form:"jackpotId" json:"jackpotId"`
	JackpotDetails      string `form:"jackpotDetails" json:"jackpotDetails"`
	Token               string `form:"token" json:"token"`
	IpAddress           string `form:"ipAddress" json:"ipAddress"`
}

func (l *PPConfig) Bet(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		UsedPromo     float64 `json:"usedPromo"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := betReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error("pp三方Bet req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if err != nil {
		logs.Error("pp三方Bet json.Unmarshal err:", err)

		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Description = "请求参数错误，请检查post参数。"
		authResp.Error = 7
		ctx.RespJson(authResp)
		return
	}
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)

	//三方来源的数据整理
	var (
		betAmount = base.StrToFloat64(req.Amount)
		thirdId   = fmt.Sprint(*req.RoundId)
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	var gameName string
	tablePre := "x_third_dianzhi_pre_order"

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 使用 BaseThirdService 的 GetUserBalanceForUpdate 方法获取用户余额
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("PP Bet api 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			authResp.Error = 2
			authResp.Description = "未找到玩家或已注销"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = 0
			ctx.RespJson(authResp)
			logs.Error("PP Bet api 查询用户余额失败 thirdId=", thirdId, " authResp := ", authResp, betAmount)
			return e
		}
		//获取投注渠道
		betChannelId := base.GetUserChannelId(ctx, userBalance)

		// 计算总余额（真金 + Bonus币）
		totalBalance := l.baseThirdService.GetTotalBalance(userBalance)

		//判断用户余额
		if totalBalance < 0 || totalBalance < betAmount {
			// 保留两位小数
			authResp.Error = 1
			authResp.Description = "余额不足"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			logs.Error("PP Bet api 用户余额不足 thirdId=", thirdId, " authResp := ", authResp, betAmount, totalBalance)
			e = errors.New("玩家余额不足")
			return e
		}

		// 计算下注金额分配
		realBetAmount, bonusBetAmount, betType, e := l.baseThirdService.CalculateBetDistribution(userBalance, betAmount)
		if e != nil {
			logs.Error("PP Bet api 计算下注金额分配失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
			authResp.Error = 1
			authResp.Description = "余额不足"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return e
		}

		// 查询订单是否存在
		betTran, exists, err := l.baseThirdService.GetOrderForUpdate(tx, thirdId)
		// 处理查询错误
		if err != nil {
			logs.Error("PP Bet api 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " e=", err)
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return err
		}

		// 判断订单是否存在
		if exists {
			logs.Error("PP Bet api 订单已存在 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " betTran=", betTran)
			authResp.Error = 3
			authResp.Description = "不允许投注"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return errors.New(fmt.Sprintf("订单已存在，thirdId= %s", thirdId))
		}

		if betAmount > 0 {
			// 使用 DeductAmountWithLogs 方法扣除下注金额并创建账变记录
			e = l.baseThirdService.DeductAmountWithLogs(
				tx,
				userBalance,
				realBetAmount,
				bonusBetAmount,
				utils.BalanceCReasonPPBet,
				l.brandName+" bet,thirdId:"+thirdId,
				thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			)
			if e != nil {
				logs.Error("PP Bet api 扣除下注金额失败 thirdId=", thirdId, " userId=", userId, " realBetAmount=", realBetAmount, " bonusBetAmount=", bonusBetAmount, " err=", e.Error())
				authResp.Error = 100
				authResp.Description = "内部服务器错误"
				authResp.Currency = l.currency
				authResp.TransactionId = myTransactionId
				authResp.Cash = totalBalance
				ctx.RespJson(authResp)
				return errors.New("修改x_user失败了")
			}

			// 直接在内存中更新用户余额
			userBalance.Amount -= realBetAmount
			userBalance.BonusAmount -= bonusBetAmount

			// 重新计算总余额
			totalBalance = l.baseThirdService.GetTotalBalance(userBalance)
		}

		//获取三方游戏名称
		var gameList struct {
			Name string `gorm:"column:Name"`
		}
		e = tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
		if e != nil {
			gameName = gamecode // 如果查询失败，使用游戏ID作为游戏名称
		} else {
			gameName = gameList.Name
		}

		// 重新计算总余额
		afterBalance := l.baseThirdService.GetTotalBalance(userBalance)
		orderData := &thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: betChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gamecode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			Currency:     l.currency,
			RawData:      string(reqBytes),
			State:        1,
			DataState:    -1,
			CreateTime:   thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			BetCtx:       string(reqBytes),
			BetCtxType:   1,
			// 添加混合下注相关字段
			BetType:        betType,
			BonusBetAmount: bonusBetAmount,
		}
		e = tx.Table(tablePre).Omit("Id").Create(orderData).Error
		if e != nil {
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			logs.Error("PP Bet api 新增注单错误 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " order=", orderData, " e=", e)
			return e
		}

		// 注意：这里不需要创建账变记录，因为 DeductAmountWithLogs 方法已经创建了
		// 推送下注事件通知
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency)
		}

		authResp.Error = 0
		authResp.Description = "成功"
		authResp.Currency = l.currency
		authResp.TransactionId = myTransactionId
		authResp.Cash = afterBalance // 返回总余额
		ctx.RespJson(authResp)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type resultReq struct {
	Hash              string `form:"hash" json:"hash"`
	UserId            string `form:"userId" json:"userId"`
	GameId            string `form:"gameId" json:"gameId"`
	RoundId           *int64 `form:"roundId" json:"roundId"`
	Amount            string `form:"amount" json:"amount"`
	Reference         string `form:"reference" json:"reference"`
	ProviderId        string `form:"providerId" json:"providerId"`
	Timestamp         *int64 `form:"timestamp" json:"timestamp"`
	RoundDetails      string `form:"roundDetails" json:"roundDetails"`
	BonusCode         string `form:"bonusCode" json:"bonusCode"`
	Platform          string `form:"platform" json:"platform"`
	Token             string `form:"token" json:"token"`
	PromoWinAmount    string `form:"promoWinAmount" json:"promoWinAmount"`
	PromoWinReference string `form:"promoWinReference" json:"promoWinReference"`
	PromoCampaignID   string `form:"promoCampaignID" json:"promoCampaignID"`
	PromoCampaignType string `form:"promoCampaignType" json:"promoCampaignType"`
}

// Result
func (l *PPConfig) Result(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := resultReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error("PP 三方Result req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if err != nil {
		logs.Error("PP 三方Result json.Unmarshal err:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Description = "请求参数错误，请检查post参数。"
		authResp.Error = 7
		ctx.RespJson(authResp)
		return
	}
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)

	// 使用 BaseThirdService 的 GetUserBalance 方法获取用户余额
	balance, err := l.baseThirdService.GetUserBalance(userId)
	if err != nil {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	// 计算总余额（真金 + Bonus币）
	totalBalance := l.baseThirdService.GetTotalBalance(balance)

	// 保留两位小数
	balance2 := float64(int(totalBalance*100)) / 100

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		gamecode  = req.GameId
		thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	)
	var thirdId string
	if req.RoundId != nil {
		thirdId = fmt.Sprint(*req.RoundId)
	} else {
		thirdId = "" // 或其他适当的默认值
	}
	//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
	tablePre := "x_third_dianzhi_pre_order"
	table := "x_third_dianzhi"

	//开启事务
	errTa := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 使用 BaseThirdService 的 GetUserBalanceForUpdate 方法获取用户余额
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("PP Result 派彩 获取用户余额失败 userId=", userId, " err=", e.Error())
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 2
			authResp.Description = "未找到玩家或已注销"
			ctx.RespJson(authResp)
			return e
		}

		// 计算总余额（真金 + Bonus币）
		txTotalBalance := l.baseThirdService.GetTotalBalance(userBalance)

		// 查询订单是否存在
		betTran, exists, err := l.baseThirdService.GetOrderForUpdate(tx, thirdId)
		// 处理查询错误
		if err != nil {
			logs.Error("PP Result api 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " e=", err)
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return err
		}

		// 判断订单是否存在
		if !exists {
			logs.Error("PP Result 派彩 订单存在 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " betTran=", betTran)
			authResp.Error = 3
			authResp.Description = "订单不存在"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return errors.New(fmt.Sprintf("订单不存在，thirdId= %s", thirdId))
		}

		if betTran.DataState != -1 {
			err = errors.New("订单已结算")
			logs.Error("PP Result 派彩 订单已结算  thirdId=", thirdId, " order=", betTran)
			authResp.Error = 3
			authResp.Description = "订单已结算"
			authResp.Currency = l.currency
			authResp.TransactionId = myTransactionId
			authResp.Cash = totalBalance
			ctx.RespJson(authResp)
			return err
		}

		logs.Info("PP   订单数据：", betTran)

		bonusBetAmount := betTran.BonusBetAmount
		// 使用 CalculateWinDistribution 方法计算派彩金额分配
		realWinAmount, bonusWinAmount := l.baseThirdService.CalculateWinDistribution(betTran.BetAmount, amount, bonusBetAmount)

		// 所有电子的有效流水取不大于下注金额的输赢绝对值 Bonus币 不记录流水
		realBetAmount := betTran.BetAmount - betTran.BonusBetAmount
		validBet := math.Abs(realWinAmount - realBetAmount)
		if validBet > math.Abs(realBetAmount) {
			validBet = math.Abs(realBetAmount)
		}
		logs.Info("PP Result 记录有效流水", l.brandName, "thirdId=", thirdId, " amount=", amount, " betAmount=", realBetAmount, " validBet=", validBet)

		// 使用 AddAmountWithLogs 方法增加派彩金额并创建账变记录
		e = l.baseThirdService.AddAmountWithLogs(
			tx,
			userBalance,
			realWinAmount,
			bonusWinAmount,
			utils.BalanceCReasonPPSettle,
			l.brandName+" result,thirdId:"+thirdId,
			thirdTime,
		)
		if e != nil {
			logs.Error("PP Result 增加派彩金额失败 thirdId=", thirdId, " userId=", userId, " realWinAmount=", realWinAmount, " bonusWinAmount=", bonusWinAmount, " err=", e.Error())
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// 直接在内存中更新用户余额
		userBalance.Amount += realWinAmount
		userBalance.BonusAmount += bonusWinAmount

		// 重新计算总余额
		txTotalBalance = l.baseThirdService.GetTotalBalance(userBalance)

		//将下注订单移动至结算订单表
		//修改成已经结算了
		e = tx.Table(tablePre).Where("Id = ?", betTran.Id).Updates(map[string]interface{}{
			"WinAmount":      amount,
			"ValidBet":       validBet,
			"RawData":        string(reqBytes),
			"GameId":         gamecode,
			"ThirdTime":      thirdTime,
			"BonusWinAmount": bonusWinAmount,
			"DataState":      1,
		}).Error
		if e != nil {
			logs.Debug("PP  结算 修改成已经结算了 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// 更新注单信息用于插入统计表
		betTran.DataState = 1
		betTran.ThirdTime = thirdTime
		betTran.ValidBet = validBet
		betTran.WinAmount = amount
		betTran.BonusWinAmount = bonusWinAmount
		betTran.GameId = gamecode
		betTran.RawData = string(reqBytes)
		betTran.Id = 0 // 重置ID以便创建新记录

		//移动至统计表
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			logs.Error("PP 结算 移动至统计表 x_third_dianzhi 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		logs.Info("PP 结算成功 移动至统计表 成功", l.brandName, "thirdId=", thirdId, " amount=", amount, " betAmount=", realBetAmount, " realWinAmount=", realWinAmount, " bonusWinAmount=", bonusWinAmount, " validBet=", validBet)

		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		// 使用内存中的总余额，如果有派彩，则使用事务中的总余额
		if amount != 0 {
			authResp.Cash = txTotalBalance
		} else {
			authResp.Cash = balance2
		}
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return nil
	})
	if errTa == nil {
		// 推送奖励事件通知
		if l.thirdGamePush != nil {
			//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, betAmount, winAmount, l.currency)
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
		}

		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != "" {
				if e := server.Db().GormDao().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp Result 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Result 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Result 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type bonusWinReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	Amount       string `form:"amount" json:"amount"`
	Reference    string `form:"reference" json:"reference"`
	ProviderId   string `form:"providerId" json:"providerId"`
	Timestamp    *int64 `form:"timestamp" json:"timestamp"`
	BonusCode    string `form:"bonusCode" json:"bonusCode"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	GameId       string `form:"gameId" json:"gameId"`
	Token        string `form:"token" json:"token"`
	RequestId    string `form:"requestId" json:"requestId"`
	RemainAmount string `form:"remainAmount" json:"remainAmount"`
}

// BonusWin
func (l *PPConfig) BonusWin(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	// 获取请求参数
	req := bonusWinReq{}
	err := ctx.Gin().ShouldBind(&req)
	if err != nil {
		logs.Error("PP BonusWin 解析请求参数失败:", err.Error())
		authResp.Error = 100
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}
	logs.Debug("PP BonusWin 请求参数:", req)

	// 验证签名
	reqBytes, _ := json.Marshal(req)
	reqMaps := map[string]any{}
	err = json.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		logs.Error("PP BonusWin 签名验证失败")
		authResp.Error = 4
		authResp.Description = "玩家身份验证失败"
		ctx.RespJson(authResp)
		return
	}

	userId, err := strconv.Atoi(req.UserId)
	if err != nil {
		logs.Error("PP BonusWin 用户ID转换失败:", err.Error())
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	// 检查交易ID是否已存在
	thirdId := req.Reference
	if len(thirdId) == 0 {
		logs.Error("PP BonusWin 交易ID为空")
		authResp.Error = 100
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}

	// 生成我方交易ID
	myTransactionId := fmt.Sprintf("%s_%s", l.brandName, thirdId)

	// 解析金额
	amount, err := strconv.ParseFloat(req.Amount, 64)
	if err != nil {
		logs.Error("PP BonusWin 金额转换失败:", err.Error())
		authResp.Error = 100
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}

	// 开始事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额并加锁
		userBalance, err := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if err != nil {
			logs.Error("PP BonusWin 获取用户余额失败:", err.Error())
			authResp.Error = 2
			authResp.Description = "未找到玩家或已注销"
			ctx.RespJson(authResp)
			return err
		}

		// 计算总余额（真金 + Bonus币）
		totalBalance := l.baseThirdService.GetTotalBalance(userBalance)
		// 保留两位小数
		balance2 := float64(int(totalBalance*100)) / 100

		// 查询订单是否存在
		var exists bool
		existingOrder, exists, err := l.baseThirdService.GetOrderForUpdate(tx, myTransactionId)
		if err != nil {
			logs.Error("PP BonusWin 查询订单失败:", err.Error())
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return err
		}

		// 如果订单已存在，返回成功
		if exists && existingOrder != nil {
			logs.Info("PP BonusWin 订单已存在:", myTransactionId)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2 + amount
			authResp.Error = 0
			authResp.Description = "成功"
			ctx.RespJson(authResp)
			return nil
		}

		// 游戏信息
		gameId := req.GameId
		gameName := fmt.Sprintf("PP游戏 %s", gameId)

		// 备注信息
		memo := l.brandName + " bonusWin,thirdId:" + thirdId
		// 处理免费旋转和奖励，将奖励加到彩金账户
		winAmount := amount

		// 使用BaseThirdService处理免费旋转和奖励
		resultOrder, err := l.baseThirdService.ProcessFreeRoundAndWin(
			tx,
			userBalance,
			winAmount,
			utils.BalanceCReasonPPBonusWin, // 使用PP彩金奖励的原因代码
			myTransactionId,
			gameId,
			gameName,
			memo,
			utils.GetCurrentTime(),
			string(reqBytes),
			userBalance.ChannelId,
		)

		if err != nil || resultOrder == nil {
			logs.Error("PP BonusWin 处理彩金奖励失败:", err.Error())
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return err
		}

		// 返回成功响应
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2 + amount
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] BonusWin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] BonusWin 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// 默认是不开启的
type jackpotWinWinReq struct {
	Hash           string `form:"hash" json:"hash"`
	ProviderId     string `form:"providerId" json:"providerId"`
	Timestamp      *int64 `form:"timestamp" json:"timestamp"`
	UserId         string `form:"userId" json:"userId"`
	GameId         string `form:"gameId" json:"gameId"`
	RoundId        *int64 `form:"roundId" json:"roundId"`
	JackpotId      *int64 `form:"jackpotId" json:"jackpotId"`
	JackpotDetails string `form:"jackpotDetails" json:"jackpotDetails"`
	Amount         string `form:"amount" json:"amount"`
	Reference      string `form:"reference" json:"reference"`
	Platform       string `form:"platform" json:"platform"`
	Token          string `form:"token" json:"token"`
}

// JackpotWin
func (l *PPConfig) JackpotWin(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := jackpotWinWinReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		logs.Error("PP 三方JackpotWin err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error("PP 三方JackpotWin req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = req.Reference
		gamecode  = req.GameId
		thirdTime = time.Now().In(tzUTC8)
	)

	//开启事务
	errTa := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		//将JackpotWin游戏直接结算订单表
		//获取三方游戏名称
		var gameList struct {
			Name string
		}
		e := tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
		if e != nil {
			logs.Debug("PP 结算 JackpotWin 获取游戏名称失败:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// Convert interface values to int safely
		sellerId, _ := (*udata)["SellerId"].(int)
		channelId, _ := (*udata)["ChannelId"].(int)

		// 创建订单记录
		order := thirdGameModel.ThirdOrder{
			SellerId:  sellerId,
			ChannelId: channelId,
			UserId:    userId,
			Brand:     l.brandName,
			ThirdId:   thirdId,
			GameId:    gamecode,
			GameName:  gameList.Name,
			BetAmount: 0,
			WinAmount: amount,
			ValidBet:  0,
			ThirdTime: thirdTime.Format("2006-01-02 15:04:05"),
			Currency:  l.currency,
			RawData:   string(reqBytes),
			DataState: 1,
		}

		// 插入订单记录
		e = tx.Table("x_third_dianzhi").Create(&order).Error
		if e != nil {
			logs.Error("PP 结算 JackpotWin 统计表 x_third_dianzhi 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		//处理结算
		if amount > 0 {
			//win
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount + ?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Debug("PP 结算 JackpotWin 处理结算 x_user 失败了:  id = ", thirdId, e)
				authResp.TransactionId = myTransactionId
				authResp.Currency = l.currency
				authResp.Cash = balance2
				authResp.Error = 100
				authResp.Description = "内部服务器错误"
				ctx.RespJson(authResp)
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: balance2,
				Amount:       amount,
				AfterAmount:  balance2 + amount,
				Reason:       utils.BalanceCReasonPPJackpotWin,
				Memo:         l.brandName + " bonusWin,thirdId:" + thirdId,
				SellerId:     sellerId,
				ChannelId:    channelId,
				CreateTime:   thirdTime.Format("2006-01-02 15:04:05"),
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Debug("PP 结算 JackpotWin 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, e)
				authResp.TransactionId = myTransactionId
				authResp.Currency = l.currency
				authResp.Cash = balance2
				authResp.Error = 100
				authResp.Description = "内部服务器错误"
				ctx.RespJson(authResp)
				return e
			}
		}
		logs.Debug("pp EndRound", " JackpotWin 結算成功", " thirdId=", thirdId)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2 + amount
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return nil
	})
	if errTa == nil {
		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != "" {
				if e := server.Db().GormDao().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp JackpotWin 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] JackpotWin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] JackpotWin 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type endRoundReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	GameId       string `form:"gameId" json:"gameId"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	ProviderId   string `form:"providerId" json:"providerId"`
	BonusCode    string `form:"bonusCode" json:"bonusCode"`
	Platform     string `form:"platform" json:"platform"`
	Token        string `form:"token" json:"token"`
	RoundDetails string `form:"roundDetails" json:"roundDetails"` // 改为 string 类型以简化处理
}

// EndRound 玩家全输
func (l *PPConfig) EndRound(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}

	req := endRoundReq{}
	var reqBytes []byte

	// 记录原始请求内容类型
	logs.Info("PP EndRound 请求内容类型:", ctx.Gin().Request.Header.Get("Content-Type"))

	// 直接使用 ShouldBind 自动处理表单数据
	if err := ctx.Gin().ShouldBind(&req); err != nil {
		logs.Error("PP 解析请求数据失败:", err)
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	// 检查必要字段
	if req.UserId == "" || req.GameId == "" || req.RoundId == nil {
		logs.Error("PP 必要字段缺失 UserId=", req.UserId, " GameId=", req.GameId, " RoundId=", req.RoundId)
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	// 将请求数据转换为 JSON 字符串以便于日志记录和后续处理
	reqBytes, _ = json.Marshal(req)
	logs.Info("PP EndRound 请求数据:", string(reqBytes))

	//验证签名
	reqMaps := map[string]any{}
	_ = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)
	//三方来源的数据整理
	var (
		gamecode  = req.GameId
		thirdTime = time.Now().In(tzUTC8)
	)

	var thirdId string
	if req.RoundId != nil {
		thirdId = fmt.Sprint(*req.RoundId)
	} else {
		thirdId = "" // 或其他适当的默认值
	}

	// 添加日志记录 RoundDetails 的类型和值
	logs.Debug("PP EndRound RoundDetails 类型:", fmt.Sprintf("%T", req.RoundDetails), "值:", req.RoundDetails)

	//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
	tablePre := "x_third_dianzhi_pre_order"
	table := "x_third_dianzhi"

	//开启事务
	errTa := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		// 使用 BaseThirdService 的 GetUserBalanceForUpdate 方法获取用户余额
		userBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("PP EndRound 派彩 获取用户余额失败 userId=", userId, " err=", e.Error())
			authResp.Error = 2
			authResp.Description = "未找到玩家或已注销"
			ctx.RespJson(authResp)
			return e
		}
		// 计算总余额（真金 + Bonus币）
		totalBalance := l.baseThirdService.GetTotalBalance(userBalance)
		// 保留两位小数
		balance2 := float64(int(totalBalance*100)) / 100

		// 查询订单是否存在
		betTran, exists, err := l.baseThirdService.GetOrderForUpdate(tx, thirdId)
		// 处理查询错误
		if err != nil {
			logs.Error("PP EndRound 派彩 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " e=", err)
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return err
		}

		// 判断订单是否存在
		if !exists {
			logs.Error("PP EndRound 派彩 订单不存在 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp)
			//订单不存在  忽略它
			authResp.Cash = balance2
			authResp.Error = 0
			authResp.Description = "成功"
			ctx.RespJson(authResp)
			return nil
		}

		if betTran.DataState != -1 {
			err = errors.New("订单已结算")
			logs.Error("PP EndRound 派彩 订单已结算  thirdId=", thirdId, " order=", betTran)
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "订单已结算"
			ctx.RespJson(authResp)
			return err
		}

		logs.Error("PP  订单数据： ", betTran)

		// 所有电子的有效流水取不大于下注金额的输赢绝对值
		realBetAmount := betTran.BetAmount - betTran.BonusBetAmount
		realWinAmount := betTran.WinAmount - betTran.BonusWinAmount
		validBet := math.Abs(realWinAmount - realBetAmount)
		if validBet > math.Abs(realBetAmount) {
			validBet = math.Abs(realBetAmount)
		}

		//bonusBetAmount := betTran.BonusBetAmount
		// 使用 CalculateWinDistribution 方法计算派彩金额分配
		//realWinAmount, bonusWinAmount := l.baseThirdService.CalculateWinDistribution(betTran.BetAmount, winAmount, bonusBetAmount)
		// 玩家全输派彩金额是0

		logs.Info("PP EndRound 记录有效流水", l.brandName, "thirdId=", thirdId, " realWinAmount=", realWinAmount, " realBetAmount=", realBetAmount, " validBet=", validBet)
		//将下注订单移动至结算订单表
		//修改成已经结算了
		e = tx.Table(tablePre).Where("Id = ?", betTran.Id).Updates(map[string]interface{}{
			"ValidBet":  validBet,
			"RawData":   string(reqBytes),
			"GameId":    gamecode,
			"ThirdTime": thirdTime.Format("2006-01-02 15:04:05"),
			"DataState": 1,
		}).Error
		if e != nil {
			logs.Error("PP  EndRound 修改成已经结算了 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, e)
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// 更新注单信息用于插入统计表
		betTran.DataState = 1
		betTran.ThirdTime = thirdTime.Format("2006-01-02 15:04:05")
		betTran.ValidBet = validBet
		betTran.RawData = string(reqBytes)
		betTran.GameId = gamecode
		betTran.Id = 0 // 重置ID以便创建新记录

		//移动至统计表
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			logs.Error("PP EndRound 移动至统计表 x_third_dianzhi 失败了:  id = ", thirdId, e)
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		logs.Debug("pp sw :", " EndRound 結算成功", " thirdId=", thirdId)
		// 使用总余额作为返回值
		authResp.Cash = balance2 // 返回总余额
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return nil
	})
	if errTa == nil {

		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != "" {
				if e := server.Db().GormDao().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp EndRound 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}

		}()
	}
}

type refundReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	Reference    string `form:"reference" json:"reference"`
	ProviderId   string `form:"providerId" json:"providerId"`
	Platform     string `form:"platform" json:"platform,omitempty"`
	Amount       string `form:"amount" json:"amount"` // 改为 string 而不是 float64
	GameId       string `form:"gameId" json:"gameId"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	Timestamp    *int64 `form:"timestamp" json:"timestamp"`
	RoundDetails string `form:"roundDetails" json:"roundDetails"` // 改为 string 而不是 interface{}
	BonusCode    string `form:"bonusCode" json:"bonusCode,omitempty"`
	Token        string `form:"token" json:"token,omitempty"`
}

// Refund
func (l *PPConfig) Refund(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string `json:"transactionId"`
		Error         int    `json:"error"`
		Description   string `json:"description"`
	}
	authResp := resp{}
	req := refundReq{}
	myTransactionId := abugo.GetUuid()
	// 记录原始请求内容类型
	logs.Info("PP 三方Refund 请求内容类型:", ctx.Gin().Request.Header.Get("Content-Type"))

	err := ctx.Gin().ShouldBind(&req)
	if err != nil {
		logs.Error("PP 三方Refund err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info("PP 三方Refund req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)

	//三方来源的数据整理
	var (
		thirdId   string
		thirdTime = time.Now().In(tzUTC8)
	)

	if req.RoundId == nil {
		logs.Error("pp Refund 订单不存在:  thirdId = ", thirdId, " RoundId 为空")
		authResp.TransactionId = myTransactionId
		authResp.Error = 120
		authResp.Description = "订单不存在"
		ctx.RespJson(authResp)
	}

	// 安全地将 RoundId 转换为字符串
	if req.RoundId != nil {
		thirdId = fmt.Sprint(*req.RoundId)
	} else {
		thirdId = ""
	}

	// 添加日志记录 RoundDetails 的类型和值
	logs.Debug("PP Refund RoundDetails 类型:", fmt.Sprintf("%T", req.RoundDetails), "值:", req.RoundDetails)
	tablePre := "x_third_dianzhi_pre_order"
	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 查询订单是否存在
		betTran, exists, e := l.baseThirdService.GetOrderForUpdate(tx, thirdId)
		// 处理查询错误
		if e != nil {
			logs.Error("PP Refund 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " authResp=", authResp, " e=", err)
			authResp.TransactionId = myTransactionId
			authResp.Error = 120
			authResp.Description = "系统异常"
			ctx.RespJson(authResp)
			return e
		}

		// 判断订单是否存在
		if !exists {
			//订单不存在  忽略它
			logs.Error("pp Refund 订单不存在:  thirdId = ", thirdId)
			authResp.TransactionId = myTransactionId
			authResp.Error = 120
			authResp.Description = "订单不存在"
			ctx.RespJson(authResp)
			return nil
		}

		if betTran.DataState == -2 { //订单已经取消过 返回成功
			//已经取消了
			logs.Error("pp Refund 订单已经取消了:  thirdId = ", thirdId)
			authResp.TransactionId = myTransactionId
			authResp.Error = 0
			authResp.Description = "成功"
			ctx.RespJson(authResp)
			return nil
		}

		if betTran.DataState != -1 {
			e = errors.New("注单状态异常")
			logs.Error("pp  Refund 注单状态异常,订单可能已结算  thirdId=", thirdId, " order=", betTran)
			authResp.TransactionId = myTransactionId
			authResp.Error = 120
			authResp.Description = "注单状态异常"
			e = errors.New("注单状态异常")
			return e
		}

		// 使用 BaseThirdService 的 GetUserBalanceForUpdate 方法获取用户余额
		txUserBalance, e := l.baseThirdService.GetUserBalanceForUpdate(tx, userId)
		if e != nil {
			logs.Error("PP Refund 取消订单 获取用户余额失败 userId=", userId, " err=", err.Error())
			authResp.TransactionId = myTransactionId
			authResp.Error = 2
			authResp.Description = "未找到玩家或已注销"
			ctx.RespJson(authResp)
			return e
		}

		// 计算总余额（真金 + Bonus币）
		txTotalBalance := l.baseThirdService.GetTotalBalance(txUserBalance)
		e = tx.Table(tablePre).Where("Id = ?", betTran.Id).Updates(map[string]interface{}{
			"RawData":   string(reqBytes),
			"DataState": -2,
		}).Error
		if e != nil {
			logs.Debug("pp 订单取消 修改x_third_dianzhi_pre_order状态失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// 获取原始下注订单的真金和Bonus币下注金额
		realBetAmount := betTran.BetAmount - betTran.BonusBetAmount
		bonusBetAmount := betTran.BonusBetAmount

		// 使用 AddAmountWithLogs 方法增加退款金额并创建账变记录
		e = l.baseThirdService.AddAmountWithLogs(
			tx,
			txUserBalance,
			realBetAmount,
			bonusBetAmount,
			utils.BalanceCReasonPPCancel,
			"pp cancel,thirdId:"+thirdId,
			thirdTime.Format("2006-01-02 15:04:05"),
		)
		if e != nil {
			logs.Error("PP Refund 增加退款金额失败 thirdId=", thirdId, " userId=", userId, " realBetAmount=", realBetAmount, " bonusBetAmount=", bonusBetAmount, " err=", e.Error())
			authResp.TransactionId = myTransactionId
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// 直接在内存中更新用户余额
		txUserBalance.Amount += realBetAmount
		txUserBalance.BonusAmount += bonusBetAmount

		// 计算总余额并格式化为两位小数
		txTotalBalance = l.baseThirdService.GetTotalBalance(txUserBalance)
		formattedBalance := float64(int(txTotalBalance*100)) / 100

		logs.Info("pp 订单取消成功了:  id = ", thirdId, " 总余额 = ", formattedBalance)
		authResp.TransactionId = myTransactionId
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)

		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Refund 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// PromoWin  暂时未做
func (l *PPConfig) PromoWin(ctx *abugo.AbuHttpContent) {

}

type adjustmentReq struct {
	Hash           string `form:"hash" json:"hash"`
	UserId         string `form:"userId" json:"userId"`
	GameId         string `form:"gameId" json:"gameId"`
	Token          string `form:"token" json:"token"`
	RoundId        *int64 `form:"roundId" json:"roundId"`
	Amount         string `form:"amount" json:"amount"`
	Reference      string `form:"reference" json:"reference"`
	ProviderId     string `form:"providerId" json:"providerId"`
	ValidBetAmount string `form:"validBetAmount" json:"validBetAmount"`
	Timestamp      *int64 `form:"timestamp" json:"timestamp"`
}

// Adjustment
func (l *PPConfig) Adjustment(ctx *abugo.AbuHttpContent) {
	//适用于真人游戏
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := adjustmentReq{}
	err := ctx.Gin().ShouldBind(&req)

	myTransactionId := abugo.GetUuid()
	if err != nil {
		logs.Error("PP 三方Adjustment err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error("PP 三方Adjustment req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}
	//查询玩家信息
	userId, _ := strconv.Atoi(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = fmt.Sprint(req.RoundId)
		gamecode  = req.GameId
		thirdTime = time.Now().In(tzUTC8)
	)

	if amount == 0 {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return
	}
	if -amount > balance2 {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 1
		authResp.Description = "余额不足"
		ctx.RespJson(authResp)
		return
	}

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		//将余额调整直接结算订单表
		//获取三方游戏名称
		var gameList struct {
			Name string
		}
		e := tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
		if e != nil {
			logs.Debug("PP 结算 Adjustment 获取游戏名称失败:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}

		// Convert interface values to int safely
		sellerId, _ := (*udata)["SellerId"].(int)
		channelId, _ := (*udata)["ChannelId"].(int)

		// 创建订单记录
		order := thirdGameModel.ThirdOrder{
			SellerId:  sellerId,
			ChannelId: channelId,
			UserId:    userId,
			Brand:     l.brandName,
			ThirdId:   thirdId,
			GameId:    gamecode,
			GameName:  gameList.Name,
			ValidBet:  0,
			ThirdTime: thirdTime.Format("2006-01-02 15:04:05"),
			Currency:  l.currency,
			RawData:   string(reqBytes),
			DataState: 1,
		}

		// 设置金额
		if amount > 0 {
			order.WinAmount = amount
		} else {
			order.BetAmount = -amount
		}

		// 插入订单记录
		e = tx.Table("x_third_dianzhi").Create(&order).Error
		if e != nil {
			logs.Debug("PP 结算 Adjustment 统计表 x_third_dianzhi 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		//处理余额调整
		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"amount": daogorm.Expr("amount + ?", amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("PP 结算 处理余额调整 处理结算 x_user 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: balance2,
			Amount:       amount,
			AfterAmount:  balance2 + amount,
			Reason:       utils.BalanceCReasonPPAdjustment,
			Memo:         l.brandName + " bonusWin,thirdId:" + thirdId,
			SellerId:     sellerId,
			ChannelId:    channelId,
			CreateTime:   thirdTime.Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("PP 结算 处理余额调整 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, e)
			authResp.TransactionId = myTransactionId
			authResp.Currency = l.currency
			authResp.Cash = balance2
			authResp.Error = 100
			authResp.Description = "内部服务器错误"
			ctx.RespJson(authResp)
			return e
		}
		logs.Debug("pp sw :", "处理余额调整成功")
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2 + amount
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Adjustment 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Adjustment 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// RoundDetails
func (l *PPConfig) RoundDetails(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Error       int    `json:"error"`
		Description string `json:"description"`
	}
	authResp := resp{}
	authResp.Error = 0
	authResp.Description = "成功"
	ctx.RespJson(authResp)
	return
}

// 获取开奖结果 OpenHistoryExtended
func (l *PPConfig) getRoundDetail(reqUser, reqGameId string, reqRound int64, reqLang string) (detailUrl string, err error) {
	querydata := map[string]any{
		"secureLogin": l.secureLogin,
		"playerId":    reqUser,
		"gameId":      reqGameId,
		"roundId":     reqRound,
		"language":    reqLang,
	}
	querydata["hash"] = l.createHash(querydata)

	url := fmt.Sprintf("%s/IntegrationService/v3/http/HistoryAPI/OpenHistoryExtended/", l.url)
	reqBytes, _ := json.Marshal(querydata)
	logs.Info("pp  请求三方开奖结果 参数:", querydata)

	httpclient := httpc.DoRequest{
		UrlPath:    url,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("pp  请求三方开奖结果 请求错误:", err.Error())
		return
	}
	logs.Info("pp  请求三方开奖结果 响应:", data)
	if errors, ok := data["error"]; ok {
		if errors.(string) != "0" {
			logs.Error("pp  请求三方开奖结果 请求错误:", errors, data["description"])
			err = fmt.Errorf("请求失败%s", data["description"])
			return
		}
	}
	detailUrl = data["url"].(string)
	return
}
