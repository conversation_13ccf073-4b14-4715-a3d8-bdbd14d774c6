package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/subtle"
	"encoding/hex"
	"fmt"
	"io"
	"sort"
	"strings"
	"unsafe"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

func CheckSignBody() gin.HandlerFunc {
	return func(c *gin.Context) {
		body, err := io.ReadAll(c.Request.Body)
		c.Request.Body.Close()
		if err != nil {
			logs.Error("CheckSignBody body error ", err)
			c.JSON(200, gin.H{
				"code": 0,
				"msg":  "sign not found body",
			})
			c.Abort()
			return
		}
		bodyStr := *(*string)(unsafe.Pointer(&body))
		paramMap := ParseJsonToClassMap(bodyStr)
		result := checkSignData(paramMap, "Sign")
		if result != nil {
			c.<PERSON>(200, result)
			c.Abort()
			return
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		c.Next()
	}
}

func CheckSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求参数
		params := c.Request.URL.Query()
		// 将参数存储在 map 中
		paramMap := make(map[string]string)
		for key, values := range params {
			paramMap[key] = values[0]
		}

		result := checkSignData(paramMap, "sign")
		if result != nil {
			c.JSON(200, result)
			c.Abort()
			return
		}

		c.Next()
	}

}

func checkSignData(paramMap map[string]string, sign string) gin.H {
	reqSign, err := paramMap[sign]
	if !err {
		return gin.H{
			"code": 0,
			"msg":  "sign not found",
			"data": "",
		}
	}
	// 排除参数中的 "sign" 参数
	delete(paramMap, sign)
	logs.Info("checkSignData paramMap ", paramMap)
	// 对参数按照键排序
	keys := make([]string, 0, len(paramMap))
	for key := range paramMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	// 遍历排序后的参数，处理键值对
	var sortedParams []string
	for _, key := range keys {
		sortedParams = append(sortedParams, fmt.Sprintf("%s=%s", key, paramMap[key]))
	}
	sortedParamsStr := strings.Join(sortedParams, "&")
	str := sortedParamsStr + "&key=kTEGAJ59zZABQ4dRInVcOC1og1vj06t1"
	// 创建一个 MD5 实例
	hash := md5.New()
	// 将字符串转换为字节数组并计算 MD5 值
	hash.Write([]byte(str))
	hashBytes := hash.Sum(nil)
	// 将 MD5 值转换为十六进制字符串
	md5Str := hex.EncodeToString(hashBytes)
	if subtle.ConstantTimeCompare([]byte(md5Str), []byte(reqSign)) != 1 {
		logs.Info("Signature mismatch detected")
		return gin.H{
			"code": 0,
			"msg":  "signature verification failed",
			"data": "",
		}
	}

	return nil
}

func Sign(paramMap map[string]string) string {
	// 排除参数中的 "sign" 参数
	delete(paramMap, "sign")
	// 对参数按照键排序
	keys := make([]string, 0, len(paramMap))
	for key := range paramMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	// 遍历排序后的参数，处理键值对
	var sortedParams []string
	for _, key := range keys {
		sortedParams = append(sortedParams, fmt.Sprintf("%s=%s", key, paramMap[key]))
	}
	sortedParamsStr := strings.Join(sortedParams, "&")
	str := sortedParamsStr + "&key=kTEGAJ59zZABQ4dRInVcOC1og1vj06t1"
	// 创建一个 MD5 实例
	hash := md5.New()
	// 将字符串转换为字节数组并计算 MD5 值
	hash.Write([]byte(str))
	hashBytes := hash.Sum(nil)
	// 将 MD5 值转换为十六进制字符串
	md5Str := hex.EncodeToString(hashBytes)

	return md5Str
}

func ParseJsonToClassMap(jsonStr string) (m map[string]string) {
	m = make(map[string]string)
	result := gjson.Parse(jsonStr)
	result.ForEach(func(key, value gjson.Result) bool {
		if value.Type == gjson.JSON && gjson.Valid(value.String()) {
			m[key.String()] = JsonToString(value.String())
		} else {
			m[key.String()] = value.String()
		}
		return true
	})
	return
}

func JsonToString(jsonStr string) string {
	str := ""
	r := gjson.Parse(jsonStr)
	r.ForEach(func(key, value gjson.Result) bool {
		if value.Type == gjson.JSON && gjson.Valid(value.String()) {
			str += JsonToString(value.String())
		} else {
			str += value.String()
		}
		return true
	})
	return str
}
