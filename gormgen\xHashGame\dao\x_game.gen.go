// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXGame(db *gorm.DB, opts ...gen.DOOption) xGame {
	_xGame := xGame{}

	_xGame.xGameDo.UseDB(db, opts...)
	_xGame.xGameDo.UseModel(&model.XGame{})

	tableName := _xGame.xGameDo.TableName()
	_xGame.ALL = field.NewAsterisk(tableName)
	_xGame.ID = field.NewInt32(tableName, "Id")
	_xGame.SellerID = field.NewInt32(tableName, "SellerId")
	_xGame.GameID = field.NewInt32(tableName, "GameId")
	_xGame.RoomLevel = field.NewInt32(tableName, "RoomLevel")
	_xGame.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xGame.GameName = field.NewString(tableName, "GameName")
	_xGame.RoomName = field.NewString(tableName, "RoomName")
	_xGame.Address = field.NewString(tableName, "Address")
	_xGame.FeeRate = field.NewFloat64(tableName, "FeeRate")
	_xGame.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xGame.RewardRateEx = field.NewString(tableName, "RewardRateEx")
	_xGame.UsdtLimitMin = field.NewFloat64(tableName, "UsdtLimitMin")
	_xGame.UsdtLimitMax = field.NewFloat64(tableName, "UsdtLimitMax")
	_xGame.TrxLimitMin = field.NewFloat64(tableName, "TrxLimitMin")
	_xGame.TrxLimitMax = field.NewFloat64(tableName, "TrxLimitMax")
	_xGame.BackFeeRate = field.NewFloat64(tableName, "BackFeeRate")
	_xGame.AgentRate = field.NewFloat64(tableName, "AgentRate")
	_xGame.RewardDownRole = field.NewString(tableName, "RewardDownRole")
	_xGame.StopRewardDownRole = field.NewString(tableName, "StopRewardDownRole")
	_xGame.State = field.NewInt32(tableName, "State")
	_xGame.LiuSuiType = field.NewInt32(tableName, "LiuSuiType")
	_xGame.FenChengRate = field.NewFloat64(tableName, "FenChengRate")
	_xGame.AmountTrx = field.NewFloat64(tableName, "AmountTrx")
	_xGame.AmountUsdt = field.NewFloat64(tableName, "AmountUsdt")
	_xGame.Chip = field.NewString(tableName, "Chip")
	_xGame.TiYan = field.NewInt32(tableName, "TiYan")
	_xGame.Lottery = field.NewString(tableName, "Lottery")
	_xGame.SUsdtLimitMin = field.NewFloat64(tableName, "SUsdtLimitMin")
	_xGame.SUsdtLimitMax = field.NewFloat64(tableName, "SUsdtLimitMax")
	_xGame.STrxLimitMin = field.NewFloat64(tableName, "STrxLimitMin")
	_xGame.STrxLimitMax = field.NewFloat64(tableName, "STrxLimitMax")
	_xGame.PTrxLimitMin = field.NewFloat64(tableName, "PTrxLimitMin")
	_xGame.PTrxLimitMax = field.NewFloat64(tableName, "PTrxLimitMax")
	_xGame.PUsdtLimitMax = field.NewFloat64(tableName, "PUsdtLimitMax")
	_xGame.PUsdtLimitMin = field.NewFloat64(tableName, "PUsdtLimitMin")
	_xGame.IsVipGame = field.NewInt32(tableName, "IsVipGame")
	_xGame.UsdtLimitMinHe = field.NewFloat64(tableName, "UsdtLimitMinHe")
	_xGame.UsdtLimitMaxHe = field.NewFloat64(tableName, "UsdtLimitMaxHe")
	_xGame.TrxLimitMinHe = field.NewFloat64(tableName, "TrxLimitMinHe")
	_xGame.TrxLimitMaxHe = field.NewFloat64(tableName, "TrxLimitMaxHe")
	_xGame.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xGame.AgentName = field.NewString(tableName, "AgentName")
	_xGame.IsRecom = field.NewInt32(tableName, "IsRecom")
	_xGame.IsNew = field.NewInt32(tableName, "IsNew")
	_xGame.IsHot = field.NewInt32(tableName, "IsHot")
	_xGame.OnlineMin = field.NewInt32(tableName, "OnlineMin")
	_xGame.OnlineMax = field.NewInt32(tableName, "OnlineMax")
	_xGame.BscAddress = field.NewString(tableName, "BscAddress")
	_xGame.UsdtBscLimitMax = field.NewFloat64(tableName, "UsdtBscLimitMax")
	_xGame.UsdtBscLimitMin = field.NewFloat64(tableName, "UsdtBscLimitMin")
	_xGame.UsdtEthLimitMax = field.NewFloat64(tableName, "UsdtEthLimitMax")
	_xGame.UsdtEthLimitMin = field.NewFloat64(tableName, "UsdtEthLimitMin")

	_xGame.fillFieldMap()

	return _xGame
}

type xGame struct {
	xGameDo xGameDo

	ALL                field.Asterisk
	ID                 field.Int32
	SellerID           field.Int32   // 运营商
	GameID             field.Int32   // 游戏id
	RoomLevel          field.Int32   // 房间等级
	ChannelID          field.Int32   // 渠道商
	GameName           field.String  // 游戏名称
	RoomName           field.String  // 房间名称
	Address            field.String  // 投注地址
	FeeRate            field.Float64 // 中奖手续费率
	RewardRate         field.Float64 // 游戏赔率
	RewardRateEx       field.String  // 特殊赔率
	UsdtLimitMin       field.Float64 // 最小下注usdt
	UsdtLimitMax       field.Float64 // 最大下注usdt
	TrxLimitMin        field.Float64 // 最少下注trx
	TrxLimitMax        field.Float64 // 最大下注trx
	BackFeeRate        field.Float64 // 返还费率
	AgentRate          field.Float64 // 分成比例
	RewardDownRole     field.String  // 降赔规则
	StopRewardDownRole field.String  // 停止降赔规则
	State              field.Int32   // 状态 1启用 2禁用
	LiuSuiType         field.Int32   // 流水算法 1单边 2双边 3输赢绝对值
	FenChengRate       field.Float64 // 代理分成比例
	AmountTrx          field.Float64
	AmountUsdt         field.Float64
	Chip               field.String
	TiYan              field.Int32 // 是否开启体验
	Lottery            field.String
	SUsdtLimitMin      field.Float64
	SUsdtLimitMax      field.Float64
	STrxLimitMin       field.Float64
	STrxLimitMax       field.Float64
	PTrxLimitMin       field.Float64
	PTrxLimitMax       field.Float64
	PUsdtLimitMax      field.Float64
	PUsdtLimitMin      field.Float64
	IsVipGame          field.Int32   // 时候参加vip流水累计 1参加,2不参加
	UsdtLimitMinHe     field.Float64 // 和最小usdt下注
	UsdtLimitMaxHe     field.Float64 // 和最大usdt下注
	TrxLimitMinHe      field.Float64 // 和最小usdt下注
	TrxLimitMaxHe      field.Float64 // 和最大trx下注
	TopAgentID         field.Int32
	AgentName          field.String
	IsRecom            field.Int32   // 是否推荐
	IsNew              field.Int32   // 是否最新
	IsHot              field.Int32   // 是否热门
	OnlineMin          field.Int32   // 最小在玩人数
	OnlineMax          field.Int32   // 最大在玩人数
	BscAddress         field.String  // bsc转账地址
	UsdtBscLimitMax    field.Float64 // bsc最大下注usdt
	UsdtBscLimitMin    field.Float64 // 最小下注usdt
	UsdtEthLimitMax    field.Float64 // eth最大下注usdt
	UsdtEthLimitMin    field.Float64 // eth最小下注usdt

	fieldMap map[string]field.Expr
}

func (x xGame) Table(newTableName string) *xGame {
	x.xGameDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xGame) As(alias string) *xGame {
	x.xGameDo.DO = *(x.xGameDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xGame) updateTableName(table string) *xGame {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.GameID = field.NewInt32(table, "GameId")
	x.RoomLevel = field.NewInt32(table, "RoomLevel")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.GameName = field.NewString(table, "GameName")
	x.RoomName = field.NewString(table, "RoomName")
	x.Address = field.NewString(table, "Address")
	x.FeeRate = field.NewFloat64(table, "FeeRate")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.RewardRateEx = field.NewString(table, "RewardRateEx")
	x.UsdtLimitMin = field.NewFloat64(table, "UsdtLimitMin")
	x.UsdtLimitMax = field.NewFloat64(table, "UsdtLimitMax")
	x.TrxLimitMin = field.NewFloat64(table, "TrxLimitMin")
	x.TrxLimitMax = field.NewFloat64(table, "TrxLimitMax")
	x.BackFeeRate = field.NewFloat64(table, "BackFeeRate")
	x.AgentRate = field.NewFloat64(table, "AgentRate")
	x.RewardDownRole = field.NewString(table, "RewardDownRole")
	x.StopRewardDownRole = field.NewString(table, "StopRewardDownRole")
	x.State = field.NewInt32(table, "State")
	x.LiuSuiType = field.NewInt32(table, "LiuSuiType")
	x.FenChengRate = field.NewFloat64(table, "FenChengRate")
	x.AmountTrx = field.NewFloat64(table, "AmountTrx")
	x.AmountUsdt = field.NewFloat64(table, "AmountUsdt")
	x.Chip = field.NewString(table, "Chip")
	x.TiYan = field.NewInt32(table, "TiYan")
	x.Lottery = field.NewString(table, "Lottery")
	x.SUsdtLimitMin = field.NewFloat64(table, "SUsdtLimitMin")
	x.SUsdtLimitMax = field.NewFloat64(table, "SUsdtLimitMax")
	x.STrxLimitMin = field.NewFloat64(table, "STrxLimitMin")
	x.STrxLimitMax = field.NewFloat64(table, "STrxLimitMax")
	x.PTrxLimitMin = field.NewFloat64(table, "PTrxLimitMin")
	x.PTrxLimitMax = field.NewFloat64(table, "PTrxLimitMax")
	x.PUsdtLimitMax = field.NewFloat64(table, "PUsdtLimitMax")
	x.PUsdtLimitMin = field.NewFloat64(table, "PUsdtLimitMin")
	x.IsVipGame = field.NewInt32(table, "IsVipGame")
	x.UsdtLimitMinHe = field.NewFloat64(table, "UsdtLimitMinHe")
	x.UsdtLimitMaxHe = field.NewFloat64(table, "UsdtLimitMaxHe")
	x.TrxLimitMinHe = field.NewFloat64(table, "TrxLimitMinHe")
	x.TrxLimitMaxHe = field.NewFloat64(table, "TrxLimitMaxHe")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.AgentName = field.NewString(table, "AgentName")
	x.IsRecom = field.NewInt32(table, "IsRecom")
	x.IsNew = field.NewInt32(table, "IsNew")
	x.IsHot = field.NewInt32(table, "IsHot")
	x.OnlineMin = field.NewInt32(table, "OnlineMin")
	x.OnlineMax = field.NewInt32(table, "OnlineMax")
	x.BscAddress = field.NewString(table, "BscAddress")
	x.UsdtBscLimitMax = field.NewFloat64(table, "UsdtBscLimitMax")
	x.UsdtBscLimitMin = field.NewFloat64(table, "UsdtBscLimitMin")
	x.UsdtEthLimitMax = field.NewFloat64(table, "UsdtEthLimitMax")
	x.UsdtEthLimitMin = field.NewFloat64(table, "UsdtEthLimitMin")

	x.fillFieldMap()

	return x
}

func (x *xGame) WithContext(ctx context.Context) *xGameDo { return x.xGameDo.WithContext(ctx) }

func (x xGame) TableName() string { return x.xGameDo.TableName() }

func (x xGame) Alias() string { return x.xGameDo.Alias() }

func (x xGame) Columns(cols ...field.Expr) gen.Columns { return x.xGameDo.Columns(cols...) }

func (x *xGame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xGame) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 52)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["RoomLevel"] = x.RoomLevel
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["RoomName"] = x.RoomName
	x.fieldMap["Address"] = x.Address
	x.fieldMap["FeeRate"] = x.FeeRate
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["RewardRateEx"] = x.RewardRateEx
	x.fieldMap["UsdtLimitMin"] = x.UsdtLimitMin
	x.fieldMap["UsdtLimitMax"] = x.UsdtLimitMax
	x.fieldMap["TrxLimitMin"] = x.TrxLimitMin
	x.fieldMap["TrxLimitMax"] = x.TrxLimitMax
	x.fieldMap["BackFeeRate"] = x.BackFeeRate
	x.fieldMap["AgentRate"] = x.AgentRate
	x.fieldMap["RewardDownRole"] = x.RewardDownRole
	x.fieldMap["StopRewardDownRole"] = x.StopRewardDownRole
	x.fieldMap["State"] = x.State
	x.fieldMap["LiuSuiType"] = x.LiuSuiType
	x.fieldMap["FenChengRate"] = x.FenChengRate
	x.fieldMap["AmountTrx"] = x.AmountTrx
	x.fieldMap["AmountUsdt"] = x.AmountUsdt
	x.fieldMap["Chip"] = x.Chip
	x.fieldMap["TiYan"] = x.TiYan
	x.fieldMap["Lottery"] = x.Lottery
	x.fieldMap["SUsdtLimitMin"] = x.SUsdtLimitMin
	x.fieldMap["SUsdtLimitMax"] = x.SUsdtLimitMax
	x.fieldMap["STrxLimitMin"] = x.STrxLimitMin
	x.fieldMap["STrxLimitMax"] = x.STrxLimitMax
	x.fieldMap["PTrxLimitMin"] = x.PTrxLimitMin
	x.fieldMap["PTrxLimitMax"] = x.PTrxLimitMax
	x.fieldMap["PUsdtLimitMax"] = x.PUsdtLimitMax
	x.fieldMap["PUsdtLimitMin"] = x.PUsdtLimitMin
	x.fieldMap["IsVipGame"] = x.IsVipGame
	x.fieldMap["UsdtLimitMinHe"] = x.UsdtLimitMinHe
	x.fieldMap["UsdtLimitMaxHe"] = x.UsdtLimitMaxHe
	x.fieldMap["TrxLimitMinHe"] = x.TrxLimitMinHe
	x.fieldMap["TrxLimitMaxHe"] = x.TrxLimitMaxHe
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["AgentName"] = x.AgentName
	x.fieldMap["IsRecom"] = x.IsRecom
	x.fieldMap["IsNew"] = x.IsNew
	x.fieldMap["IsHot"] = x.IsHot
	x.fieldMap["OnlineMin"] = x.OnlineMin
	x.fieldMap["OnlineMax"] = x.OnlineMax
	x.fieldMap["BscAddress"] = x.BscAddress
	x.fieldMap["UsdtBscLimitMax"] = x.UsdtBscLimitMax
	x.fieldMap["UsdtBscLimitMin"] = x.UsdtBscLimitMin
	x.fieldMap["UsdtEthLimitMax"] = x.UsdtEthLimitMax
	x.fieldMap["UsdtEthLimitMin"] = x.UsdtEthLimitMin
}

func (x xGame) clone(db *gorm.DB) xGame {
	x.xGameDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xGame) replaceDB(db *gorm.DB) xGame {
	x.xGameDo.ReplaceDB(db)
	return x
}

type xGameDo struct{ gen.DO }

func (x xGameDo) Debug() *xGameDo {
	return x.withDO(x.DO.Debug())
}

func (x xGameDo) WithContext(ctx context.Context) *xGameDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xGameDo) ReadDB() *xGameDo {
	return x.Clauses(dbresolver.Read)
}

func (x xGameDo) WriteDB() *xGameDo {
	return x.Clauses(dbresolver.Write)
}

func (x xGameDo) Session(config *gorm.Session) *xGameDo {
	return x.withDO(x.DO.Session(config))
}

func (x xGameDo) Clauses(conds ...clause.Expression) *xGameDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xGameDo) Returning(value interface{}, columns ...string) *xGameDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xGameDo) Not(conds ...gen.Condition) *xGameDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xGameDo) Or(conds ...gen.Condition) *xGameDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xGameDo) Select(conds ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xGameDo) Where(conds ...gen.Condition) *xGameDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xGameDo) Order(conds ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xGameDo) Distinct(cols ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xGameDo) Omit(cols ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xGameDo) Join(table schema.Tabler, on ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xGameDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xGameDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xGameDo) RightJoin(table schema.Tabler, on ...field.Expr) *xGameDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xGameDo) Group(cols ...field.Expr) *xGameDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xGameDo) Having(conds ...gen.Condition) *xGameDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xGameDo) Limit(limit int) *xGameDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xGameDo) Offset(offset int) *xGameDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xGameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xGameDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xGameDo) Unscoped() *xGameDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xGameDo) Create(values ...*model.XGame) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xGameDo) CreateInBatches(values []*model.XGame, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xGameDo) Save(values ...*model.XGame) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xGameDo) First() (*model.XGame, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGame), nil
	}
}

func (x xGameDo) Take() (*model.XGame, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGame), nil
	}
}

func (x xGameDo) Last() (*model.XGame, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGame), nil
	}
}

func (x xGameDo) Find() ([]*model.XGame, error) {
	result, err := x.DO.Find()
	return result.([]*model.XGame), err
}

func (x xGameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XGame, err error) {
	buf := make([]*model.XGame, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xGameDo) FindInBatches(result *[]*model.XGame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xGameDo) Attrs(attrs ...field.AssignExpr) *xGameDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xGameDo) Assign(attrs ...field.AssignExpr) *xGameDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xGameDo) Joins(fields ...field.RelationField) *xGameDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xGameDo) Preload(fields ...field.RelationField) *xGameDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xGameDo) FirstOrInit() (*model.XGame, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGame), nil
	}
}

func (x xGameDo) FirstOrCreate() (*model.XGame, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGame), nil
	}
}

func (x xGameDo) FindByPage(offset int, limit int) (result []*model.XGame, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xGameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xGameDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xGameDo) Delete(models ...*model.XGame) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xGameDo) withDO(do gen.Dao) *xGameDo {
	x.DO = *do.(*gen.DO)
	return x
}
