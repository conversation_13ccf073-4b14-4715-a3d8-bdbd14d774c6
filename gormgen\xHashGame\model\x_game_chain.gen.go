// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXGameChain = "x_game_chain"

// XGameChain mapped from table <x_game_chain>
type XGameChain struct {
	ChainType  int32     `gorm:"column:ChainType;primaryKey" json:"ChainType"`
	Chain      string    `gorm:"column:Chain;comment:名称" json:"Chain"`                   // 名称
	State      int32     `gorm:"column:State;default:1;comment:状态 1开启 2关闭" json:"State"` // 状态 1开启 2关闭
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
	TranState  int32     `gorm:"column:TranState;default:1" json:"TranState"`
}

// TableName XGameChain's table name
func (*XGameChain) TableName() string {
	return TableNameXGameChain
}
