package paycontroller

import (
	"encoding/json"
	"errors"
	"io"
	"math"
	"math/rand"
	"strconv"

	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

// EPay 全局实例
var EPay = new(ePay)

// ePay EPay支付处理器
type ePay struct {
	Base
}

// PayMethodCfg EPay支付方式配置
type PayMethodCfg struct {
	TradeNo string `json:"trade_no"` // 商户号
	AppID   string `json:"app_id"`   // 应用ID
	Key     string `json:"key"`      // 签名密钥
	URL     string `json:"url"`      // 支付接口地址
	Cburl   string `json:"cburl"`    // 回调地址
}

// EpayRechargeRes EPay充值响应结构
type EpayRechargeRes struct {
	Code       int    `json:"code"`         // 响应码
	Msg        string `json:"msg"`          // 响应消息
	PayURL     string `json:"pay_url"`      // 支付链接
	QrCode     string `json:"qr_code"`      // 二维码
	OrderNo    string `json:"order_no"`     // 订单号
	DisOrderNo string `json:"dis_order_no"` // 第三方订单号
	Sign       string `json:"sign"`         // 签名
	CreateTime int    `json:"create_time"`  // 创建时间
}

// EpayRechargeCallbackReq EPay充值回调请求结构
type EpayRechargeCallbackReq struct {
	TradeNo    int    `json:"trade_no"`     // 商户号
	Status     int    `json:"status"`       // 状态
	OrderNo    string `json:"order_no"`     // 订单号
	DisOrderNo string `json:"dis_order_no"` // 第三方订单号
	OrderPrice int    `json:"order_price"`  // 订单金额（分）
	RealPrice  int    `json:"real_price"`   // 实际金额（分）
	Fee        int    `json:"fee"`          // 手续费（分）
	NtiTime    int    `json:"nti_time"`     // 通知时间
	Payer      string `json:"payer"`        // 付款人
	Attach     string `json:"attach"`       // 附加数据
	CreateTime int    `json:"create_time"`  // 创建时间
	Sign       string `json:"sign"`         // 签名
}

// EpayWithdrawCallbackReq EPay提现回调请求结构
type EpayWithdrawCallbackReq struct {
	TradeNo    int    `json:"trade_no"`     // 商户号
	Status     int    `json:"status"`       // 状态
	OrderNo    string `json:"order_no"`     // 订单号
	DisOrderNo string `json:"dis_order_no"` // 第三方订单号
	Price      int    `json:"price"`        // 金额（分）
	Fee        int    `json:"fee"`          // 手续费（分）
	NtiTime    int    `json:"nti_time"`     // 通知时间
	Attach     string `json:"attach"`       // 附加数据
	Remark     string `json:"remark"`       // 备注
	CreateTime int    `json:"create_time"`  // 创建时间
	Sign       string `json:"sign"`         // 签名
}

// Init 初始化EPay支付路由
func (c *ePay) Init() {
	server.Http().PostNoAuth("/api/epay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/epay/withdraw/callback", c.withdrawCallback)
}

// Recharge 处理EPay充值请求
func (c *ePay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	// 1. 获取支付方式配置
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	var cfg PayMethodCfg
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		ctx.RespErr(errors.New("支付配置解析失败"), &errcode)
		return
	}

	// 2. 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	// 3. 计算汇率和实际金额
	var rate, amount float64
	if user.SellerID == 26 {
		// 运营商ID为26时，不进行汇率转换
		rate = 0
		amount = float64(req.Amount)
	} else {
		// 其他运营商需要进行汇率转换
		rate, err = c.getWithdrawRate(req.Symbol)
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode)
			return
		}
		amount = float64(req.Amount) / rate
	}

	// 4. 创建充值订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     int32(user.SellerID),
		ChannelID:    int32(user.ChannelID),
		UserID:       int32(user.UserID),
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      10,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3, // 待支付状态
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	err = tx.XRecharge.WithContext(ctx.Gin()).
		Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).
		Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 5. 构建支付请求参数
	params := xgo.H{
		"trade_no":       cfg.TradeNo,
		"app_id":         cfg.AppID,
		"pay_code":       payMethod.PayType,
		"price":          int(req.Amount * 100), // 转换为分
		"order_no":       rechargeOrder.ID,
		"pay_notice_url": cfg.Cburl + "/api/epay/recharge/callback",
		"user_id":        rand.Int63n(1000000),
		"user_ip":        "127.0.0.1",
	}
	params["sign"] = c.generateMd5Sign(params, cfg.Key)

	// 6. 发起支付请求
	jsonData, _ := json.Marshal(&params)
	resp, err := c.post(cfg.URL+"/payApi/PayApi/CreateOrder", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)

	if err != nil {
		logs.Info("请求EPay支付失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("请求EPay支付失败"), &errcode)
		return
	}

	// 7. 解析支付响应
	var rechargeRes EpayRechargeRes
	if err := json.Unmarshal(resp.Body(), &rechargeRes); err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("支付响应解析失败"), &errcode)
		return
	}

	if rechargeRes.Code != 200 {
		tx.Rollback()
		ctx.RespErr(errors.New(rechargeRes.Msg), &errcode)
		return
	}

	// 更新三方订单号（在事务内部）
	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
		Update(tx.XRecharge.ThirdID, rechargeRes.DisOrderNo)
	if err != nil {
		logs.Error("ePay充值: 更新三方订单号失败:", err)
		tx.Rollback()
		ctx.RespErr(errors.New("更新三方订单号失败"), &errcode)
		return
	}

	// 8. 提交事务并返回结果
	tx.Commit()

	ctx.RespOK(xgo.H{
		"payurl":  rechargeRes.PayURL,
		"orderId": rechargeOrder.ID,
	})
}

// rechargeCallback 处理EPay充值回调
func (c *ePay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 1. 读取回调数据
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("EPay充值回调数据:", string(body))

	var callbackReq EpayRechargeCallbackReq
	if err := json.Unmarshal(body, &callbackReq); err != nil {
		logs.Error("EPay充值回调数据解析失败:", err)
		ctx.Gin().String(200, "数据格式错误")
		return
	}

	// 2. 验证订单是否存在
	orderNo, err := strconv.Atoi(callbackReq.OrderNo)
	if err != nil {
		logs.Error("EPay充值回调订单号格式错误:", err)
		ctx.Gin().String(200, "订单号格式错误")
		return
	}

	order, err := c.getRechargeOrder(orderNo)
	if err != nil {
		logs.Info("EPay充值回调获取订单失败:", err)
		ctx.Gin().String(200, "订单号不存在")
		return
	}

	// 3. 检查订单状态
	if order.State == 5 {
		// 订单已经处理成功
		ctx.Gin().String(200, "success")
		return
	}

	// 4. 验证支付金额
	orderAmount := float64(callbackReq.OrderPrice) / 100 // 转换为元
	if math.Abs(order.Amount-orderAmount) > 0.01 {
		logs.Info("EPay充值回调金额不一致, 订单金额:", order.Amount, "回调金额:", orderAmount)
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 5. 获取支付方式配置并验证签名
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Info("EPay充值回调获取支付方式失败:", err)
		ctx.Gin().String(200, "支付方式不存在")
		return
	}

	var cfg PayMethodCfg
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("EPay充值回调支付配置解析失败:", err)
		ctx.Gin().String(200, "支付配置错误")
		return
	}

	// 6. 验证签名
	expectedSign := c.generateMd5Sign(xgo.H{
		"trade_no":     callbackReq.TradeNo,
		"status":       callbackReq.Status,
		"order_no":     callbackReq.OrderNo,
		"dis_order_no": callbackReq.DisOrderNo,
		"order_price":  callbackReq.OrderPrice,
		"real_price":   callbackReq.RealPrice,
		"fee":          callbackReq.Fee,
		"nti_time":     callbackReq.NtiTime,
		"payer":        callbackReq.Payer,
		"attach":       callbackReq.Attach,
		"create_time":  callbackReq.CreateTime,
	}, cfg.Key)

	if expectedSign != callbackReq.Sign {
		logs.Info("EPay充值回调签名不一致, 期望:", expectedSign, "实际:", callbackReq.Sign)
		ctx.Gin().String(200, "签名不一致")
		return
	}

	// 7. 验证实际充值金额
	realAmount := float64(callbackReq.RealPrice) / 100 // 转换为元
	if math.Abs(order.Amount-realAmount) > 0.01 {
		logs.Info("EPay充值回调实际金额不一致, 订单金额:", order.Amount, "实际金额:", realAmount)
		ctx.Gin().String(200, "充值订单金额和实际充值金额不一致")
		return
	}

	// 8. 使用事务更新第三方订单号并处理充值成功逻辑
	tx := server.DaoxHashGame().Begin()

	// 更新第三方订单号
	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(order.ID)).
		Update(tx.XRecharge.ThirdID, callbackReq.DisOrderNo)
	if err != nil {
		logs.Error("EPay充值回调更新第三方订单号失败:", err)
		tx.Rollback()
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}

	tx.Commit()

	// 9. 处理充值成功逻辑
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

	ctx.Gin().String(200, "success")
}

// withdrawCallback 处理EPay提现回调
func (c *ePay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 1. 读取回调数据
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("EPay提现回调数据:", string(body))

	var callbackReq EpayWithdrawCallbackReq
	if err := json.Unmarshal(body, &callbackReq); err != nil {
		logs.Error("EPay提现回调数据解析失败:", err)
		ctx.Gin().String(200, "数据格式错误")
		return
	}

	// 2. 验证订单是否存在
	orderNo, err := strconv.Atoi(callbackReq.OrderNo)
	if err != nil {
		logs.Error("EPay提现回调订单号格式错误:", err)
		ctx.Gin().String(200, "订单号格式错误")
		return
	}

	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		logs.Info("EPay提现回调获取订单失败:", err)
		c.withdrawCallbackHandel(orderNo, 7)
		ctx.Gin().String(200, "订单号不存在")
		return
	}

	// 3. 检查订单状态
	if order.State == 6 {
		// 订单已经处理成功
		ctx.Gin().String(200, "success")
		return
	}

	// 4. 验证提现金额
	callbackAmount := float64(callbackReq.Price) / 100 // 转换为元
	if math.Abs(order.RealAmount-callbackAmount) > 0.01 {
		logs.Info("EPay提现回调金额不一致, 订单金额:", order.RealAmount, "回调金额:", callbackAmount)
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 5. 获取支付方式配置（用于后续可能的签名验证）
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Info("EPay提现回调获取支付方式失败:", err)
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, "支付方式不存在")
		return
	}

	var cfg PayMethodCfg
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("EPay提现回调支付配置解析失败:", err)
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, "支付配置错误")
		return
	}

	// 6. 根据回调状态处理订单
	if callbackReq.Status == 2 {
		// 提现成功
		c.withdrawCallbackHandel(int(order.ID), 6)
		logs.Info("EPay提现成功, 订单ID:", order.ID)
	} else {
		// 提现失败
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("EPay提现失败, 订单ID:", order.ID, "状态:", callbackReq.Status, "备注:", callbackReq.Remark)
	}

	ctx.Gin().String(200, "success")
}
