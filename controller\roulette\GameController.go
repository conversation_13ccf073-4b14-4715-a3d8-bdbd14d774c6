package roulette

import (
	"xserver/abugo"
)

type GameController struct {
	Redis *abugo.AbuRedis
}

func (c *GameController) Init() {
	// group := server.Http().NewGroup("/api/roulette")
	// {
	// 	// group.PostNoAuth("/loginUrl", c.loginUrl)
	// 	//group.GetNoAuth("/ws", c.handleWebSocket)
	// 	group.PostNoAuth("/verify", c.verify)
	// }

}

// func (c *GameController) loginUrl(ctx *abugo.AbuHttpContent) {

// 	type RequestData struct {
// 		Lang    string
// 		GameId  int
// 		ChainId int
// 	}

// 	errcode := 0
// 	request := RequestData{}
// 	err := ctx.RequestData(&request)
// 	if ctx.RespErr(err, &errcode) {
// 		return
// 	}

// 	// 获取游戏链接
// 	gameHost := viper.GetString("roulette_host")
// 	token := ctx.Gin().GetHeader("x-token")
// 	scheme := ctx.Gin().Request.URL.Scheme
// 	if scheme == "" {
// 		if ctx.Gin().Request.TLS != nil {
// 			scheme = "https://"
// 		} else {
// 			scheme = "http://"
// 		}
// 	}
// 	apiHost := scheme + ctx.Gin().Request.Host

// 	gameUrl := fmt.Sprintf("%v?gi=%v&sip=%v&ot=%v&l=%v&cid=%v", gameHost, request.GameId, apiHost, token, request.Lang, request.ChainId)

// 	ctx.RespOK(map[string]string{
// 		"url": gameUrl,
// 	})
// }

// func (c *GameController) verify(ctx *abugo.AbuHttpContent) {
// 	token := ctx.Gin().GetHeader("x-token")
// 	// 校验token
// 	tokenData := server.GetTokenFromRedis(token)
// 	if tokenData == nil {
// 		token = ""
// 	}

// 	scheme := ctx.Gin().Request.URL.Scheme
// 	if scheme == "" {
// 		if ctx.Gin().Request.TLS != nil {
// 			scheme = "wss://"
// 		} else {
// 			scheme = "ws://"
// 		}
// 	}

// 	host := strings.Replace(ctx.Gin().Request.Host, "www.", "", -1)
// 	host = strings.Split(host, ":")[0]

// 	ws := scheme + host + "/api/ws"
// 	ctx.RespOK(map[string]string{
// 		"ws": ws,
// 		"ot": token,
// 	})
// }
