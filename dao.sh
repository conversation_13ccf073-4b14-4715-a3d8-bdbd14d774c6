#!/bin/bash

# 设置初始的tables变量
tables="x_user,x_tg_robot_guide,x_seller,x_channel,x_withdraw,x_user_address,x_finance_symbol,x_game_brand"

# 逐步添加更多表名到tables变量中
tables="$tables,x_order,x_third_dianzhi,x_third_live,x_third_lottery,x_third_qipai,x_third_quwei,x_third_sport,x_verify,x_game_list,x_custom_third,x_game,x_game_period,x_game_chain"
tables="$tables,x_agent,x_agent_child,x_agent_independence,x_agent_dailly,x_agent_independent_commission_detail,x_agent_code_t1,x_agent_commission_config,x_agent_independence_fencheng_history"
tables="$tables,x_config,x_user_settings,x_recharge,x_user_stat_address,x_user_wallet,x_withdraw_address,x_tiyanjing,x_tiyanjinex,x_active_reward_audit,x_active_reward,x_amount_change_log,x_user_dailly,x_vip_info,x_caijing_detail,x_active_log_user_sign"
tables="$tables,x_dict_gametype,x_tb_winlost_config,x_tb_banker_config,x_tb_banker_user,x_finance_method,x_user_active,x_bonus_task_user"
tables="$tables,x_transation_ut,x_withdraw_limit_config,x_vip_define,x_active_define,x_channel_game_list,x_channel_host,x_lang_game_list,x_lang_list,x_notice_v2,x_home_carousel_v2,x_vip_dailly,x_vip_monthly,x_vip_weekly,x_rank_new,x_liuhecai_map"
tables="$tables,x_bonus_task_user,x_user_recharge_withard,x_online_web_user,x_online_play_user,x_online_game_user,x_online_brand_user,x_user_recharge_withard_date,x_user_online_process,x_rank_data,x_user_more,x_custom_dailly,x_active_redeemcode_record"

# 使用gentool工具生成DAO代码
gentool -dsn "userbu:aw9#gf*S7fT1P@tcp(*************:14578)/x_hash_game?charset=utf8mb4&parseTime=True&loc=Local" -tables $tables -outPath "./gormgen/xHashGame/dao/"

tables2="x_tg_robot_log_action,x_tg_robot_stat_hour,x_tg_robot_stat_date"

gentool -dsn "userbu:aw9#gf*S7fT1P@tcp(*************:14578)/x_hash_stat?charset=utf8mb4&parseTime=True&loc=Local" -tables $tables2 -outPath "./gormgen/xHashStat/dao/"
