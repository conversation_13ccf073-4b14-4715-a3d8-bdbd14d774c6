// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXWithdrawAddress = "x_withdraw_address"

// XWithdrawAddress 用户区块链提现地址
type XWithdrawAddress struct {
	UserID     int32     `gorm:"column:UserId;primaryKey;comment:用户Id" json:"UserId"`                        // 用户Id
	Net        string    `gorm:"column:Net;primaryKey;comment:区块链网络" json:"Net"`                             // 区块链网络
	Address    string    `gorm:"column:Address;primaryKey;comment:地址" json:"Address"`                        // 地址
	State      int32     `gorm:"column:State;default:2;comment:状态 1已验证 2未验证" json:"State"`                   // 状态 1已验证 2未验证
	Memo       string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                         // 备注
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	Symbol     string    `gorm:"column:Symbol;default:_utf8mb4\'usdt\';comment:币种" json:"Symbol"`            // 币种
}

// TableName XWithdrawAddress's table name
func (*XWithdrawAddress) TableName() string {
	return TableNameXWithdrawAddress
}
