// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXVipDailly = "x_vip_dailly"

// XVipDailly mapped from table <x_vip_dailly>
type XVipDailly struct {
	ID                       int32     `gorm:"column:Id;not null" json:"Id"`
	RecordDate               time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	SellerID                 int32     `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID                int32     `gorm:"column:ChannelId;not null" json:"ChannelId"`
	UserID                   int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	LiuSui                   float64   `gorm:"column:LiuSui;default:0.000000;comment:总流水" json:"<PERSON><PERSON>ui"`                            // 总流水
	LiuSuiHaXi               float64   `gorm:"column:LiuSuiHaXi;default:0.000000;comment:哈希流水" json:"LiuSuiHaXi"`                   // 哈希流水
	LiuSuiHaXiRoulette       float64   `gorm:"column:LiuSuiHaXiRoulette;default:0.000000;comment:哈希轮盘流水" json:"LiuSuiHaXiRoulette"` // 哈希轮盘流水
	LiuSuiLottery            float64   `gorm:"column:LiuSuiLottery;default:0.000000;comment:彩票流水" json:"LiuSuiLottery"`             // 彩票流水
	LiuSuiLowLottery         float64   `gorm:"column:LiuSuiLowLottery;default:0.000000;comment:低频彩流水" json:"LiuSuiLowLottery"`      // 低频彩流水
	LiuSuiLiuHeLottery       float64   `gorm:"column:LiuSuiLiuHeLottery;default:0.000000;comment:六合彩流水" json:"LiuSuiLiuHeLottery"`  // 六合彩流水
	LiuSuiQiPai              float64   `gorm:"column:LiuSuiQiPai;default:0.000000;comment:棋牌流水" json:"LiuSuiQiPai"`                 // 棋牌流水
	LiuSuiDianZhi            float64   `gorm:"column:LiuSuiDianZhi;default:0.000000;comment:电子流水" json:"LiuSuiDianZhi"`             // 电子流水
	LiuSuiXiaoYouXi          float64   `gorm:"column:LiuSuiXiaoYouXi;default:0.000000;comment:小游戏流水" json:"LiuSuiXiaoYouXi"`        // 小游戏流水
	LiuSuiCryptoMarket       float64   `gorm:"column:LiuSuiCryptoMarket;default:0.000000;comment:低频彩流水" json:"LiuSuiCryptoMarket"`  // 低频彩流水
	LiuSuiLive               float64   `gorm:"column:LiuSuiLive;default:0.000000;comment:真人流水" json:"LiuSuiLive"`                   // 真人流水
	LiuSuiSport              float64   `gorm:"column:LiuSuiSport;default:0.000000;comment:体育流水" json:"LiuSuiSport"`                 // 体育流水
	LiuSuiTexas              float64   `gorm:"column:LiuSuiTexas;default:0.000000;comment:德州流水" json:"LiuSuiTexas"`                 // 德州流水
	State                    int32     `gorm:"column:State;default:1;comment:状态 1 待发放,2已发放" json:"State"`                           // 状态 1 待发放,2已发放
	RewardAmount             float64   `gorm:"column:RewardAmount;default:0.000000" json:"RewardAmount"`
	RewardAmountHaXi         float64   `gorm:"column:RewardAmountHaXi;default:0.000000" json:"RewardAmountHaXi"`
	RewardAmountHaXiRoulette float64   `gorm:"column:RewardAmountHaXiRoulette;default:0.000000;comment:哈希轮盘返水" json:"RewardAmountHaXiRoulette"` // 哈希轮盘返水
	RewardAmountLottery      float64   `gorm:"column:RewardAmountLottery;default:0.000000" json:"RewardAmountLottery"`
	RewardAmountLowLottery   float64   `gorm:"column:RewardAmountLowLottery;default:0.000000" json:"RewardAmountLowLottery"`
	RewardAmountLiuHeLottery float64   `gorm:"column:RewardAmountLiuHeLottery;default:0.000000" json:"RewardAmountLiuHeLottery"`
	RewardAmountQiPai        float64   `gorm:"column:RewardAmountQiPai;default:0.000000" json:"RewardAmountQiPai"`
	RewardAmountDianZhi      float64   `gorm:"column:RewardAmountDianZhi;default:0.000000" json:"RewardAmountDianZhi"`
	RewardAmountXiaoYouXi    float64   `gorm:"column:RewardAmountXiaoYouXi;default:0.000000" json:"RewardAmountXiaoYouXi"`
	RewardAmountCryptoMarket float64   `gorm:"column:RewardAmountCryptoMarket;default:0.000000" json:"RewardAmountCryptoMarket"`
	RewardAmountLive         float64   `gorm:"column:RewardAmountLive;default:0.000000" json:"RewardAmountLive"`
	RewardAmountSport        float64   `gorm:"column:RewardAmountSport;default:0.000000" json:"RewardAmountSport"`
	RewardAmountTexas        float64   `gorm:"column:RewardAmountTexas;default:0.000000" json:"RewardAmountTexas"`
}

// TableName XVipDailly's table name
func (*XVipDailly) TableName() string {
	return TableNameXVipDailly
}
