// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserAddress(db *gorm.DB, opts ...gen.DOOption) xUserAddress {
	_xUserAddress := xUserAddress{}

	_xUserAddress.xUserAddressDo.UseDB(db, opts...)
	_xUserAddress.xUserAddressDo.UseModel(&model.XUserAddress{})

	tableName := _xUserAddress.xUserAddressDo.TableName()
	_xUserAddress.ALL = field.NewAsterisk(tableName)
	_xUserAddress.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserAddress.Address = field.NewString(tableName, "Address")
	_xUserAddress.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserAddress.BetCountTrx = field.NewInt32(tableName, "BetCountTrx")
	_xUserAddress.BetCountUsdt = field.NewInt32(tableName, "BetCountUsdt")
	_xUserAddress.BetTrx = field.NewFloat64(tableName, "BetTrx")
	_xUserAddress.BetUsdt = field.NewFloat64(tableName, "BetUsdt")
	_xUserAddress.RewardTrx = field.NewFloat64(tableName, "RewardTrx")
	_xUserAddress.RewardUsdt = field.NewFloat64(tableName, "RewardUsdt")
	_xUserAddress.LiuSuiTrx = field.NewFloat64(tableName, "LiuSuiTrx")
	_xUserAddress.LiuSuiUsdt = field.NewFloat64(tableName, "LiuSuiUsdt")

	_xUserAddress.fillFieldMap()

	return _xUserAddress
}

type xUserAddress struct {
	xUserAddressDo xUserAddressDo

	ALL          field.Asterisk
	ChannelID    field.Int32
	Address      field.String
	CreateTime   field.Time
	BetCountTrx  field.Int32
	BetCountUsdt field.Int32
	BetTrx       field.Float64
	BetUsdt      field.Float64
	RewardTrx    field.Float64
	RewardUsdt   field.Float64
	LiuSuiTrx    field.Float64
	LiuSuiUsdt   field.Float64

	fieldMap map[string]field.Expr
}

func (x xUserAddress) Table(newTableName string) *xUserAddress {
	x.xUserAddressDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserAddress) As(alias string) *xUserAddress {
	x.xUserAddressDo.DO = *(x.xUserAddressDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserAddress) updateTableName(table string) *xUserAddress {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Address = field.NewString(table, "Address")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.BetCountTrx = field.NewInt32(table, "BetCountTrx")
	x.BetCountUsdt = field.NewInt32(table, "BetCountUsdt")
	x.BetTrx = field.NewFloat64(table, "BetTrx")
	x.BetUsdt = field.NewFloat64(table, "BetUsdt")
	x.RewardTrx = field.NewFloat64(table, "RewardTrx")
	x.RewardUsdt = field.NewFloat64(table, "RewardUsdt")
	x.LiuSuiTrx = field.NewFloat64(table, "LiuSuiTrx")
	x.LiuSuiUsdt = field.NewFloat64(table, "LiuSuiUsdt")

	x.fillFieldMap()

	return x
}

func (x *xUserAddress) WithContext(ctx context.Context) *xUserAddressDo {
	return x.xUserAddressDo.WithContext(ctx)
}

func (x xUserAddress) TableName() string { return x.xUserAddressDo.TableName() }

func (x xUserAddress) Alias() string { return x.xUserAddressDo.Alias() }

func (x xUserAddress) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserAddressDo.Columns(cols...)
}

func (x *xUserAddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserAddress) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["BetCountTrx"] = x.BetCountTrx
	x.fieldMap["BetCountUsdt"] = x.BetCountUsdt
	x.fieldMap["BetTrx"] = x.BetTrx
	x.fieldMap["BetUsdt"] = x.BetUsdt
	x.fieldMap["RewardTrx"] = x.RewardTrx
	x.fieldMap["RewardUsdt"] = x.RewardUsdt
	x.fieldMap["LiuSuiTrx"] = x.LiuSuiTrx
	x.fieldMap["LiuSuiUsdt"] = x.LiuSuiUsdt
}

func (x xUserAddress) clone(db *gorm.DB) xUserAddress {
	x.xUserAddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserAddress) replaceDB(db *gorm.DB) xUserAddress {
	x.xUserAddressDo.ReplaceDB(db)
	return x
}

type xUserAddressDo struct{ gen.DO }

func (x xUserAddressDo) Debug() *xUserAddressDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserAddressDo) WithContext(ctx context.Context) *xUserAddressDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserAddressDo) ReadDB() *xUserAddressDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserAddressDo) WriteDB() *xUserAddressDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserAddressDo) Session(config *gorm.Session) *xUserAddressDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserAddressDo) Clauses(conds ...clause.Expression) *xUserAddressDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserAddressDo) Returning(value interface{}, columns ...string) *xUserAddressDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserAddressDo) Not(conds ...gen.Condition) *xUserAddressDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserAddressDo) Or(conds ...gen.Condition) *xUserAddressDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserAddressDo) Select(conds ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserAddressDo) Where(conds ...gen.Condition) *xUserAddressDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserAddressDo) Order(conds ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserAddressDo) Distinct(cols ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserAddressDo) Omit(cols ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserAddressDo) Join(table schema.Tabler, on ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserAddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserAddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserAddressDo) Group(cols ...field.Expr) *xUserAddressDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserAddressDo) Having(conds ...gen.Condition) *xUserAddressDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserAddressDo) Limit(limit int) *xUserAddressDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserAddressDo) Offset(offset int) *xUserAddressDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserAddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserAddressDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserAddressDo) Unscoped() *xUserAddressDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserAddressDo) Create(values ...*model.XUserAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserAddressDo) CreateInBatches(values []*model.XUserAddress, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserAddressDo) Save(values ...*model.XUserAddress) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserAddressDo) First() (*model.XUserAddress, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddress), nil
	}
}

func (x xUserAddressDo) Take() (*model.XUserAddress, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddress), nil
	}
}

func (x xUserAddressDo) Last() (*model.XUserAddress, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddress), nil
	}
}

func (x xUserAddressDo) Find() ([]*model.XUserAddress, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserAddress), err
}

func (x xUserAddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserAddress, err error) {
	buf := make([]*model.XUserAddress, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserAddressDo) FindInBatches(result *[]*model.XUserAddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserAddressDo) Attrs(attrs ...field.AssignExpr) *xUserAddressDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserAddressDo) Assign(attrs ...field.AssignExpr) *xUserAddressDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserAddressDo) Joins(fields ...field.RelationField) *xUserAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserAddressDo) Preload(fields ...field.RelationField) *xUserAddressDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserAddressDo) FirstOrInit() (*model.XUserAddress, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddress), nil
	}
}

func (x xUserAddressDo) FirstOrCreate() (*model.XUserAddress, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserAddress), nil
	}
}

func (x xUserAddressDo) FindByPage(offset int, limit int) (result []*model.XUserAddress, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserAddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserAddressDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserAddressDo) Delete(models ...*model.XUserAddress) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserAddressDo) withDO(do gen.Dao) *xUserAddressDo {
	x.DO = *do.(*gen.DO)
	return x
}
