package third

import (
	"encoding/json"
	"xserver/server"
	"xserver/utils"
)

func NewJDService(configValue string, fc func(int) error) *JDService {
	jd := JDService{}
	json.Unmarshal([]byte(configValue), &jd)
	jd.BrandName = "jd"
	jd.LoginPath = "/island/api/login"
	jd.OrderType = "quwei"
	jd.BalanceCReasonBet = utils.BalanceCReasonJDBet
	jd.BalanceCReasonCancel = utils.BalanceCReasonJDCancel
	jd.BalanceCReasonWin = utils.BalanceCReasonJDWin
	jd.RefreshUserAmountFunc = fc
	return &jd
}

func (e *JDService) BindRouter() {
	server.Http().Post("/api/third/jd_login", e.Login)
	server.Http().PostNoAuth("/api/jd/Balance/LockBalance", e.Bet)
	server.Http().PostNoAuth("/api/jd/Balance/UnLockBalance", e.End)
	server.Http().PostNoAuth("/api/jd/Balance/GetBalance", e.Balance)
}

type JDService struct {
	WuShuangService
}
