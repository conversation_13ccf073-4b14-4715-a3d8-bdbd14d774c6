// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXVipDailly(db *gorm.DB, opts ...gen.DOOption) xVipDailly {
	_xVipDailly := xVipDailly{}

	_xVipDailly.xVipDaillyDo.UseDB(db, opts...)
	_xVipDailly.xVipDaillyDo.UseModel(&model.XVipDailly{})

	tableName := _xVipDailly.xVipDaillyDo.TableName()
	_xVipDailly.ALL = field.NewAsterisk(tableName)
	_xVipDailly.ID = field.NewInt32(tableName, "Id")
	_xVipDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xVipDailly.SellerID = field.NewInt32(tableName, "SellerId")
	_xVipDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xVipDailly.UserID = field.NewInt32(tableName, "UserId")
	_xVipDailly.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xVipDailly.LiuSuiHaXi = field.NewFloat64(tableName, "LiuSuiHaXi")
	_xVipDailly.LiuSuiHaXiRoulette = field.NewFloat64(tableName, "LiuSuiHaXiRoulette")
	_xVipDailly.LiuSuiLottery = field.NewFloat64(tableName, "LiuSuiLottery")
	_xVipDailly.LiuSuiLowLottery = field.NewFloat64(tableName, "LiuSuiLowLottery")
	_xVipDailly.LiuSuiLiuHeLottery = field.NewFloat64(tableName, "LiuSuiLiuHeLottery")
	_xVipDailly.LiuSuiQiPai = field.NewFloat64(tableName, "LiuSuiQiPai")
	_xVipDailly.LiuSuiDianZhi = field.NewFloat64(tableName, "LiuSuiDianZhi")
	_xVipDailly.LiuSuiXiaoYouXi = field.NewFloat64(tableName, "LiuSuiXiaoYouXi")
	_xVipDailly.LiuSuiCryptoMarket = field.NewFloat64(tableName, "LiuSuiCryptoMarket")
	_xVipDailly.LiuSuiLive = field.NewFloat64(tableName, "LiuSuiLive")
	_xVipDailly.LiuSuiSport = field.NewFloat64(tableName, "LiuSuiSport")
	_xVipDailly.LiuSuiTexas = field.NewFloat64(tableName, "LiuSuiTexas")
	_xVipDailly.State = field.NewInt32(tableName, "State")
	_xVipDailly.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xVipDailly.RewardAmountHaXi = field.NewFloat64(tableName, "RewardAmountHaXi")
	_xVipDailly.RewardAmountHaXiRoulette = field.NewFloat64(tableName, "RewardAmountHaXiRoulette")
	_xVipDailly.RewardAmountLottery = field.NewFloat64(tableName, "RewardAmountLottery")
	_xVipDailly.RewardAmountLowLottery = field.NewFloat64(tableName, "RewardAmountLowLottery")
	_xVipDailly.RewardAmountLiuHeLottery = field.NewFloat64(tableName, "RewardAmountLiuHeLottery")
	_xVipDailly.RewardAmountQiPai = field.NewFloat64(tableName, "RewardAmountQiPai")
	_xVipDailly.RewardAmountDianZhi = field.NewFloat64(tableName, "RewardAmountDianZhi")
	_xVipDailly.RewardAmountXiaoYouXi = field.NewFloat64(tableName, "RewardAmountXiaoYouXi")
	_xVipDailly.RewardAmountCryptoMarket = field.NewFloat64(tableName, "RewardAmountCryptoMarket")
	_xVipDailly.RewardAmountLive = field.NewFloat64(tableName, "RewardAmountLive")
	_xVipDailly.RewardAmountSport = field.NewFloat64(tableName, "RewardAmountSport")
	_xVipDailly.RewardAmountTexas = field.NewFloat64(tableName, "RewardAmountTexas")

	_xVipDailly.fillFieldMap()

	return _xVipDailly
}

type xVipDailly struct {
	xVipDaillyDo xVipDaillyDo

	ALL                      field.Asterisk
	ID                       field.Int32
	RecordDate               field.Time
	SellerID                 field.Int32
	ChannelID                field.Int32
	UserID                   field.Int32
	LiuSui                   field.Float64 // 总流水
	LiuSuiHaXi               field.Float64 // 哈希流水
	LiuSuiHaXiRoulette       field.Float64 // 哈希轮盘流水
	LiuSuiLottery            field.Float64 // 彩票流水
	LiuSuiLowLottery         field.Float64 // 低频彩流水
	LiuSuiLiuHeLottery       field.Float64 // 六合彩流水
	LiuSuiQiPai              field.Float64 // 棋牌流水
	LiuSuiDianZhi            field.Float64 // 电子流水
	LiuSuiXiaoYouXi          field.Float64 // 小游戏流水
	LiuSuiCryptoMarket       field.Float64 // 低频彩流水
	LiuSuiLive               field.Float64 // 真人流水
	LiuSuiSport              field.Float64 // 体育流水
	LiuSuiTexas              field.Float64 // 德州流水
	State                    field.Int32   // 状态 1 待发放,2已发放
	RewardAmount             field.Float64
	RewardAmountHaXi         field.Float64
	RewardAmountHaXiRoulette field.Float64 // 哈希轮盘返水
	RewardAmountLottery      field.Float64
	RewardAmountLowLottery   field.Float64
	RewardAmountLiuHeLottery field.Float64
	RewardAmountQiPai        field.Float64
	RewardAmountDianZhi      field.Float64
	RewardAmountXiaoYouXi    field.Float64
	RewardAmountCryptoMarket field.Float64
	RewardAmountLive         field.Float64
	RewardAmountSport        field.Float64
	RewardAmountTexas        field.Float64

	fieldMap map[string]field.Expr
}

func (x xVipDailly) Table(newTableName string) *xVipDailly {
	x.xVipDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xVipDailly) As(alias string) *xVipDailly {
	x.xVipDaillyDo.DO = *(x.xVipDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xVipDailly) updateTableName(table string) *xVipDailly {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.LiuSuiHaXi = field.NewFloat64(table, "LiuSuiHaXi")
	x.LiuSuiHaXiRoulette = field.NewFloat64(table, "LiuSuiHaXiRoulette")
	x.LiuSuiLottery = field.NewFloat64(table, "LiuSuiLottery")
	x.LiuSuiLowLottery = field.NewFloat64(table, "LiuSuiLowLottery")
	x.LiuSuiLiuHeLottery = field.NewFloat64(table, "LiuSuiLiuHeLottery")
	x.LiuSuiQiPai = field.NewFloat64(table, "LiuSuiQiPai")
	x.LiuSuiDianZhi = field.NewFloat64(table, "LiuSuiDianZhi")
	x.LiuSuiXiaoYouXi = field.NewFloat64(table, "LiuSuiXiaoYouXi")
	x.LiuSuiCryptoMarket = field.NewFloat64(table, "LiuSuiCryptoMarket")
	x.LiuSuiLive = field.NewFloat64(table, "LiuSuiLive")
	x.LiuSuiSport = field.NewFloat64(table, "LiuSuiSport")
	x.LiuSuiTexas = field.NewFloat64(table, "LiuSuiTexas")
	x.State = field.NewInt32(table, "State")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.RewardAmountHaXi = field.NewFloat64(table, "RewardAmountHaXi")
	x.RewardAmountHaXiRoulette = field.NewFloat64(table, "RewardAmountHaXiRoulette")
	x.RewardAmountLottery = field.NewFloat64(table, "RewardAmountLottery")
	x.RewardAmountLowLottery = field.NewFloat64(table, "RewardAmountLowLottery")
	x.RewardAmountLiuHeLottery = field.NewFloat64(table, "RewardAmountLiuHeLottery")
	x.RewardAmountQiPai = field.NewFloat64(table, "RewardAmountQiPai")
	x.RewardAmountDianZhi = field.NewFloat64(table, "RewardAmountDianZhi")
	x.RewardAmountXiaoYouXi = field.NewFloat64(table, "RewardAmountXiaoYouXi")
	x.RewardAmountCryptoMarket = field.NewFloat64(table, "RewardAmountCryptoMarket")
	x.RewardAmountLive = field.NewFloat64(table, "RewardAmountLive")
	x.RewardAmountSport = field.NewFloat64(table, "RewardAmountSport")
	x.RewardAmountTexas = field.NewFloat64(table, "RewardAmountTexas")

	x.fillFieldMap()

	return x
}

func (x *xVipDailly) WithContext(ctx context.Context) *xVipDaillyDo {
	return x.xVipDaillyDo.WithContext(ctx)
}

func (x xVipDailly) TableName() string { return x.xVipDaillyDo.TableName() }

func (x xVipDailly) Alias() string { return x.xVipDaillyDo.Alias() }

func (x xVipDailly) Columns(cols ...field.Expr) gen.Columns { return x.xVipDaillyDo.Columns(cols...) }

func (x *xVipDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xVipDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 32)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["LiuSuiHaXi"] = x.LiuSuiHaXi
	x.fieldMap["LiuSuiHaXiRoulette"] = x.LiuSuiHaXiRoulette
	x.fieldMap["LiuSuiLottery"] = x.LiuSuiLottery
	x.fieldMap["LiuSuiLowLottery"] = x.LiuSuiLowLottery
	x.fieldMap["LiuSuiLiuHeLottery"] = x.LiuSuiLiuHeLottery
	x.fieldMap["LiuSuiQiPai"] = x.LiuSuiQiPai
	x.fieldMap["LiuSuiDianZhi"] = x.LiuSuiDianZhi
	x.fieldMap["LiuSuiXiaoYouXi"] = x.LiuSuiXiaoYouXi
	x.fieldMap["LiuSuiCryptoMarket"] = x.LiuSuiCryptoMarket
	x.fieldMap["LiuSuiLive"] = x.LiuSuiLive
	x.fieldMap["LiuSuiSport"] = x.LiuSuiSport
	x.fieldMap["LiuSuiTexas"] = x.LiuSuiTexas
	x.fieldMap["State"] = x.State
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["RewardAmountHaXi"] = x.RewardAmountHaXi
	x.fieldMap["RewardAmountHaXiRoulette"] = x.RewardAmountHaXiRoulette
	x.fieldMap["RewardAmountLottery"] = x.RewardAmountLottery
	x.fieldMap["RewardAmountLowLottery"] = x.RewardAmountLowLottery
	x.fieldMap["RewardAmountLiuHeLottery"] = x.RewardAmountLiuHeLottery
	x.fieldMap["RewardAmountQiPai"] = x.RewardAmountQiPai
	x.fieldMap["RewardAmountDianZhi"] = x.RewardAmountDianZhi
	x.fieldMap["RewardAmountXiaoYouXi"] = x.RewardAmountXiaoYouXi
	x.fieldMap["RewardAmountCryptoMarket"] = x.RewardAmountCryptoMarket
	x.fieldMap["RewardAmountLive"] = x.RewardAmountLive
	x.fieldMap["RewardAmountSport"] = x.RewardAmountSport
	x.fieldMap["RewardAmountTexas"] = x.RewardAmountTexas
}

func (x xVipDailly) clone(db *gorm.DB) xVipDailly {
	x.xVipDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xVipDailly) replaceDB(db *gorm.DB) xVipDailly {
	x.xVipDaillyDo.ReplaceDB(db)
	return x
}

type xVipDaillyDo struct{ gen.DO }

func (x xVipDaillyDo) Debug() *xVipDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xVipDaillyDo) WithContext(ctx context.Context) *xVipDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xVipDaillyDo) ReadDB() *xVipDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xVipDaillyDo) WriteDB() *xVipDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xVipDaillyDo) Session(config *gorm.Session) *xVipDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xVipDaillyDo) Clauses(conds ...clause.Expression) *xVipDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xVipDaillyDo) Returning(value interface{}, columns ...string) *xVipDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xVipDaillyDo) Not(conds ...gen.Condition) *xVipDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xVipDaillyDo) Or(conds ...gen.Condition) *xVipDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xVipDaillyDo) Select(conds ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xVipDaillyDo) Where(conds ...gen.Condition) *xVipDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xVipDaillyDo) Order(conds ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xVipDaillyDo) Distinct(cols ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xVipDaillyDo) Omit(cols ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xVipDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xVipDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xVipDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xVipDaillyDo) Group(cols ...field.Expr) *xVipDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xVipDaillyDo) Having(conds ...gen.Condition) *xVipDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xVipDaillyDo) Limit(limit int) *xVipDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xVipDaillyDo) Offset(offset int) *xVipDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xVipDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xVipDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xVipDaillyDo) Unscoped() *xVipDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xVipDaillyDo) Create(values ...*model.XVipDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xVipDaillyDo) CreateInBatches(values []*model.XVipDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xVipDaillyDo) Save(values ...*model.XVipDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xVipDaillyDo) First() (*model.XVipDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDailly), nil
	}
}

func (x xVipDaillyDo) Take() (*model.XVipDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDailly), nil
	}
}

func (x xVipDaillyDo) Last() (*model.XVipDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDailly), nil
	}
}

func (x xVipDaillyDo) Find() ([]*model.XVipDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XVipDailly), err
}

func (x xVipDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XVipDailly, err error) {
	buf := make([]*model.XVipDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xVipDaillyDo) FindInBatches(result *[]*model.XVipDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xVipDaillyDo) Attrs(attrs ...field.AssignExpr) *xVipDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xVipDaillyDo) Assign(attrs ...field.AssignExpr) *xVipDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xVipDaillyDo) Joins(fields ...field.RelationField) *xVipDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xVipDaillyDo) Preload(fields ...field.RelationField) *xVipDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xVipDaillyDo) FirstOrInit() (*model.XVipDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDailly), nil
	}
}

func (x xVipDaillyDo) FirstOrCreate() (*model.XVipDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XVipDailly), nil
	}
}

func (x xVipDaillyDo) FindByPage(offset int, limit int) (result []*model.XVipDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xVipDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xVipDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xVipDaillyDo) Delete(models ...*model.XVipDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xVipDaillyDo) withDO(do gen.Dao) *xVipDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
