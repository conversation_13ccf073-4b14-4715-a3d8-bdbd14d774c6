// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXLiuhecaiMap = "x_liuhecai_map"

// XLiuhecaiMap mapped from table <x_liuhecai_map>
type XLiuhecaiMap struct {
	ID    int32  `gorm:"column:Id;primaryKey;comment:六合彩大类ID" json:"Id"`   // 六合彩大类ID
	PName string `gorm:"column:PName;comment:大类名称" json:"PName"`           // 大类名称
	Sid   int32  `gorm:"column:Sid;primaryKey;comment:六合彩小类ID" json:"Sid"` // 六合彩小类ID
	SName string `gorm:"column:SName;comment:小类名称" json:"SName"`           // 小类名称
}

// TableName XLiuhecaiMap's table name
func (*XLiuhecaiMap) TableName() string {
	return TableNameXLiuhecaiMap
}
