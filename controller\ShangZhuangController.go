package controller

import (
	"context"
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"
)

type ShuangZhuangController struct {
}

func (c *ShuangZhuangController) Init() {
	gropu := server.Http().NewGroup("/api/shuangzhuang")
	{
		err := server.XRedis().LoadScript(utils.XiaZhuangKey, script)
		if err != nil {
			logs.Error("XRedis LoadScript err", err)
		}
		gropu.Post("/config", c.config)
		gropu.Post("/confirm_shuangzhuang", c.confirm_shuangzhuang)
		gropu.PostByNoAuthMayUserToken("/get_shangzhuang_user", c.get_shangzhuang_user)
		gropu.PostByNoAuthMayUserToken("/get_shangzhuang_list", c.get_shangzhuang_list)
		gropu.Post("/in_progress_shuangzhuang", c.in_progress_shuangzhuang)
		gropu.Post("/cancel_shuangzhuang", c.cancel_shuangzhuang)
	}
}

func (c *ShuangZhuangController) config(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	ChannelId, SellerId := token.ChannelId, token.SellerId
	config, err := get_config(ChannelId, SellerId)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK(config)
}

func (c *ShuangZhuangController) confirm_shuangzhuang(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId   int   `validate:"required"` //运营商
		BankerTime int32 `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	bankerConfigTb := server.DaoxHashGame().XTbBankerConfig
	bankerConfigDb := server.DaoxHashGame().XTbBankerConfig.WithContext(context.Background())
	config, err := bankerConfigDb.Select(bankerConfigTb.BankerMinTime, bankerConfigTb.BankerMaxTime,
		bankerConfigTb.BankerMinAmount, bankerConfigTb.Status, bankerConfigTb.BankerTgGroup).
		Where(bankerConfigTb.ChannelID.Eq(int32(token.ChannelId))).
		Where(bankerConfigTb.SellerID.Eq(int32(token.SellerId))).
		Where(bankerConfigTb.Status.Eq(utils.ShuangZhuangStateOpen)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "上庄尚未开启")
			return
		}
		logs.Error("confirm_shuangzhuang bankerConfig err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.BankerAmount, userTb.AgentID, userTb.TopAgentID).
		Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("confirm_shuangzhuang user err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if config.BankerMinAmount > user.BankerAmount {
		ctx.RespErrString(true, &errcode, "尚未达到上庄金额")
		return
	}
	reqdata.BankerTime = reqdata.BankerTime * 60
	if config.BankerMinTime > reqdata.BankerTime || reqdata.BankerTime > config.BankerMaxTime {
		ctx.RespErrString(true, &errcode, "配置(分钟)参数错误")
		return
	}
	bankerUserTb := server.DaoxHashGame().XTbBankerUser
	bankerUserFindDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	conditionStatus := []int32{utils.ShuangZhuangQueue, utils.ShuangZhuangCurrently}
	first, err := bankerUserFindDb.Where(bankerUserTb.UserID.Eq(user.UserID)).
		Where(bankerUserTb.BankerStatus.In(conditionStatus...)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("confirm_shuangzhuang bankerUserFindDb err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if first != nil {
		ctx.RespErrString(true, &errcode, "进行中请勿重复提交")
		return
	}
	data := model.XTbBankerUser{
		UserID:          user.UserID,
		ChannelID:       int32(token.ChannelId),
		SellerID:        int32(token.SellerId),
		TopAgentID:      user.TopAgentID,
		AgentID:         user.AgentID,
		BankerMinTime:   config.BankerMinTime,
		BankerMaxTime:   config.BankerMaxTime,
		BankerMinAmount: config.BankerMinAmount,
		BankerTgGroup:   config.BankerTgGroup,
		BankerTime:      reqdata.BankerTime,
		BankerAmount:    user.BankerAmount,
		BankerStatus:    utils.ShuangZhuangQueue,
	}
	bankerUserDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	err = bankerUserDb.Omit(bankerUserTb.BankerStartTime, bankerUserTb.BankerEndTime).Create(&data)
	if err != nil {
		logs.Error("confirm_shuangzhuang create bankerUser err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	rkey := fmt.Sprintf(utils.QueueListKey, token.ChannelId)
	server.XRedis().RPush(rkey, data)
	ctx.RespOK()
}

func (c *ShuangZhuangController) cancel_shuangzhuang(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	token := server.GetToken(ctx)
	bankerUserTb := server.DaoxHashGame().XTbBankerUser
	bankerUserFindDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	conditionStatus := []int32{utils.ShuangZhuangQueue, utils.ShuangZhuangCurrently}
	first, err := bankerUserFindDb.Where(bankerUserTb.UserID.Eq(int32(token.UserId))).
		Where(bankerUserTb.BankerStatus.In(conditionStatus...)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "请先上庄")
			return
		}
		logs.Error("cancel_shuangzhuang bankerUserFindDb err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if first.BankerStatus == utils.ShuangZhuangCurrently {
		date := carbon.Parse(carbon.Now().String()).StdTime().Unix()
		bankerEndTime := carbon.Parse(carbon.CreateFromStdTime(first.BankerEndTime).String()).StdTime().Unix()
		if date-bankerEndTime < utils.XiaZhuangMinTime {
			ctx.RespErrString(true, &errcode, "即将结束无法下庄")
			return
		}
	}
	bankerUserDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	data := make(map[string]any)
	data["BankerStatus"] = utils.ShuangZhuangCancel
	data["BankerEndTime"] = carbon.Parse(carbon.Now().String()).StdTime()
	_, err = bankerUserDb.Where(bankerUserTb.ID.Eq(first.ID)).Updates(data)
	if err != nil {
		logs.Error("cancel_shuangzhuang Updates bankerUser err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	queueKey := fmt.Sprintf(utils.QueueListKey, 2)
	queueKey = fmt.Sprintf("%v:%v", server.Project(), queueKey)
	dataKey := fmt.Sprintf(utils.DataKey, 2)
	dataKey = fmt.Sprintf("%v:%v", server.Project(), dataKey)
	eval, err := server.XRedis().ScriptEval(utils.XiaZhuangKey, token.UserId, dataKey, queueKey)
	logs.Error("cancel_shuangzhuang eval", eval, err, token.UserId)
	ctx.RespOK()
}

var script = `
	local userId = KEYS[1]
	local hashKey = KEYS[2]
	local listKey = KEYS[3]

	-- 判断 userid 是否在 hash 中
	if redis.call('HGET', hashKey, 'UserID') == userId then
	  -- 删除整个 hash
	  redis.call('DEL', hashKey)
	else
	  -- 从 list 中查找 userid 并删除
	  local index = 0
	  local len = redis.call('LLEN', listKey)
	
	  while index < len do
		local jsonStr = redis.call('LINDEX', listKey, index)
		-- 解析 JSON
		local json = cjson.decode(jsonStr)

		local jsonUserId = tonumber(json.UserId)
	
		-- 判断 userId 是否存在
		if jsonUserId == tonumber(userId) then
		  -- 删除该 JSON 数据
		  redis.call('LREM', listKey, 1, jsonStr)
		  break
		end
	
		index = index + 1
	  end
	end
	
	return 1
`

func get_config(channelId, sellerId int) (model.XTbBankerConfig, error) {
	bankerConfigTb := server.DaoxHashGame().XTbBankerConfig
	bankerConfigDb := server.DaoxHashGame().XTbBankerConfig.WithContext(context.Background())
	var res model.XTbBankerConfig
	config, err := bankerConfigDb.Select(bankerConfigTb.BankerMinTime, bankerConfigTb.BankerMaxTime,
		bankerConfigTb.BankerMinAmount, bankerConfigTb.Status, bankerConfigTb.BankerTgGroup).
		Where(bankerConfigTb.ChannelID.Eq(int32(channelId))).
		Where(bankerConfigTb.SellerID.Eq(int32(sellerId))).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("get_config bankerConfig err", err)
		return res, err
	}
	if config != nil {
		res = *config
	}
	return res, err
}

func (c *ShuangZhuangController) get_shangzhuang_user(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	token := server.GetToken(ctx)
	if token != nil {
		ChannelId, SellerId = token.ChannelId, token.SellerId
	}
	config, err := get_config(ChannelId, SellerId)
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("Config", config)
	ctx.Put("UserId", "")
	// 获取上庄用户
	if config.Status == utils.ShuangZhuangStateOpen {
		rkey := fmt.Sprintf(utils.DataKey, ChannelId)
		getAllMap, err := server.Redis().HGetAll(rkey)
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		UserId, ok := getAllMap["UserID"]
		if ok {
			ctx.Put("UserId", UserId)
		}
	}
	ctx.RespOK()
}

func (c *ShuangZhuangController) get_shangzhuang_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Host     string
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	token := server.GetToken(ctx)
	if token != nil {
		ChannelId, SellerId = token.ChannelId, token.SellerId
	}
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 10
	}
	var limit, offset int
	limit = reqdata.PageSize
	offset = (reqdata.Page - 1) * reqdata.PageSize
	bankerUserTb := server.DaoxHashGame().XTbBankerUser
	bankerUserDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	conditionStatus := []int32{utils.ShuangZhuangQueue, utils.ShuangZhuangCurrently}
	list, count, err := bankerUserDb.Where(bankerUserTb.ChannelID.Eq(int32(ChannelId))).
		Where(bankerUserTb.SellerID.Eq(int32(SellerId))).
		Where(bankerUserTb.BankerStatus.In(conditionStatus...)).
		Order(bankerUserTb.ID.Desc()).FindByPage(offset, limit)
	if err != nil {
		logs.Error("get_shangzhuang_list bankerUserDb list err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("data", list)
	ctx.Put("total", count)
	ctx.RespOK()
}

func (c *ShuangZhuangController) in_progress_shuangzhuang(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	bankerUserTb := server.DaoxHashGame().XTbBankerUser
	bankerUserFindDb := server.DaoxHashGame().XTbBankerUser.WithContext(context.Background())
	conditionStatus := []int32{utils.ShuangZhuangQueue, utils.ShuangZhuangCurrently}
	first, err := bankerUserFindDb.Where(bankerUserTb.UserID.Eq(int32(token.UserId))).
		Where(bankerUserTb.BankerStatus.In(conditionStatus...)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "请先上庄")
			return
		}
		logs.Error("in_progress_shuangzhuang bankerUserFindDb err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.RespOK(first)
}
