// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveLogUserSign(db *gorm.DB, opts ...gen.DOOption) xActiveLogUserSign {
	_xActiveLogUserSign := xActiveLogUserSign{}

	_xActiveLogUserSign.xActiveLogUserSignDo.UseDB(db, opts...)
	_xActiveLogUserSign.xActiveLogUserSignDo.UseModel(&model.XActiveLogUserSign{})

	tableName := _xActiveLogUserSign.xActiveLogUserSignDo.TableName()
	_xActiveLogUserSign.ALL = field.NewAsterisk(tableName)
	_xActiveLogUserSign.RecordTime = field.NewTime(tableName, "RecordTime")
	_xActiveLogUserSign.RecordID = field.NewInt64(tableName, "RecordId")
	_xActiveLogUserSign.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveLogUserSign.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xActiveLogUserSign.UserID = field.NewInt32(tableName, "UserId")
	_xActiveLogUserSign.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveLogUserSign.LiuShui = field.NewFloat64(tableName, "LiuShui")
	_xActiveLogUserSign.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xActiveLogUserSign.FirstSignTime = field.NewTime(tableName, "FirstSignTime")
	_xActiveLogUserSign.LastSignTime = field.NewTime(tableName, "LastSignTime")
	_xActiveLogUserSign.Config = field.NewString(tableName, "Config")
	_xActiveLogUserSign.BaseConfig = field.NewString(tableName, "BaseConfig")
	_xActiveLogUserSign.Memo = field.NewString(tableName, "Memo")

	_xActiveLogUserSign.fillFieldMap()

	return _xActiveLogUserSign
}

// xActiveLogUserSign 用户活动签到记录
type xActiveLogUserSign struct {
	xActiveLogUserSignDo xActiveLogUserSignDo

	ALL           field.Asterisk
	RecordTime    field.Time    // 记录时间
	RecordID      field.Int64   // 记录Id
	SellerID      field.Int32   // 运营商Id
	ChannelID     field.Int32   // 渠道Id
	UserID        field.Int32   // 用户id
	ActiveID      field.Int32   // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	LiuShui       field.Float64 // 游戏流水
	RewardAmount  field.Float64 // 签到奖励金额
	FirstSignTime field.Time    // 首次签到时间
	LastSignTime  field.Time    // 最后签到时间
	Config        field.String  // 活动配置
	BaseConfig    field.String  // 基础配置
	Memo          field.String  // 备注

	fieldMap map[string]field.Expr
}

func (x xActiveLogUserSign) Table(newTableName string) *xActiveLogUserSign {
	x.xActiveLogUserSignDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveLogUserSign) As(alias string) *xActiveLogUserSign {
	x.xActiveLogUserSignDo.DO = *(x.xActiveLogUserSignDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveLogUserSign) updateTableName(table string) *xActiveLogUserSign {
	x.ALL = field.NewAsterisk(table)
	x.RecordTime = field.NewTime(table, "RecordTime")
	x.RecordID = field.NewInt64(table, "RecordId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.LiuShui = field.NewFloat64(table, "LiuShui")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.FirstSignTime = field.NewTime(table, "FirstSignTime")
	x.LastSignTime = field.NewTime(table, "LastSignTime")
	x.Config = field.NewString(table, "Config")
	x.BaseConfig = field.NewString(table, "BaseConfig")
	x.Memo = field.NewString(table, "Memo")

	x.fillFieldMap()

	return x
}

func (x *xActiveLogUserSign) WithContext(ctx context.Context) *xActiveLogUserSignDo {
	return x.xActiveLogUserSignDo.WithContext(ctx)
}

func (x xActiveLogUserSign) TableName() string { return x.xActiveLogUserSignDo.TableName() }

func (x xActiveLogUserSign) Alias() string { return x.xActiveLogUserSignDo.Alias() }

func (x xActiveLogUserSign) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveLogUserSignDo.Columns(cols...)
}

func (x *xActiveLogUserSign) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveLogUserSign) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["RecordTime"] = x.RecordTime
	x.fieldMap["RecordId"] = x.RecordID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["LiuShui"] = x.LiuShui
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["FirstSignTime"] = x.FirstSignTime
	x.fieldMap["LastSignTime"] = x.LastSignTime
	x.fieldMap["Config"] = x.Config
	x.fieldMap["BaseConfig"] = x.BaseConfig
	x.fieldMap["Memo"] = x.Memo
}

func (x xActiveLogUserSign) clone(db *gorm.DB) xActiveLogUserSign {
	x.xActiveLogUserSignDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveLogUserSign) replaceDB(db *gorm.DB) xActiveLogUserSign {
	x.xActiveLogUserSignDo.ReplaceDB(db)
	return x
}

type xActiveLogUserSignDo struct{ gen.DO }

func (x xActiveLogUserSignDo) Debug() *xActiveLogUserSignDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveLogUserSignDo) WithContext(ctx context.Context) *xActiveLogUserSignDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveLogUserSignDo) ReadDB() *xActiveLogUserSignDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveLogUserSignDo) WriteDB() *xActiveLogUserSignDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveLogUserSignDo) Session(config *gorm.Session) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveLogUserSignDo) Clauses(conds ...clause.Expression) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveLogUserSignDo) Returning(value interface{}, columns ...string) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveLogUserSignDo) Not(conds ...gen.Condition) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveLogUserSignDo) Or(conds ...gen.Condition) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveLogUserSignDo) Select(conds ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveLogUserSignDo) Where(conds ...gen.Condition) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveLogUserSignDo) Order(conds ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveLogUserSignDo) Distinct(cols ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveLogUserSignDo) Omit(cols ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveLogUserSignDo) Join(table schema.Tabler, on ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveLogUserSignDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveLogUserSignDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveLogUserSignDo) Group(cols ...field.Expr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveLogUserSignDo) Having(conds ...gen.Condition) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveLogUserSignDo) Limit(limit int) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveLogUserSignDo) Offset(offset int) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveLogUserSignDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveLogUserSignDo) Unscoped() *xActiveLogUserSignDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveLogUserSignDo) Create(values ...*model.XActiveLogUserSign) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveLogUserSignDo) CreateInBatches(values []*model.XActiveLogUserSign, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveLogUserSignDo) Save(values ...*model.XActiveLogUserSign) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveLogUserSignDo) First() (*model.XActiveLogUserSign, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveLogUserSign), nil
	}
}

func (x xActiveLogUserSignDo) Take() (*model.XActiveLogUserSign, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveLogUserSign), nil
	}
}

func (x xActiveLogUserSignDo) Last() (*model.XActiveLogUserSign, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveLogUserSign), nil
	}
}

func (x xActiveLogUserSignDo) Find() ([]*model.XActiveLogUserSign, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveLogUserSign), err
}

func (x xActiveLogUserSignDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveLogUserSign, err error) {
	buf := make([]*model.XActiveLogUserSign, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveLogUserSignDo) FindInBatches(result *[]*model.XActiveLogUserSign, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveLogUserSignDo) Attrs(attrs ...field.AssignExpr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveLogUserSignDo) Assign(attrs ...field.AssignExpr) *xActiveLogUserSignDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveLogUserSignDo) Joins(fields ...field.RelationField) *xActiveLogUserSignDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveLogUserSignDo) Preload(fields ...field.RelationField) *xActiveLogUserSignDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveLogUserSignDo) FirstOrInit() (*model.XActiveLogUserSign, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveLogUserSign), nil
	}
}

func (x xActiveLogUserSignDo) FirstOrCreate() (*model.XActiveLogUserSign, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveLogUserSign), nil
	}
}

func (x xActiveLogUserSignDo) FindByPage(offset int, limit int) (result []*model.XActiveLogUserSign, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveLogUserSignDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveLogUserSignDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveLogUserSignDo) Delete(models ...*model.XActiveLogUserSign) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveLogUserSignDo) withDO(do gen.Dao) *xActiveLogUserSignDo {
	x.DO = *do.(*gen.DO)
	return x
}
