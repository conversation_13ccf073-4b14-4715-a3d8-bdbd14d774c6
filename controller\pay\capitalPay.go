package paycontroller

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

func (c *PayController) InitCapitalPay() {
	server.Http().PostNoAuth("/api/capitalpay/countries", c.getCapitalPayCountries)
	server.Http().Post("/api/capitalpay/create_order", c.createCapitalPayOrder)
	server.Http().PostNoAuth("/api/capitalpay/callback", c.callbackCapital)

}

type CapitalRechargeRequest struct {
	Network            string `json:"network" validate:"required"`
	CryptoCurrencyCode string `json:"cryptoCurrencyCode" validate:"required"`
	FiatCurrency       string `json:"fiatCurrency" validate:"required"`
	FiatAmount         string `json:"fiatAmount" validate:"required"`
}

func (c *PayController) getCapitalPayCountries(ctx *abugo.AbuHttpContent) {
	type Country struct {
		Country   string  `json:"country"`
		Symbol    string  `json:"symbol"`
		MinAmount float64 `json:"min_amount"`
		MaxAmount float64 `json:"max_amount"`
	}
	var countries []Country

	host := ctx.Host()
	token := server.GetToken(ctx)
	_, sellerId := server.GetChannel(ctx, host)
	if token != nil {
		sellerId = token.SellerId
	}

	// 获取支付通道
	payMethod, _ := server.XDb().Table("x_finance_method").Where("Brand = ?", "capitalpay").Where("SellerId = ?", sellerId, "").First()
	if payMethod == nil {
		ctx.RespOK(countries)
		return
	}

	countriesJsonData := `[{"country":"AS","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"AD","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"AR","symbol":"ARS","min_amount":0,"max_amount":0},{"country":"AU","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"AT","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"BE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"BT","symbol":"INR","min_amount":334.02,"max_amount":167013.9},{"country":"BQ","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"BR","symbol":"BRL","min_amount":200,"max_amount":65000},{"country":"CA","symbol":"CAD","min_amount":5.47,"max_amount":22151.85},{"country":"CL","symbol":"CLP","min_amount":0,"max_amount":0},{"country":"CX","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"CC","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"CO","symbol":"COP","min_amount":41939.99,"max_amount":63616077.87},{"country":"CK","symbol":"NZD","min_amount":6.64,"max_amount":26914.67},{"country":"CY","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"CZ","symbol":"CZK","min_amount":624.66,"max_amount":374563.39},{"country":"DK","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"DO","symbol":"DOP","min_amount":1586.8,"max_amount":951486.67},{"country":"EE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"FO","symbol":"DKK","min_amount":27.66,"max_amount":112128.46},{"country":"FI","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"FR","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"GF","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"DE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"GR","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"GL","symbol":"DKK","min_amount":27.66,"max_amount":112128.46},{"country":"GP","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"GU","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"HM","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"HK","symbol":"HKD","min_amount":31.24,"max_amount":126519.58},{"country":"HU","symbol":"HUF","min_amount":9794.87,"max_amount":5873291.86},{"country":"IS","symbol":"ISK","min_amount":3806,"max_amount":2281652},{"country":"IN","symbol":"INR","min_amount":334.02,"max_amount":167013.9},{"country":"ID","symbol":"IDR","min_amount":64501,"max_amount":258113310},{"country":"IE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"IL","symbol":"ILS","min_amount":14.88,"max_amount":60952.75},{"country":"IT","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"JP","symbol":"JPY","min_amount":625,"max_amount":2521416.87},{"country":"KZ","symbol":"KZT","min_amount":1765.45,"max_amount":7199682.74},{"country":"KE","symbol":"KES","min_amount":1404.94,"max_amount":2093353.59},{"country":"KI","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"XK","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"KW","symbol":"KWD","min_amount":10,"max_amount":3500},{"country":"LV","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"LS","symbol":"ZAR","min_amount":197.6,"max_amount":301175.59},{"country":"LI","symbol":"CHF","min_amount":3.63,"max_amount":14700},{"country":"LU","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"MY","symbol":"MYR","min_amount":18.91,"max_amount":75939.31},{"country":"MT","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"MH","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"MQ","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"YT","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"MX","symbol":"MXN","min_amount":67.22,"max_amount":274089.09},{"country":"FM","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"MD","symbol":"MDL","min_amount":70.68,"max_amount":35340.57},{"country":"MC","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"ME","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"NR","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"NL","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"NZ","symbol":"NZD","min_amount":6.64,"max_amount":26914.67},{"country":"NU","symbol":"NZD","min_amount":6.64,"max_amount":26914.67},{"country":"NF","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"MK","symbol":"MKD","min_amount":0,"max_amount":0},{"country":"MP","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"NO","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"OM","symbol":"OMR","min_amount":15,"max_amount":4300},{"country":"PW","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"PS","symbol":"JOD","min_amount":19.34,"max_amount":11592.62},{"country":"PE","symbol":"PEN","min_amount":39.97,"max_amount":60674.95},{"country":"PH","symbol":"PHP","min_amount":619.69,"max_amount":943203.27},{"country":"PN","symbol":"NZD","min_amount":6.64,"max_amount":26914.67},{"country":"PL","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"PT","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"PR","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"RO","symbol":"RON","min_amount":18.45,"max_amount":74817.82},{"country":"RE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"BL","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"MF","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"PM","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"WS","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"SM","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"SG","symbol":"SGD","min_amount":5.41,"max_amount":21929.73},{"country":"SK","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"SI","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"KR","symbol":"KRW","min_amount":5476,"max_amount":22374754},{"country":"ES","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"SJ","symbol":"NOK","min_amount":43.28,"max_amount":175687.09},{"country":"SE","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"CH","symbol":"CHF","min_amount":3.63,"max_amount":14700},{"country":"TW","symbol":"TWD","min_amount":129.71,"max_amount":455750},{"country":"TL","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"TK","symbol":"NZD","min_amount":6.64,"max_amount":26914.67},{"country":"TC","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"TV","symbol":"AUD","min_amount":6.05,"max_amount":24539.14},{"country":"VI","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"AE","symbol":"AED","min_amount":100.17,"max_amount":60060.01},{"country":"GB","symbol":"GBP","min_amount":3.18,"max_amount":30000},{"country":"US","symbol":"USD","min_amount":4,"max_amount":100000},{"country":"VA","symbol":"EUR","min_amount":3.7,"max_amount":92695.59},{"country":"EH","symbol":"MAD","min_amount":0,"max_amount":0},{"country":"AX","symbol":"EUR","min_amount":3.7,"max_amount":92695.59}]`

	json.Unmarshal([]byte(countriesJsonData), &countries)

	ctx.RespOK(countries)
}

func (c *PayController) createCapitalPayOrder(ctx *abugo.AbuHttpContent) {
	errcode := 0
	rechargeReq := CapitalRechargeRequest{}
	err := ctx.RequestData(&rechargeReq)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	daoUser := server.DaoxHashGame().XUser
	db := daoUser.WithContext(ctx.Gin())
	user, err := db.Where(daoUser.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}

	// 获取支付通道
	payMethod, _ := server.XDb().Table("x_finance_method").Where("Brand = ?", "capitalpay").Where("SellerId = ?", token.SellerId, "").First()
	if payMethod == nil {
		ctx.RespErr(errors.New("pay method not found"), &errcode)
		return
	}

	config := map[string]interface{}{}
	json.Unmarshal([]byte(payMethod.String("ExtraConfig")), &config)

	//网络定义
	networkFormat := map[string]string{
		"Tron":     "TRC20Buy",
		"Ethereum": "ERC20Buy",
	}

	//server.Db().Gorm().Begin()
	//生成待支付订单
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		orderId, _ := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":  token.SellerId,
			"ChannelId": token.ChannelId,
			"UserId":    token.UserId,
			"Symbol":    strings.ToLower(rechargeReq.FiatCurrency),
			"PayId":     payMethod.Int("Id"),
			"PayType":   100,
			"Amount":    rechargeReq.FiatAmount,
			"Net":       strings.ToLower(rechargeReq.Network),
			//"RealAmount":   "",
			//"TransferRate": "",
			"State":        3,
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": user.SpecialAgent,
			"TopAgentId":   user.TopAgentID,
			//"TxId":         "",
		})

		// 参数
		extra := map[string]interface{}{
			"fiat_currency": rechargeReq.FiatCurrency,
		}
		params := map[string]interface{}{
			"merchant_ref": orderId,
			"product":      networkFormat[rechargeReq.Network],
			"amount":       rechargeReq.FiatAmount,
			"extra":        extra,
		}
		paramStr, _ := json.Marshal(params)
		timestamp := time.Now().Unix()

		str := config["id"].(string) + string(paramStr) + "MD5" + strconv.FormatInt(timestamp, 10) + config["key"].(string)
		//logs.Info("str", str)
		hash := md5.Sum([]byte(str))
		sign := hex.EncodeToString(hash[:])
		//logs.Info("sign", sign)

		postData := map[string]interface{}{
			"merchant_no": config["id"],
			"params":      string(paramStr),
			"sign_type":   "MD5",
			"timestamp":   time.Now().Unix(),
			"sign":        sign,
		}

		postDataToJson, _ := json.Marshal(postData)

		//logs.Info("postData", string(postDataToJson))

		response, err := http.Post("https://api.star-pay.vip/api/gateway/pay", "application/json", strings.NewReader(string(postDataToJson)))

		payUrl := ""
		if err != nil {
			ctx.RespErr(err, &errcode)
			return err
		}

		defer response.Body.Close()
		body, _ := io.ReadAll(response.Body)
		//logs.Info("body", string(body))
		var resData map[string]interface{}
		json.Unmarshal(body, &resData)
		code, _ := resData["code"].(float64)
		if code == 200 {

			var resParams map[string]interface{}
			json.Unmarshal([]byte(resData["params"].(string)), &resParams)

			tx.Table("x_recharge").Where("Id = ?", orderId).Update(xgo.H{
				"PayData": string(body),
				"ThirdId": resParams["system_ref"],
			})

			payUrl = resParams["payurl"].(string)
		} else {
			ctx.RespErr(errors.New(resData["message"].(string)), &errcode)
			return errors.New(resData["message"].(string))
		}

		ctx.RespOK(gin.H{
			"payUrl": payUrl,
		})

		return nil
	})

}

func (c *PayController) callbackCapital(content *abugo.AbuHttpContent) {

}
