package kwai

const (
	EventName_CompleteRegistration = "EVENT_COMPLETE_REGISTRATION" // 完成注册
	EventName_Purchase             = "EVENT_PURCHASE"              // 完成购买或结账流程
	EventName_AddToCart            = "EVENT_ADD_TO_CART"           // 添加到购物车
	EventName_FirstRecharge        = "EVENT_FIRST_DEPOSIT"         // 首次充值
)

type PostData struct {
	ClickId         string `json:"clickid"`
	EventName       string `json:"event_name"`
	PixelId         string `json:"pixelId"`
	AccessToken     string `json:"access_token"`
	TestFlag        bool   `json:"testFlag"`
	TrackFlag       bool   `json:"trackFlag"`
	IsAttributed    int    `json:"is_attributed"`
	Mmpcode         string `json:"mmpcode"`
	PixelSdkVersion string `json:"pixelSdkVersion"`
	Properties      string `json:"properties"`
	ThirdParty      string `json:"third_party"`
}
