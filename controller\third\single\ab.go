package single

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"
)

// AB单一钱包  类
type ABSingleService struct {
	apiUrl                string          // api基础接口
	operatorId            string          // 接入商编号
	allbetKey             string          // AllBet API Key
	partnerKey            string          // Partner API Key
	suffix                string          // 玩家账号后缀（接入商标识码）
	agent                 string          // （接入商标识码）
	currency              string          // 币种
	brandName             string          // 厂商标识
	RefreshUserAmountFunc func(int) error // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

func NewABSingleService(params map[string]string, fc func(int) error) *ABSingleService {
	return &ABSingleService{
		apiUrl:                params["api_url"],
		operatorId:            params["operator_id"],
		allbetKey:             params["allbet_key"],
		partnerKey:            params["partner_key"],
		suffix:                params["suffix"],
		agent:                 params["agent"],
		currency:              params["currency"],
		brandName:             "ab",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyAB = "cacheKeyAB:"

const (
	AB_Code_Success = 0 // 0 成功
)

// 辅助函数

// 生成用于签名的字符串
func (a *ABSingleService) buildStringForSignature(httpVerb, contentMD5, contentType, date, path string) string {
	return httpVerb + "\n" + contentMD5 + "\n" + contentType + "\n" + date + "\n" + path
}

// 生成签名
func (a *ABSingleService) generateSignature(stringForSignature, key string) string {
	// 尝试解码Base64编码的key
	decodedKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		// 如果解码失败，使用原始key
		decodedKey = []byte(key)
	}

	// 创建HMAC-SHA1哈希对象
	h := hmac.New(sha1.New, decodedKey)

	// 写入待签名的字符串
	h.Write([]byte(stringForSignature))

	// 计算哈希值并进行Base64编码
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// 生成HTTP授权头
func (a *ABSingleService) generateAuthHeader(signature string, isPartnerApi bool) string {
	if isPartnerApi {
		return "AB " + a.operatorId + ":" + signature
	} else {
		return "AB " + a.operatorId + ":" + signature
	}
}

// 计算Content-MD5
func (a *ABSingleService) calculateContentMD5(body []byte) string {
	if len(body) == 0 {
		return ""
	}
	hasher := md5.New()
	hasher.Write(body)
	return base64.StdEncoding.EncodeToString(hasher.Sum(nil))
}

// 生成日期头
func (a *ABSingleService) generateDateHeader() string {
	return time.Now().UTC().Format("Mon, 02 Jan 2006 15:04:05 MST")
}

// 发送请求到AllBet API
func (a *ABSingleService) sendAllBetApiRequest(path string, body []byte) ([]byte, error) {
	url := a.apiUrl + path
	method := "POST"
	contentType := "application/json; charset=UTF-8"
	date := a.generateDateHeader()
	contentMD5 := a.calculateContentMD5(body)

	stringForSignature := a.buildStringForSignature(method, contentMD5, contentType, date, path)
	signature := a.generateSignature(stringForSignature, a.allbetKey)
	authHeader := a.generateAuthHeader(signature, false)

	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-MD5", contentMD5)
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Date", date)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

// 发送请求到Partner API
func (a *ABSingleService) sendPartnerApiRequest(path string, body []byte, method string) ([]byte, error) {
	url := a.apiUrl + path
	contentType := "application/json; charset=UTF-8"
	date := a.generateDateHeader()
	contentMD5 := a.calculateContentMD5(body)

	stringForSignature := a.buildStringForSignature(method, contentMD5, contentType, date, path)
	signature := a.generateSignature(stringForSignature, a.partnerKey)
	authHeader := a.generateAuthHeader(signature, true)

	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Content-MD5", contentMD5)
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("Date", date)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

// 获取用户名
func (a *ABSingleService) getUserNameFromUserId(userId int64) string {
	return fmt.Sprintf("%d_%s", userId, a.suffix)
}

// 从用户名获取用户ID
func (a *ABSingleService) getUserIdFromUserName(userName string) (userId int64, err error) {
	if !strings.HasSuffix(userName, "_"+a.suffix) {
		return 0, errors.New("用户名后缀不匹配")
	}
	userIdStr := strings.TrimSuffix(userName, "_"+a.suffix)
	return strconv.ParseInt(userIdStr, 10, 64)
}

// 验证API签名
func (a *ABSingleService) verifyApiSignature(ctx *abugo.AbuHttpContent, path string) (bool, int, string) {
	// 获取请求头信息
	httpVerb := ctx.Gin().Request.Method
	contentMD5 := ctx.Gin().GetHeader("Content-MD5")
	contentType := ctx.Gin().GetHeader("Content-Type")
	dateHeader := ctx.Gin().GetHeader("Date")
	authHeader := ctx.Gin().GetHeader("Authorization")

	// 检查必要的请求头
	if authHeader == "" || dateHeader == "" {
		logs.Error("ab_single 验证API签名 缺少必要的请求头 authHeader=", authHeader, " dateHeader=", dateHeader)
		return false, 40000, "缺少必要的请求头"
	}

	// 解析Authorization头
	authParts := strings.Split(authHeader, " ")
	if len(authParts) != 2 || authParts[0] != "AB" {
		logs.Error("ab_single 验证API签名 Authorization头格式错误 authHeader=", authHeader)
		return false, 40000, "Authorization头格式错误"
	}

	operatorIDAndSignature := strings.Split(authParts[1], ":")
	if len(operatorIDAndSignature) != 2 {
		logs.Error("ab_single 验证API签名 Authorization头格式错误 authHeader=", authHeader)
		return false, 40000, "Authorization头格式错误"
	}

	operatorID := operatorIDAndSignature[0]
	signature := operatorIDAndSignature[1]

	// 验证操作商ID
	if operatorID != a.operatorId {
		logs.Error("ab_single 验证API签名 操作商ID不匹配 operatorID=", operatorID, " a.operatorId=", a.operatorId)
		return false, 10000, "操作商ID不匹配"
	}

	// 构建用于签名的字符串
	stringForSignature := a.buildStringForSignature(httpVerb, contentMD5, contentType, dateHeader, path)

	// 记录签名相关信息，便于调试
	//logs.Info("ab_single 验证API签名 签名信息 httpVerb=", httpVerb, " contentMD5=", contentMD5,
	//	" contentType=", contentType, " dateHeader=", dateHeader, " path=", path)
	//logs.Info("ab_single 验证API签名 stringForSignature=", stringForSignature)

	// 使用Partner API Key生成签名
	expectedSignature := a.generateSignature(stringForSignature, a.partnerKey)

	// 验证签名
	if signature != expectedSignature {
		logs.Error("ab_single 验证API签名 签名不匹配 signature=", signature, " expectedSignature=", expectedSignature)
		return false, 10001, "签名不匹配"
	}

	return true, 0, ""
}

// GetBalance 余额查询接口
// 对应API文档中的 1. 查询余额
func (a *ABSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		ResultCode int     `json:"resultCode"`        // 响应代码
		Message    string  `json:"message"`           // 错误信息
		Balance    float64 `json:"balance,omitempty"` // 玩家余额
		Version    int64   `json:"version,omitempty"` // 响应的版本号
	}
	respdata := ResponseData{
		ResultCode: AB_Code_Success,
	}

	// 获取请求参数
	playerName := ctx.Gin().Param("uid")
	if playerName == "" {
		logs.Error("ab_single 获取玩家余额 玩家账号为空")
		respdata.ResultCode = 40000
		respdata.Message = "玩家账号不存在"
		ctx.RespJson(respdata)
		return
	}

	// 验证API签名
	path := "/GetBalance/" + playerName
	isValid, resultCode, message := a.verifyApiSignature(ctx, path)
	if !isValid {
		respdata.ResultCode = resultCode
		respdata.Message = message
		ctx.RespJson(respdata)
		return
	}

	//userIdStr := strings.TrimSuffix(userName, a.suffix)
	// 从玩家账号获取用户ID
	//userId, err := strconv.Atoi(playerName)
	// 从玩家账号获取用户ID
	userId, err := a.getUserIdFromUserName(playerName)
	if err != nil {
		logs.Error("ab_single 获取玩家余额 玩家账号错误 playerName=", playerName, " err=", err.Error())
		respdata.ResultCode = 10003
		respdata.Message = "玩家账号不存在"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("ab_single 获取玩家余额 获取用户余额失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.ResultCode = 10003
			respdata.Message = "玩家账号不存在"
		} else {
			respdata.ResultCode = 50000
			respdata.Message = "服务器错误"
		}
		ctx.RespJson(respdata)
		return
	}

	respdata.Balance = userBalance.Amount
	respdata.Version = time.Now().Unix() // 使用当前时间戳作为版本号
	ctx.RespJson(respdata)
	logs.Info("ab_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

// Transfer 投注中接口
// 对应API文档中的 2. 转额
func (a *ABSingleService) Transfer(ctx *abugo.AbuHttpContent) {
	type BetDetail struct {
		BetNum             int64   `json:"betNum"`             // 唯一的注单编号
		GameRoundId        int64   `json:"gameRoundId"`        // 唯一的游戏局编号
		Status             int     `json:"status"`             // 注单状态
		BetAmount          float64 `json:"betAmount"`          // 下注金额
		Deposit            float64 `json:"deposit"`            // 预扣金额
		GameType           int     `json:"gameType"`           // 游戏类型
		BetType            int     `json:"betType"`            // 下注类型
		Commission         int     `json:"commission"`         // 免佣类型
		ExchangeRate       float64 `json:"exchangeRate"`       // 汇率（相对人民币的汇率）
		GameResult         string  `json:"gameResult"`         // 游戏结果
		GameResult2        string  `json:"gameResult2"`        // 游戏结果2
		WinOrLossAmount    float64 `json:"winOrLossAmount"`    // 输赢金额，不包括下注金额
		ValidAmount        float64 `json:"validAmount"`        // 有效投注金额
		BetTime            string  `json:"betTime"`            // 下注时间(GMT+8)
		TableName          string  `json:"tableName"`          // 桌台名称
		BetMethod          int     `json:"betMethod"`          // 下注方式
		AppType            int     `json:"appType"`            // 客户端类型
		GameRoundStartTime string  `json:"gameRoundStartTime"` // 游戏局开始时间(GMT+8)
		GameRoundEndTime   string  `json:"gameRoundEndTime"`   // 游戏局结束时间(GMT+8)
		Ip                 string  `json:"ip"`                 // 玩家的IP地址
	}

	type EventDetail struct {
		EventType       int     `json:"eventType"`       // 活动类型
		EventNum        int64   `json:"eventNum"`        // 活动编号
		EventRecordNum  int64   `json:"eventRecordNum"`  // 活动记录编号
		EventAmount     float64 `json:"eventAmount"`     // 活动结算金额
		EventCreateTime string  `json:"eventCreateTime"` // 活动创建时间(GMT+8)
	}

	type CreditDetail struct {
		VendorId string `json:"vendorId"` // 供应商ID
		BillNo   string `json:"billNo"`   // 供应端生成的单号
	}

	type RequestData struct {
		TranId   int64         `json:"tranId"`   // 唯一的交易编号
		Player   string        `json:"player"`   // 玩家帐号 (包含后缀)
		Amount   float64       `json:"amount"`   // 转额金额
		Currency string        `json:"currency"` // 货币类型
		Reason   string        `json:"reason"`   // 转额的原因
		Type     int           `json:"type"`     // 转额类型: 10=下注, 20=派彩结算, 21=手动派彩结算, 30=额度转入, 31=额度转出, 32=重新派彩, 40=活动结算
		IsRetry  bool          `json:"isRetry"`  // 标识是否为重试请求
		Details  []interface{} `json:"details"`  // 详情数组
	}

	type ResponseData struct {
		ResultCode int     `json:"resultCode"`        // 响应代码
		Message    string  `json:"message"`           // 错误信息
		Balance    float64 `json:"balance,omitempty"` // 玩家余额
		Version    int64   `json:"version,omitempty"` // 响应的版本号
	}
	respdata := ResponseData{
		ResultCode: AB_Code_Success,
		Message:    "Success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("ab_single 转额 读取请求消息体错误 err=", err.Error())
		respdata.ResultCode = 50000
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("ab_single 转额 Request.Body=", string(bodyBytes))

	// 验证API签名
	path := "/Transfer"
	isValid, resultCode, message := a.verifyApiSignature(ctx, path)
	if !isValid {
		respdata.ResultCode = resultCode
		respdata.Message = message
		ctx.RespJson(respdata)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("ab_single 转额 解析请求消息体错误 err=", err.Error())
		respdata.ResultCode = 40000
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.TranId)

	// 根据转额类型确定操作类型
	var reqTypeStr string
	switch reqdata.Type {
	case 10:
		reqTypeStr = "下注"
	case 20:
		reqTypeStr = "派彩结算"
	case 21:
		reqTypeStr = "手动派彩结算"
	case 30:
		reqTypeStr = "额度转入"
	case 31:
		reqTypeStr = "额度转出"
	case 32:
		reqTypeStr = "重新派彩"
	case 40:
		reqTypeStr = "活动结算"
	default:
		reqTypeStr = "未知类型"
	}

	// 判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(a.brandName, thirdId, ctx.Gin().Request.URL.String())
	if err != nil {
		respdata.ResultCode = 50000
		respdata.Message = "系统错误"
		ctx.RespJson(respdata)
		logs.Error("ab_single 转额 检测是否重复请求 发生错误 thirdId=", thirdId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("ab_single 转额 检测到重复请求 thirdId=", thirdId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			respdata.ResultCode = 50000
			respdata.Message = "系统错误"
			ctx.RespJson(respdata)
		}
		return
	}

	// 记录请求响应日志
	defer func() {
		respCode := 0
		if respdata.ResultCode != 0 {
			respCode = 1
		}
		base.AddRequestDB(thirdId, string(bodyBytes), respdata, respCode, a.brandName, ctx.Gin().Request.URL.String())
	}()

	// 从玩家账号获取用户ID
	//userIdStr := strings.TrimSuffix(userName, a.suffix)
	// 从玩家账号获取用户ID
	userId, err := a.getUserIdFromUserName(reqdata.Player)
	if err != nil {
		logs.Error("ab_single 转额 玩家账号错误 thirdId=", thirdId, " reqdata.Player=", reqdata.Player, " err=", err.Error())
		respdata.ResultCode = 10003
		respdata.Message = "玩家账号不存在"
		ctx.RespJson(respdata)
		return
	}

	// 确定表名
	table := "x_third_live"
	tablePre := "x_third_live_pre_order"

	// 获取当前时间
	thirdTime := time.Now().Format("2006-01-02 15:04:05")

	// 解析详情数组，根据转账类型处理不同的详情结构
	var betDetails []BetDetail
	var eventDetails []EventDetail
	var creditDetails []CreditDetail
	var billNo string

	if len(reqdata.Details) > 0 {
		switch reqdata.Type {
		case 10, 20, 21: // 下注、派彩结算、手动派彩结算
			betDetailsBytes, _ := json.Marshal(reqdata.Details)
			err = json.Unmarshal(betDetailsBytes, &betDetails)
			if err != nil {
				logs.Error("ab_single 转额 解析投注记录失败 thirdId=", thirdId, " err=", err.Error())
				respdata.ResultCode = 40000
				respdata.Message = "解析投注记录失败"
				ctx.RespJson(respdata)
				return
			}
			// 使用第一个betNum作为billNo
			if len(betDetails) > 0 {
				billNo = fmt.Sprintf("%d", betDetails[0].BetNum)
			}
		case 40: // 活动结算
			eventDetailsBytes, _ := json.Marshal(reqdata.Details)
			err = json.Unmarshal(eventDetailsBytes, &eventDetails)
			if err != nil {
				logs.Error("ab_single 转额 解析活动记录失败 thirdId=", thirdId, " err=", err.Error())
				respdata.ResultCode = 40000
				respdata.Message = "解析活动记录失败"
				ctx.RespJson(respdata)
				return
			}
			// 使用第一个eventRecordNum作为billNo
			if len(eventDetails) > 0 {
				billNo = fmt.Sprintf("%d", eventDetails[0].EventRecordNum)
			}
		case 30, 31, 32: // 额度转入、额度转出、重新派彩
			creditDetailsBytes, _ := json.Marshal(reqdata.Details)
			err = json.Unmarshal(creditDetailsBytes, &creditDetails)
			if err != nil {
				logs.Error("ab_single 转额 解析额度记录失败 thirdId=", thirdId, " err=", err.Error())
				respdata.ResultCode = 40000
				respdata.Message = "解析额度记录失败"
				ctx.RespJson(respdata)
				return
			}
			// 使用第一个billNo
			if len(creditDetails) > 0 {
				billNo = creditDetails[0].BillNo
			}
		}
	}

	// 如果billNo为空，使用thirdId
	if billNo == "" {
		billNo = thirdId
	}

	// 开始转额事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("ab_single 转额 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ResultCode = 10003
				respdata.Message = "会员不存在"
			} else {
				respdata.ResultCode = 50000
				respdata.Message = "查询用户信息失败"
			}
			return e
		}
		respdata.Balance = userBalance.Amount

		//// 检查余额是否足够（只有扣款操作需要检查）
		if reqdata.Type == 10 {
			if reqdata.Amount < 0 && userBalance.Amount < math.Abs(reqdata.Amount) {
				logs.Error("ab_single 转额 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdata.Amount=", reqdata.Amount)
				e = errors.New("玩家余额不足")
				respdata.ResultCode = 10101
				respdata.Message = "额度不足"
				return e
			}
		}

		// 获取游戏信息
		gameId := ""
		gameName := ""
		roundId := ""

		// 从详情数组中获取游戏信息
		if len(betDetails) > 0 {
			gameId = fmt.Sprintf("%d", betDetails[0].GameType)
			//gameName = betDetails[0].TableName
			roundId = fmt.Sprintf("%d", betDetails[0].BetNum)
		} else if len(eventDetails) > 0 {
			gameId = fmt.Sprintf("%d", eventDetails[0].EventType)
			//gameName = fmt.Sprintf("活动%d", eventDetails[0].EventNum)
			roundId = fmt.Sprintf("%d", eventDetails[0].EventRecordNum)
		} else if len(creditDetails) > 0 {
			gameId = creditDetails[0].VendorId
			//gameName = "额度转账"
			roundId = creditDetails[0].BillNo
		}
		logs.Info(" gameId=", gameId, " gameName=", gameName, " roundId=", roundId)

		// 创建账变记录
		var reason int
		switch reqdata.Type {
		case 10:
			reason = utils.BalanceCReasonABBet
		case 20:
			reason = utils.BalanceCReasonABWin
		case 21:
			reason = utils.BalanceCReasonABRWin
		case 30, 31, 32: //电子棋牌三方未上线忽略
			logs.Error("ab_single 转额类型 错误 thirdId=", thirdId, " Type=", reqdata.Type)
			respdata.ResultCode = 50000
			respdata.Message = "转额类型 错误"
			return e
		//	reason = utils.BalanceCReasonABTransferIn
		//case 31:
		//	reason = utils.BalanceCReasonABTransferOut
		//case 32:
		//	reason = utils.BalanceCReasonABWin
		case 40:
			reason = utils.BalanceCReasonABActivity
		default:
			reason = utils.BalanceCReasonABBet
		}

		// 批量下注处理
		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		// 批量处理注单
		// 对于下注类型，可能有多个注单需要处理
		if reqdata.Type == 10 && len(betDetails) > 0 {

			for _, betDetail := range betDetails {
				// 检查是否是加注（同一游戏局中对同一下注类型再次下注）
				var existingBet thirdGameModel.ThirdOrder
				betBillNo := fmt.Sprintf("%d", betDetail.BetNum)

				e = tx.Table(tablePre).Where("ThirdId = ? AND Brand = ? AND UserId = ? AND DataState = -1",
					betBillNo, a.brandName, userId).First(&existingBet).Error

				// 如果是加注，更新现有注单
				if e == nil {
					// 更新预设表注单
					e = tx.Table(tablePre).Where("Id = ?", existingBet.Id).Updates(map[string]interface{}{
						"BetAmount": daogorm.Expr("BetAmount + ?", betDetail.BetAmount),
						//"ValidBet":  daogorm.Expr("ValidBet + ?", betDetail.ValidAmount),
						"ThirdTime": thirdTime,
						"RawData":   string(bodyBytes),
					}).Error

					if e != nil {
						logs.Error("ab_single 转额 ", reqTypeStr, " 更新加注记录失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
						respdata.ResultCode = 50000
						respdata.Message = "更新加注记录失败"
						return e
					}

					// 记录加注信息
					logs.Info("ab_single 转额 ", reqTypeStr, " 检测到加注 thirdId=", thirdId, " betBillNo=", betBillNo, " 原始金额=", existingBet.BetAmount, " 加注金额=", betDetail.BetAmount)
					continue
				}

				// 检查是否是被取消的注单，如果是，则恢复为未结算状态并允许加注
				e = tx.Table(tablePre).Where("ThirdId = ? AND Brand = ? AND UserId = ? AND DataState = -2",
					betBillNo, a.brandName, userId).First(&existingBet).Error

				if e == nil {
					// 恢复被取消的注单为未结算状态
					e = tx.Table(tablePre).Where("Id = ?", existingBet.Id).Updates(map[string]interface{}{
						"DataState": -1, // 恢复为未结算状态
						"BetAmount": daogorm.Expr("BetAmount + ?", betDetail.BetAmount),
						"ThirdTime": thirdTime,
						"RawData":   string(bodyBytes),
					}).Error

					if e != nil {
						logs.Error("ab_single 转额 ", reqTypeStr, " 恢复取消注单失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
						respdata.ResultCode = 50000
						respdata.Message = "恢复取消注单失败"
						return e
					}

					logs.Info("ab_single 转额 ", reqTypeStr, " 恢复取消注单并加注 thirdId=", thirdId, " betBillNo=", betBillNo, " 原始金额=", existingBet.BetAmount, " 加注金额=", betDetail.BetAmount)
					continue
				}

				gameId = fmt.Sprintf("%v", betDetail.GameType)

				{
					gameList := thirdGameModel.GameList{}
					err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name").Where("GameId = ? and Brand=?", gameId, a.brandName).First(&gameList).Error
					if err != nil {
						logs.Error("ab_single 转额 ", reqTypeStr, " thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
					} else {
						gameName = gameList.Name
					}
				}

				// 创建新注单记录
				order := thirdGameModel.ThirdOrder{
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					ThirdId:      betBillNo,
					Brand:        a.brandName,
					UserId:       int(userId),
					BetAmount:    betDetail.BetAmount,
					WinAmount:    0,
					ValidBet:     0,
					State:        1,
					DataState:    -1, // -1表示未结算
					ThirdTime:    thirdTime,
					CreateTime:   thirdTime,
					Currency:     a.currency,
					GameId:       gameId,
					GameName:     gameName,
					BetCtx:       string(bodyBytes),
					BetCtxType:   3,
					//RoundId:       betBillNo,
					RawData: string(bodyBytes),
				}

				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("ab_single 转额 ", reqTypeStr, " 创建注单记录失败 thirdId=", thirdId, " betBillNo=", betBillNo, " order=", order, " error=", e.Error())
					respdata.ResultCode = 50000
					respdata.Message = "创建注单记录失败"
					return e
				}
			}
		} else {
			// 非批量下注处理（派彩结算、手动派彩结算、额度转入/转出、重新派彩、活动结算）
			// 对于派彩结算和手动派彩结算，需要查找并更新原始注单
			if (reqdata.Type == 20 || reqdata.Type == 21) && len(betDetails) > 0 {
				for _, betDetail := range betDetails {
					betBillNo := fmt.Sprintf("%d", betDetail.BetNum)

					// 查找原始注单
					var originalOrder thirdGameModel.ThirdOrder
					e = tx.Table(tablePre).Where("ThirdId = ? AND Brand = ?", betBillNo, a.brandName).First(&originalOrder).Error

					if e != nil {
						if e == daogorm.ErrRecordNotFound {
							logs.Info("ab_single 转额 原始注单不存在 thirdId=", thirdId, " betBillNo=", betBillNo, " error=", e.Error())
							//continue // 跳过不存在的注单
							respdata.ResultCode = 10006
							respdata.Message = "注单不存在"
							return e
						}
						logs.Error("ab_single 转额 查询原始注单失败 thirdId=", thirdId, " betBillNo=", betBillNo, " error=", e.Error())
						respdata.ResultCode = 10006
						respdata.Message = "查询原始注单失败"
						return e
					}
					// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
					// 注意：WinOrLossAmount，正数表示用户赢钱，负数表示用户输钱
					// 派奖金额 = 投注金额 + 玩家盈利
					winAmount := betDetail.BetAmount + betDetail.WinOrLossAmount
					betAmount := betDetail.BetAmount
					validBet := a.calculateValidBet(betAmount, betDetail.WinOrLossAmount)

					// 检查注单状态
					if originalOrder.DataState == -2 {
						// 注单已被取消，但我们需要检查是否是原始下注还是加注
						// 如果是原始下注（status=100），则不能结算
						// 如果是加注（status=110），则需要查找原始下注是否存在

						if betDetail.Status == 100 {
							// 原始下注被取消，不能结算
							logs.Error("ab_single 转额 ", reqTypeStr, " 原始注单已取消 thirdId=", thirdId, " betBillNo=", betBillNo, " order=", originalOrder)
							respdata.ResultCode = 10007
							respdata.Message = "注单已取消"
							return errors.New("注单已取消")
						} else {
							// 检查是否存在未取消的原始下注
							//var originalBet thirdGameModel.ThirdOrder
							//e = tx.Table(tablePre).Where("ThirdId = ? AND Brand = ? AND DataState != -2 AND Status = 100",
							//	betBillNo, a.brandName).First(&originalBet).Error

							originalBet := originalOrder
							// 存在未取消的原始下注，允许结算
							logs.Info("ab_single 转额 ", reqTypeStr, " 加注已取消但原始下注存在，允许结算 thirdId=", thirdId, " betBillNo=", betBillNo)

							// 更新原始下注的状态
							e = tx.Table(tablePre).Where("Id = ?", originalBet.Id).Updates(map[string]interface{}{
								"DataState": 1,         // 1表示已结算
								"WinAmount": winAmount, // 使用结算金额
								"ValidBet":  validBet,
								"BetCtx":    string(bodyBytes),
								"GameRst":   string(bodyBytes),
								"ThirdTime": thirdTime,
								"RawData":   string(bodyBytes),
							}).Error

							if e != nil {
								logs.Error("ab_single 转额 ", reqTypeStr, " 更新原始下注状态失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
								respdata.ResultCode = 50000
								respdata.Message = "更新原始下注状态失败"
								return e
							}

							// 创建或更新正式表中的注单
							originalBet.DataState = 1
							originalBet.WinAmount = winAmount
							originalBet.BetCtx = string(bodyBytes)
							originalBet.GameRst = string(bodyBytes)
							originalBet.ThirdTime = thirdTime
							originalBet.RawData = string(bodyBytes)
							originalBet.ValidBet = validBet
							originalBet.Id = 0

							// 先尝试更新，如果不存在则创建
							result := tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", betBillNo, a.brandName, userId).Updates(map[string]interface{}{
								"DataState": 1,
								"WinAmount": winAmount,
								"ValidBet":  validBet,
								"BetCtx":    string(bodyBytes),
								"GameRst":   string(bodyBytes),
								"ThirdTime": thirdTime,
								"RawData":   string(bodyBytes),
							})

							if result.Error != nil || result.RowsAffected == 0 {
								// 如果更新失败或没有记录被更新，则创建新记录
								e = tx.Table(table).Create(&originalBet).Error
								if e != nil {
									logs.Error("ab_single 转额 ", reqTypeStr, " 创建正式表注单失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
									respdata.ResultCode = 50000
									respdata.Message = "创建正式表注单失败"
									return e
								}
							}

							continue

						}
					} else if originalOrder.DataState == 1 {
						// 注单已结算，需要回滚之前的结算，然后重新结算
						if reqdata.Type == 21 { // 只有手动派彩才允许重新结算
							logs.Info("ab_single 转额 注单已结算，进行重新结算 thirdId=", thirdId, " betBillNo=", betBillNo)
							// 如果原始赢钱金额大于0，需要先扣回玩家赢的钱
							if originalOrder.WinAmount > 0 {
								// 计算需要回滚的金额
								rollbackAmount := -originalOrder.WinAmount
								// 更新用户余额（回滚之前的结算）
								resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
									"Amount": daogorm.Expr("Amount + ?", rollbackAmount),
								})
								e = resultTmp.Error
								if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && rollbackAmount != 0 {
									e = errors.New("更新条数0")
								}
								if e != nil {
									logs.Error("ab_single 转额 回滚之前结算失败 thirdId=", thirdId, " userId=", userId, " rollbackAmount=", rollbackAmount, " err=", e.Error())
									respdata.ResultCode = 50000
									respdata.Message = "回滚之前结算失败"
									return e
								}

								// 创建回滚账变记录
								rollbackLog := thirdGameModel.AmountChangeLog{
									UserId:       userBalance.UserId,
									BeforeAmount: userBalance.Amount,
									Amount:       rollbackAmount,
									AfterAmount:  userBalance.Amount + rollbackAmount,
									Reason:       utils.BalanceCReasonABRollback,
									Memo:         a.brandName + " rollback,thirdId:" + fmt.Sprintf("%d", betDetail.BetNum) + ",tranId:" + thirdId,
									SellerId:     userBalance.SellerId,
									ChannelId:    userBalance.ChannelId,
									CreateTime:   thirdTime,
								}
								e = tx.Table("x_amount_change_log").Create(&rollbackLog).Error
								if e != nil {
									logs.Error("ab_single 转额 创建回滚账变记录失败 thirdId=", thirdId, " rollbackLog=", rollbackLog, " error=", e.Error())
									respdata.ResultCode = 50000
									respdata.Message = "创建回滚账变记录失败"
									return e
								}
								// 更新用户余额（使用新的结算金额）
								userBalance.Amount += rollbackAmount // 更新余额，加上回滚金额
							}

							// 更新预注单表中的注单状态
							e = tx.Table(tablePre).Where("Id = ?", originalOrder.Id).Updates(map[string]interface{}{
								"DataState": 1,         // 1表示已结算
								"WinAmount": winAmount, //betDetail.WinOrLossAmount,
								"ValidBet":  validBet,
								"BetCtx":    string(bodyBytes),
								"GameRst":   string(bodyBytes),
								"ThirdTime": thirdTime,
								"RawData":   string(bodyBytes),
							}).Error

							if e != nil {
								logs.Error("ab_single 转额 更新原始注单状态失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
								respdata.ResultCode = 50000
								respdata.Message = "更新原始注单状态失败"
								return e
							}

							// 更新正式表中的注单状态
							e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", betBillNo, a.brandName, userId).Updates(map[string]interface{}{
								"DataState": 1,         // 1表示已结算
								"WinAmount": winAmount, //betDetail.WinOrLossAmount,
								"ValidBet":  validBet,
								"BetCtx":    string(bodyBytes),
								"GameRst":   string(bodyBytes),
								"ThirdTime": thirdTime,
								"RawData":   string(bodyBytes),
							}).Error
							if e != nil {
								logs.Error("ab_single 转额 更新正式表订单状态失败 thirdId=", thirdId, " betBillNo=", betBillNo, " error=", e.Error())
								respdata.ResultCode = 50000
								respdata.Message = "更新订单状态失败"
								return e
							}

						} else {
							// 普通派彩结算不允许重新结算已结算的注单
							logs.Error("ab_single 转额 不能转额已结算订单 thirdId=", thirdId, " betBillNo=", betBillNo, " order=", originalOrder)
							respdata.ResultCode = 10007
							respdata.Message = "注单已结算"
							return errors.New("注单已结算")
						}
					} else if originalOrder.DataState != -1 {
						// 其他异常状态
						logs.Error("ab_single 转额 注单状态异常 thirdId=", thirdId, " betBillNo=", betBillNo, " order=", originalOrder)
						respdata.ResultCode = 10007
						respdata.Message = "注单状态异常"
						e = errors.New("注单状态异常")
						return e
					} else {

						// 未结算的注单，更新预注单表中的注单状态
						e = tx.Table(tablePre).Where("Id = ?", originalOrder.Id).Updates(map[string]interface{}{
							"DataState": 1,         // 1表示已结算
							"WinAmount": winAmount, //betDetail.WinOrLossAmount,
							"ValidBet":  validBet,
							"BetCtx":    string(bodyBytes),
							"GameRst":   string(bodyBytes),
							"ThirdTime": thirdTime,
							"RawData":   string(bodyBytes),
						}).Error

						if e != nil {
							logs.Error("ab_single 转额 更新原始注单状态失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
							respdata.ResultCode = 50000
							respdata.Message = "更新原始注单状态失败"
							return e
						}

						// 创建正式表注单
						originalOrder.DataState = 1
						originalOrder.WinAmount = winAmount //betDetail.WinOrLossAmount
						originalOrder.ValidBet = validBet
						originalOrder.ThirdTime = thirdTime
						originalOrder.RawData = string(bodyBytes)
						originalOrder.BetCtx = string(bodyBytes)
						originalOrder.GameRst = string(bodyBytes)
						originalOrder.Id = 0 // 重置ID，让数据库自动生成新ID

						e = tx.Table(table).Create(&originalOrder).Error
						if e != nil {
							logs.Error("ab_single 转额 创建正式表注单失败 thirdId=", thirdId, " betBillNo=", betBillNo, " err=", e.Error())
							respdata.ResultCode = 50000
							respdata.Message = "创建正式表注单失败"
							return e
						}
					}
				}
			} else if reqdata.Type == 40 { //活动结算 直接生成一条记录
				order := thirdGameModel.ThirdOrder{
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: ChannelId,
					ThirdId:      thirdId,
					Brand:        a.brandName,
					UserId:       int(userId),
					BetAmount:    0,
					WinAmount:    reqdata.Amount,
					ValidBet:     0,
					State:        1,
					DataState:    1, // 1表示已结算
					ThirdTime:    thirdTime,
					CreateTime:   thirdTime,
					Currency:     a.currency,
					GameId:       gameId,
					GameName:     "活动结算",
					//RoundId:       roundId,
					RawData: string(bodyBytes),
				}
				// 其他类型直接创建注单
				e = tx.Table(tablePre).Create(&order).Error
				if e != nil {
					logs.Error("ab_single 转额 创建注单记录失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.ResultCode = 50000
					respdata.Message = "创建注单记录失败"
					return e
				}

				// 其他类型直接创建注单
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("ab_single 转额 创建注单记录失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.ResultCode = 50000
					respdata.Message = "创建注单记录失败"
					return e
				}
			}
		}

		// 更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", reqdata.Amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("ab_single 转额 加扣款失败 thirdId=", thirdId, " userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "加扣款失败"
			return e
		}

		//30: 额度转入
		//31: 额度转出
		//32: 重新派彩
		//只写账变 不写注单
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			Amount:       reqdata.Amount,
			BeforeAmount: userBalance.Amount,
			AfterAmount:  userBalance.Amount + reqdata.Amount,
			Reason:       reason,
			Memo:         a.brandName + " " + reqTypeStr + ",thirdId:" + billNo + ",tranId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("ab_single 转额 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "创建账变记录失败"
			return e
		}

		memo := amountLog.Memo
		e = a.AddThirdAmountLog(tx, reqdata.Amount, reason, userBalance.UserId, thirdId, thirdId, memo)
		if e != nil {
			logs.Error("ab_single 转额 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "创建账变记录失败"
			return e
		}

		respdata.Balance = userBalance.Amount + reqdata.Amount
		return nil
	})

	if err != nil {
		logs.Error("ab_single 转额 ", reqTypeStr, " 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		if reqdata.Type == 20 || reqdata.Type == 21 || reqdata.Type == 40 {
			// 推送奖励事件通知
			if a.thirdGamePush != nil {
				//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
				a.thirdGamePush.PushRewardEvent(5, a.brandName, thirdId)
			}
		}
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if a.RefreshUserAmountFunc != nil {
				tmpErr := a.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][ab_single] 转额 ", reqTypeStr, " 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][ab_single] 转额 ", reqTypeStr, " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Version = time.Now().Unix() // 使用当前时间戳作为版本号
	ctx.RespJson(respdata)
	logs.Info("ab_single 转额 ", reqTypeStr, " 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

/**
 * 记录三方账变 用于回滚注单金额
 */
func (s *ABSingleService) AddThirdAmountLog(tx *daogorm.DB, changeAmount float64, reason int, userId int, thirdId string, transactionId string, memo string) error {
	amountLog := thirdGameModel.ThirdAmountLog{
		UserId:        userId,
		Amount:        changeAmount,
		ThirdId:       thirdId,
		TransactionId: transactionId,
		Reason:        reason,
		Memo:          memo,
		Brand:         s.brandName,
		CreateTime:    time.Now().In(tzUTC8St8).Format("2006-01-02 15:04:05"),
	}
	e := tx.Table("x_third_amount_log").Create(&amountLog).Error
	if e != nil {
		return e
	}
	return nil
}

// CancelTransfer 回滚投注中奖接口
// 对应API文档中的 3. 取消转额
func (a *ABSingleService) CancelTransfer(ctx *abugo.AbuHttpContent) {
	type BetDetail struct {
		BetNum             int64   `json:"betNum"`             // 唯一的注单编号
		GameRoundId        int64   `json:"gameRoundId"`        // 唯一的游戏局编号
		Status             int     `json:"status"`             // 注单状态
		BetAmount          float64 `json:"betAmount"`          // 下注金额
		Deposit            float64 `json:"deposit"`            // 预扣金额
		GameType           int     `json:"gameType"`           // 游戏类型
		BetType            int     `json:"betType"`            // 下注类型
		Commission         int     `json:"commission"`         // 免佣类型
		ExchangeRate       float64 `json:"exchangeRate"`       // 汇率（相对人民币的汇率）
		GameResult         string  `json:"gameResult"`         // 游戏结果
		GameResult2        string  `json:"gameResult2"`        // 游戏结果2
		WinOrLossAmount    float64 `json:"winOrLossAmount"`    // 输赢金额，不包括下注金额
		ValidAmount        float64 `json:"validAmount"`        // 有效投注金额
		BetTime            string  `json:"betTime"`            // 下注时间(GMT+8)
		TableName          string  `json:"tableName"`          // 桌台名称
		BetMethod          int     `json:"betMethod"`          // 下注方式
		AppType            int     `json:"appType"`            // 客户端类型
		GameRoundStartTime string  `json:"gameRoundStartTime"` // 游戏局开始时间(GMT+8)
		GameRoundEndTime   string  `json:"gameRoundEndTime"`   // 游戏局结束时间(GMT+8)
		Ip                 string  `json:"ip"`                 // 玩家的IP地址
	}

	type EventDetail struct {
		EventType       int     `json:"eventType"`       // 活动类型
		EventNum        int64   `json:"eventNum"`        // 活动编号
		EventRecordNum  int64   `json:"eventRecordNum"`  // 活动记录编号
		EventAmount     float64 `json:"eventAmount"`     // 活动结算金额
		EventCreateTime string  `json:"eventCreateTime"` // 活动创建时间(GMT+8)
	}

	type CreditDetail struct {
		VendorId string `json:"vendorId"` // 供应商ID
		BillNo   string `json:"billNo"`   // 供应端生成的单号
	}

	type RequestData struct {
		Player          string        `json:"player"`          // 玩家帐户名称（包含代理商标识码）
		TranId          int64         `json:"tranId"`          // 唯一的交易编号
		OriginalTranId  int64         `json:"originalTranId"`  // 要取消的转额请求的tranId
		OriginalDetails []interface{} `json:"originalDetails"` // 要取消的转额详情
		Reason          string        `json:"reason"`          // 取消的原因
		IsRetry         bool          `json:"isRetry"`         // 标识是否为重试请求
	}

	type ResponseData struct {
		ResultCode int     `json:"resultCode"`        // 响应代码
		Message    string  `json:"message"`           // 错误信息
		Balance    float64 `json:"balance,omitempty"` // 玩家余额
		Version    int64   `json:"version,omitempty"` // 响应的版本号
	}
	respdata := ResponseData{
		ResultCode: AB_Code_Success,
		Message:    "Success",
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("ab_single 取消投注 读取请求消息体错误 err=", err.Error())
		respdata.ResultCode = 50000
		respdata.Message = "读取请求消息体错误"
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	}
	logs.Info("ab_single 取消投注 Request.Body=", string(bodyBytes))

	// 验证API签名
	path := "/CancelTransfer"
	isValid, resultCode, message := a.verifyApiSignature(ctx, path)
	if !isValid {
		respdata.ResultCode = resultCode
		respdata.Message = message
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("ab_single 取消投注 解析请求消息体错误 err=", err.Error())
		respdata.ResultCode = 40000
		respdata.Message = "json反序列化请求消息体错误"
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	}
	thirdId := fmt.Sprintf("%d", reqdata.TranId)
	originalThirdId := fmt.Sprintf("%d", reqdata.OriginalTranId)
	reqTypeStr := "取消投注"

	// 判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(a.brandName, thirdId, ctx.Gin().Request.URL.String())
	if err != nil {
		respdata.ResultCode = 50000
		respdata.Message = "系统错误"
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		logs.Error("ab_single 取消转额 检测是否重复请求 发生错误 thirdId=", thirdId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("ab_single 取消转额 检测到重复请求 thirdId=", thirdId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			respdata.ResultCode = 50000
			respdata.Message = "系统错误"
			respdata.Balance = 0
			respdata.Version = 0
			ctx.RespJson(respdata)
		}
		return
	}

	// 记录请求响应日志
	defer func() {
		respCode := 0
		if respdata.ResultCode != 0 {
			respCode = 1
		}
		base.AddRequestDB(thirdId, string(bodyBytes), respdata, respCode, a.brandName, ctx.Gin().Request.URL.String())
	}()

	// 从玩家账号获取用户ID
	//userId, err := strconv.Atoi(reqdata.Player)
	userId, err := a.getUserIdFromUserName(reqdata.Player)
	if err != nil {
		logs.Error("ab_single 取消投注 玩家账号错误 thirdId=", thirdId, " reqdata.Player=", reqdata.Player, " err=", err.Error())
		respdata.ResultCode = 10003
		respdata.Message = "玩家账号不存在"
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	}

	// 确定表名
	//table := "x_third_live"
	tablePre := "x_third_live_pre_order"

	// 获取当前时间
	thirdTime := time.Now().Format("2006-01-02 15:04:05")

	// 解析原始详情数组，获取betNum
	var originalBetDetails []BetDetail
	var betNums []string
	var billNo string

	if len(reqdata.OriginalDetails) > 0 {
		detailsBytes, _ := json.Marshal(reqdata.OriginalDetails)
		err = json.Unmarshal(detailsBytes, &originalBetDetails)
		if err != nil {
			logs.Error("ab_single 取消投注 解析原始投注记录失败 thirdId=", thirdId, " err=", err.Error())
			respdata.ResultCode = 40000
			respdata.Message = "解析原始投注记录失败"
			respdata.Balance = 0
			respdata.Version = 0
			ctx.RespJson(respdata)
			return
		}

		// 收集所有betNum
		for _, detail := range originalBetDetails {
			betNums = append(betNums, fmt.Sprintf("%d", detail.BetNum))
		}

		// 使用第一个betNum作为billNo
		if len(originalBetDetails) > 0 {
			billNo = fmt.Sprintf("%d", originalBetDetails[0].BetNum)
		}
	}

	// 如果billNo为空，使用originalThirdId
	if billNo == "" {
		billNo = originalThirdId
	}

	// 查询账变记录，确定要取消的交易
	thirdAmountLog := thirdGameModel.ThirdAmountLog{}
	e := server.Db().GormDao().Table("x_third_amount_log").
		Select("*").
		Where("UserId = ? and TransactionId = ? and Brand = ? ", userId, reqdata.OriginalTranId, a.brandName).
		First(&thirdAmountLog).Error
	if e != nil {
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			logs.Error("ab_single 转额 ", reqTypeStr, " 账变记录不存在 userId=", userId, "transactionId=", reqdata.OriginalTranId, " error=", e.Error())
			respdata.ResultCode = 10006
			respdata.Message = "交易不存在"
		} else {
			logs.Error("ab_single 转额 ", reqTypeStr, " 查询账变失败 userId=", userId, "transactionId=", reqdata.OriginalTranId, " error=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "查询账变失败"
		}
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	}

	// 确保 cancelAmount 的值是正确的
	// 对于投注和加注，thirdAmountLog.Amount 是负数，我们需要取其绝对值作为返还金额
	cancelAmount := thirdAmountLog.Amount
	if cancelAmount < 0 {
		cancelAmount = -cancelAmount // 取绝对值，确保返还金额为正数
	}

	// 开始回滚投注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("ab_single ", reqTypeStr, " 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ResultCode = 10003
				respdata.Message = "会员不存在"
			} else {
				respdata.ResultCode = 50000
				respdata.Message = "查询用户信息失败"
			}
			return e
		}
		respdata.Balance = userBalance.Amount

		//// 回滚金额大于用户余额
		//if userBalance.Amount < 0 || userBalance.Amount < totalBetAmount-totalWinAmount {
		//	logs.Error("ab_single ", reqTypeStr, " 玩家余额不足 thirdId=", thirdId, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " totalBetAmount=", totalBetAmount, " totalWinAmount=", totalWinAmount)
		//	e = errors.New("玩家余额不足")
		//	respdata.ResultCode = 10101
		//	respdata.Message = "额度不足"
		//	return e
		//}

		// 查询原始注单是否存在
		var originalOrders []thirdGameModel.ThirdOrder
		var whereClause string
		var whereParams []interface{}

		// 如果有betNums，则按betNum查询
		if len(betNums) > 0 {
			placeholders := make([]string, len(betNums))
			for i := range placeholders {
				placeholders[i] = "?"
				whereParams = append(whereParams, betNums[i])
			}
			whereClause = fmt.Sprintf("ThirdId IN (%s) AND Brand = ? AND UserId = ?", strings.Join(placeholders, ","))
			whereParams = append(whereParams, a.brandName, userId)
		} else {
			// 否则按originalThirdId查询
			whereClause = "ThirdId = ? AND Brand = ?"
			whereParams = []interface{}{originalThirdId, a.brandName}
		}

		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where(whereClause, whereParams...).Find(&originalOrders).Error
		if e != nil {
			logs.Error("ab_single ", reqTypeStr, " 查询注单失败 thirdId=", thirdId, " originalThirdId=", originalThirdId, " err=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "查询注单失败"
			return e
		}

		if len(originalOrders) == 0 {
			logs.Error("ab_single ", reqTypeStr, " 注单不存在 thirdId=", thirdId, " originalThirdId=", originalThirdId)
			respdata.ResultCode = 10006
			respdata.Message = "注单不存在"
			return errors.New("注单不存在")
		}

		// 计算总的取消金额
		var totalBetAmount float64
		var totalWinAmount float64

		for _, order := range originalOrders {
			// 检查原始注单状态
			if order.DataState != -1 {
				if order.DataState == -2 {
					logs.Error("ab_single ", reqTypeStr, " 注单已取消 thirdId=", thirdId, " order=", order)
					continue // 跳过已取消的注单
				} else {
					logs.Error("ab_single ", reqTypeStr, " 注单状态异常 thirdId=", thirdId, " order=", order)
					respdata.ResultCode = 10007
					respdata.Message = "注单状态异常"
					e = errors.New("注单状态异常")
					return e
				}
			}

			// 检查用户是否匹配
			if order.UserId != int(userId) {
				logs.Error("ab_single ", reqTypeStr, " 注单用户不匹配 thirdId=", thirdId, " order=", order, " userId=", userId)
				respdata.ResultCode = 10003
				respdata.Message = "注单用户不匹配"
				e = errors.New("注单用户不匹配")
				return e
			}

			resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"BetAmount": daogorm.Expr("BetAmount - ?", cancelAmount),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("ab_single ", reqTypeStr, " 更新注单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.ResultCode = 50000
				respdata.Message = "更新注单失败"
				return e
			}

			totalBetAmount += order.BetAmount
			totalWinAmount += order.WinAmount
		}

		// 回滚下注金额
		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", cancelAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && cancelAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("ab_single ", reqTypeStr, " 加款失败 thirdId=", thirdId, " userId=", userId, " cancelAmount=", cancelAmount, " err=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "加款失败"
			return e
		}

		// 创建回滚下注账变记录
		amountLogBet := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       cancelAmount,
			AfterAmount:  userBalance.Amount + cancelAmount,
			Reason:       utils.BalanceCReasonABCancel,
			Memo:         a.brandName + " cancel,thirdId:" + billNo + ",tranId:" + originalThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLogBet).Error
		if e != nil {
			logs.Error("ab_single ", reqTypeStr, " 创建回滚下注账变记录失败 thirdId=", thirdId, " amountLogBet=", amountLogBet, " error=", e.Error())
			respdata.ResultCode = 50000
			respdata.Message = "创建回滚下注账变记录失败"
			return e
		}
		respdata.Balance = userBalance.Amount + cancelAmount
		return nil
	})

	if err != nil {
		logs.Error("ab_single ", reqTypeStr, " 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		respdata.Balance = 0
		respdata.Version = 0
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if a.RefreshUserAmountFunc != nil {
				tmpErr := a.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][ab_single] ", reqTypeStr, " 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][ab_single] ", reqTypeStr, " 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	respdata.Version = time.Now().Unix() // 使用当前时间戳作为版本号
	ctx.RespJson(respdata)
	logs.Info("ab_single ", reqTypeStr, " 响应成功 thirdId=", thirdId, " respdata=", respdata)
	return
}

// Login 登录接口
// 对应API文档中的 1.1. 检查或创建玩家帐号 和 1.2. 登入游戏 组合实现
func (a *ABSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("ab_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	//// 支持的语言映射
	//langMap := map[string]string{
	//	"zh-CN": "zh_CN", // 中文简体
	//	"zh-TW": "zh_TW", // 中文繁体
	//	"en-US": "en",    // 英文
	//	"ko-KR": "ko",    // 韩文
	//	"th-TH": "th",    // 泰文
	//	"vi-VN": "vi",    // 越南文
	//	"ja-JP": "ja",    // 日文
	//	"id-ID": "id",    // 印度尼西亚文
	//	"hi-IN": "hi",    // 印地文
	//	"pt-BR": "pt_br", // 巴西葡萄牙文
	//	"es-ES": "es",    // 西班牙文
	//}
	//
	//loginLang := "zh_CN" // 默认简体中文
	//if reqdata.LangCode != "" {
	//	if mappedLang, ok := langMap[reqdata.LangCode]; ok {
	//		loginLang = mappedLang
	//	}
	//}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("ab_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyAB, userId); err != nil {
		logs.Error("ab_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	// 查询游戏信息
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", a.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("ab_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 获取用户信息
	userInfo := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,Account,Amount").Where("UserId = ?", userId).First(&userInfo).Error
	if err != nil {
		logs.Error("ab_single 登录游戏 获取用户信息失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试3")
		return
	}

	// 生成玩家账号
	playerName := a.getUserNameFromUserId(int64(userId))

	// 1. 检查或创建玩家账号
	checkOrCreateReq := struct {
		Agent  string `json:"agent"`
		Player string `json:"player"`
	}{
		Agent:  a.agent, // 直属上线代理帐号，这里使用固定值，实际应根据需求设置
		Player: playerName,
	}
	checkOrCreateReqBytes, _ := json.Marshal(checkOrCreateReq)
	checkOrCreateRespBytes, err := a.sendAllBetApiRequest("/CheckOrCreate", checkOrCreateReqBytes)
	if err != nil {
		logs.Error("ab_single 登录游戏 检查或创建玩家账号请求失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试4")
		return
	}

	type CheckOrCreateResp struct {
		ResultCode string `json:"resultCode"`
		Message    string `json:"message"`
		Data       struct {
			Player string `json:"player"`
		} `json:"data"`
	}
	checkOrCreateResp := CheckOrCreateResp{}
	err = json.Unmarshal(checkOrCreateRespBytes, &checkOrCreateResp)
	if err != nil {
		logs.Error("ab_single 登录游戏 解析检查或创建玩家账号响应失败 userId=", userId, " err=", err.Error(), " respBytes=", string(checkOrCreateRespBytes))
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试5")
		return
	}

	if checkOrCreateResp.ResultCode != "OK" && checkOrCreateResp.ResultCode != "PLAYER_EXIST" {
		logs.Error("ab_single 登录游戏 检查或创建玩家账号失败 userId=", userId, " resultCode=", checkOrCreateResp.ResultCode, " message=", checkOrCreateResp.Message)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试6")
		return
	}

	// 2. 登入游戏
	loginReq := struct {
		Player    string `json:"player"`
		TargetUrl string `json:"targetUrl,omitempty"`
		Language  string `json:"language,omitempty"`
		ReturnUrl string `json:"returnUrl,omitempty"`
		TableName string `json:"tableName,omitempty"`
	}{
		Player:    playerName,
		Language:  reqdata.LangCode,
		TableName: gameList.ThirdGameType,
	}

	if reqdata.HomeUrl != "" {
		loginReq.ReturnUrl = reqdata.HomeUrl
	}

	loginReqBytes, _ := json.Marshal(loginReq)
	loginRespBytes, err := a.sendAllBetApiRequest("/Login", loginReqBytes)
	if err != nil {
		logs.Error("ab_single 登录游戏 登入游戏请求失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}

	type LoginResp struct {
		ResultCode string `json:"resultCode"`
		Message    string `json:"message"`
		Data       struct {
			GameLoginUrl string `json:"gameLoginUrl"`
		} `json:"data"`
	}
	loginResp := LoginResp{}
	err = json.Unmarshal(loginRespBytes, &loginResp)
	if err != nil {
		logs.Error("ab_single 登录游戏 解析登入游戏响应失败 userId=", playerName, " err=", err.Error(), " respBytes=", string(loginRespBytes))
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试8")
		return
	}

	if loginResp.ResultCode != "OK" {
		logs.Error("ab_single 登录游戏 登入游戏失败 userId=", playerName, " resultCode=", loginResp.ResultCode, " message=", loginResp.Message)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试9")
		return
	}

	// 返回游戏登录URL
	ctx.RespOK(loginResp.Data.GameLoginUrl)
	logs.Info("ab_single 登录游戏 成功 userId=", playerName, " gameLoginUrl=", loginResp.Data.GameLoginUrl)
	return
}

/**
 * 查询桌台列表
 * 用于查询指定代理的当前可用的游戏桌台列表
 */
func (a *ABSingleService) GetGameTables(ctx *abugo.AbuHttpContent) {

	// 构建请求参数
	requestBody := struct {
		Agent string `json:"agent"`
	}{
		Agent: a.agent,
	}
	errcode := 0
	// 将请求参数转换为JSON
	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		logs.Error("ab_single GetGameTables 请求参数序列化失败 err=", err.Error())
		ctx.RespErrString(true, &errcode, "失败")
		return
	}

	// 发送请求到 AB API
	respBody, err := a.sendAllBetApiRequest("/GetGameTables", bodyBytes)
	if err != nil {
		logs.Error("ab_single GetGameTables 发送HTTP请求失败 err=", err.Error())
		ctx.RespErrString(true, &errcode, "失败")
		return
	}
	logs.Info("ab_single GetGameTables Request.Body=", string(respBody))
	// 解析响应内容
	type Table struct {
		TableName string `json:"tableName"` // 桌台名称
		GameType  int    `json:"gameType"`  // 游戏类型
	}

	type ResponseData struct {
		ResultCode string `json:"resultCode"`
		Message    string `json:"message"`
		Data       struct {
			Tables []Table `json:"tables"` // 游戏桌台列表
		} `json:"data"`
	}

	var responseData ResponseData
	err = json.Unmarshal(respBody, &responseData)
	if err != nil {
		logs.Error("ab_single GetGameTables 解析响应内容失败 err=", err.Error(), " respBody=", string(respBody))
		ctx.RespErrString(true, &errcode, "失败")
		return
	}

	// 返回响应
	ctx.RespJson(responseData)
	logs.Info("ab_single GetGameTables 响应成功 agent=", a.agent)
	return
}

func (s *ABSingleService) calculateValidBet(betAmount, gold float64) float64 {
	// 平局时有效流水为0
	if gold == 0 {
		return 0
	}
	// 有效流水取不大于下注金额的输赢绝对值
	validBet := math.Abs(gold)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}
	return validBet
}
