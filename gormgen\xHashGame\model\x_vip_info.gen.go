// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXVipInfo = "x_vip_info"

// XVipInfo mapped from table <x_vip_info>
type XVipInfo struct {
	ID                  int32     `gorm:"column:Id;not null" json:"Id"`
	SellerID            int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                       // 运营商
	ChannelID           int32     `gorm:"column:ChannelId;comment:渠道商" json:"ChannelId"`                     // 渠道商
	UserID              int32     `gorm:"column:UserId;primaryKey;comment:玩家id" json:"UserId"`               // 玩家id
	VipLevel            int32     `gorm:"column:VipLevel;default:1;comment:当前vip等级" json:"VipLevel"`         // 当前vip等级
	Recharge            float64   `gorm:"column:Recharge;default:0.000000;comment:累计充值" json:"Recharge"`     // 累计充值
	LiuSui              float64   `gorm:"column:LiuSui;default:0.000000;comment:累计流水" json:"LiuSui"`         // 累计流水
	KeepLiuSui          float64   `gorm:"column:KeepLiuSui;default:0.000000;comment:保级流水" json:"KeepLiuSui"` // 保级流水
	UpgradeTime         time.Time `gorm:"column:UpgradeTime;comment:升级时间" json:"UpgradeTime"`                // 升级时间
	UpgradeRewardRecord string    `gorm:"column:UpgradeRewardRecord" json:"UpgradeRewardRecord"`
	Withdraw            float64   `gorm:"column:Withdraw;default:0.000000;comment:累计提现" json:"Withdraw"`      // 累计提现
	CaiJin              float64   `gorm:"column:CaiJin;default:0.000000;comment:累计彩金Usdt" json:"CaiJin"`      // 累计彩金Usdt
	CaiJinTrx           float64   `gorm:"column:CaiJinTrx;default:0.000000;comment:累计彩金Trx" json:"CaiJinTrx"` // 累计彩金Trx
}

// TableName XVipInfo's table name
func (*XVipInfo) TableName() string {
	return TableNameXVipInfo
}
