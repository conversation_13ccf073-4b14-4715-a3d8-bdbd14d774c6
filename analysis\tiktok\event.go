package tiktok

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"strings"
	"time"
)

func toSHA256(s string) string {
	h := sha256.New()
	h.Write([]byte(s))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func NewUserData(user *User) User {
	if user.Phone != "" {
		user.Phone = strings.TrimPrefix(strings.TrimSpace(user.Phone), "+")
		user.Phone = toSHA256(user.Phone)
	}
	if user.Email != "" {
		user.Email = strings.ToLower(strings.TrimSpace(user.Email))
		user.Email = toSHA256(user.Email)
	}

	return *user
}

func (c *Client) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	logs.Info("Tiktok 请求参数", string(data))
	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, err
	}

	logs.Info("Tiktok返回：", resp.String())

	return resp, nil
}

func (c *Client) PushEvent(eventId string, eventName string, user User, properties Properties, page Page) (err error) {
	data := &PostData{
		EventSource:   "web",
		EventSourceId: c.pixelId,
		Data: []Data{
			{
				Event:      eventName,
				EventTime:  time.Now().Unix(),
				EventId:    eventId,
				User:       user,
				Properties: properties,
				Page:       page,
			},
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = c.post(c.api, map[string]string{
		"Content-Type": "application/json",
		"Access-Token": c.accessToken,
	}, jsonData)

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) CompleteRegistration(eventId string, user User) (err error) {
	properties := Properties{
		Contents: []Content{
			{
				ContentId: eventId,
			},
		},
	}
	page := Page{
		Url:      "/register",
		Referrer: "",
	}
	return c.PushEvent(eventId, EventName_CompleteRegistration, NewUserData(&user), properties, page)
}

func (c *Client) AddToCart(eventId string, user User) (err error) {
	properties := Properties{
		Contents: []Content{
			{
				ContentId: eventId,
			},
		},
	}
	page := Page{
		Url:      "/add_cart",
		Referrer: "",
	}

	return c.PushEvent(eventId, EventName_AddToCart, user, properties, page)
}

func (c *Client) Purchase(eventId string, user User, amount float64) (err error) {
	properties := Properties{
		Contents: []Content{
			{
				ContentId: eventId,
			},
		},
		Currency: "USD",
		Value:    amount,
	}
	page := Page{
		Url:      "/recharge",
		Referrer: "",
	}

	return c.PushEvent(eventId, EventName_Purchase, NewUserData(&user), properties, page)
}

func (c *Client) FirstRecharge(eventId string, user User, amount float64) (err error) {
	properties := Properties{
		Contents: []Content{
			{
				ContentId: eventId,
			},
		},
		Currency: "USD",
		Value:    amount,
	}
	page := Page{
		Url:      "/recharge",
		Referrer: "",
	}

	return c.PushEvent(eventId, EventName_FirstRecharge, NewUserData(&user), properties, page)
}
