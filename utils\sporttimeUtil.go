package utils

import (
	"fmt"
	"time"
)

// 处理体育时区问题
// TimeUtil 时间工具结构体
type TimeUtil struct{}

// ToWest4 将给定时间转换为西四区时间，返回格式化后的字符串
func (tu *TimeUtil) ToWest4(t time.Time) string {
	west4 := time.FixedZone("UTC-4", -4*60*60)
	return t.In(west4).Format("2006-01-02 15:04:05")
}

// FromWest4 将西四区时间字符串转换为time.Time
func (tu *TimeUtil) FromWest4(timeStr string) (time.Time, error) {
	west4 := time.FixedZone("UTC-4", -4*60*60)
	return time.ParseInLocation("2006-01-02 15:04:05", timeStr, west4)
}

// 更通用的时区转换方法
// ToTimezone 将时间转换到指定时区
func (tu *TimeUtil) ToTimezone(t time.Time, offsetHours int) string {
	timezone := time.FixedZone(fmt.Sprintf("UTC%+d", offsetHours), offsetHours*60*60)
	return t.In(timezone).Format("2006-01-02 15:04:05")
}

// ParseTime 解析任意时区的时间字符串
func (tu *TimeUtil) ParseTime(timeStr string) (time.Time, error) {
	// 尝试解析多种格式
	layouts := []string{
		time.RFC3339,           // 2025-02-27T23:23:58+08:00
		"2006-01-02T15:04:05Z", // UTC格式
		"2006-01-02 15:04:05",  // 标准格式
	}

	var t time.Time
	var err error
	for _, layout := range layouts {
		t, err = time.Parse(layout, timeStr)
		if err == nil {
			return t, nil
		}
	}
	return time.Time{}, fmt.Errorf("unable to parse time string: %s", timeStr)
}
