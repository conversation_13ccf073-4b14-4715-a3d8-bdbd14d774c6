package model

import (
	"github.com/shopspring/decimal"
	"xserver/gormgen/xHashGame/model"
)

type RecentProfit struct {
	BetTime      string          `json:"BetTime" gorm:"column:BetTime"`
	GameType     int             `json:"GameType" gorm:"column:GameType"`
	BrandType    int             `json:"BrandType" gorm:"column:BrandType"`
	Brand        string          `json:"Brand" gorm:"column:Brand"`
	GameId       string          `json:"GameId" gorm:"column:GameId"`
	CName        string          `json:"CName" gorm:"column:CName"`
	EName        string          `json:"EName" gorm:"column:EName"`
	CIcon        string          `json:"CIcon" gorm:"column:CIcon"`
	EIcon        string          `json:"EIcon" gorm:"column:EIcon"`
	UserId       int32           `json:"UserId" gorm:"column:UserId"`
	BetAmount    decimal.Decimal `json:"BetAmount" gorm:"column:BetAmount"`
	RewardAmount decimal.Decimal `json:"RewardAmount" gorm:"column:RewardAmount"`
	WinLost      decimal.Decimal `json:"WinLost" gorm:"column:WinLost"`
}
type RecentProfitRes struct {
	State       uint8
	OnlineUsers int32          `json:"OnlineUsers"`
	List        []RecentProfit `json:"List"`
}

type GetChainGoods struct {
	GameId    int32           `gorm:"column:GameId"`
	ChainType int             `gorm:"column:ChainType"`
	State     int32           `gorm:"column:State"`
	Period    string          `gorm:"column:Period"`
	OrderList []*model.XOrder `gorm:"column:-"`
}
