// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXCaijingDetail(db *gorm.DB, opts ...gen.DOOption) xCaijingDetail {
	_xCaijingDetail := xCaijingDetail{}

	_xCaijingDetail.xCaijingDetailDo.UseDB(db, opts...)
	_xCaijingDetail.xCaijingDetailDo.UseModel(&model.XCaijingDetail{})

	tableName := _xCaijingDetail.xCaijingDetailDo.TableName()
	_xCaijingDetail.ALL = field.NewAsterisk(tableName)
	_xCaijingDetail.ID = field.NewInt32(tableName, "Id")
	_xCaijingDetail.UserID = field.NewInt32(tableName, "UserId")
	_xCaijingDetail.SType = field.NewString(tableName, "SType")
	_xCaijingDetail.Symbol = field.NewString(tableName, "Symbol")
	_xCaijingDetail.Amount = field.NewFloat64(tableName, "Amount")
	_xCaijingDetail.MinLiuShui = field.NewFloat64(tableName, "MinLiuShui")
	_xCaijingDetail.CSGroup = field.NewString(tableName, "CSGroup")
	_xCaijingDetail.CSID = field.NewString(tableName, "CSId")
	_xCaijingDetail.CreateTime = field.NewTime(tableName, "CreateTime")
	_xCaijingDetail.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xCaijingDetail.Memo = field.NewString(tableName, "Memo")
	_xCaijingDetail.Account = field.NewString(tableName, "Account")

	_xCaijingDetail.fillFieldMap()

	return _xCaijingDetail
}

type xCaijingDetail struct {
	xCaijingDetailDo xCaijingDetailDo

	ALL        field.Asterisk
	ID         field.Int32
	UserID     field.Int32
	SType      field.String
	Symbol     field.String
	Amount     field.Float64
	MinLiuShui field.Float64 // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	CSGroup    field.String
	CSID       field.String
	CreateTime field.Time
	TopAgentID field.Int32
	Memo       field.String // 备注
	Account    field.String // 账号

	fieldMap map[string]field.Expr
}

func (x xCaijingDetail) Table(newTableName string) *xCaijingDetail {
	x.xCaijingDetailDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xCaijingDetail) As(alias string) *xCaijingDetail {
	x.xCaijingDetailDo.DO = *(x.xCaijingDetailDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xCaijingDetail) updateTableName(table string) *xCaijingDetail {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SType = field.NewString(table, "SType")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.MinLiuShui = field.NewFloat64(table, "MinLiuShui")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.Memo = field.NewString(table, "Memo")
	x.Account = field.NewString(table, "Account")

	x.fillFieldMap()

	return x
}

func (x *xCaijingDetail) WithContext(ctx context.Context) *xCaijingDetailDo {
	return x.xCaijingDetailDo.WithContext(ctx)
}

func (x xCaijingDetail) TableName() string { return x.xCaijingDetailDo.TableName() }

func (x xCaijingDetail) Alias() string { return x.xCaijingDetailDo.Alias() }

func (x xCaijingDetail) Columns(cols ...field.Expr) gen.Columns {
	return x.xCaijingDetailDo.Columns(cols...)
}

func (x *xCaijingDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xCaijingDetail) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SType"] = x.SType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["MinLiuShui"] = x.MinLiuShui
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Account"] = x.Account
}

func (x xCaijingDetail) clone(db *gorm.DB) xCaijingDetail {
	x.xCaijingDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xCaijingDetail) replaceDB(db *gorm.DB) xCaijingDetail {
	x.xCaijingDetailDo.ReplaceDB(db)
	return x
}

type xCaijingDetailDo struct{ gen.DO }

func (x xCaijingDetailDo) Debug() *xCaijingDetailDo {
	return x.withDO(x.DO.Debug())
}

func (x xCaijingDetailDo) WithContext(ctx context.Context) *xCaijingDetailDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xCaijingDetailDo) ReadDB() *xCaijingDetailDo {
	return x.Clauses(dbresolver.Read)
}

func (x xCaijingDetailDo) WriteDB() *xCaijingDetailDo {
	return x.Clauses(dbresolver.Write)
}

func (x xCaijingDetailDo) Session(config *gorm.Session) *xCaijingDetailDo {
	return x.withDO(x.DO.Session(config))
}

func (x xCaijingDetailDo) Clauses(conds ...clause.Expression) *xCaijingDetailDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xCaijingDetailDo) Returning(value interface{}, columns ...string) *xCaijingDetailDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xCaijingDetailDo) Not(conds ...gen.Condition) *xCaijingDetailDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xCaijingDetailDo) Or(conds ...gen.Condition) *xCaijingDetailDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xCaijingDetailDo) Select(conds ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xCaijingDetailDo) Where(conds ...gen.Condition) *xCaijingDetailDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xCaijingDetailDo) Order(conds ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xCaijingDetailDo) Distinct(cols ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xCaijingDetailDo) Omit(cols ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xCaijingDetailDo) Join(table schema.Tabler, on ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xCaijingDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xCaijingDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xCaijingDetailDo) Group(cols ...field.Expr) *xCaijingDetailDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xCaijingDetailDo) Having(conds ...gen.Condition) *xCaijingDetailDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xCaijingDetailDo) Limit(limit int) *xCaijingDetailDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xCaijingDetailDo) Offset(offset int) *xCaijingDetailDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xCaijingDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xCaijingDetailDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xCaijingDetailDo) Unscoped() *xCaijingDetailDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xCaijingDetailDo) Create(values ...*model.XCaijingDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xCaijingDetailDo) CreateInBatches(values []*model.XCaijingDetail, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xCaijingDetailDo) Save(values ...*model.XCaijingDetail) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xCaijingDetailDo) First() (*model.XCaijingDetail, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCaijingDetail), nil
	}
}

func (x xCaijingDetailDo) Take() (*model.XCaijingDetail, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCaijingDetail), nil
	}
}

func (x xCaijingDetailDo) Last() (*model.XCaijingDetail, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCaijingDetail), nil
	}
}

func (x xCaijingDetailDo) Find() ([]*model.XCaijingDetail, error) {
	result, err := x.DO.Find()
	return result.([]*model.XCaijingDetail), err
}

func (x xCaijingDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XCaijingDetail, err error) {
	buf := make([]*model.XCaijingDetail, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xCaijingDetailDo) FindInBatches(result *[]*model.XCaijingDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xCaijingDetailDo) Attrs(attrs ...field.AssignExpr) *xCaijingDetailDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xCaijingDetailDo) Assign(attrs ...field.AssignExpr) *xCaijingDetailDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xCaijingDetailDo) Joins(fields ...field.RelationField) *xCaijingDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xCaijingDetailDo) Preload(fields ...field.RelationField) *xCaijingDetailDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xCaijingDetailDo) FirstOrInit() (*model.XCaijingDetail, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCaijingDetail), nil
	}
}

func (x xCaijingDetailDo) FirstOrCreate() (*model.XCaijingDetail, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCaijingDetail), nil
	}
}

func (x xCaijingDetailDo) FindByPage(offset int, limit int) (result []*model.XCaijingDetail, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xCaijingDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xCaijingDetailDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xCaijingDetailDo) Delete(models ...*model.XCaijingDetail) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xCaijingDetailDo) withDO(do gen.Dao) *xCaijingDetailDo {
	x.DO = *do.(*gen.DO)
	return x
}
