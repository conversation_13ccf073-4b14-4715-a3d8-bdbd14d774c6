package third_game

// x_third_req_info
type ThirdReqInfo struct {
	Id            int64  `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	Brand         string `json:"Brand" gorm:"column:Brand"`
	ReqId         string `json:"ReqId" gorm:"column:ReqId"`
	ReqUrl        string `json:"ReqUrl" gorm:"column:ReqUrl"`
	ReqBody       string `json:"ReqBody" gorm:"column:ReqBody"`
	RespBody      string `json:"RespBody" gorm:"column:RespBody"`
	RepeatReqData string `json:"RepeatReqData" gorm:"column:RepeatReqData"`
	Code          int    `json:"Code" gorm:"column:Code"` // 0成功 1异常
	CreatedAt     int64  `json:"CreatedAt" gorm:"column:CreatedAt"`
}
