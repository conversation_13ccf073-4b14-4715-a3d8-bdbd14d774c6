// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveReward = "x_active_reward"

// XActiveReward 活动发放表
type XActiveReward struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`                                                                                     // id
	SellerID     int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                                                                                      // 运营商
	ChannelID    int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                                                                                                     // 渠道
	UserID       int32     `gorm:"column:UserId;comment: 玩家Id" json:"UserId"`                                                                                                        //  玩家Id
	State        int32     `gorm:"column:State;default:1;comment:状态 1待审核,2审核拒绝,3审核通过,4自动通过" json:"State"`                                                                            // 状态 1待审核,2审核拒绝,3审核通过,4自动通过
	ActiveID     int32     `gorm:"column:ActiveId;comment:活动Id 为了兼容新活动 新活动对应的固定ID 10001能量补给站 10002充值任务 10003哈希闯关 10004棋牌闯关 10005电子闯关 10006救援金 10007邀请好友 10008VIP返水" json:"ActiveId"` // 活动Id 为了兼容新活动 新活动对应的固定ID 10001能量补给站 10002充值任务 10003哈希闯关 10004棋牌闯关 10005电子闯关 10006救援金 10007邀请好友 10008VIP返水
	ActiveName   string    `gorm:"column:ActiveName;comment:活动名称" json:"ActiveName"`                                                                                                 // 活动名称
	Amount       float64   `gorm:"column:Amount;comment:活动送金" json:"Amount"`                                                                                                         // 活动送金
	AuditAccount string    `gorm:"column:AuditAccount;comment:审核账号" json:"AuditAccount"`                                                                                             // 审核账号
	AuditTime    time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                                                                                   // 审核时间
	AuditMemo    string    `gorm:"column:AuditMemo;comment:审核备注" json:"AuditMemo"`                                                                                                   // 审核备注
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:申请时间" json:"CreateTime"`                                                                       // 申请时间
	TopAgentID   int32     `gorm:"column:TopAgentId;comment:顶级代理Id" json:"TopAgentId"`                                                                                               // 顶级代理Id
}

// TableName XActiveReward's table name
func (*XActiveReward) TableName() string {
	return TableNameXActiveReward
}
