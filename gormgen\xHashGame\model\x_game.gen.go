// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXGame = "x_game"

// XGame mapped from table <x_game>
type XGame struct {
	ID                 int32   `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID           int32   `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                               // 运营商
	GameID             int32   `gorm:"column:GameId;comment:游戏id" json:"GameId"`                                  // 游戏id
	RoomLevel          int32   `gorm:"column:RoomLevel;comment:房间等级" json:"RoomLevel"`                            // 房间等级
	ChannelID          int32   `gorm:"column:ChannelId;default:1;comment:渠道商" json:"ChannelId"`                   // 渠道商
	GameName           string  `gorm:"column:GameName;comment:游戏名称" json:"GameName"`                              // 游戏名称
	RoomName           string  `gorm:"column:RoomName;comment:房间名称" json:"RoomName"`                              // 房间名称
	Address            string  `gorm:"column:Address;comment:投注地址" json:"Address"`                                // 投注地址
	FeeRate            float64 `gorm:"column:FeeRate;comment:中奖手续费率" json:"FeeRate"`                              // 中奖手续费率
	RewardRate         float64 `gorm:"column:RewardRate;comment:游戏赔率" json:"RewardRate"`                          // 游戏赔率
	RewardRateEx       string  `gorm:"column:RewardRateEx;comment:特殊赔率" json:"RewardRateEx"`                      // 特殊赔率
	UsdtLimitMin       float64 `gorm:"column:UsdtLimitMin;comment:最小下注usdt" json:"UsdtLimitMin"`                  // 最小下注usdt
	UsdtLimitMax       float64 `gorm:"column:UsdtLimitMax;comment:最大下注usdt" json:"UsdtLimitMax"`                  // 最大下注usdt
	TrxLimitMin        float64 `gorm:"column:TrxLimitMin;comment:最少下注trx" json:"TrxLimitMin"`                     // 最少下注trx
	TrxLimitMax        float64 `gorm:"column:TrxLimitMax;comment:最大下注trx" json:"TrxLimitMax"`                     // 最大下注trx
	BackFeeRate        float64 `gorm:"column:BackFeeRate;comment:返还费率" json:"BackFeeRate"`                        // 返还费率
	AgentRate          float64 `gorm:"column:AgentRate;comment:分成比例" json:"AgentRate"`                            // 分成比例
	RewardDownRole     string  `gorm:"column:RewardDownRole;comment:降赔规则" json:"RewardDownRole"`                  // 降赔规则
	StopRewardDownRole string  `gorm:"column:StopRewardDownRole;comment:停止降赔规则" json:"StopRewardDownRole"`        // 停止降赔规则
	State              int32   `gorm:"column:State;comment:状态 1启用 2禁用" json:"State"`                              // 状态 1启用 2禁用
	LiuSuiType         int32   `gorm:"column:LiuSuiType;default:3;comment:流水算法 1单边 2双边 3输赢绝对值" json:"LiuSuiType"` // 流水算法 1单边 2双边 3输赢绝对值
	FenChengRate       float64 `gorm:"column:FenChengRate;comment:代理分成比例" json:"FenChengRate"`                    // 代理分成比例
	AmountTrx          float64 `gorm:"column:AmountTrx;default:0.000000" json:"AmountTrx"`
	AmountUsdt         float64 `gorm:"column:AmountUsdt;default:0.000000" json:"AmountUsdt"`
	Chip               string  `gorm:"column:Chip" json:"Chip"`
	TiYan              int32   `gorm:"column:TiYan;default:2;comment:是否开启体验" json:"TiYan"` // 是否开启体验
	Lottery            string  `gorm:"column:Lottery" json:"Lottery"`
	SUsdtLimitMin      float64 `gorm:"column:SUsdtLimitMin;default:0.000000" json:"SUsdtLimitMin"`
	SUsdtLimitMax      float64 `gorm:"column:SUsdtLimitMax;default:0.000000" json:"SUsdtLimitMax"`
	STrxLimitMin       float64 `gorm:"column:STrxLimitMin;default:0.000000" json:"STrxLimitMin"`
	STrxLimitMax       float64 `gorm:"column:STrxLimitMax;default:0.000000" json:"STrxLimitMax"`
	PTrxLimitMin       float64 `gorm:"column:PTrxLimitMin;default:0.000000" json:"PTrxLimitMin"`
	PTrxLimitMax       float64 `gorm:"column:PTrxLimitMax;default:0.000000" json:"PTrxLimitMax"`
	PUsdtLimitMax      float64 `gorm:"column:PUsdtLimitMax;default:0.000000" json:"PUsdtLimitMax"`
	PUsdtLimitMin      float64 `gorm:"column:PUsdtLimitMin;default:0.000000" json:"PUsdtLimitMin"`
	IsVipGame          int32   `gorm:"column:IsVipGame;default:1;comment:时候参加vip流水累计 1参加,2不参加" json:"IsVipGame"`       // 时候参加vip流水累计 1参加,2不参加
	UsdtLimitMinHe     float64 `gorm:"column:UsdtLimitMinHe;default:0.000000;comment:和最小usdt下注" json:"UsdtLimitMinHe"` // 和最小usdt下注
	UsdtLimitMaxHe     float64 `gorm:"column:UsdtLimitMaxHe;comment:和最大usdt下注" json:"UsdtLimitMaxHe"`                  // 和最大usdt下注
	TrxLimitMinHe      float64 `gorm:"column:TrxLimitMinHe;comment:和最小usdt下注" json:"TrxLimitMinHe"`                    // 和最小usdt下注
	TrxLimitMaxHe      float64 `gorm:"column:TrxLimitMaxHe;comment:和最大trx下注" json:"TrxLimitMaxHe"`                     // 和最大trx下注
	TopAgentID         int32   `gorm:"column:TopAgentId" json:"TopAgentId"`
	AgentName          string  `gorm:"column:AgentName" json:"AgentName"`
	IsRecom            int32   `gorm:"column:IsRecom;default:2;comment:是否推荐" json:"IsRecom"`              // 是否推荐
	IsNew              int32   `gorm:"column:IsNew;default:2;comment:是否最新" json:"IsNew"`                  // 是否最新
	IsHot              int32   `gorm:"column:IsHot;default:2;comment:是否热门" json:"IsHot"`                  // 是否热门
	OnlineMin          int32   `gorm:"column:OnlineMin;comment:最小在玩人数" json:"OnlineMin"`                  // 最小在玩人数
	OnlineMax          int32   `gorm:"column:OnlineMax;comment:最大在玩人数" json:"OnlineMax"`                  // 最大在玩人数
	BscAddress         string  `gorm:"column:BscAddress;comment:bsc转账地址" json:"BscAddress"`               // bsc转账地址
	UsdtBscLimitMax    float64 `gorm:"column:UsdtBscLimitMax;comment:bsc最大下注usdt" json:"UsdtBscLimitMax"` // bsc最大下注usdt
	UsdtBscLimitMin    float64 `gorm:"column:UsdtBscLimitMin;comment:最小下注usdt" json:"UsdtBscLimitMin"`    // 最小下注usdt
	UsdtEthLimitMax    float64 `gorm:"column:UsdtEthLimitMax;comment:eth最大下注usdt" json:"UsdtEthLimitMax"` // eth最大下注usdt
	UsdtEthLimitMin    float64 `gorm:"column:UsdtEthLimitMin;comment:eth最小下注usdt" json:"UsdtEthLimitMin"` // eth最小下注usdt
}

// TableName XGame's table name
func (*XGame) TableName() string {
	return TableNameXGame
}
