// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTbBankerUser(db *gorm.DB, opts ...gen.DOOption) xTbBankerUser {
	_xTbBankerUser := xTbBankerUser{}

	_xTbBankerUser.xTbBankerUserDo.UseDB(db, opts...)
	_xTbBankerUser.xTbBankerUserDo.UseModel(&model.XTbBankerUser{})

	tableName := _xTbBankerUser.xTbBankerUserDo.TableName()
	_xTbBankerUser.ALL = field.NewAsterisk(tableName)
	_xTbBankerUser.ID = field.NewInt64(tableName, "Id")
	_xTbBankerUser.UserID = field.NewInt32(tableName, "UserId")
	_xTbBankerUser.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTbBankerUser.SellerID = field.NewInt32(tableName, "SellerId")
	_xTbBankerUser.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xTbBankerUser.AgentID = field.NewInt32(tableName, "AgentId")
	_xTbBankerUser.BankerMinTime = field.NewInt32(tableName, "BankerMinTime")
	_xTbBankerUser.BankerMaxTime = field.NewInt32(tableName, "BankerMaxTime")
	_xTbBankerUser.BankerMinAmount = field.NewFloat64(tableName, "BankerMinAmount")
	_xTbBankerUser.BankerTgGroup = field.NewString(tableName, "BankerTgGroup")
	_xTbBankerUser.BankerTime = field.NewInt32(tableName, "BankerTime")
	_xTbBankerUser.BankerStartTime = field.NewTime(tableName, "BankerStartTime")
	_xTbBankerUser.BankerEndTime = field.NewTime(tableName, "BankerEndTime")
	_xTbBankerUser.BankerAmount = field.NewFloat64(tableName, "BankerAmount")
	_xTbBankerUser.BankerStatus = field.NewInt32(tableName, "BankerStatus")
	_xTbBankerUser.Memo = field.NewString(tableName, "Memo")
	_xTbBankerUser.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTbBankerUser.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTbBankerUser.fillFieldMap()

	return _xTbBankerUser
}

// xTbBankerUser 上庄玩家记录
type xTbBankerUser struct {
	xTbBankerUserDo xTbBankerUserDo

	ALL             field.Asterisk
	ID              field.Int64
	UserID          field.Int32   // 用户id
	ChannelID       field.Int32   // 渠道id
	SellerID        field.Int32   // 运营商id
	TopAgentID      field.Int32   // 顶级代理
	AgentID         field.Int32   // 代理id
	BankerMinTime   field.Int32   // 上庄最小时间(单位：秒)
	BankerMaxTime   field.Int32   // 上庄最大时间(单位：秒)
	BankerMinAmount field.Float64 // 上庄最小金额
	BankerTgGroup   field.String  // 上庄tg群
	BankerTime      field.Int32   // 上庄时间(单位：秒)
	BankerStartTime field.Time    // 上庄开始时间
	BankerEndTime   field.Time    // 上庄结束时间
	BankerAmount    field.Float64 // 上庄余额
	BankerStatus    field.Int32   // 上庄状态 0已下庄 1上庄排队中 2正在上庄 3取消上庄
	Memo            field.String  // 描述
	CreateTime      field.Time    // 创建时间
	UpdateTime      field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTbBankerUser) Table(newTableName string) *xTbBankerUser {
	x.xTbBankerUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTbBankerUser) As(alias string) *xTbBankerUser {
	x.xTbBankerUserDo.DO = *(x.xTbBankerUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTbBankerUser) updateTableName(table string) *xTbBankerUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.BankerMinTime = field.NewInt32(table, "BankerMinTime")
	x.BankerMaxTime = field.NewInt32(table, "BankerMaxTime")
	x.BankerMinAmount = field.NewFloat64(table, "BankerMinAmount")
	x.BankerTgGroup = field.NewString(table, "BankerTgGroup")
	x.BankerTime = field.NewInt32(table, "BankerTime")
	x.BankerStartTime = field.NewTime(table, "BankerStartTime")
	x.BankerEndTime = field.NewTime(table, "BankerEndTime")
	x.BankerAmount = field.NewFloat64(table, "BankerAmount")
	x.BankerStatus = field.NewInt32(table, "BankerStatus")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTbBankerUser) WithContext(ctx context.Context) *xTbBankerUserDo {
	return x.xTbBankerUserDo.WithContext(ctx)
}

func (x xTbBankerUser) TableName() string { return x.xTbBankerUserDo.TableName() }

func (x xTbBankerUser) Alias() string { return x.xTbBankerUserDo.Alias() }

func (x xTbBankerUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xTbBankerUserDo.Columns(cols...)
}

func (x *xTbBankerUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTbBankerUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 18)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["BankerMinTime"] = x.BankerMinTime
	x.fieldMap["BankerMaxTime"] = x.BankerMaxTime
	x.fieldMap["BankerMinAmount"] = x.BankerMinAmount
	x.fieldMap["BankerTgGroup"] = x.BankerTgGroup
	x.fieldMap["BankerTime"] = x.BankerTime
	x.fieldMap["BankerStartTime"] = x.BankerStartTime
	x.fieldMap["BankerEndTime"] = x.BankerEndTime
	x.fieldMap["BankerAmount"] = x.BankerAmount
	x.fieldMap["BankerStatus"] = x.BankerStatus
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTbBankerUser) clone(db *gorm.DB) xTbBankerUser {
	x.xTbBankerUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTbBankerUser) replaceDB(db *gorm.DB) xTbBankerUser {
	x.xTbBankerUserDo.ReplaceDB(db)
	return x
}

type xTbBankerUserDo struct{ gen.DO }

func (x xTbBankerUserDo) Debug() *xTbBankerUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xTbBankerUserDo) WithContext(ctx context.Context) *xTbBankerUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTbBankerUserDo) ReadDB() *xTbBankerUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTbBankerUserDo) WriteDB() *xTbBankerUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTbBankerUserDo) Session(config *gorm.Session) *xTbBankerUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTbBankerUserDo) Clauses(conds ...clause.Expression) *xTbBankerUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTbBankerUserDo) Returning(value interface{}, columns ...string) *xTbBankerUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTbBankerUserDo) Not(conds ...gen.Condition) *xTbBankerUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTbBankerUserDo) Or(conds ...gen.Condition) *xTbBankerUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTbBankerUserDo) Select(conds ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTbBankerUserDo) Where(conds ...gen.Condition) *xTbBankerUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTbBankerUserDo) Order(conds ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTbBankerUserDo) Distinct(cols ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTbBankerUserDo) Omit(cols ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTbBankerUserDo) Join(table schema.Tabler, on ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTbBankerUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTbBankerUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTbBankerUserDo) Group(cols ...field.Expr) *xTbBankerUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTbBankerUserDo) Having(conds ...gen.Condition) *xTbBankerUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTbBankerUserDo) Limit(limit int) *xTbBankerUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTbBankerUserDo) Offset(offset int) *xTbBankerUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTbBankerUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTbBankerUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTbBankerUserDo) Unscoped() *xTbBankerUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTbBankerUserDo) Create(values ...*model.XTbBankerUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTbBankerUserDo) CreateInBatches(values []*model.XTbBankerUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTbBankerUserDo) Save(values ...*model.XTbBankerUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTbBankerUserDo) First() (*model.XTbBankerUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerUser), nil
	}
}

func (x xTbBankerUserDo) Take() (*model.XTbBankerUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerUser), nil
	}
}

func (x xTbBankerUserDo) Last() (*model.XTbBankerUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerUser), nil
	}
}

func (x xTbBankerUserDo) Find() ([]*model.XTbBankerUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTbBankerUser), err
}

func (x xTbBankerUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTbBankerUser, err error) {
	buf := make([]*model.XTbBankerUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTbBankerUserDo) FindInBatches(result *[]*model.XTbBankerUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTbBankerUserDo) Attrs(attrs ...field.AssignExpr) *xTbBankerUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTbBankerUserDo) Assign(attrs ...field.AssignExpr) *xTbBankerUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTbBankerUserDo) Joins(fields ...field.RelationField) *xTbBankerUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTbBankerUserDo) Preload(fields ...field.RelationField) *xTbBankerUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTbBankerUserDo) FirstOrInit() (*model.XTbBankerUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerUser), nil
	}
}

func (x xTbBankerUserDo) FirstOrCreate() (*model.XTbBankerUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerUser), nil
	}
}

func (x xTbBankerUserDo) FindByPage(offset int, limit int) (result []*model.XTbBankerUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTbBankerUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTbBankerUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTbBankerUserDo) Delete(models ...*model.XTbBankerUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTbBankerUserDo) withDO(do gen.Dao) *xTbBankerUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
