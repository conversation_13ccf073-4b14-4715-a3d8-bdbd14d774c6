package _type

type AgType struct {
	// PlayType 游戏下注类型
	PlayType map[string]interface{} `json:"play_type"`
}

// NewAgType NewAg 实例化
func NewAgType() *AgType {
	// PlayType 游戏下注类型
	var PlayType = map[string]interface{}{
		"BAC": map[string]string{
			"1":    "庄",
			"2":    "闲",
			"3":    "和",
			"4":    "庄对",
			"5":    "闲对",
			"6":    "大",
			"7":    "小",
			"11":   "庄(免佣)",
			"12":   "庄龙宝",
			"13":   "闲龙宝",
			"14":   "超级六",
			"15":   "任意对子",
			"16":   "完美对子",
			"17":   "庄例牌",
			"18":   "闲例牌",
			"19":   "幸运六",
			"30":   "超和0",
			"31":   "超和1",
			"32":   "超和2",
			"33":   "超和3",
			"34":   "超和4",
			"35":   "超和5",
			"36":   "超和6",
			"37":   "超和7",
			"38":   "超和8",
			"39":   "超和9",
			"9000": "Jackpot和大奖(原：完美8)",
			"9001": "Jackpot和小奖(原：幸运8)",
			"9002": "Jackpot对子大奖(原：运财8)",
			"9003": "Jackpot对子小奖(原：如意8)",
			"9004": "Jackpot时时奖(原：密密8)",
			"9006": "Jackpot免佣庄大奖",
			"9007": "Jackpot免佣庄小奖",
			"9008": "Jackpot幸运六大奖",
			"9009": "Jackpot幸运六小奖",
		},
		"DT": map[string]string{
			"21":  "龙",
			"22":  "虎",
			"23":  "和",
			"130": "龙单",
			"131": "虎单",
			"132": "龙双",
			"133": "虎双",
			"134": "龙红",
			"135": "虎红",
			"136": "龙黑",
			"137": "虎黑",
		},
		"SHB": map[string]string{
			"41": "大",
			"42": "小",
			"43": "单",
			"44": "双",
			"45": "全围",
			"46": "围1",
			"47": "围2",
			"48": "围3",
			"49": "围4",
			"50": "围5",
			"51": "围6",
			"52": "单点1",
			"53": "单点2",
			"54": "单点3",
			"55": "单点4",
			"56": "单点5",
			"57": "单点6",
			"58": "对子1",
			"59": "对子2",
			"60": "对子3",
			"61": "对子4",
			"62": "对子5",
			"63": "对子6",
			"64": "组合12",
			"65": "组合13",
			"66": "组合14",
			"67": "组合15",
			"68": "组合16",
			"69": "组合23",
			"70": "组合24",
			"71": "组合25",
			"72": "组合26",
			"73": "组合34",
			"74": "组合35",
			"75": "组合36",
			"76": "组合45",
			"77": "组合46",
			"78": "组合56",
			"79": "和值4",
			"80": "和值5",
			"81": "和值6",
			"82": "和值7",
			"83": "和值8",
			"84": "和值9",
			"85": "和值10",
			"86": "和值11",
			"87": "和值12",
			"88": "和值13",
			"89": "和值14",
			"90": "和值15",
			"91": "和值16",
			"92": "和值17",
		},
		"ROU": map[string]string{
			"101": "直接注",
			"102": "分注",
			"103": "街注",
			"104": "三数",
			"105": "4个号码",
			"106": "角注",
			"107": "列注(列1)",
			"108": "列注(列2)",
			"109": "列注(列3)",
			"110": "线注",
			"111": "打一",
			"112": "打二",
			"113": "打三",
			"114": "红",
			"115": "黑",
			"116": "大",
			"117": "小",
			"118": "单",
			"119": "双",
		},
		"NN": map[string]string{
			"211": "闲1平倍",
			"212": "闲1翻倍",
			"213": "闲2平倍",
			"214": "闲2翻倍",
			"215": "闲3平倍",
			"216": "闲3翻倍",
			"207": "庄1平倍",
			"208": "庄1翻倍",
			"209": "庄2平倍",
			"210": "庄2翻倍",
			"217": "庄3平倍",
			"218": "庄3翻倍",
		},
		"ZJH": map[string]string{
			"260": "龙",
			"261": "凤",
			"262": "对8以上",
			"263": "同花",
			"264": "顺子",
			"265": "豹子",
			"266": "同花顺",
		},
		"BJ": map[string]string{
			"220": "底注",
			"221": "分牌",
			"222": "保险",
			"223": "分牌保险",
			"224": "加注",
			"225": "分牌加注",
			"226": "完美对子",
			"227": "21+3",
			"228": "旁注",
			"229": "旁注分牌",
			"230": "旁注保险",
			"231": "旁注分牌保险",
			"232": "旁注加注",
			"233": "旁注分牌加注",
		},
		"SG": map[string]string{
			"320": "庄赢闲1",
			"321": "闲1赢",
			"322": "闲1和",
			"323": "庄赢闲2",
			"324": "闲2赢",
			"325": "闲2和",
			"326": "庄赢闲3",
			"327": "闲3赢",
			"328": "闲3和",
			"329": "庄对牌以上",
			"330": "闲1对牌以上",
			"331": "闲1三公",
			"332": "闲2对牌以上",
			"333": "闲2三公",
			"334": "闲3对牌以上",
			"335": "闲3三公",
		},
		"BF": map[string]string{
			"270": "黑牛",
			"271": "红牛",
			"272": "和",
			"273": "牛一",
			"274": "牛二",
			"275": "牛三",
			"276": "牛四",
			"277": "牛五",
			"278": "牛六",
			"279": "牛七",
			"280": "牛八",
			"281": "牛九",
			"282": "牛牛",
			"283": "双牛牛",
			"284": "银牛/金牛/炸弹/五小牛",
		},
	}

	return &AgType{
		PlayType: PlayType,
	}

}
