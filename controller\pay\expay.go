package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

// Expay 支付控制器实例
var Expay = new(expay)

// expay 支付控制器结构体
type expay struct {
	Base
}

// ExPayCallback ExPay回调请求结构体
type ExPayCallback struct {
	ClientTransactionID string  `json:"client_transaction_id"` // 客户端交易ID
	TrackerID           string  `json:"tracker_id"`            // 追踪ID
	Status              string  `json:"status"`                // 交易状态
	Amount              float64 `json:"amount"`                // 交易金额
}

// ExPayTransactionInfo ExPay交易信息响应结构体
type ExPayTransactionInfo struct {
	Description string `json:"description"` // 描述信息
	Status      string `json:"status"`      // 响应状态
	Transaction struct {
		AlterAmount         float64   `json:"alter_amount"`          // 转换后金额
		Amount              int       `json:"amount"`                // 原始金额
		CallbackURL         string    `json:"callback_url"`          // 回调URL
		ClientTransactionID string    `json:"client_transaction_id"` // 客户端交易ID
		DateCreate          time.Time `json:"date_create"`           // 创建时间
		DateUpdate          time.Time `json:"date_update"`           // 更新时间
		ExtraInfo           struct {
			RecipientName string `json:"recipient_name"` // 收款人姓名
		} `json:"extra_info"` // 额外信息
		IsRecalc              bool    `json:"is_recalc"`              // 是否重新计算
		PaymentSystem         string  `json:"payment_system"`         // 支付系统
		Rate                  float64 `json:"rate"`                   // 汇率
		Receiver              string  `json:"receiver"`               // 收款账号
		Status                string  `json:"status"`                 // 交易状态
		Token                 string  `json:"token"`                  // 支付令牌
		TokenMajorName        string  `json:"token_major_name"`       // 主要代币名称
		Total                 float64 `json:"total"`                  // 总金额
		TrackerID             string  `json:"tracker_id"`             // 追踪ID
		TransactionCommission float64 `json:"transaction_commission"` // 交易手续费
		Type                  string  `json:"type"`                   // 交易类型
	} `json:"transaction"` // 交易信息
}

// 定义轮询配置常量
const (
	PollInterval = 5 // 轮询间隔（秒）
)

// Init 初始化ExPay支付控制器，设置回调接口
func (c *expay) Init() {
	server.Http().PostNoAuth("/api/expay/recharge/callback", c.rechargeCallback) // 充值回调接口
	server.Http().PostNoAuth("/api/expay/withdraw/callback", c.withdrawCallback) // 提现回调接口
}

// Recharge 创建充值订单
// @param ctx HTTP上下文
// @param req 创建订单请求
// @param specialAgent 特殊代理标识
func (c *expay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 解析支付配置
	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		logs.Error("expay: 未找到用户", err.Error())
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getWithdrawRate(req.Symbol) // 获取汇率
		if err != nil {
			logs.Error("expay: 获取汇率失败", err.Error())
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	// 开始数据库事务
	tx := server.DaoxHashGame().Begin()
	// 创建充值订单
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      21, // ExPay的支付类型ID
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3, // 初始状态
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		// c.PushDepositCustomerIO(4, user, rechargeOrder, payMethod)
		ctx.RespErr(errors.New("创建充值订单失败"), &errcode)
		logs.Error("expay: 创建充值订单失败", err.Error())
		return
	}

	// 准备ExPay API的参数
	params := xgo.H{
		"refer_type":            payMethod.PayType,                                           // 支付类型
		"token":                 cfg["token"],                                                // 支付令牌，例如 "CARDRUBP2P"
		"sub_token":             cfg["sub_token"],                                            // 子支付令牌，例如 "SBERRUB"
		"amount":                req.Amount,                                                  // 支付金额
		"client_transaction_id": fmt.Sprintf("%d_%d", rechargeOrder.ID, user.UserID),         // 客户端交易ID
		"client_merchant_id":    fmt.Sprintf("%d", token.SellerId),                           // 商户ID
		"fingerprint":           fmt.Sprintf("user_%d", user.UserID),                         // 用户指纹标识
		"call_back_url":         fmt.Sprintf("%s/api/expay/recharge/callback", cfg["cburl"]), // 支付回调地址
	}

	// 准备请求体
	jsonBody, _ := json.Marshal(params)

	// 生成时间戳
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// 生成签名
	signature := c.generateExPaySignature(timestamp, string(jsonBody), cfg["private_key"].(string))

	// 发送请求
	resp, err := c.post(cfg["url"].(string)+"/api/transaction/create/in", map[string]string{
		"Content-Type": "application/json",
		"ApiPublic":    cfg["public_key"].(string),
		"Signature":    signature,
		"Timestamp":    timestamp,
	}, jsonBody)

	if err != nil {
		tx.Rollback()
		// c.PushDepositCustomerIO(4, user, rechargeOrder, payMethod)
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		logs.Error("expay: 发送支付请求失败", err.Error())
		return
	}

	// ExPay响应结构体
	type ExPayResponse struct {
		AlterAmount float64 `json:"alter_amount,omitempty"`
		AlterRefer  string  `json:"alter_refer,omitempty"`
		Rate        float64 `json:"rate,omitempty"`
		Refer       string  `json:"refer"`
		Status      string  `json:"status"`
		TokenName   string  `json:"token_name"`
		TrackerID   string  `json:"tracker_id"`
		Description string  `json:"description"`
	}

	var response ExPayResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("解析ExPay响应失败"), &errcode)
		logs.Error("expay: 解析ExPay响应失败", err.Error())
		return
	}

	// 更新第三方ID
	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
		Updates(map[string]interface{}{
			"ThirdId": response.TrackerID,
		})
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("更新第三方ID失败"), &errcode)
		logs.Error("expay: 更新第三方ID失败", err.Error())
		return
	}

	if response.Status != "ACCEPTED" {
		tx.Rollback()
		ctx.RespErr(errors.New(response.Description), &errcode)
		logs.Error("expay: 创建订单失败", response.Description)
		return
	}

	tx.Commit()
	ctx.RespOK(xgo.H{
		"payurl": response.AlterRefer,
	})
}

// generateExPaySignature 生成ExPay签名
// @param timestamp 时间戳
// @param body 请求体
// @param privateKey 私钥
// @return 签名字符串
func (c *expay) generateExPaySignature(timestamp string, body string, privateKey string) string {
	mac := hmac.New(sha512.New, []byte(privateKey))
	mac.Write([]byte(timestamp + body))
	return hex.EncodeToString(mac.Sum(nil))
}

// getTransactionInfo 获取ExPay交易信息
// @param trackerId 交易追踪ID
// @param cfg 支付配置
// @return 交易信息和错误信息
func (c *expay) getTransactionInfo(trackerId string, cfg map[string]any) (*ExPayTransactionInfo, error) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// 准备请求体
	reqBody, _ := json.Marshal(map[string]string{
		"tracker_id": trackerId,
	})

	// 生成签名
	signature := c.generateExPaySignature(timestamp, string(reqBody), cfg["private_key"].(string))

	// 发送请求获取交易信息
	resp, err := c.post(fmt.Sprintf("%s/api/transaction/get", cfg["url"]), map[string]string{
		"Content-Type": "application/json",
		"ApiPublic":    cfg["public_key"].(string),
		"Signature":    signature,
		"Timestamp":    timestamp,
	}, reqBody)
	if err != nil {
		logs.Error("expay: 获取交易信息失败", err.Error())
		return nil, fmt.Errorf("获取交易信息失败: %v", err)
	}

	// 记录请求响应
	logData := xgo.H{
		"type":        "transaction_get_response",
		"tracker_id":  trackerId,
		"status_code": resp.StatusCode(),
		"response":    string(resp.Body()),
		"timestamp":   time.Now().Unix(),
	}
	logJson, _ := json.Marshal(logData)
	logs.Info("expay: 获取交易信息", string(logJson))

	// 检查响应状态
	if resp.StatusCode() == 404 {
		logs.Error("expay: 交易不存在")
		return nil, fmt.Errorf("交易不存在")
	}

	// 解析响应数据
	var transInfo ExPayTransactionInfo
	if err := json.Unmarshal(resp.Body(), &transInfo); err != nil {
		logs.Error("expay: 解析交易信息失败", err.Error())
		return nil, fmt.Errorf("解析交易信息失败: %v", err)
	}

	return &transInfo, nil
}

// pollTransactionStatus 轮询交易状态直到成功或失败
// @param trackerId 交易追踪ID
// @param cfg 支付配置
// @return 交易信息和错误信息
func (c *expay) pollTransactionStatus(trackerId string, cfg map[string]any) (*ExPayTransactionInfo, error) {
	attempts := 0
	for {
		transInfo, err := c.getTransactionInfo(trackerId, cfg)
		if err != nil {
			logs.Error("expay: 轮询交易状态失败", err.Error())
			return nil, err
		}

		// 记录轮询日志
		logData := xgo.H{
			"type":       "transaction_poll",
			"tracker_id": trackerId,
			"attempt":    attempts + 1,
			"status":     transInfo.Transaction.Status,
			"timestamp":  time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Info("expay: 轮询交易状态", string(logJson))

		// 如果交易已完成或失败，返回结果
		if transInfo.Transaction.Status == "SUCCESS" || transInfo.Transaction.Status == "ERROR" {
			return transInfo, nil
		}

		attempts++
		time.Sleep(time.Duration(PollInterval) * time.Second)
	}
}

// rechargeCallback 处理充值回调
// @param ctx HTTP上下文
func (c *expay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 1. 读取并验证回调数据
	callbackData, err := c.parseAndValidateCallback(ctx)
	if err != nil {
		logs.Error("expay: 回调数据验证失败:", err)
		ctx.Gin().String(400, err.Error())
		return
	}

	// 2. 获取并验证订单信息
	order, err := c.validateRechargeOrder(callbackData.ClientTransactionID)
	if err != nil {
		logs.Error("expay: 订单验证失败:", err)
		ctx.Gin().String(400, err.Error())
		return
	}

	// 3. 获取支付配置
	cfg, err := c.getPayConfig(int(order.PayID))
	if err != nil {
		logs.Error("expay: 获取支付配置失败:", err)
		ctx.Gin().String(400, err.Error())
		return
	}

	// 4. 轮询交易状态直到成功或失败
	transInfo, err := c.pollTransactionStatus(callbackData.TrackerID, cfg)
	if err != nil {
		logs.Error("expay: 获取交易状态失败:", err)
		ctx.Gin().String(400, err.Error())
		return
	}

	// 5. 处理最终交易状态
	switch transInfo.Transaction.Status {
	case "SUCCESS":
		// 处理充值订单
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

		// 记录成功日志
		logData := xgo.H{
			"type":       "recharge_success",
			"order_id":   order.ID,
			"user_id":    order.UserID,
			"amount":     transInfo.Transaction.Amount,
			"tracker_id": callbackData.TrackerID,
			"timestamp":  time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Info("expay: 充值成功", string(logJson))
		ctx.Gin().String(200, "success")

	case "ERROR":
		// 处理充值失败订单
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7) // 状态7表示充值失败

		// 记录失败日志
		logData := xgo.H{
			"type":       "recharge_error",
			"order_id":   order.ID,
			"user_id":    order.UserID,
			"tracker_id": callbackData.TrackerID,
			"timestamp":  time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Error("expay: 充值失败", string(logJson))
		ctx.Gin().String(200, "交易已取消")

	default:
		// 记录未知状态
		logData := xgo.H{
			"type":       "recharge_unknown_status",
			"order_id":   order.ID,
			"tracker_id": callbackData.TrackerID,
			"status":     transInfo.Transaction.Status,
			"timestamp":  time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Error("expay: 未知交易状态", string(logJson))
		ctx.Gin().String(400, "未知交易状态")
	}
}

// parseAndValidateCallback 解析并验证回调数据
func (c *expay) parseAndValidateCallback(ctx *abugo.AbuHttpContent) (*ExPayCallback, error) {
	body, err := ctx.Gin().GetRawData()
	if err != nil {
		return nil, fmt.Errorf("读取请求失败")
	}

	// 记录原始数据
	logData := xgo.H{
		"type":      "callback_raw_data",
		"data":      string(body),
		"timestamp": time.Now().Unix(),
	}
	logJson, _ := json.Marshal(logData)
	logs.Info("expay: 收到回调数据", string(logJson))

	var callback ExPayCallback
	if err := json.Unmarshal(body, &callback); err != nil {
		logs.Error("expay: 解析回调数据失败", err.Error())
		return nil, fmt.Errorf("解析回调数据失败")
	}

	if callback.ClientTransactionID == "" || callback.TrackerID == "" {
		logs.Error("expay: 回调数据缺少必要字段")
		return nil, fmt.Errorf("回调数据缺少必要字段")
	}

	return &callback, nil
}

// validateRechargeOrder 验证充值订单
func (c *expay) validateRechargeOrder(clientTransactionID string) (*model.XRecharge, error) {
	// 分割字符串，获取订单ID部分
	parts := strings.Split(clientTransactionID, "_")
	if len(parts) < 1 {
		logs.Error("expay: 无效的订单ID格式")
		return nil, fmt.Errorf("无效的订单ID格式")
	}

	orderId, err := strconv.Atoi(parts[0])
	if err != nil {
		logs.Error("expay: 无效的订单ID", err.Error())
		return nil, fmt.Errorf("无效的订单ID")
	}

	order, err := c.getRechargeOrder(orderId)
	if err != nil {
		logs.Error("expay: 充值订单不存在", err.Error())
		return nil, fmt.Errorf("充值订单不存在")
	}

	return order, nil
}

// getPayConfig 获取支付配置
func (c *expay) getPayConfig(payID int) (map[string]any, error) {
	payMethod, err := c.getPayMethod(payID)
	if err != nil {
		logs.Error("expay: 无效的支付方式", err.Error())
		return nil, fmt.Errorf("无效的支付方式")
	}

	var cfg map[string]any
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("expay: 无效的支付配置", err.Error())
		return nil, fmt.Errorf("无效的支付配置")
	}

	return cfg, nil
}

// withdrawCallback 处理提现回调
// @param ctx HTTP上下文
func (c *expay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	body, err := ctx.Gin().GetRawData()
	if err != nil {
		logs.Error("expay: 失败读取请求体:", err)
		ctx.Gin().String(400, "失败读取请求")
		return
	}
	logs.Info("expay: 提现回调原始数据:", string(body))

	var callback ExPayCallback
	if err := json.Unmarshal(body, &callback); err != nil {
		logs.Error("expay: 失败解析回调数据:", err)
		ctx.Gin().String(400, "失败解析回调数据")
		return
	}

	// 检查必填字段
	if callback.ClientTransactionID == "" || callback.TrackerID == "" {
		logs.Error("expay: 回调数据缺少必要字段")
		ctx.Gin().String(400, "回调数据缺少必要字段")
		return
	}

	// 分割字符串，获取订单ID部分
	parts := strings.Split(callback.ClientTransactionID, "_")
	if len(parts) < 1 {
		logs.Error("expay: 无效的订单ID格式")
		ctx.Gin().String(400, "无效的订单ID格式")
		return
	}

	orderId, err := strconv.Atoi(parts[0])
	if err != nil {
		logs.Error("expay: 失败解析订单ID:", err)
		ctx.Gin().String(400, "无效的 client_transaction_id")
		return
	}

	order, err := c.getWithdrawOrder(orderId)
	if err != nil {
		logs.Error("expay: 提现订单不存在:", err)
		ctx.Gin().String(400, "提现订单不存在")
		return
	}

	// 检查订单状态
	if order.State == 6 || order.State == 7 {
		logs.Info("expay: 提现订单已处理:", orderId)
		return
	}

	// 获取ExPay配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("expay: 无效的配置:", err)
		ctx.Gin().String(400, "无效的配置")
		return
	}

	var cfg map[string]any
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("expay: 无效的配置:", err)
		ctx.Gin().String(400, "无效的配置")
		return
	}

	// 轮询交易状态直到成功或失败
	transInfo, err := c.pollTransactionStatus(callback.TrackerID, cfg)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("expay: 获取交易状态失败:", err)
		ctx.Gin().String(400, err.Error())
		return
	}

	// 根据交易信息更新订单状态
	var newState int
	switch transInfo.Transaction.Status {
	case "SUCCESS":
		newState = 6 // 成功提现
	case "ERROR":
		newState = 7 // 失败提现
	default:
		// 记录未知状态
		logData := xgo.H{
			"type":       "withdraw_unknown_status",
			"order_id":   order.ID,
			"tracker_id": callback.TrackerID,
			"status":     transInfo.Transaction.Status,
			"timestamp":  time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Error("expay: 未知交易状态", string(logJson))
		ctx.Gin().String(400, "未知交易状态")
		return
	}

	// 检查金额
	if newState == 6 && math.Abs(order.RealAmount-float64(transInfo.Transaction.Amount)) > 0.01 {
		logData := xgo.H{
			"type":            "withdraw_amount_error",
			"order_id":        order.ID,
			"tracker_id":      callback.TrackerID,
			"expected_amount": order.RealAmount,
			"actual_amount":   transInfo.Transaction.Amount,
			"timestamp":       time.Now().Unix(),
		}
		logJson, _ := json.Marshal(logData)
		logs.Error("expay: 金额不匹配", string(logJson))
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 更新订单状态
	c.withdrawCallbackHandel(int(order.ID), newState)

	// 记录成功日志
	logData := xgo.H{
		"type":       "withdraw_status_updated",
		"order_id":   order.ID,
		"tracker_id": callback.TrackerID,
		"new_state":  newState,
		"timestamp":  time.Now().Unix(),
	}
	logJson, _ := json.Marshal(logData)

	if newState == 6 {
		logs.Info("expay: 更新提现状态成功", string(logJson))
		ctx.Gin().String(200, "success")
	} else {
		logs.Error("expay: 更新提现状态失败", string(logJson))
		ctx.Gin().String(200, "交易已取消")
	}
}
