// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTransationUt(db *gorm.DB, opts ...gen.DOOption) xTransationUt {
	_xTransationUt := xTransationUt{}

	_xTransationUt.xTransationUtDo.UseDB(db, opts...)
	_xTransationUt.xTransationUtDo.UseModel(&model.XTransationUt{})

	tableName := _xTransationUt.xTransationUtDo.TableName()
	_xTransationUt.ALL = field.NewAsterisk(tableName)
	_xTransationUt.ID = field.NewInt64(tableName, "Id")
	_xTransationUt.UserID = field.NewInt32(tableName, "UserId")
	_xTransationUt.TxID = field.NewString(tableName, "TxId")
	_xTransationUt.FromAddress = field.NewString(tableName, "FromAddress")
	_xTransationUt.ToAddress = field.NewString(tableName, "ToAddress")
	_xTransationUt.Amount = field.NewFloat64(tableName, "Amount")
	_xTransationUt.Symbol = field.NewString(tableName, "Symbol")
	_xTransationUt.ReqTime = field.NewTime(tableName, "ReqTime")
	_xTransationUt.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTransationUt.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTransationUt.fillFieldMap()

	return _xTransationUt
}

type xTransationUt struct {
	xTransationUtDo xTransationUtDo

	ALL         field.Asterisk
	ID          field.Int64
	UserID      field.Int32
	TxID        field.String
	FromAddress field.String
	ToAddress   field.String
	Amount      field.Float64
	Symbol      field.String
	ReqTime     field.Time
	CreateTime  field.Time
	UpdateTime  field.Time

	fieldMap map[string]field.Expr
}

func (x xTransationUt) Table(newTableName string) *xTransationUt {
	x.xTransationUtDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTransationUt) As(alias string) *xTransationUt {
	x.xTransationUtDo.DO = *(x.xTransationUtDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTransationUt) updateTableName(table string) *xTransationUt {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.TxID = field.NewString(table, "TxId")
	x.FromAddress = field.NewString(table, "FromAddress")
	x.ToAddress = field.NewString(table, "ToAddress")
	x.Amount = field.NewFloat64(table, "Amount")
	x.Symbol = field.NewString(table, "Symbol")
	x.ReqTime = field.NewTime(table, "ReqTime")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTransationUt) WithContext(ctx context.Context) *xTransationUtDo {
	return x.xTransationUtDo.WithContext(ctx)
}

func (x xTransationUt) TableName() string { return x.xTransationUtDo.TableName() }

func (x xTransationUt) Alias() string { return x.xTransationUtDo.Alias() }

func (x xTransationUt) Columns(cols ...field.Expr) gen.Columns {
	return x.xTransationUtDo.Columns(cols...)
}

func (x *xTransationUt) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTransationUt) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 10)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["TxId"] = x.TxID
	x.fieldMap["FromAddress"] = x.FromAddress
	x.fieldMap["ToAddress"] = x.ToAddress
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["ReqTime"] = x.ReqTime
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTransationUt) clone(db *gorm.DB) xTransationUt {
	x.xTransationUtDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTransationUt) replaceDB(db *gorm.DB) xTransationUt {
	x.xTransationUtDo.ReplaceDB(db)
	return x
}

type xTransationUtDo struct{ gen.DO }

func (x xTransationUtDo) Debug() *xTransationUtDo {
	return x.withDO(x.DO.Debug())
}

func (x xTransationUtDo) WithContext(ctx context.Context) *xTransationUtDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTransationUtDo) ReadDB() *xTransationUtDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTransationUtDo) WriteDB() *xTransationUtDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTransationUtDo) Session(config *gorm.Session) *xTransationUtDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTransationUtDo) Clauses(conds ...clause.Expression) *xTransationUtDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTransationUtDo) Returning(value interface{}, columns ...string) *xTransationUtDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTransationUtDo) Not(conds ...gen.Condition) *xTransationUtDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTransationUtDo) Or(conds ...gen.Condition) *xTransationUtDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTransationUtDo) Select(conds ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTransationUtDo) Where(conds ...gen.Condition) *xTransationUtDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTransationUtDo) Order(conds ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTransationUtDo) Distinct(cols ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTransationUtDo) Omit(cols ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTransationUtDo) Join(table schema.Tabler, on ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTransationUtDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTransationUtDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTransationUtDo) Group(cols ...field.Expr) *xTransationUtDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTransationUtDo) Having(conds ...gen.Condition) *xTransationUtDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTransationUtDo) Limit(limit int) *xTransationUtDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTransationUtDo) Offset(offset int) *xTransationUtDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTransationUtDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTransationUtDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTransationUtDo) Unscoped() *xTransationUtDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTransationUtDo) Create(values ...*model.XTransationUt) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTransationUtDo) CreateInBatches(values []*model.XTransationUt, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTransationUtDo) Save(values ...*model.XTransationUt) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTransationUtDo) First() (*model.XTransationUt, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTransationUt), nil
	}
}

func (x xTransationUtDo) Take() (*model.XTransationUt, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTransationUt), nil
	}
}

func (x xTransationUtDo) Last() (*model.XTransationUt, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTransationUt), nil
	}
}

func (x xTransationUtDo) Find() ([]*model.XTransationUt, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTransationUt), err
}

func (x xTransationUtDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTransationUt, err error) {
	buf := make([]*model.XTransationUt, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTransationUtDo) FindInBatches(result *[]*model.XTransationUt, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTransationUtDo) Attrs(attrs ...field.AssignExpr) *xTransationUtDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTransationUtDo) Assign(attrs ...field.AssignExpr) *xTransationUtDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTransationUtDo) Joins(fields ...field.RelationField) *xTransationUtDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTransationUtDo) Preload(fields ...field.RelationField) *xTransationUtDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTransationUtDo) FirstOrInit() (*model.XTransationUt, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTransationUt), nil
	}
}

func (x xTransationUtDo) FirstOrCreate() (*model.XTransationUt, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTransationUt), nil
	}
}

func (x xTransationUtDo) FindByPage(offset int, limit int) (result []*model.XTransationUt, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTransationUtDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTransationUtDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTransationUtDo) Delete(models ...*model.XTransationUt) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTransationUtDo) withDO(do gen.Dao) *xTransationUtDo {
	x.DO = *do.(*gen.DO)
	return x
}
