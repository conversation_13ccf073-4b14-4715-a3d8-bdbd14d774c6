package single

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
)

// RSG电子游戏单一钱包类
// RSG电子游戏接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

// RSGSingleService RSG电子游戏API客户端
type RSGSingleService struct {
	apiUrl                string          // api基础接口
	clientId              string          // Client ID
	clientSecret          string          // Client Secret
	desKey                string          // DES-CBC加密的Key
	desIV                 string          // DES-CBC加密的偏移量
	systemCode            string          // systemCode商户号
	webId                 string          // webId商户号
	currency              string          // 币种
	brandName             string          // 厂商标识
	games                 map[int]string  // 游戏类型
	gameURL               string          // PC端游戏URL
	mobileGameURL         string          // 移动端游戏URL
	Debug                 bool            // 日志调试模式
	RefreshUserAmountFunc func(int) error // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

// NewRSGSingleService 初始化RSG电子游戏单一钱包
func NewRSGSingleService(params map[string]string, fc func(int) error) *RSGSingleService {
	//游戏类型
	games := map[int]string{
		1:    "Fortune Thai",
		2:    "Magic Gem",
		3:    "Royal 777",
		4:    "Love City",
		5:    "Gold Chicken",
		6:    "Pharaoh",
		7:    "Alibaba",
		8:    "Lucky Fruits",
		10:   "Jungle",
		11:   "Captain Hook",
		12:   "HUCA",
		14:   "Sweet Candy",
		15:   "Fire Spin",
		16:   "Popeye",
		17:   "Crazy Doctor",
		18:   "Nonstop",
		19:   "5 Dragons",
		21:   "72 Changes",
		23:   "Mermaid",
		24:   "Buffalo",
		25:   "Wild Panda",
		26:   "Lucky Thailand",
		27:   "God of Wealth",
		28:   "Lucky Dragon",
		29:   "HUSA",
		30:   "Dragon King",
		31:   "TiKi Party",
		32:   "Goblin Miner",
		33:   "Lucky Bar",
		34:   "Africa",
		35:   "Wizard Store",
		36:   "Mr.Doggy",
		37:   "Disco Night",
		38:   "Horror Nights",
		39:   "China Empress",
		40:   "FuWaFaFa",
		41:   "Tarzan",
		42:   "Jalapeno",
		43:   "Piggy Punch",
		44:   "Sevens High",
		45:   "Kunoichi",
		46:   "Ninja",
		47:   "Jelly 27",
		48:   "Angry Bear",
		49:   "Poseidon",
		50:   "Dancing Lion",
		51:   "Medusa",
		52:   "Medea",
		53:   "Neon Circle",
		55:   "Get High",
		56:   "Cowboy",
		58:   "The Little Match Girl",
		59:   "Mystery Panda",
		60:   "Hip Hop Monkey",
		61:   "Book of Gold",
		65:   "Tai Chi",
		66:   "Golden Leaf Clover",
		68:   "Wizard Store Gold",
		70:   "Rat's Money",
		72:   "Songkran",
		73:   "Elf Archer",
		76:   "Bear Kingdom",
		78:   "Royal 7777",
		81:   "Dragon King2",
		82:   "Pharaoh II",
		90:   "Dragon Fight",
		100:  "Roma",
		113:  "Chin Shi Huang",
		121:  "Legend of Lu Bu",
		123:  "Jurassic Treasure",
		2001: "Energy Combo",
		3001: "Ocean Emperor",
		3002: "FuWa Fishing",
		5001: "Crown 5PK",
	}

	return &RSGSingleService{
		apiUrl:                params["api_url"],       // API地址
		clientId:              params["client_id"],     // Client ID
		clientSecret:          params["client_secret"], // Client Secret
		desKey:                params["des_key"],       // DES-CBC加密的Key
		desIV:                 params["des_iv"],        // DES-CBC加密的偏移量
		systemCode:            params["system_code"],   // 系统代码
		webId:                 params["web_id"],        // 站台代码
		currency:              params["currency"],      // 币种
		brandName:             "rsg",                   // 厂商标识
		games:                 games,
		gameURL:               params["game_url"],        // PC端游戏URL
		mobileGameURL:         params["mobile_game_url"], // 移动端游戏URL
		Debug:                 false,                     // 是否调试模式
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyRSG = "cacheKeyRSG:"

// RSG返回错误码
const (
	RSG_Code_Success               = 0    // 成功
	RSG_Code_Fail                  = 1001 // 执行失败
	RSG_Code_System_Busy           = 1002 // 系统维护中
	RSG_Code_Illegal_Args          = 2001 // 无效的参数
	RSG_Code_Invalid_Decrypt       = 2002 // 解密失败
	RSG_Code_Player_Not_Exist      = 4001 // 此玩家帐户不存在
	RSG_Code_Duplicate_Seq         = 4002 // 重复的SequenNumber
	RSG_Code_Balance_Not_Enough    = 4003 // 余额不足
	RSG_Code_Seq_Not_Exist         = 4004 // 此SequenNumber不存在
	RSG_Code_Seq_Settled           = 4005 // 此SequenNumber已被结算
	RSG_Code_Seq_Cancelled         = 4006 // 此SequenNumber已被取消
	RSG_Code_Duplicate_TransId     = 4007 // 重复的TransactionId
	RSG_Code_Deny_Prepay           = 4008 // 拒绝预扣，其他原因
	RSG_Code_Transaction_Not_Found = 4009 // 找不到交易结果
)

// 计算签名字符串
func (l *RSGSingleService) getRequestSign(timestamp string, encryptedMsg string) (sign string) {
	// 先对encryptedMsg进行URL解码
	decoded, err := url.QueryUnescape(encryptedMsg)
	if err != nil {
		logs.Error("RSG 签名计算 URL解码失败 err=", err.Error())
		// 如果解码失败，使用原始字符串
		decoded = encryptedMsg
	}

	// 处理空格问题，将空格替换回+号
	// 在URL解码过程中，+号可能被错误地转换为空格
	decoded = strings.ReplaceAll(decoded, " ", "+")

	// 按照文档要求的顺序进行拼接
	src := l.clientId + l.clientSecret + timestamp + decoded

	// 输出调试信息
	//logs.Debug("RSG 签名计算 原始字符串=", src)

	// 计算MD5哈希
	hash := md5.New()
	hash.Write([]byte(src))
	sign = hex.EncodeToString(hash.Sum(nil))

	// 输出调试信息
	//logs.Debug("RSG 签名计算 生成的签名=", sign)

	return
}

// 验证签名字符串
func (l *RSGSingleService) verifyRequestSign(timestamp string, encryptedMsg string, signature string) bool {
	expectedSign := l.getRequestSign(timestamp, encryptedMsg)
	return strings.EqualFold(expectedSign, signature)
}

// 数据DES-CBC加密函数
func (l *RSGSingleService) EncryptData(plaintext string) (string, error) {
	block, err := des.NewCipher([]byte(l.desKey))
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS5填充
	paddedData := l.PKCS5Padding([]byte(plaintext), block.BlockSize())

	// 创建CBC加密模式
	mode := cipher.NewCBCEncrypter(block, []byte(l.desIV))

	// 加密数据
	ciphertext := make([]byte, len(paddedData))
	mode.CryptBlocks(ciphertext, paddedData)

	// 将加密后的数据转换为Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 数据DES-CBC解密函数
func (l *RSGSingleService) DecryptData(ciphertext string) (string, error) {
	// 先进行URL解码
	urlDecoded, err := url.QueryUnescape(ciphertext)
	if err != nil {
		logs.Warning("RSG 解密数据 URL解码失败，使用原始字符串 err=", err.Error())
		urlDecoded = ciphertext
	}

	// 处理空格问题，将空格替换回+号
	// 在URL解码过程中，+号可能被错误地转换为空格
	urlDecoded = strings.ReplaceAll(urlDecoded, " ", "+")

	// 输出调试信息
	//logs.Debug("RSG 解密数据 处理空格后=", urlDecoded)

	// 将Base64编码的密文解码
	decodedCiphertext, err := base64.StdEncoding.DecodeString(urlDecoded)
	if err != nil {
		return "", err
	}

	// 确保DES密钥是8字节长
	desKey := []byte(l.desKey)
	if len(desKey) != 8 {
		logs.Error("RSG 解密数据 DES密钥长度不正确，应为8字节，实际为", len(desKey), " 字节")
		// 如果密钥长度不足，填充到8字节
		if len(desKey) < 8 {
			padded := make([]byte, 8)
			copy(padded, desKey)
			desKey = padded
		} else {
			// 如果密钥长度超过8字节，只取前8字节
			desKey = desKey[:8]
		}
	}

	// 确保IV是8字节长
	desIV := []byte(l.desIV)
	if len(desIV) != 8 {
		logs.Error("RSG 解密数据 DES IV长度不正确，应为8字节，实际为", len(desIV), " 字节")
		// 如果IV长度不足，填充到8字节
		if len(desIV) < 8 {
			padded := make([]byte, 8)
			copy(padded, desIV)
			desIV = padded
		} else {
			// 如果IV长度超过8字节，只取前8字节
			desIV = desIV[:8]
		}
	}

	// 输出调试信息
	//logs.Debug("RSG 解密数据 密钥长度=", len(desKey), " IV长度=", len(desIV))

	block, err := des.NewCipher(desKey)
	if err != nil {
		return "", err
	}

	// 创建CBC解密模式
	mode := cipher.NewCBCDecrypter(block, desIV)

	// 解密数据
	plaintext := make([]byte, len(decodedCiphertext))
	mode.CryptBlocks(plaintext, decodedCiphertext)

	// 去除PKCS5填充
	unpaddedData := l.PKCS5Unpadding(plaintext)
	if unpaddedData == nil {
		return "", errors.New("PKCS5 unpadding failed")
	}

	return string(unpaddedData), nil
}

// PKCS5填充
func (l *RSGSingleService) PKCS5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// 去除PKCS5填充
func (l *RSGSingleService) PKCS5Unpadding(src []byte) []byte {
	length := len(src)
	if length == 0 {
		return nil
	}

	unpadding := int(src[length-1])
	if unpadding > length {
		return nil
	}

	return src[:(length - unpadding)]
}

// CreateSignature 创建API签名
func (l *RSGSingleService) CreateSignature(timestamp string, encryptedMsg string) string {
	return l.getRequestSign(timestamp, encryptedMsg)
}

// VerifySignature 验证API签名
func (l *RSGSingleService) VerifySignature(timestamp string, encryptedMsg string, signature string) bool {
	return l.verifyRequestSign(timestamp, encryptedMsg, signature)
}

// ProcessEncryptedRequest 处理表单格式的加密请求
// 验证请求头，解析表单数据，验证签名，解密数据
// 返回: 解密后的字节数组, 原始请求体, 时间戳, 签名, 错误信息
func (l *RSGSingleService) ProcessEncryptedRequest(ctx *abugo.AbuHttpContent, logPrefix string) ([]byte, []byte, string, string, error) {
	// 获取请求头
	clientID := ctx.Gin().GetHeader("X-API-ClientID")
	signature := ctx.Gin().GetHeader("X-API-Signature")
	timestamp := ctx.Gin().GetHeader("X-API-Timestamp")

	// 验证请求头
	if clientID == "" || signature == "" || timestamp == "" {
		logs.Error(logPrefix, " 请求头缺失 clientID=", clientID, " signature=", signature, " timestamp=", timestamp)
		return nil, nil, "", "", errors.New("请求头缺失")
	}

	// 验证ClientID
	if !strings.EqualFold(clientID, l.clientId) {
		logs.Error(logPrefix, " ClientID不匹配 clientID=", clientID, " l.clientId=", l.clientId)
		return nil, nil, "", "", errors.New("ClientID不匹配")
	}

	// 获取请求体
	reqBody, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error(logPrefix, " 读取请求体失败 err=", err.Error())
		return nil, nil, "", "", errors.New("读取请求体失败")
	}

	// 解析表单数据
	reqBodyStr := string(reqBody)
	if l.Debug {
		logs.Info(logPrefix, " 原始请求体 reqBody=", reqBodyStr)
	}

	// 检查请求体格式
	if !strings.HasPrefix(reqBodyStr, "Msg=") {
		logs.Error(logPrefix, " 请求体格式错误，缺少Msg= reqBody=", reqBodyStr)
		return nil, nil, "", "", errors.New("请求体格式错误，缺少Msg=")
	}

	// 提取加密数据
	encryptedMsg := reqBodyStr[4:]
	if encryptedMsg == "" {
		logs.Error(logPrefix, " 加密数据为空")
		return nil, nil, "", "", errors.New("加密数据为空")
	}

	// 验证签名
	if !l.verifyRequestSign(timestamp, encryptedMsg, signature) {
		logs.Error(logPrefix, " 签名验证失败 timestamp=", timestamp, " encryptedMsg=", encryptedMsg, " signature=", signature)
		return nil, nil, "", "", errors.New("签名验证失败")
	}

	// 解密数据
	decryptedStr, err := l.DecryptData(encryptedMsg)
	if err != nil {
		logs.Error(logPrefix, " 解密数据失败 err=", err.Error())
		return nil, nil, "", "", errors.New("解密数据失败")
	}

	//if l.Debug {
	logs.Info(logPrefix, " 解密后的数据 decryptedStr=", decryptedStr)
	//}

	return []byte(decryptedStr), reqBody, timestamp, signature, nil
}

// ProcessEncryptedResponse 处理表单格式的加密响应
// 加密响应数据，生成签名，返回响应
// 返回: 错误信息
func (l *RSGSingleService) ProcessEncryptedResponse(ctx *abugo.AbuHttpContent, respdata interface{}, logPrefix string) error {
	// 将响应数据转换为JSON
	jsonData, err := json.Marshal(respdata)
	if err != nil {
		logs.Error(logPrefix, " 响应数据转JSON失败 err=", err.Error())
		return err
	}

	if l.Debug {
		logs.Info(logPrefix, " 响应JSON数据 jsonData=", string(jsonData))
	}

	// 加密响应数据
	encryptedData, err := l.EncryptData(string(jsonData))
	if err != nil {
		logs.Error(logPrefix, " 加密响应数据失败 err=", err.Error())
		return err
	}

	// 返回响应
	// 使用RespOK而不是RespJson，确保加密后的字符串直接返回，而不是作为JSON字符串返回
	//ctx.RespOK(encryptedData)
	// 修改为输出普通字符
	ctx.Gin().String(http.StatusOK, "%s", encryptedData)

	logs.Info(logPrefix, " 响应成功 response=", string(jsonData))
	return nil
}

// Balance 获取玩家余额 API URL GetBalance
func (l *RSGSingleService) Balance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode string `json:"SystemCode"` // 系统代码，必填
		WebId      string `json:"WebId"`      // 站台代码，必填
		UserId     string `json:"UserId"`     // 会员惟一识别码，必填
		Currency   string `json:"Currency"`   // 币别代码，必填
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance interface{} `json:"Balance"` // 会员当下余额，保留两位小数
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 获取余额")
	if err != nil {
		logs.Error("RSG_single 获取余额 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 获取余额 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 获取余额 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 获取余额 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 获取余额 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("RSG_single 获取余额 获取用户余额失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.ErrorCode = RSG_Code_Player_Not_Exist
			respdata.ErrorMessage = "此玩家帐户不存在"
		} else {
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
		}
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}

	// 设置响应数据
	// 确保余额格式正确，保留两位小数
	// 根据API文档，Balance应为小数点两位，范围0.00~9999999999.99
	// 使用math.Floor保留两位小数
	balance := math.Floor(userBalance.Amount*100) / 100

	// 使用json.Number类型，确保在JSON中不带双引号，并保留两位小数
	// 使用strconv.FormatFloat而不fmt.Sprintf，避免导入fmt包
	formattedBalance := strconv.FormatFloat(balance, 'f', 2, 64)
	respdata.Data.Balance = json.Number(formattedBalance)

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
	if err != nil {
		logs.Error("RSG_single 获取余额 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 获取余额")
		return
	}
}
