package third

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

type UpDownService struct {
	url                   string
	companyKey            string
	appUrl                string
	brandName             string
	name                  string
	key                   string
	agent                 string
	currency              string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

// OrderQwei 新增字段 RefundAmount 反水金额 2024-10-12
type OrderUp struct {
	Id           int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `json:"BetChannelId" gorm:"column:BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
	RefundAmount float64 `json:"RefundAmount" gorm:"column:RefundAmount"`
}

func NewUpDownService(params map[string]string, fc func(int) error) *UpDownService {
	return &UpDownService{
		url:                   params["url"],
		appUrl:                params["appUrl"],
		name:                  params["name"],
		brandName:             "og",
		key:                   params["key"],
		companyKey:            params["companyKey"],
		agent:                 params["agent"],
		currency:              params["currency"],
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyUpdown = "cacheKeyUpdown:"

// 登录接口
func (l *UpDownService) Login(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	errcode := 0
	if err != nil {
		logs.Error("updown Login 读取消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败，系统错误")
		return
	}
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	logs.Info("updown Login bodyBytes=", string(bodyBytes))

	type RequestData struct {
		Lang     string `json:"Lang"`
		Avatar   string `json:"avatar"`
		GameId   int64  `json:"GameId"`
		Platform int    `json:"Platform"`
	}
	type ResponseData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			GameUrl string `json:"gameUrl"`
		} `json:"data"`
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("updown Login 反序列化参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败，参数错误")
		return
	}

	token := server.GetToken(ctx)
	userId := token.UserId

	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("IsTest").Where("UserId=?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("updown Login 查询用户信息错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败，系统错误")
		return
	}
	if userBalance.IsTest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	rkey := fmt.Sprintf("%v:%v:third_login_%v_updown", server.Project(), server.Module(), token.UserId)
	userName := fmt.Sprintf("%s_%d", l.agent, userId)
	lck := server.Redis().SetNxString(rkey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rkey)

	//loginToken := UserId2Token(token.UserId)
	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("updown Login 获取token失败 userId=", userId)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	r := map[string]interface{}{
		"account":      userName,
		"ip":           ctx.GetIp(),
		"agent":        l.agent,
		"token":        loginToken,
		"nickName":     token.Account,
		"languageType": reqdata.Lang,
		"companyKey":   l.companyKey,
		"avatar":       reqdata.Avatar,
		"timestamp":    fmt.Sprintf("%d", time.Now().UnixMilli()),
	}

	// 参数字典排序
	sortParam := utils.SortMapByKeys(r)
	sortJson, _ := json.Marshal(sortParam)
	apiSign := string(sortJson) + l.key

	hashedkey := utils.Md5V(apiSign)
	payload := strings.NewReader(string(sortJson))

	req, _ := http.NewRequest("POST", l.url+`/api/game/login`, payload)
	req.Header.Add("Authorization", hashedkey)
	req.Header.Add("content-type", "application/json")
	req.Header.Add("cache-control", "no-cache")
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := http.DefaultClient
	client.Transport = tr
	res, err := client.Do(req)
	if err != nil {
		logs.Error("updown Login 请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	defer res.Body.Close()
	respBytes, err := io.ReadAll(res.Body)
	if err != nil {
		logs.Error("updown Login 读取响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	logs.Info("updown Login 请求成功 respBytes=", string(respBytes))

	respdata := ResponseData{}
	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("updown Login 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	if respdata.Code != 1 {
		logs.Error("updown Login 登录失败 respdata=", respdata)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	ctx.Put("url", respdata.Data.GameUrl)
	ctx.RespOK()
}

// 获取玩家余额
func (l *UpDownService) GetBalance(ctx *abugo.AbuHttpContent) {
	// 测试接口耗时的代码 记得用完之后注释掉
	stime := time.Now().UnixMilli()
	defer func() {
		etime := time.Now().UnixMilli()
		logs.Info("updown GetBalance 耗时=", etime-stime, "ms")
	}()

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("updown GetBalance 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	// logs.Info("updown GetBalance bodyBytes=", string(bodyBytes))

	type RequestData struct {
		Token     string `json:"token"`
		AccountId string `json:"accountId"`
		Timestamp int64  `json:"timestamp"`
	}
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("updown GetBalance 反序列化参数错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}

	// token := server.GetToken(ctx)
	// userId := token.UserId
	// if userId <= 0 {
	// 	logs.Error("updown GetBalance 登录失效，重新登录")
	// 	ctx.Gin().JSON(200, gin.H{
	// 		"Code":      -1,
	// 		"timestamp": time.Now().Unix(),
	// 		"msg":       "登录失效，请重新登录",
	// 	})
	// 	return
	// }

	userId, _ := l.getUserByToken(reqdata.Token)
	if userId <= 0 {
		logs.Error("updown GetBalance 登录失效，重新登录")
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "登录失效，请重新登录",
		})
		return
	}
	if reqdata.AccountId != fmt.Sprintf("%s_%d", l.agent, userId) {
		logs.Error("updown GetBalance 用户信息错误 userId=", userId, " reqdata.AccountId=", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "用户信息错误",
		})
		return
	}

	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("updown GetBalance 查询用户信息错误 userId=", userId, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户错误",
		})
		return
	}

	ctx.Gin().JSON(200, gin.H{
		"Code":      0,
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": userBalance.Amount},
		"msg":       "",
	})
	return
}

// 下注
func (l *UpDownService) Bet(ctx *abugo.AbuHttpContent) {
	// 测试接口耗时的代码 记得用完之后注释掉
	stime := time.Now().UnixMilli()
	defer func() {
		etime := time.Now().UnixMilli()
		logs.Info("updown Bet 耗时=", etime-stime, "ms")
	}()

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("updown Bet 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	logs.Info("updown Bet bodyBytes=", string(bodyBytes))

	type RequestData struct {
		Token     string  `json:"token"`
		Money     float64 `json:"money"`
		GameId    int64   `json:"gameId"`
		OrderId   string  `json:"orderId"`
		AccountId string  `json:"accountId"`
		TimeStamp int64   `json:"timeStamp"`
		Rate      float64 `json:"rate"`
	}
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("updown Bet 反序列化参数错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}
	if reqdata.Money <= 0 {
		logs.Error("updown Bet 下注额小于等于0 thirdId=", "og_updown_"+reqdata.OrderId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "下注额小于等于0",
		})
		return
	}
	// 手续费不能小于0 小于0等于给用户送钱
	if reqdata.Rate < 0 {
		reqdata.Rate = 0
	}

	// userId := Token2UserId(reqdata.Token)
	// if userId <= 0 {
	// 	logs.Error("updown Bet 登录失效，重新登录 thirdId=", "og_updown_"+reqdata.OrderId)
	// 	ctx.Gin().JSON(200, gin.H{
	// 		"Code":      -1,
	// 		"timestamp": time.Now().Unix(),
	// 		"msg":       "登录失效，请重新登录",
	// 	})
	// 	return
	// }
	userId, _ := l.getUserByToken(reqdata.Token)
	if userId <= 0 {
		logs.Error("updown Bet 登录失效，重新登录", " reqdata.AccountId=", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "登录失效，请重新登录",
		})
		return
	}
	if reqdata.AccountId != fmt.Sprintf("%s_%d", l.agent, userId) {
		logs.Error("updown Bet 用户信息错误 userId=", userId, " reqdata.AccountId=", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "用户信息错误",
		})
		return
	}

	// 下注金额 = 传过来的钱 - 手续费；扣除手续费是我方平台收取 所以账变金额要比下注金额多一个手续费
	betAmount := reqdata.Money - reqdata.Rate
	tablepre := "x_third_quwei_pre_order"
	thirdId := "og_updown_" + reqdata.OrderId
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	gameId := fmt.Sprintf("%d", reqdata.GameId)

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		//查询订单是否存在
		betTran := thirdGameModel.ThirdOrder{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e == nil {
			logs.Error("updown Bet 订单已存在 thirdId=", thirdId)
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "订单已存在",
			})
			return errors.New("订单已存在")
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("updown Bet 查询订单错误 thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "查询订单错误",
			})
			return e
		}

		userBalance := thirdGameModel.UserBalance{}
		e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("updown Bet 查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额失败",
			})
			return e
		}

		if userBalance.Amount < reqdata.Money {
			logs.Error("updown Bet 余额不足 userId=", userId, " thirdId=", thirdId, " userBalance.Amount=", userBalance.Amount)
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "余额不足",
			})
			return errors.New("余额不足")
		}

		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

		resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, reqdata.Money).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount-?", reqdata.Money),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("updown Bet 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "更新用户余额失败",
			})
			return e
		}

		order := thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gameId,
			GameName:     l.name,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			DataState:    -1,
			Fee:          reqdata.Rate,
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("updown Bet 创建订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "创建订单失败",
			})
			return e
		}

		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       0 - reqdata.Money,
			AfterAmount:  userBalance.Amount - reqdata.Money,
			Reason:       utils.BalanceCReasonUpDownGameBet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("updown Bet 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "创建账变失败",
			})
			return e
		}

		ctx.Gin().JSON(200, gin.H{
			"Code":      0,
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"balance":   userBalance.Amount - reqdata.Money,
				"money":     reqdata.Money,
				"accountId": reqdata.AccountId,
			},
			"msg": "",
		})
		logs.Info("updown Bet 下注成功 thirdId=", thirdId)
		return nil
	})

	// 推送投注事件
	l.thirdGamePush.PushBetEvent(userId, l.name, l.brandName, betAmount, l.currency)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("updown Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("updown Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (l *UpDownService) End(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("updown End 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	logs.Info("updown End bodyBytes=", string(bodyBytes))

	type RequestData struct {
		Token        string  `json:"token"`
		LockMoney    float64 `json:"lockMoneyFloat"`
		LockMoneyStr string  `json:"lockMoney"`
		Money        float64 `json:"moneyFloat"`
		MoneyStr     string  `json:"money"`
		GameId       int64   `json:"gameId"`
		OrderId      string  `json:"orderIdStr"`
		OrderIdInt   int     `json:"orderId"`
		AccountId    string  `json:"accountId"`
		RoundId      string  `json:"roundId"`
		TimeStamp    int64   `json:"timeStamp"`
		Odds         float64 `json:"odds"`         // 赔率 updown是按照赢钱除以本金的百分比 比如1.5倍赔率 传的赔率是50
		Select       string  `json:"select"`       // 下注内容 up 或者 down
		Result       string  `json:"result"`       // 开奖结果 up-起拍价-结束价 或者 down-起拍价-结束价
		RefundAmount float64 `json:"refundAmount"` //反水金额 用户中将金额=反水金额+中将金额 反水金额不能大于投注金额 反水金额在三方后台设置
		// Info         struct {
		// 	Period          string  `json:"period"`          // 期数
		// 	Result          string  `json:"result"`          // 开奖结果
		// 	PriceStart      string  `json:"priceStart"`      // 起拍价
		// 	PriceEnd        string  `json:"priceEnd"`        // 结束价
		// 	Num             int64   `json:"num"`             // 真人下注总人数
		// 	NumUp           int64   `json:"numUp"`           // 真人买涨总人数
		// 	NumDown         int64   `json:"numDown"`         // 真人买跌总人数
		// 	BetNum          int64   `json:"betNum"`          // 真人下注总注单数
		// 	BetUpNum        int64   `json:"betUpNum"`        // 真人买涨总注单数
		// 	BetDownNum      int64   `json:"betDownNum"`      // 真人买跌总注单数
		// 	Amount          float64 `json:"amount"`          // 真人下注总金额
		// 	AmountUp        float64 `json:"amountUp"`        // 真人买涨总金额
		// 	AmountDown      float64 `json:"amountDown"`      // 真人买跌总金额
		// 	NumUpRobot      int64   `json:"numUpRobot"`      // 机器人买涨总人数
		// 	NumDownRobot    int64   `json:"numDownRobot"`    // 机器人买跌总人数
		// 	BetUpNumRobot   int64   `json:"betUpNumRobot"`   // 机器人买涨总注单数
		// 	BetDownNumRobot int64   `json:"betDownNumRobot"` // 机器人买跌总注单数
		// 	AmountUpRobot   float64 `json:"amountUpRobot"`   // 机器人买涨总金额
		// 	AmountDownRobot float64 `json:"amountDownRobot"` // 机器人买跌总金额
		// } `json:"info"` // 附加期数信息
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("updown End 反序列化参数错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       err.Error(),
		})
		return
	}
	if reqdata.MoneyStr != "" {
		floatValue, _ := strconv.ParseFloat(reqdata.MoneyStr, 64)
		reqdata.Money = floatValue
	}
	if reqdata.LockMoneyStr != "" {
		floatValue, _ := strconv.ParseFloat(reqdata.LockMoneyStr, 64)
		reqdata.LockMoney = floatValue
	}
	if reqdata.OrderIdInt > 0 {
		reqdata.OrderId = fmt.Sprintf("%v", reqdata.OrderIdInt)
	}
	if reqdata.Money < 0 {
		logs.Error("updown End 返奖金额小于0 thirdId=", "og_updown_"+reqdata.OrderId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "返奖金额小于0",
		})
		return
	}

	// userId := Token2UserId(reqdata.Token)
	// if userId <= 0 {
	// 	logs.Error("updown End 登录失效，重新登录 thirdId=", "og_updown_"+reqdata.OrderId)
	// 	ctx.Gin().JSON(200, gin.H{
	// 		"Code":      -1,
	// 		"timestamp": time.Now().Unix(),
	// 		"msg":       "登录失效，请重新登录",
	// 	})
	// 	return
	// }
	userId, _ := l.getUserByToken(reqdata.Token)
	if userId <= 0 {
		logs.Error("updown End 登录失效，重新登录", " reqdata.AccountId=", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "登录失效，请重新登录",
		})
		return
	}
	if reqdata.AccountId != fmt.Sprintf("%s_%d", l.agent, userId) {
		logs.Error("updown End 用户信息错误 userId=", userId, " reqdata.AccountId=", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"Code":      -1,
			"timestamp": time.Now().Unix(),
			"msg":       "用户信息错误",
		})
		return
	}

	//反水金额需求。解决方案1、后台界面增加字段反水金额 用户中将金额=反水金额+中将金额 反水金额不能大于投注金额
	//如果反水金额大于>0账变加反水备注
	refundAmount := reqdata.RefundAmount
	winAmount := reqdata.Money
	winAmount = winAmount + refundAmount //盈利加上反水金额
	tablepre := "x_third_quwei_pre_order"
	table := "x_third_quwei"
	thirdId := "og_updown_" + reqdata.OrderId
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	// 下注内容 开奖结果 json
	_betCtx := fmt.Sprintf("%s,赔率:%g%%", reqdata.Select, reqdata.Odds) // fmt.Sprintf("{\"下注\":\"%s\",\"赔率\":\"%g%%\"}", reqdata.Select, reqdata.Odds)
	_GameRst := ""
	if len(reqdata.Result) > 0 {
		_GameRst = strings.Split(reqdata.Result, "-")[0] // fmt.Sprintf("{\"开奖\":\"%s\"}", strings.Split(reqdata.Result, "-")[0])
	} else {
		_GameRst = reqdata.Result // fmt.Sprintf("{\"开奖\":\"%s\"}", reqdata.Result)
	}

	logs.Info("trading End third=", thirdId, "中将金额=", winAmount, "反水金额=", refundAmount)

	var order OrderUp // 声明一个变量用于之后使用
	//var betAmount float64 // 声明变量用于存储下注金额

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		//查询订单是否存在
		order = OrderUp{} //新增加了 refundAmount 反水金额字段
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			logs.Error("updown End 订单已存在 thirdId=", thirdId, " e=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				ctx.Gin().JSON(200, gin.H{
					"Code":      -1,
					"timestamp": time.Now().Unix(),
					"msg":       "订单不存在",
				})
			} else {
				ctx.Gin().JSON(200, gin.H{
					"Code":      -1,
					"timestamp": time.Now().Unix(),
					"msg":       "查询订单错误",
				})
			}
			return e
		}
		if order.DataState != -1 {
			logs.Error("updown End 订单已完成 thirdId=", thirdId, " order=", order)
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "订单已完成",
			})
			return errors.New("订单已完成")
		}

		//betAmount = order.BetAmount

		userBalance := thirdGameModel.UserBalance{}
		e = server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("updown End 查询用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额失败",
			})
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount+?", winAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("updown End 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "更新用户余额失败",
			})
			return e
		}

		validBet := math.Abs(winAmount - order.BetAmount) // 有效下注取输赢绝对值
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}
		if refundAmount > order.BetAmount {
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "反水金额不能大于投注金额",
			})
			e = errors.New("反水金额不能大于投注金额")
			return e
		}

		resultTmp = tx.Table(tablepre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":    winAmount,
			"ValidBet":     validBet,
			"RawData":      string(bodyBytes),
			"DataState":    1,
			"ThirdTime":    thirdTime,
			"BetCtx":       _betCtx,
			"GameRst":      _GameRst,
			"RefundAmount": refundAmount,
			"BetCtxType":   1,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("updown End 更新订单状态失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "更新订单状态失败",
			})
			return e
		}

		order.WinAmount = winAmount
		order.ValidBet = validBet
		order.RawData = string(bodyBytes)
		order.DataState = 1
		order.ThirdTime = thirdTime
		order.BetCtx = _betCtx
		order.GameRst = _GameRst
		order.BetCtxType = 1
		order.RefundAmount = refundAmount
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("updown End 创建正式订单失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "创建正式订单失败",
			})
			return e
		}

		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       winAmount,
			AfterAmount:  userBalance.Amount + winAmount,
			Reason:       utils.BalanceCReasonUpDownGameWin,
			Memo:         l.brandName + " settle,反水:" + fmt.Sprintf("%.2f", refundAmount) + ",thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("updown End 创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"Code":      -1,
				"timestamp": time.Now().Unix(),
				"msg":       "创建账变失败",
			})
			return e
		}

		ctx.Gin().JSON(200, gin.H{
			"Code":      0,
			"timestamp": time.Now().Unix(),
			"data":      gin.H{"Data": userBalance.Amount + winAmount},
			"msg":       "",
		})
		logs.Info("updown End 结算成功 thirdId=", thirdId)

		return nil
	})

	// 推送派奖事件
	l.thirdGamePush.PushRewardEvent(3, l.brandName, thirdId)

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("updown End 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("updown End 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (l *UpDownService) CBCDecrypt(ciphertext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	ciphercode, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return ""
	}

	iv := ciphercode[:aes.BlockSize]        // 密文的前 16 个字节为 iv
	ciphercode = ciphercode[aes.BlockSize:] // 正式密文

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphercode, ciphercode)

	plaintext := string(ciphercode) // ↓ 减去 padding
	return plaintext[:len(plaintext)-int(plaintext[len(plaintext)-1])]
}

func (l *UpDownService) CBCEncrypt(plaintext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	blockSize := len(key)
	padding := blockSize - len(plaintext)%blockSize // 填充字节
	if padding == 0 {
		padding = blockSize
	}

	// 填充 padding 个 byte(padding) 到 plaintext
	plaintext += string(bytes.Repeat([]byte{byte(padding)}, padding))
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err = rand.Read(iv); err != nil { // 将同时写到 ciphertext 的开头
		return ""
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], []byte(plaintext))

	return base64.StdEncoding.EncodeToString(ciphertext)
}

/*
func (l *UpDownService) UserId2Token(UserId int) string {
	plaintext, _ := json.Marshal(map[string]interface{}{
		"userId":    UserId,
		"timestamp": time.Now().UnixMicro(),
	})

	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"
	ciphertext := l.CBCEncrypt(string(plaintext), key)
	return url.QueryEscape(ciphertext)
}

func (l *UpDownService) Token2UserId(token string) int {
	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"

	payload := make(map[string]interface{})
	if err := json.Unmarshal([]byte(l.CBCDecrypt(token, key)), &payload); err != nil {
		logs.Debug("[ERR][UpDownService]Token2UserId:", token, err, payload)
		return -1
	}
	return int(payload["userId"].(float64))
}
*/

// 生成updown token
func (l *UpDownService) getTokenByUser(userId int, account string) (token string) {
	type UpdownTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyUpdown + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyUpdown + token
		err := server.Redis().Expire(rKeyToken, 86500)
		if err != nil {
			logs.Error("updown getTokenByUser set redis Expire rKeyToken=", rKeyToken, " error=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("updown getTokenByUser set redis Expire rKeyUser=", rKeyUser, " error=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "updown_" + uuid.NewString()
	tokendata := UpdownTokenData{
		UserId:  userId,
		Account: account,
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyUpdown + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("updown getTokenByUser set redis rKeyUser=", rKeyUser, " error:%s", err)
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86500); err != nil {
		logs.Error("updown getTokenByUser set redis rKeyToken=", rKeyToken, " error:%s", err)
		token = ""
		return
	}
	return
}

// 验证token
func (l *UpDownService) getUserByToken(token string) (userId int, account string) {
	type UpdownTokenData struct {
		UserId  int    `json:"user_id"`
		Account string `json:"account"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyUpdown + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := UpdownTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("updown getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " error=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("updown getUserIdBy token=", token, " 不存在")
	}
	return
}
