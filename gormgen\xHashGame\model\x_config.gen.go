// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXConfig = "x_config"

// XConfig mapped from table <x_config>
type XConfig struct {
	ID          int32  `gorm:"column:Id;not null" json:"Id"`
	SellerID    int32  `gorm:"column:SellerId;primaryKey;comment:运营商" json:"SellerId"`      // 运营商
	ChannelID   int32  `gorm:"column:ChannelId;primaryKey;comment:渠道商" json:"ChannelId"`    // 渠道商
	ConfigName  string `gorm:"column:ConfigName;primaryKey;comment:配置名称" json:"ConfigName"` // 配置名称
	ConfigValue string `gorm:"column:ConfigValue" json:"ConfigValue"`
	Remark      string `gorm:"column:Remark;comment:注释" json:"Remark"` // 注释
	IsShow      int32  `gorm:"column:IsShow;default:1" json:"IsShow"`
	EditAble    int32  `gorm:"column:EditAble;default:1" json:"EditAble"`
}

// TableName XConfig's table name
func (*XConfig) TableName() string {
	return TableNameXConfig
}
