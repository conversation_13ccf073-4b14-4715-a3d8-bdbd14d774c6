package paycontroller

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"
	"xserver/controller"
	"xserver/controller/customer"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"github.com/zhms/xgo/xgo"
	"golang.org/x/exp/rand"
)

type Base struct {
}

// GetLanguage 获取语言标签对应的本地化名称
// @description 根据输入的语言标签返回对应的本地化语言名称
// @param tag 语言标签，支持以下格式：
//   - 完整格式：如 "zh-CN"（中文简体）、"en-US"（美式英语）
//   - 简短格式：如 "zh"（中文）、"en"（英语）
//   - 国家/地区代码：如 "CN"（返回"zh-CN"）、"GB"（返回"en-GB"）
//
// @return 返回对应的本地化语言名称：
//   - 如果在映射表中找到完整格式的匹配，返回对应的本地化名称
//   - 如果找不到完整格式，会尝试匹配简短格式
//   - 如果都找不到匹配，则返回原始标签
func (c *Base) GetLanguage(tag string) string {
	// 国家/地区代码到语言标签的映射
	countryToLang := map[string]string{
		// 英语区域
		"SG": "en-SG",
		"GB": "en-GB",
		"US": "en-US",
		"AU": "en-AU",
		"CA": "en-CA", // 加拿大默认使用英语
		"IE": "en-IE",
		"NZ": "en-NZ",

		// 中文区域
		"CN": "zh-CN",
		"TW": "zh-TW",
		"HK": "zh-HK",

		// 法语区域
		"FR": "fr-FR",
		"BE": "fr-BE",

		// 德语区域
		"DE": "de-DE",
		"AT": "de-AT",
		"LI": "de-LI",
		"CH": "de-CH", // 瑞士默认使用德语

		// 意大利语区域
		"IT": "it-IT",

		// 西班牙语区域
		"ES": "es-ES",
		"MX": "es-US",

		// 葡萄牙语区域
		"PT": "pt-PT",
		"BR": "pt-BR",

		// 孟加拉语区域
		"BD": "bn-BD",

		// 克罗地亚语区域
		"HR": "hr-HR",

		// 捷克语区域
		"CZ": "cs-CZ",

		// 丹麦语区域
		"DK": "da-DK",

		// 希腊语区域
		"GR": "el-GR",

		// 希伯来语区域
		"IL": "he-IL", // 以色列默认使用希伯来语

		// 匈牙利语区域
		"HU": "hu-HU",

		// 印度尼西亚语区域
		"ID": "in-ID",

		// 日语区域
		"JP": "ja-JP",

		// 韩语区域
		"KR": "ko-KR",

		// 马来语区域
		"MY": "ms-MY",

		// 波斯语区域
		"IR": "fa-IR",

		// 波兰语区域
		"PL": "pl-PL",

		// 罗马尼亚语区域
		"RO": "ro-RO",

		// 俄语区域
		"RU": "ru-RU",

		// 塞尔维亚语区域
		"RS": "sr-RS",

		// 瑞典语区域
		"SE": "sv-SE",

		// 泰语区域
		"TH": "th-TH",

		// 土耳其语区域
		"TR": "tr-TR",

		// 乌尔都语区域
		"PK": "ur-PK",

		// 越南语区域
		"VN": "vi-VN",

		// 拉脱维亚语区域
		"LV": "lv-LV",

		// 立陶宛语区域
		"LT": "lt-LT",

		// 挪威语区域
		"NO": "nb-NO",

		// 斯洛伐克语区域
		"SK": "sk-SK",

		// 斯洛文尼亚语区域
		"SI": "sl-SI",

		// 保加利亚语区域
		"BG": "bg-BG",

		// 乌克兰语区域
		"UA": "uk-UA",

		// 菲律宾语区域
		"PH": "tl-PH",

		// 芬兰语区域
		"FI": "fi-FI",

		// 南非语区域
		"ZA": "af-ZA",

		// 缅甸语区域
		"MM": "my-MM",
		"ZG": "my-ZG",

		// 柬埔寨语区域
		"KH": "km-KH",

		// 阿姆哈拉语区域
		"ET": "am-ET",

		// 白俄罗斯语区域
		"BY": "be-BY",

		// 爱沙尼亚语区域
		"EE": "et-EE",

		// 斯瓦希里语区域
		"TZ": "sw-TZ",

		// 阿塞拜疆语区域
		"AZ": "az-AZ",

		// 亚美尼亚语区域
		"AM": "hy-AM",

		// 格鲁吉亚语区域
		"GE": "ka-GE",

		// 老挝语区域
		"LA": "lo-LA",

		// 蒙古语区域
		"MN": "mn-MN",

		// 尼泊尔语区域
		"NP": "ne-NP",

		// 哈萨克语区域
		"KZ": "kk-KZ",

		// 冰岛语区域
		"IS": "is-rIS",

		// 吉尔吉斯语区域
		"KG": "ky-rKG",

		// 马其顿语区域
		"MK": "mk-rMK",

		// 乌兹别克语区域
		"UZ": "uz-rUZ",

		// 僧伽罗语区域
		"LK": "si-LK",

		// 阿拉伯语区域
		"SA": "ar",
		"EG": "ar-EG",
		"AE": "ar",

		// 印度多语言（默认使用英语）
		"IN": "en-IN",
	}

	// 如果是国家/地区代码，直接返回对应的语言标签
	if langTag, exists := countryToLang[tag]; exists {
		return langTag
	}
	return ""
}

// PushCustomerIOAsync 推送客户数据到CustomerIO
// @param customerID 客户唯一标识
// @param attributes 客户属性信息
// @param eventName 事件名称
// @param eventData 事件相关数据
func (c *Base) PushCustomerIOAsync(customerID string, attributes map[string]interface{}, eventName string, eventData map[string]interface{}) {
	// 创建BaseController实例
	baseCtrl := customer.NewBaseController()

	// 使用PushCustomEvent方法
	baseCtrl.PushCustomEvent(customerID, eventName, eventData)
}

// PushDepositCustomerIO 推送充值状态信息到CustomerIO
// @param flag int 充值状态标识：
//
//	1 - 未找到玩家
//	2 - 小于最低充值额
//	3 - 待支付
//	4 - 已过期
//	5 - 充值成功
//
// @param u *model.XUser 用户信息，包含用户ID、真实姓名、邮箱等
// @param o *model.XRecharge 充值订单信息，包含订单ID、金额、币种等
// @param p *model.XFinanceMethod 支付方式信息
// 该方法会根据充值状态flag生成相应的消息，并将充值相关信息推送到CustomerIO平台
// 用于记录和跟踪用户的充值行为状态
func (c *Base) PushDepositCustomerIO(f int, u *model.XUser, o *model.XRecharge, p *model.XFinanceMethod) {
	params := map[string]any{
		"account_id": u.UserID,
		"amount":     o.Amount,
		"currency":   p.Country,
		"email":      u.Email,
	}
	switch f {
	case 5: // 充值成功
		params["comment"] = fmt.Sprintf("complete:%s_%s_%s", p.Brand, p.Country, o.Symbol)
		params["created"] = o.CreateTime
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "complete"
		params["type"] = "deposit_complete"
	case 3: // 待支付
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "pending"
		params["type"] = "deposit_pending"
	case 1: // 未找到玩家
		params["paymethod"] = "deposit_error"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "player_not_found"
		params["transaction_id"] = o.ID
		params["type"] = "deposit_error"
	case 2: // 小于最低充值额
		params["paymethod"] = "deposit_error"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "amount_too_small"
		params["transaction_id"] = o.ID
		params["type"] = "deposit_error"
	case 4: // 已过期
		params["paymethod"] = "deposit_expired"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "expired"
		params["transaction_id"] = o.ID
		params["type"] = "deposit_expired"
	}

	c.PushCustomerIOAsync(fmt.Sprint(u.UserID), map[string]interface{}{
		"account_id": u.UserID,
	},
		"deposit", // 事件名称
		params)
}

// PushWithdrawCustomerIO 推送提现状态信息到CustomerIO
// @param flag int 提现状态标识：
//
//	0 - 待审核
//	1 - 审核拒绝
//	2 - 审核通过
//	8 - 拒绝发放
//	4 - 已发放
//	5 - 正在出款
//	6 - 出款完成
//	7 - 失败退回
//
// @param u *model.XUser 用户信息，包含用户ID、真实姓名、邮箱等
// @param o *model.XWithdraw 提现订单信息，包含订单ID、金额、币种等
// @param p *model.XFinanceMethod 支付方式信息
// 该方法会根据提现状态flag生成相应的消息，并将提现相关信息推送到CustomerIO平台
// 用于记录和跟踪用户的提现行为状态
func (c *Base) PushWithdrawCustomerIO(f int, u *model.XUser, o *model.XWithdraw, p *model.XFinanceMethod) {
	params := map[string]any{
		"account_id": u.UserID,
		"amount":     o.Amount,
		"currency":   p.Country,
		"email":      u.Email,
	}
	switch f {
	case 0: // 待审核
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "pending_review"
		params["type"] = "withdraw_pending_review"
	case 1: // 审核拒绝
		params["paymethod"] = "withdraw_rejected"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "review_rejected"
		params["transaction_id"] = o.ID
		params["type"] = "withdraw_rejected"
	case 2: // 审核通过
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "review_approved"
		params["type"] = "withdraw_approved"
	case 8: // 拒绝发放
		params["paymethod"] = "withdraw_rejected"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "payout_rejected"
		params["transaction_id"] = o.ID
		params["type"] = "withdraw_rejected"
	case 4: // 已发放
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "paid"
		params["type"] = "withdraw_paid"
	case 5: // 正在出款
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "processing"
		params["type"] = "withdraw_processing"
	case 6: // 出款完成
		params["comment"] = fmt.Sprintf("complete:%s_%s_%s", p.Brand, p.Country, o.Symbol)
		params["created"] = o.CreateTime
		params["language"] = p.Country
		params["paymethod"] = fmt.Sprintf("%s_%s", p.Brand, o.Symbol)
		params["player_country"] = p.Country
		params["status"] = "complete"
		params["type"] = "withdraw_complete"
	case 7: // 失败退回
		params["paymethod"] = "withdraw_failed"
		params["date"] = time.Now().UTC().Format(time.RFC3339Nano)
		params["phone"] = u.PhoneNum
		params["status"] = "failed_refunded"
		params["transaction_id"] = o.ID
		params["type"] = "withdraw_failed"
	}

	c.PushCustomerIOAsync(fmt.Sprint(u.UserID), map[string]interface{}{
		"account_id": u.UserID,
	},
		"withdraw", // 事件名称
		params)
}

func (c *Base) getPayMethod(id int) (*model.XFinanceMethod, error) {
	// 获取支付配置信息
	dao := server.DaoxHashGame().XFinanceMethod
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(id))).First()
	if err != nil {
		return nil, err
	}

	return res, err
}

func (c *Base) getRechargeRate(symbol string) (float64, error) {
	dao := server.DaoxHashGame().XFinanceSymbol
	db := dao.WithContext(context.Background())
	res, err := db.Where(dao.Symbol.Eq(symbol)).First()
	if err != nil {
		return 0.00, err
	}

	rate := 0.00
	if res.AutoState == 1 {
		rate = res.RechargeRate
	} else {
		rate = res.RechargeRateEx
	}

	if res.RechargeFixType == 1 {
		rate += res.RechargeFix
	} else {
		rate += rate * res.RechargeFix
	}

	return rate, nil
}

func (c *Base) getWithdrawRate(symbol string) (float64, error) {
	dao := server.DaoxHashGame().XFinanceSymbol
	db := dao.WithContext(context.Background())
	res, err := db.Where(dao.Symbol.Eq(symbol)).First()
	if err != nil {
		return 0.00, err
	}

	rate := 0.00
	if res.AutoState == 1 {
		rate = res.WithwardRate
	} else {
		rate = res.WithwardRateEx
	}

	if res.RechargeFixType == 1 {
		rate += res.WithwardFix
	} else {
		rate += rate * res.WithwardFix
	}

	return rate, nil
}

func (c *Base) getUser(id int) (*model.XUser, error) {
	dao := server.DaoxHashGame().XUser
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.UserID.Eq(int32(id))).First()
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) getRechargeOrder(no int) (*model.XRecharge, error) {
	dao := server.DaoxHashGame().XRecharge
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(no))).First()

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) getWithdrawOrder(no int) (*model.XWithdraw, error) {
	dao := server.DaoxHashGame().XWithdraw
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(no))).First()

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) generateNum() string {
	// 获取当前时间戳
	now := time.Now()
	timestamp := now.Format("20060102150405") // 格式化时间戳

	// 生成随机数
	rand.Seed(uint64(now.UnixNano()))
	randomNum := rand.Int63n(100000000)

	// 拼接时间戳和随机数生成订单号
	orderID := fmt.Sprintf("%s%05d", timestamp, randomNum)
	return orderID
}

func (c *Base) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	// 将请求头转换为JSON格式便于阅读
	headerJson, _ := json.Marshal(header)

	// 一次性打印所有请求信息
	logs.Info("支付请求信息 - URL: %s, Headers: %s, Body: %s", url, string(headerJson), string(data))

	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		logs.Error("发送请求失败:", err)
		return nil, err
	}

	// 打印响应内容
	logs.Info("支付响应内容:", resp.String())

	return resp, nil
}

func (c *Base) postFrom(url string, header map[string]string, data map[string]string) (*resty.Response, error) {
	// 将请求头和表单数据转换为JSON格式便于阅读
	headerJson, _ := json.Marshal(header)
	dataJson, _ := json.Marshal(data)

	// 一次性打印所有表单请求信息
	logs.Info("支付表单请求信息 - URL: %s, Headers: %s, FormData: %s", url, string(headerJson), string(dataJson))

	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetFormData(data).
		Post(url)

	if err != nil {
		logs.Error("发送表单请求失败:", err)
		return nil, err
	}

	// 打印响应内容
	logs.Info("支付表单响应内容:", resp.String())

	return resp, nil
}

func (c *Base) generateMd5Sign(params xgo.H, cfgkey string) string {
	// 对参数按照键排序
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	var sortedParams []string
	for _, key := range keys {
		if params[key] == "" {
			continue
		}
		//str := key + "=" + params[key].(string)
		sortedParams = append(sortedParams, fmt.Sprintf("%s=%v", key, params[key]))
	}

	sortedParamsStr := strings.Join(sortedParams, "&")
	str := sortedParamsStr + "&key=" + cfgkey
	logs.Info("请求支付加密前：", str)

	// 创建一个 MD5 实例
	hash := md5.New()

	// 将字符串转换为字节数组并计算 MD5 值
	hash.Write([]byte(str))
	hashBytes := hash.Sum(nil)

	// 将 MD5 值转换为十六进制字符串
	md5Str := hex.EncodeToString(hashBytes)

	logs.Info("请求支付加密后：", md5Str)

	return md5Str
}

// rechargeCallbackHandel 处理充值回调
// @description 处理第三方支付平台的充值回调，更新订单状态并处理相关业务逻辑
// @param userId int 用户ID
// @param id int 充值订单ID
// @param result int 充值结果状态码:
//   - 3: 待支付
//   - 5: 成功
//   - 6: 超时
//   - 7: 失败
func (c *Base) rechargeCallbackHandel(userId int, id int, result int) {
	// 使用事务处理所有操作
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		// 处理充值成功的情况
		if result == 5 {
			// 调用存储过程处理充值成功的业务逻辑
			preuslt, err := tx.Db.CallProcedure("x_admin_recharge_legal", id)
			if err != nil {
				logs.Error("调用充值存储过程失败:", err, "订单ID:", id)
				return fmt.Errorf("处理充值失败: %v", err)
			}

			// 检查存储过程返回结果
			if preuslt != nil {
				// 检查错误码
				if preuslt.Exists("errcode") {
					errCode := preuslt.Int("errcode")
					logs.Error("充值存储过程返回错误码:", errCode, "订单ID:", id)
					return fmt.Errorf("充值处理失败，错误码: %d", errCode)
				}

				// 检查返回值
				if preuslt.Exists("p_ReturnValue") {
					returnValue := preuslt.Int("p_ReturnValue")
					if returnValue != 0 {
						logs.Error("充值存储过程返回非零值:", returnValue, "订单ID:", id)
						return fmt.Errorf("充值处理失败，返回值: %d", returnValue)
					}
				}
			}

			logs.Info("充值成功处理完成，订单ID:", id)

			// 获取订单信息用于推送消息
			rechargeDao := server.DaoxHashGame().XRecharge
			rechargeDb := rechargeDao.WithContext(context.Background())
			order, err := rechargeDb.Where(rechargeDao.ID.Eq(int32(id))).First()
			if err == nil {
				// 获取用户信息
				user, err := c.getUser(userId)
				if err == nil {
					// 获取支付方式信息
					payMethod, err := c.getPayMethod(int(order.PayID))
					if err == nil {
						// 推送充值成功消息到CustomerIO
						c.PushDepositCustomerIO(5, user, order, payMethod)
					} else {
						logs.Error("获取支付方式失败:", err)
					}
				} else {
					logs.Error("获取用户信息失败:", err)
				}
			} else {
				logs.Error("获取充值订单信息失败:", err)
			}
			// 发送充值到账提醒消息
			if order != nil {
				messageService := msg.NewSendMessageAPIService()
				if err := messageService.SendAccountMessage("3", int(userId), order.RealAmount, order.Symbol, nil); err != nil {
					logs.Error("发送充值到账提醒失败: %v", err)
				}
			}

			// 如果是首存，自动调用ProcessRecommendFriendReward方法处理推荐奖励等逻辑
			if order != nil && order.IsFirst == 1 {
				// 处理推荐好友奖励
				if controller.ProcessRecommendFriendReward(int32(userId), order.ID) {
					logs.Info("推荐好友奖励处理成功: userId=%d, orderId=%d", userId, order.ID)
				} else {
					logs.Error("推荐好友奖励处理失败: userId=%d, orderId=%d", userId, order.ID)
				}
			}

			return nil
		}

		// 处理其他状态（待支付、超时、失败）
		rechargeDao := server.DaoxHashGame().XRecharge
		rechargeDb := rechargeDao.WithContext(context.Background())

		// 更新订单状态，只处理状态为3(待支付)的订单
		updateResult, err := rechargeDb.Where(rechargeDao.ID.Eq(int32(id)), rechargeDao.State.Eq(3)).
			Updates(map[string]interface{}{
				"State": int32(result),
			})

		if err != nil {
			logs.Error("更新充值订单状态失败:", err, "订单ID:", id)
			return fmt.Errorf("更新订单状态失败: %v", err)
		}

		if updateResult.RowsAffected == 0 {
			logs.Error("充值订单不存在或状态不正确，订单ID:", id)
			return errors.New("订单不存在或状态不正确")
		}

		// 获取订单信息用于日志记录和推送消息
		order, err := rechargeDb.Where(rechargeDao.ID.Eq(int32(id))).First()
		if err != nil {
			logs.Error("获取充值订单信息失败:", err, "订单ID:", id)
			return nil // 状态已更新，获取订单信息失败不影响主流程
		}

		// 记录操作日志
		logs.Info("充值订单状态更新成功:",
			"订单ID:", id,
			"用户ID:", order.UserID,
			"金额:", order.Amount,
			"新状态:", result)

		// 获取用户信息和支付方式信息用于推送消息
		user, err := c.getUser(int(order.UserID))
		if err == nil {
			payMethod, err := c.getPayMethod(int(order.PayID))
			if err == nil {
				// 根据不同的失败状态进行特殊处理
				if result == 6 { // 超时
					logs.Info("充值订单超时:", id)
					// 推送充值超时消息到CustomerIO
					c.PushDepositCustomerIO(4, user, order, payMethod) // 使用已过期状态
				} else if result == 7 { // 失败
					logs.Info("充值订单失败:", id)
					// 推送充值失败消息到CustomerIO和客服系统
					c.PushDepositCustomerIO(result, user, order, payMethod)
				} else {
					// 其他状态（如待支付）
					c.PushDepositCustomerIO(result, user, order, payMethod)
				}
			} else {
				logs.Error("获取支付方式失败:", err)
			}
		} else {
			logs.Error("获取用户信息失败:", err)
		}

		return nil
	})
}

// withdrawCallbackHandel 处理提现回调
// @description 处理第三方支付平台的提现回调，更新订单状态并处理相关业务逻辑
// @param orderId int 提现订单ID
// @param result int 提现结果状态码:
//   - 5: 处理中
//   - 6: 成功
//   - 其他状态: 失败，需要退回金额
func (c *Base) withdrawCallbackHandel(orderId int, result int) {
	// 处理提现成功的情况
	if result == 6 {
		dao := server.DaoxHashGame().XWithdraw
		db := dao.WithContext(context.Background())
		// 直接更新提现订单状态为成功，只处理状态为5(处理中)的订单
		_, err := db.Where(dao.ID.Eq(int32(orderId)), dao.State.Eq(5)).Updates(map[string]interface{}{
			"State": int32(result),
		})
		if err != nil {
			logs.Error("更新提现订单状态失败:", err)
		} else {
			// 获取订单信息用于推送消息
			order, err := db.Where(dao.ID.Eq(int32(orderId))).First()
			if err == nil {
				// 获取用户信息
				user, err := c.getUser(int(order.UserID))
				if err == nil {
					// 获取支付方式信息
					payMethod, err := c.getPayMethod(int(order.PayID))
					if err == nil {
						// 推送提现成功消息到CustomerIO
						c.PushWithdrawCustomerIO(6, user, order, payMethod)
					} else {
						logs.Error("获取支付方式失败:", err)
					}
				} else {
					logs.Error("获取用户信息失败:", err)
				}
				// 发送提现成功提醒消息
				messageService := msg.NewSendMessageAPIService()
				if err := messageService.SendAccountMessage("4", int(order.UserID), order.RealAmount, order.Symbol, nil); err != nil {
					logs.Error("发送提现成功提醒失败: %v", err)
				}
			} else {
				logs.Error("获取提现订单信息失败:", err)
			}
		}
	} else {
		// 处理提现失败的情况，需要在事务中处理退款
		server.XDb().Transaction(func(tx *xgo.XTx) error {
			// 获取提现订单详情
			withdrawDao := server.DaoxHashGame().XWithdraw
			withdrawDb := withdrawDao.WithContext(context.Background())

			// 更新提现订单状态，只处理状态为5(处理中)的订单
			row, err := withdrawDb.Where(withdrawDao.ID.Eq(int32(orderId)), withdrawDao.State.Eq(5)).Updates(map[string]interface{}{
				"State": int32(result),
			})
			if err != nil {
				logs.Error("更新提现订单状态失败:", err)
				return err
			}
			var user *model.XUser
			var withdraw *model.XWithdraw
			// 如果成功更新了订单状态
			if row.RowsAffected > 0 {
				// 获取提现订单信息
				if withdraw, err = withdrawDb.Where(withdrawDao.ID.Eq(int32(orderId))).First(); err != nil {
					logs.Error("获取提现订单失败:", err)
					return err
				}

				// 获取用户信息
				userDao := server.DaoxHashGame().XUser
				userDb := userDao.WithContext(context.Background())

				if user, err = userDb.Where(userDao.UserID.Eq(withdraw.UserID)).First(); err != nil {
					logs.Error("获取用户信息失败:", err)
					return err
				}

				// 更新用户余额，将提现金额退回
				_, err = userDb.Where(userDao.UserID.Eq(withdraw.UserID)).Updates(map[string]interface{}{
					"Amount": user.Amount + withdraw.Amount,
				})
				if err != nil {
					logs.Error("更新用户余额失败:", err)
					return err
				}

				// 记录资金变动日志
				amountChangeDao := server.DaoxHashGame().XAmountChangeLog
				amountChangeDb := amountChangeDao.WithContext(context.Background())
				err = amountChangeDb.Create(&model.XAmountChangeLog{
					SellerID:     withdraw.SellerID,
					ChannelID:    withdraw.ChannelID,
					UserID:       withdraw.UserID,
					BeforeAmount: user.Amount,
					Amount:       withdraw.Amount,
					AfterAmount:  user.Amount + withdraw.Amount,
					Reason:       6, // 提现失败退回
					Memo:         fmt.Sprint(orderId),
					CreateTime:   time.Now(),
				})
				if err != nil {
					logs.Error("记录资金变动日志失败:", err)
					return err
				}

				// 获取支付方式信息
				payMethod, err := c.getPayMethod(int(withdraw.PayID))
				if err == nil {
					// 推送提现失败消息到CustomerIO
					c.PushWithdrawCustomerIO(7, user, withdraw, payMethod)
				} else {
					logs.Error("获取支付方式失败:", err)
				}
			} else {
				return errors.New("订单不存在,或订单状态不正确")
			}
			// 发送提现失败提醒消息
			messageService := msg.NewSendMessageAPIService()
			if err := messageService.SendAccountMessage("5", int(user.UserID), withdraw.RealAmount, withdraw.Symbol, nil); err != nil {
				logs.Error("发送提现成功提醒失败: %v", err)
			}
			return nil
		})
	}
}

func (c *Base) updateThirdOrder(id int32, no string) error {
	dao := server.DaoxHashGame().XRecharge
	db := dao.WithContext(context.Background())

	_, err := db.Where(dao.ID.Eq(id)).Update(dao.ThirdID, no)
	if err != nil {
		return err
	}

	return nil
}

func (c *Base) md5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}
