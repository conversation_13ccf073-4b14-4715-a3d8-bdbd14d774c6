// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXFinanceMethod = "x_finance_method"

// XFinanceMethod 充提方法
type XFinanceMethod struct {
	ID                    int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID              int32     `gorm:"column:SellerId" json:"SellerId"`
	Brand                 string    `gorm:"column:Brand;comment:支付商家  1.区块链TRON,2区块链ETH,3易币付,4PIX" json:"Brand"`             // 支付商家  1.区块链TRON,2区块链ETH,3易币付,4PIX
	Name                  string    `gorm:"column:Name;comment:支代付名称" json:"Name"`                                           // 支代付名称
	Symbol                string    `gorm:"column:Symbol;comment:支持币种" json:"Symbol"`                                        // 支持币种
	IsRecharge            int32     `gorm:"column:IsRecharge;comment:是否支持充值 1支持,2不支持" json:"IsRecharge"`                     // 是否支持充值 1支持,2不支持
	IsWithdraw            int32     `gorm:"column:IsWithdraw;comment:是否支持提款 1支持,2不支持" json:"IsWithdraw"`                     // 是否支持提款 1支持,2不支持
	MinRecharge           float64   `gorm:"column:MinRecharge;comment:充值最低金额 -1无限制" json:"MinRecharge"`                      // 充值最低金额 -1无限制
	MaxRecharge           float64   `gorm:"column:MaxRecharge;comment:充值最大金额 -1 无限制" json:"MaxRecharge"`                     // 充值最大金额 -1 无限制
	MinWithdraw           float64   `gorm:"column:MinWithdraw;comment:最小提现金额 -1 无限制" json:"MinWithdraw"`                     // 最小提现金额 -1 无限制
	MaxWithdraw           float64   `gorm:"column:MaxWithdraw;comment:最大提现金额 -1 无限制" json:"MaxWithdraw"`                     // 最大提现金额 -1 无限制
	RechargeAmountOptions string    `gorm:"column:RechargeAmountOptions;comment:充值快速金额" json:"RechargeAmountOptions"`        // 充值快速金额
	WithdrawAmountOptions string    `gorm:"column:WithdrawAmountOptions;comment:提现快速金额" json:"WithdrawAmountOptions"`        // 提现快速金额
	IsInputRechargeAmount int32     `gorm:"column:IsInputRechargeAmount;comment:是否支持自定义输入充值金额" json:"IsInputRechargeAmount"` // 是否支持自定义输入充值金额
	IsInputWithdrawAmount int32     `gorm:"column:IsInputWithdrawAmount;comment:是否支持自定义输入提款金额" json:"IsInputWithdrawAmount"` // 是否支持自定义输入提款金额
	State                 int32     `gorm:"column:State;comment:状态 1启用 禁用" json:"State"`                                     // 状态 1启用 禁用
	APIInfo               string    `gorm:"column:ApiInfo;comment:api信息" json:"ApiInfo"`                                     // api信息
	CreateTime            time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	RechargeFreeRate      float64   `gorm:"column:RechargeFreeRate;default:0.000000;comment:充值手续费率" json:"RechargeFreeRate"`      // 充值手续费率
	WithdrawFreeRate      float64   `gorm:"column:WithdrawFreeRate;default:0.000000;comment:提现手续费率" json:"WithdrawFreeRate"`      // 提现手续费率
	RechargeDaillyLimit   float64   `gorm:"column:RechargeDaillyLimit;default:0.000000;comment:充值日上线" json:"RechargeDaillyLimit"` // 充值日上线
	WithdrawDaillyLimit   float64   `gorm:"column:WithdrawDaillyLimit;default:0.000000;comment:提款日上线" json:"WithdrawDaillyLimit"` // 提款日上线
	RechargeDailly        float64   `gorm:"column:RechargeDailly;default:0.000000;comment:今日已充值金额" json:"RechargeDailly"`         // 今日已充值金额
	WithdrawDailly        float64   `gorm:"column:WithdrawDailly;default:0.000000;comment:今日已提现金额" json:"WithdrawDailly"`         // 今日已提现金额
	ExtraConfig           string    `gorm:"column:ExtraConfig" json:"ExtraConfig"`
	Icon                  string    `gorm:"column:Icon" json:"Icon"`
	MKey                  string    `gorm:"column:MKey" json:"MKey"`
	Sort                  int32     `gorm:"column:Sort;comment:排序,数字越大越靠前" json:"Sort"` // 排序,数字越大越靠前
	Country               string    `gorm:"column:Country" json:"Country"`
	Banks                 string    `gorm:"column:Banks;comment:银行编码 名称" json:"Banks"`                                     // 银行编码 名称
	WithdrawBanks         string    `gorm:"column:WithdrawBanks;comment:代付银行" json:"WithdrawBanks"`                        // 代付银行
	IsBankRecharge        int32     `gorm:"column:IsBankRecharge;comment:充值是否银行卡方式 0:否 1:是" json:"IsBankRecharge"`         // 充值是否银行卡方式 0:否 1:是
	HasBankNoRecharge     int32     `gorm:"column:HasBankNoRecharge;comment:充值是否需要传银行卡号 0:否 1:是" json:"HasBankNoRecharge"` // 充值是否需要传银行卡号 0:否 1:是
	/*
		充值方式
		1-默认
		2-需要传姓名
		3-需要传银行编码
		4-需要传姓名和银行卡号
		5-需要传姓名和银行名称
		6-需要传完整的银行信息(姓名;银行名称;编码;卡号)
		7-需要传银行编码和姓名
	*/
	RechargeWayType int32 `gorm:"column:RechargeWayType;default:1;comment:充值方式\n1-默认 \n2-需要传姓名 \n3-需要传银行编码 \n4-需要传姓名和银行卡号 \n5-需要传姓名和银行名称 \n6-需要传完整的银行信息(姓名;银行名称;编码;卡号) \n7-需要传银行编码和姓名" json:"RechargeWayType"`
	/*
		提现方式
		1-银行信息(RealName,BankName,BankCode,BankNo)
		2-银行信息(RealName,BankName,BankNo)
		3-手机号 (PhoneNum)
		4-PIX (CPFNo,PIXType,PIXAccount)
		5-银行信息(BankCode)
		6-印度银行信息(BankName, RealName,BankNo,IFSC,BrankType)
		7-银行信息(BankCode, RealName,BankNo,BrankType)
		8-银行信息+身份信息(BankCode, RealName,BankNo,BrankType,IdentifyType,IdentifyNum)
	*/
	WithdrawWayType int32  `gorm:"column:WithdrawWayType;default:1;comment:提现方式\n1-银行信息(RealName,BankName,BankCode,BankNo) \n2-银行信息(RealName,BankName,BankNo) \n3-手机号 (PhoneNum)\n4-PIX (CPFNo,PIXType,PIXAccount)\n5-银行信息(BankCode) \n6-印度银行信息(BankName, RealName,BankNo,IFSC,BrankType)\n7-银行信息(BankCode, RealName,BankNo,BrankType)\n8-银行信息+身份信息(BankCode, RealName,BankNo,BrankType,IdentifyType,IdentifyNum)" json:"WithdrawWayType"`
	PayType         string `gorm:"column:PayType;comment:支付类型" json:"PayType"`                                        // 支付类型
	WPayType        string `gorm:"column:WPayType;comment:代付通道编码" json:"WPayType"`                                    // 代付通道编码
	BankType        string `gorm:"column:BankType;comment:银行类型" json:"BankType"`                                      // 银行类型
	PoundSign       string `gorm:"column:PoundSign;comment:币种符号" json:"PoundSign"`                                    // 币种符号
	IdentifyType    string `gorm:"column:IdentifyType;comment:证件类型" json:"IdentifyType"`                              // 证件类型
	Ftype           int32  `gorm:"column:Ftype;default:1;comment:币种 1:法币 2:加密货币" json:"Ftype"`                        // 币种 1:法币 2:加密货币
	Rtype           int32  `gorm:"column:Rtype;default:3;comment:支付类型 1:加密货币 2:线上支付 3:全球法币 4:信用卡 5:银行卡" json:"Rtype"` // 支付类型 1:加密货币 2:线上支付 3:全球法币 4:信用卡 5:银行卡
	Hv              int32  `gorm:"column:Hv;default:1;comment:权重必须大于1" json:"Hv"`                                     // 权重必须大于1
	JumpType        int32  `gorm:"column:JumpType;default:1;comment:跳转类型 1：内嵌 2:新窗口" json:"JumpType"`                 // 跳转类型 1：内嵌 2:新窗口
}

// TableName XFinanceMethod's table name
func (*XFinanceMethod) TableName() string {
	return TableNameXFinanceMethod
}
