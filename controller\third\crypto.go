package third

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"time"

	"github.com/beego/beego/logs"
)

var tzUTC8 = time.FixedZone("utc+8", 8*3600)

func UserId2Token(UserId int) string {
	plaintext, _ := json.Marshal(map[string]interface{}{
		"userId":    UserId,
		"timestamp": time.Now().UnixMicro(),
	})

	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"
	ciphertext := CBCEncrypt(string(plaintext), key)
	return url.QueryEscape(ciphertext)
}

func Token2UserId(token string) int {
	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"

	token, _ = url.QueryUnescape(token)
	payload := make(map[string]interface{})
	if err := json.Unmarshal([]byte(CBCDecrypt(token, key)), &payload); err != nil {
		logs.Debug("[ERR][BearCow]Token2UserId:", token, err, payload)
		return -1
	}
	return int(payload["userId"].(float64))
}

func CBCEncrypt(plaintext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		logs.Error("CBCEncrypt NewCipher error:", err)
		return ""
	}

	blockSize := len(key)
	padding := blockSize - len(plaintext)%blockSize // 填充字节
	if padding == 0 {
		padding = blockSize
	}

	// 填充 padding 个 byte(padding) 到 plaintext
	plaintext += string(bytes.Repeat([]byte{byte(padding)}, padding))
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err = rand.Read(iv); err != nil { // 将同时写到 ciphertext 的开头
		logs.Error("CBCEncrypt rand.Read error:", err)
		return ""
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], []byte(plaintext))

	//return base64.StdEncoding.EncodeToString(ciphertext)
	// 使用 base64.URLEncoding 进行编码
	encodedStr := base64.URLEncoding.EncodeToString(ciphertext)
	return encodedStr
}

func CBCDecrypt(ciphertext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		logs.Error("CBCDecrypt NewCipher error:", err)
		return ""
	}

	ciphercode, err := base64.URLEncoding.DecodeString(ciphertext)
	if err != nil {
		logs.Error("CBCDecrypt DecodeString error:", err)
		return ""
	}

	iv := ciphercode[:aes.BlockSize]        // 密文的前 16 个字节为 iv
	ciphercode = ciphercode[aes.BlockSize:] // 正式密文

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphercode, ciphercode)

	plaintext := string(ciphercode) // ↓ 减去 padding
	return plaintext[:len(plaintext)-int(plaintext[len(plaintext)-1])]
}
