package utils

import (
	"fmt"

	"github.com/jinzhu/gorm"
)

func DBTraction(gdb *gorm.DB, funcs ...func(gdb *gorm.DB) error) (err error) {
	tx := gdb.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = fmt.Errorf("%v", err)
		}
	}()
	for _, f := range funcs {
		err = f(tx)
		if err != nil {
			tx.Rollback()
			return
		}
	}
	err = tx.Commit().Error
	return
}

type MyString string

// type MySqlParams struct {
// 	kvs   map[string]interface{}
// 	kvStr string
// }

// func (t *MySqlParams) New() *MySqlParams {
// 	t.kvs = make(map[string]interface{})
// 	t.kvStr = ""
// 	return t
// }

// func (t *MySqlParams) WhereAnd(p string, v interface{}) *MySqlParams {
// 	t.kvs[p] = v
// 	if t.kvStr != "" {
// 		t.kvStr += " and "
// 	}
// 	switch v.(type) {
// 	case int:
// 		t.kvStr = fmt.Sprintf("%s = %d", t.kvStr, v.(int))
// 		break
// 	case string:
// 		t.kvStr = fmt.Sprintf("%s = %s", t.kvStr, v.(string))
// 		break
// 	case int64:
// 		t.kvStr = fmt.Sprintf("%s = %d", t.kvStr, v.(int64))
// 		break
// 	case float32:
// 		t.kvStr = fmt.Sprintf("%s = %f", t.kvStr, v.(float32))
// 		break
// 	case float64:
// 		t.kvStr = fmt.Sprintf("%s = %f", t.kvStr, v.(float64))
// 		break
// 	case MyString:
// 		t.kvStr = fmt.Sprintf("%s = %s", t.kvStr, v.(MyString))
// 		break
// 	}
// 	return t
// }

// func (t *MySqlParams) String() string {
// 	return t.kvStr
// }

// type MySqlUpdate struct {
// 	kvs   map[string]interface{}
// 	kvStr string
// }

// func (u *MySqlUpdate) New() *MySqlUpdate {
// 	u.kvs = make(map[string]interface{})
// 	u.kvStr = ""
// 	return u
// }

// func (t *MySqlUpdate) SetUpdate(p string, v interface{}) *MySqlUpdate {
// 	t.kvs[p] = v
// 	if t.kvStr != "" {
// 		t.kvStr += " , "
// 	}
// 	switch v.(type) {
// 	case int:
// 		t.kvStr = fmt.Sprintf("%s = %d", t.kvStr, v.(int))
// 		break
// 	case string:
// 		t.kvStr = fmt.Sprintf("%s = %s", t.kvStr, v.(string))
// 		break
// 	case int64:
// 		t.kvStr = fmt.Sprintf("%s = %d", t.kvStr, v.(int64))
// 		break
// 	case float32:
// 		t.kvStr = fmt.Sprintf("%s = %f", t.kvStr, v.(float32))
// 		break
// 	case float64:
// 		t.kvStr = fmt.Sprintf("%s = %f", t.kvStr, v.(float64))
// 		break
// 	case MyString:
// 		t.kvStr = fmt.Sprintf("%s = %s", t.kvStr, v.(MyString))
// 		break
// 	}
// 	return t
// }

// func (u *MySqlUpdate) String() string {
// 	return u.kvStr
// }
