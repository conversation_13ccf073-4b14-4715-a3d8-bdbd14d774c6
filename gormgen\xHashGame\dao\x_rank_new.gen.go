// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRankNew(db *gorm.DB, opts ...gen.DOOption) xRankNew {
	_xRankNew := xRankNew{}

	_xRankNew.xRankNewDo.UseDB(db, opts...)
	_xRankNew.xRankNewDo.UseModel(&model.XRankNew{})

	tableName := _xRankNew.xRankNewDo.TableName()
	_xRankNew.ALL = field.NewAsterisk(tableName)
	_xRankNew.SellerID = field.NewInt32(tableName, "SellerId")
	_xRankNew.RankType = field.NewInt32(tableName, "RankType")
	_xRankNew.GameType = field.NewInt32(tableName, "GameType")
	_xRankNew.Symbol = field.NewString(tableName, "Symbol")
	_xRankNew.Rank = field.NewInt32(tableName, "Rank")
	_xRankNew.UserID = field.NewString(tableName, "UserId")
	_xRankNew.ChainType = field.NewInt32(tableName, "ChainType")
	_xRankNew.Brand = field.NewString(tableName, "Brand")
	_xRankNew.GameID = field.NewString(tableName, "GameId")
	_xRankNew.Address = field.NewString(tableName, "Address")
	_xRankNew.Amount = field.NewFloat64(tableName, "Amount")
	_xRankNew.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xRankNew.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xRankNew.CreateTime = field.NewTime(tableName, "CreateTime")

	_xRankNew.fillFieldMap()

	return _xRankNew
}

type xRankNew struct {
	xRankNewDo xRankNewDo

	ALL      field.Asterisk
	SellerID field.Int32 // 运营商
	/*
		1 中奖日排行,2 中奖周排行,3 中奖月排行,4 中奖实时排行
		11 下注日排行,12 下注周排行,13 下注月排行,14 下注实时排行 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行
	*/
	RankType     field.Int32
	GameType     field.Int32  // 1哈希游戏 100三方游戏
	Symbol       field.String // 币种
	Rank         field.Int32  // 名次
	UserID       field.String // 玩家id
	ChainType    field.Int32  // 网链分类 1trc 2erc 3bsc
	Brand        field.String // 游戏厂商
	GameID       field.String // 游戏id
	Address      field.String // 投注地址
	Amount       field.Float64
	RewardRate   field.Float64
	RewardAmount field.Float64
	CreateTime   field.Time // 创建时间

	fieldMap map[string]field.Expr
}

func (x xRankNew) Table(newTableName string) *xRankNew {
	x.xRankNewDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRankNew) As(alias string) *xRankNew {
	x.xRankNewDo.DO = *(x.xRankNewDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRankNew) updateTableName(table string) *xRankNew {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.RankType = field.NewInt32(table, "RankType")
	x.GameType = field.NewInt32(table, "GameType")
	x.Symbol = field.NewString(table, "Symbol")
	x.Rank = field.NewInt32(table, "Rank")
	x.UserID = field.NewString(table, "UserId")
	x.ChainType = field.NewInt32(table, "ChainType")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.Address = field.NewString(table, "Address")
	x.Amount = field.NewFloat64(table, "Amount")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xRankNew) WithContext(ctx context.Context) *xRankNewDo { return x.xRankNewDo.WithContext(ctx) }

func (x xRankNew) TableName() string { return x.xRankNewDo.TableName() }

func (x xRankNew) Alias() string { return x.xRankNewDo.Alias() }

func (x xRankNew) Columns(cols ...field.Expr) gen.Columns { return x.xRankNewDo.Columns(cols...) }

func (x *xRankNew) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRankNew) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["RankType"] = x.RankType
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Rank"] = x.Rank
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ChainType"] = x.ChainType
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xRankNew) clone(db *gorm.DB) xRankNew {
	x.xRankNewDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRankNew) replaceDB(db *gorm.DB) xRankNew {
	x.xRankNewDo.ReplaceDB(db)
	return x
}

type xRankNewDo struct{ gen.DO }

func (x xRankNewDo) Debug() *xRankNewDo {
	return x.withDO(x.DO.Debug())
}

func (x xRankNewDo) WithContext(ctx context.Context) *xRankNewDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRankNewDo) ReadDB() *xRankNewDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRankNewDo) WriteDB() *xRankNewDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRankNewDo) Session(config *gorm.Session) *xRankNewDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRankNewDo) Clauses(conds ...clause.Expression) *xRankNewDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRankNewDo) Returning(value interface{}, columns ...string) *xRankNewDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRankNewDo) Not(conds ...gen.Condition) *xRankNewDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRankNewDo) Or(conds ...gen.Condition) *xRankNewDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRankNewDo) Select(conds ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRankNewDo) Where(conds ...gen.Condition) *xRankNewDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRankNewDo) Order(conds ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRankNewDo) Distinct(cols ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRankNewDo) Omit(cols ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRankNewDo) Join(table schema.Tabler, on ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRankNewDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRankNewDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRankNewDo) Group(cols ...field.Expr) *xRankNewDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRankNewDo) Having(conds ...gen.Condition) *xRankNewDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRankNewDo) Limit(limit int) *xRankNewDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRankNewDo) Offset(offset int) *xRankNewDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRankNewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRankNewDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRankNewDo) Unscoped() *xRankNewDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRankNewDo) Create(values ...*model.XRankNew) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRankNewDo) CreateInBatches(values []*model.XRankNew, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRankNewDo) Save(values ...*model.XRankNew) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRankNewDo) First() (*model.XRankNew, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankNew), nil
	}
}

func (x xRankNewDo) Take() (*model.XRankNew, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankNew), nil
	}
}

func (x xRankNewDo) Last() (*model.XRankNew, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankNew), nil
	}
}

func (x xRankNewDo) Find() ([]*model.XRankNew, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRankNew), err
}

func (x xRankNewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRankNew, err error) {
	buf := make([]*model.XRankNew, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRankNewDo) FindInBatches(result *[]*model.XRankNew, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRankNewDo) Attrs(attrs ...field.AssignExpr) *xRankNewDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRankNewDo) Assign(attrs ...field.AssignExpr) *xRankNewDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRankNewDo) Joins(fields ...field.RelationField) *xRankNewDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRankNewDo) Preload(fields ...field.RelationField) *xRankNewDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRankNewDo) FirstOrInit() (*model.XRankNew, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankNew), nil
	}
}

func (x xRankNewDo) FirstOrCreate() (*model.XRankNew, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankNew), nil
	}
}

func (x xRankNewDo) FindByPage(offset int, limit int) (result []*model.XRankNew, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRankNewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRankNewDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRankNewDo) Delete(models ...*model.XRankNew) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRankNewDo) withDO(do gen.Dao) *xRankNewDo {
	x.DO = *do.(*gen.DO)
	return x
}
