package controller

import (
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"strings"
	"xserver/abugo"
	"xserver/server"
)

type CellxpertController struct {
}

type SummaryItem struct {
	Cnt       int     `gorm:"column:cnt"`
	UserId    int     `gorm:"column:UserId"`
	DayDate   string  `gorm:"column:DayDate"`
	Brand     string  `gorm:"column:Brand"`
	CntAmount float64 `gorm:"column:CntAmount"`
	WinAmount float64 `gorm:"column:WinAmount"`
}

type BetData struct {
	UserId       int     `gorm:"column:UserId"`
	BetCnt       int     `gorm:"column:BetCnt"`
	Amount       float64 `gorm:"column:Amount"`
	RewardAmount float64 `gorm:"column:RewardAmount"`
	CreateTime   string  `gorm:"column:CreateTime"`
	NGR          float64 `gorm:"column:NGR"`
	Symbol       string  `gorm:"column:Symbol"`
	Type         string  `gorm:"column:Type"`
}

type TransactionData struct {
	UserId     int     `gorm:"column:UserId"`
	Id         int     `gorm:"column:Id"`
	CreateTime string  `gorm:"column:CreateTime"`
	Reason     int     `gorm:"column:Reason"`
	Amount     float64 `gorm:"column:Amount"`
}

func (c *CellxpertController) Init() {
	server.Http().PostNoAuth("/api/cxp/player", c.getPlayerList)
	server.Http().PostNoAuth("/api/cxp/transactions", c.getTransactions)
	server.Http().PostNoAuth("/api/cxp/bets", c.getBetList)
	//server.Http().PostNoAuth("/api/cxp/summary", c.getBetSummary)
}

func (c *CellxpertController) getPlayerList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		LastmodifiedDateFrom string `json:"last_modified_date_from"`
		LastmodifiedDateTo   string `json:"last_modified_date_to"`
		PlayerId             int    `json:"player_id"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	if reqdata.LastmodifiedDateFrom == "" || reqdata.LastmodifiedDateTo == "" {
		ctx.RespErr(errors.New("last_modified_date_from / last_modified_date_to params error"), &errcode)
		return
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "CxdId", "<>", "", nil)
	where.Add("and", "RegisterTime", ">=", reqdata.LastmodifiedDateFrom, nil)
	where.Add("and", "RegisterTime", "<=", reqdata.LastmodifiedDateTo, nil)
	if reqdata.PlayerId != 0 {
		where.Add("and", "UserId", "=", reqdata.PlayerId, nil)
	}
	dataList, err := server.Db().Table("x_user").Where(where).Limit(3000).OrderBy("Id desc").GetList()
	showData := make([]map[string]any, 0)
	if err != nil {
		logs.Error("PlayerList:", err)
		ctx.Gin().JSON(200, showData)
	}
	for _, item := range *dataList {
		showData = append(showData, map[string]interface{}{
			"player_id":        item["UserId"],
			"CXD":              item["CxdId"],
			"AffId":            item["Id"],
			"RegistrationDate": item["RegisterTime"],
			"ISOCountry":       item["IsoCountry"],
			"UserIPAddress":    item["RegisterIp"],
		})
	}

	ctx.Gin().JSON(200, showData)
}

// record bet record
func (c *CellxpertController) getBetList(ctx *abugo.AbuHttpContent) {
	//showData := make([]map[string]any, 0)
	type RequestData struct {
		LastmodifiedDateFrom string `json:"last_modified_date_from"`
		LastmodifiedDateTo   string `json:"last_modified_date_to"`
		PlayerId             int    `json:"player_id"`
	}
	reqdata := RequestData{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		//ctx.RespErr(errors.New("last_modified_date_from / last_modified_date_to params error"), &errcode)
		return
	}
	if reqdata.LastmodifiedDateFrom == "" || reqdata.LastmodifiedDateTo == "" {
		ctx.RespErr(errors.New("last_modified_date_from / last_modified_date_to params error"), &errcode)
		return
	}

	showData := getGamesBets(reqdata.PlayerId, reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
	ctx.Gin().JSON(200, showData)
}

func (c *CellxpertController) getTransactions(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		LastmodifiedDateFrom string `json:"last_modified_date_from"`
		LastmodifiedDateTo   string `json:"last_modified_date_to"`
		PlayerId             int    `json:"player_id"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	if reqdata.LastmodifiedDateFrom == "" || reqdata.LastmodifiedDateTo == "" {
		ctx.RespErr(errors.New("last_modified_date_from / last_modified_date_to params error"), &errcode)
		return
	}

	showData := getTransactions(reqdata.PlayerId, reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
	ctx.Gin().JSON(200, showData)

}

// 获取hash游戏的投注数据
func getGamesBets(UserId int, dateFrom string, dateTo string) []map[string]any {

	showData := make([]map[string]interface{}, 0)
	//showMapData := make(map[string]map[string]interface{}, 0)
	var betData []BetData
	whereSql := "AND b.CxdId != ''"
	thirdWhereSql := "AND b.CxdId != ''"
	//whereParams := make([]interface{}, 0)

	if UserId > 0 {
		whereSql += fmt.Sprintf(" AND a.`UserId` = %d ", UserId)
		thirdWhereSql += fmt.Sprintf(" AND a.`UserId` = %d ", UserId)
		//whereParams = append(whereParams, UserId)
	}

	if dateFrom != "" && dateTo != "" {
		whereSql += fmt.Sprintf(" AND a.`CreateTime` BETWEEN '%v' AND '%v'", dateFrom, dateTo)
		thirdWhereSql += fmt.Sprintf(" AND a.`ThirdTime` BETWEEN '%v' AND '%v'", dateFrom, dateTo)
		//whereParams = append(whereParams, dateFrom, dateTo)
	}

	hashBetSql := fmt.Sprintf(`SELECT 
		MAX(a.UserId) UserId, 
		COUNT(a.UserId) BetCnt,
		SUM(a.Amount) Amount,
		SUM(a.RewardAmount) RewardAmount,
		DATE_FORMAT( a.CreateTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX(b.CxdId) CxdID,
		MAX(a.Symbol) Symbol,
		SUM(a.Amount - a.RewardAmount) AS NGR,
		'Hash Game' AS Type 
		FROM x_order a LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 AND a.RewardAmount != '' %v 
		GROUP BY DATE_FORMAT( a.CreateTime, '%%Y-%%m-%%d' ),Symbol`, whereSql)

	dianziBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_dianzhi a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	liveBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_live a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	lotteryBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_lottery a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	qipaiBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_qipai a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	quweiBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_quwei a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	sprotBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_sport a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	texasBetSql := fmt.Sprintf(`SELECT 
		MAX( a.UserId ) UserId,
		COUNT( a.UserId ) BetCnt,
		SUM( a.BetAmount ) Amount,
		SUM( a.WinAmount ) RewardAmount,
		DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ) AS CreateTime,
		MAX( b.CxdId ) CxdID,
		'USDT' AS Symbol,
		SUM( a.BetAmount - a.WinAmount ) AS NGR,
		Max( a.Brand ) Type 
		FROM x_third_texas a 
		LEFT JOIN x_user b ON a.UserId = b.UserId 
		WHERE 1 %v 
		GROUP BY DATE_FORMAT( a.ThirdTime, '%%Y-%%m-%%d' ), a.Brand`, thirdWhereSql)

	querySql := fmt.Sprintf("SELECT * FROM(%v UNION %v UNION %v UNION %v UNION %v UNION %v UNION %v UNION %v) AllBets ORDER BY CreateTime DESC", hashBetSql, dianziBetSql, liveBetSql, lotteryBetSql, qipaiBetSql, quweiBetSql, sprotBetSql, texasBetSql)

	server.Db().Gorm().Raw(querySql).Scan(&betData)

	//server.Db().Gorm().Table("x_order a").Select(""+
	//	"MAX(a.UserId) UserId,"+
	//	"COUNT(a.UserId) BetCnt,"+
	//	"SUM(a.Amount) Amount,"+
	//	"SUM(a.RewardAmount) RewardAmount,"+
	//	"DATE_FORMAT( a.CreateTime, '%Y-%m-%d' ) AS CreateTime,"+
	//	"MAX(a.Symbol) Symbol,"+
	//	"MAX(b.CxdId) CxdID,SUM(a.Amount - a.RewardAmount) AS NGR").Joins("LEFT JOIN x_user b ON a.UserId = b.UserId").Where(whereSql, whereParams...).Group("DATE_FORMAT( a.CreateTime, '%Y-%m-%d' ),Symbol").Order("CreateTime Desc").Find(&BetData)

	for _, item := range betData {
		showData = append(showData, map[string]interface{}{
			//"player_cnt": 0,
			"player_id":  item.UserId,
			"date":       item.CreateTime,
			"type":       item.Type,
			"bets_count": item.BetCnt,
			"bets":       item.Amount,
			"NGR":        item.NGR,
			"currency":   strings.ToUpper(item.Symbol),
			//"win_cnt":    0,
		})
	}
	return showData
}

// 获取转账记录（充值/提现）
func getTransactions(UserId int, dateFrom string, dateTo string) []map[string]any {
	showData := make([]map[string]interface{}, 0)

	var transactionData []TransactionData

	whereSql := "b.CxdId != '' AND a.Reason IN(1,2)"
	whereParams := make([]interface{}, 0)

	if UserId > 0 {
		whereSql += " AND a.`UserId` = ? "
		whereParams = append(whereParams, UserId)
	}

	if dateFrom != "" && dateTo != "" {
		whereSql += " AND a.`CreateTime` BETWEEN ? AND ?"
		whereParams = append(whereParams, dateFrom, dateTo)
	}

	server.Db().Gorm().Table("x_amount_change_log a").Select(""+
		"a.Id,"+
		"a.UserId UserId,"+
		"a.Amount Amount,"+
		"a.Reason Reason,"+
		"a.CreateTime CreateTime").Joins("LEFT JOIN x_user b ON a.UserId = b.UserId").Where(whereSql, whereParams...).Order("CreateTime Desc").Find(&transactionData)

	reasonType := map[int]string{
		1: "Deposit",
		2: "Withdrawal",
	}

	for _, item := range transactionData {
		showData = append(showData, map[string]interface{}{
			"player_id":            item.UserId,
			"transaction_id":       item.Id,
			"transaction_Date":     item.CreateTime,
			"transaction_type":     reasonType[item.Reason],
			"transaction_amount":   item.Amount,
			"transaction_currency": "USDT",
		})
	}
	return showData
}

// 汇总数据
//func (c *CellxpertController) getBetSummary(ctx *abugo.AbuHttpContent) {
//	showData := make([]map[string]any, 0)
//	type RequestData struct {
//		LastmodifiedDateFrom string `json:"last_modified_date_from"`
//		LastmodifiedDateTo   string `json:"last_modified_date_to"`
//		PlayerId             int    `json:"player_id"`
//	}
//
//	errcode := 0
//	reqdata := RequestData{}
//	err := ctx.RequestData(&reqdata)
//	if err != nil {
//		ctx.RespErr(err, &errcode)
//		return
//	}
//
//	item1 := c.getBetCnt(reqdata.PlayerId, "pg", "x_third_dianzhi", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	item1 = c.getBetCnt(reqdata.PlayerId, "og", "x_third_quwei", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	item1 = c.getBetCnt(reqdata.PlayerId, "evo", "x_third_live", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	item1 = c.getBetCnt(reqdata.PlayerId, "gfg_hash", "x_third_lottery", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	item1 = c.getBetCnt(reqdata.PlayerId, "gfg", "x_third_qipai", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	item1 = c.getBetCnt(reqdata.PlayerId, "UP", "x_third_sport", reqdata.LastmodifiedDateFrom, reqdata.LastmodifiedDateTo)
//	showData = append(showData, item1...)
//
//	// 获取hashGame的投注记录
//
//	ctx.Gin().JSON(200, showData)
//}

// 投注
//func (c *CellxpertController) getBetCnt(userId int, brand, tbName, dateFrom, dateTo string) (showData []map[string]any) {
//	showData = make([]map[string]any, 0)
//	showMapData := make(map[string]map[string]interface{}, 0)
//	cnt := make([]SummaryItem, 0)
//	// 下注人数
//	dbtable := server.Db().Gorm().Table(tbName)
//	whereTxt := "CreateTime BETWEEN ? AND ? AND Brand = ?"
//	whereParams := make([]interface{}, 0)
//	whereParams = append(whereParams, dateFrom, dateTo, brand)
//	if userId > 0 {
//		whereTxt += " AND `UserId` = ? "
//		whereParams = append(whereParams, userId)
//	}
//	dbtable.Select("count(DISTINCT UserId) as cnt, DATE(CreateTime) AS DayDate, UserId, '"+brand+"' as Brand").
//		Where(whereTxt, whereParams...).Group("DayDate,UserId").Find(&cnt)
//	for _, item := range cnt {
//		showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)] = map[string]interface{}{
//			"player_cnt":    item.Cnt,
//			"date":          item.DayDate,
//			"player_id":     item.UserId,
//			"brand":         item.Brand,
//			"bet_cnt":       0,
//			"bet_amount":    0,
//			"profit_amount": 0,
//			"win_cnt":       0,
//		}
//	}
//
//	// 下注次数
//	cnt = make([]SummaryItem, 0)
//	whereTxt = "CreateTime BETWEEN ? AND ? AND Brand = ?"
//
//	dbtable.Select("count(1) as cnt, UserId, DATE(CreateTime) AS DayDate,'"+brand+"' as Brand").
//		Where(whereTxt, whereParams...).Group("DayDate,UserId").Find(&cnt)
//	for _, item := range cnt {
//		_, ok := showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]
//		if ok {
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]["bet_cnt"] = item.Cnt
//		} else {
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)] = map[string]interface{}{
//				"player_cnt":    0,
//				"date":          item.DayDate,
//				"player_id":     item.UserId,
//				"brand":         item.Brand,
//				"bet_cnt":       item.Cnt,
//				"bet_amount":    0,
//				"profit_amount": 0,
//				"win_cnt":       0,
//			}
//		}
//	}
//
//	// 中奖次数
//	cnt = make([]SummaryItem, 0)
//	whereTxt = "CreateTime BETWEEN ? AND ? AND WinAmount > 0 AND Brand = ?"
//	whereParams = make([]interface{}, 0)
//	whereParams = append(whereParams, dateFrom, dateTo, brand)
//	if userId > 0 {
//		whereTxt += " AND `UserId` = ? "
//		whereParams = append(whereParams, userId)
//	}
//	dbtable.Select("count(1) as cnt,UserId, DATE(CreateTime) AS DayDate,'"+brand+"' as Brand").
//		Where(whereTxt, whereParams...).Group("DayDate,UserId").Find(&cnt)
//	for _, item := range cnt {
//		_, ok := showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]
//		if ok {
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]["win_cnt"] = item.Cnt
//		} else {
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)] = map[string]interface{}{
//				"player_cnt":    0,
//				"date":          item.DayDate,
//				"player_id":     item.UserId,
//				"brand":         item.Brand,
//				"bet_cnt":       0,
//				"bet_amount":    0,
//				"profit_amount": 0,
//				"win_cnt":       item.Cnt,
//			}
//		}
//	}
//
//	// 中奖总数
//	cntAmount := make([]SummaryItem, 0)
//	whereTxt = "CreateTime BETWEEN ? AND ? AND Brand = ?"
//	whereParams = make([]interface{}, 0)
//	whereParams = append(whereParams, dateFrom, dateTo, brand)
//	if userId > 0 {
//		whereTxt += " AND `UserId` = ? "
//		whereParams = append(whereParams, userId)
//	}
//
//	dbtable.Select("sum(BetAmount) as CntAmount,sum(WinAmount) as WinAmount,UserId, DATE(CreateTime) AS DayDate,'"+brand+"' as Brand").
//		Where(whereTxt, whereParams...).Group("DayDate,UserId").Find(&cntAmount)
//	for _, item := range cntAmount {
//		_, ok := showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]
//		if ok {
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)]["bet_amount"] = item.CntAmount
//		} else {
//			fmt.Println("------", item.WinAmount, item.CntAmount)
//			showMapData[fmt.Sprintf("%d-%s", item.UserId, item.DayDate)] = map[string]interface{}{
//				"player_cnt":    0,
//				"date":          item.DayDate,
//				"player_id":     item.UserId,
//				"brand":         item.Brand,
//				"bet_cnt":       0,
//				"bet_amount":    item.CntAmount,
//				"profit_amount": item.WinAmount - item.CntAmount,
//				"win_cnt":       0,
//			}
//		}
//	}
//
//	for _, item := range showMapData {
//		showData = append(showData, item)
//	}
//	return
//}

// NGR=总投注-总派奖（total bet amount- total payout）
