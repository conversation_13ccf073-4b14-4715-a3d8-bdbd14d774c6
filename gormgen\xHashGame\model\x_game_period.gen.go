// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXGamePeriod = "x_game_period"

// XGamePeriod mapped from table <x_game_period>
type XGamePeriod struct {
	ID         int32     `gorm:"column:Id;not null;comment:id" json:"Id"`                                            // id
	ChainType  int32     `gorm:"column:ChainType;primaryKey;default:1;comment:网链分类 1trc 2erc 3bsc" json:"ChainType"` // 网链分类 1trc 2erc 3bsc
	GameID     int32     `gorm:"column:GameId;primaryKey" json:"GameId"`
	Period     string    `gorm:"column:Period;primaryKey" json:"Period"`
	State      int32     `gorm:"column:State;default:1;comment:状态  1等待开奖  2已开奖未结算 3已开奖结算完成" json:"State"` // 状态  1等待开奖  2已开奖未结算 3已开奖结算完成
	OpenHash   string    `gorm:"column:OpenHash;comment:开奖哈希" json:"OpenHash"`                            // 开奖哈希
	OpenResult string    `gorm:"column:OpenResult;comment:开奖结果" json:"OpenResult"`                        // 开奖结果
	BlockMaker string    `gorm:"column:BlockMaker" json:"BlockMaker"`
	MakeTotal  int32     `gorm:"column:MakeTotal" json:"MakeTotal"`
	CreateTime time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	BlockTime  time.Time `gorm:"column:BlockTime" json:"BlockTime"`
}

// TableName XGamePeriod's table name
func (*XGamePeriod) TableName() string {
	return TableNameXGamePeriod
}
