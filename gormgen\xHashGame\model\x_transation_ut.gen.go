// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTransationUt = "x_transation_ut"

// XTransationUt mapped from table <x_transation_ut>
type XTransationUt struct {
	ID          int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID      int32     `gorm:"column:UserId" json:"UserId"`
	TxID        string    `gorm:"column:TxId" json:"TxId"`
	FromAddress string    `gorm:"column:FromAddress" json:"FromAddress"`
	ToAddress   string    `gorm:"column:ToAddress" json:"ToAddress"`
	Amount      float64   `gorm:"column:Amount" json:"Amount"`
	Symbol      string    `gorm:"column:Symbol" json:"Symbol"`
	ReqTime     time.Time `gorm:"column:ReqTime" json:"ReqTime"`
	CreateTime  time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime  time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
}

// TableName XTransationUt's table name
func (*XTransationUt) TableName() string {
	return TableNameXTransationUt
}
