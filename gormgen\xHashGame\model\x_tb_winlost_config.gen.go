// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTbWinlostConfig = "x_tb_winlost_config"

// XTbWinlostConfig 最近盈利配置
type XTbWinlostConfig struct {
	ChannelID       int32     `gorm:"column:ChannelId;primaryKey;comment:渠道Id" json:"ChannelId"`                           // 渠道Id
	SellerID        int32     `gorm:"column:SellerId;comment:运营商Id" json:"SellerId"`                                       // 运营商Id
	GameType        string    `gorm:"column:GameType;comment:游戏分类" json:"GameType"`                                        // 游戏分类
	IntervalTime    int32     `gorm:"column:IntervalTime;comment:计算时间(单位：秒)" json:"IntervalTime"`                          // 计算时间(单位：秒)
	DisplayRows     int32     `gorm:"column:DisplayRows;comment:显示行数" json:"DisplayRows"`                                  // 显示行数
	IsUserDistinct  int32     `gorm:"column:IsUserDistinct;default:2;comment:是否玩家去重 1是 2否" json:"IsUserDistinct"`          // 是否玩家去重 1是 2否
	MinRewardAmount float64   `gorm:"column:MinRewardAmount;default:0.000000;comment:限制最低盈利金额 0忽略" json:"MinRewardAmount"` // 限制最低盈利金额 0忽略
	MinOnlineUsers  int32     `gorm:"column:MinOnlineUsers;comment:最低在线用户数 0忽略" json:"MinOnlineUsers"`                     // 最低在线用户数 0忽略
	Memo            string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                  // 描述
	Status          int32     `gorm:"column:Status;not null;default:1;comment:1有效 2无效" json:"Status"`                      // 1有效 2无效
	CreateTime      time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTbWinlostConfig's table name
func (*XTbWinlostConfig) TableName() string {
	return TableNameXTbWinlostConfig
}
