package controller

import (
	"time"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
)

type LiveController struct {
}

// Init ...
func (this *LiveController) Init() {
	server.Http().Gin().POST("/api/live/wallet", utils.CheckSignBody(), this.wallet)
	// 代理数据路由（总流水，总输赢）
	server.Http().Gin().POST("/api/live/agent/data", utils.CheckSignBody(), this.agentData)
	// 游戏列表接口
	server.Http().Gin().POST("/api/live/games", utils.CheckSignBody(), this.games)

}

func (this *LiveController) wallet(ctx *gin.Context) {
	type RequestData struct {
		UserId int32   `validate:"required"`
		Amount float64 `validate:"required"`
	}

	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}

	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		var amount float64
		var withdrawLiuSuiAdd float64
		if req.Amount > 0 {
			amount = req.Amount
			withdrawLiuSuiAdd = amount * 5
		} else {
			amount = req.Amount
			// withdrawLiuSuiAdd = amount * -2000
		}

		activeAddUseBalancerInfo := struct {
			UserId            int32
			ActiveName        string
			RealAmount        float64
			WithdrawLiuSuiAdd float64
			BalanceCReason    int
		}{
			UserId:            req.UserId,
			ActiveName:        "直播体验金",
			RealAmount:        amount,
			WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
			BalanceCReason:    201,
		}
		// 更新用户流水和账变记录
		err := ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})

}

func (this *LiveController) agentData(ctx *gin.Context) {
	type RequestData struct {
		UserId int32  `validate:"required"`
		Date   string `validate:"required"`
	}

	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "args error",
		})
		return
	}

	type ResponseData struct {
		TotalLiuShui float64
		TotalWinLose float64
	}

	// 将字符串日期转换为 time.Time 类型
	recordDate, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		logs.Error("Date parse error: %v", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "日期格式错误，请使用 YYYY-MM-DD 格式",
		})
		return
	}

	dao := server.DaoxHashGame().XUserDailly
	data, err := dao.WithContext(nil).Where(dao.UserID.Eq(req.UserId)).Where(dao.RecordDate.Eq(recordDate)).First()

	// 初始化响应数据
	responseData := ResponseData{
		TotalLiuShui: 0,
		TotalWinLose: 0,
	}

	// 检查查询是否成功
	if err == nil && data != nil {
		// 如果查询成功，使用查询结果
		responseData.TotalWinLose = data.TotalWinLoss
		responseData.TotalLiuShui = data.TotalLiuSui
	} else {
		logs.Error("agentData query error: %v", err)
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
		"data": responseData,
	})
}

// games 获取游戏列表
func (this *LiveController) games(ctx *gin.Context) {
	type RequestData struct {
		Brand    string `json:"Brand"`    // 品牌
		GameId   string `json:"GameId"`   // 游戏ID
		Page     int    `json:"Page"`     // 页码
		PageSize int    `json:"PageSize"` // 每页数量
	}

	req := RequestData{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		logs.Error("games ShouldBindJSON err ", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "参数错误",
		})
		return
	}

	// 游戏信息结构
	type GameInfo struct {
		Brand    string `json:"brand"`
		GameID   string `json:"gameId"`
		Name     string `json:"name"`
		EName    string `json:"eName"`
		Icon     string `json:"icon"`
		EIcon    string `json:"eIcon"`
		GameType int32  `json:"gameType"`
	}

	// 查询游戏列表
	gameListDao := server.DaoxHashGame().XGameList
	gameListDb := gameListDao.WithContext(nil)

	// 构建查询条件
	query := gameListDb.Where(gameListDao.State.Eq(1)).Where(gameListDao.OpenState.Eq(1)) // 只查询启用的游戏

	// 如果指定了品牌，则按品牌过滤
	if req.Brand != "" {
		query = query.Where(gameListDao.Brand.Eq(req.Brand))
	}

	// 如果指定了游戏ID，则按游戏ID过滤
	if req.GameId != "" {
		query = query.Where(gameListDao.GameID.Eq(req.GameId))
	}

	// 分页
	offset := (req.Page - 1) * req.PageSize
	limit := req.PageSize
	var games []model.XGameList

	// 按排序字段降序排列
	total, err := query.Order(gameListDao.Sort.Desc()).ScanByPage(&games, offset, limit)
	if err != nil {
		logs.Error("games query error: %v", err)
		ctx.JSON(200, gin.H{
			"code": 0,
			"msg":  "查询失败",
		})
		return
	}

	catMap := map[int32]int32{
		1: 3,
		2: 6,
		3: 4,
		4: 11,
		5: 5,
		6: 7,
		7: 8,
	}

	// 转换为响应格式
	var gameList []GameInfo
	for _, game := range games {
		gameInfo := GameInfo{
			Brand:  game.Brand,
			GameID: game.GameID,
			Name:   game.Name,
			EName:  game.EName,
			Icon:   server.ImageUrl() + game.Icon,
			EIcon:  server.ImageUrl() + game.EIcon,
			// gameType转换
			// 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
			// 1转换为3 2转换为6 3转换为4 4转换为11 5转换为5 6转换为7 7转换为8
			GameType: catMap[game.GameType],
		}
		gameList = append(gameList, gameInfo)
	}

	res := map[string]interface{}{
		"list":  gameList,
		"count": total,
	}

	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
		"data": res,
	})
}
