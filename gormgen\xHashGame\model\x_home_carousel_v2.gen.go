// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXHomeCarouselV2 = "x_home_carousel_v2"

// XHomeCarouselV2 mapped from table <x_home_carousel_v2>
type XHomeCarouselV2 struct {
	SellerID    int32  `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID   int32  `gorm:"column:ChannelId;primaryKey" json:"ChannelId"`
	Lang        int32  `gorm:"column:Lang;primaryKey;default:1" json:"Lang"`
	ID          int32  `gorm:"column:Id;primaryKey" json:"Id"`
	Name        string `gorm:"column:Name;not null;comment:轮播图名称" json:"Name"`         // 轮播图名称
	Sort        int32  `gorm:"column:Sort;default:1;comment:排序" json:"Sort"`           // 排序
	ExpiredTime int64  `gorm:"column:ExpiredTime;comment:过期时间" json:"ExpiredTime"`     // 过期时间
	State       int32  `gorm:"column:State;default:2;comment:状态 1启用 2禁用" json:"State"` // 状态 1启用 2禁用
	URL         string `gorm:"column:Url;comment:h5图片路径" json:"Url"`                   // h5图片路径
	PcURL       string `gorm:"column:PcUrl;comment:pc图片路径" json:"PcUrl"`               // pc图片路径
	ELink       string `gorm:"column:ELink" json:"ELink"`
	PcELink     string `gorm:"column:PcELink" json:"PcELink"`
}

// TableName XHomeCarouselV2's table name
func (*XHomeCarouselV2) TableName() string {
	return TableNameXHomeCarouselV2
}
