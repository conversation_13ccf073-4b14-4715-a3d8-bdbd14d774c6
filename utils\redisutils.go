package utils

import (
	"errors"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/garyburd/redigo/redis"
)

const (
	RedisCGet    = "get"
	RedisCSet    = "set"
	RedisCSetEx  = "setex"
	RedisCDel    = "del"
	RedisCMGet   = "MGet"
	RedisCLockNX = "NX"
	RedisCLockEX = "EX"
	RedisCIncrBy = "INCRBY"
	RedisCExpire = "EXPIRE"

	RedisDefaultTimeOut       = 60
	RedisDefaultIncrByTimeOut = 5 * 60

	RedisErrSet = 100
)

func ResetMoneyKey(ip string) string {
	return fmt.Sprintf("%s.%s", RedisKPResetMoney, ip)
}

func RedisHandlerByPool(pool *redis.Pool, fun func(conn redis.Conn) error) error {
	conn := pool.Get()
	defer func(conn redis.Conn) {
		err := conn.Close()
		if err != nil {
			logs.Error("redis handler error", err)
		}
	}(conn)

	err := fun(conn)
	if err != nil {
		logs.Error("redis handler error", err)
	}
	return err
}

func GetString(pool *redis.Pool, key string) (string, error) {
	var result string
	err := RedisHandlerByPool(pool, func(conn redis.Conn) error {
		str, err := redis.String(conn.Do(RedisCGet, key))
		result = str
		return err
	})
	return result, err
}

func SetString(pool *redis.Pool, key string, v string) error {
	//var count int
	err := RedisHandlerByPool(pool, func(conn redis.Conn) error {
		_, err := conn.Do(RedisCSet, key, v)
		//result = str
		return err
	})
	return err
}

func SetStringEx(pool *redis.Pool, key string, timeout int, v string) error {
	if timeout <= 0 {
		timeout = RedisDefaultTimeOut
	}
	err := RedisHandlerByPool(pool, func(conn redis.Conn) error {
		_, err := conn.Do(RedisCSetEx, key, timeout, v)
		return err
	})
	return err
}

func IncrByEx(pool *redis.Pool, key string, incr int, timeout int) (int, error) {
	result := 0
	if timeout <= 0 {
		timeout = RedisDefaultIncrByTimeOut
	}
	err := RedisHandlerByPool(pool, func(conn redis.Conn) error {
		count, err := conn.Do(RedisCIncrBy, key, incr)
		if err != nil {
			return err
		}
		result = count.(int)
		_, err = conn.Do(RedisCExpire, key, timeout)
		if err != nil {
			return err
		}
		return err
	})
	return result, err
}

func MultiGetString(pool *redis.Pool, keys []string) []string {
	var result []string
	RedisHandlerByPool(pool, func(conn redis.Conn) error {
		strs, err := redis.Strings(conn.Do(RedisCMGet, keys))
		result = strs
		return err
	})
	return result
}

//func LockFullHandler(pool *redis.Pool, key string, v string, timeout int) {
//	isGetLock := LockRedisHandler(pool, key, v, timeout)
//	if !isGetLock {
//		return
//	}
//
//}

func LockRedisHandler(pool *redis.Pool, key string, v string, timeout int, finishDelLock bool,
	fun func(conn *redis.Conn) error) {

	RedisHandlerByPool(pool, func(conn redis.Conn) error {
		lock := lockRedisHandler(conn, key, v, timeout)
		if !lock {
			return errors.New("redis锁已锁定")
		}

		err := fun(&conn)
		if err != nil {
			//输出错误，但是继续执行
			return errors.New("redis删除锁失败")
		}

		if finishDelLock {
			_, err := conn.Do(RedisCDel, key)
			if err != nil {
				return errors.New("redis删除锁失败")
			}
		}
		return nil
	})
}

func lockRedisHandler(conn redis.Conn, key string, v string, timeout int) bool {
	//var result string
	isGetLock := true
	_, err := redis.String(conn.Do(RedisCSet, key, v, RedisCLockEX, timeout, RedisCLockNX))
	if err == redis.ErrNil {
		isGetLock = false
		err = nil
	}
	return isGetLock
}
