// 文件：controller/third/single/base/base_service.go

package base

import (
	"errors"
	"math"
	"time"
	"xserver/server"

	thirdGameModel "xserver/model/third_game"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// 订单状态常量
const (
	OrderStateUnsettle = -1 // 未结算
	OrderStateSettled  = 1  // 已结算
	OrderStateCanceled = -2 // 已取消
)

// BaseThirdService 三方游戏服务基类
type BaseThirdService struct {
	BrandName    string
	Currency     string
	TableName    string
	TablePreName string
}

// 游戏类型常量
const (
	GameTypeDianzi  = 1 // 电子
	GameTypeQipai   = 2 // 棋牌
	GameTypeQuwei   = 3 // 小游戏
	GameTypeLottery = 4 // 彩票
	GameTypeLive    = 5 // 真人
	GameTypeSport   = 6 // 体育
	GameTypeTexas   = 7 // 德州扑克
)

// 游戏类型与表名的映射关系
var gameTableTypes = map[int]string{
	GameTypeDianzi:  "x_third_dianzhi", // 电子
	GameTypeQipai:   "x_third_qipai",   // 棋牌
	GameTypeQuwei:   "x_third_quwei",   // 小游戏
	GameTypeLottery: "x_third_lottery", // 彩票
	GameTypeLive:    "x_third_live",    // 真人
	GameTypeSport:   "x_third_sport",   // 体育
	GameTypeTexas:   "x_third_texas",   // 德州扑克
}

// NewBaseThirdService 创建三方游戏服务基类
func NewBaseThirdService(brandName, currency string, gameType int) *BaseThirdService {
	// 根据游戏类型获取表名
	tableName, ok := gameTableTypes[gameType]
	if !ok {
		// 默认使用真人表
		tableName = gameTableTypes[GameTypeLive]
	}

	// 预订单表名为表名加上 "_pre_order" 后缀
	tablePreName := tableName + "_pre_order"

	return &BaseThirdService{
		BrandName:    brandName,
		Currency:     currency,
		TableName:    tableName,
		TablePreName: tablePreName,
	}
}

// GetUserBalance 获取用户余额
func (b *BaseThirdService) GetUserBalance(userId int) (*thirdGameModel.UserBalance, error) {
	tx := server.Db().GormDao()
	userBalance := thirdGameModel.UserBalance{}
	err := tx.Table("x_user").
		Select("UserId, SellerId, ChannelId, Amount, BonusAmount, Token").
		Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		return nil, err
	}
	return &userBalance, nil
}

// GetUserBalanceForUpdate 获取用户余额并加锁
func (b *BaseThirdService) GetUserBalanceForUpdate(tx *daogorm.DB, userId int) (*thirdGameModel.UserBalance, error) {
	userBalance := thirdGameModel.UserBalance{}
	err := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
		Select("UserId, SellerId, ChannelId, Amount, BonusAmount, Token").
		Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		return nil, err
	}
	return &userBalance, nil
}

// GetOrderForUpdate 查询订单是否存在并返回订单结构体和状态
// 返回值:
// order: 如果订单存在，返回订单结构体；如果不存在，返回nil
// exists: 表示订单是否存在
// err: 如果是查询错误（非记录不存在），返回错误；否则返回nil
func (b *BaseThirdService) GetOrderForUpdate(tx *daogorm.DB, thirdId string) (order *thirdGameModel.ThirdOrder, exists bool, err error) {
	orderData := thirdGameModel.ThirdOrder{}

	// 查询订单是否存在
	queryErr := tx.Table(b.TablePreName).Where("ThirdId = ? AND Brand = ?", thirdId, b.BrandName).
		Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&orderData).Error

	// 如果没有错误，说明订单存在
	if queryErr == nil && orderData.Id > 0 {
		return &orderData, true, nil
	}

	// 如果是记录不存在的错误，返回nil, false, nil
	if errors.Is(queryErr, daogorm.ErrRecordNotFound) {
		return nil, false, nil
	}

	// 其他错误，如数据库查询错误
	return nil, false, queryErr
}

// CalculateBetDistribution 计算下注金额分配
func (b *BaseThirdService) CalculateBetDistribution(userBalance *thirdGameModel.UserBalance, betAmount float64) (float64, float64, int, error) {
	realBetAmount := 0.0
	bonusBetAmount := 0.0
	betType := BetTypeReal

	// 如果真金余额足够，全部使用真金
	if userBalance.Amount >= betAmount {
		realBetAmount = betAmount
		betType = BetTypeReal
	} else if userBalance.Amount > 0 {
		// 真金余额不足，部分使用真金，部分使用Bonus币
		realBetAmount = userBalance.Amount
		bonusBetAmount = betAmount - realBetAmount

		// 检查Bonus币余额是否足够
		if userBalance.BonusAmount < bonusBetAmount {
			return 0, 0, 0, errors.New("玩家余额不足")
		}

		betType = BetTypeMixed
	} else {
		// 真金余额为0，全部使用Bonus币
		bonusBetAmount = betAmount

		// 检查Bonus币余额是否足够
		if userBalance.BonusAmount < bonusBetAmount {
			return 0, 0, 0, errors.New("玩家余额不足")
		}

		betType = BetTypeBonus
	}

	return realBetAmount, bonusBetAmount, betType, nil
}

// CalculateWinDistribution 计算派彩金额分配
func (b *BaseThirdService) CalculateWinDistribution(betAmount, winAmount, bonusBetAmount float64) (float64, float64) {
	realWinAmount := 0.0
	bonusWinAmount := 0.0
	realBetAmount := betAmount - bonusBetAmount

	if betAmount > 0 {
		realBetRatio := realBetAmount / betAmount
		bonusBetRatio := bonusBetAmount / betAmount

		realWinAmount = winAmount * realBetRatio
		bonusWinAmount = winAmount * bonusBetRatio

		// 处理精度问题
		if math.Abs(realWinAmount+bonusWinAmount-winAmount) > 0.01 {
			diff := winAmount - (realWinAmount + bonusWinAmount)
			if realBetAmount > bonusBetAmount {
				realWinAmount += diff
			} else {
				bonusWinAmount += diff
			}
		}
	} else if winAmount > 0 {
		// 当 betAmount = 0 时，将所有派彩金额分配给分配给 Bonus 账户
		bonusWinAmount = winAmount // 或者 bonusWinAmount = winAmount
	}
	return realWinAmount, bonusWinAmount
}

// DeductBetAmount 扣除下注金额
func (b *BaseThirdService) DeductBetAmount(tx *daogorm.DB, userId int, realBetAmount, bonusBetAmount float64) error {
	// 扣除真金下注金额
	if realBetAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, realBetAmount).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", realBetAmount),
			})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected <= 0 {
			return errors.New("扣除真金失败")
		}
	}

	// 扣除Bonus币下注金额
	if bonusBetAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ? AND BonusAmount >= ?", userId, bonusBetAmount).
			Updates(map[string]interface{}{
				"BonusAmount": daogorm.Expr("BonusAmount - ?", bonusBetAmount),
			})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected <= 0 {
			return errors.New("扣除Bonus币失败")
		}
	}

	return nil
}

// AddWinAmount 增加派彩金额
func (b *BaseThirdService) AddWinAmount(tx *daogorm.DB, userId int, realWinAmount, bonusWinAmount float64) error {
	// 增加真金派彩金额
	if realWinAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ?", userId).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", realWinAmount),
			})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected <= 0 {
			return errors.New("增加真金失败")
		}
	}

	// 增加Bonus币派彩金额
	if bonusWinAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ?", userId).
			Updates(map[string]interface{}{
				"BonusAmount": daogorm.Expr("BonusAmount + ?", bonusWinAmount),
			})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected <= 0 {
			return errors.New("增加Bonus币失败")
		}
	}

	return nil
}

// CreateAmountChangeLog 创建账变记录
func (b *BaseThirdService) CreateAmountChangeLog(tx *daogorm.DB, userId, sellerId, channelId int,
	beforeAmount, amount, afterAmount float64, reason int, memo, createTime string, amountType int) error {

	amountLog := struct {
		UserId       int     `gorm:"column:UserId"`
		BeforeAmount float64 `gorm:"column:BeforeAmount"`
		Amount       float64 `gorm:"column:Amount"`
		AfterAmount  float64 `gorm:"column:AfterAmount"`
		Reason       int     `gorm:"column:Reason"`
		Memo         string  `gorm:"column:Memo"`
		SellerId     int     `gorm:"column:SellerId"`
		ChannelId    int     `gorm:"column:ChannelId"`
		CreateTime   string  `gorm:"column:CreateTime"`
		AmountType   int     `gorm:"column:AmountType"`
	}{
		UserId:       userId,
		BeforeAmount: beforeAmount,
		Amount:       amount,
		AfterAmount:  afterAmount,
		Reason:       reason,
		Memo:         memo,
		SellerId:     sellerId,
		ChannelId:    channelId,
		CreateTime:   createTime,
		AmountType:   amountType,
	}

	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}

// CreateCombinedAmountChangeLog 创建合并账变记录（同时记录真金和彩金的变动）
func (b *BaseThirdService) CreateCombinedAmountChangeLog(tx *daogorm.DB, userId, sellerId, channelId int,
	beforeAmount, amount, afterAmount float64,
	beforeBonusAmount, bonusAmount, afterBonusAmount float64,
	reason int, memo, createTime string) error {

	amountLog := struct {
		UserId            int     `gorm:"column:UserId"`
		BeforeAmount      float64 `gorm:"column:BeforeAmount"`
		Amount            float64 `gorm:"column:Amount"`
		AfterAmount       float64 `gorm:"column:AfterAmount"`
		BeforeBonusAmount float64 `gorm:"column:BeforeBonusAmount"`
		BonusAmount       float64 `gorm:"column:BonusAmount"`
		AfterBonusAmount  float64 `gorm:"column:AfterBonusAmount"`
		Reason            int     `gorm:"column:Reason"`
		Memo              string  `gorm:"column:Memo"`
		SellerId          int     `gorm:"column:SellerId"`
		ChannelId         int     `gorm:"column:ChannelId"`
		CreateTime        string  `gorm:"column:CreateTime"`
		AmountType        int     `gorm:"column:AmountType"`
	}{
		UserId:            userId,
		BeforeAmount:      beforeAmount,
		Amount:            amount,
		AfterAmount:       afterAmount,
		BeforeBonusAmount: beforeBonusAmount,
		BonusAmount:       bonusAmount,
		AfterBonusAmount:  afterBonusAmount,
		Reason:            reason,
		Memo:              memo,
		SellerId:          sellerId,
		ChannelId:         channelId,
		CreateTime:        createTime,
		AmountType:        0, // 0 表示同时包含真金和彩金
	}

	return tx.Table("x_amount_change_log").Create(&amountLog).Error
}

// CalculateProfit 计算平台输赢
func (b *BaseThirdService) CalculateProfit(realBetAmount, bonusBetAmount, realWinAmount, bonusWinAmount float64) (float64, float64, float64) {
	realProfit := realBetAmount - realWinAmount
	bonusProfit := bonusBetAmount - bonusWinAmount
	totalProfit := realProfit + bonusProfit
	return realProfit, bonusProfit, totalProfit
}

// ProcessFreeRound 处理免费旋转
func (b *BaseThirdService) ProcessFreeRound(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, winAmount float64,
	reason int, memo, createTime string) error {

	// 免费旋转，全部返还到Bonus币
	// 使用AddAmountWithLogs方法增加Bonus币并创建账变记录
	if winAmount > 0 {
		// 免费旋转奖励全部加到Bonus币中，真金部分为0
		return b.AddAmountWithLogs(tx, userBalance, 0, winAmount, reason, memo+" free round reward", createTime)
	}

	return nil
}

// ProcessBet 处理下注
func (b *BaseThirdService) ProcessBet(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, betAmount float64,
	betReason int, thirdId, gameId, gameName, memo, thirdTime, rawData string, betChannelId int) (*thirdGameModel.ThirdOrder, error) {

	// 计算下注金额分配
	realBetAmount, bonusBetAmount, betType, err := b.CalculateBetDistribution(userBalance, betAmount)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注 计算下注金额分配失败 thirdId=", thirdId, " userId=", userBalance.UserId, " betAmount=", betAmount, " err=", err.Error())
		return nil, err
	}

	// 扣除下注金额并创建账变记录
	err = b.DeductAmountWithLogs(tx, userBalance, realBetAmount, bonusBetAmount, betReason, memo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注 扣除下注金额失败 thirdId=", thirdId, " userId=", userBalance.UserId, " realBetAmount=", realBetAmount, " bonusBetAmount=", bonusBetAmount, " err=", err.Error())
		return nil, err
	}

	// 创建注单
	order := &thirdGameModel.ThirdOrder{
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		BetChannelId: betChannelId,
		UserId:       userBalance.UserId,
		Brand:        b.BrandName,
		ThirdId:      thirdId,
		GameId:       gameId,
		GameName:     gameName,
		BetAmount:    betAmount,
		WinAmount:    0, // 下注时没有派彩
		ValidBet:     0,
		ThirdTime:    thirdTime,
		Currency:     b.Currency,
		RawData:      rawData,
		State:        1, // 未开奖状态
		Fee:          0,
		DataState:    OrderStateUnsettle, // 未开奖状态
		CreateTime:   thirdTime,
		// 新增字段
		BetType:        betType,
		BonusBetAmount: bonusBetAmount,
		BonusWinAmount: 0,
	}

	// 创建预订单
	err = tx.Table(b.TablePreName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 下注 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", err.Error())
		return nil, err
	}
	return order, nil
}

// ProcessWin 处理派彩
func (b *BaseThirdService) ProcessWin(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, order *thirdGameModel.ThirdOrder,
	winAmount float64, winReason int, memo, thirdTime string, rawData string) error {

	// 检查注单状态
	if order.State == OrderStateSettled {
		logs.Error(b.BrandName+"_single 派彩 注单已结算 thirdId=", order.ThirdId)
		return errors.New("注单已结算")
	}

	if order.State == OrderStateCanceled {
		logs.Error(b.BrandName+"_single 派彩 注单已取消 thirdId=", order.ThirdId)
		return errors.New("注单已取消")
	}

	// 计算派彩金额分配
	realWinAmount, bonusWinAmount := b.CalculateWinDistribution(order.BetAmount, winAmount, order.BonusBetAmount)

	// 增加派彩金额并创建账变记录
	err := b.AddAmountWithLogs(tx, userBalance, realWinAmount, bonusWinAmount, winReason, memo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 派彩 增加派彩金额失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " realWinAmount=", realWinAmount, " bonusWinAmount=", bonusWinAmount, " err=", err.Error())
		return err
	}

	realBetAmount := order.BetAmount - order.BonusBetAmount
	validBet := math.Abs(realWinAmount - realBetAmount)
	if validBet > math.Abs(realBetAmount) {
		validBet = math.Abs(realBetAmount)
	}
	// 更新注单
	updateData := map[string]interface{}{
		"WinAmount":      winAmount,
		"BonusWinAmount": bonusWinAmount,
		"ValidBet":       validBet,
		"RawData":        rawData,
		"State":          OrderStateSettled,
		"DataState":      OrderStateSettled,
	}

	// 更新原始注单状态
	err = tx.Table(b.TableName).Where("ThirdId = ? AND Brand = ?", order.ThirdId, b.BrandName).Updates(updateData).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 派彩 更新注单失败 thirdId=", order.ThirdId, " error=", err.Error())
		return err
	}

	// 更新注单信息用于插入统计表
	statOrder := *order // 复制注单数据
	statOrder.Id = 0    // 重置ID以便创建新记录
	statOrder.WinAmount = winAmount
	statOrder.BonusWinAmount = bonusWinAmount
	statOrder.ValidBet = validBet
	statOrder.RawData = rawData
	statOrder.DataState = OrderStateSettled
	statOrder.ThirdTime = thirdTime

	// 将注单数据插入到统计表
	err = tx.Table(b.TableName).Create(&statOrder).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 派彩 插入统计表失败 thirdId=", order.ThirdId, " error=", err.Error())
		return err
	}

	logs.Info(b.BrandName+"_single 派彩成功 移动至统计表 成功 thirdId=", order.ThirdId, " winAmount=", winAmount, " betAmount=", order.BetAmount, " realWinAmount=", realWinAmount, " bonusWinAmount=", bonusWinAmount)

	return nil
}

// ProcessCancelBet 处理取消下注
func (b *BaseThirdService) ProcessCancelBet(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, order *ThirdOrderModel,
	cancelReason int, memo, thirdTime string) error {

	// 检查注单状态
	if order.State != OrderStateUnsettle {
		logs.Error(b.BrandName+"_single 取消下注 注单已取消 thirdId=", order.ThirdId)
		return nil
	}

	// 计算需要返还的金额
	realBetAmount := order.BetAmount - order.BonusBetAmount
	realRefundAmount := realBetAmount         //真金返还金额
	bonusRefundAmount := order.BonusBetAmount //bonus币返还金额

	// 如果已经派彩，需要扣除已派彩金额
	//if order.State == OrderStateSettled {
	//	realRefundAmount = realBetAmount - order.RealWinAmount
	//	bonusRefundAmount = order.BonusBetAmount - order.BonusWinAmount
	//
	//	// 如果派彩金额大于下注金额，需要扣除多余的派彩金额
	//	if realRefundAmount < 0 {
	//		// 扣除真金
	//		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userBalance.UserId, -realRefundAmount).
	//			Updates(map[string]interface{}{
	//				"Amount": daogorm.Expr("Amount - ?", -realRefundAmount),
	//			})
	//		if resultTmp.Error != nil {
	//			logs.Error(b.BrandName+"_single 取消下注 扣除多余真金派彩失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " amount=", -realRefundAmount, " err=", resultTmp.Error.Error())
	//			return resultTmp.Error
	//		}
	//		if resultTmp.RowsAffected <= 0 {
	//			logs.Error(b.BrandName+"_single 取消下注 扣除多余真金派彩失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " amount=", -realRefundAmount)
	//			return errors.New("扣除多余派彩失败")
	//		}
	//
	//		// 创建真金账变记录
	//		err := b.CreateRefundAmountChangeLogs(tx, userBalance, realRefundAmount, 0, cancelReason, memo, thirdTime)
	//		if err != nil {
	//			logs.Error(b.BrandName+"_single 取消下注 创建真金账变记录失败 thirdId=", order.ThirdId, " err=", err.Error())
	//			return err
	//		}
	//
	//		realRefundAmount = 0
	//	}
	//
	//	if bonusRefundAmount < 0 {
	//		// 扣除Bonus币
	//		resultTmp := tx.Table("x_user").Where("UserId = ? AND BonusAmount >= ?", userBalance.UserId, -bonusRefundAmount).
	//			Updates(map[string]interface{}{
	//				"BonusAmount": daogorm.Expr("BonusAmount - ?", -bonusRefundAmount),
	//			})
	//		if resultTmp.Error != nil {
	//			logs.Error(b.BrandName+"_single 取消下注 扣除多余Bonus币派彩失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " amount=", -bonusRefundAmount, " err=", resultTmp.Error.Error())
	//			return resultTmp.Error
	//		}
	//		if resultTmp.RowsAffected <= 0 {
	//			logs.Error(b.BrandName+"_single 取消下注 扣除多余Bonus币派彩失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " amount=", -bonusRefundAmount)
	//			return errors.New("扣除多余派彩失败")
	//		}
	//
	//		// 创建Bonus币账变记录
	//		err := b.CreateRefundAmountChangeLogs(tx, userBalance, 0, bonusRefundAmount, cancelReason, memo, thirdTime)
	//		if err != nil {
	//			logs.Error(b.BrandName+"_single 取消下注 创建Bonus币账变记录失败 thirdId=", order.ThirdId, " err=", err.Error())
	//			return err
	//		}
	//
	//		bonusRefundAmount = 0
	//	}
	//}

	// 返还真金和Bonus币
	if realRefundAmount > 0 || bonusRefundAmount > 0 {
		// 使用AddAmountWithLogs方法返还金额并创建账变记录
		err := b.AddAmountWithLogs(tx, userBalance, realRefundAmount, bonusRefundAmount, cancelReason, memo+" cancel bet refund", thirdTime)
		if err != nil {
			logs.Error(b.BrandName+"_single 取消下注 返还金额失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " realAmount=", realRefundAmount, " bonusAmount=", bonusRefundAmount, " err=", err.Error())
			return err
		}
	}

	// 更新注单状态
	err := tx.Table(b.TableName).Where("ThirdId = ? AND Brand = ?", order.ThirdId, b.BrandName).
		Updates(map[string]interface{}{
			"State":     1,
			"DataState": OrderStateCanceled,
		}).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 取消下注 更新注单状态失败 thirdId=", order.ThirdId, " error=", err.Error())
		return err
	}

	return nil
}

// ProcessResettle 处理重新结算
func (b *BaseThirdService) ProcessResettle(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, order *ThirdOrderModel,
	newWinAmount float64, resettleReason int, memo, thirdTime string) error {

	// 检查注单状态
	if order.State == OrderStateCanceled {
		logs.Error(b.BrandName+"_single 重新结算 注单已取消 thirdId=", order.ThirdId)
		return errors.New("注单已取消")
	}

	// 计算新的派彩金额分配
	newRealWinAmount, newBonusWinAmount := b.CalculateWinDistribution(order.BetAmount, newWinAmount, order.BonusBetAmount)

	// 计算派彩差额
	realWinDiff := newRealWinAmount - order.RealWinAmount
	bonusWinDiff := newBonusWinAmount - order.BonusWinAmount

	// 处理派彩差额
	if realWinDiff != 0 || bonusWinDiff != 0 {
		if realWinDiff > 0 || bonusWinDiff > 0 {
			// 如果有增加的金额，使用AddAmountWithLogs方法
			posRealWinDiff := math.Max(0, realWinDiff)
			posBonusWinDiff := math.Max(0, bonusWinDiff)

			if posRealWinDiff > 0 || posBonusWinDiff > 0 {
				err := b.AddAmountWithLogs(tx, userBalance, posRealWinDiff, posBonusWinDiff, resettleReason, memo+" resettle addition", thirdTime)
				if err != nil {
					logs.Error(b.BrandName+"_single 重新结算 增加金额失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " realAmount=", posRealWinDiff, " bonusAmount=", posBonusWinDiff, " err=", err.Error())
					return err
				}
			}
		}

		if realWinDiff < 0 || bonusWinDiff < 0 {
			// 如果有需要扣除的金额，使用DeductAmountWithLogs方法
			negRealWinDiff := math.Abs(math.Min(0, realWinDiff))
			negBonusWinDiff := math.Abs(math.Min(0, bonusWinDiff))

			if negRealWinDiff > 0 || negBonusWinDiff > 0 {
				err := b.DeductAmountWithLogs(tx, userBalance, negRealWinDiff, negBonusWinDiff, resettleReason, memo+" resettle deduction", thirdTime)
				if err != nil {
					logs.Error(b.BrandName+"_single 重新结算 扣除金额失败 thirdId=", order.ThirdId, " userId=", userBalance.UserId, " realAmount=", negRealWinDiff, " bonusAmount=", negBonusWinDiff, " err=", err.Error())
					return err
				}
			}
		}
	}

	// 更新注单
	updateData := map[string]interface{}{
		"WinAmount":      newWinAmount,
		"RealWinAmount":  newRealWinAmount,
		"BonusWinAmount": newBonusWinAmount,
		"State":          OrderStateSettled,
		"DataState":      OrderStateSettled,
	}

	err := tx.Table(b.TableName).Where("ThirdId = ? AND Brand = ?", order.ThirdId, b.BrandName).Updates(updateData).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 重新结算 更新注单失败 thirdId=", order.ThirdId, " error=", err.Error())
		return err
	}

	return nil
}

// GetOrder 获取订单
func (b *BaseThirdService) GetOrder(tx *daogorm.DB, thirdId string) (*ThirdOrderModel, error) {
	var order ThirdOrderModel
	err := tx.Table(b.TableName).Where("ThirdId = ? AND Brand = ?", thirdId, b.BrandName).First(&order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 获取订单失败 thirdId=", thirdId, " error=", err.Error())
		return nil, err
	}
	return &order, nil
}

// ProcessBetAndWin 处理下注并派彩（一体化模式游戏）
func (b *BaseThirdService) ProcessBetAndWin(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, betAmount, winAmount float64,
	betReason, winReason int, thirdId, gameId, gameName, betMemo, winMemo, thirdTime, rawData string, betChannelId int) (*thirdGameModel.ThirdOrder, error) {

	// 计算下注金额分配
	realBetAmount, bonusBetAmount, betType, err := b.CalculateBetDistribution(userBalance, betAmount)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 计算下注金额分配失败 thirdId=", thirdId, " userId=", userBalance.UserId, " betAmount=", betAmount, " err=", err.Error())
		return nil, err
	}

	// 扣除下注金额并创建账变记录
	err = b.DeductAmountWithLogs(tx, userBalance, realBetAmount, bonusBetAmount, betReason, betMemo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 扣除下注金额失败 thirdId=", thirdId, " userId=", userBalance.UserId, " realBetAmount=", realBetAmount, " bonusBetAmount=", bonusBetAmount, " err=", err.Error())
		return nil, err
	}

	// 注意：DeductAmountWithLogs 方法已经创建了账变记录，不需要再次创建

	//下注后更新余额最新金额
	userBalance.Amount = userBalance.Amount - betAmount
	userBalance.BonusAmount = userBalance.BonusAmount - bonusBetAmount

	// 计算派彩金额分配
	realWinAmount, bonusWinAmount := b.CalculateWinDistribution(betAmount, winAmount, bonusBetAmount)
	// 增加派彩金额并创建账变记录
	err = b.AddAmountWithLogs(tx, userBalance, realWinAmount, bonusWinAmount, winReason, winMemo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 增加派彩金额失败 thirdId=", thirdId, " userId=", userBalance.UserId, " realWinAmount=", realWinAmount, " bonusWinAmount=", bonusWinAmount, " err=", err.Error())
		return nil, err
	}

	validBet := math.Abs(realWinAmount - realBetAmount)
	if validBet > math.Abs(realBetAmount) {
		validBet = math.Abs(realBetAmount)
	}

	// 创建注单
	order := &thirdGameModel.ThirdOrder{
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		BetChannelId: betChannelId,
		UserId:       userBalance.UserId,
		Brand:        b.BrandName,
		ThirdId:      thirdId,
		GameId:       gameId,
		GameName:     gameName,
		BetAmount:    betAmount,
		WinAmount:    winAmount,
		ValidBet:     validBet,
		ThirdTime:    thirdTime,
		Currency:     b.Currency,
		RawData:      rawData,
		State:        1, // 已开奖状态
		Fee:          0,
		DataState:    OrderStateSettled, // 已开奖状态
		CreateTime:   thirdTime,
		// 新增字段
		BetType:        betType,
		BonusBetAmount: bonusBetAmount,
		BonusWinAmount: bonusWinAmount,
	}

	// 创建预订单
	err = tx.Table(b.TablePreName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", err.Error())
		return nil, err
	}

	// 保存订单ID
	orderId := order.Id

	// 创建正式订单
	order.Id = 0
	err = tx.Table(b.TableName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", err.Error())
		return nil, err
	}

	// 恢复订单ID
	order.Id = orderId

	return order, nil
}

// ProcessBetFeeDeduct 处理费用扣除 处理打赏、小费等支出
func (b *BaseThirdService) ProcessBetFeeDeduct(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, betAmount float64,
	betReason int, thirdId, gameId, gameName, betMemo, thirdTime, rawData string, betChannelId int) (*thirdGameModel.ThirdOrder, error) {

	// 计算下注金额分配
	realBetAmount, bonusBetAmount, betType, err := b.CalculateBetDistribution(userBalance, betAmount)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 计算下注金额分配失败 thirdId=", thirdId, " userId=", userBalance.UserId, " betAmount=", betAmount, " err=", err.Error())
		return nil, err
	}

	// 扣除下注金额并创建账变记录
	err = b.DeductAmountWithLogs(tx, userBalance, realBetAmount, bonusBetAmount, betReason, betMemo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 下注并派彩 扣除下注金额失败 thirdId=", thirdId, " userId=", userBalance.UserId, " realBetAmount=", realBetAmount, " bonusBetAmount=", bonusBetAmount, " err=", err.Error())
		return nil, err
	}

	// 创建注单
	order := &thirdGameModel.ThirdOrder{
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		BetChannelId: betChannelId,
		UserId:       userBalance.UserId,
		Brand:        b.BrandName,
		ThirdId:      thirdId,
		GameId:       gameId,
		GameName:     gameName,
		BetAmount:    betAmount,
		WinAmount:    0,
		ValidBet:     0,
		ThirdTime:    thirdTime,
		Currency:     b.Currency,
		RawData:      rawData,
		State:        1, // 已开奖状态
		Fee:          0,
		DataState:    OrderStateSettled, // 已开奖状态
		CreateTime:   thirdTime,
		// 新增字段
		BetType:        betType,
		BonusBetAmount: bonusBetAmount,
		BonusWinAmount: 0,
	}

	// 创建预订单
	err = tx.Table(b.TablePreName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 费用扣除 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", err.Error())
		return nil, err
	}

	// 保存订单ID
	orderId := order.Id

	// 创建正式订单
	order.Id = 0
	err = tx.Table(b.TableName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 费用扣除 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", err.Error())
		return nil, err
	}

	// 恢复订单ID
	order.Id = orderId

	return order, nil
}

// ProcessFreeRoundAndWin 处理免费旋或奖励类游戏 奖励加到彩金
func (b *BaseThirdService) ProcessFreeRoundAndWin(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, winAmount float64,
	winReason int, thirdId, gameId, gameName, winMemo, thirdTime, rawData string, betChannelId int) (*thirdGameModel.ThirdOrder, error) {

	if winAmount != 0 {
		return nil, errors.New("派奖金额不能是0")
	}
	// 免费旋转奖励全部加到Bonus币中，真金部分为0
	err := b.AddAmountWithLogs(tx, userBalance, 0, winAmount, winReason, winMemo, thirdTime)
	if err != nil {
		logs.Error(b.BrandName+"_single 免费或奖励类游戏派彩 增加派彩金额失败 thirdId=", thirdId, " userId=", userBalance.UserId, " winAmount=", winAmount, " bonusWinAmount=", winAmount, " err=", err)
		return nil, err
	}

	// 创建注单
	order := &thirdGameModel.ThirdOrder{
		SellerId:       userBalance.SellerId,
		ChannelId:      userBalance.ChannelId,
		BetChannelId:   userBalance.ChannelId,
		UserId:         userBalance.UserId,
		Brand:          b.BrandName,
		ThirdId:        thirdId,
		GameId:         gameId,
		GameName:       gameName,
		BetAmount:      0,
		WinAmount:      winAmount,
		ValidBet:       0,
		BonusBetAmount: 0,
		BonusWinAmount: winAmount,
		ThirdTime:      thirdTime,
		Currency:       b.Currency,
		RawData:        rawData,
		State:          1,                 // 已开奖状态
		DataState:      OrderStateSettled, // 已开奖状态
		CreateTime:     thirdTime,
		// 新增字段
		BetType: BetTypeBonus,
	}

	// 创建预订单
	err = tx.Table(b.TablePreName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single  免费或奖励类游戏派彩 创建预设注单失败 thirdId=", thirdId, " orderPre=", order, " error=", err.Error())
		return nil, err
	}

	// 保存订单ID
	orderId := order.Id

	// 创建正式订单
	order.Id = 0
	err = tx.Table(b.TableName).Create(order).Error
	if err != nil {
		logs.Error(b.BrandName+"_single 免费或奖励类游戏派彩 创建正式注单失败 thirdId=", thirdId, " order=", order, " error=", err.Error())
		return nil, err
	}

	// 恢复订单ID
	order.Id = orderId

	return order, nil
}

// ProcessFeeDeduction 处理费用扣除（入场费、小费等）
func (b *BaseThirdService) ProcessFeeDeduction(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance, feeAmount float64,
	reason int, memo, thirdTime string) (float64, float64, error) {

	totalBalance := userBalance.Amount + userBalance.BonusAmount

	if totalBalance < feeAmount {
		return 0, 0, errors.New("玩家余额不足")
	}

	realFeeAmount := 0.0
	bonusFeeAmount := 0.0

	if totalBalance > 0 {
		realFeeRatio := userBalance.Amount / totalBalance
		bonusFeeRatio := userBalance.BonusAmount / totalBalance

		realFeeAmount = feeAmount * realFeeRatio
		bonusFeeAmount = feeAmount * bonusFeeRatio

		// 处理精度问题
		if math.Abs(realFeeAmount+bonusFeeAmount-feeAmount) > 0.01 {
			diff := feeAmount - (realFeeAmount + bonusFeeAmount)
			if realFeeAmount > bonusFeeAmount {
				realFeeAmount += diff
			} else {
				bonusFeeAmount += diff
			}
		}
	}

	// 根据真金和Bonus币比例调整扣除金额
	if realFeeAmount > userBalance.Amount {
		realFeeAmount = userBalance.Amount
		bonusFeeAmount = feeAmount - realFeeAmount
	}

	if bonusFeeAmount > userBalance.BonusAmount {
		bonusFeeAmount = userBalance.BonusAmount
		realFeeAmount = feeAmount - bonusFeeAmount
	}

	// 使用DeductAmountWithLogs方法扣除金额并创建账变记录
	if realFeeAmount > 0 || bonusFeeAmount > 0 {
		err := b.DeductAmountWithLogs(tx, userBalance, realFeeAmount, bonusFeeAmount, reason, memo+" fee deduction", thirdTime)
		if err != nil {
			logs.Error(b.BrandName+"_single 费用扣除失败 userId=", userBalance.UserId, " realAmount=", realFeeAmount, " bonusAmount=", bonusFeeAmount, " err=", err.Error())
			return 0, 0, err
		}
	}

	return realFeeAmount, bonusFeeAmount, nil
}

// GetTotalBalance 获取总余额
func (b *BaseThirdService) GetTotalBalance(userBalance *thirdGameModel.UserBalance) float64 {
	return userBalance.Amount + userBalance.BonusAmount
}

// FormatTime 格式化时间
func (b *BaseThirdService) FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// GetCurrentTime 获取当前时间
func (b *BaseThirdService) GetCurrentTime() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

// DeductAmountWithLogs 扣除金额并创建账变记录
func (b *BaseThirdService) DeductAmountWithLogs(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance,
	realAmount, bonusAmount float64, reason int, memo, thirdTime string) error {

	// 记录原始金额
	originalAmount := userBalance.Amount
	originalBonusAmount := userBalance.BonusAmount

	// 扣除真金金额
	if realAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userBalance.UserId, realAmount).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", realAmount),
			})
		if result.Error != nil {
			logs.Error(b.BrandName+"_single 扣除真金失败 userId=", userBalance.UserId, " amount=", realAmount, " err=", result.Error.Error())
			return result.Error
		}
		if result.RowsAffected <= 0 {
			logs.Error(b.BrandName+"_single 扣除真金失败 userId=", userBalance.UserId, " amount=", realAmount)
			return errors.New("扣除真金失败")
		}
	}

	// 扣除Bonus币金额
	if bonusAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ? AND BonusAmount >= ?", userBalance.UserId, bonusAmount).
			Updates(map[string]interface{}{
				"BonusAmount": daogorm.Expr("BonusAmount - ?", bonusAmount),
			})
		if result.Error != nil {
			logs.Error(b.BrandName+"_single 扣除Bonus币失败 userId=", userBalance.UserId, " amount=", bonusAmount, " err=", result.Error.Error())
			return result.Error
		}
		if result.RowsAffected <= 0 {
			logs.Error(b.BrandName+"_single 扣除Bonus币失败 userId=", userBalance.UserId, " amount=", bonusAmount)
			return errors.New("扣除Bonus币失败")
		}
	}

	// 计算扣除后的金额
	afterAmount := originalAmount
	afterBonusAmount := originalBonusAmount
	if realAmount > 0 {
		afterAmount = originalAmount - realAmount
	}
	if bonusAmount > 0 {
		afterBonusAmount = originalBonusAmount - bonusAmount
	}

	// 创建合并账变记录（同时记录真金和彩金的变动）
	err := b.CreateCombinedAmountChangeLog(
		tx,
		userBalance.UserId,
		userBalance.SellerId,
		userBalance.ChannelId,
		originalAmount,
		-realAmount,
		afterAmount,
		originalBonusAmount,
		-bonusAmount,
		afterBonusAmount,
		reason,
		memo,
		thirdTime,
	)
	if err != nil {
		logs.Error(b.BrandName+"_single 创建合并账变记录失败 userId=", userBalance.UserId, " err=", err.Error())
		return err
	}

	return nil
}

// AddAmountWithLogs 增加金额并创建账变记录
func (b *BaseThirdService) AddAmountWithLogs(tx *daogorm.DB, userBalance *thirdGameModel.UserBalance,
	realAmount, bonusAmount float64, reason int, memo, thirdTime string) error {

	// 记录原始金额
	originalAmount := userBalance.Amount
	originalBonusAmount := userBalance.BonusAmount

	// 增加真金金额
	if realAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ?", userBalance.UserId).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", realAmount),
			})
		if result.Error != nil {
			logs.Error(b.BrandName+"_single 增加真金失败 userId=", userBalance.UserId, " amount=", realAmount, " err=", result.Error.Error())
			return result.Error
		}
		if result.RowsAffected <= 0 {
			logs.Error(b.BrandName+"_single 增加真金失败 userId=", userBalance.UserId, " amount=", realAmount)
			return errors.New("增加真金失败")
		}
	}

	// 增加Bonus币金额
	if bonusAmount > 0 {
		result := tx.Table("x_user").Where("UserId = ?", userBalance.UserId).
			Updates(map[string]interface{}{
				"BonusAmount": daogorm.Expr("BonusAmount + ?", bonusAmount),
			})
		if result.Error != nil {
			logs.Error(b.BrandName+"_single 增加Bonus币失败 userId=", userBalance.UserId, " amount=", bonusAmount, " err=", result.Error.Error())
			return result.Error
		}
		if result.RowsAffected <= 0 {
			logs.Error(b.BrandName+"_single 增加Bonus币失败 userId=", userBalance.UserId, " amount=", bonusAmount)
			return errors.New("增加Bonus币失败")
		}
	}

	// 计算增加后的金额
	afterAmount := originalAmount
	afterBonusAmount := originalBonusAmount
	if realAmount > 0 {
		afterAmount = originalAmount + realAmount
	}
	if bonusAmount > 0 {
		afterBonusAmount = originalBonusAmount + bonusAmount
	}

	// 创建合并账变记录（同时记录真金和彩金的变动）
	err := b.CreateCombinedAmountChangeLog(
		tx,
		userBalance.UserId,
		userBalance.SellerId,
		userBalance.ChannelId,
		originalAmount,
		realAmount,
		afterAmount,
		originalBonusAmount,
		bonusAmount,
		afterBonusAmount,
		reason,
		memo,
		thirdTime,
	)
	if err != nil {
		logs.Error(b.BrandName+"_single 创建合并账变记录失败 userId=", userBalance.UserId, " err=", err.Error())
		return err
	}

	return nil
}
