server:
  env: dev
  project: xgotest
  ipdata: ./config/ipdata.dat #qqwry数据文件地址
  snowflakenode: 1 #雪花算法节点
  http:
    port: 4034
  rpc:
    port: 7564 #prc监听端口
    test: 127.0.0.1:7564
  dbx:
    host: 127.0.0.1
    port: 3306
    user: root
    password: root123456
    database: x_test
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: true
  db:
    host: *************
    port: 14578
    user: userbu
    password: aw9#gf*S7fT1P
    database: x_hash_game
    connmaxidletime: 20 #最大待机时间
    connmaxlifetime: 25 #连接最长生命周期
    connmaxidle: 10 #最大等待连接数
    connmaxopen: 100 #最大打开连接数
    logmode: false
  redis:
    host: 127.0.0.1
    port: 6379
    db: 14
    password: root #tTDsAgrPGf3GU2sw
    maxidle: 10 #最大的空闲连接数，表示即使没有redis连接时依然可以保持N个空闲的连接，而不被清除，随时处于待命状态。
    maxactive: 100 #最大的激活连接数，表示同时最多有N个连接
    idletimeout: 60 #最大的空闲连接等待时间，超过此时间后，空闲连接将被关闭
