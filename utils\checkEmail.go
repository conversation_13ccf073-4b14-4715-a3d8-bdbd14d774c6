package utils

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
)

// VerimailVerifier 使用 verimail.io API 验证邮箱地址
type VerimailVerifier struct {
	APIKey  string
	BaseURL string
}

// VerimailResponse verimail.io API 响应结构
type VerimailResponse struct {
	Status      string `json:"status"`
	Email       string `json:"email"`
	Result      string `json:"result"`
	Deliverable bool   `json:"deliverable"`
	User        string `json:"user"`
	Domain      string `json:"domain"`
	DidYouMean  string `json:"did_you_mean"`
}

// NewVerimailVerifier 创建一个新的 VerimailVerifier 实例
func NewVerimailVerifier() *VerimailVerifier {
	return &VerimailVerifier{
		APIKey:  "********************************",
		BaseURL: "https://api.verimail.io/v3/verify",
	}
}

// Verify 验证邮箱地址是否有效
func (v *VerimailVerifier) Verify(email string) (bool, error) {
	// 构建请求URL
	params := url.Values{}
	params.Add("key", v.APIKey)
	params.Add("email", email)

	// 发送请求
	resp, err := http.Get(fmt.Sprintf("%s?%s", v.BaseURL, params.Encode()))
	if err != nil {
		return false, fmt.Errorf("请求Verimail API失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var result VerimailResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return false, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查API错误
	if result.Status != "success" {
		return false, fmt.Errorf("API错误: 验证邮箱失败")
	}

	// 根据结果返回
	// deliverable 表示邮箱可以接收邮件
	// 但我们需要排除 catch_all 和 disposable 类型的邮箱
	if result.Result == "catch_all" || result.Result == "disposable" {
		return false, nil
	}

	return result.Deliverable, nil
}
