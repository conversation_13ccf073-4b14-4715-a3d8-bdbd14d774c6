package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/active"
	"xserver/gormgen/xHashGame/dao"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/model"
	"xserver/server"
	"xserver/utils"

	"github.com/golang-module/carbon/v2"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
	"github.com/spf13/viper"
	"github.com/zhms/xgo/xgo"
	gorm2 "gorm.io/gorm"
)

const KActiveIdEachBetFanShui int = 1
const KActiveIdRechange int = 2
const KActiveIdHaXiBreakout int = 3
const KActiveIdHaXiBreakout_15 int = 15
const KActiveIdHaXiBreakout_16 int = 16
const KActiveIdHaXiBreakout_17 int = 17
const KActiveIdHaXiBreakout_18 int = 18
const KActiveIdQipaiBreakout int = 4
const KActiveIdDianziBreakout int = 5
const KActiveIdRescue int = 6
const KActiveIdInvite int = 7
const KActiveIdVipFanShui int = 8
const KActiveIdXianglongfuhu = 9
const KActiveIdLucklySpin = 100 //幸运转盘

var huodonginfo = `[{"id":1,"name":"能量补给站","state":2,"checked":false},{"id":9,"name":"降龙伏虎","state":2,"checked":false},{"id":2,"name":"充值任务","state":2,"checked":false},{"id":3,"name":"哈希闯关","state":2,"checked":false},{"id":4,"name":"棋牌闯关","state":2,"checked":false},{"id":5,"name":"电子闯关","state":2,"checked":false},{"id":6,"name":"救援金","state":2,"checked":false},{"id":7,"name":"邀请好友","state":2,"checked":false},{"id":8,"name":"VIP返水","state":2,"checked":true},{"id":10,"name":"每日首存次日送","state":2,"checked":true},{"id":11,"name":"电子游戏每周大派送","state":2,"checked":true}]`

type ActiveController struct {
}

func (c *ActiveController) Init() {
	gropu := server.Http().NewGroup("/api/active")
	{
		gropu.PostNoAuth("/list", c.list)
		gropu.PostNoAuth("/detail", c.detail)
		gropu.PostNoAuth("/queuedmsg", c.queuedmsg)
		gropu.Post("/get_reward", c.get_reward)
		gropu.Post("/reward_list", c.reward_list)

		//新活动api
		gropu.PostByNoAuthMayUserToken("/active_define", c.active_define)
		gropu.PostNoAuth("/active_info", c.active_info)
		gropu.Post("/active_info_user", c.active_info_user)
		gropu.Post("/active_apply", c.active_apply)
		gropu.Post("/active_reward_list", c.active_reward_list)
		gropu.PostNoAuth("/home_carousel_list", c.home_carousel_list)
		gropu.PostNoAuth("/v2/home_carousel_list", c.home_carousel_v2_list)
		gropu.PostNoAuth("/spin_withward", c.spin_withward)
		gropu.PostNoAuth("/spin_sharelist", c.spin_sharelist)
	}
}

func (c *ActiveController) spin_withward(ctx *abugo.AbuHttpContent) {
	data, _ := server.XRedis().LRange("active_spin_withward", 0, -1)
	ctx.RespOK(data)
}

func (c *ActiveController) spin_sharelist(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Country  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Country == "" {
		data, _ := server.Db().Query("SELECT Id,PhoneNum FROM x_spin_share_account ORDER BY RAND(MD5(CONCAT(id, RAND()))) LIMIT 20", []interface{}{})
		for i := 0; i < len(*data); i++ {
			server.Db().Query("update x_spin_share_account set ShowTimes = ShowTimes + 1 where id = ?", []interface{}{(*data)[i]["Id"]})
		}
		ctx.RespOK(data)
	} else {
		data, _ := server.Db().Query("SELECT Id,PhoneNum FROM x_spin_share_account where Country = ? ORDER BY RAND(MD5(CONCAT(id, RAND()))) LIMIT 20", []interface{}{reqdata.Country})
		for i := 0; i < len(*data); i++ {
			server.Db().Query("update x_spin_share_account set ShowTimes = ShowTimes + 1 where id = ?", []interface{}{(*data)[i]["Id"]})
		}
		ctx.RespOK(data)
	}
}

func (c *ActiveController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		LangId   int
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.LangId == 0 {
		reqdata.LangId = 1
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	rediskey := fmt.Sprintf("%s:%s:active:%d:%d", server.Project(), server.Module(), SellerId, reqdata.LangId)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		tm := time.Now()
		tstr := tm.Format(utils.TimeFormatStr)
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", SellerId, 0)
		where.Add("and", "State", "=", 1, 0)
		where.Add("and", "ChannelId", "=", ChannelId, 0)
		where.Add("and", "LangId", "=", reqdata.LangId, 0)
		where.Add("and", "StartTime", "<=", tstr, "")
		where.Add("and", "EndTime", ">=", tstr, "")
		fields := "Id,Sort,Title,TitleImg,StartTime,Endtime,CreateTime,BtnState"
		presult, err := server.Db().Table("x_active").Select(fields).Where(where).OrderBy("sort desc").GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		result := *presult
		for i := 0; i < len(result); i++ {
			result[i]["CreateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["CreateTime"]))
			result[i]["StartTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["StartTime"]))
			result[i]["Endtime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["Endtime"]))
			if result[i]["TitleImg"] != nil && len(abugo.GetStringFromInterface(result[i]["TitleImg"])) > 0 {
				result[i]["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(result[i]["TitleImg"])
			}
		}
		ctx.RespOK(result)
		server.Redis().SetEx(rediskey, 10, result)
	}
}

func (c *ActiveController) detail(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Id       int `validate:"required"`
		SellerId int `validate:"required"` //运营商
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, "")
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", SellerId, 0)
	where.Add("and", "Id", "=", reqdata.Id, 0)

	fields := "Id,Title,TitleImg,StartTime,Endtime,CreateTime,Content,ContentImg,BtnState"
	presult, err := server.Db().Table("x_active").Select(fields).Where(where).OrderBy("sort desc").GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespErrString(presult == nil, &errcode, "活动不存在") {
		return
	}
	result := *presult
	result["CreateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result["CreateTime"]))
	result["StartTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result["StartTime"]))
	result["Endtime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result["Endtime"]))
	if result["TitleImg"] != nil && len(abugo.GetStringFromInterface(result["TitleImg"])) > 0 {
		result["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(result["TitleImg"])
	}
	if result["ContentImg"] != nil && len(abugo.GetStringFromInterface(result["ContentImg"])) > 0 {
		result["ContentImg"] = server.ImageUrl() + abugo.GetStringFromInterface(result["ContentImg"])
	}
	ctx.RespOK(result)
}

func (c *ActiveController) queuedmsg(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		LangId   int
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.LangId == 0 {
		reqdata.LangId = 1
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "=", SellerId, 0)
	where.Add("and", "ChannelId", "= ", ChannelId, 0)
	where.Add("and", "State", "=", 1, 0)
	where.Add("and", "LangId", "=", reqdata.LangId, 0)
	pdata, _ := server.Db().Table("x_queuedmsg").Where(where).OrderBy("Sort desc").GetList()
	ctx.RespOK(*pdata)
}

func (c *ActiveController) get_reward(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		ActiveId int
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if token.ChannelId == 0 || token.SellerId == 0 {
		token.ChannelId, token.SellerId = server.GetChannel(ctx, reqdata.Host)
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")
	where.Add("and", "ActiveId", "=", reqdata.ActiveId, "")
	where.Add("and", "CreateTime", ">=", abugo.GetLocalDate(), "")
	tm := time.Now().Add(time.Hour * 24)
	where.Add("and", "CreateTime", "<", tm.In(time.Local).Format("2006-01-02"), "")
	record, _ := server.Db().Table("x_active_reward").Where(where).GetOne()
	if record != nil {
		ctx.RespErrString(true, &errcode, "今日已申请")
		return
	}
	where = abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", reqdata.ActiveId, "")
	where.Add("and", "State", "=", 1, "")
	activedata, _ := server.Db().Table("x_active").Where(where).GetOne()
	if activedata == nil {
		ctx.RespErrString(true, &errcode, "活动不存在")
		return
	}
	activename := abugo.GetStringFromInterface((*activedata)["Title"])
	server.Db().Conn().Exec("Insert into x_active_reward(SellerId,ChannelId,UserId,ActiveId,ActiveName)values(?,?,?,?,?)", token.SellerId, token.ChannelId, token.UserId, reqdata.ActiveId, activename)
	ctx.RespOK()
}

func (c *ActiveController) reward_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "= ", token.UserId, "")
	total, result := server.Db().Table("x_active_reward").OrderBy("id desc").Where(where).PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", result)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 新活动api
func (c *ActiveController) active_define(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Host      string
		Lang      int
		AgentCode string
		Cat       int
	}
	errcode := 0
	reqdata := RequestData{Lang: 1}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	// token := server.GetToken(ctx)
	// if token != nil {
	// 	ChannelId, SellerId = token.ChannelId, token.SellerId
	// }

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	agent, _ := server.Db().Query("select * from x_agent_independence where Host = ?", []interface{}{host})
	// 推广链接
	if len(reqdata.AgentCode) > 0 {
		promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
		indepenAgent, err := server.Db().Query("select * from x_agent_independence where PromotionHost = ?", []interface{}{promotionHost})
		if err == nil && len(*indepenAgent) > 0 {
			agent = indepenAgent
		}
	}

	query := server.Db().Table("x_active_define a").
		Select("a.*, b.Sort,b.TopSort").
		Join(fmt.Sprintf("left join x_active_define_sort b on a.Id = b.Id and b.Lang = %v", reqdata.Lang))
		// Where(where)

	if reqdata.Cat > 0 {
		query.WhereRaw(fmt.Sprintf("a.SellerId = %d and a.ChannelId = %d and a.State = 1 and JSON_CONTAINS(Cats, '%d')", SellerId, ChannelId, reqdata.Cat))
	} else {
		query.WhereRaw(fmt.Sprintf("a.SellerId = %d and a.ChannelId = %d and a.State = 1", SellerId, ChannelId))
	}

	result, err := query.OrderBy("b.Sort desc, b.Id desc").GetList()

	if ctx.RespErr(err, &errcode) {
		return
	}

	for i := 0; i < len(*result); i++ {
		if (*result)[i]["TitleImg"] != nil && len(abugo.GetStringFromInterface((*result)[i]["TitleImg"])) > 0 {
			(*result)[i]["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface((*result)[i]["TitleImg"])
		}
		if (*result)[i]["TopImg"] != nil && len(abugo.GetStringFromInterface((*result)[i]["TopImg"])) > 0 {
			(*result)[i]["TopImg"] = server.ImageUrl() + abugo.GetStringFromInterface((*result)[i]["TopImg"])
		}
		if (*result)[i]["TopImgEn"] != nil && len(abugo.GetStringFromInterface((*result)[i]["TopImgEn"])) > 0 {
			(*result)[i]["TopImgEn"] = server.ImageUrl() + abugo.GetStringFromInterface((*result)[i]["TopImgEn"])
		}
		if (*result)[i]["TitleImgCn"] != nil && len(abugo.GetStringFromInterface((*result)[i]["TitleImgCn"])) > 0 {
			(*result)[i]["TitleImgCn"] = server.ImageUrl() + abugo.GetStringFromInterface((*result)[i]["TitleImgCn"])
		}
		if (*result)[i]["TitleImgEn"] != nil && len(abugo.GetStringFromInterface((*result)[i]["TitleImgEn"])) > 0 {
			(*result)[i]["TitleImgEn"] = server.ImageUrl() + abugo.GetStringFromInterface((*result)[i]["TitleImgEn"])
		}

		// 将TitleImgLangJson转换成map
		if (*result)[i]["TitleImgLang"] != nil {
			TitleImgLangJson := abugo.GetStringFromInterface((*result)[i]["TitleImgLang"])
			TitleImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleImgLangJson), &TitleImgLangMap)
			titleImg := abugo.GetStringFromInterface(TitleImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if titleImg != "" {
				(*result)[i]["TitleImg"] = server.ImageUrl() + titleImg
			}

			if reqdata.Lang > 2 && titleImg == "" {
				(*result)[i]["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["2"])
			}

			if reqdata.Lang == 13 && titleImg == "" {
				(*result)[i]["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["1"])
			}

		}

		if (*result)[i]["TopImgLang"] != nil {
			TopImgLangJson := abugo.GetStringFromInterface((*result)[i]["TopImgLang"])
			TopImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TopImgLangJson), &TopImgLangMap)
			topImg := abugo.GetStringFromInterface(TopImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if topImg != "" {
				(*result)[i]["TopImg"] = server.ImageUrl() + topImg
			}

			if reqdata.Lang > 2 && topImg == "" {
				(*result)[i]["TopImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TopImgLangMap["2"])
			}

			if reqdata.Lang == 13 && topImg == "" {
				(*result)[i]["TopImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TopImgLangMap["1"])
			}

		}
		(*result)[i]["DetailImg"] = ""
		if (*result)[i]["DetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*result)[i]["DetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*result)[i]["DetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*result)[i]["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*result)[i]["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}
		(*result)[i]["PcDetailImg"] = ""
		if (*result)[i]["PcDetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*result)[i]["PcDetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*result)[i]["PcDetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*result)[i]["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*result)[i]["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}

		if (*result)[i]["TitleLang"] != nil {
			TitleLangJson := abugo.GetStringFromInterface((*result)[i]["TitleLang"])
			TitleLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleLangJson), &TitleLangMap)
			(*result)[i]["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap[abugo.GetStringFromInterface(reqdata.Lang)])

			if (reqdata.Lang > 2 && reqdata.Lang < 13 || reqdata.Lang > 13) && (*result)[i]["TitleLang"] == "" {
				(*result)[i]["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["2"])
			}

			if reqdata.Lang == 13 && (*result)[i]["TitleLang"] == "" {
				(*result)[i]["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["1"])
			}

		}

		delete((*result)[i], "TitleImgLang")
		delete((*result)[i], "TopImgLang")
		delete((*result)[i], "DetailImgLang")
		delete((*result)[i], "PcDetailImgLang")

		(*result)[i]["seconds"] = 0
		ActiveId := abugo.GetInt64FromInterface((*result)[i]["ActiveId"])
		// 欧洲杯
		if ActiveId == utils.KActiveIdOuZhouBeiTianTianSong ||
			ActiveId == utils.KActiveIdOuZhouBeiMeiRiJiaZeng ||
			ActiveId == utils.KActiveIdOuZhouBeiRongYaoZhiZheng ||
			ActiveId == utils.KActiveIdOuZhouBeiZhuLiOuJue ||
			ActiveId == utils.KActiveIdOuZhouBeiTiyuZhouLiuShui {
			var EffectStartTime int64
			var EffectEndTime int64
			var seconds float64
			if (*result)[i]["EffectStartTime"] != nil {
				EffectStartTime = abugo.GetInt64FromInterface((*result)[i]["EffectStartTime"])
			}
			if (*result)[i]["EffectEndTime"] != nil {
				EffectEndTime = abugo.GetInt64FromInterface((*result)[i]["EffectEndTime"])
			}
			if EffectStartTime > 0 && EffectEndTime > 0 {
				now := carbon.Parse(carbon.Now().String()).StdTime()
				EffectStartT := carbon.Parse(carbon.CreateFromTimestampMilli(EffectStartTime).String()).StdTime()
				EffectEndT := carbon.Parse(carbon.CreateFromTimestampMilli(EffectEndTime).String()).StdTime()
				if now.Before(EffectEndT) { // 未结束
					if now.After(EffectStartT) { // 已经开始
						seconds = EffectEndT.Sub(now).Seconds()
					} else { // 未开始
						seconds = EffectEndT.Sub(EffectStartT).Seconds()
					}
				}
				(*result)[i]["seconds"] = seconds
			}
		}
	}

	if len(*agent) == 0 || abugo.GetInt64FromInterface((*agent)[0]["IsSelfActive"]) != 1 {
		ctx.RespOK(result)
	} else {
		if abugo.GetInt64FromInterface((*agent)[0]["IsSelfActive"]) != 1 {
			ctx.RespOK(result)
			return
		}
		AceiveInfo := abugo.GetStringFromInterface((*agent)[0]["ActiveInfo"])
		if AceiveInfo == "" {
			AceiveInfo = huodonginfo
		}
		huodongobj := []interface{}{}
		json.Unmarshal([]byte(AceiveInfo), &huodongobj)
		retdata := []map[string]interface{}{}
		for i := 0; i < len(*result); i++ {
			item := (*result)[i]
			for j := 0; j < len(huodongobj); j++ {
				if abugo.InterfaceToInt(item["ActiveId"]) == abugo.InterfaceToInt(huodongobj[j].(map[string]interface{})["id"]) {
					if abugo.InterfaceToInt(huodongobj[j].(map[string]interface{})["state"]) == 1 {
						retdata = append(retdata, item)
					}
				}
			}
		}
		ctx.RespOK(retdata)
	}
}

func (c *ActiveController) active_info(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		ActiveId int `validate:"required"`
		Host     string
		Lang     int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)

	if reqdata.Lang == 0 {
		reqdata.Lang = 1
	}

	if reqdata.ActiveId == KActiveIdVipFanShui {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "= ", SellerId, 0)
		where.Add("and", "ChannelId", "= ", ChannelId, 0)
		result, err := server.Db().Table("x_vip_define").Where(where).OrderBy("VipLevel desc").GetOne()
		if ctx.RespErr(err, &errcode) {
			return
		}
		if result != nil {
			data := make([]map[string]interface{}, 0, 11)
			for i := 0; i < 11; i++ {
				tmp := make(map[string]interface{})
				tmp["Level"] = i + 1
				tmp["LimitValue"] = 0
				tmp["RewardValue"] = 0
				tmp["State"] = (*result)["State"]
				data = append(data, tmp)
			}
			//1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery 8Texas 9LowLottery 10CryptoMarket 11HexLottery(香港六合彩)
			data[0]["RewardValue"] = (*result)["RewardRateHaXi"]
			data[1]["RewardValue"] = (*result)["RewardRateXiaoYouXi"]
			data[2]["RewardValue"] = (*result)["RewardRateDianZhi"]
			data[3]["RewardValue"] = (*result)["RewardRateQiPai"]
			data[4]["RewardValue"] = (*result)["RewardRateLive"]
			data[5]["RewardValue"] = (*result)["RewardRateSport"]
			data[6]["RewardValue"] = (*result)["RewardRateLottery"]
			data[7]["RewardValue"] = (*result)["RewardRateTexas"]
			data[8]["RewardValue"] = (*result)["RewardRateLowLottery"]
			data[9]["RewardValue"] = (*result)["RewardRateCryptoMarket"]
			data[10]["RewardValue"] = (*result)["RewardRateHexLottery"]
			ctx.Put("data", data)
		}
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "= ", SellerId, 0)
		where.Add("and", "ChannelId", "= ", ChannelId, 0)
		where.Add("and", "ActiveId", "= ", reqdata.ActiveId, 0)
		result, err := server.Db().Table("x_active_info").OrderBy("Level asc").Where(where).GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", result)
	}

	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", SellerId, 0)
	where.Add("and", "ChannelId", "= ", ChannelId, 0)
	where.Add("and", "ActiveId", "= ", reqdata.ActiveId, 0)
	activeInfo, err := server.Db().Table("x_active_define").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if activeInfo != nil {
		if (*activeInfo)["TitleImg"] != nil && len(abugo.GetStringFromInterface((*activeInfo)["TitleImg"])) > 0 {
			(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface((*activeInfo)["TitleImg"])
		}

		if (*activeInfo)["TitleImgLang"] != nil {
			TitleImgLangJson := abugo.GetStringFromInterface((*activeInfo)["TitleImgLang"])
			TitleImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleImgLangJson), &TitleImgLangMap)
			titleImg := abugo.GetStringFromInterface(TitleImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if titleImg != "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + titleImg
			}

			if reqdata.Lang > 2 && titleImg == "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["2"])
			}

			if reqdata.Lang == 13 && titleImg == "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["1"])
			}

		}

		(*activeInfo)["DetailImg"] = ""
		if (*activeInfo)["DetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*activeInfo)["DetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}

		(*activeInfo)["PcDetailImg"] = ""
		if (*activeInfo)["PcDetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*activeInfo)["PcDetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}

		if (*activeInfo)["TitleLang"] != nil {
			TitleLangJson := abugo.GetStringFromInterface((*activeInfo)["TitleLang"])
			TitleLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleLangJson), &TitleLangMap)
			(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap[abugo.GetStringFromInterface(reqdata.Lang)])

			if (reqdata.Lang > 2 && reqdata.Lang < 13 || reqdata.Lang > 13) && (*activeInfo)["TitleLang"] == "" {
				(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["2"])
			}

			if reqdata.Lang == 13 && (*activeInfo)["TitleLang"] == "" {
				(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["1"])
			}

		}
		if reqdata.ActiveId == utils.KActiveIdCiRiSong {
			cStr := (*activeInfo)["Config"]
			if cStr != nil && cStr != "" {
				str, ok := cStr.(string)
				if ok {
					ciriConf := model.ACiRiConfig{}
					err = json.Unmarshal([]byte(str), &ciriConf)
					if err == nil {
						(*activeInfo)["CiRiConfig"] = ciriConf
					}
				}
			}
		}
		if reqdata.ActiveId == utils.KActiveIdCumulativeWeeklyRecharge {
			var result model.CumulativeWeeklyRechargeRes
			var configData []model.CumulativeWeeklyRechargeParam
			cStr := (*activeInfo)["Config"]
			if cStr != nil && cStr != "" {
				str, ok := cStr.(string)
				if ok {
					err = json.Unmarshal([]byte(str), &configData)
					if err != nil {
						if ctx.RespErr(errors.New("活动配置有误"), &errcode) {
							return
						}
					}
				}
			}
			now := carbon.Parse(carbon.Now().String()).StdTime()
			// 这周还剩多少秒
			endTime := carbon.Parse(carbon.Now().EndOfWeek().String()).StdTime()
			diff := endTime.Sub(now)
			result.Seconds = diff.Seconds()
			result.TotalRecharge = 0
			result.List = configData
			ctx.Put("data", result)
		}
		if reqdata.ActiveId == utils.KActiveIdDianziMeiZhouSong || reqdata.ActiveId == utils.KActiveIdZhenrenMeiZhouSong || reqdata.ActiveId == utils.KActiveIdQiPaiMeiZhouSong {
			var result model.WeekendBreakThroughRes
			var configData []model.WeekendBreakThroughParam
			cStr := (*activeInfo)["Config"]
			if cStr != nil && cStr != "" {
				str, ok := cStr.(string)
				if ok {
					err = json.Unmarshal([]byte(str), &configData)
					if err != nil {
						if ctx.RespErr(errors.New("活动配置有误"), &errcode) {
							return
						}
					}
				}
			}
			result.List = configData
			ctx.Put("data", result)
		}
		if reqdata.ActiveId != utils.KActiveIdCumulativeWeeklyRecharge &&
			reqdata.ActiveId != utils.KActiveIdSignReward &&
			reqdata.ActiveId != utils.KActiveIdRecommendFriendReward &&
			reqdata.ActiveId != utils.KActiveIdDianziMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdZhenrenMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdQiPaiMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_15 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_16 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_17 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_18 &&
			reqdata.ActiveId != utils.KActiveIdQipaiBreakout &&
			reqdata.ActiveId != utils.KActiveIdDianziBreakout &&
			reqdata.ActiveId != utils.KActiveIdZhenrenBreakout &&
			reqdata.ActiveId != utils.RegisterGift &&
			reqdata.ActiveId != utils.FirstDepositGift &&
			reqdata.ActiveId != utils.MultipleDepositGift {
			delete(*activeInfo, "Config")
		}
	}
	// 如果是首充活动，返回用户的活动资格检查结果
	if reqdata.ActiveId == utils.FirstDepositGift {
		ctx.Put("ActiveQualifications", map[string]interface{}{
			"IsEligible": true,
			"ErrorCode":  utils.ActiveISuccess,
			"ErrorMsg":   "",
		})
	}
	ctx.Put("active", activeInfo)
	ctx.RespOK()
}

func (c *ActiveController) active_info_user(ctx *abugo.AbuHttpContent) {
	defer recover()
	now := time.Now()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		ActiveId int `validate:"required"`
		IsAgent  int
		Host     string
		Lang     int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	sql := ""

	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", sellerId, 0)
	where.Add("and", "ChannelId", "= ", channelId, 0)
	where.Add("and", "ActiveId", "= ", reqdata.ActiveId, 0)
	activeInfo, err := server.Db().Table("x_active_define").Where(where).GetOne()
	if ctx.RespErr(err, &errcode) {
		return
	}

	if reqdata.Lang == 0 {
		reqdata.Lang = 1
	}
	if activeInfo != nil {
		if (*activeInfo)["TitleImg"] != nil && len(abugo.GetStringFromInterface((*activeInfo)["TitleImg"])) > 0 {
			(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface((*activeInfo)["TitleImg"])
		}

		// 将TitleImgLangJson转换成map
		if (*activeInfo)["TitleImgLang"] != nil {
			TitleImgLangJson := abugo.GetStringFromInterface((*activeInfo)["TitleImgLang"])
			TitleImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleImgLangJson), &TitleImgLangMap)
			titleImg := abugo.GetStringFromInterface(TitleImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if titleImg != "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + titleImg
			}

			if reqdata.Lang > 2 && titleImg == "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["2"])
			}

			if reqdata.Lang == 13 && titleImg == "" {
				(*activeInfo)["TitleImg"] = server.ImageUrl() + abugo.GetStringFromInterface(TitleImgLangMap["1"])
			}

		}

		(*activeInfo)["DetailImg"] = ""
		if (*activeInfo)["DetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*activeInfo)["DetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*activeInfo)["DetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}

		(*activeInfo)["PcDetailImg"] = ""
		if (*activeInfo)["PcDetailImgLang"] != nil {
			DetailImgLangJson := abugo.GetStringFromInterface((*activeInfo)["PcDetailImgLang"])
			DetailImgLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(DetailImgLangJson), &DetailImgLangMap)
			detailImg := abugo.GetStringFromInterface(DetailImgLangMap[abugo.GetStringFromInterface(reqdata.Lang)])
			if detailImg != "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + detailImg
			}

			if reqdata.Lang > 2 && detailImg == "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["2"])
			}

			if reqdata.Lang == 13 && detailImg == "" {
				(*activeInfo)["PcDetailImg"] = server.ImageUrl() + abugo.GetStringFromInterface(DetailImgLangMap["1"])
			}
		}

		if (*activeInfo)["TitleLang"] != nil {
			TitleLangJson := abugo.GetStringFromInterface((*activeInfo)["TitleLang"])
			TitleLangMap := make(map[string]interface{})
			json.Unmarshal([]byte(TitleLangJson), &TitleLangMap)
			(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap[abugo.GetStringFromInterface(reqdata.Lang)])

			if (reqdata.Lang > 2 && reqdata.Lang < 13 || reqdata.Lang > 13) && (*activeInfo)["TitleLang"] == "" {
				(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["2"])
			}

			if reqdata.Lang == 13 && (*activeInfo)["TitleLang"] == "" {
				(*activeInfo)["TitleLang"] = abugo.GetStringFromInterface(TitleLangMap["1"])
			}

		}

		delete((*activeInfo), "TitleImgLang")
		delete((*activeInfo), "TopImgLang")
		delete((*activeInfo), "DetailImgLang")
		delete((*activeInfo), "PcDetailImgLang")

		if reqdata.ActiveId == utils.KActiveIdCiRiSong {
			cStr := (*activeInfo)["Config"]
			if cStr != nil && cStr != "" {
				str, ok := cStr.(string)
				if ok {
					ciriConf := model.ACiRiConfig{}
					err = json.Unmarshal([]byte(str), &ciriConf)
					if err == nil {
						(*activeInfo)["CiRiConfig"] = ciriConf
					}
				}
			}
		}
		if reqdata.ActiveId != KActiveIdLucklySpin &&
			reqdata.ActiveId != utils.KActiveIdCumulativeWeeklyRecharge &&
			reqdata.ActiveId != utils.KActiveIdSignReward &&
			reqdata.ActiveId != utils.KActiveIdRecommendFriendReward &&
			reqdata.ActiveId != utils.KActiveIdDianziMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdZhenrenMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdQiPaiMeiZhouSong &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_15 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_16 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_17 &&
			reqdata.ActiveId != utils.KActiveIdHaXiBreakout_18 &&
			reqdata.ActiveId != utils.KActiveIdQipaiBreakout &&
			reqdata.ActiveId != utils.KActiveIdDianziBreakout &&
			reqdata.ActiveId != utils.KActiveIdZhenrenBreakout &&
			reqdata.ActiveId != utils.KDIYActiveIdRecharge &&
			reqdata.ActiveId != utils.RegisterGift &&
			reqdata.ActiveId != utils.FirstDepositGift &&
			reqdata.ActiveId != utils.MultipleDepositGift {
			delete(*activeInfo, "Config")
		}
	}
	ctx.Put("active", activeInfo)

	if reqdata.ActiveId == KActiveIdVipFanShui {
		//获取用户当前VIPLevel对应的VIP日返水比例
		sql = fmt.Sprintf("select * from x_vip_define where SellerId=%d and ChannelId=%d and VipLevel=(select VipLevel from x_vip_info where UserId=%d)",
			sellerId, channelId, token.UserId)
		result, err := server.Db().Query(sql, nil)
		if ctx.RespErr(err, &errcode) {
			return
		}
		data := make([]map[string]interface{}, 0, 11)
		for i := 0; i < 11; i++ {
			tmp := make(map[string]interface{})
			tmp["Level"] = i + 1 //1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery 8texas
			tmp["LimitValue"] = 0
			tmp["RewardValue"] = 0
			tmp["RewardAmount"] = 0
			tmp["State"] = 1
			data = append(data, tmp)
		}
		if result != nil && len(*result) > 0 {
			data[0]["RewardValue"] = (*result)[0]["RewardRateHaXi"] //1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery 8texas 11HexLottery香港六合彩
			data[1]["RewardValue"] = (*result)[0]["RewardRateXiaoYouXi"]
			data[2]["RewardValue"] = (*result)[0]["RewardRateDianZhi"]
			data[3]["RewardValue"] = (*result)[0]["RewardRateQiPai"]
			data[4]["RewardValue"] = (*result)[0]["RewardRateLive"]
			data[5]["RewardValue"] = (*result)[0]["RewardRateSport"]
			data[6]["RewardValue"] = (*result)[0]["RewardRateLottery"]
			data[7]["RewardValue"] = (*result)[0]["RewardRateTexas"]
			data[8]["RewardValue"] = (*result)[0]["RewardRateLowLottery"]
			data[9]["RewardValue"] = (*result)[0]["RewardRateCryptoMarket"]
			data[10]["RewardValue"] = (*result)[0]["RewardRateHexLottery"]
			data[0]["State"] = (*result)[0]["State"]
			data[1]["State"] = (*result)[0]["State"]
			data[2]["State"] = (*result)[0]["State"]
			data[3]["State"] = (*result)[0]["State"]
			data[4]["State"] = (*result)[0]["State"]
			data[5]["State"] = (*result)[0]["State"]
			data[6]["State"] = (*result)[0]["State"]
			data[7]["State"] = (*result)[0]["State"]
			data[8]["State"] = (*result)[0]["State"]
			data[9]["State"] = (*result)[0]["State"]
			data[10]["State"] = (*result)[0]["State"]
		}
		//获取昨天VIP日返水金额
		sql = fmt.Sprintf("select * from x_vip_dailly where SellerId=%d and ChannelId=%d and UserId=%d and RecordDate='%s'",
			sellerId, channelId, token.UserId, time.Now().Add(-24*time.Hour).Format(abugo.DateLayout))
		yesterdayFanShui, err := server.Db().Query(sql, nil)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if yesterdayFanShui != nil && len(*yesterdayFanShui) > 0 {
			data[0]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountHaXi"] //1haxi 2xiaoyouxi 3dianzi 4qipai 5live 6sport 7lottery 8texas
			data[1]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountXiaoYouXi"]
			data[2]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountDianZhi"]
			data[3]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountQiPai"]
			data[4]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountLive"]
			data[5]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountSport"]
			data[6]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountLottery"]
			data[7]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountTexas"]
			data[8]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountLowLottery"]
			data[9]["RewardAmount"] = (*yesterdayFanShui)[0]["RewardAmountCryptoMarket"]
			data[10]["RewardValue"] = (*yesterdayFanShui)[0]["RewardRateHexLottery"]
		}
		ctx.Put("data", data)
	} else if reqdata.ActiveId == KActiveIdLucklySpin {

		for {
			userdata, _ := server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).First()
			if userdata == nil {
				udata := map[string]interface{}{
					"UserId":      token.UserId,
					"NowLevel":    1,
					"SpinCount":   1,
					"TotalAmount": 0,
					"NowAmount":   0,
					"FlushTime":   xgo.GetLocalTime(),
				}
				server.XDb().Table("x_active_userdata").Insert(udata)
				userdata = &xgo.XMap{RawData: udata}
				continue
			}
			ValidSecond := userdata.Int64("ValidSecond")
			ObtainTime := abugo.LocalTimeToTimeStamp(abugo.UtcToLocalTime(userdata.String("ObtainTime")))
			if ObtainTime+ValidSecond < time.Now().Unix() {
				server.Db().Query("UPDATE x_active_userdata SET TotalAmount = 0,NowAmount = 0,ObtainTime = NULL,SpinCount = 1,ObtainRecord = '[]',FlushTime = NULL WHERE UserId = ?;", []interface{}{token.UserId})
				userdata.Set("TotalAmount", 0)
				userdata.Set("NowAmount", 0)
				userdata.Set("ObtainTime", nil)
				userdata.Set("SpinCount", 1)
				userdata.Set("ObtainRecord", "[]")
				userdata.Set("CanApply", 2)
			} else {
				if userdata.String("FlushTime") != "" {
					FlushTime := abugo.TimeStampToLocalDate(abugo.LocalTimeToTimeStamp(abugo.UtcToLocalTime(userdata.String("FlushTime"))))
					if FlushTime != abugo.GetLocalDate() {
						server.Db().Query("UPDATE x_active_userdata SET SpinCount = SpinCount + 1 WHERE UserId = ?;", []interface{}{token.UserId})
						userdata.Set("SpinCount", userdata.Int("SpinCount")+1)
					}
				}
			}
			if userdata.Float64("TotalAmount") > 0 && math.Abs(userdata.Float64("TotalAmount")-userdata.Float64("NowAmount")) < 0.001 {
				userdata.Set("CanApply", 1)
			} else {
				userdata.Set("CanApply", 2)
			}
			userdata.Delete("FlushTime")
			userdata.Delete("InviteCount")
			userdata.Delete("InviteNeed")
			userdata.Delete("NowLevel")
			userdata.Delete("ObtionChildren")
			userdata.Delete("RechargeReq")
			if userdata.String("ObtainRecord") == "" {
				userdata.Set("ObtainRecord", "[]")
			}
			ctx.Put("userdata", userdata.RawData)
			break
		}
	} else if reqdata.ActiveId == utils.KActiveIdCumulativeWeeklyRecharge { // 累计周充值，豪礼享不停
		data, err := cumulativeWeeklyRechargeData(reqdata.ActiveId, ctx)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", data)
	} else if reqdata.ActiveId == utils.KActiveIdSignReward { // 签到奖励
		data, err := signRewardData(reqdata.ActiveId, ctx)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", data)
	} else if reqdata.ActiveId == utils.KActiveIdRecommendFriendReward { // 推荐好友奖励
		data, err := RecommendFriendRewardData(reqdata.ActiveId, reqdata.IsAgent, ctx)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", data)
	} else if reqdata.ActiveId == utils.KActiveIdDianziMeiZhouSong ||
		reqdata.ActiveId == utils.KActiveIdZhenrenMeiZhouSong ||
		reqdata.ActiveId == utils.KActiveIdQiPaiMeiZhouSong { // 每周闯关
		data, err := WeekendBreakThroughData(reqdata.ActiveId, ctx)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", data)
	} else if reqdata.ActiveId == utils.MultipleDepositGift { // 新老用户复充送豪礼
		// 使用通用逻辑处理，从 x_active_info 表获取数据
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "= ", sellerId, 0)
		where.Add("and", "ChannelId", "= ", channelId, 0)
		where.Add("and", "ActiveId", "= ", reqdata.ActiveId, 0)
		result, err := server.Db().Table("x_active_info").OrderBy("Level asc").Where(where).GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 初始化申请状态
		for j := 0; j < len(*result); j++ {
			(*result)[j]["Applyed"] = 0
		}

		// 查询用户是否已申请过该活动
		sql = fmt.Sprintf("select * from x_active_reward_audit where SellerId=%d and ChannelId=%d and UserId=%d and ActiveId=%d and RecordDate='%s'",
			sellerId, channelId, token.UserId, reqdata.ActiveId, time.Now().Format(abugo.DateLayout))
		applyRecord, err := server.Db().Query(sql, nil)
		if ctx.RespErr(err, &errcode) {
			return
		}
		if applyRecord != nil && len(*applyRecord) > 0 {
			for i := 0; i < len(*applyRecord); i++ {
				for j := 0; j < len(*result); j++ {
					if abugo.GetInt64FromInterface((*applyRecord)[i]["ActiveLevel"]) == abugo.GetInt64FromInterface((*result)[j]["Level"]) {
						(*result)[j]["Applyed"] = 1
					}
				}
			}
		}

		ctx.Put("data", result)
	} else if reqdata.ActiveId == utils.KActiveIdHaXiBreakout ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_15 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_16 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_17 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_18 ||
		reqdata.ActiveId == utils.KActiveIdQipaiBreakout ||
		reqdata.ActiveId == utils.KActiveIdDianziBreakout ||
		reqdata.ActiveId == utils.KActiveIdZhenrenBreakout ||
		reqdata.ActiveId == utils.KActiveIdMidAutumnZhenrenBreakout ||
		reqdata.ActiveId == utils.KActiveIdMidAutumnBreakout ||
		reqdata.ActiveId == utils.KActiveIdCryptoGamesBreakout { // 每日闯关
		data, err := TodayBreakThroughData(reqdata.ActiveId, ctx)
		if ctx.RespErr(err, &errcode) {
			return
		}
		ctx.Put("data", data)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "= ", sellerId, 0)
		where.Add("and", "ChannelId", "= ", channelId, 0)
		where.Add("and", "ActiveId", "= ", reqdata.ActiveId, 0)
		result, err := server.Db().Table("x_active_info").OrderBy("Level asc").Where(where).GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		if reqdata.ActiveId != KActiveIdEachBetFanShui { //不是vip返水也不是投注返水才有申请记录可以查询
			for j := 0; j < len(*result); j++ {
				(*result)[j]["Applyed"] = 0
			}

			if reqdata.ActiveId == KActiveIdInvite {
				sql = fmt.Sprintf("select * from x_active_reward_audit where SellerId=%d and ChannelId=%d and UserId=%d and ActiveId=%d",
					sellerId, channelId, token.UserId, reqdata.ActiveId)
			} else {
				sql = fmt.Sprintf("select * from x_active_reward_audit where SellerId=%d and ChannelId=%d and UserId=%d and ActiveId=%d and RecordDate='%s'",
					sellerId, channelId, token.UserId, reqdata.ActiveId, time.Now().Format(abugo.DateLayout))
			}
			applyRecord, err := server.Db().Query(sql, nil)
			if ctx.RespErr(err, &errcode) {
				return
			}
			if applyRecord != nil && len(*applyRecord) > 0 {
				for i := 0; i < len(*applyRecord); i++ {
					for j := 0; j < len(*result); j++ {
						if abugo.GetInt64FromInterface((*applyRecord)[i]["ActiveLevel"]) == abugo.GetInt64FromInterface((*result)[j]["Level"]) {
							(*result)[j]["Applyed"] = 1
						}
						if reqdata.ActiveId != KActiveIdXianglongfuhu {
							(*result)[j]["Applyed"] = 1
						}
					}
				}
			}
		}
		ctx.Put("data", result)

		//如果是充值活动返回当日累计充值数额
		if reqdata.ActiveId == KActiveIdRechange {
			TodayRecharge := float64(0)
			sql = fmt.Sprintf("SELECT SUM(RealAmount) AS TodayRecharge FROM x_recharge WHERE UserId=%d AND CreateTime>='%s' AND CreateTime<='%s' AND State=5",
				token.UserId, time.Now().Format(abugo.DateLayout)+" 00:00:00", time.Now().Format(abugo.DateLayout)+" 23:59:59")
			today, err := server.Db().Query(sql, nil)
			if ctx.RespErr(err, &errcode) {
				return
			}
			if today != nil && len(*today) > 0 {
				TodayRecharge = abugo.GetFloat64FromInterface((*today)[0]["TodayRecharge"])
			}
			ctx.Put("TodayRecharge", TodayRecharge)
		}
		//首充次日送返回上日首充数值
		if reqdata.ActiveId == utils.KActiveIdCiRiSong {
			lastDayFirstCharge, _, _ := lastDayFirstCharge(now, server.Db().Gorm(), token.UserId)
			ctx.Put("lastDayFirstCharge", lastDayFirstCharge)
			vipDailly, _ := lastDayVipDailly(now, server.Db().Gorm(), token.UserId)
			ctx.Put("lastDayDianZiLiuSui", vipDailly.LiuSuiDianZhi)
		}

		//如果是邀请好友那么返回已经邀请的好友个数
		if reqdata.ActiveId == KActiveIdInvite {
			InviteNumber := float64(0)
			activeValidLiuShui := float64(100000000)
			activeValidRecharge := float64(100000000)
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "SellerId", "= ", sellerId, 0)
				where.Add("and", "ChannelId", "= ", channelId, 0)
				where.Add("and", "ActiveId", "= ", KActiveIdInvite, 0)
				inviteDefine, err := server.Db().Table("x_active_define").Where(where).GetOne()
				if ctx.RespErr(err, &errcode) {
					return
				}
				if inviteDefine != nil {
					activeValidLiuShui = abugo.GetFloat64FromInterface((*inviteDefine)["ValidLiuShui"])
					activeValidRecharge = abugo.GetFloat64FromInterface((*inviteDefine)["ValidRecharge"])
				}
			}
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "x_agent_child.UserId", "=", token.UserId, 0)
				where.Add("and", "x_agent_child.ChildLevel", "=", 0, "")
				where.Add("and", "x_vip_info.LiuSui", ">= ", activeValidLiuShui, "")
				where.Add("and", "x_vip_info.Recharge", ">= ", activeValidRecharge, "")
				join := "LEFT JOIN x_vip_info ON x_agent_child.Child = x_vip_info.UserId"
				sel := "COUNT(x_agent_child.Child) AS InviteNumber"
				total, err := server.Db().Table("x_agent_child").Select(sel).Join(join).Where(where).GetOne()
				if ctx.RespErr(err, &errcode) {
					return
				}
				if total != nil {
					InviteNumber = abugo.GetFloat64FromInterface((*total)["InviteNumber"])
				}
			}
			ctx.Put("InviteNumber", InviteNumber)
		}

		// 如果是首充活动，返回用户的活动资格检查结果
		if reqdata.ActiveId == utils.FirstDepositGift {
			// 检查用户是否符合首充活动基本领取资格
			activeQualifications := active.CheckFirstDepositEnable(int32(sellerId), int32(channelId), utils.FirstDepositGift, int32(token.UserId))
			ctx.Put("ActiveQualifications", activeQualifications)
		}
		if reqdata.ActiveId == utils.MultipleDepositGift {
			// 检查用户是否符合复充活动基本领取资格
			activeQualifications := active.CheckMultipleDepositEnable(int32(sellerId), int32(channelId), utils.MultipleDepositGift, int32(token.UserId))
			ctx.Put("ActiveQualifications", activeQualifications)
		}
	}

	ctx.RespOK()
}

func spin_sort(arr []map[string]interface{}) {
	n := len(arr)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if xgo.ToFloat(arr[j]["amount"]) > xgo.ToFloat(arr[j+1]["amount"]) {
				arr[j], arr[j+1] = arr[j+1], arr[j]
			}
		}
	}
}

// active_apply 处理用户申请活动奖励的请求
// 根据不同的活动类型(ActiveId)执行相应的处理逻辑:
// - 充值任务(KActiveIdRechange): 处理充值相关的奖励申请
// - 救援金(KActiveIdRescue): 处理救援金申请
// - 邀请好友(KActiveIdInvite): 调用存储过程处理邀请奖励
// - 降龙伏虎(KActiveIdXianglongfuhu): 调用存储过程处理相关奖励
// - 次日送(KActiveIdCiRiSong): 处理次日送活动奖励
// - 幸运转盘(KActiveIdLucklySpin): 处理幸运转盘抽奖逻辑
// - 其他活动类型: 调用通用存储过程处理
// 参数:
//   - ctx: HTTP请求上下文，包含请求数据和用户信息
//
// 返回:
//   - 处理结果通过ctx返回给客户端
func (c *ActiveController) active_apply(ctx *abugo.AbuHttpContent) {
	defer recover() // 捕获可能的panic异常

	// 定义请求数据结构
	type RequestData struct {
		Id         int // x_active_define.Id - 活动定义ID
		ActiveId   int `validate:"required"` // 活动ID，必填
		Level      int `validate:"required"` // 申请的活动等级，必填
		RedeemCode string
	}
	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 获取用户令牌信息
	token := server.GetToken(ctx)
	host := ctx.Host()
	channelId, sellerId := server.GetChannel(ctx, host)
	procedureName := "" // 存储过程名称，用于通用活动处理

	// 根据不同活动类型进行处理
	// 处理充值任务活动
	if reqdata.ActiveId == KActiveIdRechange {
		// 调用充值任务奖励处理函数
		err, errcode = awardIdRecharge(token.UserId, reqdata.ActiveId, token, reqdata.Level)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == KActiveIdRescue {
		// 处理救援金活动
		err, errcode = applyRescue(token.UserId, reqdata.ActiveId, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == KActiveIdInvite {
		// 处理邀请好友活动，使用专用存储过程
		procedureName = "x_api_user_active_apply_invite"
	} else if reqdata.ActiveId == KActiveIdXianglongfuhu {
		// 处理降龙伏虎活动，使用专用存储过程
		procedureName = "x_api_user_active_apply_xianglongfuhu"
	} else if reqdata.ActiveId == utils.KActiveIdCiRiSong {
		// 处理次日送活动
		err, errcode := awardCiRiSong(token.UserId, reqdata.ActiveId, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

	} else if reqdata.ActiveId == KActiveIdLucklySpin {
		// 处理幸运转盘活动
		// 获取活动定义和用户数据
		activedata, _ := server.XDb().Table("x_active_define").Where("ActiveId=?", reqdata.ActiveId).First()
		userdata, _ := server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).First()
		if userdata == nil {
			ctx.RespErr(errors.New("获取数据失败"), &errcode)
			return
		}

		// 解析活动配置
		actviecfg := &model.SpinConfig{}
		err := json.Unmarshal([]byte(activedata.String("Config")), &actviecfg)
		if err != nil {
			logs.Error("活动配置有误1", err)
			ctx.RespErr(errors.New("活动配置有误"), &errcode)
			return
		}

		// 验证配置数据完整性
		if len(actviecfg.Round) != 3 || len(actviecfg.SpinData) != 8 {
			logs.Error("活动配置有误2", actviecfg)
			ctx.RespErr(errors.New("活动配置有误"), &errcode)
			return
		}

		// 获取用户当前状态
		NowLevel := userdata.Int("NowLevel")
		NowAmount := userdata.Float64("NowAmount")
		TotalAmount := userdata.Float64("TotalAmount")

		if reqdata.Level == 1 {
			// 处理抽奖请求
			if TotalAmount > 0 && math.Abs(TotalAmount-NowAmount) < 0.001 {
				// 已达到提现条件，需要先提现
				ctx.RespErr(errors.New("已满足提现需求,请先提现"), &errcode)
				return
			} else {
				// 检查抽奖次数
				SpinCount := userdata.Int("SpinCount")
				if SpinCount <= 0 {
					ctx.RespErr(errors.New("抽奖次数不足"), &errcode)
					return
				}

				if TotalAmount == 0 {
					// 首次抽奖，确定奖池总额
					ids := []map[string]interface{}{}
					// 筛选类型为1的奖项(总额奖项)
					for i := 0; i < len(actviecfg.SpinData); i++ {
						if actviecfg.SpinData[i].Type == 1 {
							ids = append(ids, map[string]interface{}{
								"id":     i,
								"amount": actviecfg.SpinData[i].Data[0],
							})
						}
					}
					if len(ids) < 3 {
						logs.Error("活动配置有误3", actviecfg)
						ctx.RespErr(errors.New("活动配置有误3"), &errcode)
						return
					}

					// 按金额排序
					spin_sort(ids)

					// 根据用户等级选择对应奖项
					idx := xgo.ToInt(ids[NowLevel-1]["id"])
					rdata := actviecfg.SpinData[idx]

					// 设置返回数据
					ctx.Put("spindata", map[string]interface{}{
						"Id":     rdata.Id,
						"Type":   rdata.Type,
						"Amount": rdata.Data[0],
					})

					// 获取对应轮次配置
					round := actviecfg.Round[NowLevel-1]

					// 随机计算实际获得金额
					randnum := rand.Int()
					randnum = randnum % (round.RandRate[1] - round.RandRate[0] + 1)

					SpinAmount := float64(rdata.Data[0]) * float64(randnum+round.RandRate[0]) / 100

					// 更新用户数据
					server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).Update(map[string]interface{}{
						"NowAmount":   SpinAmount,                    // 当前金额
						"TotalAmount": rdata.Data[0],                 // 总金额
						"ObtainTime":  xgo.GetLocalTime(),            // 获得时间
						"ValidSecond": round.ExpireTime * 60 * 60,    // 有效期(秒)
						"InviteNeed":  round.InviteCount,             // 邀请要求
						"RechargeReq": round.Recharge,                // 充值要求
						"SpinCount":   userdata.Int("SpinCount") - 1, // 减少抽奖次数
					})
					// 更新刷新时间
					server.Db().Query("update x_active_userdata set FlushTime = now() where UserId = ?", []interface{}{token.UserId})

					// 重新获取用户数据并处理返回
					userdata, _ = server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).First()
					userdata.Set("CanApply", 2) // 设置不可申请状态
					// 删除不需要返回的字段
					userdata.Delete("FlushTime")
					userdata.Delete("InviteCount")
					userdata.Delete("InviteNeed")
					userdata.Delete("NowLevel")
					userdata.Delete("ObtionChildren")
					userdata.Delete("RechargeReq")
					if userdata.String("ObtainRecord") == "" {
						userdata.Set("ObtainRecord", "[]")
					}
					ctx.Put("userdata", userdata.RawData)
				} else {
					// 非首次抽奖，增加当前金额
					ids := []int{}
					// 筛选类型为2的奖项(增量奖项)
					for i := 0; i < len(actviecfg.SpinData); i++ {
						if actviecfg.SpinData[i].Type == 2 {
							ids = append(ids, i)
						}
					}
					if len(ids) <= 0 {
						ctx.RespErr(errors.New("活动配置有误"), &errcode)
						return
					}

					// 随机选择一个奖项
					randnum := rand.Int()
					randnum = randnum % len(ids)
					rdata := actviecfg.SpinData[ids[randnum]]

					// 随机计算增加金额的百分比
					randnum = rand.Int()
					randnum = randnum % (rdata.Data[1] - rdata.Data[0] + 1)

					// 计算增加的金额
					SpinAmount := float64(randnum+rdata.Data[0]) * (TotalAmount - NowAmount) / 100

					// 设置返回数据
					ctx.Put("spindata", map[string]interface{}{
						"Id":     rdata.Id,
						"Type":   rdata.Type,
						"Amount": SpinAmount,
					})

					// 更新用户数据
					server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).Update(map[string]interface{}{
						"NowAmount": userdata.Float64("NowAmount") + SpinAmount, // 增加当前金额
						"SpinCount": userdata.Int("SpinCount") - 1,              // 减少抽奖次数
					})

					// 重新获取用户数据并处理返回
					userdata, _ = server.XDb().Table("x_active_userdata").Where("UserId=?", token.UserId).First()
					userdata.Set("CanApply", 2) // 设置不可申请状态
					// 删除不需要返回的字段
					userdata.Delete("FlushTime")
					userdata.Delete("InviteCount")
					userdata.Delete("InviteNeed")
					userdata.Delete("NowLevel")
					userdata.Delete("ObtionChildren")
					userdata.Delete("RechargeReq")
					if userdata.String("ObtainRecord") == "" {
						userdata.Set("ObtainRecord", "[]")
					}
					ctx.Put("userdata", userdata.RawData)
				}
			}
		} else if reqdata.Level == 2 {
			// 处理提现请求
			if TotalAmount == 0 {
				// 检查是否有可提现金额
				ctx.RespErr(errors.New("无提现金额,请先获得提现金额"), &errcode)
				return
			}
			if NowAmount < TotalAmount {
				// 检查是否达到提现条件(当前金额需要大于等于总金额)
				ctx.RespErr(errors.New("未满足提现需求,无法提现"), &errcode)
				return
			}
			// 使用专用存储过程处理幸运转盘提现
			procedureName = "x_api_user_active_apply_luckyspin"
		}
		// 处理首充复充活动
	} else if reqdata.ActiveId == utils.FirstDepositGift {
		// 调用首充礼金处理函数
		errcode, err = active.FirstDepositGift(int32(sellerId), int32(channelId), int32(reqdata.ActiveId), int32(token.UserId), int32(reqdata.Level))
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.MultipleDepositGift {
		// 调用复充礼金处理函数
		errcode, err = active.MultipleDepositGift(reqdata.ActiveId, reqdata.Level, ctx)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdCumulativeWeeklyRecharge {
		// 处理累计周充值活动
		err, errcode = cumulativeWeeklyRecharge(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdSignReward {
		// 处理签到奖励活动
		err, errcode = signReward(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdRecommendFriendReward {
		// 处理推荐好友奖励活动
		err, errcode = RecommendFriendReward(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdDianziMeiZhouSong ||
		reqdata.ActiveId == utils.KActiveIdZhenrenMeiZhouSong ||
		reqdata.ActiveId == utils.KActiveIdQiPaiMeiZhouSong {
		// 处理每周送活动(电子/真人/棋牌)
		err, errcode = WeekendBreakThrough(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdHaXiBreakout ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_15 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_16 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_17 ||
		reqdata.ActiveId == utils.KActiveIdHaXiBreakout_18 ||
		reqdata.ActiveId == utils.KActiveIdQipaiBreakout ||
		reqdata.ActiveId == utils.KActiveIdDianziBreakout ||
		reqdata.ActiveId == utils.KActiveIdZhenrenBreakout ||
		reqdata.ActiveId == utils.KActiveIdMidAutumnZhenrenBreakout ||
		reqdata.ActiveId == utils.KActiveIdMidAutumnBreakout ||
		reqdata.ActiveId == utils.KActiveIdCryptoGamesBreakout {
		// 处理每日闯关活动(哈希/棋牌/电子/真人等)
		err, errcode = TodayBreakThrough(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KActiveIdNewFirstDeposit {
		// 处理新首存活动
		err, errcode = newFirstDeposit(reqdata.ActiveId, reqdata.Level, token)
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.ActiveId == utils.KDIYActiveIdRecharge {
		// todo: 领取自定义充值活动奖励
	} else if reqdata.ActiveId == utils.RegisterGift {
		// 注册礼金活动自动发放，不需要手动申请
		err = fmt.Errorf("注册礼金活动自动发放，不需要手动申请")
		errcode = 400 // 参数错误
		ctx.RespErr(err, &errcode)
		return
	} else {
		// 不支持的活动类型
		err = errors.New("unsupport apply ActiveId")
	}
	// 领取兑换码活动奖励
	if reqdata.ActiveId == utils.RedeemCodeGift {
		errcode = active.RedeemCodeActive(reqdata.ActiveId, reqdata.Level, ctx, reqdata.RedeemCode)

		// 构建符合要求的返回数据结构
		responseData := map[string]interface{}{
			"code": 200,
			"data": map[string]interface{}{
				"status": errcode,
			},
		}

		ctx.RespJson(responseData)
		return
	}
	// 检查是否有错误发生
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 如果有存储过程名字的话，调用存储过程处理活动申请
	if procedureName != "" {
		// 调用数据库存储过程
		presult, err := c.active_call_db_procedure(procedureName, token.UserId, reqdata.Level)
		if ctx.RespErr(err, &errcode) {
			return
		}
		// 检查存储过程执行结果是否有错误
		if ctx.RespProcedureErr(presult) {
			return
		}
		// 如果存储过程返回了订单信息，发送通知消息
		if presult != nil && (*presult)["Id"] != nil {
			// 根据环境设置消息前缀
			env := "测试服,"
			if !server.Debug() {
				env = "正式服,"
			}
			// 构造通知消息
			msg := fmt.Sprintf(`%v新的新活动领取订单,请立即审核
		编号: %v
		金额: %v
		时间: %v`, env, (*presult)["Id"], (*presult)["Amount"], (*presult)["NowTime"])
			// 发送通知到Telegram机器人
			req.Post(viper.GetString("tgbotapi")+"/sendmsg", msg)
		}
	}

	// 调用管理后台API(可能是用于统计或通知)
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}

	// 返回成功响应
	ctx.RespOK()
}

func lastDayFirstCharge(now time.Time, gdb *gorm.DB, UserId int) (decimal.Decimal, error, int) {
	todayStart := utils.DayStartByTime(now)
	lastDayStart := todayStart.AddDate(0, 0, -1)
	lastDayFirstCharge := model.Charge{}
	noHaveRecord := gdb.Table(utils.TableRecharge).
		Order("CreateTime asc").Where("UserId = ? and CreateTime >= ? and CreateTime < ? and State = 5", UserId, utils.FormatDateTime(lastDayStart), utils.FormatDateTime(todayStart)).
		First(&lastDayFirstCharge).RecordNotFound()
	//没有找到订单
	if noHaveRecord {
		return decimal.Zero, errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}
	return lastDayFirstCharge.RealAmount, nil, utils.ActiveSuccess
}

func todayFirstCharge(now time.Time, gdb *gorm.DB, UserId int) (decimal.Decimal, error, int) {
	todayStart := utils.DayStartByTime(now)
	lastDayFirstCharge := model.Charge{}
	noHave := gdb.Table(utils.TableRecharge).
		Order("CreateTime asc").Where("UserId = ?  and State = 5 and CreateTime >= ?", UserId, utils.FormatDateTime(todayStart)).
		First(&lastDayFirstCharge).RecordNotFound()

	if noHave {
		return decimal.Zero, nil, utils.ActiveSuccess
	}
	return lastDayFirstCharge.RealAmount, nil, utils.ActiveSuccess
}

func todayVipDailly(now time.Time, gdb *gorm.DB, UserId int) (model.VipDailly, int) {

	vipDailly := model.VipDailly{}
	gdb.Table(utils.TableVipDailly).
		Where("UserId = ? and RecordDate = ?", UserId, utils.FormatDate(now)).
		First(&vipDailly)
	return vipDailly, utils.ActiveISuccess
}

func lastDayVipDailly(now time.Time, gdb *gorm.DB, UserId int) (model.VipDailly, int) {
	todayStart := utils.DayStartByTime(now)
	lastDayStart := todayStart.AddDate(0, 0, -1)

	vipDailly := model.VipDailly{}
	gdb.Table(utils.TableVipDailly).
		Where("UserId = ? and RecordDate = ?", UserId, utils.FormatDate(lastDayStart)).
		First(&vipDailly)
	return vipDailly, utils.ActiveISuccess
}

func ciRiTodayADefine(gdb *gorm.DB, now time.Time, token *server.TokenData, ActiveId int) model.ActiveDefine {
	aDefine := model.ActiveDefine{}
	err := gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ?", token.SellerId, token.ChannelId, ActiveId).
		First(&aDefine).Error
	if err != nil {
		return aDefine
	}
	if utils.IsActiveTest {
		return aDefine
	}

	today := utils.FormatDate(now)
	list := []model.ActiveDefineOld{}
	err = gdb.Table(utils.TableActiveDefineOld).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ? and UpdateDate in (?)",
			token.SellerId, token.ChannelId, ActiveId, []string{today}).
		Find(&list).Error
	//如果存在则使用最后两个查看是否有昨天的记录
	if err == nil {
		defineMap := make(map[string]model.ActiveDefine)
		for _, old := range list {
			defineMap[string(old.UpdateDate)] = old.ActiveDefine
		}
		if v, ok := defineMap[today]; ok {
			return v
		}
	}

	return aDefine
}

func ciRiActiveDefine(gdb *gorm.DB, now time.Time, token *server.TokenData, ActiveId int) model.ActiveDefine {
	yestoday := utils.FormatDate(now.AddDate(0, 0, -1))
	today := utils.FormatDate(now)
	aDefine := model.ActiveDefine{}
	noHave := gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ?", token.SellerId, token.ChannelId, ActiveId).
		First(&aDefine).RecordNotFound()
	if utils.IsActiveTest {
		return aDefine
	}

	list := []model.ActiveDefineOld{}
	noHave = gdb.Table(utils.TableActiveDefineOld).Order("UpdateDate desc").
		Limit(2).Where("SellerId = ？ and ChannelId = ? and ActiveId = ? and UpdateDate in (?)",
		token.SellerId, token.ChannelId, ActiveId, strings.Join([]string{yestoday, today}, ",")).
		Find(&list).RecordNotFound()
	//如果存在则使用最后两个查看是否有昨天的记录
	if !noHave {
		defineMap := make(map[string]model.ActiveDefine)
		for _, old := range list {
			defineMap[string(old.UpdateDate)] = old.ActiveDefine
		}

		if v, ok := defineMap[yestoday]; ok {
			return v
		}

		if v, ok := defineMap[today]; ok {
			return v
		}
	}

	return aDefine
}

func ciRiActiveInfo(gdb *gorm.DB, now time.Time, token *server.TokenData, ActiveId int) model.ActiveInfo {
	yestoday := utils.FormatDate(now.AddDate(0, 0, -1))
	today := utils.FormatDate(now)
	list := []model.ActiveInfoOld{}

	if utils.IsActiveTest {
		info := model.ActiveInfo{}
		gdb.Table(utils.TableActiveInfo).
			Where("SellerId = ? and ChannelId = ? and ActiveId = ?",
				token.SellerId, token.ChannelId, ActiveId).
			First(&info).RecordNotFound()
		return info
	}

	noHave := gdb.Table(utils.TableActiveInfoOld).Order("UpdateTime desc").
		Limit(2).Where("SellerId = ? and ChannelId = ? and ActiveId = ? and UpdateDate in (?)",
		token.SellerId, token.ChannelId, ActiveId, strings.Join([]string{yestoday, today}, ",")).
		Find(&list).RecordNotFound()
	//如果存在则使用最后两个查看是否有昨天的记录
	if !noHave {
		infoMap := make(map[string]model.ActiveInfo)
		for _, old := range list {
			infoMap[string(old.UpdateDate)] = old.ActiveInfo
		}

		if v, ok := infoMap[yestoday]; ok {
			return v
		}

		if v, ok := infoMap[today]; ok {
			return v
		}
	}
	aInfo := model.ActiveInfo{}
	gdb.Table(utils.TableActiveInfo).Where("SellerId = ? and ChannelId = ? and ActiveId = ?", token.SellerId, token.ChannelId, ActiveId).
		First(&aInfo).RecordNotFound()
	return aInfo
}

// 救济金
func applyRescue(UserId int, ActiveId int, token *server.TokenData) (error, int) {
	now := time.Now()
	recharge, rErr, code := lastDayFirstCharge(now, server.Db().Gorm(), UserId)
	if rErr != nil {
		logs.Error(fmt.Sprintf("救济金 找不到昨日充值 userId:%d now:%s", UserId, utils.FormatDate(now)), rErr)
		return rErr, code
	}
	gdb := server.Db().Gorm()

	var ADefine, CiRiADefine model.ActiveDefine
	CiRiADefine = ciRiTodayADefine(gdb, now, token, utils.KActiveIdCiRiSong)
	var useFullPrice bool = true
	//是否计算
	if have, _ := active.VerifyActiveDefine(CiRiADefine, now); have {
		//useFullPrice = true
		if recharge.GreaterThanOrEqual(CiRiADefine.MinDeposit) {
			useFullPrice = false
		}
	}

	var AInfo model.ActiveInfo
	yestodayStart := utils.DayStartByTime(now.AddDate(0, 0, -1))
	todayStart := utils.DayStartByTime(now)
	type Result struct {
		Total decimal.Decimal `gorm:"column:Total"`
	}
	result := Result{}
	err := gdb.Table(utils.TableRecharge).
		Select("sum(RealAmount) as Total").
		Where("UserId = ? and State = 5 and CreateTime >= ? and CreateTime < ?", UserId, utils.FormatDateTime(yestodayStart), utils.FormatDateTime(todayStart)).
		First(&result).Error
	if err != nil {
		logs.Error(fmt.Sprintf("救济金 找不到昨日充值总值 userId:%d now:%s yestodayStart:%s todayStart:%s",
			UserId, utils.FormatDate(now), yestodayStart, todayStart), rErr)
		return err, utils.ActiveIInternal
	}

	err = gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ?", token.SellerId, token.ChannelId, ActiveId).
		First(&ADefine).Error
	if err != nil {
		logs.Error(fmt.Sprintf("救济金 找不到活动定义 active_define userId:%d now:%s sellerId:%d channelId:%d activeid:%d",
			UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId), rErr)
		return errors.New(utils.ActiveRNotDefine), utils.ActiveINotDefine
	}
	if ADefine.State != utils.ActiveStateOpen {
		return errors.New(utils.ActiveRNotDefine), utils.ActiveINotDefine
	}

	checkCharge := result.Total
	if !useFullPrice {
		checkCharge = checkCharge.Sub(recharge)
	}
	if checkCharge.LessThan(ADefine.MinDeposit) {
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}

	userDailly := model.UserDailly{}
	gdb.Table(utils.TableUserDailly).
		Where("UserId = ? and RecordDate = ?", UserId, utils.FormatDate(now.AddDate(0, 0, -1))).
		First(&userDailly)
	useAmount := userDailly.TotalWinLoss.Mul(decimal.NewFromInt(-1)).
		Sub(userDailly.TotalCaiJin)

	err = gdb.Table(utils.TableActiveInfo).Order("LimitValue desc").
		Where("SellerId = ? and ChannelId = ? and ActiveId = ? and LimitValue <= ?", token.SellerId, token.ChannelId, ActiveId, useAmount).
		First(&AInfo).Error
	if err != nil {
		logs.Error(fmt.Sprintf("救济金 找不到活动详情 active_info userId:%d now:%s sellerId:%d channelId:%d activeid:%d limitValue:%s",
			UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, useAmount), rErr)
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}

	shouldAward := useAmount.Mul(AInfo.RewardValue)
	if shouldAward.LessThan(decimal.Zero) {
		logs.Error(fmt.Sprintf("救济金 没有达成条件 1 userId:%d now:%s sellerId:%d channelId:%d activeid:%d shouldAward:%s",
			UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, shouldAward), rErr)
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}
	if shouldAward.GreaterThan(ADefine.MaxReward) {
		shouldAward = ADefine.MaxReward
	}

	vipDailly, code := lastDayVipDailly(now, server.Db().Gorm(), UserId)
	limitLiuShui := shouldAward.Mul(ADefine.MinLiuShui)

	if limitLiuShui.GreaterThan(vipDailly.LiuSui) {
		logs.Error(fmt.Sprintf("救济金 没有达成条件 2流水 userId:%d now:%s sellerId:%d channelId:%d activeid:%d limitLiuShui:%s daillyDianZiliushui:%s",
			UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, limitLiuShui, vipDailly.LiuSuiDianZhi), rErr)
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}

	err = utils.DBTraction(server.Db().Gorm(), func(gdb *gorm.DB) error {
		user := model.UserInfo{}
		//获取奖励
		err := gdb.Table(utils.TableUser).
			Where("UserId = ?", UserId).First(&user).Error
		if err != nil {
			logs.Error(fmt.Sprintf("救济金 没有达成条件 用户不存在 1 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d",
				UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId), rErr)
			return errors.New(utils.ActiveRInternal)
		}
		if user.Id == utils.UnDefineId {
			logs.Error(fmt.Sprintf("救济金 没有达成条件 用户不存在 2 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d",
				UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId), rErr)
			return errors.New(utils.ActiveRAccountNoHave)
		}
		if user.IsTest == utils.UserTestType {
			logs.Error(fmt.Sprintf("救济金 没有达成条件 测试用户 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d",
				UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId), rErr)
			return errors.New(utils.ActiveRIsTest)
		}

		activeAwardAudit := model.ActiveAwardAudit{}
		noHaveRecord := gdb.Table(utils.TableActiveRewardAudit).
			Where("UserId = ? and ActiveId = ? and RecordDate >= ?", UserId, ActiveId, utils.FormatDate(now)).
			First(&activeAwardAudit).RecordNotFound()
		if !noHaveRecord {
			logs.Error(fmt.Sprintf("救济金 已经领取活动 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d auditId:%d",
				UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, activeAwardAudit.Id), rErr)
			return errors.New(utils.ActiveRAlreadyApply)
		}

		addAudit := model.AddActiveAwardAudit{}
		addAudit.ActiveId = ActiveId
		addAudit.UserId = user.UserId
		addAudit.SellerId = user.SellerId
		addAudit.ChannelId = user.ChannelId
		addAudit.ActiveLevel = AInfo.Level
		addAudit.CreateTime = utils.MyString(utils.FormatDateTime(now))
		addAudit.LiuShui = decimal.Zero
		addAudit.LimitLiuShui = shouldAward.Mul(ADefine.MinLiuShui)
		addAudit.ActiveMemo = ADefine.Title
		addAudit.NetWinLoss = useAmount
		addAudit.TotalRecharge = checkCharge
		addAudit.Amount = shouldAward
		addAudit.RecordDate = utils.FormatDate(now)
		//人工审核处理
		switch ADefine.AuditType {
		case utils.ActiveAuditRenGong:
			addAudit.AuditState = utils.ActiveAwardAuditStateWait
			err = gdb.Table(utils.TableActiveRewardAudit).Create(&addAudit).Error
			if err != nil {
				logs.Error(fmt.Sprintf("数据库错误 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d reason:%s",
					UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, err.Error()), rErr)
				return err
			}
			break
		case utils.ActiveAuditAuto:
			addAudit.AuditState = utils.ActiveAwardAuditStateAutoPass
			formatTime := utils.MyString(utils.FormatDateTime(now))
			addAudit.AuditTime = &formatTime
			err = gdb.Table(utils.TableActiveRewardAudit).Create(&addAudit).Error
			if err != nil {
				logs.Error(fmt.Sprintf("数据库错误 x_user userId:%d now:%s sellerId:%d channelId:%d activeid:%d reason:%s",
					UserId, utils.FormatDate(now), token.SellerId, token.ChannelId, ActiveId, err.Error()), rErr)
				return err
			}

			return CommonActiveHandle(utils.BalanceCReasonAJiuYuanJin, gdb, addAudit, user, ADefine, now)
		default:
			return errors.New("未找到审核类型")
		}
		return nil
	})
	return err, utils.ActiveIInternal
}

// ActiveDefine 需要增加领取百分比字段
func awardIdRecharge(UserId int, ActiveId int, token *server.TokenData, level int) (error, int) {
	now := time.Now()
	todayStart := utils.DayStartByTime(now)
	recharge, rErr, code := todayFirstCharge(now, server.Db().Gorm(), UserId)
	if rErr != nil {
		return rErr, code
	}
	vipDailly, code := todayVipDailly(now, server.Db().Gorm(), UserId)
	gdb := server.Db().Gorm()

	var CiRiADefine, ADefine model.ActiveDefine
	var useFullPrice bool = true
	CiRiADefine = ciRiTodayADefine(gdb, now, token, utils.KActiveIdCiRiSong)
	//是否计算
	if have, _ := active.VerifyActiveDefine(CiRiADefine, now); have {
		if recharge.GreaterThanOrEqual(CiRiADefine.MinDeposit) {
			useFullPrice = false
		}
	}

	type Result struct {
		Total decimal.Decimal `gorm:"column:Total"`
	}
	result := Result{}
	err := gdb.Table(utils.TableRecharge).
		Select("sum(RealAmount) as Total").
		Where("UserId = ? and State = 5 and CreateTime >= ?", UserId, utils.FormatDateTime(todayStart)).
		First(&result).Error

	if err != nil {
		return err, utils.ActiveIInternal
	}

	useAmount := result.Total
	if !useFullPrice {
		useAmount = useAmount.Sub(recharge)
	}

	err = gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ? ", token.SellerId, token.ChannelId, ActiveId).
		First(&ADefine).Error
	if err != nil {
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}
	if ADefine.State != utils.ActiveStateOpen {
		return errors.New(utils.ActiveRNotDefine), utils.ActiveINotDefine
	}
	var AInfo model.ActiveInfo
	err = gdb.Table(utils.TableActiveInfo).Order("LimitValue desc").
		Where("SellerId = ? and ChannelId = ? and ActiveId = ? and Level = ?", token.SellerId, token.ChannelId, ActiveId, level).
		First(&AInfo).Error
	if err != nil {
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}
	if useAmount.LessThan(AInfo.LimitValue) {
		logs.Error(fmt.Sprintf("now:%s useAmount:%s limitValue:%s", utils.FormatDateTime(now), useAmount, AInfo.LimitValue), errors.New(utils.ActiveRNotMatch))
		return errors.New(utils.ActiveRNotMatch), utils.ActiveINotMatch
	}

	err = utils.DBTraction(server.Db().Gorm(), func(gdb *gorm.DB) error {
		user := model.UserInfo{}
		//获取奖励
		err := gdb.Table(utils.TableUser).
			Where("UserId = ?", UserId).First(&user).Error
		if err != nil {
			logs.Error("active sql err", err)
			return errors.New(utils.ActiveRInternal)
		}
		if user.Id == utils.UnDefineId {
			return errors.New(utils.ActiveRAccountNoHave)
		}
		if user.IsTest == utils.UserTestType {
			return errors.New(utils.ActiveRIsTest)
		}

		activeAwardAudit := model.ActiveAwardAudit{}
		noHave := gdb.Table(utils.TableActiveRewardAudit).
			Where("UserId = ? and ActiveId = ? and ActiveLevel = ? and RecordDate >= ?", UserId, ActiveId, AInfo.Level, utils.FormatDate(now)).
			First(&activeAwardAudit).RecordNotFound()
		if !noHave {
			return errors.New(utils.ActiveRAlreadyApply)
		}

		if recharge.LessThan(ADefine.MinDeposit) {
			return errors.New(utils.ActiveRNotMatch)
		}

		limitLiuShui := AInfo.RewardValue.Mul(ADefine.MinLiuShui)
		if limitLiuShui.
			GreaterThan(vipDailly.LiuSui) {
			return errors.New(utils.ActiveRNotMatch)
		}

		shouldAward := AInfo.RewardValue

		addAudit := model.AddActiveAwardAudit{}
		addAudit.ActiveId = ActiveId
		addAudit.UserId = user.UserId
		addAudit.SellerId = user.SellerId
		addAudit.ChannelId = user.ChannelId
		addAudit.ActiveLevel = AInfo.Level
		addAudit.CreateTime = utils.MyString(utils.FormatDateTime(now))
		addAudit.LiuShui = vipDailly.LiuSuiDianZhi
		addAudit.LimitLiuShui = limitLiuShui
		addAudit.ActiveMemo = ADefine.Title
		addAudit.TotalRecharge = useAmount
		addAudit.Amount = shouldAward
		addAudit.RecordDate = utils.FormatDate(now)
		//人工审核处理
		switch ADefine.AuditType {
		case utils.ActiveAuditRenGong:
			addAudit.AuditState = utils.ActiveAwardAuditStateWait
			err = gdb.Table(utils.TableActiveRewardAudit).Create(&addAudit).Error
			if err != nil {
				logs.Error("active sql err", err)
				return err
			}
		case utils.ActiveAuditAuto:
			addAudit.AuditState = utils.ActiveAwardAuditStateAutoPass
			formatTime := utils.MyString(utils.FormatDateTime(now))
			addAudit.AuditTime = &formatTime
			err = gdb.Table(utils.TableActiveRewardAudit).Create(&addAudit).Error
			if err != nil {
				logs.Error("active sql err", err)
				return err
			}

			return CommonActiveHandle(utils.BalanceCReasonAChongZhiRenWu, gdb, addAudit, user, ADefine, now)
		default:
			return errors.New("未找到审核类型")
		}
		return nil
	})
	return err, utils.ActiveIInternal
}

// ActiveDefine 需要增加领取百分比字段
func awardCiRiSong(UserId int, ActiveId int, token *server.TokenData) (error, int) {
	now := time.Now()
	recharge, rErr, code := lastDayFirstCharge(now, server.Db().Gorm(), UserId)
	if rErr != nil {
		return rErr, code
	}
	vipDailly, _ := lastDayVipDailly(now, server.Db().Gorm(), UserId)

	gdb := server.Db().Gorm()

	var ADefine = model.ActiveDefine{}
	ADefine = ciRiActiveDefine(gdb, now, token, ActiveId)

	if _, AErr := active.VerifyActiveDefine(ADefine, now); AErr != nil {
		return AErr, utils.ActiveIInternal
	}
	var AInfo = model.ActiveInfo{}
	AInfo = ciRiActiveInfo(gdb, now, token, ActiveId)
	err := active.VerifyActiveInfo(AInfo)
	if err != nil {
		return err, utils.ActiveIInternal
	}

	err = utils.DBTraction(server.Db().Gorm(), func(gdb *gorm.DB) error {
		user := model.UserInfo{}
		//获取奖励
		err := gdb.Table(utils.TableUser).
			Where("UserId = ?", UserId).First(&user).Error
		if err != nil {
			logs.Error("active sql err", err)
			return errors.New(utils.ActiveRInternal)
		}
		if user.Id == utils.UnDefineId {
			return errors.New(utils.ActiveRAccountNoHave)
		}
		if user.IsTest == utils.UserTestType {
			return errors.New(utils.ActiveRIsTest)
		}

		activeAwardAudit := model.ActiveAwardAudit{}
		noHave := gdb.Table(utils.TableActiveRewardAudit).
			Where("UserId = ? and ActiveId = ? and RecordDate = ?", UserId, ActiveId, utils.FormatDate(now)).
			First(&activeAwardAudit).RecordNotFound()
		if !noHave {
			return errors.New(utils.ActiveRAlreadyApply)
		}

		if recharge.LessThan(ADefine.MinDeposit) {
			return errors.New(utils.ActiveRNotMatch)
		}

		shouldAward := recharge.Mul(AInfo.RewardValue)
		if shouldAward.GreaterThan(ADefine.MaxReward) {
			shouldAward = ADefine.MaxReward
		}

		awardLiuShui := decimal.Zero
		CiRiConfig := model.ACiRiConfig{}
		err = json.Unmarshal([]byte(ADefine.Config), &CiRiConfig)
		if err == nil {
			awardLiuShui = shouldAward.Mul(ADefine.MinLiuShui)
		}
		if awardLiuShui.
			GreaterThan(vipDailly.LiuSuiDianZhi) {
			return errors.New(utils.ActiveRNotMatch)
		}

		limitLiuShui := recharge.Mul(decimal.NewFromFloat32(CiRiConfig.MinAwardLiuShui))
		//没有打足流水
		if limitLiuShui.
			GreaterThan(vipDailly.LiuSuiDianZhi) {
			return errors.New(utils.ActiveRNotMatch)
		}

		addAudit := model.AddActiveAwardAudit{}
		addAudit.ActiveId = ActiveId
		addAudit.UserId = user.UserId
		addAudit.SellerId = user.SellerId
		addAudit.ChannelId = user.ChannelId
		addAudit.ActiveLevel = AInfo.Level
		addAudit.CreateTime = utils.MyString(utils.FormatDateTime(now))
		addAudit.LiuShui = vipDailly.LiuSuiDianZhi
		addAudit.LimitLiuShui = limitLiuShui
		addAudit.ActiveMemo = ADefine.Title
		addAudit.TotalRecharge = recharge
		addAudit.Amount = shouldAward
		addAudit.RecordDate = utils.FormatDate(now)
		//人工审核处理
		switch ADefine.AuditType {
		case utils.ActiveAuditRenGong:
			addAudit.AuditState = utils.ActiveAwardAuditStateWait
			err = gdb.Table(utils.TableActiveRewardAudit).
				Create(&addAudit).Error
			if err != nil {
				logs.Error("active sql err", err)
				return err
			}
			break
		case utils.ActiveAuditAuto:
			addAudit.AuditState = utils.ActiveAwardAuditStateAutoPass
			formatTime := utils.MyString(utils.FormatDateTime(now))
			addAudit.AuditTime = &formatTime
			err = gdb.Table(utils.TableActiveRewardAudit).
				Create(&addAudit).Error
			if err != nil {
				logs.Error("active sql err", err)
				return err
			}

			return CommonActiveHandle(utils.BalanceCReasonACiRiSong, gdb, addAudit, user, ADefine, now)
		default:
			return errors.New("未找到审核类型")
		}
		return nil
	})
	return err, utils.ActiveIInternal
}

func CommonActiveHandle(reason int, gdb *gorm.DB, audit model.AddActiveAwardAudit,
	user model.UserInfo, define model.ActiveDefine, now time.Time) error {
	//更改用户信息
	err := gdb.Table(utils.TableActiveReward).
		Omit("Id").
		Create(&model.ActiveReward{
			SellerId:  user.SellerId,
			ChannelId: user.ChannelId,
			UserId:    audit.UserId,

			State:      audit.AuditState,
			ActiveId:   audit.ActiveId,
			ActiveName: define.Title,
			Amount:     audit.Amount,
			AuditTime:  utils.MyString(utils.FormatDateTime(now)),
			CreateTime: utils.MyString(utils.FormatDateTime(now)),
		}).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}

	beforeAmount := user.Amount
	user.Amount = user.Amount.Add(audit.Amount)
	user.WithdrawLiuSui = user.WithdrawLiuSui.Add(audit.LimitLiuShui)

	err = gdb.Table(utils.TableUser).
		Where("Id = ?", user.Id).
		Updates(model.UserInfo{
			Amount:         user.Amount,
			WithdrawLiuSui: user.WithdrawLiuSui,
		}).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}
	//获取audlt id
	aFAudit := model.ActiveAwardAudit{}
	err = gdb.Table(utils.TableActiveRewardAudit).Where("UserId = ? and ActiveId = ? and ActiveLevel = ? and RecordDate = ?", audit.UserId,
		audit.ActiveId, audit.ActiveLevel, audit.RecordDate).First(&aFAudit).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}

	err = gdb.Table(utils.TableAmountChangeLog).
		Omit("Id").
		Create(&model.AmountChargeLog{
			UserId:       audit.UserId,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			BeforeAmount: beforeAmount,
			Amount:       audit.Amount,
			AfterAmount:  user.Amount,
			Reason:       reason,
			Memo:         fmt.Sprintf("%d", aFAudit.Id),
			CreateTime:   utils.MyString(utils.FormatDateTime(now)),
		}).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}
	vipInfo := model.VipInfo{}
	err = gdb.Table(utils.TableVipInfo).
		Where("UserId = ? ", audit.UserId).
		First(&vipInfo).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}
	err = gdb.Table(utils.TableVipInfo).
		Where("Id = ?", vipInfo.Id).
		Updates(model.VipInfo{
			CaiJin: vipInfo.CaiJin.Add(audit.Amount),
		}).Error
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}
	if audit.Amount.GreaterThan(decimal.Zero) {
		err = gdb.Table(utils.TableCaiJinDetail).
			Omit("Id").
			Create(&model.CaiJinDetail{
				UserId:     audit.UserId,
				SType:      define.Title,
				Symbol:     utils.SymbolUSDT,
				Amount:     audit.Amount,
				CSGroup:    user.CsGroup,
				CSId:       user.CsId,
				CreateTime: utils.MyString(utils.FormatDateTime(now)),
			}).Error
		if err != nil {
			logs.Error("active sql err", err)
			return errors.New(utils.ActiveRInternal)
		}
	}
	userDailly := model.UserDailly{
		RecordDate: utils.FormatDate(now),
	}
	noHaveRecord := gdb.Table(utils.TableUserDailly).
		Where("UserId = ? and RecordDate = ?", audit.UserId, userDailly.RecordDate).
		First(&userDailly).RecordNotFound()
	//if err != nil {
	//	return err
	//}
	userDailly.TotalCaiJin = userDailly.TotalCaiJin.Add(audit.Amount)
	if noHaveRecord {
		err = gdb.Table(utils.TableUserDailly).
			Create(model.UserDailly{
				UserId:     audit.UserId,
				ChannelId:  audit.ChannelId,
				SellerId:   audit.SellerId,
				RecordDate: userDailly.RecordDate,
				FineTime:   nil,
			}).Error
	} else {
		err = gdb.Table(utils.TableUserDailly).
			Where("UserId = ? and RecordDate = ?", userDailly.UserId, userDailly.RecordDate).
			Updates(model.UserDailly{
				TotalCaiJin: userDailly.TotalCaiJin,
			}).Error
	}
	if err != nil {
		logs.Error("active sql err", err)
		return errors.New(utils.ActiveRInternal)
	}
	return err
}

func (c *ActiveController) active_call_db_procedure(procedureName string, UserId int, Level int) (*map[string]interface{}, error) {
	pdata := []interface{}{UserId, Level}
	return server.Db().CallProcedure(procedureName, pdata...)
}

func (c *ActiveController) active_reward_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "= ", token.UserId, "")
	total, data := server.Db().Table("x_active_reward_audit").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	xActiveDefine := server.DaoxHashGame().XActiveDefine
	if data != nil {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["CreateTime"] = abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*data)[i]["CreateTime"])) * 1000
			(*data)[i]["AuditTime"] = abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*data)[i]["AuditTime"])) * 1000

			activeId := abugo.GetInt64FromInterface((*data)[i]["ActiveId"])
			activeDefine, _ := xActiveDefine.WithContext(nil).Where(xActiveDefine.ActiveID.Eq(int32(activeId))).First()
			(*data)[i]["ActiveTitle"] = ""
			if activeDefine != nil {
				(*data)[i]["ActiveTitle"] = activeDefine.Title
			}

		}
	}
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *ActiveController) home_carousel_list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Lang     int
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	if reqdata.Lang == 0 {
		reqdata.Lang = 1
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", SellerId, 0)
	where.Add("and", "ChannelId", "= ", ChannelId, "")
	where.Add("and", "Lang", "= ", reqdata.Lang, 0)
	where.Add("and", "State", "= ", 1, 0)
	data, err := server.Db().Table("x_home_carousel").Where(where).OrderBy("Sort desc").GetList()
	if ctx.RespErr(err, &errcode) {
		return
	}

	nowTime := time.Now().UnixMilli()
	if data != nil {
		retData := make([]map[string]interface{}, 0, len(*data))
		for i := 0; i < len(*data); i++ {
			(*data)[i]["Url"] = server.ImageUrl() + abugo.GetStringFromInterface((*data)[i]["Url"])
			if abugo.GetInt64FromInterface((*data)[i]["ExpiredTime"]) > nowTime || abugo.GetInt64FromInterface((*data)[i]["ExpiredTime"]) == 0 {
				retData = append(retData, (*data)[i])
			}
		}
		ctx.Put("data", retData)
	} else {
		ctx.Put("data", data)
	}
	ctx.RespOK()
}

func (c *ActiveController) home_carousel_v2_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Lang     int
		Host     string
	}
	errcode := 0
	reqdata := RequestData{Lang: 1}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)

	carouselDao := server.DaoxHashGame().XHomeCarouselV2
	carouselDb := carouselDao.WithContext(nil)

	nowTime := time.Now().UnixMilli()
	result, err := carouselDb.Where(carouselDao.SellerID.Eq(int32(SellerId))).
		Where(carouselDao.ChannelID.Eq(int32(ChannelId))).
		Where(carouselDao.Lang.Eq(int32(reqdata.Lang))).
		Where(carouselDao.State.Eq(1)).
		Where(carouselDb.Where(carouselDao.ExpiredTime.Gt(nowTime)).Or(carouselDao.ExpiredTime.Eq(0))).
		Order(carouselDao.Sort.Desc()).
		Find()

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	for i := range result {
		if result[i].URL != "" {
			result[i].URL = server.ImageUrl() + result[i].URL
		}
		if result[i].PcURL != "" {
			result[i].PcURL = server.ImageUrl() + result[i].PcURL
		}
	}

	ctx.Put("data", result)

	ctx.RespOK()
}

// 累计周充值，豪礼享不停 详情
func cumulativeWeeklyRechargeData(activeId int, ctx *abugo.AbuHttpContent) (model.CumulativeWeeklyRechargeRes, error) {
	defer recover()
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	var result model.CumulativeWeeklyRechargeRes
	now := carbon.Parse(carbon.Now().String()).StdTime()
	ADefine, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("cumulativeWeeklyRechargeData GetActiveDefine err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	startTime := carbon.Parse(carbon.Now().StartOfWeek().String()).StdTime()
	var configData []model.CumulativeWeeklyRechargeParam
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return result, errors.New("活动配置有误")
	}
	// 充值金额
	var liuSui model.RealAmountLiuSui
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())
	err = rechargeDb.Select(rechargeTb.RealAmount.Sum().As(rechargeTb.RealAmount.ColumnName().String())).
		Where(rechargeTb.UserID.Eq(int32(token.UserId))).
		Where(rechargeTb.State.Eq(5)).
		Where(rechargeTb.CreateTime.Between(startTime, now)).Scan(&liuSui)
	if err != nil {
		logs.Error("cumulativeWeeklyRechargeData rechargeDb err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Select(activeRewardAuditTb.ActiveLevel).Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.CreateTime.Between(startTime, now)).Find()
	if err != nil {
		logs.Error("cumulativeWeeklyRechargeData activeRewardAudit err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	if len(activeRewardAudit) > 0 {
		for index, configPar := range configData {
			equal := liuSui.RealAmount.GreaterThanOrEqual(configPar.RechargeAmount)
			if equal {
				configData[index].State = 1 // 待领取
			}
			for _, RewardAudit := range activeRewardAudit {
				if configPar.ID == RewardAudit.ActiveLevel {
					configData[index].State = 2 // 已领取
				}
			}
		}
	} else {
		for index, configPar := range configData {
			equal := liuSui.RealAmount.GreaterThanOrEqual(configPar.RechargeAmount)
			if equal {
				configData[index].State = 1 // 待领取
			}
		}
	}
	// 这周还剩多少秒
	endTime := carbon.Parse(carbon.Now().EndOfWeek().String()).StdTime()
	diff := endTime.Sub(now)
	result.Seconds = diff.Seconds()
	result.TotalRecharge, _ = liuSui.RealAmount.Float64()
	result.List = configData
	return result, nil
}

// 累计周充值，豪礼享不停
func cumulativeWeeklyRecharge(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	startTime := carbon.Parse(carbon.Now().StartOfWeek().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(int32(level))).
		Where(activeRewardAuditTb.CreateTime.Between(startTime, now)).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("cumulativeWeeklyRecharge activeRewardAudit err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if activeRewardAudit != nil {
		return errors.New("已申请"), utils.ActiveIAlreadyApply
	}
	var configData []model.CumulativeWeeklyRechargeConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal

	}
	var selectData model.CumulativeWeeklyRechargeConfig
	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
		}
	}
	if selectData.ID == 0 {
		return errors.New("参数错误"), utils.ActiveIParamsIllegal
	}
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("cumulativeWeeklyRecharge user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	// 充值金额
	var liuSui model.RealAmountLiuSui
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())
	err = rechargeDb.Select(rechargeTb.RealAmount.Sum().As(rechargeTb.RealAmount.ColumnName().String())).
		Where(rechargeTb.UserID.Eq(user.UserID)).
		Where(rechargeTb.State.Eq(5)).
		Where(rechargeTb.CreateTime.Between(startTime, now)).Scan(&liuSui)
	if err != nil {
		logs.Error("cumulativeWeeklyRecharge rechargeDb err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	// 条件
	rechargetEqual := liuSui.RealAmount.GreaterThanOrEqual(selectData.RechargeAmount)
	if !rechargetEqual {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	realAmount, _ := selectData.GiveAmount.Float64()
	withdrawLiuSuiAdd, _ := selectData.GiveAmount.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	var baseConfigStr []byte
	totalLiushui := 0.0
	firstRecharge := 0.0
	totalRecharge, _ := liuSui.RealAmount.Float64()
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         ADefine.AuditType,
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             level,
		RealAmount:        realAmount,
		TotalLiushui:      totalLiushui,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        "",
		BalanceCReason:    utils.BalanceCReasonCumulativeWeeklyRecharge,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     firstRecharge,
		TotalRecharge:     totalRecharge,
		MinLiuShui:        ADefine.MinLiuShui,
	}

	err = active.SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("cumulativeWeeklyRecharge saveActiveData err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

func signReward(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	var configData []model.SignRewardConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal

	}
	var baseData model.SignRewardBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	// 判断今天有没有签过到
	startTime := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	userSignTb := server.DaoxHashGame().XActiveLogUserSign
	userSignDb := server.DaoxHashGame().XActiveLogUserSign.WithContext(context.Background())
	sign, err := userSignDb.Where(userSignTb.UserID.Eq(int32(token.UserId))).
		Where(userSignTb.ActiveID.Eq(int32(activeId))).
		Where(userSignTb.RecordTime.Between(startTime, now)).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("signReward sign err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if sign != nil {
		return errors.New("已申请"), utils.ActiveIAlreadyApply
	}
	// 条件
	var liuSui model.SignRewardLiuSui
	var customUsdtLiuSui model.CustomUsdtLiuSui
	var customTrxLiuSui model.CustomTrxLiuSui
	userDaillyTb := server.DaoxHashGame().XUserDailly
	userDaillyDb := server.DaoxHashGame().XUserDailly.WithContext(context.Background())
	customDaillyTb := server.DaoxHashGame().XCustomDailly
	customDaillyDb := server.DaoxHashGame().XCustomDailly.WithContext(context.Background())

	err = customDaillyDb.Select(customDaillyTb.LiuSui.Sum().As("LiuSuiUsdt")).
		Where(customDaillyTb.UserID.Eq(int32(token.UserId))).
		Where(customDaillyTb.IsTest.Eq(2)).
		Where(customDaillyTb.IsGameAddress.Eq(2)).
		Where(customDaillyTb.RecordDate.Between(startTime, now)).
		Scan(&customUsdtLiuSui)
	if err != nil {
		logs.Error("signReward customDaillyDb err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	err = customDaillyDb.Select(customDaillyTb.LiuSui.Sum().As("LiuSuiTrx")).
		Where(customDaillyTb.UserID.Eq(int32(token.UserId))).
		Where(customDaillyTb.RecordDate.Between(startTime, now)).
		Scan(&customTrxLiuSui)

	if err != nil {
		logs.Error("signReward customDaillyDb err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}

	err = userDaillyDb.Select(userDaillyTb.LiuSuiUsdt.Sum().As(userDaillyTb.LiuSuiUsdt.ColumnName().String()),
		userDaillyTb.LiuSuiLottery.Sum().As(userDaillyTb.LiuSuiLottery.ColumnName().String()),
		userDaillyTb.LiuSuiQiPai.Sum().As(userDaillyTb.LiuSuiQiPai.ColumnName().String()),
		userDaillyTb.LiuSuiDianZhi.Sum().As(userDaillyTb.LiuSuiDianZhi.ColumnName().String()),
		userDaillyTb.LiuSuiXiaoYouXi.Sum().As(userDaillyTb.LiuSuiXiaoYouXi.ColumnName().String()),
		userDaillyTb.LiuSuiLive.Sum().As(userDaillyTb.LiuSuiLive.ColumnName().String()),
		userDaillyTb.LiuSuiSport.Sum().As(userDaillyTb.LiuSuiSport.ColumnName().String()),
		userDaillyTb.LiuSuiTexas.Sum().As(userDaillyTb.LiuSuiTexas.ColumnName().String()),
		userDaillyTb.LiuSuiTrx.Sum().As(userDaillyTb.LiuSuiTrx.ColumnName().String()),
	).Where(userDaillyTb.UserID.Eq(int32(token.UserId))).Where(userDaillyTb.RecordDate.Between(startTime, now)).Scan(&liuSui)

	if err != nil {
		logs.Error("signReward userDaillyDb err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}

	convertUstd := customTrxLiuSui.LiuSuiTrx.Div(baseData.TrxPrice)
	total := customUsdtLiuSui.LiuSuiUsdt.Add(liuSui.LiuSuiLottery).
		Add(liuSui.LiuSuiQiPai).
		Add(liuSui.LiuSuiDianZhi).
		Add(liuSui.LiuSuiXiaoYouXi).
		Add(liuSui.LiuSuiLive).
		Add(liuSui.LiuSuiSport).
		Add(liuSui.LiuSuiTexas).
		Add(convertUstd)
	totalEqual := total.GreaterThanOrEqual(baseData.MixBetLimit)
	if !totalEqual {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINoSigncondition
	}
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.FirstSignTime, userTb.LastSignTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("signReward user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	yesterday := now.AddDate(0, 0, -1)
	lastTime := user.LastSignTime
	isYesterday := lastTime.Day() == yesterday.Day() && lastTime.Month() == yesterday.Month() && lastTime.Year() == yesterday.Year()
	var days int32
	firstTime := now
	if isYesterday {
		firstTime = user.FirstSignTime
		firstSignTime := carbon.Parse(carbon.CreateFromStdTime(user.FirstSignTime).StartOfDay().String()).StdTime()
		lastSignTime := carbon.Parse(carbon.CreateFromStdTime(user.LastSignTime).StartOfDay().String()).StdTime()
		days = int32(lastSignTime.Sub(firstSignTime).Hours()/24) + 1
	}
	days += 1                             // 加上今天
	if days > int32(baseData.RemakeDay) { // 重置
		days = 1
		firstTime = now
	}
	var selectData model.SignRewardConfig
	for _, v := range configData {
		if v.SignDay == days {
			selectData = v
		}
	}
	if selectData.ID == 0 {
		return errors.New("参数错误"), utils.ActiveIParamsIllegal
	}
	realAmountDecimal := selectData.Award.Add(selectData.AdditionalReward)
	withdrawLiuSuiAdd, _ := realAmountDecimal.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	baseConfigStr, _ := json.Marshal(baseData)
	totalLiushui, _ := total.Float64()
	realAmount, _ := realAmountDecimal.Float64()
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         ADefine.AuditType,
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             int(selectData.ID),
		RealAmount:        realAmount,
		TotalLiushui:      totalLiushui,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        "",
		BalanceCReason:    utils.BalanceCReasonSignReward,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     0.0,
		TotalRecharge:     0.0,
		FirstSignTime:     &firstTime,
		LastSignTime:      &now,
		MinLiuShui:        ADefine.MinLiuShui,
	}
	tx1 := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		userSign := &model2.XActiveLogUserSign{
			RecordTime:    now,
			SellerID:      saveActiveDataInfo.SellerID,
			ChannelID:     saveActiveDataInfo.ChannelID,
			UserID:        user.UserID,
			ActiveID:      int32(activeId),
			LiuShui:       totalLiushui,
			RewardAmount:  realAmount,
			FirstSignTime: firstTime,
			LastSignTime:  now,
			Config:        string(configStr),
			BaseConfig:    string(baseConfigStr),
		}
		userSignDb = tx.XActiveLogUserSign.WithContext(context.Background())
		err = userSignDb.Create(userSign)
		if err != nil {
			logs.Error(err)
			return err
		}
		userDb = tx.XUser.WithContext(context.Background())
		_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
			"FirstSignTime": firstTime,
			"LastSignTime":  now,
		})
		if err != nil {
			logs.Error(err)
			return err
		}
		err = active.SaveActiveData(tx, user, saveActiveDataInfo)
		if err != nil {
			logs.Error(err)
			return err
		}
		return nil
	})
	if tx1 != nil {
		logs.Error("signReward tx1 err", tx1)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

// 签到奖励 详情
func signRewardData(activeId int, ctx *abugo.AbuHttpContent) (model.SignRewardRes, error) {
	defer recover()
	var result model.SignRewardRes
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	ADefine, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("signRewardData GetActiveDefine err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	var baseData model.SignRewardBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return result, errors.New("活动配置有误")
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.FirstSignTime, userTb.LastSignTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("signRewardData user err", err)
		return result, err
	}
	nowTime := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	lastSignTime := carbon.Parse(carbon.CreateFromStdTime(user.LastSignTime).StartOfDay().String()).StdTime()
	days := int32(nowTime.Sub(lastSignTime).Hours() / 24)
	if days == 0 || days == 1 { // 昨天或者今天
		firstSignTime := carbon.Parse(carbon.CreateFromStdTime(user.FirstSignTime).StartOfDay().String()).StdTime()
		countDays := int32(lastSignTime.Sub(firstSignTime).Hours()/24) + 1
		result.FirstSignTime = user.FirstSignTime.Format("2006-01-02")
		result.LastSignTime = user.LastSignTime.Format("2006-01-02")
		if days == 1 && countDays >= int32(baseData.RemakeDay) { // 昨天 且 超过重制
			result.FirstSignTime = ""
			result.LastSignTime = ""
		} else if days == 0 { // 今天已签到
			result.IsTodaySign = 1
		}
	}
	if result.FirstSignTime != "" && result.LastSignTime != "" {
		firstSignTime := carbon.Parse(carbon.CreateFromStdTime(user.FirstSignTime).StartOfDay().String()).StdTime()
		result.Days = int64(lastSignTime.Sub(firstSignTime).Hours()/24) + 1
		startTime := user.FirstSignTime
		endTime := user.LastSignTime
		var liuSui model.SignRewardSum
		userSignTb := server.DaoxHashGame().XActiveLogUserSign
		userSignDb := server.DaoxHashGame().XActiveLogUserSign.WithContext(context.Background())
		err = userSignDb.Select(userSignTb.RecordID.Count().As(userSignTb.RecordID.ColumnName().String()),
			userSignTb.RewardAmount.Sum().As(userSignTb.RewardAmount.ColumnName().String()),
		).Where(userSignTb.UserID.Eq(int32(token.UserId))).Where(userSignTb.RecordTime.Between(startTime, endTime)).Scan(&liuSui)
		if err != nil {
			logs.Error("signRewardData userSignDb err", err)
			return result, errors.New("系统错误,请稍后再试")
		}
		result.RewardAmount = liuSui.RewardAmount
	}
	return result, nil
}

// 推荐好友单人奖励
func RecommendFriendRewardIndividual(tx *dao.Query, recharge *model2.XRecharge, userId, agentId int32, registerTime time.Time, ctx *abugo.AbuHttpContent) error {
	defer recover()

	// 记录函数调用开始日志
	logs.Info("RecommendFriendRewardIndividual 开始处理推荐好友单人奖励: 被邀请用户ID=%d, 邀请人ID=%d, 充值订单=%s, 充值金额=%.2f, 注册时间=%v",
		userId, agentId, recharge.OrderID, recharge.RealAmount, registerTime)

	// 直属用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.FirstSignTime, userTb.LastSignTime, userTb.SellerID, userTb.ChannelID, userTb.InviteRewardUsers).Where(userTb.UserID.Eq(agentId)).First()
	if err != nil {
		logs.Error("RecommendFriendRewardIndividual 查询邀请人信息失败 - 被邀请用户ID=%d, 邀请人ID=%d: %v",
			userId, agentId, err)
		return err
	}
	if user.IsTest == 1 {
		logs.Info("RecommendFriendRewardIndividual 邀请人是测试用户，跳过奖励发放: 被邀请用户ID=%d, 邀请人ID=%d",
			userId, agentId)
		return nil
	}
	token := &server.TokenData{
		SellerId:  int(user.SellerID),
		ChannelId: int(user.ChannelID),
	}
	activeId := utils.KActiveIdRecommendFriendReward
	now := carbon.Parse(carbon.Now().String()).StdTime()
	host := ctx.Host()
	channelId, sellerId := server.GetChannel(ctx, host)
	ADefine, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		// 如果活动不存在，静默返回，不影响主流程
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Info("RecommendFriendRewardIndividual 推荐好友活动未配置，跳过奖励发放: 被邀请用户ID=%d, 邀请人ID=%d, SellerID=%d, ChannelID=%d, ActiveID=%d",
				userId, agentId, token.SellerId, token.ChannelId, activeId)

			// 调用诊断函数，记录详细诊断信息
			diagnosis := DiagnoseRecommendFriendActivity(token.SellerId, token.ChannelId)
			logs.Info("RecommendFriendRewardIndividual 推荐好友活动配置诊断 - 被邀请用户ID=%d, 邀请人ID=%d:\n%s",
				userId, agentId, diagnosis)

			return nil
		}
		logs.Error("RecommendFriendRewardIndividual getADefineInfo err - 被邀请用户ID=%d, 邀请人ID=%d: %v",
			userId, agentId, err)
		return errors.New("系统错误,请稍后再试")
	}
	// 是否活动失效
	_, err = active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return nil
	}
	// 活动配置
	var configData []model.RecommendFriendRewardConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return nil

	}
	var baseData model.RecommendFriendRewardBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return nil
	}
	// 判断是否在规定的时间内
	egulationTime := registerTime.AddDate(0, 0, int(baseData.RegisterDay))
	if !recharge.CreateTime.Before(egulationTime) { // 判断充值时间是否在注册多少天内完成
		logs.Info("RecommendFriendRewardIndividual 充值时间超出有效期，跳过奖励发放: 被邀请用户ID=%d, 邀请人ID=%d, 注册时间=%v, 充值时间=%v, 有效期限=%v",
			userId, agentId, registerTime, recharge.CreateTime, egulationTime)
		return nil
	}
	// 判断首充是否满足
	rechargeDecimal := decimal.NewFromFloat(recharge.RealAmount)
	rechargetEqual := rechargeDecimal.GreaterThanOrEqual(baseData.FirstChargeUstdLimit)
	if !rechargetEqual {
		logs.Info("RecommendFriendRewardIndividual 充值金额不足，跳过奖励发放: 被邀请用户ID=%d, 邀请人ID=%d, 充值金额=%.2f, 最低要求=%.2f",
			userId, agentId, recharge.RealAmount, baseData.FirstChargeUstdLimit)
		return nil
	}
	firstRecharge, _ := rechargeDecimal.Float64()
	realAmount, _ := baseData.Award.Float64()
	withdrawLiuSuiAdd, _ := baseData.Award.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	baseConfigStr, _ := json.Marshal(baseData)
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         2, // 单人自动发放奖励不管后台配置
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             1,
		RealAmount:        realAmount,
		TotalLiushui:      0,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        "单人奖励",
		BalanceCReason:    utils.BalanceCReasonRecommendFriendReward,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     firstRecharge,
		TotalRecharge:     0.0,
		InviteRewardType:  1,
		ChildUserID:       userId,
		OrderId:           user.InviteRewardUsers + 1,
		MinLiuShui:        ADefine.MinLiuShui,
	}
	tx1 := tx.Transaction(func(tx *dao.Query) error {
		// 修改用户为1已返奖励
		userDb = tx.XUser.WithContext(context.Background())
		_, err = userDb.Where(userTb.UserID.Eq(userId)).Updates(map[string]any{
			"IsInviteReward": 1,
		})
		if err != nil {
			logs.Error(err)
			return err
		}
		// 直属邀请人数+1
		userDb = tx.XUser.WithContext(context.Background())
		_, err = userDb.Where(userTb.UserID.Eq(agentId)).Updates(map[string]any{
			"InviteRewardUsers": gorm2.Expr("InviteRewardUsers + ?", 1),
		})
		if err != nil {
			logs.Error(err)
			return err
		}
		err = active.SaveActiveData(tx, user, saveActiveDataInfo)
		if err != nil {
			logs.Error(err)
			return err
		}
		return nil
	})
	if tx1 != nil {
		logs.Error("RecommendFriendRewardIndividual tx1 err - 被邀请用户ID=%d, 邀请人ID=%d: %v",
			userId, agentId, err)
		return errors.New("系统错误,请稍后再试")
	}

	// 记录成功完成日志
	logs.Info("RecommendFriendRewardIndividual 推荐好友单人奖励发放成功: 被邀请用户ID=%d, 邀请人ID=%d, 奖励金额=%.2f, 邀请序号=%d",
		userId, agentId, realAmount, user.InviteRewardUsers+1)

	return nil
}

// 推荐好友额外奖励
func RecommendFriendReward(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	var configData []model.RecommendFriendRewardConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var baseData model.RecommendFriendRewardBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var selectData model.RecommendFriendRewardConfig
	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
		}
	}
	if selectData.ID == 0 {
		return errors.New("参数错误"), utils.ActiveIParamsIllegal
	}
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.InviteRewardUsers).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("RecommendFriendReward user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	// 判断条件是否满足
	if selectData.TotalMax > user.InviteRewardUsers {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	vipInfoTb := server.DaoxHashGame().XVipInfo
	vipInfoDb := server.DaoxHashGame().XVipInfo.WithContext(context.Background())
	satisfyCount, _ := userDb.Where(userTb.AgentID.Eq(user.UserID)).
		Where(userTb.IsInviteReward.Eq(1)).
		Where(vipInfoTb.VipLevel.Gte(baseData.Level)).
		Join(vipInfoDb, vipInfoTb.UserID.EqCol(userTb.UserID)).Count()
	if selectData.TotalMax > int32(satisfyCount) {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(int32(level))).
		Where(activeRewardAuditTb.InviteRewardType.Eq(2)).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("RecommendFriendReward activeRewardAudit err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if activeRewardAudit != nil {
		return errors.New("已申请"), utils.ActiveIAlreadyApply
	}
	realAmount, _ := selectData.AdditionalReward.Float64()
	withdrawLiuSuiAdd, _ := selectData.AdditionalReward.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	var baseConfigStr []byte
	totalLiushui := 0.0
	firstRecharge := 0.0
	totalRecharge := 0.0
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         ADefine.AuditType,
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             level,
		RealAmount:        realAmount,
		TotalLiushui:      totalLiushui,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        "额外奖励",
		BalanceCReason:    utils.BalanceCReasonRecommendFriendReward,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     firstRecharge,
		TotalRecharge:     totalRecharge,
		InviteRewardType:  2,
		InviteRewardUsers: int32(satisfyCount),
		MinLiuShui:        ADefine.MinLiuShui,
	}
	err = active.SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("RecommendFriendReward saveActiveData err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

// 推荐好友额外奖励 详情
func RecommendFriendRewardData(activeId int, isAgent int, ctx *abugo.AbuHttpContent) (model.RecommendFriendRewardRes, error) {
	defer recover()
	var result model.RecommendFriendRewardRes
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	_, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("RecommendFriendRewardData GetActiveDefine err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.InviteRewardUsers).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("RecommendFriendRewardData user err", err)
		return result, err
	}
	var liuSui model.RecommendFriendRewardSum
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	err = activeRewardAuditDb.Select(activeRewardAuditTb.Amount.Sum().As(activeRewardAuditTb.Amount.ColumnName().String())).
		Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).Scan(&liuSui)
	if err != nil {
		logs.Error("RecommendFriendRewardData activeRewardAuditDb err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	result.Num = user.InviteRewardUsers
	result.RewardAmount = liuSui.Amount
	if isAgent == 2 {
		//获取邀请码
		presult, err := server.Db().CallProcedure("x_api_new_agent_getcode", token.UserId)
		if err != nil {
			logs.Error("RecommendFriendRewardData x_api_new_agent_getcode err", err)
			return result, errors.New("系统错误,请稍后再试")
		}
		AgentCode := abugo.GetStringFromInterface((*presult)["AgentCode"])
		result.AgentCode = AgentCode
	}
	return result, nil
}

// 每周闯关
func WeekendBreakThrough(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	var configData []model.BreakThroughConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var baseData model.BreakThroughBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var selectData model.BreakThroughConfig
	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
		}
	}
	if selectData.ID == 0 {
		return errors.New("参数错误"), utils.ActiveIParamsIllegal
	}
	todayWeek := carbon.Now().DayOfWeek()
	if todayWeek != utils.WeekendMeiZhouSongDays { // 如果不是周一
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("WeekendBreakThrough user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	// 是否已经领取
	todayStartTime := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(int32(level))).
		Where(activeRewardAuditTb.CreateTime.Between(todayStartTime, now)).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("WeekendBreakThrough activeRewardAudit err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if activeRewardAudit != nil {
		return errors.New("已申请"), utils.ActiveIAlreadyApply
	}
	// 判断条件是否满足
	startTime := carbon.Parse(carbon.Now().StartOfWeek().SubWeek().String()).StdTime()
	endTime := carbon.Parse(carbon.Now().EndOfWeek().SubWeek().String()).StdTime()
	result, err := breakThroughLiuShui(ADefine.GameType, startTime, endTime, user.UserID, baseData.TrxPrice)
	if err != nil {
		logs.Error("WeekendBreakThrough breakThroughLiuShui err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	totalEqual := result.LiuSui.GreaterThanOrEqual(selectData.LimitValue)
	if !totalEqual {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	realAmount, _ := selectData.RewardValue.Float64()
	withdrawLiuSuiAdd, _ := selectData.RewardValue.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	baseConfigStr, _ := json.Marshal(baseData)
	totalLiushui, _ := result.LiuSui.Float64()
	firstRecharge := 0.0
	totalRecharge := 0.0
	var BalanceCReason int
	if activeId == utils.KActiveIdDianziMeiZhouSong {
		BalanceCReason = utils.BalanceCReasonADianZiWeekendDaPaiSong
	} else if activeId == utils.KActiveIdZhenrenMeiZhouSong {
		BalanceCReason = utils.BalanceCReasonAZhenRenWeekendDaPaiSong
	} else if activeId == utils.KActiveIdQiPaiMeiZhouSong {
		BalanceCReason = utils.BalanceCReasonAQiPaihWeekendDaPaiSong
	}
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         ADefine.AuditType,
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             level,
		RealAmount:        realAmount,
		TotalLiushui:      totalLiushui,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        "",
		BalanceCReason:    BalanceCReason,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     firstRecharge,
		TotalRecharge:     totalRecharge,
		GameType:          ADefine.GameType,
		MinLiuShui:        ADefine.MinLiuShui,
	}
	err = active.SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("WeekendBreakThrough saveActiveData err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

func breakThroughLiuShui(gameType string, startTime, endTime time.Time, userID int32, trxPrice float32) (result model.BreakThroughLiuShui, err error) {
	var haxiLiuShui model.BreakThroughLiuShui
	var thirdLiuShui model.BreakThroughLiuShui
	start := startTime.Format(utils.TimeFormatStr)
	end := endTime.Format(utils.TimeFormatStr)
	customThirdTb := server.DaoxHashGame().XCustomThird
	customThirdDb := server.DaoxHashGame().XCustomThird.WithContext(context.Background())
	if gameType == "" { // 通用
		haxiScan := server.Db().Gorm().Raw("select SUM(CASE WHEN symbol='usdt' THEN LiuSui ELSE LiuSui/? END) AS LiuSui From x_custom_dailly"+
			" WHERE UserId =? AND (`RecordDate` BETWEEN ? AND ?)", trxPrice, userID, start, end).Scan(&haxiLiuShui)
		err = haxiScan.Error
		if err != nil {
			logs.Error("breakThroughLiuShui haxiScan err", err)
			return
		}
		err = customThirdDb.Select(customThirdTb.LiuSui.Sum().As(customThirdTb.LiuSui.ColumnName().String())).
			Where(customThirdTb.UserID.Eq(userID)).Where(customThirdTb.RecordDate.Between(startTime, endTime)).Scan(&thirdLiuShui)
		if err != nil {
			logs.Error("breakThroughLiuShui customThirdDb err", err)
			return
		}
	} else {
		gameTypeArr := strings.Split(gameType, ",")
		var gameTypeIntArr []int32
		for _, s := range gameTypeArr {
			num, _ := strconv.Atoi(s)
			gameTypeIntArr = append(gameTypeIntArr, int32(num))
		}
		var haxiWhere []int32
		var thirdWhere []int32
		for _, gameTypeInt := range gameTypeIntArr {
			if gameTypeInt == 1 || gameTypeInt == 3 {
				haxiWhere = append(haxiWhere, gameTypeInt)
			} else if gameTypeInt == 101 || gameTypeInt == 102 || gameTypeInt == 103 || gameTypeInt == 104 || gameTypeInt == 105 || gameTypeInt == 106 {
				thirdWhere = append(thirdWhere, gameTypeInt-100)
			} else {
				thirdWhere = append(thirdWhere, gameTypeInt)
			}
		}
		// 查询
		if len(haxiWhere) > 0 {
			haxiScan := server.Db().Gorm().Raw("select SUM(CASE WHEN symbol='usdt' THEN LiuSui ELSE LiuSui/? END) AS LiuSui From x_custom_dailly AS A"+
				" INNER JOIN x_tb_game AS B ON A.GameId=B.GameId WHERE UserId =? AND (`RecordDate` BETWEEN ? AND ?) AND B.GameType IN(?)", trxPrice, userID, start, end, haxiWhere).Scan(&haxiLiuShui)
			err = haxiScan.Error
			if err != nil {
				logs.Error("breakThroughLiuShui haxiScan err", err)
				return
			}
		}
		if len(thirdWhere) > 0 {
			err = customThirdDb.Select(customThirdTb.LiuSui.Sum().As(customThirdTb.LiuSui.ColumnName().String())).
				Where(customThirdTb.UserID.Eq(userID)).Where(customThirdTb.RecordDate.Between(startTime, endTime)).
				Where(customThirdTb.ThirdType.In(thirdWhere...)).Scan(&thirdLiuShui)
			if err != nil {
				logs.Error("breakThroughLiuShui customThirdDb err", err)
				return
			}
		}
	}
	result.LiuSui = haxiLiuShui.LiuSui.Add(thirdLiuShui.LiuSui)
	return
}

// 每周闯关 详情
func WeekendBreakThroughData(activeId int, ctx *abugo.AbuHttpContent) (model.WeekendBreakThroughRes, error) {
	defer recover()
	var result model.WeekendBreakThroughRes
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	ADefine, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("WeekendBreakThroughData GetActiveDefine err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	var configData []model.WeekendBreakThroughParam
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return result, errors.New("活动配置有误")
	}
	var baseData model.BreakThroughBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return result, errors.New("活动配置有误")
	}
	todayWeek := carbon.Now().DayOfWeek()
	var startTime time.Time
	var endTime time.Time
	if todayWeek == utils.WeekendMeiZhouSongDays { // 周一显示上周数据
		startTime = carbon.Parse(carbon.Now().StartOfWeek().SubWeek().String()).StdTime()
		endTime = carbon.Parse(carbon.Now().EndOfWeek().SubWeek().String()).StdTime()
	} else {
		// 显示这周数据
		startTime = carbon.Parse(carbon.Now().StartOfWeek().String()).StdTime()
		endTime = carbon.Parse(carbon.Now().EndOfWeek().String()).StdTime()
	}
	liushuiResult, err := breakThroughLiuShui(ADefine.GameType, startTime, endTime, int32(token.UserId), baseData.TrxPrice)
	if err != nil {
		logs.Error("WeekendBreakThroughData breakThroughLiuShui err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	for index, config := range configData {
		if todayWeek == utils.WeekendMeiZhouSongDays {
			equal := liushuiResult.LiuSui.GreaterThanOrEqual(config.LimitValue)
			if equal {
				configData[index].State = 1 // 待领取
			}
		}
	}
	result.List = configData
	result.LiuSui = liushuiResult.LiuSui
	return result, nil
}

// 每日闯关
func TodayBreakThrough(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	var configData []model.BreakThroughConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var baseData model.BreakThroughBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	var selectData model.BreakThroughConfig
	var lastData model.BreakThroughConfig
	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
		}
		than := v.LimitValue.GreaterThan(lastData.LimitValue)
		if than {
			lastData = v
		}
	}
	var memo string
	zero := decimal.NewFromFloat32(0)
	if selectData.ID == 0 {
		than := ADefine.ExtReward.GreaterThan(zero)
		if than && lastData.ID > 0 && lastData.ID+1 == int32(level) { // 额外奖励
			selectData = lastData
			selectData.RewardValue = ADefine.ExtReward
			memo = "额外奖励"
		} else {
			return errors.New("参数错误"), utils.ActiveIParamsIllegal
		}
	}
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("TodayBreakThrough user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	// 是否已经领取
	todayStartTime := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(int32(level))).
		Where(activeRewardAuditTb.CreateTime.Between(todayStartTime, now)).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("TodayBreakThrough activeRewardAudit err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if activeRewardAudit != nil {
		return errors.New("已申请"), utils.ActiveIAlreadyApply
	}
	// 判断条件是否满足
	than := baseData.RechargeAmount.GreaterThan(zero)
	if than { // 充值条件
		// 充值金额
		var liuSui model.RealAmountLiuSui
		rechargeTb := server.DaoxHashGame().XRecharge
		rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())
		err = rechargeDb.Select(rechargeTb.RealAmount.Sum().As(rechargeTb.RealAmount.ColumnName().String())).
			Where(rechargeTb.UserID.Eq(user.UserID)).
			Where(rechargeTb.State.Eq(5)).
			Where(rechargeTb.CreateTime.Between(todayStartTime, now)).Scan(&liuSui)
		if err != nil {
			logs.Error("TodayBreakThrough rechargeDb err", err)
			return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
		}
		// 充值条件
		rechargetEqual := liuSui.RealAmount.GreaterThanOrEqual(baseData.RechargeAmount)
		if !rechargetEqual {
			return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
		}
	}
	result, err := breakThroughLiuShui(ADefine.GameType, todayStartTime, now, user.UserID, baseData.TrxPrice)
	if err != nil {
		logs.Error("TodayBreakThrough breakThroughLiuShui err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	totalEqual := result.LiuSui.GreaterThanOrEqual(selectData.LimitValue)
	if !totalEqual {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	realAmount, _ := selectData.RewardValue.Float64()
	withdrawLiuSuiAdd, _ := selectData.RewardValue.Mul(ADefine.MinLiuShui).Float64()
	configStr, _ := json.Marshal(configData)
	baseConfigStr, _ := json.Marshal(baseData)
	totalLiushui, _ := result.LiuSui.Float64()
	firstRecharge := 0.0
	totalRecharge := 0.0
	var BalanceCReason int
	if activeId == utils.KActiveIdHaXiBreakout {
		BalanceCReason = utils.BalanceCReasonAHashChuangGuan
	} else if activeId == utils.KActiveIdHaXiBreakout_15 {
		BalanceCReason = utils.BalanceCReasonAHashChuangGuan
	} else if activeId == utils.KActiveIdHaXiBreakout_16 {
		BalanceCReason = utils.BalanceCReasonAHashChuangGuan
	} else if activeId == utils.KActiveIdHaXiBreakout_17 {
		BalanceCReason = utils.BalanceCReasonAHashChuangGuan
	} else if activeId == utils.KActiveIdHaXiBreakout_18 {
		BalanceCReason = utils.BalanceCReasonAHashChuangGuan
	} else if activeId == utils.KActiveIdQipaiBreakout {
		BalanceCReason = utils.BalanceCReasonAQiPaiChuangGuan
	} else if activeId == utils.KActiveIdDianziBreakout {
		BalanceCReason = utils.BalanceCReasonADianZiChuangGuan
	} else if activeId == utils.KActiveIdZhenrenBreakout {
		BalanceCReason = utils.BalanceCReasonAZhenRenChuangGuan
	} else if activeId == utils.KActiveIdMidAutumnZhenrenBreakout {
		BalanceCReason = utils.BalanceCReasonAMidAutumnZhenrenChuangGuan
	} else if activeId == utils.KActiveIdMidAutumnBreakout {
		BalanceCReason = utils.BalanceCReasonAMidAutumnMidAutumnChuangGuan
	} else if activeId == utils.KActiveIdCryptoGamesBreakout {
		BalanceCReason = utils.BalanceCReasonACryptoGamesChuangGuan
	}
	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:         ADefine.AuditType,
		SellerID:          int32(token.SellerId),
		ChannelID:         int32(token.ChannelId),
		ActiveId:          activeId,
		Level:             level,
		RealAmount:        realAmount,
		TotalLiushui:      totalLiushui,
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,
		ActiveName:        ADefine.Title,
		ActiveMemo:        memo,
		BalanceCReason:    BalanceCReason,
		ConfigStr:         configStr,
		BastConfigStr:     baseConfigStr,
		FirstRecharge:     firstRecharge,
		TotalRecharge:     totalRecharge,
		GameType:          ADefine.GameType,
		MinLiuShui:        ADefine.MinLiuShui,
	}
	err = active.SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("TodayBreakThrough saveActiveData err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

// 每日闯关 详情
func TodayBreakThroughData(activeId int, ctx *abugo.AbuHttpContent) (model.TodayBreakThroughRes, error) {
	defer recover()
	var result model.TodayBreakThroughRes
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	ADefine, err := active.GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("TodayBreakThroughData GetActiveDefine err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	var baseData model.BreakThroughBaseConfig
	err = json.Unmarshal([]byte(ADefine.BaseConfig), &baseData)
	if err != nil {
		return result, errors.New("活动配置有误")
	}
	todayStartTime := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	liushuiResult, err := breakThroughLiuShui(ADefine.GameType, todayStartTime, now, int32(token.UserId), baseData.TrxPrice)
	if err != nil {
		logs.Error("WeekendBreakThroughData breakThroughLiuShui err", err)
		return result, errors.New("系统错误,请稍后再试")
	}
	result.LiuSui = liushuiResult.LiuSui
	return result, nil
}

// 新首存活动180
func newFirstDeposit(activeId int, level int, token *server.TokenData) (error, int) {
	defer recover()
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, activeId)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, utils.ActiveINotDefine
	}
	var configData []model.NewFirstDepositConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	if len(configData) == 0 {
		return errors.New("活动配置有误"), utils.ActiveIParamsIllegal
	}
	selectDataCount := len(configData)
	// 用户
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("newFirstDeposit user err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	if user.IsTest == 1 {
		return errors.New("该玩家禁止领取"), utils.ActiveIIsTest
	}
	// 是否已经领取
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Order(activeRewardAuditTb.ID.Desc()).First()
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		logs.Error("newFirstDeposit activeRewardAudit err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	var receivedLevel int32
	if activeRewardAudit != nil {
		receivedLevel = activeRewardAudit.ActiveLevel
	}
	if receivedLevel >= int32(selectDataCount) {
		return errors.New("不符合领取条件"), utils.ActiveIAlreadyApply
	}
	// 判断条件是否满足
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())
	recharge, err := rechargeDb.Select(rechargeTb.ID, rechargeTb.RealAmount, rechargeTb.PayTime).
		Where(rechargeTb.UserID.Eq(int32(token.UserId))).
		Where(rechargeTb.State.Eq(5)).
		Order(rechargeTb.PayTime.Asc()).
		Limit(selectDataCount).Find()
	if err != nil {
		logs.Error("newFirstDeposit recharge err", err)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	rechargeCount := len(recharge)
	if rechargeCount == 0 || int32(rechargeCount) <= receivedLevel {
		return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
	}
	rechargeFirst := recharge[0]
	if rechargeFirst != nil {
		// 新用户定义
		limitTime := carbon.Parse(utils.NewFirstDepositDate).StdTime()
		if rechargeFirst.PayTime.Before(limitTime) {
			return errors.New(utils.ActiveRNocondition), utils.ActiveINocondition
		}
	}
	percentage := decimal.NewFromInt32(100)
	var resData []model.NewFirstData
	for _, v := range configData {
		if v.ID > receivedLevel && int32(rechargeCount) >= v.ID {
			var data model.NewFirstData
			giveProportion := v.GiveProportion.Div(percentage)
			rechargetAmount := recharge[v.ID-1].RealAmount // 充值金额
			rechargetDecimal := decimal.NewFromFloat(rechargetAmount)
			amountDecimal := rechargetDecimal.Mul(giveProportion)
			equal := amountDecimal.GreaterThanOrEqual(v.GiveLimit)
			realDecimal := decimal.NewFromInt32(0)
			if equal {
				realDecimal = realDecimal.Add(v.GiveLimit)
			} else {
				realDecimal = realDecimal.Add(amountDecimal)
			}
			data.Level = v.ID
			data.RealAmount, _ = realDecimal.Float64()
			data.TotalRecharge = rechargetAmount
			// 本金 + 彩金 * 流水倍数
			totalDecimal := rechargetDecimal.Add(realDecimal)
			data.WithdrawLiuSuiAdd, _ = totalDecimal.Mul(v.LiushuiMultiple).Float64()
			data.MinLiuShui = v.LiushuiMultiple
			resData = append(resData, data)
		}
	}

	configStr, _ := json.Marshal(configData)
	firstRecharge := 0.0

	saveActiveDataInfo := model.SaveActiveDataInfo{
		AuditType:      ADefine.AuditType,
		SellerID:       int32(token.SellerId),
		ChannelID:      int32(token.ChannelId),
		ActiveId:       activeId,
		TotalLiushui:   0,
		ActiveName:     ADefine.Title,
		ActiveMemo:     "",
		BalanceCReason: utils.BalanceCReasonNewFirstDeposit,
		ConfigStr:      configStr,
		FirstRecharge:  firstRecharge,
		GameType:       ADefine.GameType,
	}

	tx1 := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		for _, v := range resData {
			saveActiveDataInfo.Level = int(v.Level)
			saveActiveDataInfo.RealAmount = v.RealAmount
			saveActiveDataInfo.WithdrawLiuSuiAdd = v.WithdrawLiuSuiAdd
			saveActiveDataInfo.TotalRecharge = v.TotalRecharge
			saveActiveDataInfo.MinLiuShui = v.MinLiuShui
			err = active.SaveActiveData(tx, user, saveActiveDataInfo)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if tx1 != nil {
		logs.Error("newFirstDeposit tx1 err", tx1)
		return errors.New("系统错误,请稍后再试"), utils.ActiveIInternal
	}
	return nil, utils.ActiveISuccess
}

// DiagnoseRecommendFriendActivity 诊断推荐好友活动配置问题
// 参数:
//   - sellerId: 商户ID
//   - channelId: 渠道ID
//
// 返回:
//   - string: 诊断结果详情
func DiagnoseRecommendFriendActivity(sellerId, channelId int) string {
	gdb := server.Db().Gorm()
	activeId := utils.KActiveIdRecommendFriendReward

	var result strings.Builder
	result.WriteString(fmt.Sprintf("=== 推荐好友活动配置诊断 ===\n"))
	result.WriteString(fmt.Sprintf("商户ID: %d, 渠道ID: %d, 活动ID: %d\n\n", sellerId, channelId, activeId))

	// 1. 检查目标配置是否存在
	var targetConfig model.ActiveDefine
	err := gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ?", sellerId, channelId, activeId).
		First(&targetConfig).Error

	if err == nil {
		result.WriteString("✅ 目标配置存在\n")
		result.WriteString(fmt.Sprintf("   - 配置ID: %d\n", targetConfig.Id))
		result.WriteString(fmt.Sprintf("   - 活动名称: %s\n", targetConfig.Title))
		result.WriteString(fmt.Sprintf("   - 状态: %d\n", targetConfig.State))

		// 检查时间
		if targetConfig.EffectStartTime > 0 {
			startTime := time.UnixMilli(targetConfig.EffectStartTime)
			result.WriteString(fmt.Sprintf("   - 开始时间: %v\n", startTime))
		}
		if targetConfig.EffectEndTime > 0 {
			endTime := time.UnixMilli(targetConfig.EffectEndTime)
			result.WriteString(fmt.Sprintf("   - 结束时间: %v\n", endTime))
		}

		// 检查配置内容
		result.WriteString(fmt.Sprintf("   - Config长度: %d\n", len(targetConfig.Config)))
		result.WriteString(fmt.Sprintf("   - BaseConfig长度: %d\n", len(targetConfig.BaseConfig)))

		if len(targetConfig.Config) == 0 {
			result.WriteString("   ⚠️  Config配置为空\n")
		}
		if len(targetConfig.BaseConfig) == 0 {
			result.WriteString("   ⚠️  BaseConfig配置为空\n")
		}
	} else {
		result.WriteString("❌ 目标配置不存在\n")
		result.WriteString(fmt.Sprintf("   错误: %v\n", err))
	}

	// 2. 检查该商户的所有活动配置
	var sellerConfigs []model.ActiveDefine
	err = gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ?", sellerId).
		Find(&sellerConfigs).Error

	if err == nil {
		result.WriteString(fmt.Sprintf("\n📋 该商户(SellerId=%d)的所有活动配置:\n", sellerId))
		for _, config := range sellerConfigs {
			result.WriteString(fmt.Sprintf("   - 活动ID: %d, 渠道ID: %d, 名称: %s, 状态: %d\n",
				config.ActiveId, config.ChannelId, config.Title, config.State))
		}
	}

	// 3. 检查该活动ID在其他商户的配置
	var otherConfigs []model.ActiveDefine
	err = gdb.Table(utils.TableActiveDefine).
		Where("ActiveId = ?", activeId).
		Limit(10).
		Find(&otherConfigs).Error

	if err == nil {
		result.WriteString(fmt.Sprintf("\n🔍 推荐好友活动(ActiveId=%d)在其他商户的配置:\n", activeId))
		if len(otherConfigs) == 0 {
			result.WriteString("   ❌ 没有任何商户配置了推荐好友活动\n")
		} else {
			for _, config := range otherConfigs {
				result.WriteString(fmt.Sprintf("   - 商户ID: %d, 渠道ID: %d, 名称: %s, 状态: %d\n",
					config.SellerId, config.ChannelId, config.Title, config.State))
			}
		}
	}

	// 4. 建议解决方案
	result.WriteString("\n💡 解决建议:\n")
	if targetConfig.Id == 0 {
		result.WriteString("   1. 在后台管理系统中为该商户/渠道配置推荐好友活动\n")
		result.WriteString("   2. 确保活动状态为启用(State=1)\n")
		result.WriteString("   3. 设置正确的活动时间范围\n")
		result.WriteString("   4. 配置完整的Config和BaseConfig内容\n")

		if len(otherConfigs) > 0 {
			result.WriteString(fmt.Sprintf("   5. 可以参考商户ID=%d的配置进行设置\n", otherConfigs[0].SellerId))
		}
	} else {
		if targetConfig.State != 1 {
			result.WriteString("   1. 将活动状态设置为启用(State=1)\n")
		}
		if len(targetConfig.Config) == 0 || len(targetConfig.BaseConfig) == 0 {
			result.WriteString("   2. 完善活动配置内容(Config和BaseConfig)\n")
		}
	}

	return result.String()
}

// ProcessRecommendFriendReward 处理推荐好友奖励
// 参数:
//   - userId: 用户ID
//   - rechargeId: 订单ID
//
// 返回:
//   - bool: 处理是否成功
//
// 功能:
//   - 根据用户ID和订单ID查询相关信息
//   - 检查是否为首次充值且有推荐人
//   - 异步处理推荐好友奖励
//   - 更新充值记录的调整状态
func ProcessRecommendFriendReward(userId, orderId int32) bool {
	// 查询充值记录
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())
	recharge, err := rechargeDb.Where(rechargeTb.ID.Eq(orderId)).First()
	if err != nil {
		logs.Error("ProcessRecommendFriendReward 查询充值记录失败: userId=%d, orderId=%s, err=%v", userId, orderId, err)
		return false
	}

	// 查询用户信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.AgentID, userTb.RegisterTime, userTb.IsInviteReward).Where(userTb.UserID.Eq(int32(userId))).First()
	if err != nil {
		logs.Error("ProcessRecommendFriendReward 查询用户信息失败: userId=%d, orderId=%s, err=%v", userId, orderId, err)
		// 即使查询失败也要更新充值状态
		_, _ = rechargeDb.Where(rechargeTb.ID.Eq(recharge.ID)).Update(rechargeTb.AdJustState, 3)
		return false
	}

	if recharge.IsFirst == 1 && user.AgentID > 0 && user.IsInviteReward == 2 {
		// 直接调用推荐好友奖励处理函数，不使用事务，同步执行
		err := RecommendFriendRewardIndividual(nil, recharge, int32(userId), user.AgentID, user.RegisterTime, nil)
		if err != nil {
			logs.Error("推荐好友奖励处理失败: userId=%d, orderId=%s, err=%v", userId, orderId, err)
			// 即使奖励处理失败，也要更新充值状态
			_, _ = rechargeDb.Where(rechargeTb.ID.Eq(recharge.ID)).Update(rechargeTb.AdJustState, 3)
			return false
		}

		// 更新充值记录的调整状态
		_, err = rechargeDb.Where(rechargeTb.ID.Eq(recharge.ID)).Update(rechargeTb.AdJustState, 3)
		if err != nil {
			logs.Error("ProcessRecommendFriendReward 更新充值状态失败: userId=%d, orderId=%s, err=%v", userId, orderId, err)
			return false
		}

		logs.Info("推荐好友奖励处理成功: userId=%d, orderId=%s, agentId=%d", userId, orderId, user.AgentID)
		return true
	} else {
		// 不符合奖励条件，直接更新充值状态
		_, err := rechargeDb.Where(rechargeTb.ID.Eq(recharge.ID)).Update(rechargeTb.AdJustState, 3)
		if err != nil {
			logs.Error("ProcessRecommendFriendReward 更新充值状态失败: userId=%d, orderId=%s, err=%v", userId, orderId, err)
			return false
		}
		logs.Info("ProcessRecommendFriendReward 不符合奖励条件: userId=%d, orderId=%s, isFirst=%d, agentId=%d, isInviteReward=%d",
			userId, orderId, recharge.IsFirst, user.AgentID, user.IsInviteReward)
		return true // 不符合奖励条件，但处理流程是成功的
	}
}
