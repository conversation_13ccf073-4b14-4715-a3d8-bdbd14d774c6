package third_game

// x_third_amount_log
type ThirdAmountLog struct {
	Id            int     `json:"Id" gorm:"column:Id"`
	Brand         string  `json:"Brand" gorm:"column:Brand"`
	UserId        int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount  float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount        float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount   float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	CreateTime    string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo          string  `json:"Memo" gorm:"column:Memo"`
	TransactionId string  `json:"TransactionId" gorm:"column:TransactionId"`
	ThirdId       string  `json:"ThirdId" gorm:"column:ThirdId"`
	Reason        int     `json:"Reason" gorm:"column:Reason"`
}
