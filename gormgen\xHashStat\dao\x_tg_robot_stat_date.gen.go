// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashStat/model"
)

func newXTgRobotStatDate(db *gorm.DB, opts ...gen.DOOption) xTgRobotStatDate {
	_xTgRobotStatDate := xTgRobotStatDate{}

	_xTgRobotStatDate.xTgRobotStatDateDo.UseDB(db, opts...)
	_xTgRobotStatDate.xTgRobotStatDateDo.UseModel(&model.XTgRobotStatDate{})

	tableName := _xTgRobotStatDate.xTgRobotStatDateDo.TableName()
	_xTgRobotStatDate.ALL = field.NewAsterisk(tableName)
	_xTgRobotStatDate.RecordDate = field.NewTime(tableName, "RecordDate")
	_xTgRobotStatDate.RobotID = field.NewInt64(tableName, "RobotId")
	_xTgRobotStatDate.TgChatID = field.NewInt64(tableName, "TgChatId")
	_xTgRobotStatDate.ActionType = field.NewInt32(tableName, "ActionType")
	_xTgRobotStatDate.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRobotStatDate.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgRobotStatDate.ActionCount = field.NewInt32(tableName, "ActionCount")
	_xTgRobotStatDate.IsInResourceDb = field.NewInt32(tableName, "IsInResourceDb")
	_xTgRobotStatDate.RobotUsername = field.NewString(tableName, "RobotUsername")
	_xTgRobotStatDate.SellerName = field.NewString(tableName, "SellerName")
	_xTgRobotStatDate.ChannelName = field.NewString(tableName, "ChannelName")
	_xTgRobotStatDate.Memo = field.NewString(tableName, "Memo")
	_xTgRobotStatDate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTgRobotStatDate.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTgRobotStatDate.fillFieldMap()

	return _xTgRobotStatDate
}

// xTgRobotStatDate 机器人日统计
type xTgRobotStatDate struct {
	xTgRobotStatDateDo xTgRobotStatDateDo

	ALL            field.Asterisk
	RecordDate     field.Time   // 日期
	RobotID        field.Int64  // 机器人Id
	TgChatID       field.Int64  //  tg用户Id
	ActionType     field.Int32  // 行为分类
	SellerID       field.Int32  // 运营商id
	ChannelID      field.Int32  // 渠道id
	ActionCount    field.Int32  // 行为次数
	IsInResourceDb field.Int32  // 是否在库
	RobotUsername  field.String // 机器人Username
	SellerName     field.String // 运营商名称
	ChannelName    field.String // 渠道名称
	Memo           field.String // 备注
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTgRobotStatDate) Table(newTableName string) *xTgRobotStatDate {
	x.xTgRobotStatDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRobotStatDate) As(alias string) *xTgRobotStatDate {
	x.xTgRobotStatDateDo.DO = *(x.xTgRobotStatDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRobotStatDate) updateTableName(table string) *xTgRobotStatDate {
	x.ALL = field.NewAsterisk(table)
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.RobotID = field.NewInt64(table, "RobotId")
	x.TgChatID = field.NewInt64(table, "TgChatId")
	x.ActionType = field.NewInt32(table, "ActionType")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ActionCount = field.NewInt32(table, "ActionCount")
	x.IsInResourceDb = field.NewInt32(table, "IsInResourceDb")
	x.RobotUsername = field.NewString(table, "RobotUsername")
	x.SellerName = field.NewString(table, "SellerName")
	x.ChannelName = field.NewString(table, "ChannelName")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRobotStatDate) WithContext(ctx context.Context) *xTgRobotStatDateDo {
	return x.xTgRobotStatDateDo.WithContext(ctx)
}

func (x xTgRobotStatDate) TableName() string { return x.xTgRobotStatDateDo.TableName() }

func (x xTgRobotStatDate) Alias() string { return x.xTgRobotStatDateDo.Alias() }

func (x xTgRobotStatDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRobotStatDateDo.Columns(cols...)
}

func (x *xTgRobotStatDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRobotStatDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["RobotId"] = x.RobotID
	x.fieldMap["TgChatId"] = x.TgChatID
	x.fieldMap["ActionType"] = x.ActionType
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["ActionCount"] = x.ActionCount
	x.fieldMap["IsInResourceDb"] = x.IsInResourceDb
	x.fieldMap["RobotUsername"] = x.RobotUsername
	x.fieldMap["SellerName"] = x.SellerName
	x.fieldMap["ChannelName"] = x.ChannelName
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTgRobotStatDate) clone(db *gorm.DB) xTgRobotStatDate {
	x.xTgRobotStatDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRobotStatDate) replaceDB(db *gorm.DB) xTgRobotStatDate {
	x.xTgRobotStatDateDo.ReplaceDB(db)
	return x
}

type xTgRobotStatDateDo struct{ gen.DO }

func (x xTgRobotStatDateDo) Debug() *xTgRobotStatDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRobotStatDateDo) WithContext(ctx context.Context) *xTgRobotStatDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRobotStatDateDo) ReadDB() *xTgRobotStatDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRobotStatDateDo) WriteDB() *xTgRobotStatDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRobotStatDateDo) Session(config *gorm.Session) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRobotStatDateDo) Clauses(conds ...clause.Expression) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRobotStatDateDo) Returning(value interface{}, columns ...string) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRobotStatDateDo) Not(conds ...gen.Condition) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRobotStatDateDo) Or(conds ...gen.Condition) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRobotStatDateDo) Select(conds ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRobotStatDateDo) Where(conds ...gen.Condition) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRobotStatDateDo) Order(conds ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRobotStatDateDo) Distinct(cols ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRobotStatDateDo) Omit(cols ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRobotStatDateDo) Join(table schema.Tabler, on ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRobotStatDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRobotStatDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRobotStatDateDo) Group(cols ...field.Expr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRobotStatDateDo) Having(conds ...gen.Condition) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRobotStatDateDo) Limit(limit int) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRobotStatDateDo) Offset(offset int) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRobotStatDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRobotStatDateDo) Unscoped() *xTgRobotStatDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRobotStatDateDo) Create(values ...*model.XTgRobotStatDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRobotStatDateDo) CreateInBatches(values []*model.XTgRobotStatDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRobotStatDateDo) Save(values ...*model.XTgRobotStatDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRobotStatDateDo) First() (*model.XTgRobotStatDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatDate), nil
	}
}

func (x xTgRobotStatDateDo) Take() (*model.XTgRobotStatDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatDate), nil
	}
}

func (x xTgRobotStatDateDo) Last() (*model.XTgRobotStatDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatDate), nil
	}
}

func (x xTgRobotStatDateDo) Find() ([]*model.XTgRobotStatDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRobotStatDate), err
}

func (x xTgRobotStatDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRobotStatDate, err error) {
	buf := make([]*model.XTgRobotStatDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRobotStatDateDo) FindInBatches(result *[]*model.XTgRobotStatDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRobotStatDateDo) Attrs(attrs ...field.AssignExpr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRobotStatDateDo) Assign(attrs ...field.AssignExpr) *xTgRobotStatDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRobotStatDateDo) Joins(fields ...field.RelationField) *xTgRobotStatDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRobotStatDateDo) Preload(fields ...field.RelationField) *xTgRobotStatDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRobotStatDateDo) FirstOrInit() (*model.XTgRobotStatDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatDate), nil
	}
}

func (x xTgRobotStatDateDo) FirstOrCreate() (*model.XTgRobotStatDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotStatDate), nil
	}
}

func (x xTgRobotStatDateDo) FindByPage(offset int, limit int) (result []*model.XTgRobotStatDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRobotStatDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRobotStatDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRobotStatDateDo) Delete(models ...*model.XTgRobotStatDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRobotStatDateDo) withDO(do gen.Dao) *xTgRobotStatDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
