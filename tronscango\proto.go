package tronscango

type Response struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	//Data interface{} `json:"data"`
}

type AddressTransferGetListResData struct {
	AddressCheck uint32 `json:"Address_Check" gorm:"column:Address_Check"`
	Address      string `json:"Address" gorm:"column:Address"`
	AddressLevel int32  `json:"AddressLevel" gorm:"column:AddressLevel"`
	TransferTime string `json:"TransferTime" gorm:"column:TransferTime"`
}

type AddressTransferGetListReq struct {
	AddressList string `json:"addressList" validate:"required"`
	Loops       int    `json:"loops" validate:"gte=0,lte=5"`
}

type AddressTransferGetListRes struct {
	Response
	Data []AddressTransferGetListResData `json:"data"`
}
