package single

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"io"
	"math"
	"strconv"
	"time"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"
)

// Define Pachinko-specific error codes
const (
	Pachinko_CodeUnknownError    = 1000
	Pachinko_CodeInvalidPlayerId = 1001
	Pachinko_CodeVendorDisabled  = 1002

	Pachinko_ExchangeRate        = 22.0  // 转换幣值，按 1:22 转为 CNY
	Pachinko_AmplificationFactor = 220.0 // 放大倍数 先按汇率转换1:22，最低投注100故需要在放大220倍才能下注
)

// PachinkoService represents the service for handling Pachinko game API requests
type PachinkoService struct {
	apiUrl                string
	currency              string
	appIdKey              string // APPID KEY
	secretIv              string // Secret (IV)
	walletKey             string // Secret (IV)
	gameCode              string // 游戏代码
	brandName             string
	RefreshUserAmountFunc func(int) error
}

// TradingPointsResponse represents the response structure for point transactions
type TradingPointsResponse struct {
	ErrorMsg *string `json:"errorMsg"`
}

func NewPachinkoService(temp map[string]string, fc func(int) error) *PachinkoService {
	return &PachinkoService{
		apiUrl:                temp["url"],
		appIdKey:              temp["appIdKey"],
		secretIv:              temp["secretIv"],
		currency:              temp["currency"],
		walletKey:             temp["walletKey"],
		brandName:             "pachinko",
		RefreshUserAmountFunc: fc,
	}
}

// Balance handles balance inquiry requests
func (s *PachinkoService) Balance(ctx *abugo.AbuHttpContent) {
	// BalanceRequest represents the request structure for balance inquiry
	type balanceRequest struct {
		Cmd  string `json:"cmd"`
		UID  string `json:"uid"`
		Time string `json:"time"`
		Sign string `json:"sign"`
	}

	// BalanceResponse represents the response structure for balance inquiry
	type balanceResponse struct {
		ErrorMsg *string `json:"errorMsg"`
		Currency string  `json:"currency"`
		Balance  float64 `json:"balance,omitempty"`
	}

	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Info("Pachinko 获取原始数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to read request")
		return
	}

	// 2. 解析请求
	var req balanceRequest
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Info("Pachinko 解析请求数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "invalid request data")
		return
	}

	// 	// 密钥ID
	kid := s.appIdKey
	if !s.verifyJWT(req.Sign, kid) {
		logs.Info("Pachinko 签名验证失败")
		s.respondWithError(ctx, Pachinko_CodeVendorDisabled, "invalid sign")
		return
	}

	// 4. 获取用户余额
	userId, _ := strconv.Atoi(req.UID)
	balance, err := getUserBalance(userId)
	if err != nil {
		logs.Info("Pachinko Balance 获取余额失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "failed to get balance")
		return
	}
	//按1：22比值转换并放大
	balance = balance / Pachinko_ExchangeRate * Pachinko_AmplificationFactor
	// 5. 返回成功响应
	resp := balanceResponse{
		ErrorMsg: nil,
		Currency: s.currency,
		Balance:  balance,
	}
	ctx.RespJson(resp)
	logs.Info("Pachinko Balance 查询余额成功, uid=", req.UID, "balance=", balance)
}

// TradingPoints handles point transaction requests
func (s *PachinkoService) TradingPoints(ctx *abugo.AbuHttpContent) {
	// 输出请求头信息
	//headers := ctx.Gin().Request.Header
	//var headerInfo strings.Builder
	//headerInfo.WriteString("\nPachinko Debit 请求头信息:\n")
	//headerInfo.WriteString("----------------------------------------\n")
	//for key, values := range headers {
	//	headerInfo.WriteString(fmt.Sprintf("%-20s: %v\n", key, values))
	//}
	//headerInfo.WriteString("----------------------------------------")
	//logs.Info(headerInfo.String())

	type tradingPointsRequest struct {
		Cmd        string  `json:"cmd"`
		OrderID    string  `json:"orderId"`
		UID        string  `json:"uid"`
		GameTypeID int     `json:"gametypeId"`
		MachineID  int     `json:"machineId"`
		Amount     float64 `json:"amount"`
		Reason     string  `json:"reason"`
		Time       string  `json:"time"`
		Sign       string  `json:"sign"`
	}

	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Info("Pachinko 获取原始数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to read request")
		return
	}

	// 重新设置请求体，以便后续方法可以读取
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 2. 解析请求
	var req tradingPointsRequest
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Info("Pachinko 解析请求数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "invalid request data")
		return
	}

	// 3. 根据 cmd 调用相应的方法
	switch req.Cmd {
	case "GetBalance":
		s.Balance(ctx)
		return
	case "TradingPoints":
		if req.Amount > 0 {
			s.Credit(ctx)
		} else if req.Amount < 0 {
			s.Debit(ctx)
		} else {
			s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "amount cannot be zero")
		}
		return
	default:
		s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "invalid cmd")
		return
	}
}

// DebitRequest represents the request structure for debit transactions
type debitRequest struct {
	UID        string  `json:"uid"`
	OrderID    string  `json:"orderId"`
	GameTypeID int     `json:"gametypeId"`
	MachineID  int     `json:"machineId"`
	Amount     float64 `json:"amount"`
	Reason     string  `json:"reason"`
	Sign       string  `json:"sign"`
	Time       string  `json:"time"`
}

// Debit handles the deduction of points for the Pachinko game
func (s *PachinkoService) Debit(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Info("Pachinko Debit 获取原始数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to read request")
		return
	}
	logs.Info("Pachinko Debit原始请求数据:", string(bodyBytes))

	// 2. 解析请求
	var req debitRequest
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Info("Pachinko Debit 解析请求数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "invalid request data")
		return
	}

	if !s.verifyJWT(req.Sign, req.UID) {
		logs.Info("Pachinko Debit 签名验证失败")
		s.respondWithError(ctx, Pachinko_CodeVendorDisabled, "invalid sign")
		return
	}

	// 4. 检查订单ID是否已存在
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, req.OrderID, ctx.Gin().Request.URL.String())
	if err != nil {
		logs.Info("Pachinko Debit 检测是否重复请求 发生错误 orderId=", req.OrderID, " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "System error")
		return
	}
	if duplicate {
		logs.Info("Pachinko Debit 检测到重复请求 orderId=", req.OrderID)
		ctx.RespJson(duplicateResult)
		return
	}
	userId_ := req.UID
	userId, _ := strconv.Atoi(userId_)
	amount := math.Abs(req.Amount) //三方返回的的Amount是负或者正数，需要取绝对值变正数，以扣款或加款
	//按1：22比值转换
	amount = amount * Pachinko_ExchangeRate / Pachinko_AmplificationFactor
	// 开启事务处理
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 5. 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
			Select("UserId,SellerId,ChannelId,Amount,Token").
			Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Info("Pachinko Debit 失败，会员不存在, orderId=", req.OrderID, "uid=", userId)
			}
			return e
		}

		balance := userBalance.Amount
		// 6. 检查余额
		// 将余额和金额转换为相同的单位进行比较
		if balance < 0 || balance < amount {
			return errors.New("insufficient balance")
		}

		// 7. 更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, amount).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount-?", amount),
			})
		if resultTmp.Error != nil {
			return resultTmp.Error
		}
		if resultTmp.RowsAffected <= 0 {
			return errors.New("更新条数0")
		}

		// 8. 创建账变记录
		afterBalance := balance - amount
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       -amount,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceCReasonPachinkoBet,
			Memo:         fmt.Sprintf(" bet,orderId:%s", req.OrderID),
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   time.Now().Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			return e
		}

		return nil
	})

	if err != nil {
		logs.Info("Pachinko Debit 失败, uid=", req.UID, "orderId=", req.OrderID, "err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to process transaction")
		return
	}

	logs.Info("Pachinko Debit 成功, uid=", req.UID, "amount=", amount)

	resp := TradingPointsResponse{ErrorMsg: nil}
	ctx.RespJson(resp)

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if err != nil {
			respCode = 1
		}
		base.AddRequestDB(req.OrderID, string(bodyBytes), resp, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	// 发送余额变动通知
	if err == nil && s.RefreshUserAmountFunc != nil {
		go func() {
			if err := s.RefreshUserAmountFunc(userId); err != nil {
				logs.Error("Pachinko Debit 刷新用户余额失败 userId=", userId, " err=", err)
			}
		}()
	}
}

// CreditRequest represents the request structure for credit transactions
type creditRequest struct {
	UID        string  `json:"uid"`
	OrderID    string  `json:"orderId"`
	GameTypeID int     `json:"gametypeId"`
	MachineID  int     `json:"machineId"`
	Amount     float64 `json:"amount"`
	Reason     string  `json:"reason"`
	Sign       string  `json:"sign"`
	Time       string  `json:"time"`
}

// Credit handles the addition of points for the Pachinko game
func (s *PachinkoService) Credit(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Info("Pachinko Credit 获取原始数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to read request")
		return
	}
	logs.Info("Pachinko Credit原始请求数据:", string(bodyBytes))

	// 2. 解析请求
	var req creditRequest
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Info("Pachinko Credit 解析请求数据失败 ", " err=", err)
		s.respondWithError(ctx, Pachinko_CodeInvalidPlayerId, "invalid request data")
		return
	}

	if !s.verifyJWT(req.Sign, req.UID) {
		logs.Info("Pachinko Credit 签名验证失败")
		s.respondWithError(ctx, Pachinko_CodeVendorDisabled, "invalid sign")
		return
	}

	// 4. 检查订单ID是否已存在
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, req.OrderID, ctx.Gin().Request.URL.String())
	if err != nil {
		logs.Info("Pachinko Credit 检测是否重复请求 发生错误 orderId=", req.OrderID, " err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "System error")
		return
	}
	if duplicate {
		logs.Info("Pachinko Credit 检测到重复请求 orderId=", req.OrderID)
		ctx.RespJson(duplicateResult)
		return
	}
	userId_ := req.UID
	userId, _ := strconv.Atoi(userId_)
	amount := math.Abs(req.Amount) //三方返回的的Amount是负或者正数，需要取绝对值变正数，以扣款或加款
	//按1：22比值转换
	amount = amount * Pachinko_ExchangeRate / Pachinko_AmplificationFactor
	// 开启事务处理
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 5. 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
			Select("UserId,SellerId,ChannelId,Amount,Token").
			Where("UserId = ?", req.UID).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Info("Pachinko Credit 失败，会员不存在, orderId=", req.OrderID, "uid=", req.UID)
			}
			return e
		}

		// 6. 更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId=?", req.UID).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount+?", amount),
			})
		if resultTmp.Error != nil {
			return resultTmp.Error
		}
		if resultTmp.RowsAffected <= 0 {
			return errors.New("更新条数0")
		}

		// 7. 创建账变记录
		afterBalance := userBalance.Amount + amount
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceCReasonPachinkoWin,
			Memo:         fmt.Sprintf(" win,orderId:%s", req.OrderID),
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   time.Now().Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			return e
		}

		return nil
	})

	if err != nil {
		logs.Info("Pachinko Credit 失败, uid=", req.UID, "orderId=", req.OrderID, "err=", err)
		s.respondWithError(ctx, Pachinko_CodeUnknownError, "Failed to process transaction")
		return
	}

	logs.Info("Pachinko Credit 成功, uid=", req.UID, "amount=", amount)

	resp := TradingPointsResponse{ErrorMsg: nil}
	ctx.RespJson(resp)

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if err != nil {
			respCode = 1
		}
		base.AddRequestDB(req.OrderID, string(bodyBytes), resp, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	// 发送余额变动通知
	if err == nil && s.RefreshUserAmountFunc != nil {
		go func() {
			if err := s.RefreshUserAmountFunc(userId); err != nil {
				logs.Error("Pachinko Credit 刷新用户余额失败 userId=", userId, " err=", err)
			}
		}()
	}
}

// Login 处理 Pachinko 游戏的登录请求
func (s *PachinkoService) Login(ctx *abugo.AbuHttpContent) {

	type loginRequest struct {
		GameId     string `json:"GameId" validate:"required"` // 游戏code
		LangCode   string `json:"LangCode"`                   // 语言 en
		HomeUrl    string `json:"HomeUrl"`                    // 返回商户地址
		DeviceType string `json:"DeviceType"`                 // 设备类型 mobile
	}

	errcode := 0
	reqdata := loginRequest{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("Pachinko 解析数据失败 ", " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyQQPoker, token.UserId); err != nil {
		logs.Error("Pachinko 登录失败 userId=", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 生成游戏访问网址
	gameAccessURL, err := s.GenerateGameAccessURL(""+fmt.Sprintf("%d", token.UserId), reqdata.HomeUrl, 300)
	if err != nil {
		logs.Info("生成游戏访问网址失败: %v", err)
		ctx.RespErrString(true, &errcode, err.Error())
	} else {
		logs.Error("生成的游戏访问网址: %s", gameAccessURL)
		ctx.Put("url", gameAccessURL)
	}
	ctx.RespOK()
}

// verifyJWT verifies the JWT token
func (s *PachinkoService) verifyJWT(tokenString, uid string) bool {
	parser := &jwt.Parser{
		SkipClaimsValidation: true, // 禁用默认的时间验证
	}

	token, err := parser.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		secret := s.walletKey
		return []byte(secret), nil
	})

	if err != nil {
		logs.Error("JWT验证失败: %v, token: %s", err, tokenString)
		return false
	}

	// 验证 claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		currentTime := time.Now().Unix()
		//logs.Info("JWT验证 - 当前时间:", currentTime)

		// 验证过期时间
		if exp, ok := claims["exp"].(float64); ok {
			expTime := int64(exp)
			//logs.Info("JWT验证 - 过期时间:", expTime)
			if expTime < currentTime {
				logs.Error("JWT token 已过期 - 过期时间:", expTime, " 当前时间:", currentTime)
				return false
			}
		}

		// 验证创建时间，允许10分钟的时间差（前后都允许）
		if iat, ok := claims["iat"].(float64); ok {
			iatTime := int64(iat)
			//logs.Info("JWT验证 - 创建时间:", iatTime)
			timeDiff := currentTime - iatTime
			if timeDiff < -600 || timeDiff > 600 { // 允许正负10分钟的时间差
				logs.Error("JWT token 创建时间不正确 - 创建时间:", iatTime, " 当前时间:", currentTime, " 时间差:", timeDiff, "秒")
				return false
			}
		}
		return true
	}

	logs.Error("JWT token claims验证失败")
	return false
}

// respondWithError 发送错误响应
func (s *PachinkoService) respondWithError(ctx *abugo.AbuHttpContent, code int, message string) {
	errMsg := message
	resp := TradingPointsResponse{ErrorMsg: &errMsg}
	ctx.RespJson(resp)
}
