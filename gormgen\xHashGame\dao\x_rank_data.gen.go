// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRankDatum(db *gorm.DB, opts ...gen.DOOption) xRankDatum {
	_xRankDatum := xRankDatum{}

	_xRankDatum.xRankDatumDo.UseDB(db, opts...)
	_xRankDatum.xRankDatumDo.UseModel(&model.XRankDatum{})

	tableName := _xRankDatum.xRankDatumDo.TableName()
	_xRankDatum.ALL = field.NewAsterisk(tableName)
	_xRankDatum.SellerID = field.NewInt32(tableName, "SellerId")
	_xRankDatum.RankType = field.NewInt32(tableName, "RankType")
	_xRankDatum.Rank = field.NewInt32(tableName, "Rank")
	_xRankDatum.GameType = field.NewInt32(tableName, "GameType")
	_xRankDatum.Symbol = field.NewString(tableName, "Symbol")
	_xRankDatum.UserID = field.NewString(tableName, "UserId")
	_xRankDatum.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xRankDatum.ChainType = field.NewInt32(tableName, "ChainType")
	_xRankDatum.Brand = field.NewString(tableName, "Brand")
	_xRankDatum.GameID = field.NewString(tableName, "GameId")
	_xRankDatum.Address = field.NewString(tableName, "Address")
	_xRankDatum.Amount = field.NewFloat64(tableName, "Amount")
	_xRankDatum.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xRankDatum.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xRankDatum.BetTime = field.NewTime(tableName, "BetTime")
	_xRankDatum.RewardTime = field.NewTime(tableName, "RewardTime")
	_xRankDatum.CreateTime = field.NewTime(tableName, "CreateTime")

	_xRankDatum.fillFieldMap()

	return _xRankDatum
}

type xRankDatum struct {
	xRankDatumDo xRankDatumDo

	ALL      field.Asterisk
	SellerID field.Int32 // 运营商
	/*
		1 中奖日排行,2 中奖周排行,3 中奖月排行,4 中奖实时排行
		11 下注日排行,12 下注周排行,13 下注月排行,14 下注实时排行 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行
	*/
	RankType     field.Int32
	Rank         field.Int32  // 名次
	GameType     field.Int32  // 1哈希游戏 100三方游戏
	Symbol       field.String // 币种
	UserID       field.String // 玩家id
	VipLevel     field.Int32  // Vip等级
	ChainType    field.Int32  // 网链分类 1trc 2erc 3bsc
	Brand        field.String // 游戏厂商
	GameID       field.String // 游戏id
	Address      field.String // 投注地址
	Amount       field.Float64
	RewardRate   field.Float64
	RewardAmount field.Float64
	BetTime      field.Time // 下注时间
	RewardTime   field.Time // 结算时间
	CreateTime   field.Time // 创建时间

	fieldMap map[string]field.Expr
}

func (x xRankDatum) Table(newTableName string) *xRankDatum {
	x.xRankDatumDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRankDatum) As(alias string) *xRankDatum {
	x.xRankDatumDo.DO = *(x.xRankDatumDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRankDatum) updateTableName(table string) *xRankDatum {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.RankType = field.NewInt32(table, "RankType")
	x.Rank = field.NewInt32(table, "Rank")
	x.GameType = field.NewInt32(table, "GameType")
	x.Symbol = field.NewString(table, "Symbol")
	x.UserID = field.NewString(table, "UserId")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.ChainType = field.NewInt32(table, "ChainType")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.Address = field.NewString(table, "Address")
	x.Amount = field.NewFloat64(table, "Amount")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.BetTime = field.NewTime(table, "BetTime")
	x.RewardTime = field.NewTime(table, "RewardTime")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xRankDatum) WithContext(ctx context.Context) *xRankDatumDo {
	return x.xRankDatumDo.WithContext(ctx)
}

func (x xRankDatum) TableName() string { return x.xRankDatumDo.TableName() }

func (x xRankDatum) Alias() string { return x.xRankDatumDo.Alias() }

func (x xRankDatum) Columns(cols ...field.Expr) gen.Columns { return x.xRankDatumDo.Columns(cols...) }

func (x *xRankDatum) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRankDatum) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 17)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["RankType"] = x.RankType
	x.fieldMap["Rank"] = x.Rank
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["ChainType"] = x.ChainType
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["BetTime"] = x.BetTime
	x.fieldMap["RewardTime"] = x.RewardTime
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xRankDatum) clone(db *gorm.DB) xRankDatum {
	x.xRankDatumDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRankDatum) replaceDB(db *gorm.DB) xRankDatum {
	x.xRankDatumDo.ReplaceDB(db)
	return x
}

type xRankDatumDo struct{ gen.DO }

func (x xRankDatumDo) Debug() *xRankDatumDo {
	return x.withDO(x.DO.Debug())
}

func (x xRankDatumDo) WithContext(ctx context.Context) *xRankDatumDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRankDatumDo) ReadDB() *xRankDatumDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRankDatumDo) WriteDB() *xRankDatumDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRankDatumDo) Session(config *gorm.Session) *xRankDatumDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRankDatumDo) Clauses(conds ...clause.Expression) *xRankDatumDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRankDatumDo) Returning(value interface{}, columns ...string) *xRankDatumDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRankDatumDo) Not(conds ...gen.Condition) *xRankDatumDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRankDatumDo) Or(conds ...gen.Condition) *xRankDatumDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRankDatumDo) Select(conds ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRankDatumDo) Where(conds ...gen.Condition) *xRankDatumDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRankDatumDo) Order(conds ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRankDatumDo) Distinct(cols ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRankDatumDo) Omit(cols ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRankDatumDo) Join(table schema.Tabler, on ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRankDatumDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRankDatumDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRankDatumDo) Group(cols ...field.Expr) *xRankDatumDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRankDatumDo) Having(conds ...gen.Condition) *xRankDatumDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRankDatumDo) Limit(limit int) *xRankDatumDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRankDatumDo) Offset(offset int) *xRankDatumDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRankDatumDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRankDatumDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRankDatumDo) Unscoped() *xRankDatumDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRankDatumDo) Create(values ...*model.XRankDatum) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRankDatumDo) CreateInBatches(values []*model.XRankDatum, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRankDatumDo) Save(values ...*model.XRankDatum) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRankDatumDo) First() (*model.XRankDatum, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankDatum), nil
	}
}

func (x xRankDatumDo) Take() (*model.XRankDatum, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankDatum), nil
	}
}

func (x xRankDatumDo) Last() (*model.XRankDatum, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankDatum), nil
	}
}

func (x xRankDatumDo) Find() ([]*model.XRankDatum, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRankDatum), err
}

func (x xRankDatumDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRankDatum, err error) {
	buf := make([]*model.XRankDatum, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRankDatumDo) FindInBatches(result *[]*model.XRankDatum, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRankDatumDo) Attrs(attrs ...field.AssignExpr) *xRankDatumDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRankDatumDo) Assign(attrs ...field.AssignExpr) *xRankDatumDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRankDatumDo) Joins(fields ...field.RelationField) *xRankDatumDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRankDatumDo) Preload(fields ...field.RelationField) *xRankDatumDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRankDatumDo) FirstOrInit() (*model.XRankDatum, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankDatum), nil
	}
}

func (x xRankDatumDo) FirstOrCreate() (*model.XRankDatum, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRankDatum), nil
	}
}

func (x xRankDatumDo) FindByPage(offset int, limit int) (result []*model.XRankDatum, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRankDatumDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRankDatumDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRankDatumDo) Delete(models ...*model.XRankDatum) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRankDatumDo) withDO(do gen.Dao) *xRankDatumDo {
	x.DO = *do.(*gen.DO)
	return x
}
