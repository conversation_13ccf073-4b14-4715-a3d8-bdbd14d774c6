package third

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

// 区块链Lottory
type HashLottoryResp struct {
	Code      int    `json:"code"`
	Timestamp string `json:"timestamp"`
	Msg       string `json:"msg"`
	Data      struct {
		GameUrl string `json:"gameUrl"`
	} `json:"data"`
}

func NewHashLottoryService(appUrl, url, companyKey, name, key, agent string, fc func(int) error) *HashLottoryService {
	return &HashLottoryService{
		Url:                   url,
		AppUrl:                appUrl,
		Name:                  name,
		BrandName:             "og",
		Key:                   key,
		CompanyKey:            company<PERSON>ey,
		Agent:                 agent,
		RefreshUserAmountFunc: fc,
	}
}

type HashLottoryService struct {
	Url                   string
	CompanyKey            string
	AppUrl                string
	BrandName             string
	Name                  string
	Key                   string
	Agent                 string
	RefreshUserAmountFunc func(int) error
}

func (e *HashLottoryService) JD_Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Lang     string
		GameId   int64
		Platform int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("JD_Login:", err)
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}

	token := server.GetToken(ctx)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
	if data == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	username := e.Agent + "_" + strconv.Itoa(token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	// balance := e.UserAmount(token.UserId)

	r := map[string]interface{}{
		"account":      username,
		"gameId":       reqdata.GameId,
		"ip":           ctx.GetIp(),
		"agent":        e.Agent,
		"companyKey":   e.CompanyKey,
		"nickName":     token.Account,
		"platform":     reqdata.Platform,
		"appUrl":       e.AppUrl,
		"theme":        "S004",
		"lobbyType":    "0",
		"token":        UserId2Token(token.UserId),
		"timestamp":    fmt.Sprintf("%d", time.Now().UnixMilli()),
		"languageType": reqdata.Lang,
	}

	// 参数字典排序
	sortParam := utils.SortMapByKeys(r)
	sortJson, _ := json.Marshal(sortParam)
	apiSign := strings.ToLower(string(sortJson) + e.Key)
	hashedkey := utils.Md5V(apiSign)
	payload := strings.NewReader(string(sortJson))

	req, _ := http.NewRequest("POST", e.Url+`/api/game/login`, payload)

	logs.Info("[INFO][OG-GAME]Login:", e.Url+`/api/game/login`, string(sortJson), hashedkey)
	req.Header.Add("Authorization", hashedkey)
	req.Header.Add("content-type", "application/json")
	req.Header.Add("cache-control", "no-cache")
	http.DefaultClient.Timeout = 10 * time.Second
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := http.DefaultClient
	client.Transport = tr
	res, err := client.Do(req)
	if err != nil {
		logs.Error("JD_Login third api error:", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	jdata := HashLottoryResp{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("JD_Login1:", err, string(body))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	if jdata.Code != 0 {
		logs.Error("JD_Login2:", err, jdata.Code, jdata.Msg)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	ctx.Put("url", jdata.Data.GameUrl)
	ctx.RespOK()
}

func (e *HashLottoryService) UserId2Token(UserId int) string {
	plaintext, _ := json.Marshal(map[string]interface{}{
		"userId":    UserId,
		"timestamp": time.Now().UnixMicro(),
	})

	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"
	ciphertext := e.CBCEncrypt(string(plaintext), key)
	return url.QueryEscape(ciphertext)
}

func (e *HashLottoryService) Token2UserId(token string) int {
	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"

	token, _ = url.QueryUnescape(token)
	payload := make(map[string]interface{})
	if err := json.Unmarshal([]byte(e.CBCDecrypt(token, key)), &payload); err != nil {
		logs.Debug("[ERR][HashLottoryService]Token2UserId:", token, err, payload)
		return -1
	}
	return int(payload["userId"].(float64))
}

func (e *HashLottoryService) UserAmount(UserId int) float64 {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Select("Amount").Where(where).GetOne()
	balance := float64(0)
	if udata != nil {
		balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	}
	return balance
}

func (e *HashLottoryService) JD_Balance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token     string `json:"token"`
		AccountId string `json:"accountId"`
		Timestamp int64  `json:"timestamp"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	token := server.GetToken(ctx)
	UserId := token.UserId
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	balance := e.UserAmount(UserId)

	ctx.Gin().JSON(200, gin.H{
		"Code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance},
		"msg":       "",
	})
}

func (e *HashLottoryService) JD_Bet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token     string  `json:"token"`
		Money     float64 `json:"money"`
		GameId    int64   `json:"gameId"`
		OrderId   string  `json:"orderId"`
		AccoundId string  `json:"accoundId"`
		TimeStamp int64   `json:"timeStamp"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	UserId := Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	balance := e.UserAmount(UserId)
	if reqdata.Money < 0 {
		logs.Info("JD_Bet: money lt 0", reqdata)
		ctx.Gin().JSON(200, gin.H{"Code": 9004})
		return
	}
	if balance < reqdata.Money {
		ctx.Gin().JSON(200, gin.H{"Code": 9004})
		return
	}

	reqdataByte, _ := json.Marshal(reqdata)
	logs.Debug("JD_Bet:", reqdata)

	server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", reqdata.Money, UserId, reqdata.Money)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Where(where).GetOne()
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	order := xgo.H{
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
		"BetChannelId": ChannelId,
		"UserId":       UserId,
		"Brand":        "og",
		"ThirdId":      reqdata.OrderId,
		"GameId":       reqdata.GameId,
		"GameName":     e.Name,
		"BetAmount":    reqdata.Money,
		"WinAmount":    0,
		"ValidBet":     0,
		"ThirdTime":    time.Now().Format("2006-01-02 15:04:05"),
		"Currency":     "CNY",
		"RawData":      string(reqdataByte),
		"DataState":    -1,
	}

	server.Db().Table("x_third_quwei_pre_order").Insert(order)

	amountLog := xgo.H{
		"UserId":       UserId,
		"BeforeAmount": balance + reqdata.Money,
		"Amount":       0 - reqdata.Money,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonOgGameBet,
		"Memo":         "og bet,thirdId:" + reqdata.OrderId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)

	ctx.Gin().JSON(200, gin.H{
		"Code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data": gin.H{
			"balance":   balance,
			"money":     reqdata.Money,
			"accoundId": reqdata.AccoundId,
		},
		"msg": "",
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][HashLottoryService] JDB_Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][HashLottoryService] JDB_Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(UserId)
}

func (e *HashLottoryService) JD_End(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token     string  `json:"token"`
		LockMoney float64 `json:"lockMoney"`
		Money     float64 `json:"money"`
		GameId    int64   `json:"gameId"`
		OrderId   string  `json:"orderId"`
		AccoundId string  `json:"accoundId"`
		RoundId   string  `json:"roundId"`
		TimeStamp int64   `json:"timeStamp"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	UserId := Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	balance := e.UserAmount(UserId)

	if balance+reqdata.Money < 0 {
		ctx.Gin().JSON(200, gin.H{"Code": 8100})
		return
	}

	reqDataByte, _ := json.Marshal(reqdata)
	logs.Debug("JD_End:", reqdata)

	where := abugo.AbuDbWhere{}
	where.Add("and", "Brand", "=", "og", nil)
	where.Add("and", "ThirdId", "=", reqdata.OrderId, nil)
	udata, _ := server.Db().Table("x_third_quwei_pre_order").Where(where).GetOne()

	if udata == nil {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}

	// reback money
	rebackMoney := reqdata.Money
	// lose game not need reduce amount
	if reqdata.Money < 0 {
		rebackMoney = 0
	}
	// winAmount gte 0
	winAmount := reqdata.Money
	if winAmount < 0 {
		winAmount = 0
	}
	validBet := math.Abs(reqdata.LockMoney - rebackMoney)
	server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", rebackMoney, UserId)
	server.Db().Conn().Exec("update x_third_quwei_pre_order set WinAmount = ?, ValidBet = ?,DataState = 1 where ThirdId = ?", reqdata.Money, validBet, reqdata.OrderId)

	betTranData := *udata
	//server.Db().Gorm().Table("x_third_quwei_pre_order").Where("Id=?", betTranData["Id"]).Delete(udata)

	delete(betTranData, "Id")
	delete(betTranData, "CreateTime")
	betTranData["WinAmount"] = winAmount
	betTranData["ValidBet"] = validBet
	betTranData["DataState"] = 1
	betTranData["RawData"] = string(reqDataByte)

	server.Db().Table("x_third_quwei").Insert(betTranData)
	where = abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ = server.Db().Table("x_user").Where(where).GetOne()
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])

	amountLog := xgo.H{
		"UserId":       UserId,
		"BeforeAmount": balance - reqdata.Money,
		"Amount":       reqdata.Money,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonOgGameWin,
		"Memo":         "og settle,thirdId:" + reqdata.OrderId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)

	ctx.Gin().JSON(200, gin.H{
		"Code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance},
		"msg":       "",
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][HashLottoryService] JDB_End 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][HashLottoryService] JDB_End 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(UserId)
}

func (e *HashLottoryService) CBCDecrypt(ciphertext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	ciphercode, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return ""
	}

	iv := ciphercode[:aes.BlockSize]        // 密文的前 16 个字节为 iv
	ciphercode = ciphercode[aes.BlockSize:] // 正式密文

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphercode, ciphercode)

	plaintext := string(ciphercode) // ↓ 减去 padding
	return plaintext[:len(plaintext)-int(plaintext[len(plaintext)-1])]
}

func (e *HashLottoryService) CBCEncrypt(plaintext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	blockSize := len(key)
	padding := blockSize - len(plaintext)%blockSize // 填充字节
	if padding == 0 {
		padding = blockSize
	}

	// 填充 padding 个 byte(padding) 到 plaintext
	plaintext += string(bytes.Repeat([]byte{byte(padding)}, padding))
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err = rand.Read(iv); err != nil { // 将同时写到 ciphertext 的开头
		return ""
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], []byte(plaintext))

	return base64.StdEncoding.EncodeToString(ciphertext)
}
