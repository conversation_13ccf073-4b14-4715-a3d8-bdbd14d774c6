package paycontroller

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
	"golang.org/x/crypto/bcrypt"
)

// MainPay 支付控制器实例
var MainPay = new(mainPay)

// mainPay 支付控制器结构体
type mainPay struct {
	Base
}

// MainPayConfig 支付配置结构体
type MainPayConfig struct {
	APIKey      string `json:"api_key"`      // API密钥
	SecretKey   string `json:"secret_key"`   // 密钥
	URL         string `json:"url"`          // API地址
	CallbackURL string `json:"callback_url"` // 回调地址
	Currency    string `json:"currency"`     // 货币类型，例如 MYR
	DoMain      string `json:"domain"`       // 收银台域名
	BcryptKey   string `json:"bcrypt_key"`   // bcrypt密钥
}

// MainPayRechargeResponse API响应结构体
type MainPayRechargeResponse struct {
	Data struct {
		Status     int    `json:"status"`
		StatusText string `json:"status_text"`
		Link       string `json:"link"`
	} `json:"data"`
}

// MainPayCheckStatusResponse 订单状态查询响应结构体
type MainPayCheckStatusResponse struct {
	Data struct {
		Status        string  `json:"status"`
		OrderNo       string  `json:"orderNo"`
		ClientOrderId string  `json:"clientOrderId"`
		Amount        float64 `json:"amount"`
		Currency      string  `json:"currency"`
	} `json:"data"`
}

// MainPayBankResponse 银行列表响应结构体
type MainPayBankResponse struct {
	Data []struct {
		Code string `json:"code"`
		Name string `json:"name"`
	} `json:"data"`
}

// MainPayCallbackRequest 回调请求结构体
type MainPayCallbackRequest struct {
	Token         string `json:"token"`         // 加密的回调令牌
	OrderNo       string `json:"orderNo"`       // 第三方订单号
	ClientOrderId string `json:"clientOrderId"` // 商户订单号
	Amount        string `json:"amount"`        // 订单金额（字符串格式）
	Status        string `json:"status"`        // 订单状态: completed-成功, failed-失败
}

// MainPayCheckStatusRequest 订单状态查询请求结构体
type MainPayCheckStatusRequest struct {
	ClientOrderId string `json:"clientOrderId"` // 商户订单号
}

// MainPayExchangeRateResponse 汇率查询响应结构体
type MainPayExchangeRateResponse struct {
	Data struct {
		Currency string  `json:"currency"` // 货币类型
		Rate     float64 `json:"rate"`     // 汇率
	} `json:"data"`
}

// MainPayCountryExchangeRateResponse 按国家获取汇率响应结构体
type MainPayCountryExchangeRateResponse struct {
	Data []struct {
		Country  string  `json:"country"`  // 国家名称
		Currency string  `json:"currency"` // 货币类型
		Rate     float64 `json:"rate"`     // 汇率
		Symbol   string  `json:"symbol"`   // 货币符号
	} `json:"data"`
}

// CurrencyFreaksResponse CurrencyFreaks API响应结构体
type CurrencyFreaksResponse struct {
	Date  string            `json:"date"`
	Base  string            `json:"base"`
	Rates map[string]string `json:"rates"`
}

// 全局汇率缓存结构体
type GlobalRateCache struct {
	Rates     map[string]float64 `json:"rates"`     // 所有货币汇率
	Timestamp time.Time          `json:"timestamp"` // 缓存时间
}

// 全局汇率缓存，缓存1小时
var globalRateCache *GlobalRateCache
var rateCacheDuration = 60 * time.Minute

// Init 初始化函数
func (c *mainPay) Init() {
	server.Http().PostNoAuth("/api/mainpay/recharge/callback", c.rechargeCallback)
	server.Http().Get("/api/mainpay/banks", c.getBanks)
	server.Http().PostNoAuth("/api/mainpay/getrate", c.getExchangeRateByCountry)
}

// getBanks 获取银行列表
func (c *mainPay) getBanks(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 获取支付方式ID
	methodId, err := strconv.Atoi(ctx.Gin().Query("method_id"))
	if err != nil {
		ctx.RespErr(errors.New("无效的支付方式ID"), &errcode)
		return
	}

	// 获取支付方式
	payMethod, err := c.getPayMethod(methodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 解析支付配置
	var cfg MainPayConfig
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("mainpay: 解析支付配置失败", err)
		ctx.RespErr(errors.New("支付配置错误"), &errcode)
		return
	}

	// 发送请求获取银行列表
	resp, err := c.post(cfg.URL+"/api/payment-gateways/banks?type=deposit", map[string]string{
		"X-API-KEY":    cfg.APIKey,
		"X-SECRET-KEY": cfg.BcryptKey,
		"X-CURRENCY":   cfg.Currency,
		"accept":       "application/json",
	}, nil)

	if err != nil {
		logs.Error("mainpay: 获取银行列表失败", err)
		ctx.RespErr(errors.New("获取银行列表失败"), &errcode)
		return
	}

	// 解析响应
	var response MainPayBankResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("mainpay: 解析银行列表响应失败", err, string(resp.Body()))
		ctx.RespErr(errors.New("解析银行列表响应失败"), &errcode)
		return
	}

	// 返回银行列表
	ctx.RespOK(response.Data)
}

// getExchangeRateByCountry 根据国家获取汇率 (HTTP接口)
func (c *mainPay) getExchangeRateByCountry(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 解析JSON请求体获取symbol参数
	var requestData map[string]interface{}
	if err := json.NewDecoder(ctx.Gin().Request.Body).Decode(&requestData); err != nil {
		logs.Error("mainpay: 解析JSON请求失败", err)
		ctx.RespErr(errors.New("请求格式错误"), &errcode)
		return
	}

	// 获取货币符号参数，默认为CNY
	symbol := "CNY"
	if symbolStr, ok := requestData["symbol"].(string); ok && symbolStr != "" {
		symbol = symbolStr
	} else if symbolStr, ok := requestData["Symbol"].(string); ok && symbolStr != "" {
		symbol = symbolStr
	}

	// 调用内部方法获取汇率
	rate, err := c.getRate(symbol)
	if err != nil {
		ctx.RespErr(errors.New(err.Error()), &errcode)
		return
	}

	// 返回汇率信息
	ctx.RespOK(map[string]interface{}{
		"symbol": strings.ToUpper(symbol),
		"rate":   rate,
	})
}

// getRate 内部方法：获取CurrencyFreaks汇率（全局缓存）
func (c *mainPay) getRate(symbol string) (float64, error) {
	// 将货币符号转换为大写，CurrencyFreaks API需要大写
	currency := strings.ToUpper(symbol)

	// 检查全局缓存是否存在且未过期
	if globalRateCache != nil && time.Since(globalRateCache.Timestamp) < rateCacheDuration {
		// 从缓存中获取指定货币的汇率
		if rate, exists := globalRateCache.Rates[currency]; exists {
			logs.Info("mainpay: 使用缓存汇率 %s: %.6f (缓存时间: %v)", currency, rate, globalRateCache.Timestamp.Format("15:04:05"))
			return rate, nil
		} else {
			logs.Error("mainpay: 缓存中不支持的货币类型", currency)
			return 0, fmt.Errorf("不支持的货币类型: %s", currency)
		}
	}

	// 缓存不存在或已过期，重新获取所有汇率
	logs.Info("mainpay: 汇率缓存不存在或已过期，重新获取所有汇率")

	// 调用CurrencyFreaks API获取实时汇率
	apiURL := "https://api.currencyfreaks.com/v2.0/rates/latest?apikey=********************************"

	// 记录开始时间
	startTime := time.Now()

	// 使用req库发送GET请求，添加性能监控
	resp, err := req.Get(apiURL, req.Header{
		"accept":     "application/json",
		"User-Agent": "MainPay-Client/1.0",
	})

	// 记录API调用时间
	apiDuration := time.Since(startTime)
	logs.Info("mainpay: CurrencyFreaks API调用耗时: %v", apiDuration)

	if err != nil {
		logs.Error("mainpay: 调用CurrencyFreaks API失败", err)
		return 0, fmt.Errorf("调用CurrencyFreaks API失败: %v", err)
	}

	// 解析CurrencyFreaks API响应
	var currencyFreaksResp CurrencyFreaksResponse
	respBody := resp.Bytes()

	if err := json.Unmarshal(respBody, &currencyFreaksResp); err != nil {
		logs.Error("mainpay: 解析CurrencyFreaks响应失败", err, string(respBody))
		return 0, fmt.Errorf("解析CurrencyFreaks响应失败: %v", err)
	}

	// 转换所有汇率并保存到全局缓存
	allRates := make(map[string]float64)
	for curr, rateStr := range currencyFreaksResp.Rates {
		rate, err := strconv.ParseFloat(rateStr, 64)
		if err != nil {
			logs.Warning("mainpay: 跳过无效汇率 %s: %s", curr, rateStr)
			continue
		}
		// 添加0.1的调整（保持原有逻辑）
		allRates[curr] = rate + 0.1
	}

	// 更新全局缓存
	globalRateCache = &GlobalRateCache{
		Rates:     allRates,
		Timestamp: time.Now(),
	}

	// 记录缓存的货币数量
	logs.Info("mainpay: 成功缓存 %d 种货币汇率", len(allRates))

	// 获取指定货币的汇率
	if rate, exists := allRates[currency]; exists {
		logs.Info("mainpay: 成功获取CurrencyFreaks汇率 %s: %.6f (已缓存所有汇率)", currency, rate)
		return rate, nil
	} else {
		logs.Error("mainpay: 不支持的货币类型", currency)
		return 0, fmt.Errorf("不支持的货币类型: %s", currency)
	}
}

// Recharge 充值接口
func (c *mainPay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	// 如果是直接通过JSON请求体发送的充值请求，尝试从请求体中获取参数
	if req == nil {
		// 解析JSON请求体
		var requestData map[string]interface{}
		if err := json.NewDecoder(ctx.Gin().Request.Body).Decode(&requestData); err != nil {
			logs.Error("mainpay: 解析请求数据失败", err)
			ctx.RespErr(errors.New("解析请求数据失败"), &errcode)
			return
		}

		logs.Info("mainpay: 收到直接充值请求", requestData)

		// 获取支付方式ID
		methodId, err := strconv.Atoi(ctx.Gin().Query("method_id"))
		if err != nil {
			// 尝试从请求体中获取
			if methodIdStr, ok := requestData["method_id"].(string); ok {
				methodId, err = strconv.Atoi(methodIdStr)
				if err != nil {
					ctx.RespErr(errors.New("无效的支付方式ID"), &errcode)
					return
				}
			} else {
				// 使用默认支付方式ID
				methodId = 0 // 这里需要根据实际情况设置一个默认值
				logs.Info("mainpay: 未指定支付方式ID，使用默认值", methodId)
			}
		}

		// 创建充值请求
		req = &CreateOrderReq{
			MethodId:         methodId,
			RechargeWithward: 1,     // 充值
			Symbol:           "MYR", // 默认使用马来西亚币
		}

		// 获取金额
		if amountStr, ok := requestData["amount"].(string); ok {
			amount, err := strconv.ParseFloat(amountStr, 64)
			if err == nil {
				req.Amount = amount
			} else {
				ctx.RespErr(errors.New("无效的金额"), &errcode)
				return
			}
		} else if amountFloat, ok := requestData["amount"].(float64); ok {
			req.Amount = amountFloat
		} else {
			ctx.RespErr(errors.New("缺少金额参数"), &errcode)
			return
		}

		// 处理币种
		if symbol, ok := requestData["symbol"].(string); ok && symbol != "" {
			req.Symbol = symbol
		}

		// 处理信用卡信息
		if cardNumber, ok := requestData["cardNumber"].(string); ok {
			req.CardNumber = cardNumber
		}

		if cardHolderName, ok := requestData["cardHolderName"].(string); ok {
			req.CardHolderName = cardHolderName
		}

		if cardExpMn, ok := requestData["cardExpMn"].(string); ok {
			req.CardExpMonth = cardExpMn
		}

		if cardExpYy, ok := requestData["cardExpYy"].(string); ok {
			req.CardExpYear = cardExpYy
		}

		if cardCvv, ok := requestData["cardCvv"].(string); ok {
			req.CardCVV = cardCvv
		}

		// 处理银行信息
		if customerBankAccount, ok := requestData["customerBankAccount"].(string); ok {
			req.BankAccount = customerBankAccount
		}

		if customerBankHolderName, ok := requestData["customerBankHolderName"].(string); ok {
			req.BankAccountName = customerBankHolderName
		} else if cardHolderName, ok := requestData["cardHolderName"].(string); ok {
			// 如果没有customerBankHolderName但有cardHolderName，使用cardHolderName作为银行账户名
			req.BankAccountName = cardHolderName
		}

		if bankCode, ok := requestData["code"].(string); ok {
			req.BankCode = bankCode
		}

		// 处理渠道信息
		if channel, ok := requestData["channel"].(string); ok {
			req.PayType = channel
		}

		// 记录其他可能的参数
		if redirectUrl, ok := requestData["redirectUrl"].(string); ok {
			logs.Info("mainpay: 客户端提供的重定向URL:", redirectUrl)
		}

		if clientOrderId, ok := requestData["clientOrderId"].(string); ok {
			logs.Info("mainpay: 客户端提供的订单ID:", clientOrderId)
		}
	}

	// 获取支付方式
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 解析支付配置
	var cfg MainPayConfig
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("mainpay: 解析支付配置失败", err)
		ctx.RespErr(errors.New("支付配置错误"), &errcode)
		return
	}

	// 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		logs.Error("mainpay: 未找到用户", err)
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	// 获取汇率 - 使用CurrencyFreaks API
	rate, err := c.getRate(req.Symbol)
	if err != nil {
		logs.Error("mainpay: 获取汇率失败", err)
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}

	// 计算实际金额
	currencyAmount := req.Amount * rate // 用户需要支付的法币金额
	usdtAmount := req.Amount            // 用户实际充值的USDT金额

	// 开始数据库事务
	tx := server.DaoxHashGame().Begin()

	// 创建充值订单
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      25, // MainPay 的唯一 ID
		Amount:       currencyAmount,
		RealAmount:   usdtAmount,
		TransferRate: rate,
		State:        3, // 待支付
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	// 保存订单到数据库
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		logs.Error("mainpay: 创建订单失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 准备API请求参数
	requestBody := map[string]interface{}{
		"email":         "<EMAIL>",
		"phoneNumber":   "13912345678",
		"amount":        fmt.Sprintf("%.2f", rechargeOrder.Amount),
		"clientOrderId": fmt.Sprintf("%d", rechargeOrder.ID),
		"code":          "105", // 默认代码
		"redirectUrl":   fmt.Sprintf("%s/api/mainpay/recharge/callback", cfg.CallbackURL),
	}

	// 获取支付渠道类型
	payChannel := ""

	// 从请求中获取渠道类型
	if ctx.Gin().PostForm("channel") != "" {
		payChannel = ctx.Gin().PostForm("channel")
	} else if req.PayType != "" {
		payChannel = req.PayType
	} else {
		// 尝试从支付方式名称中判断
		payMethodType := strings.ToLower(payMethod.PayType)
		if strings.Contains(payMethodType, "fpx") {
			payChannel = "fpx"
		} else if strings.Contains(payMethodType, "card") {
			payChannel = "cards"
		} else {
			// 默认使用信用卡支付
			payChannel = "cards"
		}
	}

	logs.Info("mainpay: 支付渠道类型:", payChannel)

	// 根据支付渠道类型添加特定参数
	if payChannel == "fpx" {
		// FPX支付 (马来西亚网上银行支付)
		requestBody["channel"] = "fpx"

		// 如果前端传递了银行ID，则使用它
		if req.BankCode != "" {
			requestBody["bankId"] = req.BankCode
		} else if ctx.Gin().PostForm("bankId") != "" {
			requestBody["bankId"] = ctx.Gin().PostForm("bankId")
		} else {
			// 默认使用Maybank
			requestBody["bankId"] = "MB2U0227"
		}

		// 如果前端传递了银行账户信息，则使用它
		if req.BankAccount != "" {
			requestBody["customerBankAccount"] = req.BankAccount
		} else if ctx.Gin().PostForm("customerBankAccount") != "" {
			requestBody["customerBankAccount"] = ctx.Gin().PostForm("customerBankAccount")
		}

		if req.BankAccountName != "" {
			requestBody["customerBankHolderName"] = req.BankAccountName
		} else if ctx.Gin().PostForm("customerBankHolderName") != "" {
			requestBody["customerBankHolderName"] = ctx.Gin().PostForm("customerBankHolderName")
		}
	} else if payChannel == "cards" {
		// 信用卡支付
		requestBody["channel"] = "cards"

		// 如果前端传递了信用卡信息，则使用它
		if req.CardNumber != "" {
			requestBody["cardNumber"] = req.CardNumber
		} else if ctx.Gin().PostForm("cardNumber") != "" {
			requestBody["cardNumber"] = ctx.Gin().PostForm("cardNumber")
		}

		if req.CardHolderName != "" {
			requestBody["cardHolderName"] = req.CardHolderName
		} else if ctx.Gin().PostForm("cardHolderName") != "" {
			requestBody["cardHolderName"] = ctx.Gin().PostForm("cardHolderName")
		}

		if req.CardExpMonth != "" {
			requestBody["cardExpMn"] = req.CardExpMonth
		} else if ctx.Gin().PostForm("cardExpMn") != "" {
			requestBody["cardExpMn"] = ctx.Gin().PostForm("cardExpMn")
		}

		if req.CardExpYear != "" {
			requestBody["cardExpYy"] = req.CardExpYear
		} else if ctx.Gin().PostForm("cardExpYy") != "" {
			requestBody["cardExpYy"] = ctx.Gin().PostForm("cardExpYy")
		}

		if req.CardCVV != "" {
			requestBody["cardCvv"] = req.CardCVV
		} else if ctx.Gin().PostForm("cardCvv") != "" {
			requestBody["cardCvv"] = ctx.Gin().PostForm("cardCvv")
		}

		// 信用卡支付也需要银行账户信息
		if req.BankAccount != "" {
			requestBody["customerBankAccount"] = req.BankAccount
		} else if ctx.Gin().PostForm("customerBankAccount") != "" {
			requestBody["customerBankAccount"] = ctx.Gin().PostForm("customerBankAccount")
		}
	} else {
		// 未知渠道类型，默认使用信用卡
		logs.Warning("mainpay: 未知的支付渠道类型:", payChannel, "，默认使用信用卡支付")
		requestBody["channel"] = "cards"
	}

	// 处理通用参数
	// 如果code参数存在，使用它
	if req.BankCode != "" && requestBody["code"] == nil {
		requestBody["code"] = req.BankCode
	} else if ctx.Gin().PostForm("code") != "" && requestBody["code"] == nil {
		requestBody["code"] = ctx.Gin().PostForm("code")
	}

	// 确保customerBankHolderName参数存在，这是API必需的参数
	if requestBody["customerBankHolderName"] == nil {
		// 如果cardHolderName存在，使用它作为customerBankHolderName
		if cardHolderName, ok := requestBody["cardHolderName"].(string); ok && cardHolderName != "" {
			requestBody["customerBankHolderName"] = cardHolderName
		} else if req.BankAccountName != "" {
			requestBody["customerBankHolderName"] = req.BankAccountName
		} else if req.CardHolderName != "" {
			requestBody["customerBankHolderName"] = req.CardHolderName
		} else if user.RealName != "" {
			requestBody["customerBankHolderName"] = user.RealName
		} else {
			// 如果没有任何可用的名称，使用用户的昵称或默认值
			if user.NickName != "" {
				requestBody["customerBankHolderName"] = user.NickName
			} else {
				requestBody["customerBankHolderName"] = "Customer"
			}
		}
	}

	// 确保customerBankAccount参数存在，这是API必需的参数
	if requestBody["customerBankAccount"] == nil {
		// 如果已经有银行账户信息，使用它
		if req.BankAccount != "" {
			requestBody["customerBankAccount"] = req.BankAccount
		}
	}

	// 发送请求
	jsonData, _ := json.Marshal(requestBody)

	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-API-KEY":    cfg.APIKey,
		"X-SECRET-KEY": cfg.BcryptKey,
		"X-CURRENCY":   req.Symbol,
		"accept":       "application/json",
	}

	// 发送请求 - 清理URL中的空格
	baseURL := strings.TrimSpace(cfg.URL)
	requestURL := baseURL + "/api/payment-gateways/deposit"
	logs.Info("mainpay: 请求URL:", requestURL)
	resp, err := c.post(requestURL, headers, jsonData)

	if err != nil {
		logs.Error("mainpay: 发送支付请求失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		return
	}

	// 解析响应
	var response MainPayRechargeResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("mainpay: 解析响应失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("解析响应失败"), &errcode)
		return
	}

	// 检查响应状态
	if (response.Data.Status != 200 && response.Data.Status != 201) || response.Data.Link == "" {
		logs.Error("mainpay: 创建订单失败", response.Data.Status, response.Data.StatusText)
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败: "+response.Data.StatusText), &errcode)
		return
	}

	// 记录成功状态
	logs.Info("mainpay: 成功创建支付链接", response.Data.Status, response.Data.StatusText)

	// 提交事务
	tx.Commit()

	// 从响应链接中提取订单号
	// 示例链接: /acquiring/redirect/uat?token=xxx&method=&customer_name=xxx&order_id=I-20250411-A3FLH&...
	orderNo := ""
	if response.Data.Link != "" {
		parts := strings.Split(response.Data.Link, "order_id=")
		if len(parts) > 1 {
			orderNo = strings.Split(parts[1], "&")[0]
		}
	}

	// 更新三方订单号
	if orderNo != "" {
		if err = c.updateThirdOrder(rechargeOrder.ID, orderNo); err != nil {
			logs.Error("mainpay: 更新三方订单号失败", err)
			// 不回滚事务，只记录错误
		}
	}

	// 构建完整的支付URL - 清理域名中的空格
	domain := strings.TrimSpace(cfg.DoMain)
	payURL := domain + response.Data.Link
	logs.Info("mainpay: 支付URL:", payURL)

	// 返回支付URL
	ctx.RespOK(xgo.H{
		"payurl": payURL,
	})
}

// decryptToken 解密回调令牌
func (c *mainPay) decryptToken(encryptedData string, orderNo string) (string, error) {
	// 根据JavaScript代码，密钥是订单号的前16个字符，不足16个字符则用空格填充
	keyBytes := orderNo
	if len(keyBytes) > 16 {
		keyBytes = keyBytes[:16]
	} else if len(keyBytes) < 16 {
		keyBytes = keyBytes + strings.Repeat(" ", 16-len(keyBytes))
	}

	// 从加密数据中提取IV (前24个字符是base64编码的IV)
	if len(encryptedData) <= 24 {
		return "", errors.New("加密数据格式不正确")
	}

	// 提取IV和加密文本
	ivBase64 := encryptedData[:24]
	encryptedText := encryptedData[24:]

	// 解码IV
	ivBytes, err := base64.StdEncoding.DecodeString(ivBase64)
	if err != nil {
		return "", fmt.Errorf("解码IV失败: %v", err)
	}

	// 创建AES解密器
	block, err := aes.NewCipher([]byte(keyBytes))
	if err != nil {
		return "", fmt.Errorf("创建解密器失败: %v", err)
	}

	// 创建CBC模式解密器
	mode := cipher.NewCBCDecrypter(block, ivBytes)

	// 解码base64加密文本
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", fmt.Errorf("解码加密文本失败: %v", err)
	}
	// 解密
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// 去除PKCS#7填充
	padding := int(plaintext[len(plaintext)-1])
	if padding > 0 && padding <= 16 {
		plaintext = plaintext[:len(plaintext)-padding]
	}
	return string(plaintext), nil
}

// verifyToken 验证令牌是否有效
func (c *mainPay) verifyToken(decryptedToken string, secretKey string) (bool, error) {
	// 尝试使用bcrypt验证（两种参数顺序都尝试）
	err := bcrypt.CompareHashAndPassword([]byte(decryptedToken), []byte(secretKey))
	if err == nil {
		logs.Info("verifyToken: 令牌验证成功", "decryptedToken:", decryptedToken, "secretKey:", secretKey)
		return true, nil
	}
	// 验证失败，返回错误
	return false, errors.New("令牌验证失败")
}

// 充值回调处理
func (c *mainPay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("mainpay: 读取回调数据失败", err)
		ctx.Gin().String(400, "读取数据失败")
		return
	}

	bodyStr := string(bodyBytes)
	logs.Info("mainpay: 收到原始回调数据", bodyStr)

	var callback MainPayCallbackRequest

	// 尝试解析JSON格式
	if err := json.Unmarshal(bodyBytes, &callback); err != nil {
		logs.Warning("mainpay: JSON解析回调数据失败，尝试解析其他格式", err)

		// 尝试解析空格分隔的格式
		parts := strings.Fields(bodyStr)
		if len(parts) >= 5 {
			callback.Token = parts[0] + " " + parts[1] // 假设前两部分是Token
			callback.OrderNo = parts[2]
			callback.ClientOrderId = parts[3]
			callback.Amount = parts[4]
			if len(parts) > 5 {
				callback.Status = parts[5]
			}
		} else {
			logs.Error("mainpay: 无法解析回调数据格式", bodyStr)
			ctx.Gin().String(400, "解析数据失败")
			return
		}
	}

	logs.Info("mainpay: 解析后的充值回调数据", callback)

	// 获取订单信息
	orderId, _ := strconv.Atoi(callback.ClientOrderId)
	order, err := c.getRechargeOrder(orderId)
	if err != nil {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 订单不存在", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 获取支付配置失败", err)
		ctx.Gin().String(400, "获取支付配置失败")
		return
	}

	var cfg MainPayConfig
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 解析支付配置失败", err)
		ctx.Gin().String(400, "支付配置错误")
		return
	}

	// 验证令牌
	if callback.Token != "" {
		logs.Info("mainpay: 尝试验证令牌", callback.Token)

		// 处理可能的空格问题
		token := strings.ReplaceAll(callback.Token, " ", "")

		// 尝试解密令牌
		decryptedToken, err := c.decryptToken(token, callback.OrderNo)
		if err != nil {
			c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
			logs.Error("mainpay: 解密令牌失败", err)
			ctx.Gin().String(400, "令牌验证失败")
			return
		}

		// 验证令牌
		isValid, err := c.verifyToken(decryptedToken, cfg.SecretKey)
		if err != nil || !isValid {
			c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
			logs.Error("mainpay: 令牌验证失败", err)
			ctx.Gin().String(400, "令牌验证失败")
			return
		}

		logs.Info("mainpay: 令牌验证成功")
	} else {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 回调缺少令牌")
		ctx.Gin().String(400, "回调缺少令牌")
		return
	}

	// 检查订单状态
	if order.State == 5 {
		logs.Info("mainpay: 订单已处理", orderId)
		ctx.Gin().String(200, "success")
		return
	}

	// 验证金额
	callbackAmount, err := strconv.ParseFloat(callback.Amount, 64)
	if err != nil {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 解析回调金额失败", err, callback.Amount)
		ctx.Gin().String(400, "解析金额失败")
		return
	}

	if math.Abs(order.Amount-callbackAmount) > 0.01 {
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		logs.Error("mainpay: 金额不匹配", order.Amount, callbackAmount)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 处理订单状态
	if callback.Status == "completed" { // 成功
		// 在处理回调前，通过API查询订单状态，确认订单确实已完成
		isOrderCompleted, err := c.verifyOrderStatus(cfg, callback.ClientOrderId)
		if err != nil {
			c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
			logs.Error("mainpay: 查询订单状态失败", err)
			ctx.Gin().String(400, "查询订单状态失败")
			return
		}

		if !isOrderCompleted {
			c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
			logs.Error("mainpay: 订单状态验证失败，API查询结果与回调不一致")
			ctx.Gin().String(400, "订单状态验证失败")
			return
		}

		logs.Info("mainpay: 订单状态验证成功，API查询结果与回调一致")

		// 处理充值
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
		ctx.Gin().String(200, "success")
	} else {
		logs.Info("mainpay: 订单失败", callback.Status)
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 7)
		ctx.Gin().String(200, "failed")
	}
}

// verifyOrderStatus 验证订单状态
func (c *mainPay) verifyOrderStatus(cfg MainPayConfig, clientOrderId string) (bool, error) {
	// 准备API请求参数
	requestBody := map[string]interface{}{
		"clientOrderId": clientOrderId,
	}

	// 发送请求
	jsonData, _ := json.Marshal(requestBody)

	// 准备请求头
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-API-KEY":    cfg.APIKey,
		"X-SECRET-KEY": cfg.BcryptKey,
		"X-CURRENCY":   cfg.Currency,
		"accept":       "application/json",
	}

	// 发送请求 - 清理URL中的空格
	baseURL := strings.TrimSpace(cfg.URL)
	requestURL := baseURL + "/api/payment-gateways/check-status"
	logs.Info("mainpay: 查询订单状态URL:", requestURL)
	resp, err := c.post(requestURL, headers, jsonData)

	if err != nil {
		logs.Error("mainpay: 查询订单状态失败", err)
		return false, err
	}

	// 解析响应
	var response MainPayCheckStatusResponse
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("mainpay: 解析响应失败", err)
		return false, err
	}

	// 检查订单状态
	logs.Info("mainpay: API查询订单状态", response.Data.Status)

	// 根据API文档，订单状态为"completed"表示成功
	return response.Data.Status == "completed", nil
}
