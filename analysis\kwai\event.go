package kwai

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
)

func (c *Client) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	logs.Info("Kwai 请求参数", string(data))
	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, err
	}

	logs.Info("Kwai返回：", resp.String())

	return resp, nil
}

func (c *Client) PushEvent(clickId string, eventName string, properties map[string]interface{}) (err error) {
	propertiesFormat, _ := json.Marshal(properties)
	data := &PostData{
		ClickId:         clickId,
		EventName:       eventName,
		PixelId:         c.pixelId,
		AccessToken:     c.accessToken,
		TestFlag:        false,
		TrackFlag:       c.isTest,
		IsAttributed:    1,
		Mmpcode:         "PL",
		PixelSdkVersion: "9.9.9",
		Properties:      string(propertiesFormat),
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = c.post(c.api, map[string]string{
		"Content-Type": "application/json",
	}, jsonData)

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) CompleteRegistration(clickId string, userId int) (err error) {
	return c.PushEvent(clickId, EventName_CompleteRegistration, map[string]interface{}{
		"content_id":   userId,
		"content_type": "product",
		"content_name": "用户ID",
	})
}

func (c *Client) Purchase(clickId string, amount float64) (err error) {
	return c.PushEvent(clickId, EventName_Purchase, map[string]interface{}{
		"currency": "USD",
		"value":    amount,
	})
}

func (c *Client) FirstRecharge(clickId string, amount float64) (err error) {
	return c.PushEvent(clickId, EventName_FirstRecharge, map[string]interface{}{
		"currency": "USD",
		"value":    amount,
	})
}

func (c *Client) AddToCart(clickId string, userId int) (err error) {
	return c.PushEvent(clickId, EventName_AddToCart, map[string]interface{}{
		"content_id":   userId,
		"content_type": "product",
		"content_name": "用户ID",
	})
}
