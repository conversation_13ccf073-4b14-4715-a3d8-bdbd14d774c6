// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXLangGameList(db *gorm.DB, opts ...gen.DOOption) xLangGameList {
	_xLangGameList := xLangGameList{}

	_xLangGameList.xLangGameListDo.UseDB(db, opts...)
	_xLangGameList.xLangGameListDo.UseModel(&model.XLangGameList{})

	tableName := _xLangGameList.xLangGameListDo.TableName()
	_xLangGameList.ALL = field.NewAsterisk(tableName)
	_xLangGameList.LangID = field.NewInt32(tableName, "LangId")
	_xLangGameList.Brand = field.NewString(tableName, "Brand")
	_xLangGameList.GameID = field.NewString(tableName, "GameId")
	_xLangGameList.Sort = field.NewInt32(tableName, "Sort")
	_xLangGameList.CreateTime = field.NewTime(tableName, "CreateTime")
	_xLangGameList.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xLangGameList.fillFieldMap()

	return _xLangGameList
}

type xLangGameList struct {
	xLangGameListDo xLangGameListDo

	ALL        field.Asterisk
	LangID     field.Int32  // 域名Id 来源x_channel_host的Id
	Brand      field.String // 品牌
	GameID     field.String // 游戏Id
	Sort       field.Int32  // 排序,数字越大越靠前
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xLangGameList) Table(newTableName string) *xLangGameList {
	x.xLangGameListDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xLangGameList) As(alias string) *xLangGameList {
	x.xLangGameListDo.DO = *(x.xLangGameListDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xLangGameList) updateTableName(table string) *xLangGameList {
	x.ALL = field.NewAsterisk(table)
	x.LangID = field.NewInt32(table, "LangId")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.Sort = field.NewInt32(table, "Sort")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xLangGameList) WithContext(ctx context.Context) *xLangGameListDo {
	return x.xLangGameListDo.WithContext(ctx)
}

func (x xLangGameList) TableName() string { return x.xLangGameListDo.TableName() }

func (x xLangGameList) Alias() string { return x.xLangGameListDo.Alias() }

func (x xLangGameList) Columns(cols ...field.Expr) gen.Columns {
	return x.xLangGameListDo.Columns(cols...)
}

func (x *xLangGameList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xLangGameList) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["LangId"] = x.LangID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xLangGameList) clone(db *gorm.DB) xLangGameList {
	x.xLangGameListDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xLangGameList) replaceDB(db *gorm.DB) xLangGameList {
	x.xLangGameListDo.ReplaceDB(db)
	return x
}

type xLangGameListDo struct{ gen.DO }

func (x xLangGameListDo) Debug() *xLangGameListDo {
	return x.withDO(x.DO.Debug())
}

func (x xLangGameListDo) WithContext(ctx context.Context) *xLangGameListDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xLangGameListDo) ReadDB() *xLangGameListDo {
	return x.Clauses(dbresolver.Read)
}

func (x xLangGameListDo) WriteDB() *xLangGameListDo {
	return x.Clauses(dbresolver.Write)
}

func (x xLangGameListDo) Session(config *gorm.Session) *xLangGameListDo {
	return x.withDO(x.DO.Session(config))
}

func (x xLangGameListDo) Clauses(conds ...clause.Expression) *xLangGameListDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xLangGameListDo) Returning(value interface{}, columns ...string) *xLangGameListDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xLangGameListDo) Not(conds ...gen.Condition) *xLangGameListDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xLangGameListDo) Or(conds ...gen.Condition) *xLangGameListDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xLangGameListDo) Select(conds ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xLangGameListDo) Where(conds ...gen.Condition) *xLangGameListDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xLangGameListDo) Order(conds ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xLangGameListDo) Distinct(cols ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xLangGameListDo) Omit(cols ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xLangGameListDo) Join(table schema.Tabler, on ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xLangGameListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xLangGameListDo) RightJoin(table schema.Tabler, on ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xLangGameListDo) Group(cols ...field.Expr) *xLangGameListDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xLangGameListDo) Having(conds ...gen.Condition) *xLangGameListDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xLangGameListDo) Limit(limit int) *xLangGameListDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xLangGameListDo) Offset(offset int) *xLangGameListDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xLangGameListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xLangGameListDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xLangGameListDo) Unscoped() *xLangGameListDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xLangGameListDo) Create(values ...*model.XLangGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xLangGameListDo) CreateInBatches(values []*model.XLangGameList, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xLangGameListDo) Save(values ...*model.XLangGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xLangGameListDo) First() (*model.XLangGameList, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangGameList), nil
	}
}

func (x xLangGameListDo) Take() (*model.XLangGameList, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangGameList), nil
	}
}

func (x xLangGameListDo) Last() (*model.XLangGameList, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangGameList), nil
	}
}

func (x xLangGameListDo) Find() ([]*model.XLangGameList, error) {
	result, err := x.DO.Find()
	return result.([]*model.XLangGameList), err
}

func (x xLangGameListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XLangGameList, err error) {
	buf := make([]*model.XLangGameList, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xLangGameListDo) FindInBatches(result *[]*model.XLangGameList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xLangGameListDo) Attrs(attrs ...field.AssignExpr) *xLangGameListDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xLangGameListDo) Assign(attrs ...field.AssignExpr) *xLangGameListDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xLangGameListDo) Joins(fields ...field.RelationField) *xLangGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xLangGameListDo) Preload(fields ...field.RelationField) *xLangGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xLangGameListDo) FirstOrInit() (*model.XLangGameList, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangGameList), nil
	}
}

func (x xLangGameListDo) FirstOrCreate() (*model.XLangGameList, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XLangGameList), nil
	}
}

func (x xLangGameListDo) FindByPage(offset int, limit int) (result []*model.XLangGameList, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xLangGameListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xLangGameListDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xLangGameListDo) Delete(models ...*model.XLangGameList) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xLangGameListDo) withDO(do gen.Dao) *xLangGameListDo {
	x.DO = *do.(*gen.DO)
	return x
}
