// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserReduce(db *gorm.DB, opts ...gen.DOOption) xUserReduce {
	_xUserReduce := xUserReduce{}

	_xUserReduce.xUserReduceDo.UseDB(db, opts...)
	_xUserReduce.xUserReduceDo.UseModel(&model.XUserReduce{})

	tableName := _xUserReduce.xUserReduceDo.TableName()
	_xUserReduce.ALL = field.NewAsterisk(tableName)
	_xUserReduce.UserID = field.NewInt32(tableName, "UserId")
	_xUserReduce.ReduceLiuShui = field.NewFloat64(tableName, "ReduceLiuShui")
	_xUserReduce.RealReduceLiuShui = field.NewFloat64(tableName, "RealReduceLiuShui")
	_xUserReduce.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserReduce.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserReduce.fillFieldMap()

	return _xUserReduce
}

// xUserReduce 玩家扣减流水
type xUserReduce struct {
	xUserReduceDo xUserReduceDo

	ALL               field.Asterisk
	UserID            field.Int32
	ReduceLiuShui     field.Float64 // 需要扣减流水
	RealReduceLiuShui field.Float64 // 真实扣减流水
	CreateTime        field.Time    // 创建时间
	UpdateTime        field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserReduce) Table(newTableName string) *xUserReduce {
	x.xUserReduceDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserReduce) As(alias string) *xUserReduce {
	x.xUserReduceDo.DO = *(x.xUserReduceDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserReduce) updateTableName(table string) *xUserReduce {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.ReduceLiuShui = field.NewFloat64(table, "ReduceLiuShui")
	x.RealReduceLiuShui = field.NewFloat64(table, "RealReduceLiuShui")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserReduce) WithContext(ctx context.Context) *xUserReduceDo {
	return x.xUserReduceDo.WithContext(ctx)
}

func (x xUserReduce) TableName() string { return x.xUserReduceDo.TableName() }

func (x xUserReduce) Alias() string { return x.xUserReduceDo.Alias() }

func (x xUserReduce) Columns(cols ...field.Expr) gen.Columns { return x.xUserReduceDo.Columns(cols...) }

func (x *xUserReduce) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserReduce) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 5)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ReduceLiuShui"] = x.ReduceLiuShui
	x.fieldMap["RealReduceLiuShui"] = x.RealReduceLiuShui
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserReduce) clone(db *gorm.DB) xUserReduce {
	x.xUserReduceDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserReduce) replaceDB(db *gorm.DB) xUserReduce {
	x.xUserReduceDo.ReplaceDB(db)
	return x
}

type xUserReduceDo struct{ gen.DO }

func (x xUserReduceDo) Debug() *xUserReduceDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserReduceDo) WithContext(ctx context.Context) *xUserReduceDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserReduceDo) ReadDB() *xUserReduceDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserReduceDo) WriteDB() *xUserReduceDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserReduceDo) Session(config *gorm.Session) *xUserReduceDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserReduceDo) Clauses(conds ...clause.Expression) *xUserReduceDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserReduceDo) Returning(value interface{}, columns ...string) *xUserReduceDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserReduceDo) Not(conds ...gen.Condition) *xUserReduceDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserReduceDo) Or(conds ...gen.Condition) *xUserReduceDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserReduceDo) Select(conds ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserReduceDo) Where(conds ...gen.Condition) *xUserReduceDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserReduceDo) Order(conds ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserReduceDo) Distinct(cols ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserReduceDo) Omit(cols ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserReduceDo) Join(table schema.Tabler, on ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserReduceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserReduceDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserReduceDo) Group(cols ...field.Expr) *xUserReduceDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserReduceDo) Having(conds ...gen.Condition) *xUserReduceDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserReduceDo) Limit(limit int) *xUserReduceDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserReduceDo) Offset(offset int) *xUserReduceDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserReduceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserReduceDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserReduceDo) Unscoped() *xUserReduceDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserReduceDo) Create(values ...*model.XUserReduce) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserReduceDo) CreateInBatches(values []*model.XUserReduce, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserReduceDo) Save(values ...*model.XUserReduce) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserReduceDo) First() (*model.XUserReduce, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserReduce), nil
	}
}

func (x xUserReduceDo) Take() (*model.XUserReduce, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserReduce), nil
	}
}

func (x xUserReduceDo) Last() (*model.XUserReduce, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserReduce), nil
	}
}

func (x xUserReduceDo) Find() ([]*model.XUserReduce, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserReduce), err
}

func (x xUserReduceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserReduce, err error) {
	buf := make([]*model.XUserReduce, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserReduceDo) FindInBatches(result *[]*model.XUserReduce, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserReduceDo) Attrs(attrs ...field.AssignExpr) *xUserReduceDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserReduceDo) Assign(attrs ...field.AssignExpr) *xUserReduceDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserReduceDo) Joins(fields ...field.RelationField) *xUserReduceDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserReduceDo) Preload(fields ...field.RelationField) *xUserReduceDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserReduceDo) FirstOrInit() (*model.XUserReduce, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserReduce), nil
	}
}

func (x xUserReduceDo) FirstOrCreate() (*model.XUserReduce, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserReduce), nil
	}
}

func (x xUserReduceDo) FindByPage(offset int, limit int) (result []*model.XUserReduce, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserReduceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserReduceDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserReduceDo) Delete(models ...*model.XUserReduce) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserReduceDo) withDO(do gen.Dao) *xUserReduceDo {
	x.DO = *do.(*gen.DO)
	return x
}
