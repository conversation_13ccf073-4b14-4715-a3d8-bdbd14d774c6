package single

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// Refund 退款 API URL Refund
func (l *RSGSingleService) Refund(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string  `json:"SystemCode"`    // 系統代碼(只限英數)，必填，长度2~20
		WebId         string  `json:"WebId"`         // 站台代碼(只限英數)，必填，长度3~20
		UserId        string  `json:"UserId"`        // 會員惟一識別碼(只限英數)，必填，长度3~20
		GameId        int     `json:"GameId"`        // 遊戲代碼(只限 3001 & 3002)，必填
		Currency      string  `json:"Currency"`      // 幣別代碼(請參照代碼表)，必填，长度2~5
		TransactionId string  `json:"TransactionId"` // 交易惟一識別碼(只限英數、@)，必填，长度8~30
		SessionId     string  `json:"SessionId"`     // 同一交易過程識別碼，必填，长度36
		Amount        float64 `json:"Amount"`        // 預扣金額(小數點兩位) (範圍0.00~9999999999.99)，必填
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
			Amount  float64 `json:"Amount"`  // 会员退款金额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 退款")
	if err != nil {
		logs.Error("RSG_single 退款 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 退款 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 退款 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 退款 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 退款 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	thirdId := reqdata.TransactionId

	// 判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(l.brandName, thirdId, ctx.Gin().Request.URL.String())
	if err != nil {
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		logs.Error("rsg 退款 检测是否重复请求 发生错误 thirdId=", thirdId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("rsg 退款 检测到重复请求 thirdId=", thirdId)
		if duplicateResult != nil && err == nil {
			l.ProcessEncryptedResponse(ctx, duplicateResult, "RSG_single 退款")
		} else {
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		}
		return
	}

	// 记录请求响应日志
	defer func() {
		respCode := 0
		if respdata.ErrorCode != RSG_Code_Success {
			respCode = 1
		}
		base.AddRequestDB(thirdId, string(decryptedBytes), respdata, respCode, l.brandName, ctx.Gin().Request.URL.String())
	}()

	// 开始退款事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 退款 获取用户余额失败 userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		}

		// 查询账变记录，确定订单号
		thirdAmountLog := thirdGameModel.ThirdAmountLog{}
		err = server.Db().GormDao().Table("x_third_amount_log").
			Where("UserId = ? and TransactionId = ? and Brand = ? and Reason = ? ", userId, reqdata.SessionId, l.brandName, 2).
			First(&thirdAmountLog).Error
		if err == nil {
			// 交易存在，返回成功信息
			logs.Info("RSG_single 退款 检查交易状态 交易已经存在 已经退过款 thirdId=", thirdId)
			// 设置响应数据
			respdata.Data.Balance = userBalance.Amount + reqdata.Amount
			balance := math.Floor(respdata.Data.Balance*100) / 100
			respdata.Data.Balance = balance
			amount := math.Floor(reqdata.Amount*100) / 100
			respdata.Data.Amount = amount

			return nil
		} else if err != daogorm.ErrRecordNotFound {
			// 查询出错
			logs.Error("RSG_single 退款 检查交易状态 查询交易失败 thirdId=", thirdId, " err=", err)
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return err
		}

		// 获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)

		//// 获取游戏名称
		//gameName := ""
		//if name, ok := l.games[reqdata.GameId]; ok {
		//	gameName = name
		//} else {
		//	gameList := thirdGameModel.GameList{}
		//	e = tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
		//	if e == nil {
		//		gameName = gameList.Name
		//	} else {
		//		gameName = l.brandName + "_" + strconv.Itoa(reqdata.GameId)
		//	}
		//}
		//
		//// 创建退款订单
		//rawData, _ := json.Marshal(reqdata)
		//refundOrder := thirdGameModel.ThirdOrder{
		//	UserId:     userId,
		//	ThirdId:    thirdId,
		//	Brand:      l.brandName,
		//	GameId:     strconv.Itoa(reqdata.GameId),
		//	GameName:   gameName,
		//	BetAmount:  0,
		//	WinAmount:  reqdata.Amount,
		//	DataState:  5, // 退款状态
		//	RawData:    string(rawData),
		//	CreateTime: utils.GetCurrentTime(),
		//	ThirdTime:  utils.GetCurrentTime(),
		//	SellerId:   userBalance.SellerId,
		//	ChannelId:  ChannelId,
		//}
		//e = tx.Table(tableRefund).Create(&refundOrder).Error
		//if e != nil {
		//	logs.Error("RSG_single 退款 创建退款订单失败 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
		//	respdata.ErrorCode = RSG_Code_System_Busy
		//	respdata.ErrorMessage = "系统维护中"
		//	return e
		//}

		// 更新用户余额
		if reqdata.Amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Update("Amount", daogorm.Expr("Amount + ?", reqdata.Amount))
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
				e = fmt.Errorf("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 预付款 增加用户余额失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Amount,
				AfterAmount:  userBalance.Amount + reqdata.Amount,
				Reason:       utils.BalanceCReasonRSGRefund,
				Memo:         "RSG refund,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    ChannelId,
				CreateTime:   utils.GetCurrentTime(),
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("RSG_single 退款 创建账变记录失败 userId=", userId, " reqdata.Amount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			//保存原始订单号，用于后续查单 2=加款 1=扣款
			e := l.AddThirdAmountLog(tx, reqdata.Amount, 2, userId, reqdata.TransactionId, reqdata.SessionId, amountLog.Memo)
			if e != nil {
				logs.Error("RSG_single 插入三方账变记录失败: err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}

		// 设置响应数据
		respdata.Data.Balance = userBalance.Amount + reqdata.Amount
		balance := math.Floor(respdata.Data.Balance*100) / 100
		respdata.Data.Balance = balance

		amount := math.Floor(reqdata.Amount*100) / 100
		respdata.Data.Amount = amount
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 退款 事务处理失败 userId=", userId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
	if err != nil {
		logs.Error("RSG_single 退款 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 退款")
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 退款 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 退款 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}
