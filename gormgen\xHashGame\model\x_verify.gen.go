// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXVerify = "x_verify"

// XVerify mapped from table <x_verify>
type XVerify struct {
	SellerID   int32     `gorm:"column:SellerId;primaryKey;comment:运营商" json:"SellerId"`        // 运营商
	ChannelID  int32     `gorm:"column:ChannelId;primaryKey;comment:渠道" json:"ChannelId"`       // 渠道
	Account    string    `gorm:"column:Account;primaryKey;comment:账号" json:"Account"`           // 账号
	UseType    int32     `gorm:"column:UseType;primaryKey;comment:使用途径 1注册 2登录" json:"UseType"` // 使用途径 1注册 2登录
	VerifyCode string    `gorm:"column:VerifyCode;not null;comment:验证码" json:"VerifyCode"`      // 验证码
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
}

// TableName XVerify's table name
func (*XVerify) TableName() string {
	return TableNameXVerify
}
