# OFA API 客户端

OFA API 客户端是一个用于与 OFA 游戏平台进行交互的 Go 语言接口库。它提供了一系列方法来处理用户登录、余额查询、下注确认、取消下注、结算和回滚等操作。

## 功能特性

- 用户登录和登出
- 余额查询
- 下注确认
- 取消下注
- 结算
- 回滚结算
- 游戏列表和活动列表查询
- 安全的签名验证机制

## 签名验证功能

OFA API 客户端实现了安全的签名验证机制，确保 API 请求和响应的真实性和完整性。主要包括以下功能：

1. **CreateSignature**: 生成 API 签名，用于验证请求的合法性
2. **VerifySignature**: 验证 API 签名，确保响应未被篡改
3. **AES 加密/解密**: 使用 AES/ECB/PKCS5Padding 算法对敏感数据进行加密和解密
4. **响应签名验证**: 在 sendRequest 方法中自动验证响应签名

## 使用示例

```go
// 创建 OFA 服务实例
params := map[string]string{
    "apiUrl":        "https://api.ofa.com",
    "requestMd5Key": "your_request_key",
    "callbackMd5Key": "your_callback_key",
    "merchantCode":  "YOUR_MERCHANT_CODE",
    "currency":      "CNY",
    "homeUrl":       "https://www.example.com",
    "brandName":     "OFA",
}

ofaService := single.NewOFASingleService(params, nil)

// 使用服务实例调用 API 方法
// 例如: ofaService.Login(ctx)
```

## 测试

在 `test` 目录中提供了测试脚本，用于验证 API 客户端的功能：

1. `test_ofa_signature.go`: 测试签名生成和验证功能
2. `test_ofa_api_signature.go`: 测试实际 API 调用中的签名验证

运行测试：

```bash
go run test/test_ofa_signature.go
go run test/test_ofa_api_signature.go
```

## 安全注意事项

- 请妥善保管 MD5 密钥，不要在代码中硬编码
- 定期更换密钥，提高安全性
- 验证所有 API 响应的签名，防止中间人攻击
- 使用 HTTPS 进行 API 通信，确保传输安全
