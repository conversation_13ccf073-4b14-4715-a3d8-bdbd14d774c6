package single

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
)

// CreatePlayer 创建玩家账户 API URL SingleWallet/Player/CreatePlayer
func (l *RSGSingleService) CreatePlayer(userId int, userIdStr string) (int, string, error) {
	// 构造请求URL
	createPlayerURL := fmt.Sprintf("%s/SingleWallet/Player/CreatePlayer", l.apiUrl)

	// 构造请求数据
	createPlayerReq := struct {
		SystemCode string `json:"SystemCode"`
		WebId      string `json:"WebId"`
		UserId     string `json:"UserId"`
		Currency   string `json:"Currency"`
	}{
		SystemCode: l.systemCode,
		WebId:      l.webId,
		UserId:     userIdStr,
		Currency:   l.currency,
	}

	// 将请求数据转为JSON
	createPlayerReqBytes, err := json.Marshal(createPlayerReq)
	if err != nil {
		logs.Error("RSG_single 创建玩家 序列化请求数据失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 加密请求数据
	encryptedData, err := l.EncryptData(string(createPlayerReqBytes))
	if err != nil {
		logs.Error("RSG_single 创建玩家 加密请求数据失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 构造表单数据
	formData := url.Values{}
	formData.Set("Msg", encryptedData)

	// 获取当前时间戳
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// 创建HTTP请求
	req, err := http.NewRequest("POST", createPlayerURL, bytes.NewBufferString(formData.Encode()))
	if err != nil {
		logs.Error("RSG_single 创建玩家 创建HTTP请求失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("X-API-ClientID", l.clientId)
	req.Header.Set("X-API-Timestamp", timestamp)
	req.Header.Set("X-API-Signature", l.CreateSignature(timestamp, encryptedData))

	// 发送请求
	client := &http.Client{}
	createPlayerResp, err := client.Do(req)
	if err != nil {
		logs.Error("RSG_single 创建玩家 发送请求失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", err
	}
	defer createPlayerResp.Body.Close()

	// 读取响应内容
	createPlayerRespBody, err := io.ReadAll(createPlayerResp.Body)
	if err != nil {
		logs.Error("RSG_single 创建玩家 读取响应失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 解密响应数据
	decryptedCreatePlayerResp, err := l.DecryptData(string(createPlayerRespBody))
	if err != nil {
		logs.Error("RSG_single 创建玩家 解密响应失败 err=", err.Error(), " response=", string(createPlayerRespBody))
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 解析创建玩家响应
	var createPlayerRespData struct {
		ErrorCode    int    `json:"ErrorCode"`
		ErrorMessage string `json:"ErrorMessage"`
		Timestamp    int64  `json:"Timestamp"`
		Data         struct {
			SystemCode string `json:"SystemCode"`
			WebId      string `json:"WebId"`
			UserId     string `json:"UserId"`
		} `json:"Data"`
	}

	err = json.Unmarshal([]byte(decryptedCreatePlayerResp), &createPlayerRespData)
	if err != nil {
		logs.Error("RSG_single 创建玩家 解析响应失败 err=", err.Error(), " decryptedResponse=", decryptedCreatePlayerResp)
		return RSG_Code_System_Busy, "系统维护中", err
	}

	// 检查创建玩家响应状态
	if createPlayerRespData.ErrorCode != 0 && createPlayerRespData.ErrorCode != 3010 { // 3010表示玩家已存在，也是可接受的
		logs.Error("RSG_single 创建玩家 失败 ErrorCode=", createPlayerRespData.ErrorCode, " ErrorMessage=", createPlayerRespData.ErrorMessage)
		return createPlayerRespData.ErrorCode, createPlayerRespData.ErrorMessage, fmt.Errorf("创建玩家失败: %s", createPlayerRespData.ErrorMessage)
	}

	// 记录创建玩家成功日志
	logs.Info("RSG_single 创建玩家 成功或玩家已存在 userId=", userId)
	return 0, "", nil
}

// GetLobbyURLToken 获取游戏大厅URL API URL SingleWallet/Player/GetURLToken
func (l *RSGSingleService) GetLobbyURLToken(userIdStr, userName, gameCode, language, exitAction string) (int, string, string, error) {
	// 构造请求URL
	getLobbyURLURL := fmt.Sprintf("%s/SingleWallet/Player/GetURLToken", l.apiUrl)
	gameId, _ := strconv.Atoi(gameCode)
	// 构造请求数据
	getLobbyURLReq := struct {
		SystemCode string `json:"SystemCode"`
		WebId      string `json:"WebId"`
		GameId     int    `json:"GameId"`
		UserId     string `json:"UserId"`
		UserName   string `json:"UserName"`
		Currency   string `json:"Currency"`
		Language   string `json:"Language"`
		ExitAction string `json:"ExitAction"`
	}{
		SystemCode: l.systemCode,
		WebId:      l.webId,
		UserId:     userIdStr,
		UserName:   userName,
		Currency:   l.currency,
		Language:   language,
		ExitAction: exitAction,
		GameId:     gameId,
	}

	// 将请求数据转为JSON
	getLobbyURLReqBytes, err := json.Marshal(getLobbyURLReq)
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 序列化请求数据失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", "", err
	}
	logs.Info("请求数据", string(getLobbyURLReqBytes))
	// 加密请求数据
	encryptedData, err := l.EncryptData(string(getLobbyURLReqBytes))
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 加密请求数据失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", "", err
	}

	// 构造表单数据
	formData := url.Values{}
	formData.Set("Msg", encryptedData)

	// 获取当前时间戳
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// 创建HTTP请求
	req, err := http.NewRequest("POST", getLobbyURLURL, bytes.NewBufferString(formData.Encode()))
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 创建HTTP请求失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", "", err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("X-API-ClientID", l.clientId)
	req.Header.Set("X-API-Timestamp", timestamp)
	req.Header.Set("X-API-Signature", l.CreateSignature(timestamp, encryptedData))

	// 发送请求
	client := &http.Client{}
	getLobbyURLResp, err := client.Do(req)
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 发送请求失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", "", err
	}
	defer getLobbyURLResp.Body.Close()

	// 读取响应内容
	getLobbyURLRespBody, err := io.ReadAll(getLobbyURLResp.Body)
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 读取响应失败 err=", err.Error())
		return RSG_Code_System_Busy, "系统维护中", "", err
	}

	// 解密响应数据
	decryptedGetLobbyURLResp, err := l.DecryptData(string(getLobbyURLRespBody))
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 解密响应失败 err=", err.Error(), " response=", string(getLobbyURLRespBody))
		return RSG_Code_System_Busy, "系统维护中", "", err
	}

	// 解析获取大厅URL响应
	var getLobbyURLRespData struct {
		ErrorCode    int    `json:"ErrorCode"`
		ErrorMessage string `json:"ErrorMessage"`
		Timestamp    int64  `json:"Timestamp"`
		Data         struct {
			URL string `json:"URL"`
		} `json:"Data"`
	}

	err = json.Unmarshal([]byte(decryptedGetLobbyURLResp), &getLobbyURLRespData)
	if err != nil {
		logs.Error("RSG_single 获取大厅URL 解析响应失败 err=", err.Error(), " decryptedResponse=", decryptedGetLobbyURLResp)
		return RSG_Code_System_Busy, "系统维护中", "", err
	}

	// 检查获取大厅URL响应状态
	if getLobbyURLRespData.ErrorCode != 0 {
		logs.Error("RSG_single 获取大厅URL 失败 ErrorCode=", getLobbyURLRespData.ErrorCode, " ErrorMessage=", getLobbyURLRespData.ErrorMessage)
		return getLobbyURLRespData.ErrorCode, getLobbyURLRespData.ErrorMessage, "", fmt.Errorf("获取大厅URL失败: %s", getLobbyURLRespData.ErrorMessage)
	}

	return 0, "", getLobbyURLRespData.Data.URL, nil
}

// Login 玩家登录 API URL Login
// 结合了CreatePlayer和GetLobbyURLToken两个API的功能
func (l *RSGSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("RSG_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	loginLang := reqdata.LangCode
	if loginLang == "" {
		loginLang = "zh-CN"
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("RSG_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}

	userId := token.UserId
	if err, errcode = base.IsLoginByUserId("cacheKeyRSG", userId); err != nil {
		logs.Error("RSG_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	// 查询游戏信息
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", "rsg", reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("RSG_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("RSG_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	// 获取用户信息
	userIdStr := strconv.Itoa(userId)

	// 查询用户信息，包括用户名
	var userName string
	userName = fmt.Sprint(token.UserId)
	//err = server.Db().GormDao().Table("x_user").Select("UserName").Where("UserId = ?", userId).Row().Scan(&userName)
	//if err != nil {
	//	logs.Error("RSG_single 登录游戏 获取用户名失败 userId=", userId, " err=", err.Error())
	//	userName = "player" + userIdStr // 设置默认用户名
	//}

	// 第一步：创建玩家账户 (SingleWallet/Player/CreatePlayer)
	_, errorMessage, err := l.CreatePlayer(userId, userIdStr)
	if err != nil {
		logs.Error("RSG_single 登录游戏 创建玩家失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, errorMessage)
		return
	}

	// 第二步：获取游戏大厅URL (SingleWallet/Player/GetLobbyURLToken)
	// 如果用户名为空，使用默认用户名
	//if userName == "" {
	//	userName = "player" + userIdStr
	//}

	// 使用请求中的语言或默认语言
	exitAction := reqdata.HomeUrl
	_, errorMessage, gameURL, err := l.GetLobbyURLToken(userIdStr, userName, reqdata.GameId, loginLang, exitAction)
	if err != nil {
		logs.Error("RSG_single 登录游戏 获取游戏URL失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, errorMessage)
		return
	}

	// 返回游戏URL
	ctx.RespOK(gameURL)
	logs.Info("RSG_single 登录游戏 成功 userId=", userId, " URL=", gameURL)
}
