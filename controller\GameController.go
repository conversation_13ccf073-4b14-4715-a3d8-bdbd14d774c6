package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/model"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/shopspring/decimal"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

type GameController struct {
}

func (c *GameController) Init() {
	gropu := server.Http().NewGroup("/api/game")
	{
		gropu.PostNoAuth("/list", c.list)
		gropu.PostNoAuth("/reward_pool", c.reward_pool)
		gropu.Post("/bet_record", c.bet_record)
		gropu.Post("/bet_recordex", c.bet_recordex)
		gropu.PostNoAuth("/bet_recordtest", c.bet_recordtest)
		gropu.PostNoAuth("/luzi", c.luzi)
		gropu.PostNoAuth("/next", c.next)
		gropu.Post("/record_abstract", c.record_abstract)
		gropu.PostNoAuth("/gamelist", c.gamelist)
		gropu.PostNoAuth("/get_game_list", c.get_game_list)
		gropu.Post("/transfer_record", c.transferRecord)
		gropu.Post("/new_transfer_record", c.newTransferRecord)
		gropu.PostByNoAuthMayUserToken("/recentProfit", c.recentProfit)
		gropu.PostNoAuth("/getChainList", c.getChainList)
		gropu.Post("/sport_bet_detail", c.sport_bet_detail) //体育详情界面处理
		gropu.Post("/hash_bet_record_info", c.hash_bet_record_info)
		gropu.Post("/hash_bet_record_group", c.hash_bet_record_group)
	}
}

func (c *GameController) list(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Host      string
		AgentCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	//host = "clientview5.flea7900.vip"
	AgentId := 0
	agent, _ := server.Db().Query("select * from x_agent_independence where Host = ?", []interface{}{host})
	if len(*agent) > 0 {
		AgentId = int(abugo.GetInt64FromInterface((*agent)[0]["UserId"]))
	}
	// 推广链接
	if len(reqdata.AgentCode) > 0 {
		promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
		indepenAgent, err := server.Db().Query("select * from x_agent_independence where PromotionHost = ?", []interface{}{promotionHost})
		if err == nil && len(*indepenAgent) > 0 {
			AgentId = int(abugo.GetInt64FromInterface((*indepenAgent)[0]["UserId"]))
		}
	}

	rediskey := fmt.Sprintf("%s:%s:game:%d_%d:%d", server.Project(), server.Module(), SellerId, ChannelId, AgentId)
	redisdata := server.Redis().Get(rediskey)

	// 获取在线人数配置
	type SetOnlineCntData struct {
		Display    int32 `json:"display"`    // 在玩人数是否显示
		DefaultMin int32 `json:"defaultMin"` // 为0时默认最小值
		DefaultMax int32 `json:"defaultMax"` // 为0时默认最大值
		Data       []struct {
			GameType int     `json:"gameType"`
			Brand    string  `json:"brand"`
			Multiple float32 `json:"multiple"`
		} `json:"data"`
	}
	var setOnlineCntData SetOnlineCntData
	// 从redis hash list中获取配置数据
	redisData := server.CRedis().HGet("CONFIG", "SET_ONLINE_CNT")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 setOnlineCntData
		if err := json.Unmarshal([]byte(dataStr), &setOnlineCntData); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	// 获取1分钟前的时间
	now := carbon.Now()
	startTime := now.SubMinutes(1).ToDateTimeString()
	endTime := now.ToDateTimeString()

	// 获取棋牌的在线人数
	type Record struct {
		Cnt int `gorm:"column:cnt"`
	}

	betCnt := 0
	var record Record
	if redisdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", SellerId, 0)
		where.Add("and", "State", "=", 1, 0)
		where.Add("and", "ChannelId", "=", ChannelId, 0)
		where.Add("and", "TopAgentId", "=", AgentId, nil)
		fields := `GameId,RoomLevel,Address,RewardRateEx,UsdtLimitMin,UsdtLimitMax,UsdtBscLimitMin,UsdtBscLimitMax,UsdtEthLimitMin,UsdtEthLimitMax,TrxLimitMin,TrxLimitMax,BackFeeRate,FeeRate,Chip,Lottery,PUsdtLimitMin,
		PUsdtLimitMax,PTrxLimitMin,PTrxLimitMax,SUsdtLimitMin,SUsdtLimitMax,STrxLimitMin,STrxLimitMax,UsdtLimitMinHe,UsdtLimitMaxHe,TrxLimitMinHe,TrxLimitMaxHe,
		IsNew,IsHot,IsRecom,OnlineMin,OnlineMax,BscAddress`
		presult, err := server.Db().Table("x_game").Select(fields).Where(where).GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}

		// 用于存储游戏ID对应的随机OnlineCnt
		onlineCntCache := make(map[int64]int)
		for i := 0; i < len(*presult); i++ {
			v := 2 - abugo.GetFloat64FromInterface((*presult)[i]["FeeRate"])
			(*presult)[i]["RewardRate"] = math.Floor(v*100000+0.5) / 100000
			gameId := abugo.GetInt64FromInterface((*presult)[i]["GameId"])
			(*presult)[i]["OnlineCnt"] = 0
			(*presult)[i]["ShowOnline"] = 0

			if setOnlineCntData.Display == 1 {
				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) as cnt from x_order where GameId=%v and CreateTime BETWEEN "%v" and "%v"`, gameId, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt

				if betCnt > 0 {
					for _, datum := range setOnlineCntData.Data {
						if 0 == int64(datum.GameType) && "hashOrigin" == datum.Brand && datum.Multiple > 0 {
							(*presult)[i]["OnlineCnt"] = math.Ceil(float64(float32(betCnt) * datum.Multiple))
						}

						if 0 == int64(datum.GameType) && "hashOrigin" == datum.Brand && datum.Multiple == 0 {
							(*presult)[i]["OnlineCnt"] = betCnt
						}
					}
				} else {
					if val, ok := onlineCntCache[gameId]; ok {
						(*presult)[i]["OnlineCnt"] = val
					} else {
						defaultMin := setOnlineCntData.DefaultMin
						defaultMax := setOnlineCntData.DefaultMax
						if (*presult)[i]["OnlineMin"].(int64) > 0 || (*presult)[i]["OnlineMax"].(int64) > 0 {
							defaultMin = int32((*presult)[i]["OnlineMin"].(int64))
							defaultMax = int32((*presult)[i]["OnlineMax"].(int64))
						}
						rand.Seed(time.Now().UnixNano())
						onlineCnt := rand.Intn(int(defaultMax-defaultMin+1)) + int(defaultMin)
						(*presult)[i]["OnlineCnt"] = onlineCnt
						onlineCntCache[gameId] = onlineCnt
					}

				}
				(*presult)[i]["ShowOnline"] = 1
			}

		}
		// if len(*agent) > 0 && IsSelfAddress == 1 {
		// 	for i := 0; i < len(*presult); i++ {
		// 		sql := "select * from x_game_address_pool where SellerId = ? and ChannelId = ? and GameId = ? and RoomLevel = ? and AgentId = ?"
		// 		address, _ := server.Db().Query(sql, []interface{}{reqdata.SellerId, ChannelId, (*presult)[i]["GameId"], (*presult)[i]["RoomLevel"], (*agent)[0]["UserId"]})
		// 		if len(*address) > 0 {
		// 			(*presult)[i]["Address"] = (*address)[0]["Address"]
		// 		}
		// 	}
		// }
		ctx.RespOK(*presult)
		server.Redis().SetEx(rediskey, 10, *presult)
	}
}

func (c *GameController) reward_pool(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, "")
	rediskey := fmt.Sprintf("%s:%s:reward_pool:%d", server.Project(), server.Module(), SellerId)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := make(map[string]interface{})
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		data := map[string]interface{}{}
		data["Address"] = server.GetConfigString(SellerId, 0, "RewardAddress")
		data["Trx"] = server.GetConfigFloat(SellerId, 0, "RewardPoolTrx")
		data["Usdt"] = server.GetConfigFloat(SellerId, 0, "RewardPoolUsdt")
		for i := 1; i < 100; i++ {
			idx := fmt.Sprint(i)
			address := server.GetConfigString(SellerId, 0, "RewardAddress"+idx)
			if len(address) > 10 {
				data["Address"+idx] = address
				data["Trx"+idx] = server.GetConfigFloat(SellerId, 0, "RewardPoolTrx"+idx)
				data["Usdt"+idx] = server.GetConfigFloat(SellerId, 0, "RewardPoolUsdt"+idx)
			} else {
				break
			}
		}
		ctx.RespOK(data)
		server.Redis().SetEx(rediskey, 10, data)
	}
}

// 体育投注详情
func (c *GameController) sport_bet_detail(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Id int64 `json:"Id" validate:"required"` // 游戏代码
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("获取体育投注详情，参数解析失败：", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	// 查询注单是否存在
	order := thirdGameModel.ThirdSportOrder{}
	err = server.Db().GormDao().Table("x_third_sport_pre_order").Where("Id=?", reqdata.Id).First(&order).Error
	if err != nil {
		logs.Error("获取体育投注详情，查询注单失败：", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	// 方法2：使用辅助函数
	display, err := thirdGameModel.ParseOrderFromJson(order.Brand, order.RawData)
	if err != nil {
		logs.Error("获取体育投注详情，解析失败：", err)
		return
	}
	// 从数据库订单中获取所有字段信息
	display.Id = order.Id
	display.ThirdId = order.ThirdId
	display.UserId = strconv.Itoa(order.UserId)
	display.Brand = order.Brand

	// 前台显示字段
	display.BetAmount = order.BetAmount // 投注金额
	display.WinAmount = order.WinAmount // 中奖金额
	display.Currency = order.Currency   // 币种
	display.Status = order.DataState    // 订单状态
	// 处理可能为 nil 的指针字段
	if order.BetTime != nil {
		display.BetTime = *order.BetTime
	}
	// BetLocalTime 需要从 BetLocalTime 转换
	if order.BetLocalTime != nil {
		display.BetLocalTime = *order.BetLocalTime // 直接使用 BetTime，因为现在都是字符串类型
	}
	if order.SettleTime != nil {
		display.SettleTime = *order.SettleTime
	}
	//display.RawData = order.RawData

	// 计算玩家输赢（修正后的正确计算方式）
	playerProfit := order.WinAmount - order.BetAmount // 中奖金额 - 投注金额
	playerProfit, _ = decimal.NewFromFloat(playerProfit).Round(6).Float64()

	// 根据订单状态设置玩家输赢状态
	display.Profit = playerProfit
	display.ProfitStatus = ""

	if order.DataState == 1 || order.DataState == 2 { // 已结算状态
		if playerProfit > 0 {
			display.ProfitStatus = "赢" // 玩家中奖金额大于投注金额
		} else if playerProfit < 0 {
			display.ProfitStatus = "输" // 玩家中奖金额小于投注金额
		} else {
			display.ProfitStatus = "平" // 金额相等
		}
	} else if order.DataState == -1 { // 未开奖状态
		display.ProfitStatus = "未开奖"
		display.Profit = 0
	} else if order.DataState == -2 { // 已撤单状态
		display.ProfitStatus = "已撤单"
		display.Profit = 0
	}

	ctx.RespOK(display)

}

func (c *GameController) bet_recordex(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		GameId    int
		Symbol    string
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 10
	}
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1 // 默认波场
	}
	type ReturnData struct {
		Id            int     `gorm:"column:Id"`
		GameId        int     `gorm:"column:GameId"`
		Memo          string  `gorm:"column:Memo"`
		RewardAmount  float64 `gorm:"column:RewardAmount"`
		CreateTime    string  `gorm:"column:CreateTime"`
		BlockHash     string  `gorm:"column:BlockHash"`
		BlockNum      int     `gorm:"column:BlockNum"`
		Symbol        string  `gorm:"column:Symbol"`
		Amount        float64 `gorm:"column:Amount"`
		IsWin         int     `gorm:"column:IsWin"`
		State         int     `gorm:"column:State"`
		Period        string  `gorm:"column:Period"`
		NextBlockHash string  `gorm:"column:NextBlockHash"`
		BetArea       string  `gorm:"column:BetArea"`
		LiuSui        float64 `gorm:"column:LiuSui"`
		TxId          string  `gorm:"column:TxId"`
		OpenArea      string  `gorm:"column:OpenArea"`
		ChainType     int     `gorm:"column:ChainType"`
	}
	token := server.GetToken(ctx)
	OrderBy := "CreateTime desc, Id desc"
	PageKey := "Id"
	dbtable := server.Db().Gorm().Table("x_order")
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "UserId", "=", token.UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "GameId", "=", reqdata.GameId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Symbol", "=", reqdata.Symbol, "")
	server.Db().AddWhere(&sql, &params, "and", "ChainType", "=", reqdata.ChainType, 0)
	type DataCount struct {
		Total int `gorm:"column:Total"`
	}
	dt := DataCount{}
	dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
	if dt.Total == 0 {
		ctx.Put("data", []ReturnData{})
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", 0)
		ctx.RespOK()
		return
	}
	result := []ReturnData{}
	paramsbytes, _ := json.Marshal(params)
	logs.Debug("bet_record sql:", sql, string(paramsbytes))
	offset := (reqdata.Page - 1) * reqdata.PageSize
	dbtable.Where(sql, params...).Limit(reqdata.PageSize).Offset(offset).Order(OrderBy).Group("BlockNum").Find(&result)
	for i := 0; i < len(result); i++ {
		result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
	}
	ctx.Put("data", result)
	ctx.Put("page", reqdata.Page)
	ctx.Put("pagesize", reqdata.PageSize)
	ctx.Put("total", dt.Total)
	ctx.RespOK()
}

func (c *GameController) bet_record(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		QueryType int
		Days      int //1、7、30、60、90 1天 7天 30天 60天 90天 Days=0 代表前台选时间段查询，已经弃用
		Symbol    string
		Brand     string
		StartTime int64 // 前端时区的毫秒时间戳  >=StartTime
		EndTime   int64 // 前端时区的毫秒时间戳 <=EndTime
		TimeZone  int   //客户当前所在时区分钟偏移量, javascript的Date().getTimezoneOffset()
		State     int   // 0 全部状态 -1 待结算 -2 撤单 1 已结算
	}
	nowtime := time.Now().UnixMilli()
	defer func() {
		logs.Info("bet_record time:%d", time.Now().UnixMilli()-nowtime)
	}()
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 10
	}

	//endtime := abugo.GetLocalTime()
	//starttime := ""
	//if reqdata.Days == -1 { //非自定义日期 1、7、30、60、90 1天 7天 30天 60天 90天 Days=-1 代表前台选时间段查询
	//	starttime = abugo.TimeStampToLocalTime(reqdata.StartTime)
	//	endtime = abugo.TimeStampToLocalTime(reqdata.EndTime)
	//} else { //自定义日期
	//	st := abugo.TimeStampToLocalDate(time.Now().Unix() * 1000)
	//	st = st + " 00:00:00"
	//	if reqdata.Days == 1 {
	//		endtime = st
	//	}
	//	t, _ := time.ParseInLocation("2006-01-02 15:04:05", st, time.Local)
	//	duration, _ := time.ParseDuration(fmt.Sprintf("-%dh", reqdata.Days*24))
	//	t = t.Add(duration)
	//	starttime = t.Format("2006-01-02 15:04:05")
	//}

	timeZone := reqdata.TimeZone
	if timeZone == 0 {
		// 如果前端没传时区默认是 北京 UTC+8，所以是 -480
		timeZone = -480
	}
	// 1. 生成日期记录
	startTimeBeijing := utils.ConvertToBeijingTime(reqdata.StartTime, timeZone)
	endTimeBeijing := utils.ConvertToBeijingTime(reqdata.EndTime, timeZone)

	starttime := abugo.TimeStampToLocalTime(startTimeBeijing)
	endtime := abugo.TimeStampToLocalTime(endTimeBeijing)

	joinSqlTemp := "left join x_game_list ON %s.`Brand` = x_game_list.`Brand` AND %s.`GameId` = x_game_list.`GameId`"
	if reqdata.QueryType == 1 { //转账,余额哈希
		type ReturnData struct {
			Id            int     `gorm:"column:Id"`
			GameId        int     `gorm:"column:GameId"`
			Memo          string  `gorm:"column:Memo"`
			RewardAmount  float64 `gorm:"column:RewardAmount"`
			CreateTime    string  `gorm:"column:CreateTime"`
			BlockHash     string  `gorm:"column:BlockHash"`
			BlockNum      int     `gorm:"column:BlockNum"`
			Symbol        string  `gorm:"column:Symbol"`
			Amount        float64 `gorm:"column:Amount"`
			IsWin         int     `gorm:"column:IsWin"`
			State         int     `gorm:"column:State"`
			Period        string  `gorm:"column:Period"`
			NextBlockHash string  `gorm:"column:NextBlockHash"`
			BetArea       string  `gorm:"column:BetArea"`
			LiuSui        float64 `gorm:"column:LiuSui"`
			TxId          string  `gorm:"column:TxId"`
			OpenArea      string  `gorm:"column:OpenArea"`
			ChainType     int     `gorm:"column:ChainType"`
		}
		token := server.GetToken(ctx)
		OrderBy := "CreateTime desc, Id desc"
		PageKey := "Id"
		dbtable := server.Db().Gorm().Table("x_order")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "Symbol", "=", reqdata.Symbol, "")
		server.Db().AddWhere(&sql, &params, "and", "CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "CreateTime", "<", endtime, "")
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		paramsbytes, _ := json.Marshal(params)
		logs.Debug("bet_record sql:", sql, string(paramsbytes))
		offset := (reqdata.Page - 1) * reqdata.PageSize
		dbtable.Where(sql, params...).Limit(reqdata.PageSize).Offset(offset).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 2 { //api彩票
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_lottery.Id desc"
		PageKey := "x_third_lottery.Id"
		dbtable := server.Db().Gorm().Table("x_third_lottery")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_lottery.UserId", "=", token.UserId, 0)
		//sql += fmt.Sprintf(" and GameId in (%d,%d)", 713, 711)//哈希彩票返回所有彩票注单
		server.Db().AddWhere(&sql, &params, "and", "x_third_lottery.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_lottery.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_lottery.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}

		dbtable.Select("x_third_lottery.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_lottery", "x_third_lottery")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 3 { // gfg棋牌
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_qipai.Id desc"
		PageKey := "x_third_qipai.Id"
		dbtable := server.Db().Gorm().Table("x_third_qipai")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_qipai.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_qipai.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_qipai.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_qipai.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_qipai.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_qipai", "x_third_qipai")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()

	} else if reqdata.QueryType == 4 { //gfg电子
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_dianzhi.Id desc"
		PageKey := "x_third_dianzhi.Id"
		dbtable := server.Db().Gorm().Table("x_third_dianzhi")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_dianzhi.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_dianzhi.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_dianzhi.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_dianzhi.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_dianzhi.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_dianzhi", "x_third_dianzhi")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 5 { //小游戏
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_quwei.Id desc"
		PageKey := "x_third_quwei.Id"
		dbtable := server.Db().Gorm().Table("x_third_quwei")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_quwei.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_quwei.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_quwei.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_quwei.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_quwei.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_quwei", "x_third_quwei")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
			if result[i].Brand == "og" && result[i].GameId == "35" {
				// updown 游戏不要把期数信息返回给用户前端了
				tmpRawDataOgUpDown := struct {
					LockMoney string  `json:"lockMoney"`
					Money     string  `json:"money"`
					GameId    int64   `json:"gameId"`
					OrderId   int     `json:"orderId"`
					AccountId string  `json:"accountId"`
					RoundId   string  `json:"roundId"`
					TimeStamp int64   `json:"timeStamp"`
					Odds      float64 `json:"odds"`   // 赔率 updown是按照赢钱除以本金的百分比 比如1.5倍赔率 传的赔率是50
					Select    string  `json:"select"` // 下注内容 up 或者 down
					Result    string  `json:"result"` // 开奖结果 up-起拍价-结束价 或者 down-起拍价-结束价
				}{}
				json.Unmarshal([]byte(result[i].RawData), &tmpRawDataOgUpDown)
				tmpRawDataOgUpDownBytes, _ := json.Marshal(tmpRawDataOgUpDown)
				result[i].RawData = string(tmpRawDataOgUpDownBytes)
			}
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 6 { //一分哈希
		type ReturnData struct {
			Id            int     `gorm:"column:Id"`
			GameId        int     `gorm:"column:GameId"`
			Memo          string  `gorm:"column:Memo"`
			RewardAmount  float64 `gorm:"column:RewardAmount"`
			CreateTime    string  `gorm:"column:CreateTime"`
			BlockHash     string  `gorm:"column:BlockHash"`
			BlockNum      int     `gorm:"column:BlockNum"`
			Symbol        string  `gorm:"column:Symbol"`
			Amount        float64 `gorm:"column:Amount"`
			IsWin         int     `gorm:"column:IsWin"`
			State         int     `gorm:"column:State"`
			Period        string  `gorm:"column:Period"`
			NextBlockHash string  `gorm:"column:NextBlockHash"`
			BetArea       string  `gorm:"column:BetArea"`
			LiuSui        float64 `gorm:"column:LiuSui"`
			TxId          string  `gorm:"column:TxId"`
			OpenArea      string  `gorm:"column:OpenArea"`
			EName         string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "Id desc"
		PageKey := "Id"
		dbtable := server.Db().Gorm().Table("x_order")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "Symbol", "=", reqdata.Symbol, "")
		server.Db().AddWhere(&sql, &params, "and", "(GameId", "<", 100, "")
		server.Db().AddWhere(&sql, &params, "or", "(GameId", ">", 200, "")
		server.Db().AddWhere(&sql, &params, ") and", "CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 7 { //真人
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_live.Id desc"
		PageKey := "x_third_live.Id"
		dbtable := server.Db().Gorm().Table("x_third_live")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_live.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_live.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_live.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_live.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_live.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_live", "x_third_live")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)

		gameListDao := server.DaoxHashGame().XGameList
		gameListDb := gameListDao.WithContext(ctx.Gin())
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
			gameRes, _ := gameListDb.Where(gameListDao.GameID.Eq(result[i].GameId)).First()
			if gameRes != nil {
				result[i].GameName = gameRes.Name
			}
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 8 { //体育
		type ReturnData struct {
			Id           int     `gorm:"column:Id"`
			Brand        string  `gorm:"column:Brand"`
			ThirdId      string  `gorm:"column:ThirdId"`
			GameId       string  `gorm:"column:GameId"`
			GameName     string  `gorm:"column:GameName"`
			BetAmount    float64 `gorm:"column:BetAmount"`
			WinAmount    float64 `gorm:"column:WinAmount"`
			Fee          float64 `gorm:"column:Fee"`
			State        int     `gorm:"column:DataState"`
			RawData      string  `gorm:"column:RawData"`
			ThirdTime    string  `gorm:"column:ThirdTime"`
			SettleTime   string  `gorm:"column:SettleTime"`   //结算时间
			BetTime      string  `gorm:"column:BetTime"`      //投注时间 三方时间
			BetLocalTime string  `gorm:"column:BetLocalTime"` //投注时间-北京时间
			CreateTime   string  `gorm:"column:CreateTime"`   //创建注单时间 等同投注时间
			ValidBet     float32 `gorm:"column:ValidBet"`
			EName        string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_sport_pre_order.Id desc"
		PageKey := "x_third_sport_pre_order.Id"
		dbtable := server.Db().Gorm().Table("x_third_sport_pre_order")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_sport_pre_order.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_sport_pre_order.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_sport_pre_order.BetLocalTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_sport_pre_order.BetLocalTime", "<", endtime, "")
		if reqdata.State != 0 {
			server.Db().AddWhere(&sql, &params, "and", "x_third_sport_pre_order.DataState", "=", reqdata.State, 0)
		}

		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_sport_pre_order.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_sport_pre_order", "x_third_sport_pre_order")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			// 转换投注时间和结算时间为 UTC 毫秒时间戳
			result[i].BetTime = convertSportTime(result[i].BetTime, result[i].Brand)
			result[i].SettleTime = convertSportTime(result[i].SettleTime, result[i].Brand)
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
			result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
		}

		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	} else if reqdata.QueryType == 9 { //api彩票2
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			DataState int     `gorm:"column:DataState"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			BetTime   string  `gorm:"column:BetTime"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		var result []ReturnData

		sql := ""
		if reqdata.Brand == "bpc" {
			sql += fmt.Sprintf(" %%s.GameId in (%d,%d,%d)", 30, 44, 1)
		} else if reqdata.Brand == "ssc" {
			sql += fmt.Sprintf(" %%s.GameId in (%d,%d,%d,%d)", 667, 715, 669, 717)
		} else if reqdata.Brand == "ttjsc" {
			sql += fmt.Sprintf(" %%s.GameId in (%d,%d)", 328, 253)
		} else {
			sql += fmt.Sprintf(" %%s.GameId in (%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d)", 30, 44, 328, 667, 715, 253, 669, 717, 713, 711, 329, 254, 670, 668, 714, 710, 716, 712, 672, 671, 1)
		}

		type DataCount struct {
			Total int `gorm:"column:Total"`
		}

		dtLot := DataCount{}
		dtPre := DataCount{}

		sqlLot := fmt.Sprintf("UserId=%d and BetTime>='%s' and BetTime<'%s' and %s", token.UserId, starttime, endtime, sql)
		sqlPre := fmt.Sprintf("UserId=%d and DataState=%d and BetTime>='%s' and BetTime<'%s' and %s", token.UserId, -1, starttime, endtime, sql)

		dbtableLot := server.Db().Gorm().Table("x_third_lottery")
		dbtableLot.Select(fmt.Sprintf("Count(%s) as Total", "Id")).Where(fmt.Sprintf(sqlLot, "x_third_lottery")).Scan(&dtLot)

		dbtablePre := server.Db().Gorm().Table("x_third_lottery_pre_order")
		dbtablePre.Select(fmt.Sprintf("Count(%s) as Total", "Id")).Where(fmt.Sprintf(sqlPre, "x_third_lottery_pre_order")).Scan(&dtPre)

		offset := (reqdata.Page - 1) * reqdata.PageSize
		unionsql := fmt.Sprintf("select x_third_lottery.Id AS Id, x_third_lottery.Brand, ThirdId, x_third_lottery.GameId, GameName, x_third_lottery.BetAmount, WinAmount, ValidBet, DataState, RawData, ThirdTime, BetTime, EName from x_third_lottery left join x_game_list ON x_third_lottery.`Brand` = x_game_list.`Brand` AND x_third_lottery.`GameId` = x_game_list.`GameId` where x_third_lottery.UserId=%d and x_third_lottery.BetTime>='%s' and x_third_lottery.BetTime<'%s' and %s "+
			"union all select x_third_lottery_pre_order.Id AS Id, x_third_lottery_pre_order.Brand, ThirdId, x_third_lottery_pre_order.GameId, GameName, x_third_lottery_pre_order.BetAmount, WinAmount, ValidBet, DataState, RawData, ThirdTime, BetTime, EName from x_third_lottery_pre_order left join x_game_list ON x_third_lottery_pre_order.`Brand` = x_game_list.`Brand` AND x_third_lottery_pre_order.`GameId` = x_game_list.`GameId` where x_third_lottery_pre_order.UserId=%d and x_third_lottery_pre_order.DataState=%d and x_third_lottery_pre_order.BetTime>='%s' and x_third_lottery_pre_order.BetTime<'%s'  and %s "+
			"order by BetTime desc, Id desc limit %d offset %d", token.UserId, starttime, endtime, fmt.Sprintf(sql, "x_third_lottery"), token.UserId, -1, starttime, endtime, fmt.Sprintf(sql, "x_third_lottery_pre_order"), reqdata.PageSize, offset)

		server.Db().Gorm().Raw(unionsql).Scan(&result)

		if len(result) > 0 {
			ctx.Put("data", result)
			total := dtLot.Total + dtPre.Total
			ctx.Put("total", total)
		} else {
			ctx.Put("data", []ReturnData{})
			ctx.Put("total", 0)

		}
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.RespOK()
	} else if reqdata.QueryType == 10 { //德州
		type ReturnData struct {
			Id        int     `gorm:"column:Id"`
			Brand     string  `gorm:"column:Brand"`
			ThirdId   string  `gorm:"column:ThirdId"`
			GameId    string  `gorm:"column:GameId"`
			GameName  string  `gorm:"column:GameName"`
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
			Fee       float64 `gorm:"column:Fee"`
			State     int     `gorm:"column:State"`
			RawData   string  `gorm:"column:RawData"`
			ThirdTime string  `gorm:"column:ThirdTime"`
			ValidBet  float32 `gorm:"column:ValidBet"`
			EName     string  `gorm:"column:EName"`
		}
		token := server.GetToken(ctx)
		OrderBy := "x_third_texas.Id desc"
		PageKey := "x_third_texas.Id"
		dbtable := server.Db().Gorm().Table("x_third_texas")
		sql := ""
		params := []interface{}{}
		server.Db().AddWhere(&sql, &params, "and", "x_third_texas.UserId", "=", token.UserId, 0)
		server.Db().AddWhere(&sql, &params, "and", "x_third_texas.Brand", "=", reqdata.Brand, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_texas.CreateTime", ">=", starttime, "")
		server.Db().AddWhere(&sql, &params, "and", "x_third_texas.CreateTime", "<", endtime, "")
		type MinData struct {
			MinValue int `gorm:"column:MinValue"`
		}
		md := MinData{}
		dbtable.Select(fmt.Sprintf("%s as MinValue", PageKey)).Order(OrderBy).Where(sql, params...).Limit(1).Offset((reqdata.Page - 1) * reqdata.PageSize).Scan(&md)
		type DataCount struct {
			Total int `gorm:"column:Total"`
		}
		dt := DataCount{}
		dbtable.Select(fmt.Sprintf("Count(%s) as Total", PageKey)).Where(sql, params...).Scan(&dt)
		if dt.Total == 0 {
			ctx.Put("data", []ReturnData{})
			ctx.Put("page", reqdata.Page)
			ctx.Put("pagesize", reqdata.PageSize)
			ctx.Put("total", 0)
			ctx.RespOK()
			return
		}
		result := []ReturnData{}
		dbtable.Select("x_third_texas.*, EName").Where(fmt.Sprintf("%s <= ?", PageKey), md.MinValue).Where(sql, params...).Joins(fmt.Sprintf(joinSqlTemp, "x_third_texas", "x_third_texas")).Limit(reqdata.PageSize).Order(OrderBy).Find(&result)
		for i := 0; i < len(result); i++ {
			result[i].ThirdTime = abugo.LocalTimeToUtc(result[i].ThirdTime)
		}
		ctx.Put("data", result)
		ctx.Put("page", reqdata.Page)
		ctx.Put("pagesize", reqdata.PageSize)
		ctx.Put("total", dt.Total)
		ctx.RespOK()
	}
}

func (c *GameController) hash_bet_record_info(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		BlockNum int    `validate:"required"` // 区块号或期数
		GameId   int    `validate:"required"` // 游戏ID
		Symbol   string `validate:"required"` // 币种符号
		// ChainType int    `validate:"required"` // 链类型
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 返回数据结构
	type ReturnData struct {
		Id         int     `gorm:"column:id"`         // 订单ID
		BetArea    string  `gorm:"column:betArea"`    // 投注区域
		Symbol     string  `gorm:"column:Symbol"`     // 币种符号
		Amount     float64 `gorm:"column:Amount"`     // 投注金额
		WinLose    float64 `gorm:"column:WinLose"`    // 输赢金额
		State      int     `gorm:"column:State"`      // 订单状态
		CreateTime string  `gorm:"column:CreateTime"` // 创建时间
	}

	// 获取用户token
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErr(errors.New("用户未登录"), &errcode)
		return
	}

	// 构建查询
	dbtable := server.Db().Gorm().Table("x_order")

	// 构建WHERE条件
	whereConditions := []string{
		"gameId = ?",
		"userId = ?",
		"(blockNum = ? OR period = ?)",
		"Symbol = ?",
	}
	params := []interface{}{
		reqdata.GameId,
		token.UserId,
		reqdata.BlockNum,
		reqdata.BlockNum,
		reqdata.Symbol,
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 执行查询 - 对应SQL: select id,betArea,Symbol,Amount,(RewardAmount-Amount) as WinLose from x_order where gameId=102 and userId=80226356 and (blockNum=72672620 or period=72672620) and Symbol='usdt' order by createTime desc
	result := []ReturnData{}
	err = dbtable.
		Select("id, betArea, Symbol, Amount, (RewardAmount - Amount) as WinLose, State, CreateTime").
		Where(whereClause, params...).
		Order("createTime desc").
		Find(&result).Error

	if err != nil {
		logs.Error("hash_bet_record_info query error: %v", err)
		ctx.RespErr(errors.New("查询失败"), &errcode)
		return
	}

	// 返回结果
	ctx.RespOK(result)
}

func (c *GameController) hash_bet_record_group(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page          int    // 页码，默认为1
		PageSize      int    // 每页大小，默认为15
		HashType      int    // Hash分类 1:哈希竞猜 2:哈希轮盘
		Symbol        string // 币种符号（可选）
		ChainType     int    // 链类型（可选）
		StartTime     int64  // 开始时间（可选）
		EndTime       int64  // 结束时间（可选）
		GroupOrderNum string // 组合订单号（可选）
		TimeZone      int    //客户当前所在时区分钟偏移量, javascript的Date().getTimezoneOffset()
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 设置默认分页参数
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize == 0 {
		reqdata.PageSize = 15
	}

	// 返回数据结构
	type ReturnData struct {
		Id            int     `gorm:"column:Id"`
		UserId        int32   `gorm:"column:UserId"`       // 用户ID
		GameId        int     `gorm:"column:GameId"`       // 游戏ID
		ChainType     int     `gorm:"column:ChainType"`    // 链类型
		Period        string  `gorm:"column:Period"`       // 期数
		BlockNum      int     `gorm:"column:BlockNum"`     // 区块号
		Symbol        string  `gorm:"column:Symbol"`       // 币种符号
		Count         int     `gorm:"column:Count"`        // 投注次数
		RewardAmount  float64 `gorm:"column:RewardAmount"` // 总奖励金额
		Amount        float64 `gorm:"column:Amount"`       // 总投注金额
		State         int     `gorm:"column:State"`        // 状态
		CreateTime    string  `gorm:"column:CreateTime"`   // 创建时间
		GroupOrderNum string  `gorm:"column:GroupOrderNum"`
		NextBlockHash string  `gorm:"column:NextBlockHash"`
		BlockHash     string  `gorm:"column:BlockHash"`
		OpenArea      string  `gorm:"column:OpenArea"`
		BetArea       string  `gorm:"column:BetArea"`
	}

	// 获取用户token
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErr(errors.New("用户未登录"), &errcode)
		return
	}

	// 计算偏移量
	offset := (reqdata.Page - 1) * reqdata.PageSize

	// 构建查询
	dbtable := server.Db().Gorm().Table("x_order")

	// 构建WHERE条件
	whereConditions := []string{"UserId = ?"}
	params := []interface{}{token.UserId}

	if reqdata.StartTime > 0 && reqdata.EndTime > 0 {
		timeZone := reqdata.TimeZone
		if timeZone == 0 {
			// 如果前端没传时区默认是 北京 UTC+8，所以是 -480
			timeZone = -480
		}
		// 1. 生成日期记录
		startTimeBeijing := utils.ConvertToBeijingTime(reqdata.StartTime, timeZone)
		endTimeBeijing := utils.ConvertToBeijingTime(reqdata.EndTime, timeZone)

		// 时间戳转Time.time
		startTime := time.UnixMilli(startTimeBeijing).Format("2006-01-02 15:04:05")
		endTime := time.UnixMilli(endTimeBeijing).Format("2006-01-02 15:04:05")
		whereConditions = append(whereConditions, "CreateTime >= ? AND CreateTime <= ?")
		params = append(params, startTime, endTime)
	}

	if reqdata.GroupOrderNum != "" {
		if strings.Contains(reqdata.GroupOrderNum, "_") {
			whereConditions = append(whereConditions, "CONCAT(UserId, '_', IFNULL(Period, BlockNum)) = ?")
		} else {
			whereConditions = append(whereConditions, "Id = ?")
		}
		params = append(params, reqdata.GroupOrderNum)
	}

	// 如果指定了Symbol，添加到条件中
	if reqdata.Symbol != "" {
		whereConditions = append(whereConditions, "Symbol = ?")
		params = append(params, reqdata.Symbol)
	}

	// 如果指定了ChainType，添加到条件中
	if reqdata.ChainType > 0 {
		whereConditions = append(whereConditions, "ChainType = ?")
		params = append(params, reqdata.ChainType)
	}

	var gameIds []int
	if reqdata.HashType == 1 {
		gameIds = []int{1, 2, 3, 4, 5, 11, 12, 201, 202, 203, 204, 205, 102, 103, 104, 105, 131, 132, 133, 134, 135, 301, 302, 331, 332}
	} else if reqdata.HashType == 2 {
		gameIds = []int{13, 106, 116, 126, 136, 206, 303, 313, 323, 333}
	}
	if len(gameIds) > 0 {
		whereConditions = append(whereConditions, "GameId IN (?)")
		params = append(params, gameIds)
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 执行查询 - 对应SQL: SELECT UserId,GameId,ChainType,Period,IFNULL(blockNum, period) As BlockNum, Symbol, count(*) As Count,sum(RewardAmount) RewardAmount, sum(Amount) Amount,State FROM `x_order` where UserId=80226356 GROUP BY BlockNum,gameId,ChainType,symbol order by createTime desc
	result := []ReturnData{}
	err = dbtable.
		Select("Min(Id) As Id,UserId, GameId, ChainType, Period, BlockNum, Symbol, count(*) AS Count, sum(RewardAmount) AS RewardAmount, sum(Amount) AS Amount, State, Min(CreateTime) AS CreateTime,NextBlockHash,BlockHash,OpenArea,BetArea").
		Where(whereClause, params...).
		Group("Period, BlockNum, GameId, ChainType, Symbol").
		Order("createTime desc").
		Limit(reqdata.PageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		logs.Error("hash_bet_record_group query error: %v", err)
		ctx.RespErr(errors.New("查询失败"), &errcode)
		return
	}

	for i := 0; i < len(result); i++ {
		number := result[i].Period
		if number == "" || result[i].Period == "0" {
			number = fmt.Sprintf("%d", result[i].BlockNum)
		}
		result[i].GroupOrderNum = fmt.Sprintf("%d_%s", result[i].UserId, number)
	}

	// 查询总数（用于分页）- 需要统计分组后的数量
	type CountResult struct {
		Total int64 `gorm:"column:total"`
	}
	var countResult CountResult

	// 使用子查询来统计分组后的数量
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) as total FROM (
			SELECT BlockNum, GameId, ChainType, Symbol
			FROM x_order
			WHERE %s
			GROUP BY Period,BlockNum, GameId, ChainType, Symbol
		) as grouped_data
	`, whereClause)

	err = server.Db().Gorm().Raw(countQuery, params...).Scan(&countResult).Error

	var total int64
	if err != nil {
		logs.Error("hash_bet_record_group count error: %v", err)
		// 如果计数失败，设置为结果数量
		total = int64(len(result))
	} else {
		total = countResult.Total
	}

	// 返回结果
	ctx.Put("data", result)
	ctx.Put("page", reqdata.Page)
	ctx.Put("pagesize", reqdata.PageSize)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *GameController) bet_recordtest(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
		GameId   int
		Symbol   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	where := abugo.AbuDbWhere{}
	where.Add("and", "GameId", "=", reqdata.GameId, 0)
	where.Add("and", "Symbol", "=", reqdata.Symbol, "")
	total, presult := server.Db().Table("x_order").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", *presult)
	ctx.Put("page", reqdata.Page)
	ctx.Put("pagesize", reqdata.PageSize)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *GameController) luzi(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Address string `validate:"required"`
		GameId  int    `validate:"required"`
		Host    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, _ := server.GetChannel(ctx, reqdata.Host)
	where := abugo.AbuDbWhere{}
	where.Add("and", "ChannelId", "=", ChannelId, 0)
	where.Add("and", "GameId", "=", reqdata.GameId, 0)
	where.Add("and", "FromAddress", "=", reqdata.Address, "")
	presult, _ := server.Db().Table("x_order").Select("State,IsWin,BetArea,OpenArea").Where(where).OrderBy("id desc").Limit(66).GetList()
	ctx.Put("data", *presult)
	ctx.RespOK()
}

func (c *GameController) next(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		GameId int `validate:"required"` //运营商
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	rediskey := fmt.Sprintf("%s:%s:game_next:%d", server.Project(), server.Module(), reqdata.GameId)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := make(map[string]interface{})
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		sql := "SELECT GameId,Period,OpenTime,TIME_TO_SEC(TIMEDIFF(OpenTime,NOW())) AS LeftTime FROM x_game_period WHERE GameId = ? and OpenTime < DATE_ADD(NOW(),INTERVAL 60 SECOND) ORDER BY OpenTime DESC LIMIT 1"
		presult, err := server.Db().Query(sql, []interface{}{reqdata.GameId})
		if ctx.RespErr(err, &errcode) {
			return
		}
		server.Redis().SetEx(rediskey, 1, (*presult)[0])
		ctx.RespOK((*presult)[0])
	}
}

func (c *GameController) record_abstract(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Days      int
		Symbol    string `validate:"required"` //币种
		StartTime int64  // 前端时区的毫秒时间戳  >=StartTime Days=0 才有=值
		EndTime   int64  // 前端时区的毫秒时间戳 <=EndTime
		TimeZone  int    //客户当前所在时区分钟偏移量, javascript的Date().getTimezoneOffset()
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	result := []interface{}{}
	//endtime := abugo.GetLocalTime()
	//starttime := ""
	//st := abugo.TimeStampToLocalDate(time.Now().Unix() * 1000)
	//st = st + " 00:00:00"
	//if reqdata.Days == 1 {
	//	endtime = st
	//}
	//t, _ := time.ParseInLocation("2006-01-02 15:04:05", st, time.Local)
	//duration, _ := time.ParseDuration(fmt.Sprintf("-%dh", reqdata.Days*24))
	//t = t.Add(duration)
	//starttime = t.Format("2006-01-02 15:04:05")

	// 获取当前时间
	//now := carbon.Now()

	//var starttime, endtime string

	//switch reqdata.Days {
	//case 0:
	//	starttime = now.StartOfDay().ToString()
	//	endtime = now.EndOfDay().ToString()
	//case 1:
	//	starttime = now.Yesterday().StartOfDay().ToString()
	//	endtime = now.Yesterday().EndOfDay().ToString()
	//case 7:
	//	starttime = now.SubDays(6).StartOfDay().ToString() // 近7天从今天算起包括今天
	//	endtime = now.EndOfDay().ToString()
	//case 30:
	//	starttime = now.SubDays(29).StartOfDay().ToString() // 近30天从今天算起包括今天
	//	endtime = now.EndOfDay().ToString()
	//default:
	//	starttime = now.StartOfDay().ToString()
	//	endtime = now.EndOfDay().ToString()
	//}

	timeZone := reqdata.TimeZone
	if timeZone == 0 {
		// 如果前端没传时区默认是 北京 UTC+8，所以是 -480
		timeZone = -480
	}
	// 1. 生成日期记录
	startTimeBeijing := utils.ConvertToBeijingTime(reqdata.StartTime, timeZone)
	endTimeBeijing := utils.ConvertToBeijingTime(reqdata.EndTime, timeZone)

	starttime := abugo.TimeStampToLocalTime(startTimeBeijing)
	endtime := abugo.TimeStampToLocalTime(endTimeBeijing)

	{
		sql := `SELECT
		COUNT(id) AS TotalBetCount,
		IFNULL(SUM(Amount),0) AS Amount,
		IFNULL(SUM(RewardAmount),0) AS RewardAmount,
		SUM(LiuSui) as LiuSui
		FROM x_order WHERE UserId = ? AND Symbol=  ? and CreateTime >= ? and CreateTime <= ?`
		data, _ := server.Db().Query(sql, []interface{}{token.UserId, reqdata.Symbol, starttime, endtime})
		(*data)[0]["type"] = "haxi"
		result = append(result, (*data)[0])
	}
	if reqdata.Symbol == "usdt" {
		{
			//sql := `SELECT
			//COUNT(id) AS TotalBetCount,
			//IFNULL(SUM(BetAmount),0) AS Amount,
			//IFNULL(SUM(WinAmount),0) AS RewardAmount,
			//SUM(ValidBet) as LiuSui
			//FROM x_third_lottery WHERE UserId = ? and ThirdTime >= ? and ThirdTime < ? and State = 1 and GameId in (713,711)`
			//data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			//(*data)[0]["type"] = "lottery"
			//result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			(IFNULL(SUM(WinAmount),0) - IFNULL(SUM(Fee),0)) AS RewardAmount,
			SUM(ValidBet) as LiuSui
			FROM x_third_qipai WHERE UserId = ? and ThirdTime >= ? and ThirdTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "qipai"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			(IFNULL(SUM(WinAmount),0) - IFNULL(SUM(Fee),0)) AS RewardAmount,
			SUM(ValidBet) as LiuSui
			FROM x_third_dianzhi WHERE UserId = ? and  ThirdTime >= ? and ThirdTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "dianzi"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			SUM(ValidBet) as LiuSui,
			SUM(Fee) AS Fee
			FROM x_third_quwei WHERE UserId = ? and ThirdTime >= ? and ThirdTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "xiaoyouxi"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			SUM(ValidBet) as LiuSui
			FROM x_third_live WHERE UserId = ? and ThirdTime >= ? and ThirdTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "live"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			SUM(ValidBet) as LiuSui
			FROM x_third_sport WHERE UserId = ? and BetLocalTime >= ? and BetLocalTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "sport"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			SUM(ValidBet) as LiuSui
			FROM x_third_texas WHERE UserId = ? and ThirdTime >= ? and ThirdTime <= ?`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})
			(*data)[0]["type"] = "texas"
			result = append(result, (*data)[0])
		}
		{
			sql := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			IFNULL(SUM(ValidBet),0) as LiuSui
			FROM x_third_lottery WHERE UserId = ? and BetTime >= ? and BetTime <= ? and GameId in (30,44,328,667,715,253,669,717,713,711,329, 254, 670, 668, 714, 710, 716, 712,672,671,1,618,984)`
			data, _ := server.Db().Query(sql, []interface{}{token.UserId, starttime, endtime})

			sqls := `SELECT
			COUNT(id) AS TotalBetCount,
			IFNULL(SUM(BetAmount),0) AS Amount,
			IFNULL(SUM(WinAmount),0) AS RewardAmount,
			IFNULL(SUM(ValidBet),0) as LiuSui
			FROM x_third_lottery_pre_order WHERE UserId = ? and DataState = ? and BetTime >= ? and BetTime <= ? and GameId in (30,44,328,667,715,253,669,717,713,711,329, 254, 670, 668, 714, 710, 716, 712,672,671,1,618,984)`
			datas, _ := server.Db().Query(sqls, []interface{}{token.UserId, -1, starttime, endtime})

			redata := make(map[string]interface{})
			redata["Amount"] = abugo.GetFloat64FromInterface((*data)[0]["Amount"]) + abugo.GetFloat64FromInterface((*datas)[0]["Amount"])
			redata["LiuSui"] = abugo.GetFloat64FromInterface((*data)[0]["LiuSui"]) + abugo.GetFloat64FromInterface((*datas)[0]["LiuSui"])
			redata["RewardAmount"] = abugo.GetFloat64FromInterface((*data)[0]["RewardAmount"]) + abugo.GetFloat64FromInterface((*datas)[0]["RewardAmount"])
			redata["TotalBetCount"] = abugo.GetFloat64FromInterface((*data)[0]["TotalBetCount"]) + abugo.GetFloat64FromInterface((*datas)[0]["TotalBetCount"])
			redata["type"] = "caipiao"

			result = append(result, redata)
		}
	}
	ctx.RespOK(result)
}

func (c *GameController) gamelist(ctx *abugo.AbuHttpContent) {
	rediskey := fmt.Sprintf("%v:%v:gamelist", server.Project(), server.Module())
	rdata := server.Redis().Get(rediskey)
	if rdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(rdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "State", "=", 1, nil)
		where.Add("and", "OpenState", "=", 1, nil)
		where.Add("and", "GameType", "=", 1, nil)
		data, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
		ctx.RespOK(data)
		bytes, _ := json.Marshal(data)
		server.Redis().SetStringEx(rediskey, 10, string(bytes))
	}
}

func (c *GameController) get_game_list(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page        int
		PageSize    int
		Cat         int
		Brand       []string
		Name        string
		GameSubType []string
		GameId      []string
		IsHot       int
		IsNew       int
		ShowBlocked int
		Lang        int
		AgentCode   string
		Host        string
		Limit       *int
		Offset      *int
	}
	errcode := 0
	reqdata := RequestData{Lang: 1}
	err := ctx.RequestData(&reqdata)

	if ctx.RespErr(err, &errcode) {
		return
	}

	host, _, _ := net.SplitHostPort(ctx.Host())
	if reqdata.Host != "" {
		host = reqdata.Host
	}

	host = strings.Replace(host, "www.", "", -1)

	// 获取当前请求域名对应的GameSortEx
	gameSortEx := c.GetGameSortEx(host, reqdata.Lang)
	// gameSortEx 转换为map
	gameSortExMap := map[string][]map[string]interface{}{}
	independenceAgent, err := server.XDb().Table("x_agent_independence").Where("Host = ? and State = 1", host).First()
	// 推广链接
	if len(reqdata.AgentCode) > 0 {
		promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
		indepenAgent, err := server.XDb().Table("x_agent_independence").Where("PromotionHost = ? and State = 1", promotionHost).First()
		if err == nil && indepenAgent != nil {
			independenceAgent = indepenAgent
		}
	}
	if independenceAgent == nil {
		json.Unmarshal([]byte(gameSortEx), &gameSortExMap)
	} else {
		if independenceAgent.Int("IsSelfMinorGameOrder") == 1 {
			json.Unmarshal([]byte(independenceAgent.String("MinorGameOrder")), &gameSortExMap)
		} else {
			json.Unmarshal([]byte(gameSortEx), &gameSortExMap)
		}
	}

	// 定义gameSortExMap中的游戏类型对应的类型
	catGameSortExMap := map[int]string{
		3:  "2",
		6:  "8",
		4:  "5",
		11: "11",
		5:  "4",
		7:  "7",
		8:  "12",
	}

	// 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-德州棋牌
	catMap := map[int]int{
		3:  1, //
		6:  2,
		4:  3,
		11: 4,
		5:  5,
		7:  6,
		8:  7, //德州扑克
	}

	gameListDao := server.DaoxHashGame().XGameList
	gameListDb := gameListDao.WithContext(nil)
	gameChannelListDao := server.DaoxHashGame().XChannelGameList
	gameChannelListDb := gameChannelListDao.WithContext(nil)
	channelHostDao := server.DaoxHashGame().XChannelHost
	channelHostDb := channelHostDao.WithContext(nil)
	LangGameListDao := server.DaoxHashGame().XLangGameList
	LangGameListDb := LangGameListDao.WithContext(nil)
	//LangListDao := server.DaoxHashGame().XLangList
	//LangListDb := LangListDao.WithContext(nil)

	// 获取Host信息
	hostInfos, _ := channelHostDb.Where(channelHostDao.Host.Eq(host)).Where(channelHostDao.State.Eq(1)).First()
	hostId := 0
	if hostInfos != nil {
		hostId = int(hostInfos.ID)
	}
	country := ctx.Gin().Request.Header.Get("country")

	query := gameListDb.Where(gameListDao.OpenState.Eq(1)).Where(gameListDao.State.Eq(1))
	// 获取xchannelgamelist中是否有该hostid的数据
	channelGameListCount, _ := gameChannelListDb.Where(gameChannelListDao.HostID.Eq(int32(hostId))).Count()
	langGameListCount, _ := LangGameListDb.Where(LangGameListDao.LangID.Eq(int32(reqdata.Lang))).Count()
	// 语言状态是否开启
	//LangListInfos, _ := LangListDb.Where(LangListDao.State.Eq(1)).Where(LangListDao.ID.Eq(int32(reqdata.Lang))).First()

	switch {
	case channelGameListCount > 0:
		query.Select(gameListDao.ALL, gameChannelListDao.Sort).Omit(gameListDao.Sort).
			LeftJoin(gameChannelListDao, gameChannelListDao.HostID.Eq(int32(hostId)), gameListDao.GameID.EqCol(gameChannelListDao.GameID), gameListDao.Brand.EqCol(gameChannelListDao.Brand))
	case langGameListCount > 0:
		query.Select(gameListDao.ALL, LangGameListDao.Sort).Omit(gameListDao.Sort).
			LeftJoin(LangGameListDao, LangGameListDao.LangID.Eq(int32(reqdata.Lang)), gameListDao.GameID.EqCol(LangGameListDao.GameID), gameListDao.Brand.EqCol(LangGameListDao.Brand))
	default:
		query.Select(gameListDao.ALL)
	}

	if reqdata.Cat != 0 {
		query.Where(gameListDao.GameType.Eq(int32(catMap[reqdata.Cat])))
	}

	// 特殊处理彩票
	if reqdata.Cat == 11 {
		query.Where(gameListDao.GameSubType.Neq(""))
	}

	// // 特殊处理小游戏
	// if reqdata.Cat == 4 {
	// 	if !funk.Contains(reqdata.GameId, "35") || !funk.Contains(reqdata.GameId, "29") {
	// 		query.Where(gameListDao.GameID.Neq("35"), gameListDao.GameID.Neq("29"))
	// 	}
	// }

	if reqdata.Name != "" {
		query.Where(gameListDb.Where(gameListDao.Name.Like("%" + reqdata.Name + "%")).Or(gameListDao.EName.Like("%" + reqdata.Name + "%")))
	}

	if reqdata.GameId != nil {
		query.Where(gameListDao.GameID.In(reqdata.GameId...))
	} else {
		query.Where(gameListDao.SpecailDisplay.Eq(1))
	}

	if reqdata.IsHot != 0 {
		query.Where(gameListDao.IsHot.Eq(int32(reqdata.IsHot)))
	}

	if reqdata.IsNew != 0 {
		query.Where(gameListDao.IsNew.Eq(int32(reqdata.IsNew)))
	}

	if reqdata.Brand != nil && reqdata.Cat == 0 {
		cats := []int32{}
		catMap := map[string]int{
			"2":  1,
			"4":  5,
			"5":  3,
			"7":  6,
			"8":  2,
			"11": 4,
			"12": 7,
		}

		for i, v := range gameSortExMap {
			if i != "1" {
				for _, vv := range v {
					if vv["id"].(string) == reqdata.Brand[0] && vv["state"].(float64) == 1 {
						cat := catMap[i]
						cats = append(cats, int32(cat))
					}
				}
			}

		}

		if len(cats) > 0 {
			query.Where(gameListDao.GameType.In(cats...))
		}
		query.Where(gameListDao.Brand.In(reqdata.Brand...))
	}

	if reqdata.Brand != nil && reqdata.Cat != 0 {
		brandArr := []string{}
		for _, v := range gameSortExMap[catGameSortExMap[reqdata.Cat]] {
			if v["state"].(float64) == 1 {
				brandArr = append(brandArr, v["id"].(string))
			}
		}
		brandArrTmp := []string{}
		specialBrandArr := []string{}
		specialGameBrands := []string{"pg", "pp", "jili", "spribe"}
		xGameBrand := server.DaoxHashGame().XGameBrand
		// 两个slice中相同的元素
		for _, v := range reqdata.Brand {
			brandInfo, _ := xGameBrand.WithContext(ctx.Gin()).Where(xGameBrand.GameType.Eq(int32(catMap[reqdata.Cat]))).Where(xGameBrand.Brand.Eq(v)).First()
			if strings.Contains(strings.Join(specialGameBrands, ","), v) && !strings.Contains(brandInfo.CountryList, country) {
				specialBrandArr = append(specialBrandArr, v)
			} else if v == "omg" {
				specialBrandArr = []string{"pg", "pp", "jili", "spribe"}
			} else {
				for _, vv := range brandArr {
					if v == vv {
						brandArrTmp = append(brandArrTmp, v)
					}
				}
			}
		}

		if len(brandArrTmp) > 0 && len(specialBrandArr) == 0 {
			query.Where(gameListDao.Brand.In(brandArrTmp...))
		}

		if len(brandArrTmp) == 0 && len(specialBrandArr) > 0 {
			query.Where(gameListDao.SpecialBrand.In(specialBrandArr...))
		}

		if len(brandArrTmp) > 0 && len(specialBrandArr) > 0 {
			query.Where(gameListDb.Where(gameListDao.Brand.In(brandArrTmp...)).Or(gameListDao.SpecialBrand.In(specialBrandArr...)))
		}

		if len(brandArrTmp) == 0 && len(specialBrandArr) == 0 {
			query.Where(gameListDao.Brand.In(""))
		}

	}

	if len(reqdata.Brand) == 0 && reqdata.Cat != 11 {
		brandArr := []string{}
		newBrandArr := []string{}
		specialBrandArr := []string{}
		specialGameBrands := []string{"pg", "pp", "jili", "spribe"}
		xGameBrand := server.DaoxHashGame().XGameBrand
		for _, v := range gameSortExMap[catGameSortExMap[reqdata.Cat]] {
			if v["state"].(float64) == 1 {
				if v["id"].(string) != "omg" {
					brandArr = append(brandArr, v["id"].(string))
				}
			}
		}

		for _, v := range brandArr {
			brandInfo, _ := xGameBrand.WithContext(ctx.Gin()).Where(xGameBrand.GameType.Eq(int32(catMap[reqdata.Cat]))).Where(xGameBrand.Brand.Eq(v)).First()
			if brandInfo != nil {
				if strings.Contains(strings.Join(specialGameBrands, ","), v) && !strings.Contains(brandInfo.CountryList, country) {
					specialBrandArr = append(specialBrandArr, v)
				} else {
					newBrandArr = append(newBrandArr, v)
				}
			}
		}

		if len(newBrandArr) > 0 && len(specialBrandArr) == 0 {
			query.Where(gameListDao.Brand.In(newBrandArr...))
		}

		if len(newBrandArr) == 0 && len(specialBrandArr) > 0 {
			query.Where(gameListDao.SpecialBrand.In(specialBrandArr...))
		}

		if len(newBrandArr) > 0 && len(specialBrandArr) > 0 {
			query.Where(gameListDb.Where(gameListDao.Brand.In(newBrandArr...)).Or(gameListDao.SpecialBrand.In(specialBrandArr...)))
		}

		if len(newBrandArr) == 0 && len(specialBrandArr) == 0 && reqdata.Cat != 0 {
			query.Where(gameListDao.Brand.In(""))
		}

	}

	if len(reqdata.Brand) == 0 && reqdata.Cat == 11 {
		brandArr := []string{}
		for _, v := range gameSortExMap[catGameSortExMap[reqdata.Cat]] {
			if v["state"].(float64) == 1 {
				brandArr = append(brandArr, v["id"].(string))
			}
		}
		query.Where(gameListDao.GameSubType.In(brandArr...))
	}

	if reqdata.GameSubType != nil && reqdata.Cat != 0 {
		query.Where(gameListDao.GameSubType.In(reqdata.GameSubType...))
	}

	// 如果分页为空则默认1
	if reqdata.Page == 0 {
		reqdata.Page = 1
	}

	if reqdata.PageSize == 0 {
		reqdata.PageSize = 15
	}

	limit := 0
	offset := 0
	if reqdata.Limit == nil {
		limit = reqdata.PageSize
		offset = reqdata.PageSize * (reqdata.Page - 1)
	} else {
		limit = *reqdata.Limit
		offset = *reqdata.Offset
	}

	// 三方厂商不支持的IP地区，前端页面不显示该厂商游戏

	logs.Debug("get_game_list country=", country)

	// specialGameBrands := []string{"pg", "pp", "jili", "spribe"}
	// allDisplayGames := []string{}
	// for _, brand := range reqdata.Brand {
	// 	if strings.Contains(strings.Join(specialGameBrands, ","), brand) {
	// 		xGameBrand := server.DaoxHashGame().XGameBrand
	// 		brandInfo, _ := xGameBrand.WithContext(ctx.Gin()).Where(xGameBrand.GameType.Eq(int32(catMap[reqdata.Cat]))).Where(xGameBrand.Brand.Eq(brand)).First()
	// 		if !strings.Contains(brandInfo.CountryList, country) {
	// 			allDisplayGames = append(allDisplayGames, strings.Split(brandInfo.SpecialGames, ",")...)
	// 		}
	// 	}
	// }

	// logs.Debug("allDisplayGames=", allDisplayGames)

	// if len(allDisplayGames) > 0 {
	// 	// query.Where(gameListDao.GameID.In(allDisplayGames...))
	// } else {
	// 	query.Where(gameListDao.CountryList.Like("%" + country + "%"))
	// }

	query.Where(gameListDao.CountryList.Like("%" + country + "%"))

	// if reqdata.Brand != nil && strings.Contains(strings.Join(specialGameBrands, ","), reqdata.Brand[0]) {
	// 	xGameBrand := server.DaoxHashGame().XGameBrand
	// 	brandInfo, _ := xGameBrand.WithContext(ctx.Gin()).Where(xGameBrand.GameType.Eq(int32(catMap[reqdata.Cat]))).Where(xGameBrand.Brand.Eq(reqdata.Brand[0])).First()
	// 	if !strings.Contains(brandInfo.CountryList, country) {
	// 		query.Where(gameListDao.GameID.In(strings.Split(brandInfo.DisplayGames, ",")...))
	// 	} else {
	// 		query.Where(gameListDao.CountryList.Like("%" + country + "%"))
	// 	}
	// } else {
	// 	query.Where(gameListDao.CountryList.Like("%" + country + "%"))
	// }

	// 获取排序配置
	type SortTypeData struct {
		SortType       int32 `json:"sortType"`
		ArtificialType int32 `json:"artificialType"`
	}
	redisSortTypeData := server.CRedis().HGet("CONFIG", "GAME_LIST_SORT_TYPE")
	sortTypeData := &SortTypeData{}
	if redisSortTypeData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisSortTypeData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)
		json.Unmarshal([]byte(dataStr), &sortTypeData)
	}

	switch sortTypeData.SortType {
	case 1:
		if sortTypeData.ArtificialType == 1 {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			}
		} else {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.Sort.Desc(), gameListDao.UserCount.Desc(), gameListDao.ID.Desc())
			}
		}
	case 2:
		if sortTypeData.ArtificialType == 1 {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			}
		} else {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.Sort.Desc(), gameListDao.BetAmount.Desc(), gameListDao.ID.Desc())
			}
		}
	case 3:
		if sortTypeData.ArtificialType == 1 {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			}
		} else {
			switch {
			case channelGameListCount > 0:
				query.Order(gameChannelListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			case langGameListCount > 0:
				query.Order(LangGameListDao.Sort.Desc(), gameListDao.Sort.Desc(), gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			default:
				query.Order(gameListDao.Sort.Desc(), gameListDao.Rtp.Desc(), gameListDao.ID.Desc())
			}
		}
	default:
		query.Order(gameListDao.Sort.Desc(), gameListDao.ID.Desc())
	}

	type ResultData struct {
		model2.XGameList
		OnlineCnt  int
		ShowOnline int
		// OmgShow    bool
	}

	var result []ResultData
	count, err := query.Debug().ScanByPage(&result, offset, limit)

	// 获取在线人数配置
	type SetOnlineCntData struct {
		Display    int32 `json:"display"`    // 在玩人数是否显示
		DefaultMin int32 `json:"defaultMin"` // 为0时默认最小值
		DefaultMax int32 `json:"defaultMax"` // 为0时默认最大值
		Data       []struct {
			GameType int     `json:"gameType"`
			Brand    string  `json:"brand"`
			Multiple float32 `json:"multiple"`
		} `json:"data"`
	}
	var setOnlineCntData SetOnlineCntData
	// 从redis hash list中获取配置数据
	redisData := server.CRedis().HGet("CONFIG", "SET_ONLINE_CNT")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 setOnlineCntData
		if err := json.Unmarshal([]byte(dataStr), &setOnlineCntData); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	// 获取1分钟前的时间
	now := carbon.Now()
	startTime := now.SubMinutes(1).ToDateTimeString()
	endTime := now.ToDateTimeString()

	// 获取两分钟前的时间
	towMinuteStratTime := now.SubMinutes(2).ToDateTimeString()
	// 获取5分钟前的时间
	fiveMinuteStartTime := now.SubMinutes(5).ToDateTimeString()

	// 获取棋牌的在线人数
	type Record struct {
		Cnt int `gorm:"column:cnt"`
	}

	betCnt := 0

	for i, _ := range result {
		result[i].OnlineCnt = 0
		result[i].ShowOnline = 0
		// if len(allDisplayGames) > 0 {
		// 	result[i].OmgShow = true
		// }
		if setOnlineCntData.Display == 1 {
			gameId := result[i].GameID
			brand := result[i].Brand
			gameId, brand = c.changeGame(gameId, brand)

			gameType := result[i].GameType
			result[i].OnlineCnt = 0
			result[i].ShowOnline = 1
			// 获取1分钟前该游戏的投注人数
			if gameType == 1 {
				record := Record{}
				// 处理特殊游戏

				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_dianzhi where GameId = "%v" and Brand = "%v" and CreateTime between "%v" and "%v"`, gameId, brand, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)

				betCnt = record.Cnt
			}

			if gameType == 2 {
				record := Record{}
				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_qipai where GameId = "%v" and Brand = "%v" and CreateTime between "%v" and "%v"`, gameId, brand, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt
			}

			if gameType == 3 {
				record := Record{}
				computeTime := startTime
				if brand == "og" {
					computeTime = towMinuteStratTime
				}

				if brand == "xyx" {
					computeTime = fiveMinuteStartTime
				}

				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_quwei where GameId = "%v" and CreateTime between "%v" and "%v"`, gameId, computeTime, endTime)

				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt
			}

			if gameType == 4 {
				record := Record{}
				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_lottery_pre_order where GameId = "%v" and CreateTime between "%v" and "%v"`, gameId, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt
			}

			if gameType == 5 {
				record := Record{}
				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_live_pre_order where GameId = "%v" and Brand = "%v" and CreateTime between "%v" and "%v"`, gameId, brand, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt
			}

			if gameType == 6 {
				record := Record{}
				sql := fmt.Sprintf(`select count(DISTINCT(UserId)) cnt from x_third_sport_pre_order where Brand = "%v" and CreateTime between "%v" and "%v"`, brand, startTime, endTime)
				server.Db().Gorm().Raw(sql).First(&record)
				betCnt = record.Cnt
			}

			if betCnt > 0 {
				for _, datum := range setOnlineCntData.Data {
					if gameType == int32(datum.GameType) && brand == datum.Brand && datum.Multiple > 0 {
						result[i].OnlineCnt = int(math.Ceil(float64(float32(betCnt) * datum.Multiple)))
					}

					if gameType == int32(datum.GameType) && brand == datum.Brand && datum.Multiple == 0 {
						result[i].OnlineCnt = betCnt
					}
				}
			} else {
				defaultMin := setOnlineCntData.DefaultMin
				defaultMax := setOnlineCntData.DefaultMax
				if result[i].OnlineMin > 0 || result[i].OnlineMax > 0 {
					defaultMin = result[i].OnlineMin
					defaultMax = result[i].OnlineMax
				}

				rand.Seed(time.Now().UnixNano())
				result[i].OnlineCnt = rand.Intn(int(defaultMax-defaultMin+1)) + int(defaultMin)
			}
		}
	}

	if result == nil {
		result = []ResultData{}
	}

	res := map[string]interface{}{
		"list":  result,
		"count": count,
	}
	ctx.RespOK(res)
}

func (c *GameController) transferRecord(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId    int `validate:"required"` //游戏
		ChainType int //1 tron 2 eth 3 bsc
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	// 获取当前时间
	now := time.Now()
	// 获取今天的开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("2006-01-02 15:04:05")
	endtime := time.Now().Format("2006-01-02 15:04:05")

	var resultData struct {
		Trx  interface{}
		Usdt interface{}
		List []model.RecodeData
	}
	record, err := getCountrRecord(token.UserId, reqdata.GameId, "usdt", todayStart, endtime)
	if err != nil {
		logs.Error("transferRecord getCountrRecord", err)
		errmsg := "系统错误,请稍后再试"
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	resultData.Usdt = record
	record, err = getCountrRecord(token.UserId, reqdata.GameId, "trx", todayStart, endtime)
	if err != nil {
		logs.Error("transferRecord getCountrRecord", err)
		errmsg := "系统错误,请稍后再试"
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	resultData.Trx = record
	OrderBy := "Id desc"
	dbtable := server.Db().Gorm().Table("x_order")
	sql := ""
	params := []interface{}{}
	server.Db().AddWhere(&sql, &params, "and", "UserId", "=", token.UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "GameId", "=", reqdata.GameId, 0)
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", ">=", todayStart, "")
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", "<", endtime, "")
	server.Db().AddWhere(&sql, &params, "and", "ChainType", "=", reqdata.ChainType, 1)
	result := []model.RecodeData{}
	dbtable.Where(sql, params...).Limit(200).Order(OrderBy).Find(&result)
	for i := 0; i < len(result); i++ {
		result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
	}
	resultData.List = result
	ctx.RespOK(resultData)
}

func (c *GameController) newTransferRecord(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		OrderId   int
		ChainType int //1 tron 2 eth 3 bsc
		GameId    int `validate:"required"` //游戏
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	OrderBy := "Id desc"
	dbtable := server.Db().Gorm().Table("x_order")
	sql := ""
	params := []interface{}{}
	// 获取当前时间
	now := time.Now()
	// 获取今天的开始时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("2006-01-02 15:04:05")
	server.Db().AddWhere(&sql, &params, "and", "UserId", "=", token.UserId, 0)
	server.Db().AddWhere(&sql, &params, "and", "GameId", "=", reqdata.GameId, 0)
	server.Db().AddWhere(&sql, &params, "and", "Id", ">", reqdata.OrderId, 0)
	server.Db().AddWhere(&sql, &params, "and", "CreateTime", ">=", todayStart, "")
	server.Db().AddWhere(&sql, &params, "and", "ChainType", "=", reqdata.ChainType, 1)
	result := []model.RecodeData{}
	dbtable.Where(sql, params...).Order(OrderBy).Find(&result)
	for i := 0; i < len(result); i++ {
		result[i].CreateTime = abugo.LocalTimeToUtc(result[i].CreateTime)
	}
	ctx.Put("list", result)
	ctx.RespOK()
}

func getCountrRecord(userId, gameId int, symbol, startTime, endTime string) (*[]map[string]interface{}, error) {
	sql := `SELECT
		COUNT(id) AS TotalBetCount,
		IFNULL(SUM(Amount),0) AS Amount,
		IFNULL(SUM(RewardAmount),0) AS RewardAmount,
		SUM(LiuSui) as LiuSui
		FROM x_order WHERE UserId = ? and GameId = ? and Symbol = ? and CreateTime >= ? and CreateTime < ? `
	return server.Db().Query(sql, []interface{}{userId, gameId, symbol, startTime, endTime})
}

func (c *GameController) recentProfit(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		Host     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	rediskey := fmt.Sprintf("%s:%s:recentProfit:%d:%d", server.Project(), server.Module(), SellerId, ChannelId)
	var result model.RecentProfitRes
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		json.Unmarshal(redisdata.([]byte), &result)
	} else {
		winlostConfigTb := server.DaoxHashGame().XTbWinlostConfig
		winlostConfigDb := server.DaoxHashGame().XTbWinlostConfig.WithContext(context.Background())
		winlostConfig, err := winlostConfigDb.Where(winlostConfigTb.SellerID.Eq(int32(SellerId))).
			Where(winlostConfigTb.ChannelID.Eq(int32(ChannelId))).Where(winlostConfigTb.Status.Eq(1)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		if winlostConfig == nil {
			result.List = make([]model.RecentProfit, 0)
			ctx.RespOK(result)
			return
		}
		// 在线人数(假)
		rand.NewSource(time.Now().UnixNano())
		randomNumber := rand.Intn(1000) + 1
		if winlostConfig.MinOnlineUsers > 0 {
			winlostConfig.MinOnlineUsers += int32(randomNumber)
		}
		country := ctx.Gin().Request.Header.Get("country")
		var recentProfitList []model.RecentProfit
		raw := server.Db().Gorm().Raw("CALL PlayManage_x_order_winlost_GetLastList(?,?,?,?,?,?,?,?)",
			SellerId, ChannelId, winlostConfig.GameType, winlostConfig.IntervalTime, winlostConfig.DisplayRows,
			winlostConfig.IsUserDistinct, winlostConfig.MinRewardAmount, country).
			Scan(&recentProfitList)
		err = raw.Error
		if err != nil {
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		result.State = 1
		result.OnlineUsers = winlostConfig.MinOnlineUsers
		result.List = recentProfitList
		server.Redis().SetEx(rediskey, 15, result)
	}
	ctx.RespOK(result)
}

func (c *GameController) getChainList(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Host      string
		GameType  int
		ChainType int
		Page      int `validate:"required"`
		PageSize  int `validate:"required"`
		AgentCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	agenId := server.GetAgentIndependenceUserId(ctx, reqdata.Host, reqdata.AgentCode)
	gameTb := server.DaoxHashGame().XGame
	gameDb := server.DaoxHashGame().XGame.WithContext(context.Background())
	if agenId > 0 {
		gameDb = gameDb.Where(gameTb.TopAgentID.Eq(int32(agenId)))
	}
	leng := 1
	if reqdata.GameType == 1 {
		gameDb = gameDb.Where(gameTb.GameID.In(utils.GameTypeHaXi...))
	} else if reqdata.GameType == 2 {
		gameDb = gameDb.Where(gameTb.GameID.In(utils.GameTypeOneMinuteHaXi...))
	} else if reqdata.GameType == 3 {
		gameDb = gameDb.Where(gameTb.GameID.In(utils.GameTypeThreeMinuteHaXi...))
	} else if reqdata.GameType == 0 {
		leng = 3
	}
	gameList, err := gameDb.Select(gameTb.GameID.Distinct().As(gameTb.GameID.ColumnName().String())).
		Where(gameTb.SellerID.Eq(int32(SellerId))).
		Where(gameTb.ChannelID.Eq(int32(ChannelId))).Where(gameTb.State.Eq(1)).Find()
	if err != nil {
		logs.Error("getList find game list err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	var conditionGameId = make([]int32, 0, len(gameList))
	res := make([]model.GetChainGoods, 0, len(gameList)*leng)
	for _, game := range gameList {
		_, ok := utils.GameTypeHaXiMap[game.GameID]
		if ok {
			res = append(res, getChainGoodsData(reqdata.ChainType, game.GameID)...)
			continue
		}
		_, ok = utils.GameTypeMinuteHaXiMap[game.GameID]
		if ok {
			conditionGameId = append(conditionGameId, game.GameID)
			continue
		}
	}
	if len(conditionGameId) > 0 {
		gamePeriodList := make([]model.GetChainGoods, 0)
		t := carbon.Parse(carbon.Now().String()).StdTime()
		s, _ := time.ParseDuration(fmt.Sprintf("-%ds", utils.BlockTimeSecond))
		date := t.Add(s)
		err = server.Db().Gorm().Raw("SELECT B.ChainType,B.GameId,B.Period,B.State FROM ( "+
			"SELECT ChainType,GameId,MIN(Period) AS Period FROM x_game_period  WHERE GameId IN (?) AND BlockTime >= ? GROUP BY GameId,ChainType ) AS A "+
			"INNER JOIN x_game_period AS B ON A.ChainType=B.ChainType AND A.GameId=B.GameId AND A.Period=B.Period",
			conditionGameId, date.Format(utils.TimeFormatStr)).
			Scan(&gamePeriodList).Error
		if err != nil {
			logs.Error("getList find game Period err", err)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		for _, gamePeriod := range gamePeriodList {
			res = append(res, model.GetChainGoods{
				GameId:    gamePeriod.GameId,
				ChainType: gamePeriod.ChainType,
				State:     gamePeriod.State,
				Period:    gamePeriod.Period,
			})
		}
	}
	pageRes := make([]model.GetChainGoods, 0)
	if len(res) > 0 {
		pageRes = paginate(res, reqdata.Page, reqdata.PageSize)
		if len(pageRes) > 0 {
			orderTb := server.DaoxHashGame().XOrder
			orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())
			conds := []field.Expr{
				orderTb.ID.As("Id"), orderTb.GameID.As("GameId"), orderTb.Memo, orderTb.RewardAmount, orderTb.CreateTime, orderTb.BlockHash, orderTb.BlockNum,
				orderTb.Symbol, orderTb.Amount, orderTb.IsWin, orderTb.State, orderTb.Period, orderTb.NextBlockHash, orderTb.BetArea, orderTb.OpenArea,
			}
			for index, v := range pageRes {
				order, err := orderDb.Select(conds...).
					Where(orderTb.GameID.Eq(v.GameId)).
					Where(orderTb.ChainType.Eq(int32(v.ChainType))).Order(orderTb.CreateTime.Desc()).Limit(15).Find()
				if err != nil {
					logs.Error("getList find order Period gameID: %v ChainType: %v err", v.GameId, v.ChainType, err)
					ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
					return
				}
				pageRes[index].OrderList = order
			}
		}
		if pageRes == nil {
			pageRes = make([]model.GetChainGoods, 0)
		}
	}
	ctx.Put("list", pageRes)
	ctx.RespOK()
}

func getChainGoodsData(chainType int, gameId int32) []model.GetChainGoods {
	if chainType > 0 {
		return []model.GetChainGoods{{
			GameId:    gameId,
			State:     1,
			ChainType: chainType,
		}}
	}
	loop := 3
	res := make([]model.GetChainGoods, 0, loop)
	for i := 1; i < loop; i++ {
		res = append(res, model.GetChainGoods{
			GameId:    gameId,
			State:     1,
			ChainType: i,
			Period:    "0",
		})
	}
	return res
}

// 分页函数
func paginate(items []model.GetChainGoods, page int, pageSize int) []model.GetChainGoods {
	// 检查页码是否合法
	if page <= 0 || page > (len(items)+pageSize-1)/pageSize {
		return nil // 返回空切片
	}

	// 检查页面大小是否合法
	if pageSize <= 0 {
		return nil // 返回空切片
	}

	// 计算起始索引
	startIndex := (page - 1) * pageSize

	// 计算结束索引
	endIndex := startIndex + pageSize

	// 处理 endIndex 超出范围
	if endIndex > len(items) {
		endIndex = len(items)
	}

	// 获取分页后的切片
	return items[startIndex:endIndex]
}

func (c *GameController) changeGame(gameId string, brand string) (string, string) {
	pgToGfg := map[string]string{
		"126": "801",
		"74":  "803",
		"98":  "804",
		"104": "802",
	}

	pgToOmg := map[string]string{
		"1682240": "1682240",
		"1695365": "1695365",
		"39":      "39",
		"40":      "40",
		"42":      "42",
		"48":      "48",
		"54":      "54",
		"68":      "68",
		"84":      "84",
		"89":      "89",
		"135":     "135",
		"1543462": "1543462",
		"1580541": "1580541",
	}

	spribeToOmg := map[string]string{
		"22005": "9",
		"22001": "100001",
	}

	if v, ok := pgToGfg[gameId]; ok && brand == "pg" {
		return v, "gfg"
	}

	if v, ok := pgToOmg[gameId]; ok && brand == "pg" {
		return v, "omg"
	}

	if v, ok := spribeToOmg[gameId]; ok && brand == "spribe" {
		return v, "omg"
	}

	return gameId, brand

}

func (c *GameController) GetGameSortEx(host string, lang int) string {
	xChannelHostDao := server.DaoxHashGame().XChannelHost
	xLangListDao := server.DaoxHashGame().XLangList

	// 获取 xChannelHost 和 xLangList 的 GameSortEx 字段值
	xChannelHostInfo, _ := xChannelHostDao.WithContext(nil).
		Select(xChannelHostDao.GameSortEx).
		Where(xChannelHostDao.Host.Eq(host)).
		First()

	xLangListInfo, _ := xLangListDao.WithContext(nil).
		Select(xLangListDao.GameSortEx).
		Where(xLangListDao.ID.Eq(int32(lang))).
		First()

	defaultGameSortEx := `{"1":[{"id":2,"name":"哈希单双","state":1},{"id":1,"name":"哈希大小","state":1},{"id":5,"name":"哈希牛牛","state":1},{"id":12,"name":"和值单双","state":1},{"id":11,"name":"和值大小","state":1},{"id":4,"name":"幸运庄闲","state":1},{"id":102,"name":"一分单双","state":1},{"id":101,"name":"一分大小","state":1},{"id":105,"name":"一分牛牛","state":1},{"id":131,"name":"三分大小","state":1},{"id":132,"name":"三分单双","state":1},{"id":133,"name":"三分幸运","state":1},{"id":134,"name":"三分庄闲","state":1},{"id":135,"name":"三分牛牛","state":1},{"id":3,"name":"幸运哈希","state":1},{"id":103,"name":"一分幸运","state":1},{"id":104,"name":"一分庄闲","state":1},{"id":6,"name":"哈希快三","state":2},{"id":7,"name":"哈希PK10","state":2}],"2":[{"id":"gfg","name":"gfg电子","state":1},{"id":"pg","name":"pg电子","state":1},{"id":"pp","name":"pp电子","state":1},{"id":"ont","name":"OneTouch Generic","state":2},{"id":"klb","name":"Kalamba","state":2},{"id":"hsg","name":"Hacksaw Gaming","state":2},{"id":"nlc","name":"Nolimit City","state":2},{"id":"avx","name":"AvatarUX","state":2}],"4":[{"id":"wm","name":"wm真人","state":1},{"id":"ag","name":"ag真人","state":1},{"id":"evo","name":"evo真人","state":1},{"id":"astar","name":"astar真人","state":1}],"5":[{"id":"spribe","name":"spribe","state":1},{"id":"xyx","name":"xyx","state":1},{"id":"og","name":"OG","state":1}],"7":[{"id":"up","name":"up体育","state":1},{"id":"easybet","name":"easybet体育","state":1},{"id":"three_up","name":"三昇体育","state":1},{"id":"btsg","name":"Betsy体育","state":1}],"8":[{"id":"ont","name":"OneTouch Generic","state":1},{"id":"ontt","name":"OneTouch Table game","state":1},{"id":"gfg","name":"gfg棋牌","state":1}],"11":[{"id":"bpc","name":"低频彩","state":1},{"id":"ssc","name":"时时彩","state":1},{"id":"ttjsc","name":"天天极速彩","state":1},{"id":"pk10","name":"PK拾","state":1},{"id":"ks","name":"快三","state":1}]}`

	// 根据条件返回 GameSortEx 的值
	// switch {
	// case xChannelHostInfo == nil && xLangListInfo == nil:
	// 	return defaultGameSortEx
	// case xLangListInfo != nil && xLangListInfo.GameSortEx != "":
	// 	return xLangListInfo.GameSortEx
	// case xChannelHostInfo != nil && xChannelHostInfo.GameSortEx != "":
	// 	return xChannelHostInfo.GameSortEx
	// default:
	// 	return defaultGameSortEx
	// }

	if xChannelHostInfo != nil && xChannelHostInfo.GameSortEx != "" {
		return xChannelHostInfo.GameSortEx
	}

	if xLangListInfo != nil && xLangListInfo.GameSortEx != "" {
		return xLangListInfo.GameSortEx
	}

	return defaultGameSortEx
}

// 转换体育注单时间为毫秒时间戳
func convertSportTime(timeStr string, brand string) string {
	if timeStr == "" {
		return ""
	}

	var t time.Time
	var err error

	switch brand {
	case "up": // UP体育，数据库时间是 GMT+02
		loc := time.FixedZone("GMT+2", 2*3600)
		t, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	case "saba", "three_up": // 沙巴体育和三升体育，数据库时间是 GMT-04
		loc := time.FixedZone("GMT-4", -4*3600)
		t, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	case "cbk": // 板球，数据库时间是 UTC0
		t, err = time.Parse("2006-01-02 15:04:05", timeStr) // UTC0
	case "wickets", "fb", "easybetsp": // 这些平台，数据库时间是 GMT+08
		loc := time.FixedZone("GMT+8", 8*3600)
		t, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	default:
		loc := time.FixedZone("GMT+8", 8*3600)
		t, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	}

	if err != nil {
		return timeStr
	}

	// 转换为毫秒时间戳
	return fmt.Sprintf("%d", t.UnixMilli())
}
