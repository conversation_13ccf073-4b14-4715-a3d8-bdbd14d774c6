// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXBonusTaskUser = "x_bonus_task_user"

// XBonusTaskUser mapped from table <x_bonus_task_user>
type XBonusTaskUser struct {
	ID         int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID     int32     `gorm:"column:UserId;not null" json:"UserId"`
	Type       int32     `gorm:"column:Type;not null" json:"Type"`
	Bonus      float64   `gorm:"column:Bonus;not null" json:"Bonus"`
	LiuShuiOdd int32     `gorm:"column:LiuShuiOdd;not null" json:"LiuShuiOdd"`
	Memo       string    `gorm:"column:Memo" json:"Memo"`
	State      int32     `gorm:"column:State;not null;default:1;comment:1 开启 2关闭 3倒计时 4结束" json:"State"` // 1 开启 2关闭 3倒计时 4结束
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
	Condition1 float64   `gorm:"column:Condition1" json:"Condition1"`
	Condition2 float64   `gorm:"column:Condition2" json:"Condition2"`
	Condition3 float64   `gorm:"column:Condition3" json:"Condition3"`
	Progress1  float64   `gorm:"column:Progress1;not null;default:0.000000" json:"Progress1"`
	Progress2  float64   `gorm:"column:Progress2;not null;default:0.000000" json:"Progress2"`
	Progress3  float64   `gorm:"column:Progress3;not null;default:0.000000" json:"Progress3"`
	TimeLine   time.Time `gorm:"column:TimeLine;not null;default:CURRENT_TIMESTAMP" json:"TimeLine"`
}

// TableName XBonusTaskUser's table name
func (*XBonusTaskUser) TableName() string {
	return TableNameXBonusTaskUser
}
