package paycontroller

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var RichWay = new(richWay)

type richWay struct {
	Base
}

// RichWayMethodConfig 三方配置
type RichWayMethodConfig struct {
	AppID string `json:"app_id"` // 三方ID
	Key   string `json:"key"`    // 三方密钥
	Url   string `json:"url"`    // 三方地址
	Cburl string `json:"cburl"`  // 回调地址
}

type RichWayRechargeRequest struct {
	Amount          string `json:"amount"`          // 金额
	NotifyUrl       string `json:"notifyUrl"`       // 回调地址
	CustomerPhone   string `json:"customerPhone"`   // 手机号
	CustomerName    string `json:"customerName"`    // 姓名
	CustomerEmail   string `json:"customerEmail"`   // 邮箱
	MerchantOrderId string `json:"merchantOrderId"` // 商户订单号
	PayMethod       string `json:"payMethod"`       // 支付方式 （PH_GCASH_URL   PH_QRPH_DYNAMIC）
}

type RichWayRechargeResponse struct {
	Code            int    `json:"code"`            // 返回码
	Msg             string `json:"msg"`             // 返回信息
	Status          string `json:"status"`          // 返回状态
	PayURL          string `json:"payUrl"`          // 返回地址
	Amount          string `json:"amount"`          // 金额
	Fee             string `json:"fee"`             // 手续费
	CreateTimeL     int64  `json:"createTimeL"`     // 创建时间
	OrderID         string `json:"orderId"`         // 订单号
	MerchantOrderID string `json:"merchantOrderId"` // 商户订单号
	PayType         string `json:"payType"`         // 支付方式
	Sign            string `json:"sign"`            // 签名
	TraceID         string `json:"traceId"`         // 跟踪ID
	CustomerName    string `json:"customerName"`    // 姓名
	CustomerEmail   string `json:"customerEmail"`   // 邮箱
	CustomerPhone   string `json:"customerPhone"`   // 手机号
}

type RichWayCallbackRequest struct {
	AccountNum      string `json:"accountNum"`      // 银行卡号
	Amount          string `json:"amount"`          // 金额
	Code            int    `json:"code"`            // 返回码
	CreateTimeL     int64  `json:"createTimeL"`     // 创建时间
	CustomerBank    string `json:"customerBank"`    // 开户行
	CustomerName    string `json:"customerName"`    // 姓名
	EndToEndID      string `json:"endToEndId"`      // 结束订单号
	Fee             string `json:"fee"`             // 手续费
	MerchantOrderID string `json:"merchantOrderId"` // 商户订单号
	Msg             string `json:"msg"`             // 返回信息
	OrderID         string `json:"orderId"`         // 订单号
	PayType         string `json:"payType"`         // 支付方式
	Sign            string `json:"sign"`            // 签名
	Status          string `json:"status"`          // 返回状态
	TraceID         string `json:"traceId"`         // 跟踪ID
}

func (c *richWay) Init() {
	server.Http().PostNoAuth("/api/richway/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/richway/withdraw/callback", c.withdrawCallback)
}

// Recharge 充值订单
func (c *richWay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	// 获取请求参数
	errcode := 0
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	cfg := RichWayMethodConfig{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 新增订单
	token := server.GetToken(ctx)

	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getWithdrawRate(req.Symbol) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	// 创建订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      14,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		// 向客服系统推送消息
		// c.PushDepositCustomerIO(4, user, rechargeOrder, payMethod)
		return
	}
	cfg.Cburl = fmt.Sprintf("%s/api/richway/recharge/callback", cfg.Cburl)
	//发起支付请求
	params := RichWayRechargeRequest{
		Amount:          strconv.FormatFloat(rechargeOrder.Amount*100, 'f', 0, 64),
		NotifyUrl:       cfg.Cburl,
		CustomerPhone:   user.PhoneNum,
		CustomerName:    user.RealName,
		CustomerEmail:   user.Email,
		MerchantOrderId: strconv.Itoa(int(rechargeOrder.ID)),
		PayMethod:       payMethod.PayType,
	}
	jsonData, _ := json.Marshal(&params) //json序列化
	authorization := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", cfg.AppID, cfg.Key)))
	logs.Info("RichWay请求参数:", string(jsonData))

	client := &http.Client{}                                                                                                   //创建客户端
	httpreq, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/open-api/pay/payment", cfg.Url), bytes.NewBuffer(jsonData)) // 创建请求
	httpreq.Header.Set("Content-Type", "application/json")                                                                     // 设置请求头
	httpreq.Header.Set("Authorization", "Basic "+authorization)
	var httpres *http.Response
	httpres, err = client.Do(httpreq)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("发起支付请求失败"), &errcode)
		return
	}
	response := RichWayRechargeResponse{} //定义接收结构体
	var respBytes []byte
	defer httpres.Body.Close()
	// 读取响应
	if respBytes, err = io.ReadAll(httpres.Body); err != nil {
		logs.Error("RichWay读取响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		tx.Rollback()
		return
	}

	logs.Info("RichWay响应消息体:", string(respBytes))
	// 解析响应
	if err = json.Unmarshal(respBytes, &response); err != nil {
		logs.Error("RichWay解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		tx.Rollback()
		return
	}
	// 返回响应
	if response.Code != 200 {
		tx.Rollback()
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return
	}

	tx.Commit()
	ctx.RespOK(xgo.H{
		"payurl": response.PayURL,
	})
}

// RechargeCallback 充值回调
func (c *richWay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 获取请求参数
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("RichWay回调数据:", string(body))
	cb := RichWayCallbackRequest{}
	json.Unmarshal(body, &cb)
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(cb.MerchantOrderID)
	order, err := c.getRechargeOrder(orderNo)
	if err != nil {
		logs.Info("RichWay回调获取订单失败", err)
		ctx.Gin().String(200, "订单号不存在")
		return
	}

	// 是否成功订单
	if order.State == 5 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	amount, err := strconv.ParseFloat(cb.Amount, 64)
	amount = amount / 100
	if err != nil {
		logs.Info("RichWay回调金额转换错误", err)
		ctx.Gin().String(200, "金额转换错误")
		return
	}
	if math.Abs((order.Amount-amount)/100) > 0.01 {
		logs.Info("RichWay回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}
	// 验证支付方式
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	cfg := RichWayMethodConfig{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 发起支付请求,查询订单
	authorization := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", cfg.AppID, cfg.Key)))
	apiurl := fmt.Sprintf("%s/open-api/pay/query?orderId=%s&merchantOrderId=%s", cfg.Url, cb.OrderID, cb.MerchantOrderID)
	client := &http.Client{}                                   //创建客户端
	httpreq, _ := http.NewRequest(http.MethodGet, apiurl, nil) // 创建请求
	httpreq.Header.Set("Content-Type", "application/json")     // 设置请求头
	httpreq.Header.Set("Authorization", "Basic "+authorization)
	httpres, err := client.Do(httpreq)
	if err != nil {
		ctx.Gin().String(200, "三方订单不存在")
		return
	}
	defer httpres.Body.Close()

	// 更新三方订单号
	err = c.updateThirdOrder(order.ID, cb.OrderID)
	if err != nil {
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

	ctx.Gin().String(200, "success")
}

// withdrawCallback 提现下发回调
func (c *richWay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("RichWay提现回调数据:", string(body))

	callbackReq := RichWayCallbackRequest{}
	json.Unmarshal(body, &callbackReq)
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(callbackReq.MerchantOrderID)
	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		ctx.Gin().String(200, "订单号不存在")
		return
	}
	// 是否成功订单
	if order.State == 6 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	amount, err := strconv.ParseFloat(callbackReq.Amount, 64)
	amount = amount / 100
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("RichWay提现回调金额转换错误")
		ctx.Gin().String(200, "下发金额转换错误")
		return
	}

	if math.Abs((order.RealAmount-amount)/100) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("RichWay提现回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证签名
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("RichWay提现回调支付方式不存在")
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	cfg := PayMethodCfg{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("RichWay提现回调支付配置解析失败")
		ctx.Gin().String(200, "支付配置错误")
		return
	}
	switch callbackReq.Status {
	case "00":
		c.withdrawCallbackHandel(int(order.ID), 5)
		ctx.Gin().String(200, "success")
		break
	case "01":
		c.withdrawCallbackHandel(int(order.ID), 6)
		ctx.Gin().String(200, "success")
		break
	case "02", "03":
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, "success")
		break
	}
}
