package controller

import (
	"math"
	"sort"
	"time"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"
)

type RecordController struct {
}

func (c *RecordController) Init() {
	gropu := server.Http().NewGroup("/api/rec")
	{
		gropu.Post("/recharge", c.recharge)
		gropu.Post("/withdraw", c.withdraw)
		gropu.Post("/transfer", c.transfer)
		gropu.Post("/caijin", c.caijin)
		gropu.Post("/winloss_report", c.winloss_report)
	}
}

func (c *RecordController) recharge(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Page      int
		PageSize  int
		StartTime int64 //毫秒时间戳 >=StartTime
		EndTime   int64 //毫秒时间戳 <=EndTime
		Host      string
		State     int //状态：1未找到玩家 2小于最低充值额 5充值成功
		Id        int
		Symbol    string //币种：USDT, TRX 等
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	type RecRechargeDataTmp struct {
		Id         interface{}
		UserId     interface{} //用户ID
		Amount     interface{} //金额
		Symbol     interface{} //usdt trx
		RealAmount interface{} //实际到账usdt
		CreateTime interface{} //充值时间
		State      interface{} //1未找到玩家 2小于最低充值额 5充值成功
		PayType    interface{} //支付类型 1 链上充值 2.pix
		TxId       interface{} // 交易哈希
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", token.SellerId, 0)
	where.Add("and", "UserId", "= ", token.UserId, "")
	where.Add("and", "Id", "= ", reqdata.Id, 0)
	where.Add("and", "State", "= ", reqdata.State, 0)
	if reqdata.Symbol != "" {
		where.Add("and", "Symbol", "= ", reqdata.Symbol, "")
	}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	total, presult := server.Db().Table("x_recharge").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	data := make([]RecRechargeDataTmp, 0, total)
	if presult != nil {
		for i := 0; i < len(*presult); i++ {
			(*presult)[i]["CreateTime"] = abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*presult)[i]["CreateTime"])) * 1000
			data = append(data, RecRechargeDataTmp{
				Id:         (*presult)[i]["Id"],
				UserId:     (*presult)[i]["UserId"],
				Amount:     (*presult)[i]["Amount"],
				RealAmount: (*presult)[i]["RealAmount"],
				Symbol:     (*presult)[i]["Symbol"],
				CreateTime: (*presult)[i]["CreateTime"],
				State:      (*presult)[i]["State"],
				PayType:    (*presult)[i]["PayType"],
				TxId:       (*presult)[i]["TxId"],
			})
		}
	}
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *RecordController) withdraw(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Page      int
		PageSize  int
		StartTime int64 //毫秒时间戳 >=StartTime
		EndTime   int64 //毫秒时间戳 <=EndTime
		Host      string
		State     int    //状态：0待审核 1审核拒绝 2审核通过 4已发放 5正在出款 6出款完成
		Symbol    string //币种：USDT, TRX 等
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	type RecWithdrawDataTmp struct {
		Id         interface{}
		UserId     interface{} //用户ID
		Amount     interface{} //金额
		Symbol     interface{} //usdt trx
		Address    interface{} //提现地址
		CreateTime interface{} //提现时间
		State      interface{} //0 待审核 1审核拒绝 2审核通过 4已发放 5正在出款 6出款完成
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", token.SellerId, 0)
	where.Add("and", "UserId", "= ", token.UserId, "")
	where.Add("and", "State", "= ", reqdata.State, 0)
	if reqdata.Symbol != "" {
		where.Add("and", "Symbol", "= ", reqdata.Symbol, "")
	}
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	total, presult := server.Db().Table("x_withdraw").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	data := make([]RecWithdrawDataTmp, 0, total)
	if presult != nil {
		for i := 0; i < len(*presult); i++ {
			(*presult)[i]["CreateTime"] = abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*presult)[i]["CreateTime"])) * 1000
			data = append(data, RecWithdrawDataTmp{
				Id:         (*presult)[i]["Id"],
				UserId:     (*presult)[i]["UserId"],
				Amount:     (*presult)[i]["Amount"],
				Symbol:     (*presult)[i]["Symbol"],
				Address:    (*presult)[i]["Address"],
				CreateTime: (*presult)[i]["CreateTime"],
				State:      (*presult)[i]["State"],
			})
		}
	}
	ctx.Put("data", presult)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *RecordController) transfer(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int //`validate:"required"` //运营商
		Page      int
		PageSize  int
		StartTime int64 //毫秒时间戳 >=StartTime
		EndTime   int64 //毫秒时间戳 <=EndTime
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	//ChannelId := server.GetChannel(ctx)
	where := abugo.AbuDbWhere{}
	//where.Add("and", "SellerId", "= ", reqdata.SellerId, 0)
	//where.Add("and", "ChannelId", "= ", ChannelId, 0)
	where.Add("and", "UserId", "= ", token.UserId, "")
	where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
	where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
	total, data := server.Db().Table("x_amount_sync").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if data != nil {
		for i := 0; i < len(*data); i++ {
			(*data)[i]["CreateTime"] = abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*data)[i]["CreateTime"])) * 1000
		}
	}
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

// CaiJinDataAbs.Ctype彩金类型
// 1 人工存入(后台增资)
// 2 vip日返水
// 3 vip升级礼金
// 4 vip月礼金
// 5 代理佣金
// 6 充值任务
// 7 哈希闯关
// 8 棋牌闯关
// 9 电子闯关
// 10 救援金
// 11 邀请好友
// 12 降龙伏虎
// 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
// 能量补给站返TRX是直接返到玩家冷钱包的就不加了,不显示在红利里面了
const (
	CaiJinTypeAddAmount            int = 1
	CaiJinTypeVipFanShui           int = 2
	CaiJinTypeVipLiJinUpgrade      int = 3
	CaiJinTypeVipLiJinMonth        int = 4
	CaiJinTypeVipLiJinWeek         int = 13
	CaiJinTypeAgentCommission      int = 5
	CaiJinTypeActiveRecharge       int = 6
	CaiJinTypeActiveHaXiBreakout   int = 7
	CaiJinTypeActiveQipaiBreakout  int = 8
	CaiJinTypeActiveDianziBreakout int = 9
	CaiJinTypeActiveRescue         int = 10
	CaiJinTypeActiveInvite         int = 11
	CaiJinTypeActiveXianglongfuhu  int = 12
)

func (c *RecordController) caijin(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Page      int
		PageSize  int
		StartTime int64  //毫秒时间戳 >=StartTime
		EndTime   int64  //毫秒时间戳 <=EndTime
		Symbol    string //币种：USDT, TRX 等
		State     int    //状态：1待发放 2已发放
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	type RecCaiJinDataAbs struct {
		Id         interface{} //int
		UserId     interface{} //int     //用户ID
		Ctype      interface{} //int     //彩金类型
		Amount     interface{} //float64 //金额
		Symbol     interface{} //string  //usdt trx
		CreateTime int64       //充值时间
		State      interface{} //int     //状态 1待发放 2已发放
	}
	token := server.GetToken(ctx)
	data := make([]RecCaiJinDataAbs, 0)
	total := int64(0)
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "= ", token.UserId, "")
		where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(reqdata.StartTime), "")
		where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(reqdata.EndTime), "")
		where.Add("and", "Amount", ">", 0, "") //只显示增资添加类型，扣费类型不显示
		if reqdata.Symbol != "" {
			where.Add("and", "Symbol", "= ", reqdata.Symbol, "")
		}
		where.Add("and", "Reason", "in", "(4,5,17,18,19,25,26,27,28,29,30,31,68,201,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,52,53,54,55,56,57,58,59,60,61,64,65,67)", "")
		totalTmp, dataTmp := server.Db().Table("x_amount_change_log").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
		total += totalTmp
		if dataTmp != nil {
			for i := 0; i < len(*dataTmp); i++ {
				state := 2 //默认已发放状态
				if reqdata.State > 0 {
					state = reqdata.State
				}
				symbol := "usdt"
				if reqdata.Symbol != "" {
					symbol = reqdata.Symbol
				}
				data = append(data, RecCaiJinDataAbs{
					Id:         (*dataTmp)[i]["Id"],
					UserId:     (*dataTmp)[i]["UserId"],
					Ctype:      (*dataTmp)[i]["Reason"],
					Amount:     (*dataTmp)[i]["Amount"],
					Symbol:     symbol,
					CreateTime: abugo.LocalTimeToTimeStamp(abugo.GetStringFromInterface((*dataTmp)[i]["CreateTime"])) * 1000,
					State:      state,
				})
			}
		}
	}
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *RecordController) winloss_report(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId  int `validate:"required"` //运营商
		Symbol    string
		StartTime int64 // 前端时区的毫秒时间戳
		EndTime   int64 // 前端时区的毫秒时间戳
		TimeZone  int   //客户当前所在时区分钟偏移量, javascript的Date().getTimezoneOffset()
		Days      int   //0 1 7 30
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if ctx.RespErrString(reqdata.Symbol != "usdt" && reqdata.Symbol != "trx", &errcode, "币种不支持") {
		return
	}

	type RecWinLossDataTmp struct {
		RecordDate string  //日期格式'2023-06-22'
		BetCount   int     //投注笔数
		BetAmount  float64 //有效投注
		WinLoss    float64 //输赢
	}
	totalBetCount := int(0)
	totalWinLoss := float64(0)
	timeZone := reqdata.TimeZone
	if timeZone == 0 {
		// 如果前端没传时区默认是 北京 UTC+8，所以是 -480
		timeZone = -480
	}
	// 1. 生成日期记录
	startTimeBeijing := utils.ConvertToBeijingTime(reqdata.StartTime, timeZone)
	endTimeBeijing := utils.ConvertToBeijingTime(reqdata.EndTime, timeZone)

	// 计算日期范围
	startDate := time.Unix(startTimeBeijing/1000, 0)
	endDate := time.Unix(endTimeBeijing/1000, 0)

	// 使用北京时区
	beijingLoc := time.FixedZone("Asia/Shanghai", 8*60*60)

	// 获取开始日期和结束日期的自然日
	startDay := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, beijingLoc)
	endDay := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 0, 0, 0, 0, beijingLoc)
	days := int(math.Ceil(endDay.Sub(startDay).Hours() / 24))

	// 生成日期记录
	data := make([]*RecWinLossDataTmp, 0)
	for i := 0; i <= days; i++ {
		d := RecWinLossDataTmp{}
		currentDay := startDay.AddDate(0, 0, i)
		d.RecordDate = currentDay.Format("2006-01-02")
		data = append(data, &d)
	}

	// 2. 查询每天数据
	for i := 0; i < len(data); i++ {
		var StartTime, EndTime int64
		currentDate := startDay.AddDate(0, 0, i)

		if i == 0 {
			// 第一天：使用实际开始时间到当天23:59:59
			StartTime = startTimeBeijing
			EndTime = time.Date(
				currentDate.Year(),
				currentDate.Month(),
				currentDate.Day(),
				23, 59, 59, 999999999,
				beijingLoc,
			).Unix() * 1000
		} else if i == len(data)-1 {
			// 最后一天：使用当天00:00:00到实际结束时间
			StartTime = time.Date(
				currentDate.Year(),
				currentDate.Month(),
				currentDate.Day(),
				0, 0, 0, 0,
				beijingLoc,
			).Unix() * 1000
			EndTime = endTimeBeijing
		} else {
			// 中间的天：使用当天00:00:00到23:59:59
			StartTime = time.Date(
				currentDate.Year(),
				currentDate.Month(),
				currentDate.Day(),
				0, 0, 0, 0,
				beijingLoc,
			).Unix() * 1000
			EndTime = time.Date(
				currentDate.Year(),
				currentDate.Month(),
				currentDate.Day(),
				23, 59, 59, 999999999,
				beijingLoc,
			).Unix() * 1000
		}

		//logs.Info("-- 统计报表开始日期：", abugo.TimeStampToLocalTime(StartTime), "结束日期：", abugo.TimeStampToLocalTime(EndTime))

		{ //统计哈希
			{
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "Symbol", "= ", reqdata.Symbol, "")
				where.Add("and", "CreateTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "CreateTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(Amount),0) as Amount,IFNULL(sum(RewardAmount),0) as RewardAmount,IFNULL(sum(LiuSui),0) as LiuSui`
				presult, _ := server.Db().Table("x_order").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
		}
		if reqdata.Symbol == "usdt" {
			{ //哈希彩票
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "State", "=", 1, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_lottery").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //棋牌
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_qipai").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //电子
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_dianzhi").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //小游戏
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_quwei").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //真人
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_live").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //体育
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "BetLocalTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "BetLocalTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_sport").Select(sel).Where(where).GetList()

				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
			{ //德州
				where := abugo.AbuDbWhere{}
				where.Add("and", "UserId", "= ", token.UserId, "")
				where.Add("and", "ThirdTime", ">=", abugo.TimeStampToLocalTime(StartTime), "")
				where.Add("and", "ThirdTime", "<=", abugo.TimeStampToLocalTime(EndTime), "")
				sel := `Count(Id) as Count,IFNULL(sum(BetAmount),0) as Amount,IFNULL(sum(WinAmount),0) as RewardAmount,IFNULL(sum(ValidBet),0) as LiuSui`
				presult, _ := server.Db().Table("x_third_texas").Select(sel).Where(where).GetList()
				if presult != nil {
					data[i].BetAmount += abugo.GetFloat64FromInterface((*presult)[0]["LiuSui"])
					data[i].WinLoss += abugo.GetFloat64FromInterface((*presult)[0]["RewardAmount"]) - abugo.GetFloat64FromInterface((*presult)[0]["Amount"])
					data[i].BetCount += int(abugo.GetInt64FromInterface((*presult)[0]["Count"]))
				}
			}
		}
	}

	// 转换日期回用户时区
	for i := 0; i < len(data); i++ {
		data[i].RecordDate = utils.ConvertToUserTimezone(data[i].RecordDate, reqdata.TimeZone)
		totalBetCount += data[i].BetCount
		totalWinLoss += data[i].WinLoss
	}

	// 在返回数据前添加排序
	sort.Slice(data, func(i, j int) bool {
		return data[i].RecordDate > data[j].RecordDate // 从大到小排序
	})
	ctx.Put("data", data)
	ctx.Put("totalBetCount", totalBetCount)
	ctx.Put("totalWinLoss", totalWinLoss)
	ctx.RespOK()
}
