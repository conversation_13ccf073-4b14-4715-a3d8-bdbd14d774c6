package utils

import "regexp"

const (
	VERIFY_EXP_USERNAME = `^[a-zA-Z0-9_]{6,30}$`
	VERIFY_EXP_MAIl     = `\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*`
)

// 校验用户名
func VerifyUSERNAME(str string) bool {
	return verifyFormat(VERIFY_EXP_USERNAME, str)
}

// 校验邮箱
func VerifyMAIl(str string) bool {
	return verifyFormat(VERIFY_EXP_MAIl, str)
}

func verifyFormat(exp, str string) bool {
	reg := regexp.MustCompile(exp)
	return reg.MatchString(str)
}
