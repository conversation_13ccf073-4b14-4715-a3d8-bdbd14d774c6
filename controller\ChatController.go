package controller

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

type ChatController struct {
	companyids map[int]string
	url        string
	appid      string
	appsecret  string
	token      string

	urlv2       string
	appidv2     string
	appsecretv2 string
	tokenv2     string
}

func (c *ChatController) Init() {
	server.Http().PostNoAuth("/chatapi/hongbao", c.hongbao)
	server.Http().PostNoAuth("/api/hongbao", c.hongbao)
	server.Http().PostNoAuth("/api/hongbao_take_times", c.hongbao_take_times)
	server.Http().PostNoAuth("/api/chat/login", c.login)
	server.Http().PostNoAuth("/api/chat/login_v2", c.login_v2)

	c.companyids = make(map[int]string)
	c.url = viper.GetString("chat.url")
	c.appid = viper.GetString("chat.appid")
	c.appsecret = viper.GetString("chat.appsecret")

	c.urlv2 = viper.GetString("chat2.url")
	c.appidv2 = viper.GetString("chat2.appid")
	c.appsecretv2 = viper.GetString("chat2.appsecret")

	if len(c.url) > 0 {
		go func() {
			for {
				data := fmt.Sprintf("{\"appId\":\"%s\",\"AppSecret\":\"%s\"}", c.appid, c.appsecret)
				url := c.url + "/apiPlat/accessToken?language=zh"
				logs.Info("chat init ", url, " ", data)
				resp, err := http.Post(url, "application/json", strings.NewReader(data))
				if err != nil {
					logs.Error("ChatController init:", err)
					time.Sleep(time.Second * 10)
				} else {
					body, err := io.ReadAll(resp.Body)
					resp.Body.Close()
					if err != nil {
						logs.Error("获取聊天室token失败:", err)
						time.Sleep(time.Second * 10)
					} else {
						resultdata := map[string]interface{}{}
						json.Unmarshal(body, &resultdata)
						resultcode := abugo.GetInt64FromInterface(resultdata["code"])
						if resultcode == 200 {
							resultdata = resultdata["data"].(map[string]interface{})
							c.token = abugo.GetStringFromInterface(resultdata["accessToken"])
							delay := abugo.GetInt64FromInterface(resultdata["expiresIn"])
							logs.Debug("聊天室获取token成功:", c.token)
							time.Sleep(time.Second * time.Duration(delay/2))
						} else {
							logs.Error("获取聊天室token失败:", string(body))
							time.Sleep(time.Second * 10)
						}
					}

				}
			}
		}()
	}

	if len(c.urlv2) > 0 {
		go func() {
			for {
				data := fmt.Sprintf("{\"appId\":\"%s\",\"AppSecret\":\"%s\"}", c.appidv2, c.appsecretv2)
				resp, err := http.Post(c.url+"/apiPlat/accessToken?language=zh", "application/json", strings.NewReader(data))
				if err != nil {
					logs.Error("ChatController init:", err)
					time.Sleep(time.Second * 10)
				} else {
					body, err := io.ReadAll(resp.Body)
					resp.Body.Close()
					if err != nil {
						logs.Error("获取聊天室token失败:", err)
						time.Sleep(time.Second * 10)
					} else {
						resultdata := map[string]interface{}{}
						json.Unmarshal(body, &resultdata)
						resultcode := abugo.GetInt64FromInterface(resultdata["code"])
						if resultcode == 200 {
							resultdata = resultdata["data"].(map[string]interface{})
							c.tokenv2 = abugo.GetStringFromInterface(resultdata["accessToken"])
							delay := abugo.GetInt64FromInterface(resultdata["expiresIn"])
							logs.Debug("聊天室获取token成功:", c.tokenv2)
							time.Sleep(time.Second * time.Duration(delay/2))
						} else {
							logs.Error("获取聊天室token失败:", string(body))
							time.Sleep(time.Second * 10)
						}
					}

				}
			}
		}()
	}
}

func (c *ChatController) login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token    string
		UId      string
		LangCode string
		Host     string
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	if reqdata.UId == "" && reqdata.Token == "" {
		ctx.RespNoAuth(2, "未登录或登录已过期")
		return
	}
	//channelid, _ := server.GetChannel(ctx, reqdata.Host)
	////channelid = 2

	channels, _ := server.Db().Table("x_channel").GetList()
	for _, v := range *channels {
		channelid := int(abugo.GetInt64FromInterface(v["ChannelId"]))
		chatcompanyid := abugo.GetStringFromInterface(v["ChatCompanyId"])
		c.companyids[channelid] = chatcompanyid
	}

	host := ctx.Host()
	if reqdata.Host != "" {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	channelid, _ := server.GetChannel(ctx, host)

	vipcode := ""
	if len(c.token) > 0 && len(c.companyids[channelid]) > 0 {
		UserId := reqdata.UId
		identityCode := "VISITOR"
		if reqdata.Token != "" {
			tokenbytes := server.Http().GetToken(reqdata.Token)
			if tokenbytes == nil {
				ctx.RespNoAuth(2, "未登录或登录已过期")
				return
			}
			token := server.TokenData{}
			json.Unmarshal(tokenbytes.([]byte), &token)
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, nil)
			where.Add("and", "Enter", "=", 1, nil)
			cb, _ := server.Db().Table("x_chat_black").Where(where).GetOne()
			if cb != nil {
				ctx.RespOK("您禁止进入聊天室")
				return
			}
			UserId = fmt.Sprint(token.UserId)
			identityCode = "1"
			where = abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, nil)
			vdata, _ := server.Db().Table("x_vip_info").Where(where).GetOne()
			if vdata != nil {
				v := abugo.GetInt64FromInterface((*vdata)["VipLevel"])
				if v > 0 {
					vipcode = "level" + fmt.Sprintf("%v", v)
				}
			}
		}
		requrl := fmt.Sprintf("%s/apiPlat/userAsync?access_token=%s", c.url, c.token)

		// 获取用户头像
		avatar := ""
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", UserId, nil)
		userdata, _ := server.Db().Table("x_user").Where(where).GetOne()

		if userdata == nil {
			avatar = fmt.Sprintf("%s/avatar/avatar%d.webp", server.ImageUrl(), 1)
		} else {
			headId := abugo.GetStringFromInterface((*userdata)["HeadId"])
			if headId != "" {
				avatar = fmt.Sprintf("%s/avatar/avatar%s.webp", server.ImageUrl(), headId)
			} else {
				avatar = fmt.Sprintf("%s/avatar/avatar%d.webp", server.ImageUrl(), 1)
			}
		}

		data := map[string]interface{}{
			"companyId":  c.companyids[channelid],
			"optionType": "SUBSCRIBE ",
			"userList": []map[string]interface{}{
				{
					"userId":       fmt.Sprint(UserId),
					"nickName":     fmt.Sprint(UserId),
					"userName":     fmt.Sprint(UserId),
					"vipCode":      vipcode,
					"identityCode": identityCode,
					"parentId":     fmt.Sprint(UserId),
					"parentIds":    fmt.Sprint(UserId),
					"userIcon":     avatar,
				},
			}}
		bytedata, _ := json.Marshal(&data)
		logs.Info("chat userAsync:", requrl, " ", string(bytedata))
		resp, err := http.Post(requrl, "application/json", strings.NewReader(string(bytedata)))
		if err == nil {
			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err == nil {
				resultdata := map[string]interface{}{}
				json.Unmarshal(body, &resultdata)
				requrl = fmt.Sprintf("%s/apiPlat/userAuth?access_token=%s&language=%v", c.url, c.token, reqdata.LangCode)
				hash := md5.New()
				hash.Write([]byte(fmt.Sprintf("%v_%v_Iz4Qy3Os", UserId, c.companyids[channelid])))
				data = map[string]interface{}{
					"userId":    fmt.Sprint(UserId),
					"companyId": c.companyids[channelid],
					"nickName":  fmt.Sprint(UserId),
					"method":    "https",
					"domain":    fmt.Sprint(strings.Split(ctx.Host(), ":")[0], "/api/user"),
					"port":      443,
					"SessionId": fmt.Sprint(UserId),
					"vipCode":   vipcode,
					"userIcon":  avatar,
				}

				bytedata, _ = json.Marshal(&data)
				logs.Debug("chat login:", string(bytedata))
				resp, err = http.Post(requrl, "application/json", strings.NewReader(string(bytedata)))
				if err == nil {
					body, err = io.ReadAll(resp.Body)
					resp.Body.Close()
					if err == nil {
						resultdata = map[string]interface{}{}
						json.Unmarshal(body, &resultdata)
						if resultdata["data"] != nil {
							userurl := abugo.GetStringFromInterface(resultdata["data"].(map[string]interface{})["userAuthUrl"])
							userurl, _ = url.QueryUnescape(userurl)
							ctx.RespOK(userurl)
						} else {
							fmt.Println("access_token:", string(body))
							ctx.RespOK("")
						}
					} else {
						fmt.Println("chat error1:", err)
						ctx.RespOK("")
					}
				} else {
					fmt.Println("chat error2", err)
					ctx.RespOK("")
				}
			} else {
				fmt.Println("chat error3", err)
				ctx.RespOK("")
			}
		} else {
			fmt.Println("chat error4", err)
			ctx.RespOK("")
		}
	} else {
		fmt.Println("chat error5")
		ctx.RespOK("")
	}
}

func (c *ChatController) hongbao(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		CompanyId string
		UserId    string
		PayPass   string
		RedAmount float64
		RedId     int
		Operate   string
		PlaceId   int64
		PlaceName string
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	logs.Debug("****************************hongbao****************************")
	logs.Debug(reqdata)
	SessionId := ctx.Query("SessionId")
	if SessionId != reqdata.UserId {
		ctx.RespJson(gin.H{"code": 100, "message": "SessionId 不正确"})
		return
	}
	UserId := int(abugo.GetInt64FromInterface(reqdata.UserId))
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", int(abugo.GetInt64FromInterface(reqdata.UserId)), nil)
	userdata, err := server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		ctx.RespJson(gin.H{"code": 100, "message": "读取玩家数据失败"})
		return
	}
	if userdata == nil {
		ctx.RespJson(gin.H{"code": 100, "message": "游客不可领取"})
		return
	}
	channels, _ := server.Db().Table("x_channel").GetList()
	for _, v := range *channels {
		channelid := int(abugo.GetInt64FromInterface(v["ChannelId"]))
		chatcompanyid := abugo.GetStringFromInterface(v["ChatCompanyId"])
		c.companyids[channelid] = chatcompanyid
	}

	ChannelId := int(abugo.GetFloat64FromInterface((*userdata)["ChannelId"]))
	if reqdata.CompanyId != c.companyids[ChannelId] {
		ctx.RespJson(gin.H{"code": 100, "message": "公司不正确"})
		return
	}
	sellerId := int(abugo.GetFloat64FromInterface((*userdata)["SellerId"]))

	lockkey := fmt.Sprintf("hongbao_take_lock:%d", UserId)
	if !server.XRedis().GetLock(lockkey, 10) {
		ctx.RespJson(gin.H{"code": 100, "message": "正在领取红包"})
		return
	}
	defer func() {
		server.XRedis().ReleaseLock(lockkey)
	}()

	if reqdata.RedId != 0 {
		retdata, _ := server.Db().CallProcedure("x_api_hongbao_times", UserId, sellerId)
		logs.Info("hongbao 2 ", retdata)
		if retdata != nil && (*retdata)["errmsg"] != nil {
			ctx.RespJson(gin.H{"code": 100, "message": (*retdata)["errmsg"]})
			return
		}
		if abugo.GetInt64FromInterface((*retdata)["TakeTimes"]) < 1 {
			ctx.RespJson(gin.H{"code": 100, "message": "红包领取次数不足"})
			return
		}
	}

	retdata, _ := server.Db().CallProcedure("x_api_hongbao", reqdata.Operate, UserId, reqdata.RedId, reqdata.RedAmount, reqdata.PayPass, reqdata.PlaceId, reqdata.PlaceName)
	logs.Debug("hongbao reqdata 3", reqdata)
	if retdata != nil && (*retdata)["errmsg"] != nil {
		ctx.RespJson(gin.H{"code": 100, "message": (*retdata)["errmsg"]})
		return
	}
	ctx.RespJson(gin.H{"code": 200, "message": "成功"})
}

func (c *ChatController) hongbao_take_times(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		CompanyId string
		UserId    string
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	logs.Info("hongbao_take_times 1 %+v", reqdata)
	SessionId := ctx.Query("SessionId")
	if SessionId != reqdata.UserId {
		ctx.RespJson(gin.H{"code": 100, "message": "SessionId 不正确"})
		return
	}
	UserId := int(abugo.GetInt64FromInterface(reqdata.UserId))
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", int(abugo.GetInt64FromInterface(reqdata.UserId)), nil)
	userdata, err := server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		ctx.RespJson(gin.H{"code": 100, "message": "读取玩家数据失败"})
		return
	}
	if userdata == nil {
		ctx.RespJson(gin.H{"code": 100, "message": "游客不可领取"})
		return
	}
	channels, _ := server.Db().Table("x_channel").GetList()
	for _, v := range *channels {
		channelid := int(abugo.GetInt64FromInterface(v["ChannelId"]))
		chatcompanyid := abugo.GetStringFromInterface(v["ChatCompanyId"])
		c.companyids[channelid] = chatcompanyid
	}
	ChannelId := int(abugo.GetFloat64FromInterface((*userdata)["ChannelId"]))
	if reqdata.CompanyId != c.companyids[ChannelId] {
		ctx.RespJson(gin.H{"code": 100, "message": "公司不正确"})
		return
	}
	sellerId := int(abugo.GetFloat64FromInterface((*userdata)["SellerId"]))

	retdata, _ := server.Db().CallProcedure("x_api_hongbao_times", UserId, sellerId)
	logs.Info("hongbao_take_times 2 ", retdata)

	if retdata != nil && (*retdata)["errmsg"] != nil {
		ctx.RespJson(gin.H{"code": 100, "message": (*retdata)["errmsg"]})
		return
	}

	ctx.RespJson(gin.H{"code": 200, "message": "成功", "data": retdata})
}

func (c *ChatController) login_v2(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token    string
		UId      string
		LangCode string
		Host     string
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	if reqdata.UId == "" && reqdata.Token == "" {
		ctx.RespNoAuth(2, "未登录或登录已过期")
		return
	}
	channels, _ := server.Db().Table("x_channel").GetList()
	for _, v := range *channels {
		channelid := int(abugo.GetInt64FromInterface(v["ChannelId"]))
		chatcompanyid := abugo.GetStringFromInterface(v["ChatCompanyId"])
		c.companyids[channelid] = chatcompanyid
	}
	channelid, _ := server.GetChannel(ctx, reqdata.Host)
	channelid = 54
	vipcode := ""
	if len(c.tokenv2) > 0 && len(c.companyids[channelid]) > 0 {
		UserId := reqdata.UId
		identityCode := "VISITOR"
		if reqdata.Token != "" {
			tokenbytes := server.Http().GetToken(reqdata.Token)
			if tokenbytes == nil {
				ctx.RespNoAuth(2, "未登录或登录已过期")
				return
			}
			token := server.TokenData{}
			json.Unmarshal(tokenbytes.([]byte), &token)
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, nil)
			where.Add("and", "Enter", "=", 1, nil)
			cb, _ := server.Db().Table("x_chat_black").Where(where).GetOne()
			if cb != nil {
				ctx.RespOK("您禁止进入聊天室")
				return
			}
			UserId = fmt.Sprint(token.UserId)
			identityCode = "1"
			where = abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, nil)
			vdata, _ := server.Db().Table("x_vip_info").Where(where).GetOne()
			if vdata != nil {
				v := abugo.GetInt64FromInterface((*vdata)["VipLevel"])
				if v > 0 {
					vipcode = "level" + fmt.Sprintf("%v", v)
				}
			}
		}
		requrl := fmt.Sprintf("%s/apiPlat/userAsync?access_token=%s", c.urlv2, c.tokenv2)
		data := map[string]interface{}{
			"companyId":  c.companyids[channelid],
			"optionType": "SUBSCRIBE ",
			"userList": []map[string]interface{}{
				{
					"userId":       fmt.Sprint(UserId),
					"nickName":     fmt.Sprint(UserId),
					"userName":     fmt.Sprint(UserId),
					"vipCode":      vipcode,
					"identityCode": identityCode,
					"parentId":     fmt.Sprint(UserId),
					"parentIds":    fmt.Sprint(UserId),
				},
			}}
		bytedata, _ := json.Marshal(&data)
		resp, err := http.Post(requrl, "application/json", strings.NewReader(string(bytedata)))
		if err == nil {
			body, err := io.ReadAll(resp.Body)
			resp.Body.Close()
			if err == nil {
				resultdata := map[string]interface{}{}
				json.Unmarshal(body, &resultdata)
				requrl = fmt.Sprintf("%s/apiPlat/userAuth?access_token=%s&language=%v", c.urlv2, c.tokenv2, reqdata.LangCode)
				hash := md5.New()
				hash.Write([]byte(fmt.Sprintf("%v_%v_Iz4Qy3Os", UserId, c.companyids[channelid])))
				data = map[string]interface{}{
					"userId":    fmt.Sprint(UserId),
					"companyId": c.companyids[channelid],
					"nickName":  fmt.Sprint(UserId),
					"method":    "https",
					"domain":    fmt.Sprint(strings.Split(ctx.Host(), ":")[0], "/api/user"),
					"port":      443,
					"SessionId": fmt.Sprint(UserId),
					"vipCode":   vipcode,
				}

				bytedata, _ = json.Marshal(&data)
				logs.Debug("chat login:", string(bytedata))
				resp, err = http.Post(requrl, "application/json", strings.NewReader(string(bytedata)))
				if err == nil {
					body, err = io.ReadAll(resp.Body)
					resp.Body.Close()
					if err == nil {
						resultdata = map[string]interface{}{}
						json.Unmarshal(body, &resultdata)
						if resultdata["data"] != nil {
							userurl := abugo.GetStringFromInterface(resultdata["data"].(map[string]interface{})["userAuthUrl"])
							userurl, _ = url.QueryUnescape(userurl)
							ctx.RespOK(userurl)
						} else {
							fmt.Println("access_token:", string(body))
							ctx.RespOK("")
						}
					} else {
						fmt.Println("chat error1:", err)
						ctx.RespOK("")
					}
				} else {
					fmt.Println("chat error2", err)
					ctx.RespOK("")
				}
			} else {
				fmt.Println("chat error3", err)
				ctx.RespOK("")
			}
		} else {
			fmt.Println("chat error4", err)
			ctx.RespOK("")
		}
	} else {
		fmt.Println("chat error5")
		ctx.RespOK("")
	}
}
