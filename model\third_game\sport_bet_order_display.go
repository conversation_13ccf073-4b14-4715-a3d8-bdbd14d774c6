package third_game

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"strconv"
	"strings"
	"time"
	"xserver/utils"
)

// SportBetOrderDisplay 体育投注订单显示结构体
type SportBetOrderDisplay struct {
	// 基本订单信息
	Id           int64   `json:"Id"`           // 状态
	ThirdId      string  `json:"thirdId"`      // 订单ID
	UserId       string  `json:"userId"`       // 用户ID
	Brand        string  `json:"brand"`        // 品牌
	BetAmount    float64 `json:"betAmount"`    // 投注金额
	WinAmount    float64 `json:"winAmount"`    // 赢取金额
	Currency     string  `json:"currency"`     // 货币
	BetTime      string  `json:"betTime"`      // 投注时间
	BetLocalTime string  `json:"betLocalTime"` // 本地投注时间
	SettleTime   string  `json:"settleTime"`   // 结算时间

	// 投注详情 - 数组以支持多个投注项
	BetDetails []BetDetail `json:"betDetails"` // 投注详情列表

	// 串关信息
	SeriesType string `json:"seriesType"` // 投注类型（如"2x1*1"） 是否串关//关次类型，0 单关、1 串关
	AllUp      int    `json:"allUp"`      // 串关数

	Profit       float64 `json:"profit"`       // 玩家纯利润 去除本金后 净盈利
	ProfitStatus string  `json:"profitStatus"` // 盈利状态 显示 赢、输、平、未开奖、已撤单
	Status       int     `json:"status"`       // 状态 1, 2:  已结算订单  -1:  未开奖  -2: 已撤单

	// 原始数据
	RawData string `json:"rawData"` // 原始数据
}

// BetDetail 投注详情结构体
type BetDetail struct {
	// 比赛信息
	SportType  string `json:"sportType"`  // 体育类型
	LeagueName string `json:"leagueName"` // 联赛名称
	MatchName  string `json:"matchName"`  // 比赛名称
	MatchTime  string `json:"matchTime"`  // 比赛时间

	// 投注详情
	BetType       string  `json:"betType"`       // 投注类型
	BetOption     string  `json:"betOption"`     // 投注选项
	Odds          float64 `json:"odds"`          // 赔率
	HandicapValue string  `json:"handicapValue"` // 让球值
	BetResult     string  `json:"betResult"`     // 投注结果
	Score         string  `json:"score"`         // 比分
	IsLive        bool    `json:"isLive"`        // 是否为滚球
}

// ParseSportBetOrder 解析不同品牌的体育投注订单
func (s *SportBetOrderDisplay) ParseSportBetOrder(brand string, jsonData string) error {
	switch strings.ToUpper(brand) {
	case "CBK":
		return s.parseCBKBetOrder(jsonData)
	case "FB":
		return s.parseFBBetOrder(jsonData)
	case "SABA":
		return s.parseSABABetOrder(jsonData)
	case "THREE_UP":
		return s.parseThreeUpBetOrder(jsonData)
	case "UP":
		return s.parseUPBetOrder(jsonData)
	case "WICKETS":
		return s.parseWicketsBetOrder(jsonData)
	default:
		return fmt.Errorf("不支持的品牌: %s", brand)
	}
}

// 从map中获取字符串值
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case string:
			return v
		case float64:
			return fmt.Sprintf("%v", v)
		case int:
			return fmt.Sprintf("%d", v)
		case int64:
			return fmt.Sprintf("%d", v)
		case bool:
			return fmt.Sprintf("%v", v)
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	return ""
}

// 从map中获取数值
func getFloat64Value(m map[string]interface{}, key string) float64 {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case float64:
			return v
		case string:
			f, _ := strconv.ParseFloat(v, 64)
			return f
		case int:
			return float64(v)
		case int64:
			return float64(v)
		default:
			f, _ := strconv.ParseFloat(fmt.Sprintf("%v", v), 64)
			return f
		}
	}
	return 0
}

// 从map中获取整数值
func getIntValue(m map[string]interface{}, key string) int {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			i, _ := strconv.Atoi(v)
			return i
		default:
			i, _ := strconv.Atoi(fmt.Sprintf("%v", v))
			return i
		}
	}
	return 0
}

// 从map中获取布尔值
func getBoolValue(m map[string]interface{}, key string) bool {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case bool:
			return v
		case string:
			b, _ := strconv.ParseBool(v)
			return b
		case int:
			return v != 0
		case float64:
			return v != 0
		default:
			return false
		}
	}
	return false
}

// 从map中获取时间值（毫秒时间戳）
func getTimeFromMsValue(m map[string]interface{}, key string) time.Time {
	if val, ok := m[key]; ok {
		var msTime int64
		switch v := val.(type) {
		case int64:
			msTime = v
		case float64:
			msTime = int64(v)
		case string:
			msTime, _ = strconv.ParseInt(v, 10, 64)
		default:
			return time.Time{}
		}

		if msTime > 0 {
			return time.UnixMilli(msTime)
		}
	}
	return time.Time{}
}

// 从map中获取时间值（字符串格式）
//func getTimeFromString(m map[string]interface{}, key string, layout string) time.Time {
//	if val, ok := m[key]; ok {
//		if timeStr, ok := val.(string); ok {
//			t, err := time.Parse(layout, timeStr)
//			if err == nil {
//				return t
//			}
//		}
//	}
//	return time.Time{}
//}

// 解析CBK品牌订单
func (s *SportBetOrderDisplay) parseCBKBetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// 获取订单数据
	if dataObj, ok := data["data"].(map[string]interface{}); ok {
		if order, ok := dataObj["order"].(map[string]interface{}); ok {
			// 判断投注类型
			betType := getStringValue(order, "type")

			if betType == "multi" {
				// 处理串关投注记录
				legs, ok := order["legs"].([]interface{})
				if !ok {
					logs.Error("无法解析串关投注记录的legs字段")
					//return fmt.Errorf("无法解析串关投注记录的legs字段")
				}

				for _, legItem := range legs {
					leg, ok := legItem.(map[string]interface{})
					if !ok {
						continue
					}

					detail := BetDetail{
						SportType:     getStringValue(leg, "sportName"),
						LeagueName:    getStringValue(leg, "leagueName"),
						MatchName:     getStringValue(leg, "eventName"),
						MatchTime:     utils.ParseTimeToLocalStr(getStringValue(leg, "eventTime")),
						BetType:       getStringValue(leg, "marketName"),
						BetOption:     getStringValue(leg, "stakeName"),
						Odds:          getFloat64Value(leg, "odds"),
						HandicapValue: getStringValue(leg, "handicap"),
						BetResult:     getStringValue(leg, "resultType"),
					}
					s.BetDetails = append(s.BetDetails, detail)
				}
			} else {
				// 处理单注投注记录
				detail := BetDetail{
					SportType:     getStringValue(order, "sportName"),
					LeagueName:    getStringValue(order, "leagueName"),
					MatchName:     getStringValue(order, "eventName"),
					MatchTime:     utils.ParseTimeToLocalStr(getStringValue(order, "eventTime")),
					BetType:       getStringValue(order, "marketName"),
					BetOption:     getStringValue(order, "stakeName"),
					Odds:          parseOdds(getStringValue(order, "odds")),
					HandicapValue: getStringValue(order, "handicap"),
					BetResult:     getStringValue(order, "resultType"),
				}
				s.BetDetails = append(s.BetDetails, detail)
			}
		} else {
			//return fmt.Errorf("无法找到order字段")
			logs.Error("无法找到order字段")
		}
	} else {
		logs.Error("无法找到order字段")
		//return fmt.Errorf("无法找到data字段")
	}

	return nil
}

// 解析赔率
func parseOdds(oddsStr string) float64 {
	odds, err := strconv.ParseFloat(oddsStr, 64)
	if err != nil {
		return 0
	}
	return odds
}

// 解析FB品牌订单
func (s *SportBetOrderDisplay) parseFBBetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// 获取串关信息
	s.SeriesType = getStringValue(data, "seriesType")
	s.AllUp = getIntValue(data, "allUp")

	// 处理投注项列表
	if betList, ok := data["betList"].([]interface{}); ok {
		for _, item := range betList {
			if betItem, ok := item.(map[string]interface{}); ok {
				// 创建投注详情
				detail := BetDetail{
					MatchName:  getStringValue(betItem, "matchName"),
					LeagueName: getStringValue(betItem, "tournamentName"),
					BetType:    getStringValue(betItem, "marketName"),
					BetOption:  getStringValue(betItem, "optionName"),
					IsLive:     getBoolValue(betItem, "isInplay"),
				}

				// 设置比赛时间
				matchTime := getStringValue(betItem, "matchTime")
				detail.MatchTime = utils.ParseTimeToLocalStr(matchTime)

				// 设置赔率
				betOdds := getStringValue(betItem, "betOdds")
				odds, _ := strconv.ParseFloat(betOdds, 64)
				detail.Odds = odds

				// 设置投注结果
				settleStatus := getIntValue(betItem, "settleStatus")
				//switch settleStatus {
				//case 1:
				//	detail.BetResult = "赢"
				//case 2:
				//	detail.BetResult = "输"
				//case 3:
				//	detail.BetResult = "平"
				//default:
				//	detail.BetResult = fmt.Sprintf("结果:%d", settleStatus)
				//}
				detail.BetResult = fmt.Sprintf("结果:%d", settleStatus)
				s.BetDetails = append(s.BetDetails, detail)
			}
		}
	}

	return nil
}

// 解析SABA品牌订单
func (s *SportBetOrderDisplay) parseSABABetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// SABA下注类型映射表
	betTypeMap := map[int]string{
		1:   "让球",
		2:   "单双盘",
		3:   "大小盘",
		4:   "波胆 (足球)",
		5:   "全场.独赢盘",
		6:   "总进球 (足球)",
		7:   "上半场让球",
		8:   "上半场大小盘",
		10:  "优胜冠军",
		11:  "总角球数 (足球)",
		12:  "上半场.单双盘",
		13:  "零失球 (足球)",
		14:  "最先进球/最后进球(足球)",
		15:  "上半场.独赢盘",
		16:  "半场.全场 (足球)",
		17:  "下半场让球 (足球)",
		18:  "下半场大小盘 (足球)",
		19:  "Substitutes",
		20:  "胜负盘",
		21:  "上半场胜负盘",
		22:  "得下一分 (足球)",
		23:  "下一角球",
		24:  "双重机会 (足球)",
		25:  "获胜球队 (足球)",
		26:  "双方/一方/两者皆不得分 (足球)",
		27:  "零失球的胜方 (足球)",
		28:  "三项让分投注 (足球)",
		29:  "串关",
		30:  "上半场波胆 (足球)",
		38:  "单场串关 (足球/篮球)",
		121: "主队 (不获胜球队) (足球)",
		122: "客队 (不获胜球队) (足球)",
		123: "和局/不是和局 (足球)",
		124: "全场 1X2 亚洲盘 (足球)",
		125: "上半场 1X2 亚洲盘 (足球)",
	}

	// 获取下注类型的函数
	getBetTypeName := func(betTypeID int) string {
		if name, ok := betTypeMap[betTypeID]; ok {
			return name
		}
		return fmt.Sprintf("未知类型(%d)", betTypeID)
	}

	// 判断是单场投注还是串关投注
	action := getStringValue(data, "action")

	if action == "PlaceBet" {
		// 单场投注
		betTypeID := getIntValue(data, "betType")
		betTypeName := getStringValue(data, "betTypeName")

		// 如果betTypeName为空，则使用映射表中的名称
		if betTypeName == "" {
			betTypeName = getBetTypeName(betTypeID)
		}

		detail := BetDetail{
			SportType:     getStringValue(data, "sportTypeName"),
			LeagueName:    getStringValue(data, "leagueName"),
			MatchName:     fmt.Sprintf("%s vs %s", getStringValue(data, "homeName"), getStringValue(data, "awayName")),
			BetType:       betTypeName,
			BetOption:     getStringValue(data, "betChoice"),
			Odds:          getFloat64Value(data, "odds"),
			HandicapValue: getStringValue(data, "point"),
			Score:         fmt.Sprintf("%d:%d", getIntValue(data, "homeScore"), getIntValue(data, "awayScore")),
			IsLive:        getBoolValue(data, "isLive"),
		}
		// 设置比赛时间
		matchTime := getStringValue(data, "kickOffTime")
		detail.MatchTime = utils.ParseTimeToLocalStr(matchTime)

		s.BetDetails = append(s.BetDetails, detail)
	} else if action == "PlaceBetParlay" {
		// 串关投注
		// 设置串关类型
		s.SeriesType = "0" //0单关 1串关
		s.AllUp = 1        // 默认串关数为1

		// 获取串关详情
		if ticketDetail, ok := data["ticketDetail"].([]interface{}); ok {
			for _, item := range ticketDetail {
				if betItem, ok := item.(map[string]interface{}); ok {
					betTypeID := getIntValue(betItem, "betType")
					betTypeName := getStringValue(betItem, "betTypeName")

					// 如果betTypeName为空，则使用映射表中的名称
					if betTypeName == "" {
						betTypeName = getBetTypeName(betTypeID)
					}

					detail := BetDetail{
						SportType:     getStringValue(betItem, "sportTypeName"),
						LeagueName:    getStringValue(betItem, "leagueName"),
						MatchName:     fmt.Sprintf("%s vs %s", getStringValue(betItem, "homeName"), getStringValue(betItem, "awayName")),
						BetType:       betTypeName,
						BetOption:     getStringValue(betItem, "betChoice"),
						Odds:          getFloat64Value(betItem, "odds"),
						HandicapValue: getStringValue(betItem, "point"),
						Score:         fmt.Sprintf("%d:%d", getIntValue(betItem, "homeScore"), getIntValue(betItem, "awayScore")),
						IsLive:        getBoolValue(betItem, "isLive"),
					}
					// 设置比赛时间
					matchTime := getStringValue(betItem, "kickOffTime")
					detail.MatchTime = utils.ParseTimeToLocalStr(matchTime)

					s.BetDetails = append(s.BetDetails, detail)
				}
			}

			// 获取串关数量
			if txns, ok := data["txns"].([]interface{}); ok && len(txns) > 0 {
				if txn, ok := txns[0].(map[string]interface{}); ok {
					if detail, ok := txn["detail"].([]interface{}); ok && len(detail) > 0 {
						if detailItem, ok := detail[0].(map[string]interface{}); ok {
							s.AllUp = getIntValue(detailItem, "betCount")
						}
					}
				}
			}
		}
	}

	return nil
}

// 解析THREE_UP品牌订单
func (s *SportBetOrderDisplay) parseThreeUpBetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// 创建投注详情
	detail := BetDetail{
		SportType:     getStringValue(data, "sport"),
		LeagueName:    getStringValue(data, "league_name"),
		MatchName:     fmt.Sprintf("%s vs %s", getStringValue(data, "teamA_name"), getStringValue(data, "teamB_name")),
		BetType:       getStringValue(data, "bet_type"),
		BetOption:     getStringValue(data, "team_bet"),
		Odds:          getFloat64Value(data, "odds"),
		HandicapValue: fmt.Sprintf("%d", getIntValue(data, "handicap_value")),
		BetResult:     getStringValue(data, "result"),
		Score:         getStringValue(data, "score"),
		IsLive:        getIntValue(data, "islive") == 1,
	}
	// 设置比赛时间
	matchTime := getStringValue(data, "match_date")
	detail.MatchTime = utils.ParseTimeToLocalStr(matchTime)

	s.BetDetails = append(s.BetDetails, detail)

	return nil
}

// 解析UP品牌订单
func (s *SportBetOrderDisplay) parseUPBetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// 获取串关信息
	betType := getStringValue(data, "BetType")

	// 处理投注项列表
	if selections, ok := data["SelectionsDetails"].([]interface{}); ok {
		for _, item := range selections {
			if selection, ok := item.(map[string]interface{}); ok {
				// 创建投注详情
				detail := BetDetail{}

				// 获取体育类型
				if sport, ok := selection["Sport"].(map[string]interface{}); ok {
					detail.SportType = getStringValue(sport, "Name")
				}

				// 获取投注选项
				if odd, ok := selection["Odd"].(map[string]interface{}); ok {
					detail.BetOption = getStringValue(odd, "Name")
				}

				// 获取比赛名称
				if event, ok := selection["Event"].(map[string]interface{}); ok {
					detail.MatchName = getStringValue(event, "Name")
				}
				detail.BetType = betType
				s.BetDetails = append(s.BetDetails, detail)
			}
		}
	}

	return nil
}

// 解析WICKETS品牌订单
func (s *SportBetOrderDisplay) parseWicketsBetOrder(jsonData string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return err
	}

	// 初始化投注详情数组
	s.BetDetails = make([]BetDetail, 0)

	// 创建投注详情
	detail := BetDetail{
		SportType:  getStringValue(data, "eventTypeName"),
		LeagueName: getStringValue(data, "competitionName"),
		MatchName:  getStringValue(data, "eventName"),
		BetType:    getStringValue(data, "marketName"),
		BetOption:  getStringValue(data, "selectionName"),
		Odds:       getFloat64Value(data, "matchOddsReq"),
		BetResult:  getStringValue(data, "betStatus"),
	}

	s.BetDetails = append(s.BetDetails, detail)

	return nil
}

// NewSportBetOrderDisplay 创建新的体育投注订单显示对象
func NewSportBetOrderDisplay() *SportBetOrderDisplay {
	return &SportBetOrderDisplay{
		BetDetails: make([]BetDetail, 0),
	}
}

// ParseOrderFromJson 从JSON解析订单数据
func ParseOrderFromJson(brand string, jsonData string) (*SportBetOrderDisplay, error) {
	display := NewSportBetOrderDisplay()
	err := display.ParseSportBetOrder(brand, jsonData)
	if err != nil {
		return nil, err
	}
	return display, nil
}
