// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserDailly(db *gorm.DB, opts ...gen.DOOption) xUserDailly {
	_xUserDailly := xUserDailly{}

	_xUserDailly.xUserDaillyDo.UseDB(db, opts...)
	_xUserDailly.xUserDaillyDo.UseModel(&model.XUserDailly{})

	tableName := _xUserDailly.xUserDaillyDo.TableName()
	_xUserDailly.ALL = field.NewAsterisk(tableName)
	_xUserDailly.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserDailly.UserID = field.NewInt32(tableName, "UserId")
	_xUserDailly.RecordDate = field.NewTime(tableName, "RecordDate")
	_xUserDailly.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserDailly.BetTrx = field.NewFloat64(tableName, "BetTrx")
	_xUserDailly.BonusBetTrx = field.NewFloat64(tableName, "BonusBetTrx")
	_xUserDailly.RewardTrx = field.NewFloat64(tableName, "RewardTrx")
	_xUserDailly.BonusRewardTrx = field.NewFloat64(tableName, "BonusRewardTrx")
	_xUserDailly.LiuSuiTrx = field.NewFloat64(tableName, "LiuSuiTrx")
	_xUserDailly.BetUsdt = field.NewFloat64(tableName, "BetUsdt")
	_xUserDailly.BonusBetUsdt = field.NewFloat64(tableName, "BonusBetUsdt")
	_xUserDailly.RewardUsdt = field.NewFloat64(tableName, "RewardUsdt")
	_xUserDailly.BonusRewardUsdt = field.NewFloat64(tableName, "BonusRewardUsdt")
	_xUserDailly.LiuSuiUsdt = field.NewFloat64(tableName, "LiuSuiUsdt")
	_xUserDailly.HaXiRouletteBetTrx = field.NewFloat64(tableName, "HaXiRouletteBetTrx")
	_xUserDailly.HaXiRouletteBonusBetTrx = field.NewFloat64(tableName, "HaXiRouletteBonusBetTrx")
	_xUserDailly.HaXiRouletteRewardTrx = field.NewFloat64(tableName, "HaXiRouletteRewardTrx")
	_xUserDailly.HaXiRouletteBonusRewardTrx = field.NewFloat64(tableName, "HaXiRouletteBonusRewardTrx")
	_xUserDailly.HaXiRouletteLiuSuiTrx = field.NewFloat64(tableName, "HaXiRouletteLiuSuiTrx")
	_xUserDailly.HaXiRouletteBetUsdt = field.NewFloat64(tableName, "HaXiRouletteBetUsdt")
	_xUserDailly.HaXiRouletteBonusBetUsdt = field.NewFloat64(tableName, "HaXiRouletteBonusBetUsdt")
	_xUserDailly.HaXiRouletteRewardUsdt = field.NewFloat64(tableName, "HaXiRouletteRewardUsdt")
	_xUserDailly.HaXiRouletteBonusRewardUsdt = field.NewFloat64(tableName, "HaXiRouletteBonusRewardUsdt")
	_xUserDailly.HaXiRouletteLiuSuiUsdt = field.NewFloat64(tableName, "HaXiRouletteLiuSuiUsdt")
	_xUserDailly.LiuSuiLottery = field.NewFloat64(tableName, "LiuSuiLottery")
	_xUserDailly.LiuSuiLowLottery = field.NewFloat64(tableName, "LiuSuiLowLottery")
	_xUserDailly.LiuSuiLiuHeLottery = field.NewFloat64(tableName, "LiuSuiLiuHeLottery")
	_xUserDailly.LiuSuiQiPai = field.NewFloat64(tableName, "LiuSuiQiPai")
	_xUserDailly.LiuSuiDianZhi = field.NewFloat64(tableName, "LiuSuiDianZhi")
	_xUserDailly.LiuSuiXiaoYouXi = field.NewFloat64(tableName, "LiuSuiXiaoYouXi")
	_xUserDailly.LiuSuiCryptoMarket = field.NewFloat64(tableName, "LiuSuiCryptoMarket")
	_xUserDailly.LiuSuiLive = field.NewFloat64(tableName, "LiuSuiLive")
	_xUserDailly.LiuSuiSport = field.NewFloat64(tableName, "LiuSuiSport")
	_xUserDailly.LiuSuiTexas = field.NewFloat64(tableName, "LiuSuiTexas")
	_xUserDailly.FineLiuSuiTrx = field.NewFloat64(tableName, "FineLiuSuiTrx")
	_xUserDailly.FineLiuSuiUsdt = field.NewFloat64(tableName, "FineLiuSuiUsdt")
	_xUserDailly.FineAccount = field.NewString(tableName, "FineAccount")
	_xUserDailly.FineTime = field.NewTime(tableName, "FineTime")
	_xUserDailly.FineMemo = field.NewString(tableName, "FineMemo")
	_xUserDailly.BetCountTrx = field.NewInt32(tableName, "BetCountTrx")
	_xUserDailly.BetCountUsdt = field.NewInt32(tableName, "BetCountUsdt")
	_xUserDailly.TotalWinLoss = field.NewFloat64(tableName, "TotalWinLoss")
	_xUserDailly.TotalLiuSui = field.NewFloat64(tableName, "TotalLiuSui")
	_xUserDailly.TrxRate = field.NewFloat64(tableName, "TrxRate")
	_xUserDailly.TotalCaiJin = field.NewFloat64(tableName, "TotalCaiJin")
	_xUserDailly.TotalCaiJinTrx = field.NewFloat64(tableName, "TotalCaiJinTrx")

	_xUserDailly.fillFieldMap()

	return _xUserDailly
}

type xUserDailly struct {
	xUserDaillyDo xUserDaillyDo

	ALL                         field.Asterisk
	ChannelID                   field.Int32
	UserID                      field.Int32
	RecordDate                  field.Time
	SellerID                    field.Int32
	BetTrx                      field.Float64 // Trx下注
	BonusBetTrx                 field.Float64 // Bonus-Trx下注
	RewardTrx                   field.Float64 // trx返奖
	BonusRewardTrx              field.Float64 // Bonus-trx返奖
	LiuSuiTrx                   field.Float64 // trx流水
	BetUsdt                     field.Float64 // usdt下注
	BonusBetUsdt                field.Float64 // Bonus-usdt下注
	RewardUsdt                  field.Float64 // usdt返奖
	BonusRewardUsdt             field.Float64 // Bonus-usdt返奖
	LiuSuiUsdt                  field.Float64 // usdt流水
	HaXiRouletteBetTrx          field.Float64 // 哈希轮盘下注Trx
	HaXiRouletteBonusBetTrx     field.Float64 // 哈希轮盘下注Bonus-Trx
	HaXiRouletteRewardTrx       field.Float64 // 哈希轮盘返奖Trx
	HaXiRouletteBonusRewardTrx  field.Float64 // 哈希轮盘返奖Bonus-Trx
	HaXiRouletteLiuSuiTrx       field.Float64 // 哈希轮盘流水Trx
	HaXiRouletteBetUsdt         field.Float64 // 哈希轮盘下注Usdt
	HaXiRouletteBonusBetUsdt    field.Float64 // 哈希轮盘下注Bonus-Usdt
	HaXiRouletteRewardUsdt      field.Float64 // 哈希轮盘返奖Usdt
	HaXiRouletteBonusRewardUsdt field.Float64 // 哈希轮盘返奖Bonus-Usdt
	HaXiRouletteLiuSuiUsdt      field.Float64 // 哈希轮盘流水Usdt
	LiuSuiLottery               field.Float64 // 彩票流水
	LiuSuiLowLottery            field.Float64 // 低频彩流水
	LiuSuiLiuHeLottery          field.Float64 // 六合彩流水
	LiuSuiQiPai                 field.Float64 // 棋牌流水
	LiuSuiDianZhi               field.Float64 // 电子流水
	LiuSuiXiaoYouXi             field.Float64 // 小游戏流水
	LiuSuiCryptoMarket          field.Float64 // 低频彩流水
	LiuSuiLive                  field.Float64 // 真人流水
	LiuSuiSport                 field.Float64 // 体育流水
	LiuSuiTexas                 field.Float64 // 德州流水
	FineLiuSuiTrx               field.Float64 // 扣除流水trx
	FineLiuSuiUsdt              field.Float64 // 扣除流水Usdt
	FineAccount                 field.String  // 扣除流水操作人
	FineTime                    field.Time    // 扣除流水时间
	FineMemo                    field.String
	BetCountTrx                 field.Int32
	BetCountUsdt                field.Int32
	TotalWinLoss                field.Float64 // 今日输赢总额(包含所有游戏) 赢为正 输为负
	TotalLiuSui                 field.Float64 // 今日总流水usdt+trx
	TrxRate                     field.Float64 // 今日trx汇率
	TotalCaiJin                 field.Float64 // 今日赠送彩金总额Usdt
	TotalCaiJinTrx              field.Float64 // 今日赠送彩金总额Trx

	fieldMap map[string]field.Expr
}

func (x xUserDailly) Table(newTableName string) *xUserDailly {
	x.xUserDaillyDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserDailly) As(alias string) *xUserDailly {
	x.xUserDaillyDo.DO = *(x.xUserDaillyDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserDailly) updateTableName(table string) *xUserDailly {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.BetTrx = field.NewFloat64(table, "BetTrx")
	x.BonusBetTrx = field.NewFloat64(table, "BonusBetTrx")
	x.RewardTrx = field.NewFloat64(table, "RewardTrx")
	x.BonusRewardTrx = field.NewFloat64(table, "BonusRewardTrx")
	x.LiuSuiTrx = field.NewFloat64(table, "LiuSuiTrx")
	x.BetUsdt = field.NewFloat64(table, "BetUsdt")
	x.BonusBetUsdt = field.NewFloat64(table, "BonusBetUsdt")
	x.RewardUsdt = field.NewFloat64(table, "RewardUsdt")
	x.BonusRewardUsdt = field.NewFloat64(table, "BonusRewardUsdt")
	x.LiuSuiUsdt = field.NewFloat64(table, "LiuSuiUsdt")
	x.HaXiRouletteBetTrx = field.NewFloat64(table, "HaXiRouletteBetTrx")
	x.HaXiRouletteBonusBetTrx = field.NewFloat64(table, "HaXiRouletteBonusBetTrx")
	x.HaXiRouletteRewardTrx = field.NewFloat64(table, "HaXiRouletteRewardTrx")
	x.HaXiRouletteBonusRewardTrx = field.NewFloat64(table, "HaXiRouletteBonusRewardTrx")
	x.HaXiRouletteLiuSuiTrx = field.NewFloat64(table, "HaXiRouletteLiuSuiTrx")
	x.HaXiRouletteBetUsdt = field.NewFloat64(table, "HaXiRouletteBetUsdt")
	x.HaXiRouletteBonusBetUsdt = field.NewFloat64(table, "HaXiRouletteBonusBetUsdt")
	x.HaXiRouletteRewardUsdt = field.NewFloat64(table, "HaXiRouletteRewardUsdt")
	x.HaXiRouletteBonusRewardUsdt = field.NewFloat64(table, "HaXiRouletteBonusRewardUsdt")
	x.HaXiRouletteLiuSuiUsdt = field.NewFloat64(table, "HaXiRouletteLiuSuiUsdt")
	x.LiuSuiLottery = field.NewFloat64(table, "LiuSuiLottery")
	x.LiuSuiLowLottery = field.NewFloat64(table, "LiuSuiLowLottery")
	x.LiuSuiLiuHeLottery = field.NewFloat64(table, "LiuSuiLiuHeLottery")
	x.LiuSuiQiPai = field.NewFloat64(table, "LiuSuiQiPai")
	x.LiuSuiDianZhi = field.NewFloat64(table, "LiuSuiDianZhi")
	x.LiuSuiXiaoYouXi = field.NewFloat64(table, "LiuSuiXiaoYouXi")
	x.LiuSuiCryptoMarket = field.NewFloat64(table, "LiuSuiCryptoMarket")
	x.LiuSuiLive = field.NewFloat64(table, "LiuSuiLive")
	x.LiuSuiSport = field.NewFloat64(table, "LiuSuiSport")
	x.LiuSuiTexas = field.NewFloat64(table, "LiuSuiTexas")
	x.FineLiuSuiTrx = field.NewFloat64(table, "FineLiuSuiTrx")
	x.FineLiuSuiUsdt = field.NewFloat64(table, "FineLiuSuiUsdt")
	x.FineAccount = field.NewString(table, "FineAccount")
	x.FineTime = field.NewTime(table, "FineTime")
	x.FineMemo = field.NewString(table, "FineMemo")
	x.BetCountTrx = field.NewInt32(table, "BetCountTrx")
	x.BetCountUsdt = field.NewInt32(table, "BetCountUsdt")
	x.TotalWinLoss = field.NewFloat64(table, "TotalWinLoss")
	x.TotalLiuSui = field.NewFloat64(table, "TotalLiuSui")
	x.TrxRate = field.NewFloat64(table, "TrxRate")
	x.TotalCaiJin = field.NewFloat64(table, "TotalCaiJin")
	x.TotalCaiJinTrx = field.NewFloat64(table, "TotalCaiJinTrx")

	x.fillFieldMap()

	return x
}

func (x *xUserDailly) WithContext(ctx context.Context) *xUserDaillyDo {
	return x.xUserDaillyDo.WithContext(ctx)
}

func (x xUserDailly) TableName() string { return x.xUserDaillyDo.TableName() }

func (x xUserDailly) Alias() string { return x.xUserDaillyDo.Alias() }

func (x xUserDailly) Columns(cols ...field.Expr) gen.Columns { return x.xUserDaillyDo.Columns(cols...) }

func (x *xUserDailly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserDailly) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 46)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["BetTrx"] = x.BetTrx
	x.fieldMap["BonusBetTrx"] = x.BonusBetTrx
	x.fieldMap["RewardTrx"] = x.RewardTrx
	x.fieldMap["BonusRewardTrx"] = x.BonusRewardTrx
	x.fieldMap["LiuSuiTrx"] = x.LiuSuiTrx
	x.fieldMap["BetUsdt"] = x.BetUsdt
	x.fieldMap["BonusBetUsdt"] = x.BonusBetUsdt
	x.fieldMap["RewardUsdt"] = x.RewardUsdt
	x.fieldMap["BonusRewardUsdt"] = x.BonusRewardUsdt
	x.fieldMap["LiuSuiUsdt"] = x.LiuSuiUsdt
	x.fieldMap["HaXiRouletteBetTrx"] = x.HaXiRouletteBetTrx
	x.fieldMap["HaXiRouletteBonusBetTrx"] = x.HaXiRouletteBonusBetTrx
	x.fieldMap["HaXiRouletteRewardTrx"] = x.HaXiRouletteRewardTrx
	x.fieldMap["HaXiRouletteBonusRewardTrx"] = x.HaXiRouletteBonusRewardTrx
	x.fieldMap["HaXiRouletteLiuSuiTrx"] = x.HaXiRouletteLiuSuiTrx
	x.fieldMap["HaXiRouletteBetUsdt"] = x.HaXiRouletteBetUsdt
	x.fieldMap["HaXiRouletteBonusBetUsdt"] = x.HaXiRouletteBonusBetUsdt
	x.fieldMap["HaXiRouletteRewardUsdt"] = x.HaXiRouletteRewardUsdt
	x.fieldMap["HaXiRouletteBonusRewardUsdt"] = x.HaXiRouletteBonusRewardUsdt
	x.fieldMap["HaXiRouletteLiuSuiUsdt"] = x.HaXiRouletteLiuSuiUsdt
	x.fieldMap["LiuSuiLottery"] = x.LiuSuiLottery
	x.fieldMap["LiuSuiLowLottery"] = x.LiuSuiLowLottery
	x.fieldMap["LiuSuiLiuHeLottery"] = x.LiuSuiLiuHeLottery
	x.fieldMap["LiuSuiQiPai"] = x.LiuSuiQiPai
	x.fieldMap["LiuSuiDianZhi"] = x.LiuSuiDianZhi
	x.fieldMap["LiuSuiXiaoYouXi"] = x.LiuSuiXiaoYouXi
	x.fieldMap["LiuSuiCryptoMarket"] = x.LiuSuiCryptoMarket
	x.fieldMap["LiuSuiLive"] = x.LiuSuiLive
	x.fieldMap["LiuSuiSport"] = x.LiuSuiSport
	x.fieldMap["LiuSuiTexas"] = x.LiuSuiTexas
	x.fieldMap["FineLiuSuiTrx"] = x.FineLiuSuiTrx
	x.fieldMap["FineLiuSuiUsdt"] = x.FineLiuSuiUsdt
	x.fieldMap["FineAccount"] = x.FineAccount
	x.fieldMap["FineTime"] = x.FineTime
	x.fieldMap["FineMemo"] = x.FineMemo
	x.fieldMap["BetCountTrx"] = x.BetCountTrx
	x.fieldMap["BetCountUsdt"] = x.BetCountUsdt
	x.fieldMap["TotalWinLoss"] = x.TotalWinLoss
	x.fieldMap["TotalLiuSui"] = x.TotalLiuSui
	x.fieldMap["TrxRate"] = x.TrxRate
	x.fieldMap["TotalCaiJin"] = x.TotalCaiJin
	x.fieldMap["TotalCaiJinTrx"] = x.TotalCaiJinTrx
}

func (x xUserDailly) clone(db *gorm.DB) xUserDailly {
	x.xUserDaillyDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserDailly) replaceDB(db *gorm.DB) xUserDailly {
	x.xUserDaillyDo.ReplaceDB(db)
	return x
}

type xUserDaillyDo struct{ gen.DO }

func (x xUserDaillyDo) Debug() *xUserDaillyDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserDaillyDo) WithContext(ctx context.Context) *xUserDaillyDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserDaillyDo) ReadDB() *xUserDaillyDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserDaillyDo) WriteDB() *xUserDaillyDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserDaillyDo) Session(config *gorm.Session) *xUserDaillyDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserDaillyDo) Clauses(conds ...clause.Expression) *xUserDaillyDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserDaillyDo) Returning(value interface{}, columns ...string) *xUserDaillyDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserDaillyDo) Not(conds ...gen.Condition) *xUserDaillyDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserDaillyDo) Or(conds ...gen.Condition) *xUserDaillyDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserDaillyDo) Select(conds ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserDaillyDo) Where(conds ...gen.Condition) *xUserDaillyDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserDaillyDo) Order(conds ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserDaillyDo) Distinct(cols ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserDaillyDo) Omit(cols ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserDaillyDo) Join(table schema.Tabler, on ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserDaillyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserDaillyDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserDaillyDo) Group(cols ...field.Expr) *xUserDaillyDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserDaillyDo) Having(conds ...gen.Condition) *xUserDaillyDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserDaillyDo) Limit(limit int) *xUserDaillyDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserDaillyDo) Offset(offset int) *xUserDaillyDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserDaillyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserDaillyDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserDaillyDo) Unscoped() *xUserDaillyDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserDaillyDo) Create(values ...*model.XUserDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserDaillyDo) CreateInBatches(values []*model.XUserDailly, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserDaillyDo) Save(values ...*model.XUserDailly) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserDaillyDo) First() (*model.XUserDailly, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserDailly), nil
	}
}

func (x xUserDaillyDo) Take() (*model.XUserDailly, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserDailly), nil
	}
}

func (x xUserDaillyDo) Last() (*model.XUserDailly, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserDailly), nil
	}
}

func (x xUserDaillyDo) Find() ([]*model.XUserDailly, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserDailly), err
}

func (x xUserDaillyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserDailly, err error) {
	buf := make([]*model.XUserDailly, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserDaillyDo) FindInBatches(result *[]*model.XUserDailly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserDaillyDo) Attrs(attrs ...field.AssignExpr) *xUserDaillyDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserDaillyDo) Assign(attrs ...field.AssignExpr) *xUserDaillyDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserDaillyDo) Joins(fields ...field.RelationField) *xUserDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserDaillyDo) Preload(fields ...field.RelationField) *xUserDaillyDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserDaillyDo) FirstOrInit() (*model.XUserDailly, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserDailly), nil
	}
}

func (x xUserDaillyDo) FirstOrCreate() (*model.XUserDailly, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserDailly), nil
	}
}

func (x xUserDaillyDo) FindByPage(offset int, limit int) (result []*model.XUserDailly, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserDaillyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserDaillyDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserDaillyDo) Delete(models ...*model.XUserDailly) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserDaillyDo) withDO(do gen.Dao) *xUserDaillyDo {
	x.DO = *do.(*gen.DO)
	return x
}
