// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXNoticeV2 = "x_notice_v2"

// XNoticeV2 mapped from table <x_notice_v2>
type XNoticeV2 struct {
	UID        int32     `gorm:"column:UId;primaryKey;autoIncrement:true" json:"UId"`
	SellerID   int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                       // 运营商
	ChannelID  int32     `gorm:"column:ChannelId;not null;default:1;comment:渠道id" json:"ChannelId"`          // 渠道id
	ID         int32     `gorm:"column:Id;not null;comment:公共id" json:"Id"`                                  // 公共id
	Nick       string    `gorm:"column:Nick;not null;comment:公告名字" json:"Nick"`                              // 公告名字
	Title      string    `gorm:"column:Title;not null;comment:公告标题" json:"Title"`                            // 公告标题
	LangID     int32     `gorm:"column:LangId;not null;default:1;comment:语言id" json:"LangId"`                // 语言id
	Content    string    `gorm:"column:Content;comment:公告内容" json:"Content"`                                 // 公告内容
	State      int32     `gorm:"column:State;comment:状态 1启用 2禁用" json:"State"`                               // 状态 1启用 2禁用
	UpdateTime time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
	Sort       int32     `gorm:"column:Sort;comment:排序 数字越大越靠前" json:"Sort"`                                 // 排序 数字越大越靠前
	Type       int32     `gorm:"column:Type;default:1;comment:公告类型 1系统 2活动" json:"Type"`                     // 公告类型 1系统 2活动
	Img        string    `gorm:"column:Img;comment:图片" json:"Img"`                                           // 图片
	Link       string    `gorm:"column:Link;comment:跳转路径" json:"Link"`                                       // 跳转路径
	IsNew      int32     `gorm:"column:IsNew;default:2;comment:新公告 1是,2不是" json:"IsNew"`                     // 新公告 1是,2不是
	IsPop      int32     `gorm:"column:IsPop;default:2;comment:弹窗 1是，2不是" json:"IsPop"`                      // 弹窗 1是，2不是
	PcLink     string    `gorm:"column:PcLink" json:"PcLink"`
	GameType   string    `gorm:"column:GameType;comment:游戏类型" json:"GameType"` // 游戏类型
}

// TableName XNoticeV2's table name
func (*XNoticeV2) TableName() string {
	return TableNameXNoticeV2
}
