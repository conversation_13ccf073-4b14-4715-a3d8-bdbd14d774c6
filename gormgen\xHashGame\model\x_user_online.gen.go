// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserOnline = "x_user_online"

// XUserOnline mapped from table <x_user_online>
type XUserOnline struct {
	ID        int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID    int32     `gorm:"column:UserId;not null" json:"UserId"`
	Day       time.Time `gorm:"column:Day;not null" json:"Day"`
	Time      int32     `gorm:"column:Time;not null;comment:second" json:"Time"` // second
	CreatedAt time.Time `gorm:"column:CreatedAt;not null" json:"CreatedAt"`
	UpdatedAt time.Time `gorm:"column:UpdatedAt;not null" json:"UpdatedAt"`
}

// TableName XUserOnline's table name
func (*XUserOnline) TableName() string {
	return TableNameXUserOnline
}
