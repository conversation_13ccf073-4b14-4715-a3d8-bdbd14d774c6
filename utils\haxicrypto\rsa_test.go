package haxicrypto

import (
	"encoding/base64"
	"os"
	"testing"
)

func TestGenRsaKey(t *testing.T) {
	prvkey, pubkey, err := GenRsaKey(2048)
	if err != nil {
		t.Error(err)
		return
	}

	path := "../../config/"
	{
		f, err := os.Create(path + "rsa_key")
		if err != nil {
			t.Error(err)
			return
		}
		f.Write(prvkey)
		f.Close()
	}
	{
		f, err := os.Create(path + "rsa_key.pub")
		if err != nil {
			t.Error(err)
			return
		}
		f.Write(pubkey)
		f.Close()
	}
	t.Log("创建成功")
}

func TestRsaEncrypt(t *testing.T) {
	text := "哈喽 World"

	pubkey, err := os.ReadFile("../../config/rsa_key.pub")
	if err != nil {
		t.Error(err)
		return
	}

	dist, err := RsaEncrypt(pubkey, []byte(text))
	if err != nil {
		t.Error(err)
		return
	}
	encrypted := base64.StdEncoding.EncodeToString(dist)
	t.Log("密文：", encrypted)

	prvkey, err := os.ReadFile("../../config/rsa_key")
	if err != nil {
		t.Error(err)
		return
	}

	cipher, _ := base64.StdEncoding.DecodeString(encrypted)

	decrypted, err := RsaDecrypt(prvkey, cipher)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log("明文：", string(decrypted))
}
