// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXChannelGameList(db *gorm.DB, opts ...gen.DOOption) xChannelGameList {
	_xChannelGameList := xChannelGameList{}

	_xChannelGameList.xChannelGameListDo.UseDB(db, opts...)
	_xChannelGameList.xChannelGameListDo.UseModel(&model.XChannelGameList{})

	tableName := _xChannelGameList.xChannelGameListDo.TableName()
	_xChannelGameList.ALL = field.NewAsterisk(tableName)
	_xChannelGameList.HostID = field.NewInt32(tableName, "HostId")
	_xChannelGameList.Brand = field.NewString(tableName, "Brand")
	_xChannelGameList.GameID = field.NewString(tableName, "GameId")
	_xChannelGameList.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xChannelGameList.Host = field.NewString(tableName, "Host")
	_xChannelGameList.Sort = field.NewInt32(tableName, "Sort")
	_xChannelGameList.CreateTime = field.NewTime(tableName, "CreateTime")
	_xChannelGameList.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xChannelGameList.fillFieldMap()

	return _xChannelGameList
}

type xChannelGameList struct {
	xChannelGameListDo xChannelGameListDo

	ALL        field.Asterisk
	HostID     field.Int32  // 域名Id 来源x_channel_host的Id
	Brand      field.String // 品牌
	GameID     field.String // 游戏Id
	ChannelID  field.Int32  // 渠道Id
	Host       field.String // 域名
	Sort       field.Int32  // 排序,数字越大越靠前
	CreateTime field.Time   // 创建时间
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xChannelGameList) Table(newTableName string) *xChannelGameList {
	x.xChannelGameListDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xChannelGameList) As(alias string) *xChannelGameList {
	x.xChannelGameListDo.DO = *(x.xChannelGameListDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xChannelGameList) updateTableName(table string) *xChannelGameList {
	x.ALL = field.NewAsterisk(table)
	x.HostID = field.NewInt32(table, "HostId")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Host = field.NewString(table, "Host")
	x.Sort = field.NewInt32(table, "Sort")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xChannelGameList) WithContext(ctx context.Context) *xChannelGameListDo {
	return x.xChannelGameListDo.WithContext(ctx)
}

func (x xChannelGameList) TableName() string { return x.xChannelGameListDo.TableName() }

func (x xChannelGameList) Alias() string { return x.xChannelGameListDo.Alias() }

func (x xChannelGameList) Columns(cols ...field.Expr) gen.Columns {
	return x.xChannelGameListDo.Columns(cols...)
}

func (x *xChannelGameList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xChannelGameList) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 8)
	x.fieldMap["HostId"] = x.HostID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Host"] = x.Host
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xChannelGameList) clone(db *gorm.DB) xChannelGameList {
	x.xChannelGameListDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xChannelGameList) replaceDB(db *gorm.DB) xChannelGameList {
	x.xChannelGameListDo.ReplaceDB(db)
	return x
}

type xChannelGameListDo struct{ gen.DO }

func (x xChannelGameListDo) Debug() *xChannelGameListDo {
	return x.withDO(x.DO.Debug())
}

func (x xChannelGameListDo) WithContext(ctx context.Context) *xChannelGameListDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xChannelGameListDo) ReadDB() *xChannelGameListDo {
	return x.Clauses(dbresolver.Read)
}

func (x xChannelGameListDo) WriteDB() *xChannelGameListDo {
	return x.Clauses(dbresolver.Write)
}

func (x xChannelGameListDo) Session(config *gorm.Session) *xChannelGameListDo {
	return x.withDO(x.DO.Session(config))
}

func (x xChannelGameListDo) Clauses(conds ...clause.Expression) *xChannelGameListDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xChannelGameListDo) Returning(value interface{}, columns ...string) *xChannelGameListDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xChannelGameListDo) Not(conds ...gen.Condition) *xChannelGameListDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xChannelGameListDo) Or(conds ...gen.Condition) *xChannelGameListDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xChannelGameListDo) Select(conds ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xChannelGameListDo) Where(conds ...gen.Condition) *xChannelGameListDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xChannelGameListDo) Order(conds ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xChannelGameListDo) Distinct(cols ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xChannelGameListDo) Omit(cols ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xChannelGameListDo) Join(table schema.Tabler, on ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xChannelGameListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xChannelGameListDo) RightJoin(table schema.Tabler, on ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xChannelGameListDo) Group(cols ...field.Expr) *xChannelGameListDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xChannelGameListDo) Having(conds ...gen.Condition) *xChannelGameListDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xChannelGameListDo) Limit(limit int) *xChannelGameListDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xChannelGameListDo) Offset(offset int) *xChannelGameListDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xChannelGameListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xChannelGameListDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xChannelGameListDo) Unscoped() *xChannelGameListDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xChannelGameListDo) Create(values ...*model.XChannelGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xChannelGameListDo) CreateInBatches(values []*model.XChannelGameList, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xChannelGameListDo) Save(values ...*model.XChannelGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xChannelGameListDo) First() (*model.XChannelGameList, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelGameList), nil
	}
}

func (x xChannelGameListDo) Take() (*model.XChannelGameList, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelGameList), nil
	}
}

func (x xChannelGameListDo) Last() (*model.XChannelGameList, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelGameList), nil
	}
}

func (x xChannelGameListDo) Find() ([]*model.XChannelGameList, error) {
	result, err := x.DO.Find()
	return result.([]*model.XChannelGameList), err
}

func (x xChannelGameListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XChannelGameList, err error) {
	buf := make([]*model.XChannelGameList, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xChannelGameListDo) FindInBatches(result *[]*model.XChannelGameList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xChannelGameListDo) Attrs(attrs ...field.AssignExpr) *xChannelGameListDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xChannelGameListDo) Assign(attrs ...field.AssignExpr) *xChannelGameListDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xChannelGameListDo) Joins(fields ...field.RelationField) *xChannelGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xChannelGameListDo) Preload(fields ...field.RelationField) *xChannelGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xChannelGameListDo) FirstOrInit() (*model.XChannelGameList, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelGameList), nil
	}
}

func (x xChannelGameListDo) FirstOrCreate() (*model.XChannelGameList, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XChannelGameList), nil
	}
}

func (x xChannelGameListDo) FindByPage(offset int, limit int) (result []*model.XChannelGameList, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xChannelGameListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xChannelGameListDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xChannelGameListDo) Delete(models ...*model.XChannelGameList) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xChannelGameListDo) withDO(do gen.Dao) *xChannelGameListDo {
	x.DO = *do.(*gen.DO)
	return x
}
