// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXAgent = "x_agent"

// XAgent mapped from table <x_agent>
type XAgent struct {
	ID                           int32   `gorm:"column:Id;not null" json:"Id"`
	UserID                       int32   `gorm:"column:UserId;primaryKey;comment:代理id" json:"UserId"` // 代理id
	SellerID                     int32   `gorm:"column:SellerId;not null" json:"SellerId"`
	ChannelID                    int32   `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	Address                      string  `gorm:"column:Address" json:"Address"`
	IsTopAgent                   int32   `gorm:"column:IsTopAgent;default:2;comment:是否是顶级代理 1是 2不是" json:"IsTopAgent"`                       // 是否是顶级代理 1是 2不是
	IsNeedActive                 int32   `gorm:"column:IsNeedActive;not null;default:1;comment:提款是否需要激活钱包 1激活 2不需要激活" json:"IsNeedActive"`   // 提款是否需要激活钱包 1激活 2不需要激活
	ChildCount                   int32   `gorm:"column:ChildCount;comment:团队总人数" json:"ChildCount"`                                          // 团队总人数
	DictChildCount               int32   `gorm:"column:DictChildCount;comment:直属总人数" json:"DictChildCount"`                                  // 直属总人数
	TotalLiuSuiTrx               float64 `gorm:"column:TotalLiuSuiTrx;default:0.000000;comment:总流水trx" json:"TotalLiuSuiTrx"`                // 总流水trx
	TotalLiuSuiUsdt              float64 `gorm:"column:TotalLiuSuiUsdt;default:0.000000;comment:总流水usdt" json:"TotalLiuSuiUsdt"`             // 总流水usdt
	TotalBetTrx                  float64 `gorm:"column:TotalBetTrx;default:0.000000;comment:总下注trx" json:"TotalBetTrx"`                      // 总下注trx
	TotalBetUsdt                 float64 `gorm:"column:TotalBetUsdt;default:0.000000;comment:总下注usdt" json:"TotalBetUsdt"`                   // 总下注usdt
	TotalRewardTrx               float64 `gorm:"column:TotalRewardTrx;default:0.000000;comment:总返奖trx" json:"TotalRewardTrx"`                // 总返奖trx
	TotalRewardUsdt              float64 `gorm:"column:TotalRewardUsdt;default:0.000000;comment:总返奖usdt" json:"TotalRewardUsdt"`             // 总返奖usdt
	SelfLiuSuiTrx                float64 `gorm:"column:SelfLiuSuiTrx;default:0.000000;comment:自身流水trx" json:"SelfLiuSuiTrx"`                 // 自身流水trx
	SelfLiuSuiUsdt               float64 `gorm:"column:SelfLiuSuiUsdt;default:0.000000;comment:自身流水usdt" json:"SelfLiuSuiUsdt"`              // 自身流水usdt
	SelfBetTrx                   float64 `gorm:"column:SelfBetTrx;default:0.000000;comment:自身下注trx" json:"SelfBetTrx"`                       // 自身下注trx
	SelfBetUsdt                  float64 `gorm:"column:SelfBetUsdt;default:0.000000;comment:自身下注usdt" json:"SelfBetUsdt"`                    // 自身下注usdt
	SelfRewardTrx                float64 `gorm:"column:SelfRewardTrx;default:0.000000;comment:自身返奖trx" json:"SelfRewardTrx"`                 // 自身返奖trx
	SelfRewardUsdt               float64 `gorm:"column:SelfRewardUsdt;default:0.000000;comment:自身返奖usdt" json:"SelfRewardUsdt"`              // 自身返奖usdt
	DictLiuSuiTrx                float64 `gorm:"column:DictLiuSuiTrx;default:0.000000;comment:直属下级总流水trx" json:"DictLiuSuiTrx"`              // 直属下级总流水trx
	DictLiuSuiUsdt               float64 `gorm:"column:DictLiuSuiUsdt;default:0.000000;comment:直属下级总流水usdt" json:"DictLiuSuiUsdt"`           // 直属下级总流水usdt
	DictBetTrx                   float64 `gorm:"column:DictBetTrx;default:0.000000;comment:直属下级总下注trx" json:"DictBetTrx"`                    // 直属下级总下注trx
	DictBetUsdt                  float64 `gorm:"column:DictBetUsdt;default:0.000000;comment:直属下级总下注usdt" json:"DictBetUsdt"`                 // 直属下级总下注usdt
	DictRewardTrx                float64 `gorm:"column:DictRewardTrx;default:0.000000;comment:直属下级总返奖trx" json:"DictRewardTrx"`              // 直属下级总返奖trx
	DictRewardUsdt               float64 `gorm:"column:DictRewardUsdt;default:0.000000;comment:直属下级总返奖usdt" json:"DictRewardUsdt"`           // 直属下级总返奖usdt
	TotalCommissionTrx           float64 `gorm:"column:TotalCommissionTrx;default:0.000000;comment:历史总佣金" json:"TotalCommissionTrx"`         // 历史总佣金
	TotalCommissionUsdt          float64 `gorm:"column:TotalCommissionUsdt;default:0.000000;comment:历史总佣金" json:"TotalCommissionUsdt"`       // 历史总佣金
	GetedCommissionTrx           float64 `gorm:"column:GetedCommissionTrx;default:0.000000;comment:已领取佣金" json:"GetedCommissionTrx"`         // 已领取佣金
	GetedCommissionUsdt          float64 `gorm:"column:GetedCommissionUsdt;default:0.000000;comment:已领取佣金" json:"GetedCommissionUsdt"`       // 已领取佣金
	AuditCommissionTrx           float64 `gorm:"column:AuditCommissionTrx;default:0.000000;comment:待审核佣金" json:"AuditCommissionTrx"`         // 待审核佣金
	AuditCommissionUsdt          float64 `gorm:"column:AuditCommissionUsdt;default:0.000000;comment:待审核佣金" json:"AuditCommissionUsdt"`       // 待审核佣金
	AgentLevel                   int32   `gorm:"column:AgentLevel;comment:代理层级" json:"AgentLevel"`                                           // 代理层级
	FineCommissionTrx            float64 `gorm:"column:FineCommissionTrx;default:0.000000;comment:审核拒绝,罚没佣金" json:"FineCommissionTrx"`       // 审核拒绝,罚没佣金
	FineCommissionUsdt           float64 `gorm:"column:FineCommissionUsdt;default:0.000000;comment:审核拒绝,罚没佣金" json:"FineCommissionUsdt"`     // 审核拒绝,罚没佣金
	CanGetCommissionTrx          float64 `gorm:"column:CanGetCommissionTrx;default:0.000000;comment:可领取佣金trx" json:"CanGetCommissionTrx"`    // 可领取佣金trx
	CanGetCommissionUsdt         float64 `gorm:"column:CanGetCommissionUsdt;default:0.000000;comment:可领取佣金Usdt" json:"CanGetCommissionUsdt"` // 可领取佣金Usdt
	NewLiuSuiTrx                 float64 `gorm:"column:NewLiuSuiTrx;default:0.000000;comment:新版代理trx业绩,包含直属的业绩" json:"NewLiuSuiTrx"`         // 新版代理trx业绩,包含直属的业绩
	NewDictLiuSuiTrx             float64 `gorm:"column:NewDictLiuSuiTrx;default:0.000000;comment:新版代理trx直属业绩" json:"NewDictLiuSuiTrx"`       // 新版代理trx直属业绩
	NewLiuSuiHaXiRouletteTrx     float64 `gorm:"column:NewLiuSuiHaXiRouletteTrx;default:0.000000" json:"NewLiuSuiHaXiRouletteTrx"`
	NewDictLiuSuiHaXiRouletteTrx float64 `gorm:"column:NewDictLiuSuiHaXiRouletteTrx;default:0.000000" json:"NewDictLiuSuiHaXiRouletteTrx"`
	NewLiuSui                    float64 `gorm:"column:NewLiuSui;default:0.000000;comment:新版代理业绩,包含直属业绩" json:"NewLiuSui"`              // 新版代理业绩,包含直属业绩
	NewLiuSuiDict                float64 `gorm:"column:NewLiuSuiDict;default:0.000000;comment:新版代理直属业绩" json:"NewLiuSuiDict"`           // 新版代理直属业绩
	NewLiuSuiHaXi                float64 `gorm:"column:NewLiuSuiHaXi;default:0.000000;comment:新版代理哈希业绩,包含直属" json:"NewLiuSuiHaXi"`      // 新版代理哈希业绩,包含直属
	NewLiuSuiHaXiDict            float64 `gorm:"column:NewLiuSuiHaXiDict;default:0.000000;comment:新版代理哈希直属业绩" json:"NewLiuSuiHaXiDict"` // 新版代理哈希直属业绩
	NewLiuSuiHaXiRoulette        float64 `gorm:"column:NewLiuSuiHaXiRoulette;default:0.000000" json:"NewLiuSuiHaXiRoulette"`
	NewLiuSuiHaXiRouletteDict    float64 `gorm:"column:NewLiuSuiHaXiRouletteDict;default:0.000000" json:"NewLiuSuiHaXiRouletteDict"`
	NewLiuSuiLottery             float64 `gorm:"column:NewLiuSuiLottery;default:0.000000" json:"NewLiuSuiLottery"`
	NewLiuSuiLotteryDict         float64 `gorm:"column:NewLiuSuiLotteryDict;default:0.000000" json:"NewLiuSuiLotteryDict"`
	NewLiuSuiLowLottery          float64 `gorm:"column:NewLiuSuiLowLottery;default:0.000000" json:"NewLiuSuiLowLottery"`
	NewLiuSuiLowLotteryDict      float64 `gorm:"column:NewLiuSuiLowLotteryDict;default:0.000000" json:"NewLiuSuiLowLotteryDict"`
	NewLiuSuiLiuHeLottery        float64 `gorm:"column:NewLiuSuiLiuHeLottery;default:0.000000" json:"NewLiuSuiLiuHeLottery"`
	NewLiuSuiLiuHeLotteryDict    float64 `gorm:"column:NewLiuSuiLiuHeLotteryDict;default:0.000000" json:"NewLiuSuiLiuHeLotteryDict"`
	NewLiuSuiQiPai               float64 `gorm:"column:NewLiuSuiQiPai;default:0.000000" json:"NewLiuSuiQiPai"`
	NewLiuSuiQiPaiDict           float64 `gorm:"column:NewLiuSuiQiPaiDict;default:0.000000" json:"NewLiuSuiQiPaiDict"`
	NewLiuSuiDianZhi             float64 `gorm:"column:NewLiuSuiDianZhi;default:0.000000" json:"NewLiuSuiDianZhi"`
	NewLiuSuiDianZhiDict         float64 `gorm:"column:NewLiuSuiDianZhiDict;default:0.000000" json:"NewLiuSuiDianZhiDict"`
	NewLiuSuiXiaoYouXi           float64 `gorm:"column:NewLiuSuiXiaoYouXi;default:0.000000" json:"NewLiuSuiXiaoYouXi"`
	NewLiuSuiXiaoYouXiDict       float64 `gorm:"column:NewLiuSuiXiaoYouXiDict;default:0.000000" json:"NewLiuSuiXiaoYouXiDict"`
	NewLiuSuiCryptoMarket        float64 `gorm:"column:NewLiuSuiCryptoMarket;default:0.000000" json:"NewLiuSuiCryptoMarket"`
	NewLiuSuiCryptoMarketDict    float64 `gorm:"column:NewLiuSuiCryptoMarketDict;default:0.000000" json:"NewLiuSuiCryptoMarketDict"`
	NewLiuSuiLive                float64 `gorm:"column:NewLiuSuiLive;default:0.000000" json:"NewLiuSuiLive"`
	NewLiuSuiLiveDict            float64 `gorm:"column:NewLiuSuiLiveDict;default:0.000000" json:"NewLiuSuiLiveDict"`
	NewLiuSuiSport               float64 `gorm:"column:NewLiuSuiSport;default:0.000000" json:"NewLiuSuiSport"`
	NewLiuSuiSportDict           float64 `gorm:"column:NewLiuSuiSportDict;default:0.000000" json:"NewLiuSuiSportDict"`
	NewLiuSuiTexas               float64 `gorm:"column:NewLiuSuiTexas;default:0.000000" json:"NewLiuSuiTexas"`
	NewLiuSuiTexasDict           float64 `gorm:"column:NewLiuSuiTexasDict;default:0.000000" json:"NewLiuSuiTexasDict"`
	NewCommissionTrx             float64 `gorm:"column:NewCommissionTrx;default:0.000000;comment:历史总佣金trx" json:"NewCommissionTrx"`                        // 历史总佣金trx
	NewCommission                float64 `gorm:"column:NewCommission;default:0.000000;comment:历史总佣金" json:"NewCommission"`                                 // 历史总佣金
	AgentType                    int32   `gorm:"column:AgentType;comment:代理类型 1无,2无限代,3三级代" json:"AgentType"`                                              // 代理类型 1无,2无限代,3三级代
	TotalCommissionTrxT1         float64 `gorm:"column:TotalCommissionTrx_t1;default:0.000000;comment:独立代理—历史总佣金" json:"TotalCommissionTrx_t1"`            // 独立代理—历史总佣金
	FineCommissionTrxT1          float64 `gorm:"column:FineCommissionTrx_t1;default:0.000000;comment:独立代理-审核拒绝,罚没佣金" json:"FineCommissionTrx_t1"`          // 独立代理-审核拒绝,罚没佣金
	AvailableCommissionTrxT1     float64 `gorm:"column:AvailableCommissionTrx_t1;default:0.000000;comment:独立代理-可领取佣金trx" json:"AvailableCommissionTrx_t1"` // 独立代理-可领取佣金trx
	BackCommissionTrxT1          float64 `gorm:"column:BackCommissionTrx_t1;default:0.000000;comment:独立代理-退回佣金" json:"BackCommissionTrx_t1"`               // 独立代理-退回佣金
	GetedCommissionTrxT1         float64 `gorm:"column:GetedCommissionTrx_t1;default:0.000000;comment:独立代理-已领取佣金" json:"GetedCommissionTrx_t1"`            // 独立代理-已领取佣金
	TotalCommissionUsdtT1        float64 `gorm:"column:TotalCommissionUsdt_t1;default:0.000000" json:"TotalCommissionUsdt_t1"`
	FineCommissionUsdtT1         float64 `gorm:"column:FineCommissionUsdt_t1;default:0.000000" json:"FineCommissionUsdt_t1"`
	AvailableCommissionUsdtT1    float64 `gorm:"column:AvailableCommissionUsdt_t1;default:0.000000" json:"AvailableCommissionUsdt_t1"`
	BackCommissionUsdtT1         float64 `gorm:"column:BackCommissionUsdt_t1;default:0.000000" json:"BackCommissionUsdt_t1"`
	GetedCommissionUsdtT1        float64 `gorm:"column:GetedCommissionUsdt_t1;default:0.000000" json:"GetedCommissionUsdt_t1"`
}

// TableName XAgent's table name
func (*XAgent) TableName() string {
	return TableNameXAgent
}
