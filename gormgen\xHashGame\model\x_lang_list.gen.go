// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXLangList = "x_lang_list"

// XLangList mapped from table <x_lang_list>
type XLangList struct {
	ID                int32     `gorm:"column:Id;primaryKey;comment:前端语言Id" json:"Id"`                  // 前端语言Id
	GameSort          string    `gorm:"column:GameSort;comment:游戏大类排序" json:"GameSort"`                 // 游戏大类排序
	GameSortEx        string    `gorm:"column:GameSortEx;comment:游戏小类排序" json:"GameSortEx"`             // 游戏小类排序
	LangName          string    `gorm:"column:LangName;not null;comment:语言名称" json:"LangName"`          // 语言名称
	LangAlisa         string    `gorm:"column:LangAlisa;primaryKey;comment:语言简写" json:"LangAlisa"`      // 语言简写
	State             int32     `gorm:"column:State;not null;default:1;comment:1:开启 2:关闭" json:"State"` // 1:开启 2:关闭
	CreateTime        time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	UpdateTime        time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP" json:"UpdateTime"`
	SportTarget       string    `gorm:"column:SportTarget;comment:体育跳转" json:"SportTarget"`               // 体育跳转
	LoginRegisterType string    `gorm:"column:LoginRegisterType;comment:登录注册类型" json:"LoginRegisterType"` // 登录注册类型
}

// TableName XLangList's table name
func (*XLangList) TableName() string {
	return TableNameXLangList
}
