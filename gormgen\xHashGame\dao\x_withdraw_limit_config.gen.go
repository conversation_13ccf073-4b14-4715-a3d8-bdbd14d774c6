// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXWithdrawLimitConfig(db *gorm.DB, opts ...gen.DOOption) xWithdrawLimitConfig {
	_xWithdrawLimitConfig := xWithdrawLimitConfig{}

	_xWithdrawLimitConfig.xWithdrawLimitConfigDo.UseDB(db, opts...)
	_xWithdrawLimitConfig.xWithdrawLimitConfigDo.UseModel(&model.XWithdrawLimitConfig{})

	tableName := _xWithdrawLimitConfig.xWithdrawLimitConfigDo.TableName()
	_xWithdrawLimitConfig.ALL = field.NewAsterisk(tableName)
	_xWithdrawLimitConfig.ID = field.NewInt32(tableName, "Id")
	_xWithdrawLimitConfig.SellerID = field.NewInt32(tableName, "SellerId")
	_xWithdrawLimitConfig.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xWithdrawLimitConfig.Type = field.NewInt32(tableName, "Type")
	_xWithdrawLimitConfig.Status = field.NewInt32(tableName, "Status")
	_xWithdrawLimitConfig.MaxAmountPerDay = field.NewFloat64(tableName, "MaxAmountPerDay")
	_xWithdrawLimitConfig.MaxCountPerDay = field.NewInt32(tableName, "MaxCountPerDay")
	_xWithdrawLimitConfig.FeeFreeQuotaPerDay = field.NewFloat64(tableName, "FeeFreeQuotaPerDay")
	_xWithdrawLimitConfig.FeeFreeCountPerDay = field.NewInt32(tableName, "FeeFreeCountPerDay")
	_xWithdrawLimitConfig.FeePercent = field.NewFloat64(tableName, "FeePercent")
	_xWithdrawLimitConfig.MaxAmountEveryTime = field.NewFloat64(tableName, "MaxAmountEveryTime")
	_xWithdrawLimitConfig.VipLevel = field.NewInt32(tableName, "VipLevel")
	_xWithdrawLimitConfig.UserID = field.NewInt32(tableName, "UserId")
	_xWithdrawLimitConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xWithdrawLimitConfig.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xWithdrawLimitConfig.fillFieldMap()

	return _xWithdrawLimitConfig
}

// xWithdrawLimitConfig 提款限制表
type xWithdrawLimitConfig struct {
	xWithdrawLimitConfigDo xWithdrawLimitConfigDo

	ALL                field.Asterisk
	ID                 field.Int32   // 自增id
	SellerID           field.Int32   // 运营商
	ChannelID          field.Int32   // 渠道商
	Type               field.Int32   // 配置类型(1:全局 2:VIP 3:代理 4:个人)
	Status             field.Int32   // 状态（1开启 2关闭）
	MaxAmountPerDay    field.Float64 // 单日提款金额上限
	MaxCountPerDay     field.Int32   // 单日提款次数上限
	FeeFreeQuotaPerDay field.Float64 // 单日免手续费额度
	FeeFreeCountPerDay field.Int32   // 单日免手续费提款次数
	FeePercent         field.Float64 // 超出后手续费%
	MaxAmountEveryTime field.Float64 // 单次提款上限
	VipLevel           field.Int32   // vip等级
	UserID             field.Int32   // 玩家id
	CreateTime         field.Time    // 创建时间
	UpdateTime         field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xWithdrawLimitConfig) Table(newTableName string) *xWithdrawLimitConfig {
	x.xWithdrawLimitConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xWithdrawLimitConfig) As(alias string) *xWithdrawLimitConfig {
	x.xWithdrawLimitConfigDo.DO = *(x.xWithdrawLimitConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xWithdrawLimitConfig) updateTableName(table string) *xWithdrawLimitConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Type = field.NewInt32(table, "Type")
	x.Status = field.NewInt32(table, "Status")
	x.MaxAmountPerDay = field.NewFloat64(table, "MaxAmountPerDay")
	x.MaxCountPerDay = field.NewInt32(table, "MaxCountPerDay")
	x.FeeFreeQuotaPerDay = field.NewFloat64(table, "FeeFreeQuotaPerDay")
	x.FeeFreeCountPerDay = field.NewInt32(table, "FeeFreeCountPerDay")
	x.FeePercent = field.NewFloat64(table, "FeePercent")
	x.MaxAmountEveryTime = field.NewFloat64(table, "MaxAmountEveryTime")
	x.VipLevel = field.NewInt32(table, "VipLevel")
	x.UserID = field.NewInt32(table, "UserId")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xWithdrawLimitConfig) WithContext(ctx context.Context) *xWithdrawLimitConfigDo {
	return x.xWithdrawLimitConfigDo.WithContext(ctx)
}

func (x xWithdrawLimitConfig) TableName() string { return x.xWithdrawLimitConfigDo.TableName() }

func (x xWithdrawLimitConfig) Alias() string { return x.xWithdrawLimitConfigDo.Alias() }

func (x xWithdrawLimitConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xWithdrawLimitConfigDo.Columns(cols...)
}

func (x *xWithdrawLimitConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xWithdrawLimitConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 15)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Type"] = x.Type
	x.fieldMap["Status"] = x.Status
	x.fieldMap["MaxAmountPerDay"] = x.MaxAmountPerDay
	x.fieldMap["MaxCountPerDay"] = x.MaxCountPerDay
	x.fieldMap["FeeFreeQuotaPerDay"] = x.FeeFreeQuotaPerDay
	x.fieldMap["FeeFreeCountPerDay"] = x.FeeFreeCountPerDay
	x.fieldMap["FeePercent"] = x.FeePercent
	x.fieldMap["MaxAmountEveryTime"] = x.MaxAmountEveryTime
	x.fieldMap["VipLevel"] = x.VipLevel
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xWithdrawLimitConfig) clone(db *gorm.DB) xWithdrawLimitConfig {
	x.xWithdrawLimitConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xWithdrawLimitConfig) replaceDB(db *gorm.DB) xWithdrawLimitConfig {
	x.xWithdrawLimitConfigDo.ReplaceDB(db)
	return x
}

type xWithdrawLimitConfigDo struct{ gen.DO }

func (x xWithdrawLimitConfigDo) Debug() *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xWithdrawLimitConfigDo) WithContext(ctx context.Context) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xWithdrawLimitConfigDo) ReadDB() *xWithdrawLimitConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xWithdrawLimitConfigDo) WriteDB() *xWithdrawLimitConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xWithdrawLimitConfigDo) Session(config *gorm.Session) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xWithdrawLimitConfigDo) Clauses(conds ...clause.Expression) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xWithdrawLimitConfigDo) Returning(value interface{}, columns ...string) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xWithdrawLimitConfigDo) Not(conds ...gen.Condition) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xWithdrawLimitConfigDo) Or(conds ...gen.Condition) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xWithdrawLimitConfigDo) Select(conds ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xWithdrawLimitConfigDo) Where(conds ...gen.Condition) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xWithdrawLimitConfigDo) Order(conds ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xWithdrawLimitConfigDo) Distinct(cols ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xWithdrawLimitConfigDo) Omit(cols ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xWithdrawLimitConfigDo) Join(table schema.Tabler, on ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xWithdrawLimitConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xWithdrawLimitConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xWithdrawLimitConfigDo) Group(cols ...field.Expr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xWithdrawLimitConfigDo) Having(conds ...gen.Condition) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xWithdrawLimitConfigDo) Limit(limit int) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xWithdrawLimitConfigDo) Offset(offset int) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xWithdrawLimitConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xWithdrawLimitConfigDo) Unscoped() *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xWithdrawLimitConfigDo) Create(values ...*model.XWithdrawLimitConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xWithdrawLimitConfigDo) CreateInBatches(values []*model.XWithdrawLimitConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xWithdrawLimitConfigDo) Save(values ...*model.XWithdrawLimitConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xWithdrawLimitConfigDo) First() (*model.XWithdrawLimitConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawLimitConfig), nil
	}
}

func (x xWithdrawLimitConfigDo) Take() (*model.XWithdrawLimitConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawLimitConfig), nil
	}
}

func (x xWithdrawLimitConfigDo) Last() (*model.XWithdrawLimitConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawLimitConfig), nil
	}
}

func (x xWithdrawLimitConfigDo) Find() ([]*model.XWithdrawLimitConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XWithdrawLimitConfig), err
}

func (x xWithdrawLimitConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XWithdrawLimitConfig, err error) {
	buf := make([]*model.XWithdrawLimitConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xWithdrawLimitConfigDo) FindInBatches(result *[]*model.XWithdrawLimitConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xWithdrawLimitConfigDo) Attrs(attrs ...field.AssignExpr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xWithdrawLimitConfigDo) Assign(attrs ...field.AssignExpr) *xWithdrawLimitConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xWithdrawLimitConfigDo) Joins(fields ...field.RelationField) *xWithdrawLimitConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xWithdrawLimitConfigDo) Preload(fields ...field.RelationField) *xWithdrawLimitConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xWithdrawLimitConfigDo) FirstOrInit() (*model.XWithdrawLimitConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawLimitConfig), nil
	}
}

func (x xWithdrawLimitConfigDo) FirstOrCreate() (*model.XWithdrawLimitConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XWithdrawLimitConfig), nil
	}
}

func (x xWithdrawLimitConfigDo) FindByPage(offset int, limit int) (result []*model.XWithdrawLimitConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xWithdrawLimitConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xWithdrawLimitConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xWithdrawLimitConfigDo) Delete(models ...*model.XWithdrawLimitConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xWithdrawLimitConfigDo) withDO(do gen.Dao) *xWithdrawLimitConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
