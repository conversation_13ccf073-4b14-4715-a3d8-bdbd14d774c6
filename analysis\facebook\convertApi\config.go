package convertApi

import (
	"encoding/json"
	"fmt"
	"sync"
	"xserver/server"

	"github.com/beego/beego/logs"
)

type Maidian struct {
	FB struct {
		BackEnd struct {
			PixelID      string `json:"PIXEL_ID"`
			ACCESS_TOKEN string `json:"ACCESS_TOKEN"`
		} `json:"BackEnd"`
		Front struct {
			PixelID string `json:"PIXEL_ID"`
		} `json:"Front"`
	} `json:"FB"`
}

var mu sync.Mutex

func loadConfig(host string) {
	mu.Lock()
	defer mu.Unlock()
	clients = make(map[string]*Client)

	xChannelHost := server.DaoxHashGame().XChannelHost
	hostConfig, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	if hostConfig == nil {
		logs.Error("暂无FB埋点配置！")
		return
	}

	if hostConfig.Maidian == "" {
		logs.Error("暂无FB埋点配置")
		return
	}

	var cfg Maidian

	err := json.Unmarshal([]byte(hostConfig.Maidian), &cfg)
	if err != nil {
		logs.Error(err)
		return
	}

	api := fmt.Sprintf("https://graph.facebook.com/v20.0/%v/events?access_token=%v", cfg.FB.BackEnd.PixelID, cfg.FB.BackEnd.ACCESS_TOKEN)

	clients[host] = &Client{
		host: host,
		api:  api,
	}
	return
}
