// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTgRobotLogAction = "x_tg_robot_log_action"

// XTgRobotLogAction 机器人行为日志
type XTgRobotLogAction struct {
	ID             int64     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:记录Id" json:"Id"`                      // 记录Id
	RobotID        int64     `gorm:"column:RobotId;comment:机器人Id" json:"RobotId"`                                         // 机器人Id
	TgChatID       int64     `gorm:"column:TgChatId;comment: tg用户Id" json:"TgChatId"`                                     //  tg用户Id
	SellerID       int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	ChannelID      int32     `gorm:"column:ChannelId;comment:渠道id" json:"ChannelId"`                                      // 渠道id
	ActionType     int32     `gorm:"column:ActionType;comment:行为分类(1:start 2:注册 3: 领U 4:领T 5:激活)" json:"ActionType"`      // 行为分类(1:start 2:注册 3: 领U 4:领T 5:激活)
	IsInResourceDb int32     `gorm:"column:IsInResourceDb;comment:是否在库用户 0不在库 1在库" json:"IsInResourceDb"`                 // 是否在库用户 0不在库 1在库
	Memo           string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                  // 备注
	RecordTime     time.Time `gorm:"column:RecordTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"RecordTime"` // 记录时间
}

// TableName XTgRobotLogAction's table name
func (*XTgRobotLogAction) TableName() string {
	return TableNameXTgRobotLogAction
}
