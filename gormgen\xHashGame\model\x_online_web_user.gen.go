// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOnlineWebUser = "x_online_web_user"

// XOnlineWebUser mapped from table <x_online_web_user>
type XOnlineWebUser struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Online     int32     `gorm:"column:Online;not null;comment:在线人数" json:"Online"`                                   // 在线人数
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"CreateTime"` // 记录时间
}

// TableName XOnlineWebUser's table name
func (*XOnlineWebUser) TableName() string {
	return TableNameXOnlineWebUser
}
