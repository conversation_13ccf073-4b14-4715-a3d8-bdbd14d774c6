package third_game

// 体育三方重新结算记录
type ThirdSportResettleLog struct {
	Id             int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	UserId         int     `json:"UserId" gorm:"column:UserId"`
	Brand          string  `json:"Brand" gorm:"column:Brand"`
	ThirdId        string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId         string  `json:"GameId" gorm:"column:GameId"`
	GameName       string  `json:"GameName" gorm:"column:GameName"`
	BetAmount      float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount      float64 `json:"WinAmount" gorm:"column:WinAmount"`
	Amount         float64 `json:"Amount" gorm:"column:Amount"`
	ChangeType     int     `json:"ChangeType" gorm:"column:ChangeType"`
	ThirdTime      string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency       string  `json:"Currency" gorm:"column:Currency"`
	RawData        string  `json:"RawData" gorm:"column:RawData"`
	State          int     `json:"State" gorm:"column:State"`
	CreateTime     string  `json:"CreateTime" gorm:"column:CreateTime"`
	BetCtxType     int     `json:"BetCtxType" gorm:"column:BetCtxType"`
	ResettleNumber int     `json:"ResettleNumber" gorm:"column:ResettleNumber"`
	ThirdRefId     string  `json:"ThirdRefId" gorm:"column:ThirdRefId"`
	ResettleTime   *string `json:"ResettleTime" gorm:"column:ResettleTime"`
	CancelTime     *string `json:"CancelTime" gorm:"column:CancelTime"`
	Memo           string  `json:"Memo" gorm:"column:Memo"`
}
