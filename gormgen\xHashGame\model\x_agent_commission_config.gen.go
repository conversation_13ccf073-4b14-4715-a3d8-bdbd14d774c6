// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCommissionConfig = "x_agent_commission_config"

// XAgentCommissionConfig mapped from table <x_agent_commission_config>
type XAgentCommissionConfig struct {
	ID            int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"` // id
	Name          string    `gorm:"column:Name;comment:方案名称" json:"Name"`                         // 方案名称
	ModelID       int32     `gorm:"column:ModelId;default:1;comment:模式Id" json:"ModelId"`         // 模式Id
	Data          string    `gorm:"column:Data;comment:方案数据" json:"Data"`                         // 方案数据
	State         int32     `gorm:"column:State;not null;default:1;comment:1启用,2禁用" json:"State"` // 1启用,2禁用
	CreateTime    time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP" json:"CreateTime"`
	CreateAccount string    `gorm:"column:CreateAccount" json:"CreateAccount"`
	Memo          string    `gorm:"column:Memo;comment:备注" json:"Memo"` // 备注
	UseCount      int32     `gorm:"column:UseCount" json:"UseCount"`
}

// TableName XAgentCommissionConfig's table name
func (*XAgentCommissionConfig) TableName() string {
	return TableNameXAgentCommissionConfig
}
