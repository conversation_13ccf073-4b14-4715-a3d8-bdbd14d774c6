package bigo

const (
	EventName_CompleteRegistration = "ec_register" // 完成注册
	EventName_Purchase             = "ec_purchase" // 完成购买或结账流程
	EventName_AddToCart            = "ec_add_cart" // 添加到购物车
	EventName_FirstRecharge        = "ec_order"    // 首次充值
)

type PostData struct {
	Bbg         string `json:"bbg"`
	PixelId     string `json:"pixel_id"`
	TimestampMs int64  `json:"timestamp_ms"`
	Url         bool   `json:"url"`
	Referer     bool   `json:"referer"`
	Ip          int    `json:"ip"`
	UserAgent   string `json:"user_agent"`
	ExternalId  string `json:"external_id"`
	Email       string `json:"email"`
	PhoneNumber string `json:"phone_number"`
	CookieId    string `json:"cookie_id"`
	Event       Event  `json:"event"`
}

type Event struct {
	EventId      string  `json:"event_id"`
	Monetary     float64 `json:"monetary"`
	Currency     string  `json:"currency"`
	OrigMonetary float64 `json:"orig_monetary"`
}
