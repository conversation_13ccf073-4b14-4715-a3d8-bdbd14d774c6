// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTbBankerConfig(db *gorm.DB, opts ...gen.DOOption) xTbBankerConfig {
	_xTbBankerConfig := xTbBankerConfig{}

	_xTbBankerConfig.xTbBankerConfigDo.UseDB(db, opts...)
	_xTbBankerConfig.xTbBankerConfigDo.UseModel(&model.XTbBankerConfig{})

	tableName := _xTbBankerConfig.xTbBankerConfigDo.TableName()
	_xTbBankerConfig.ALL = field.NewAsterisk(tableName)
	_xTbBankerConfig.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTbBankerConfig.SellerID = field.NewInt32(tableName, "SellerId")
	_xTbBankerConfig.BankerMinTime = field.NewInt32(tableName, "BankerMinTime")
	_xTbBankerConfig.BankerMaxTime = field.NewInt32(tableName, "BankerMaxTime")
	_xTbBankerConfig.BankerMinAmount = field.NewFloat64(tableName, "BankerMinAmount")
	_xTbBankerConfig.BankerTgGroup = field.NewString(tableName, "BankerTgGroup")
	_xTbBankerConfig.Status = field.NewInt32(tableName, "Status")
	_xTbBankerConfig.Memo = field.NewString(tableName, "Memo")
	_xTbBankerConfig.Operator = field.NewString(tableName, "Operator")
	_xTbBankerConfig.OperUserID = field.NewInt32(tableName, "OperUserID")
	_xTbBankerConfig.DeviceType = field.NewInt32(tableName, "DeviceType")
	_xTbBankerConfig.DeviceID = field.NewString(tableName, "DeviceID")
	_xTbBankerConfig.CreateTime = field.NewTime(tableName, "CreateTime")
	_xTbBankerConfig.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xTbBankerConfig.fillFieldMap()

	return _xTbBankerConfig
}

// xTbBankerConfig 渠道上庄配置
type xTbBankerConfig struct {
	xTbBankerConfigDo xTbBankerConfigDo

	ALL             field.Asterisk
	ChannelID       field.Int32   // 渠道id
	SellerID        field.Int32   // 运营商id
	BankerMinTime   field.Int32   // 上庄最小时间(单位：秒)
	BankerMaxTime   field.Int32   // 上庄最大时间(单位：秒)
	BankerMinAmount field.Float64 // 上庄最小金额
	BankerTgGroup   field.String  // 上庄tg群
	Status          field.Int32   // 状态 1开启 2关闭
	Memo            field.String  // 描述
	Operator        field.String  // 操作员
	OperUserID      field.Int32   // 操作员ID
	DeviceType      field.Int32   // 设备类型
	DeviceID        field.String  // 设备ID
	CreateTime      field.Time    // 创建时间
	UpdateTime      field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xTbBankerConfig) Table(newTableName string) *xTbBankerConfig {
	x.xTbBankerConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTbBankerConfig) As(alias string) *xTbBankerConfig {
	x.xTbBankerConfigDo.DO = *(x.xTbBankerConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTbBankerConfig) updateTableName(table string) *xTbBankerConfig {
	x.ALL = field.NewAsterisk(table)
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.BankerMinTime = field.NewInt32(table, "BankerMinTime")
	x.BankerMaxTime = field.NewInt32(table, "BankerMaxTime")
	x.BankerMinAmount = field.NewFloat64(table, "BankerMinAmount")
	x.BankerTgGroup = field.NewString(table, "BankerTgGroup")
	x.Status = field.NewInt32(table, "Status")
	x.Memo = field.NewString(table, "Memo")
	x.Operator = field.NewString(table, "Operator")
	x.OperUserID = field.NewInt32(table, "OperUserID")
	x.DeviceType = field.NewInt32(table, "DeviceType")
	x.DeviceID = field.NewString(table, "DeviceID")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xTbBankerConfig) WithContext(ctx context.Context) *xTbBankerConfigDo {
	return x.xTbBankerConfigDo.WithContext(ctx)
}

func (x xTbBankerConfig) TableName() string { return x.xTbBankerConfigDo.TableName() }

func (x xTbBankerConfig) Alias() string { return x.xTbBankerConfigDo.Alias() }

func (x xTbBankerConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xTbBankerConfigDo.Columns(cols...)
}

func (x *xTbBankerConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTbBankerConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["BankerMinTime"] = x.BankerMinTime
	x.fieldMap["BankerMaxTime"] = x.BankerMaxTime
	x.fieldMap["BankerMinAmount"] = x.BankerMinAmount
	x.fieldMap["BankerTgGroup"] = x.BankerTgGroup
	x.fieldMap["Status"] = x.Status
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["Operator"] = x.Operator
	x.fieldMap["OperUserID"] = x.OperUserID
	x.fieldMap["DeviceType"] = x.DeviceType
	x.fieldMap["DeviceID"] = x.DeviceID
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xTbBankerConfig) clone(db *gorm.DB) xTbBankerConfig {
	x.xTbBankerConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTbBankerConfig) replaceDB(db *gorm.DB) xTbBankerConfig {
	x.xTbBankerConfigDo.ReplaceDB(db)
	return x
}

type xTbBankerConfigDo struct{ gen.DO }

func (x xTbBankerConfigDo) Debug() *xTbBankerConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xTbBankerConfigDo) WithContext(ctx context.Context) *xTbBankerConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTbBankerConfigDo) ReadDB() *xTbBankerConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTbBankerConfigDo) WriteDB() *xTbBankerConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTbBankerConfigDo) Session(config *gorm.Session) *xTbBankerConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTbBankerConfigDo) Clauses(conds ...clause.Expression) *xTbBankerConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTbBankerConfigDo) Returning(value interface{}, columns ...string) *xTbBankerConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTbBankerConfigDo) Not(conds ...gen.Condition) *xTbBankerConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTbBankerConfigDo) Or(conds ...gen.Condition) *xTbBankerConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTbBankerConfigDo) Select(conds ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTbBankerConfigDo) Where(conds ...gen.Condition) *xTbBankerConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTbBankerConfigDo) Order(conds ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTbBankerConfigDo) Distinct(cols ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTbBankerConfigDo) Omit(cols ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTbBankerConfigDo) Join(table schema.Tabler, on ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTbBankerConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTbBankerConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTbBankerConfigDo) Group(cols ...field.Expr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTbBankerConfigDo) Having(conds ...gen.Condition) *xTbBankerConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTbBankerConfigDo) Limit(limit int) *xTbBankerConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTbBankerConfigDo) Offset(offset int) *xTbBankerConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTbBankerConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTbBankerConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTbBankerConfigDo) Unscoped() *xTbBankerConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTbBankerConfigDo) Create(values ...*model.XTbBankerConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTbBankerConfigDo) CreateInBatches(values []*model.XTbBankerConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTbBankerConfigDo) Save(values ...*model.XTbBankerConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTbBankerConfigDo) First() (*model.XTbBankerConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerConfig), nil
	}
}

func (x xTbBankerConfigDo) Take() (*model.XTbBankerConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerConfig), nil
	}
}

func (x xTbBankerConfigDo) Last() (*model.XTbBankerConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerConfig), nil
	}
}

func (x xTbBankerConfigDo) Find() ([]*model.XTbBankerConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTbBankerConfig), err
}

func (x xTbBankerConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTbBankerConfig, err error) {
	buf := make([]*model.XTbBankerConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTbBankerConfigDo) FindInBatches(result *[]*model.XTbBankerConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTbBankerConfigDo) Attrs(attrs ...field.AssignExpr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTbBankerConfigDo) Assign(attrs ...field.AssignExpr) *xTbBankerConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTbBankerConfigDo) Joins(fields ...field.RelationField) *xTbBankerConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTbBankerConfigDo) Preload(fields ...field.RelationField) *xTbBankerConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTbBankerConfigDo) FirstOrInit() (*model.XTbBankerConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerConfig), nil
	}
}

func (x xTbBankerConfigDo) FirstOrCreate() (*model.XTbBankerConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTbBankerConfig), nil
	}
}

func (x xTbBankerConfigDo) FindByPage(offset int, limit int) (result []*model.XTbBankerConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTbBankerConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTbBankerConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTbBankerConfigDo) Delete(models ...*model.XTbBankerConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTbBankerConfigDo) withDO(do gen.Dao) *xTbBankerConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
