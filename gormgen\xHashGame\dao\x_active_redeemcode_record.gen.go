// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveRedeemcodeRecord(db *gorm.DB, opts ...gen.DOOption) xActiveRedeemcodeRecord {
	_xActiveRedeemcodeRecord := xActiveRedeemcodeRecord{}

	_xActiveRedeemcodeRecord.xActiveRedeemcodeRecordDo.UseDB(db, opts...)
	_xActiveRedeemcodeRecord.xActiveRedeemcodeRecordDo.UseModel(&model.XActiveRedeemcodeRecord{})

	tableName := _xActiveRedeemcodeRecord.xActiveRedeemcodeRecordDo.TableName()
	_xActiveRedeemcodeRecord.ALL = field.NewAsterisk(tableName)
	_xActiveRedeemcodeRecord.ID = field.NewInt32(tableName, "Id")
	_xActiveRedeemcodeRecord.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveRedeemcodeRecord.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xActiveRedeemcodeRecord.UserID = field.NewInt32(tableName, "UserId")
	_xActiveRedeemcodeRecord.RedeemCode = field.NewString(tableName, "RedeemCode")
	_xActiveRedeemcodeRecord.UseCount = field.NewString(tableName, "UseCount")
	_xActiveRedeemcodeRecord.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xActiveRedeemcodeRecord.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveRedeemcodeRecord.ActiveName = field.NewString(tableName, "ActiveName")
	_xActiveRedeemcodeRecord.RedeemTime = field.NewTime(tableName, "RedeemTime")
	_xActiveRedeemcodeRecord.CreateTime = field.NewTime(tableName, "CreateTime")
	_xActiveRedeemcodeRecord.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xActiveRedeemcodeRecord.fillFieldMap()

	return _xActiveRedeemcodeRecord
}

// xActiveRedeemcodeRecord 活动兑换码记录表
type xActiveRedeemcodeRecord struct {
	xActiveRedeemcodeRecordDo xActiveRedeemcodeRecordDo

	ALL          field.Asterisk
	ID           field.Int32   // 记录ID
	SellerID     field.Int32   // 运营商
	ChannelID    field.Int32   // 渠道
	UserID       field.Int32   // 玩家ID
	RedeemCode   field.String  // 兑换码
	UseCount     field.String  // 此码可兑次数
	RewardAmount field.Float64 // 获得奖励金额
	ActiveID     field.Int32   // 对应活动ID
	ActiveName   field.String  // 对应活动名称
	RedeemTime   field.Time    // 兑换时间
	CreateTime   field.Time    // 创建时间
	UpdateTime   field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xActiveRedeemcodeRecord) Table(newTableName string) *xActiveRedeemcodeRecord {
	x.xActiveRedeemcodeRecordDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveRedeemcodeRecord) As(alias string) *xActiveRedeemcodeRecord {
	x.xActiveRedeemcodeRecordDo.DO = *(x.xActiveRedeemcodeRecordDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveRedeemcodeRecord) updateTableName(table string) *xActiveRedeemcodeRecord {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.RedeemCode = field.NewString(table, "RedeemCode")
	x.UseCount = field.NewString(table, "UseCount")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.ActiveName = field.NewString(table, "ActiveName")
	x.RedeemTime = field.NewTime(table, "RedeemTime")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xActiveRedeemcodeRecord) WithContext(ctx context.Context) *xActiveRedeemcodeRecordDo {
	return x.xActiveRedeemcodeRecordDo.WithContext(ctx)
}

func (x xActiveRedeemcodeRecord) TableName() string { return x.xActiveRedeemcodeRecordDo.TableName() }

func (x xActiveRedeemcodeRecord) Alias() string { return x.xActiveRedeemcodeRecordDo.Alias() }

func (x xActiveRedeemcodeRecord) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveRedeemcodeRecordDo.Columns(cols...)
}

func (x *xActiveRedeemcodeRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveRedeemcodeRecord) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 12)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["RedeemCode"] = x.RedeemCode
	x.fieldMap["UseCount"] = x.UseCount
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["ActiveName"] = x.ActiveName
	x.fieldMap["RedeemTime"] = x.RedeemTime
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xActiveRedeemcodeRecord) clone(db *gorm.DB) xActiveRedeemcodeRecord {
	x.xActiveRedeemcodeRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveRedeemcodeRecord) replaceDB(db *gorm.DB) xActiveRedeemcodeRecord {
	x.xActiveRedeemcodeRecordDo.ReplaceDB(db)
	return x
}

type xActiveRedeemcodeRecordDo struct{ gen.DO }

func (x xActiveRedeemcodeRecordDo) Debug() *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveRedeemcodeRecordDo) WithContext(ctx context.Context) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveRedeemcodeRecordDo) ReadDB() *xActiveRedeemcodeRecordDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveRedeemcodeRecordDo) WriteDB() *xActiveRedeemcodeRecordDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveRedeemcodeRecordDo) Session(config *gorm.Session) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveRedeemcodeRecordDo) Clauses(conds ...clause.Expression) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveRedeemcodeRecordDo) Returning(value interface{}, columns ...string) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveRedeemcodeRecordDo) Not(conds ...gen.Condition) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveRedeemcodeRecordDo) Or(conds ...gen.Condition) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveRedeemcodeRecordDo) Select(conds ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveRedeemcodeRecordDo) Where(conds ...gen.Condition) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveRedeemcodeRecordDo) Order(conds ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveRedeemcodeRecordDo) Distinct(cols ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveRedeemcodeRecordDo) Omit(cols ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveRedeemcodeRecordDo) Join(table schema.Tabler, on ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveRedeemcodeRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveRedeemcodeRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveRedeemcodeRecordDo) Group(cols ...field.Expr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveRedeemcodeRecordDo) Having(conds ...gen.Condition) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveRedeemcodeRecordDo) Limit(limit int) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveRedeemcodeRecordDo) Offset(offset int) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveRedeemcodeRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveRedeemcodeRecordDo) Unscoped() *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveRedeemcodeRecordDo) Create(values ...*model.XActiveRedeemcodeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveRedeemcodeRecordDo) CreateInBatches(values []*model.XActiveRedeemcodeRecord, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveRedeemcodeRecordDo) Save(values ...*model.XActiveRedeemcodeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveRedeemcodeRecordDo) First() (*model.XActiveRedeemcodeRecord, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRedeemcodeRecord), nil
	}
}

func (x xActiveRedeemcodeRecordDo) Take() (*model.XActiveRedeemcodeRecord, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRedeemcodeRecord), nil
	}
}

func (x xActiveRedeemcodeRecordDo) Last() (*model.XActiveRedeemcodeRecord, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRedeemcodeRecord), nil
	}
}

func (x xActiveRedeemcodeRecordDo) Find() ([]*model.XActiveRedeemcodeRecord, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveRedeemcodeRecord), err
}

func (x xActiveRedeemcodeRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveRedeemcodeRecord, err error) {
	buf := make([]*model.XActiveRedeemcodeRecord, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveRedeemcodeRecordDo) FindInBatches(result *[]*model.XActiveRedeemcodeRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveRedeemcodeRecordDo) Attrs(attrs ...field.AssignExpr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveRedeemcodeRecordDo) Assign(attrs ...field.AssignExpr) *xActiveRedeemcodeRecordDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveRedeemcodeRecordDo) Joins(fields ...field.RelationField) *xActiveRedeemcodeRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveRedeemcodeRecordDo) Preload(fields ...field.RelationField) *xActiveRedeemcodeRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveRedeemcodeRecordDo) FirstOrInit() (*model.XActiveRedeemcodeRecord, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRedeemcodeRecord), nil
	}
}

func (x xActiveRedeemcodeRecordDo) FirstOrCreate() (*model.XActiveRedeemcodeRecord, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRedeemcodeRecord), nil
	}
}

func (x xActiveRedeemcodeRecordDo) FindByPage(offset int, limit int) (result []*model.XActiveRedeemcodeRecord, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveRedeemcodeRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveRedeemcodeRecordDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveRedeemcodeRecordDo) Delete(models ...*model.XActiveRedeemcodeRecord) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveRedeemcodeRecordDo) withDO(do gen.Dao) *xActiveRedeemcodeRecordDo {
	x.DO = *do.(*gen.DO)
	return x
}
