package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/controller/active"
	"xserver/controller/xemail"
	xHashGameModel "xserver/gormgen/xHashGame/model"
	"xserver/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/spf13/viper"
	"github.com/zhms/xgo/xgo"
)

const App_version = "1.0.9"

type AppController struct {
	sms_url     string
	sms_account string
	sms_key     string
	sms_path    string

	cn_sms_url     string
	cn_sms_account string
	cn_sms_key     string
	cn_sms_path    string

	email_url                string
	email_apikey             string
	email_secret             string
	email_appid              string
	email_from_email_address string

	langmap map[int]string

	sms_debug int

	flyEmailSiteID      string
	flyEmailTrackAPIKey string
	flyEmailAppAPIKey   string
}

var majorgamesort = `[{"id":1,"state":1,"name":"哈希竞猜"},{"id":2,"state":1,"name":"电子游戏"},{"id":3,"state":1,"name":"棋牌游戏"},{"id":4,"state":1,"name":"真人视讯"},{"id":5,"state":1,"name":"哈希电子"},{"id":6,"state":1,"name":"哈希彩票"},{"id":7,"state":1,"name":"体育竞技"}]`
var minorgamesort = `{"1":[{"id": 2,"name": "哈希单双","ename": "Hash odd and even","state": 1},{"id": 1,"name": "哈希大小","ename": "Hash size","state": 1},{"id": 5,"name": "哈希牛牛","ename": "Hash cow cow","state": 1},{"id": 12,"name": "和值单双","ename": "sum value odd or even","state": 1},{"id": 11,"name": "和值大小","ename": "sum size","state": 1},{"id": 4,"name": "幸运庄闲","ename": "Lucky banker","state": 1},{"id": 102,"name": "一分单双","ename": "One point odd and even","state": 1},{"id": 101,"name": "一分大小","ename": "one minute size","state": 1},{"id": 105,"name": "一分牛牛","ename": "One point cow cow","state": 1},{"id": 131,"name": "三分大小","ename": "Three-quarter size","state": 1},{"id": 132,"name": "三分单双","ename": "Three point odd or even","state": 1},{"id": 133,"name": "三分幸运","ename": "Three points lucky","state": 1},{"id": 134,"name": "三分庄闲","ename": "Three points of village leisure","state": 1},{"id": 135,"name": "三分牛牛","ename": "three points bull","state": 1},{"id": 3,"name": "幸运哈希","ename": "lucky hash","state": 1},{"id": 103,"name": "一分幸运","ename": "One point of luck","state": 1},{"id": 104,"name": "一分庄闲","ename": "One point of leisure","state": 1},{"id": 6,"name": "哈希快三","ename": "Hash fast three","state": 2},{"id": 7,"name": "哈希PK10","ename": "HashPK10","state": 2}],"2":[{"id":"gfg","name":"gfg电子","state":1},{"id":"pg","name":"pg电子","state":1},{"id":"pp","name":"pp电子","state":1}],"4":[{"id":"evo","name":"evo真人","state":1},{"id":"ag","name":"ag真人","state":1},{"id":"wm","name":"wm真人","state":2},{"id":"astar","name":"astar真人","state":1}],"5":[{"id":"spribe","name":"spribe","state":1},{"id":"xyx","name":"xyx","state":1}],"7":[{"id":"up","name":"up体育","state":1},{"id":"easybet","name":"easybet体育","state":1},{"id":"three_up","name":"三昇体育","state":1}]}`
var majorgamesortnew = `[{"id":2,"name":"哈希","state":1},{"id":3,"name":"电子","state":1},{"id":4,"name":"区块链","state":1},{"id":5,"name":"真人","state":1},{"id":6,"name":"棋牌","state":1},{"id":7,"name":"体育","state":1}]`

func (c *AppController) Init() {
	c.sms_url = viper.GetString("sms.url")
	c.sms_account = viper.GetString("sms.account")
	c.sms_key = viper.GetString("sms.key")
	c.sms_path = viper.GetString("sms.path")

	c.cn_sms_url = viper.GetString("cn_sms.url")
	c.cn_sms_account = viper.GetString("cn_sms.account")
	c.cn_sms_key = viper.GetString("cn_sms.key")
	c.cn_sms_path = viper.GetString("cn_sms.path")

	c.email_url = viper.GetString("email.url")
	c.email_apikey = viper.GetString("email.apikey")
	c.email_secret = viper.GetString("email.secret")
	c.email_appid = viper.GetString("email.appid")
	c.email_from_email_address = viper.GetString("email.from_email_address")

	c.sms_debug = viper.GetInt("sms_debug")

	c.flyEmailSiteID = "66f1438226c305d4fa2f"
	c.flyEmailTrackAPIKey = "822c066cf3844597c261"
	c.flyEmailAppAPIKey = "8fbbad68fa41f437a9e93eddd39ed3c5"

	server.Http().PostByNoAuthMayUserToken("/api/app/info", c.info)
	server.Http().PostNoAuth("/api/app/recharge_config", c.recharge_config)
	server.Http().PostNoAuth("/api/app/send_sms", c.send_sms)
	server.Http().PostNoAuth("/api/app/send_email", c.send_email)
}

func (c *AppController) info(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		SellerId  int `validate:"required"`
		Lang      string
		LangId    int
		Host      string
		AgentCode string
	}{}

	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if reqdata.Host != "" {
		host = reqdata.Host
	}

	ChannelId, SellerId := server.GetChannel(ctx, host)

	data, err := getGameEntranceInfo(ctx, SellerId, reqdata.Lang, host, reqdata.LangId, reqdata.AgentCode)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	//var data xgo.XMap

	xChannelHost := server.DaoxHashGame().XChannelHost
	tbChannelHost, err := xChannelHost.WithContext(ctx.Gin()).Where(xChannelHost.Host.Eq(host)).First()

	langListDao := server.DaoxHashGame().XLangList
	langListDb := langListDao.WithContext(nil)
	langListInfos, _ := langListDb.Where(langListDao.ID.Eq(int32(reqdata.LangId))).First()

	xSeller := server.DaoxHashGame().XSeller
	seller, err := xSeller.WithContext(nil).Where(xSeller.SellerID.Eq(int32(SellerId))).First()

	// 国家是否屏蔽
	country := ctx.Gin().Request.Header.Get("country")
	if strings.Contains(tbChannelHost.CountryList, country) {
		data.Set("IsBank", false)
	} else {
		data.Set("IsBank", true)
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 获取社交媒体多语言数据
	// 定义嵌套 map
	var socialLinksResults map[string]map[string]string

	var sellerSocialLinksResults map[string]map[string]string
	var channelSocialLinksResults map[string]map[string]string

	channelSocialLinks := tbChannelHost.SocialLinks
	sellerSocialLinks := seller.SocialLinks
	//if socialLinks == "" {
	//	socialLinks = seller.SocialLinks
	//}

	// 解析 JSON
	//_ = json.Unmarshal([]byte(socialLinks), &socialLinksResults)
	_ = json.Unmarshal([]byte(channelSocialLinks), &channelSocialLinksResults)
	_ = json.Unmarshal([]byte(sellerSocialLinks), &sellerSocialLinksResults)
	// strLang := strconv.Itoa(reqdata.LangId)
	socialLinksResults = channelSocialLinksResults
	if socialLinksResults != nil {
		for l := 1; l < 15; l++ {
			strLang := strconv.Itoa(l)

			for i := 1; i < 9; i++ {
				itemStrKey := strconv.Itoa(i)

				if socialLinksResults[strLang][itemStrKey] == "" {
					socialLinksResults[strLang][itemStrKey] = sellerSocialLinksResults[strLang][itemStrKey]
				}

				// if socialLinksResults[strLang][itemStrKey] == "" && (reqdata.LangId == 1 || reqdata.LangId == 13) {
				// 	socialLinksResults[strLang][itemStrKey] = socialLinksResults["1"][itemStrKey]
				// } else {
				// 	socialLinksResults[strLang][itemStrKey] = socialLinksResults["2"][itemStrKey]
				// }

				if socialLinksResults[strLang][itemStrKey] == "" && (l > 2 && l < 13 || l > 13) {
					socialLinksResults[strLang][itemStrKey] = socialLinksResults["2"][itemStrKey]
				}

				if socialLinksResults[strLang][itemStrKey] == "" && l == 13 {
					socialLinksResults[strLang][itemStrKey] = socialLinksResults["1"][itemStrKey]
				}

			}
		}
	}

	socialLinkResult, _ := json.Marshal(socialLinksResults[strconv.Itoa(reqdata.LangId)])

	var loginRegisterType string
	if langListInfos != nil {
		loginRegisterType = langListInfos.LoginRegisterType
	} else {
		loginRegisterType = `{"email": 1, "phone": 0, "account": 1}`
	}

	data.Set("TJ51Id", tbChannelHost.TJ51ID)
	data.Set("SocialLinks", string(socialLinkResult))
	data.Set("Maidian", tbChannelHost.Maidian)
	data.Set("Webclip", tbChannelHost.Webclip)
	data.Set("IsAIEnable", tbChannelHost.IsAIEnable)
	data.Set("LoginRegisterType", loginRegisterType)
	data.Set("ThirdAuth", seller.ThirdAuth)
	data.Set("SportTarget", "")

	var sportTargetStr string

	// 添加对tbChannelHost的空值检查
	if tbChannelHost != nil && tbChannelHost.SportTarget != "" && tbChannelHost.SportTarget != "{}" && tbChannelHost.SportTarget != `{"Brand":"","GameId":""}` {
		sportTargetStr = tbChannelHost.SportTarget
	} else if langListInfos != nil {
		sportTargetStr = langListInfos.SportTarget
	}

	if sportTargetStr != "" {
		type SportTarget struct {
			Brand  string `json:"Brand"`
			GameId string `json:"GameId"`
		}

		type GameSort struct {
			ID    string `json:"id"`
			Name  string `json:"name"`
			EName string `json:"ename"`
			State int    `json:"state"`
		}

		var gameSortEx map[string][]GameSort
		var sportTarget SportTarget
		json.Unmarshal([]byte(sportTargetStr), &sportTarget)
		json.Unmarshal([]byte(tbChannelHost.GameSortEx), &gameSortEx)

		if values, ok := gameSortEx["7"]; ok {
			for _, v := range values {
				if strings.ToLower(v.ID) == strings.ToLower(sportTarget.Brand) && v.State == 1 {
					data.Set("SportTarget", sportTargetStr)
				}
			}
		}
	}

	token := server.GetToken(ctx)
	data.Set("TopAgentId", 0)
	user := &xHashGameModel.XUser{}
	if token != nil {
		daoUser := server.DaoxHashGame().XUser
		db := daoUser.WithContext(ctx.Gin())
		user, err = db.Where(daoUser.UserID.Eq(int32(token.UserId))).Select(daoUser.TopAgentID).First()
		if ctx.RespErr(err, &errcode) {
			logs.Error(err)
			return
		}
		data.Set("TopAgentId", user.TopAgentID)
	}

	// 检查注册赠送活动是否开启（无论用户是否登录都检查）
	activeDefine, err := active.GetRegisterGiftActive(SellerId, ChannelId)
	if err != nil {
		logs.Error("获取注册赠送活动配置失败:", err)
		// 继续执行，不返回错误，不设置 RegisterGiftConfig 字段
	} else if activeDefine != nil && activeDefine.State == utils.ActiveStateOpen {
		// 活动存在且开启状态，解析活动基础配置并添加到返回结果中
		var config active.RegisterGiftBaseConfig
		err = json.Unmarshal([]byte(activeDefine.BaseConfig), &config)
		if err == nil {
			// 只有在活动存在且开启状态时才添加活动配置到返回结果
			data.Set("RegisterGiftConfig", config)
		} else {
			logs.Error("解析注册赠送活动配置失败:", err)
			// 解析失败，不设置 RegisterGiftConfig 字段
		}
	}

	ctx.RespOK(data.Map())
}

func getGameEntranceInfo(ctx *abugo.AbuHttpContent, _ int, _, pHost string, langId int, agentCode string) (*xgo.XMap, error) {
	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	if pHost == "" {
		pHost = host
	}

	ChannelId, SellerId := server.GetChannel(ctx, pHost)

	independence_agent, err := server.XDb().Table("x_agent_independence").Where("Host = ? and State = 1", host).First()
	// 推广链接
	if len(agentCode) > 0 {
		promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, agentCode)
		indepenAgent, err := server.XDb().Table("x_agent_independence").Where("PromotionHost = ? and State = 1", promotionHost).First()
		if err == nil && indepenAgent != nil {
			independence_agent = indepenAgent
		}
	}

	seller, err := server.XDb().Table("x_seller").Where("SellerId = ?", SellerId).First()

	if err != nil {
		logs.Error("AppController info", err)
		return nil, errors.New("获取失败,请稍后再试")
	}
	cachekey := fmt.Sprintf("info:%s", host)
	data, err := server.XRedis().GetCacheMap(cachekey, func() (*xgo.XMap, error) {
		table := server.XDb().Table("x_channel")
		table = table.Select("ChannelId AS Id,ShowName As Name,ChatOpen")
		table = table.Where("SellerId = ? and ChannelId = ?", SellerId, ChannelId)
		idata, err := table.First()
		if err != nil {
			return nil, err
		}
		if idata == nil {
			return nil, fmt.Errorf("域名未配置或未开放")
		}
		idata.Set("LotteryCommission", server.GetConfigString(SellerId, 0, "LotteryCommission"))
		idata.Set("QiPaiCommission", server.GetConfigString(SellerId, 0, "QiPaiCommission"))
		idata.Set("XiaoYouXiCommission", server.GetConfigString(SellerId, 0, "XiaoYouXiCommission"))

		idata.Set("LotteryOpen", server.GetConfigString(SellerId, 0, "LotteryOpen"))
		idata.Set("QiPaiOpen", server.GetConfigString(SellerId, 0, "QiPaiOpen"))
		idata.Set("XiaoYouXiOpen", server.GetConfigString(SellerId, 0, "XiaoYouXiOpen"))

		idata.Set("MinRecharge", 0)
		idata.Set("MinRechargeTrx", 0)
		// 修改为从币种管理读取
		symbol, _ := server.XDb().Table("x_finance_symbol").Select("RechargeMin").Where("Symbol = ?", "USDT").First()
		if symbol != nil {
			idata.Set("MinRecharge", symbol.Float32("RechargeMin"))
		}
		symbol, _ = server.XDb().Table("x_finance_symbol").Select("RechargeMin").Where("Symbol = ?", "TRX").First()
		if symbol != nil {
			idata.Set("MinRechargeTrx", symbol.Float32("RechargeMin"))
		}
		idata.Set("PhoneRegOpen", server.GetConfigInt(SellerId, 0, "PhoneRegOpen"))

		trxWalletA := server.GetConfigString(SellerId, 0, "WalletA")
		trxWalletB := server.GetConfigString(SellerId, 0, "WalletB")
		idata.Set("WalletA", trxWalletA)
		idata.Set("WalletB", trxWalletB)
		// 游戏链开关
		gameChainTb := server.DaoxHashGame().XGameChain
		gameChainDb := server.DaoxHashGame().XGameChain.WithContext(ctx.Gin())
		gameChain, err := gameChainDb.Select(gameChainTb.ChainType, gameChainTb.State, gameChainTb.TranState).Find()
		if err != nil {
			return nil, err
		}
		gameChainMap := make(map[int32]struct{})
		gameChainMapTran := make(map[int32]struct{})
		for _, chain := range gameChain {
			if chain.State == 1 {
				gameChainMap[chain.ChainType] = struct{}{}
			}
			if chain.TranState == 1 {
				gameChainMapTran[chain.ChainType] = struct{}{}
			}
		}

		chainListData := []model.ChainListData{
			{
				Id:      utils.TrxType,
				Name:    utils.TrxTypeName,
				WalletA: trxWalletA,
				WalletB: trxWalletB,
			},
			{
				Id:      utils.EthType,
				Name:    utils.EthTypeName,
				WalletA: server.GetConfigString(SellerId, 0, "EthWalletA"),
				WalletB: server.GetConfigString(SellerId, 0, "EthWalletB"),
			},
			{
				Id:      utils.BscType,
				Name:    utils.BscTypeName,
				WalletA: server.GetConfigString(SellerId, 0, "BscWalletA"),
				WalletB: server.GetConfigString(SellerId, 0, "BscWalletB"),
			},
		}
		var chainListDataInfo = make([]model.ChainListData, 0, len(chainListData))
		var chainListDataTranInfo = make([]model.ChainListData, 0, len(chainListData))
		for index, chainData := range chainListData {
			_, ok := gameChainMap[chainData.Id]
			_, okTran := gameChainMapTran[chainData.Id]
			if ok {
				chainListDataInfo = append(chainListDataInfo, chainListData[index])
			}
			if okTran {
				chainListDataTranInfo = append(chainListDataTranInfo, chainListData[index])
			}
		}
		idata.Set("ChainListData", chainListDataInfo)
		idata.Set("ChainListDataTran", chainListDataTranInfo)
		idata.Set("PeriodWalletA", server.GetConfigString(SellerId, 0, "PeriodWalletA"))
		idata.Set("PeriodWalletB", server.GetConfigString(SellerId, 0, "PeriodWalletB"))

		idata.Set("VipTrxPrice", server.GetConfigString(SellerId, 0, "VipTrxPrice"))
		idata.Set("YiDunVerify", server.GetConfigString(SellerId, 0, "YiDunVerify"))

		idata.Set("TiYanJingTrx", server.GetConfigString(SellerId, 0, "TiYanJingTrx"))
		idata.Set("TiYanJingUsdt", server.GetConfigString(SellerId, 0, "TiYanJingUsdt"))
		idata.Set("TiYanJingUsdtSecond", server.GetConfigString(SellerId, 0, "TiYanJingUsdtSecond"))
		idata.Set("TiYanJingUsdtLiushuiBeishu", server.GetConfigFloat(SellerId, 0, "TiYanJingUsdtLiushuiBeishu"))

		idata.Set("GameSort", "")
		idata.Set("GameSortEx", "")
		idata.Set("GameSortNew", "")

		GameSort, err := server.XDb().Table("x_channel_host").Select("GameSort,GameSortEx,GameSortNew").
			Where("ChannelId = ? and Host = ? and State = 1", ChannelId, host).First()
		if err != nil {
			return nil, err
		}
		if GameSort != nil {
			langListDao := server.DaoxHashGame().XLangList
			langListDb := langListDao.WithContext(nil)
			langListInfos, _ := langListDb.Where(langListDao.ID.Eq(int32(langId))).First()

			idata.Set("GameSort", GameSort.String("GameSort"))
			if GameSort.String("GameSortEx") != "" {
				idata.Set("GameSortEx", GameSort.String("GameSortEx"))
			} else if langListInfos != nil {
				idata.Set("GameSortEx", langListInfos.GameSortEx)
			}

			if GameSort.String("GameSortNew") != "" {
				idata.Set("GameSortNew", GameSort.String("GameSortNew"))
				logs.Info("GameSortNew打印", GameSort.String("GameSortNew"))
			} else if langListInfos != nil {
				idata.Set("GameSortNew", langListInfos.GameSort)
				logs.Info("GameSortNew打印,LangId=%d", langListInfos.GameSort, langId)
			}
		}
		ChangePassowrdDays := server.GetConfigInt(SellerId, 0, "ChangePassowrdDays")
		if ChangePassowrdDays <= 0 {
			ChangePassowrdDays = 10000000
		}
		idata.Set("ChangePassowrdDays", ChangePassowrdDays)

		ChangeWalletPassowrdDays := server.GetConfigInt(SellerId, 0, "ChangeWalletPassowrdDays")
		if ChangeWalletPassowrdDays <= 0 {
			ChangeWalletPassowrdDays = 10000000
		}
		idata.Set("ChangeWalletPassowrdDays", ChangeWalletPassowrdDays)

		idata.Set("IsDuiHuan", 1)
		idata.Set("IsAgent", 2)
		idata.Set("TgUrl", "https://t.me/OKhash_Game_bot")
		idata.Set("GName", "OK")

		if seller != nil {
			idata.Set("Name", seller.String("ShowName"))
			idata.Set("Icon", seller.String("Icon"))
			idata.Set("Logo", seller.String("Logo"))
			idata.Set("Logo2", seller.String("Logo2"))
			idata.Set("IosIcon", seller.String("IosIcon"))
			idata.Set("SampleName", seller.String("SampleName"))
		}

		if independence_agent != nil {
			idata.Set("IsAgent", 1)
			idata.Set("AgentId", independence_agent.Int("UserId"))
			idata.Set("GName", "98")
			idata.Set("IsDuiHuan", independence_agent.Int("IsDuiHuan"))

			idata.Set("Name", independence_agent.String("ShowName"))
			idata.Set("Icon", independence_agent.String("Icon"))
			idata.Set("Logo", independence_agent.String("Logo"))
			idata.Set("Logo2", independence_agent.String("Logo2"))
			idata.Set("IosIcon", independence_agent.String("IosIcon"))

			IsSelfMajorGameOrder := independence_agent.Int("IsSelfMajorGameOrder")
			if IsSelfMajorGameOrder == 1 {
				idata.Set("GameSort", independence_agent.String("MajorGameOrder"))
			}
			IsSelfMinorGameOrder := independence_agent.Int("IsSelfMinorGameOrder")
			if IsSelfMinorGameOrder == 1 {
				idata.Set("GameSortEx", independence_agent.String("MinorGameOrder"))
			}
			IsSelfMajorGameOrderNew := independence_agent.Int("IsSelfMajorGameOrderNew")
			if IsSelfMajorGameOrderNew == 1 {
				idata.Set("GameSortNew", independence_agent.String("MajorGameOrderNew"))
				logs.Info("GameSortNew打印", independence_agent.String("MajorGameOrderNew"))
			}
			IsSelfTgBot := independence_agent.Int("IsSelfTgBot")
			if IsSelfTgBot == 1 {
				tginfo := map[string]interface{}{}
				err := json.Unmarshal([]byte(independence_agent.String("TgInfo")), &tginfo)
				if err != nil {
					idata.Set("TgUrl", tginfo["TgAddress"])
				}
			}
		}

		if idata.String("GameSort") == "" || idata.String("GameSort") == "{}" {
			idata.Set("GameSort", majorgamesort)
		}
		if idata.String("GameSortEx") == "" || idata.String("GameSortEx") == "{}" {
			idata.Set("GameSortEx", minorgamesort)
		}
		if idata.String("GameSortNew") == "" || idata.String("GameSortNew") == "{}" {
			idata.Set("GameSortNew", majorgamesortnew)
			logs.Info("GameSortNew打印", majorgamesortnew)
		}

		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 3, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			idata.Set("XiaoYouXi", gamelist)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 2, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			idata.Set("QiPai", gamelist)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 5, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			idata.Set("ZhenRen", gamelist)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 4, nil)
			where.Add("and", "GameId", "in", "('711', '713')", nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			idata.Set("CaiPiao", gamelist)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 6, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			idata.Set("TiYu", gamelist)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 4, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			type caipiao struct {
				Id       string
				GameList []*map[string]interface{}
			}
			gamelist2 := make([]*caipiao, 0)
			t := *gamelist
			pk := []*map[string]interface{}{}
			ks := []*map[string]interface{}{}
			bpc := []*map[string]interface{}{}
			ssc := []*map[string]interface{}{}
			ttjsc := []*map[string]interface{}{}

			pkId := []int64{713}
			ksId := []int64{711}
			bpcId := []int64{30, 44}
			sscId := []int64{667, 715, 669, 717, 671}
			ttjscId := []int64{328, 253}

			for k := range t {
				gameId, err := strconv.Atoi(t[k]["GameId"].(string))
				if err == nil {
					if in_array_int64(pkId, int64(gameId)) {
						//PK拾
						pk = append(pk, &t[k])
					}
					if in_array_int64(ksId, int64(gameId)) {
						//快三
						ks = append(ks, &t[k])
					}
					if in_array_int64(bpcId, int64(gameId)) {
						//低频彩
						bpc = append(bpc, &t[k])
					}
					if in_array_int64(sscId, int64(gameId)) {
						//时时彩
						ssc = append(ssc, &t[k])
					}
					if in_array_int64(ttjscId, int64(gameId)) {
						//天天极速彩
						ttjsc = append(ttjsc, &t[k])
					}
				}
			}
			gamelist2 = append(gamelist2, &caipiao{Id: "pk10", GameList: pk})
			gamelist2 = append(gamelist2, &caipiao{Id: "ks", GameList: ks})
			gamelist2 = append(gamelist2, &caipiao{Id: "bpc", GameList: bpc})
			gamelist2 = append(gamelist2, &caipiao{Id: "ssc", GameList: ssc})
			gamelist2 = append(gamelist2, &caipiao{Id: "ttjsc", GameList: ttjsc})
			idata.Set("CaiPiaoNew", gamelist2)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "State", "=", 1, nil)
			where.Add("and", "OpenState", "=", 1, nil)
			where.Add("and", "GameType", "=", 4, nil)
			gamelist, _ := server.Db().Table("x_game_list").Where(where).OrderBy("sort desc").GetList()
			type caipiao struct {
				Id       string
				GameList []*map[string]interface{}
			}
			gamelist2 := make([]*caipiao, 0)
			t := *gamelist
			guan := []*map[string]interface{}{}
			xin := []*map[string]interface{}{}

			guanId := []int64{30, 44, 329, 254, 670, 668, 714, 710, 716, 712, 672, 1}
			xinId := []int64{713, 711, 328, 667, 715, 253, 669, 717, 671}

			for k := range t {
				gameId, err := strconv.Atoi(t[k]["GameId"].(string))
				if err == nil {
					if in_array_int64(guanId, int64(gameId)) {
						//官方
						guan = append(guan, &t[k])
					}
					if in_array_int64(xinId, int64(gameId)) {
						//信用
						xin = append(xin, &t[k])
					}
				}
			}
			gamelist2 = append(gamelist2, &caipiao{Id: "guan", GameList: guan})
			gamelist2 = append(gamelist2, &caipiao{Id: "xin", GameList: xin})
			idata.Set("CaiPiaoGuanXin", gamelist2)
		}
		idata.Set("HomeData", server.GetConfigString(SellerId, 0, "HomeData"))
		server.XRedis().Set(cachekey, idata.Map(), 10)
		return idata, nil
	})
	if err != nil {
		logs.Error("AppController info", err)
		return nil, errors.New("请求失败,请稍后再试")
	}

	country := ctx.Gin().Request.Header.Get("country")
	support_country := "AT,BE,CY,EE,FI,FR,DE,GR,IE,IT,LV,LT,LU,MT,NL,PT,SK,SI,ES,TH,CN,HK,SG,MY,ID,VN,IN,MN,JP,BN,KR,BR,PH,SA,UY,SR,PE,GY,CO,CL,AR,GB,BO,PY,KH,LA,GE"
	if !strings.Contains(support_country, country) {
		GameSortEx := map[string]interface{}{}
		json.Unmarshal([]byte(data.String("GameSortEx")), &GameSortEx)
		//for _, v := range GameSortEx {
		//	for _, v2 := range v.([]interface{}) {
		//		v3 := v2.(map[string]interface{})
		//		if abugo.InterfaceToString(v3["id"]) == "evo" {
		//			v3["state"] = 2
		//		}
		//	}
		//}
		bytes, _ := json.Marshal(GameSortEx)
		data.Set("GameSortEx", string(bytes))
	}
	//强制关闭wm wm需要开启 2025-3-6
	//{
	//	GameSortEx := map[string]interface{}{}
	//	json.Unmarshal([]byte(data.String("GameSortEx")), &GameSortEx)
	//	for _, v := range GameSortEx {
	//		for _, v2 := range v.([]interface{}) {
	//			v3 := v2.(map[string]interface{})
	//			if abugo.InterfaceToString(v3["id"]) == "wm" {
	//				v3["state"] = 2
	//			}
	//		}
	//	}
	//	bytes, _ := json.Marshal(GameSortEx)
	//	data.Set("GameSortEx", string(bytes))
	//}
	// {
	// 	AllGameSortEx := map[string]interface{}{}
	// 	json.Unmarshal([]byte(minorgamesort), &AllGameSortEx)

	// 	GameSortEx := map[string]interface{}{}
	// 	json.Unmarshal([]byte(data.String("GameSortEx")), &GameSortEx)
	// 	for k, v := range GameSortEx {
	// 		for _, v2 := range v.([]interface{}) {
	// 			v3 := v2.(map[string]interface{})
	// 			vs := abugo.InterfaceToString(v3["id"])
	// 			allarr := AllGameSortEx[k].([]interface{})
	// 			finded := false
	// 			for _, v4 := range allarr {
	// 				v5 := v4.(map[string]interface{})
	// 				if abugo.InterfaceToString(v5["id"]) == vs {
	// 					finded = true
	// 					break
	// 				}
	// 			}
	// 			if !finded {
	// 				v3["state"] = 2
	// 			}
	// 		}
	// 	}
	// 	bytes, _ := json.Marshal(GameSortEx)
	// 	data.Set("GameSortEx", string(bytes))
	// }

	//data.Set("GameSortEx", `{"1":[{"id":2,"name":"哈希单双","state":1},{"id":1,"name":"哈希大小","state":1},{"id":5,"name":"哈希牛牛","state":1},{"id":12,"name":"和值单双","state":1},{"id":11,"name":"和值大小","state":1},{"id":4,"name":"幸运庄闲","state":1},{"id":102,"name":"一分单双","state":1},{"id":101,"name":"一分大小","state":1},{"id":105,"name":"一分牛牛","state":1},{"id":3,"name":"幸运哈希","state":1},{"id":103,"name":"一分幸运","state":1},{"id":104,"name":"一分庄闲","state":1},{"id":6,"name":"哈希快三","state":2},{"id":7,"name":"哈希PK10","state":2}],"2":[{"id":"gfg","name":"gfg电子","state":1},{"id":"pg","name":"pg电子","state":1},{"id":"pp","name":"pp电子","state":1}],"4":[{"id":"evo","name":"evo真人","state":2},{"id":"ag","name":"ag真人","state":1},{"id":"wm","name":"wm真人","state":2}],"7":[{"id":"up","name":"up体育","state":1}]}`)

	{
		AllGameSortEx := map[string]interface{}{}
		e := json.Unmarshal([]byte(minorgamesort), &AllGameSortEx)
		if e != nil {
			logs.Error(e)
		}

		GameSortEx := map[string]interface{}{}
		e = json.Unmarshal([]byte(data.String("GameSortEx")), &GameSortEx)
		if e != nil {
			logs.Error(e)
		}

		for k, v := range AllGameSortEx {
			if _, ok := GameSortEx[k]; !ok {
				GameSortEx[k] = v
				continue
			}
			subgames_all := v.([]interface{})
			subgames := GameSortEx[k].([]interface{})
			for _, sgall := range subgames_all {
				finded := false
				for _, sg := range subgames {
					if xgo.ToInt(sgall.(map[string]interface{})["id"]) == xgo.ToInt(sg.(map[string]interface{})["id"]) {
						finded = true
						break
					}
				}
				if !finded {
					subgames = append(subgames, sgall)
				}
			}
			GameSortEx[k] = subgames
		}
		bytes, _ := json.Marshal(GameSortEx)
		data.Set("GameSortEx", string(bytes))
	}
	{
		GameSortEx := map[string]interface{}{}
		json.Unmarshal([]byte(data.String("GameSortEx")), &GameSortEx)
		catMap := map[string]int{
			"2":  1, //
			"8":  2,
			"5":  3,
			"11": 4,
			"4":  5,
			"7":  6,
			"12": 7, //德州扑克
		}
		xGameBrand := server.DaoxHashGame().XGameBrand
		dao := server.DaoxHashGame().XGameList
		db := dao.WithContext(ctx.Gin())
		country := ctx.Gin().Request.Header.Get("country")

		for k, v := range GameSortEx {
			// 检查 v 是否为 []interface{} 类型
			slice, ok := v.([]interface{})
			if !ok {
				return nil, fmt.Errorf("expected []interface{}, got %T for key %s", v, k)
			}
			omgOpen := false
			if k == "2" || k == "5" {
				for _, v2 := range slice {
					// 断言 v2 为 map[string]interface{}
					innerMap, ok := v2.(map[string]interface{})
					brand, ok := innerMap["id"].(string)
					if !ok {
						return nil, fmt.Errorf("expected map[string]interface{}, got %T", v2)
					}
					if state, ok := innerMap["state"].(float64); ok && brand == "omg" && int(state) == 1 {
						omgOpen = true
					}
				}
			}

			if k != "1" && k != "11" {
				for _, v2 := range slice {
					// 断言 v2 为 map[string]interface{}
					innerMap, ok := v2.(map[string]interface{})
					if !ok {
						return nil, fmt.Errorf("expected map[string]interface{}, got %T", v2)
					}
					brand, ok := innerMap["id"].(string)
					if !ok {
						return nil, fmt.Errorf("expected map[string]interface{}, got %T", v2)
					}
					query := db.Where(dao.GameType.Eq(int32(catMap[k]))).Where(dao.State.Eq(1)).Where(dao.OpenState.Eq(1)).Where(dao.Brand.Eq(brand))
					count, _ := query.Count()

					query2 := db.Where(dao.GameType.Eq(int32(catMap[k]))).Where(dao.State.Eq(1)).Where(dao.OpenState.Eq(1)).Where(dao.IsNew.Eq(1)).Where(dao.Brand.Eq(brand))
					newCount, _ := query2.Count()

					innerMap["game_count"] = count
					innerMap["new_game_count"] = newCount

					tbGameBrand, err := xGameBrand.WithContext(ctx.Gin()).
						Where(xGameBrand.GameType.Eq(int32(catMap[k]))).Where(xGameBrand.Brand.Eq(brand)).First()
					if err != nil {
						continue
					}
					innerMap["country_open"] = strings.Contains(tbGameBrand.CountryList, country)

					// 特殊处理电子游戏
					if (brand == "pg" || brand == "pp" || brand == "jili" || brand == "spribe") && (k == "2" || k == "5") {
						// 获取omg的区域限制
						gameBrand, err := xGameBrand.WithContext(ctx.Gin()).
							Where(xGameBrand.GameType.Eq(int32(catMap[k]))).Where(xGameBrand.Brand.Eq(brand)).First()
						if err != nil {
							continue
						}

						if !strings.Contains(gameBrand.CountryList, country) {
							innerMap["country_open"] = true
							// 返回特殊游戏数量
							currentBrand, err := xGameBrand.WithContext(ctx.Gin()).Where(xGameBrand.GameType.Eq(int32(catMap[k]))).Where(xGameBrand.Brand.Eq(brand)).First()

							if err != nil {
								continue
							}
							query := db.Where(dao.OpenState.Eq(1)).Where(dao.State.Eq(1)).Where(dao.Brand.Eq("omg")).Where(dao.GameID.In(strings.Split(currentBrand.SpecialGames, ",")...))
							count, _ := query.Count()

							query2 := db.Where(dao.OpenState.Eq(1)).Where(dao.State.Eq(1)).Where(dao.Brand.Eq("omg")).Where(dao.GameID.In(strings.Split(currentBrand.SpecialGames, ",")...)).Where(dao.IsNew.Eq(1))
							newCount, _ := query2.Count()

							innerMap["game_count"] = count
							innerMap["new_game_count"] = newCount
						}

						if !strings.Contains(gameBrand.CountryList, country) && !omgOpen {
							innerMap["country_open"] = false
						}

						logs.Info("brand", brand, "c_open", innerMap["country_open"], "omg_open", omgOpen)

					}

				}
			} else {
				if k != "11" {
					// k == "1" || k == "7"
					for _, v2 := range slice {
						innerMap, ok := v2.(map[string]interface{})
						if !ok {
							return nil, fmt.Errorf("expected map[string]interface{}, got %T", v2)
						}
						innerMap["game_count"] = 1
						innerMap["new_game_count"] = 1
					}
				} else {
					// k == "11"
					for _, v2 := range slice {
						innerMap, ok := v2.(map[string]interface{})
						if !ok {
							return nil, fmt.Errorf("expected map[string]interface{}, got %T", v2)
						}
						gameSubType, ok := innerMap["id"].(string)
						query := db.Where(dao.GameType.Eq(int32(catMap[k]))).
							Where(dao.State.Eq(1)).
							Where(dao.OpenState.Eq(1)).
							Where(dao.GameSubType.Eq(gameSubType))
						if country != "" {
							query.Where(dao.CountryList.Like("%" + country + "%"))
						}
						count, _ := query.Count()

						query2 := db.Where(dao.GameType.Eq(int32(catMap[k]))).
							Where(dao.State.Eq(1)).
							Where(dao.OpenState.Eq(1)).
							Where(dao.IsNew.Eq(1)).
							Where(dao.GameSubType.Eq(gameSubType))
						if country != "" {
							query2.Where(dao.CountryList.Like("%" + country + "%"))
						}
						newCount, _ := query2.Count()

						innerMap["game_count"] = count
						innerMap["new_game_count"] = newCount
					}
				}
			}

		}
		bytes, _ := json.Marshal(GameSortEx)
		data.Set("GameSortEx", string(bytes))
	}

	data.Set("ChannelId", ChannelId)
	data.Set("SellerId", SellerId)
	data.Set("Country", country)
	data.Set("Ip", ctx.GetIp())
	data.Set("ImageUrl", server.ImageUrl())
	data.Set("TopAgentId", 0)
	data.Set("VerifyAddress", server.GetConfigString(SellerId, 0, "VerifyAddress"))
	data.Set("BscVerifyAddress", server.GetConfigString(SellerId, 0, "BscVerifyAddress"))

	return data, nil
}
func in_array_int64(arr []int64, s int64) bool {
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}
func (c *AppController) recharge_config(ctx *abugo.AbuHttpContent) {
	ctx.Put("WithdrawLimit", server.GetConfigString(1, 0, "WithdrawLimit"))
	ctx.Put("WithdrawFee", server.GetConfigString(1, 0, "WithdrawFee"))
	ctx.Put("EthWithdrawLimit", server.GetConfigString(1, 0, "EthWithdrawLimit"))
	ctx.Put("EthWithdrawFee", server.GetConfigString(1, 0, "EthWithdrawFee"))
	ctx.RespOK()
}

func (c *AppController) send_email(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int    `validate:"required"`
		Email    string `validate:"required"`
		UseType  int    `validate:"required"` // 1注册,2重置密码,3绑定
		Language string `validate:"required"` // zh, en
		Host     string `validate:"required"`
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, reqdata.Host)
	ChannelId := 1 // 不区分渠道，只区分运营商

	// 生成并保存验证码
	code := rand.Intn(8999) + 1000
	tb := server.DaoxHashGame().XVerify
	do := tb.WithContext(context.Background())
	saveerr := do.Save(&xHashGameModel.XVerify{
		SellerID:   int32(SellerId),
		ChannelID:  int32(ChannelId),
		Account:    reqdata.Email,
		UseType:    int32(reqdata.UseType),
		VerifyCode: abugo.GetStringFromInterface(code),
		CreateTime: carbon.Now().StdTime(),
	})
	if err != nil {
		logs.Error("send_email save ", reqdata.Email, code, saveerr)
		ctx.RespErrString(true, &errcode, "save code failed")
		return
	}

	// 测试环境直接返回验证码
	if c.sms_debug == 100 {
		ctx.RespOK(gin.H{"VerifyCode": code})
		return
	}

	// 外网2是98模板 其他都用OK模板
	seller := "OK"
	if SellerId == 2 {
		seller = "98"
	} else if SellerId == 4 {
		seller = "98Sport"
	}
	// 除了zh都用英文模板
	language := "zh"
	if reqdata.Language != "zh" {
		language = "en"
	}

	merr := xemail.SendEmail(seller, abugo.GetStringFromInterface(code), language, reqdata.Email)
	if merr != nil {
		logs.Error("SendEmail merr ", merr)
		ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *AppController) send_sms(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int     `validate:"required"`
		PhoneNum string  `validate:"required"`
		UseType  float64 `validate:"required"` //1注册,2登录
		Host     string
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	arrphonenum := strings.Split(reqdata.PhoneNum, "-")
	if len(arrphonenum) != 2 {
		ctx.RespErrString(true, &errcode, "手机号不正确")
		return
	}
	if arrphonenum[1] == "" {
		ctx.RespErrString(true, &errcode, "手机号不正确")
		return
	}
	code := rand.Int()%1000 + 1000
	msg := fmt.Sprintf("您的验证码为:%d 此验证码10分钟内有效", code)
	_, SellerId := server.GetChannel(ctx, reqdata.Host)
	ChannelId := 1 // 不区分渠道，只区分运营商
	server.XDb().Table("x_verify").Replace(gin.H{
		"Account":    reqdata.PhoneNum,
		"SellerId":   SellerId,
		"ChannelId":  ChannelId,
		"VerifyCode": code,
		"UseType":    reqdata.UseType,
	})
	if arrphonenum[0] == "86" {
		if c.sms_debug == 100 {
			ctx.RespOK(gin.H{"VerifyCode": code})
		} else {
			reqpath := fmt.Sprintf(c.cn_sms_path, c.cn_sms_key, c.cn_sms_account, arrphonenum[1], fmt.Sprintf("%d", code))
			resp, err := req.Get(c.cn_sms_url + reqpath)
			if err != nil {
				logs.Error(err)
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			defer resp.Response().Body.Close()
			body, err := io.ReadAll(resp.Response().Body)
			if err != nil {
				logs.Error(err)
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			jdata := map[string]interface{}{}
			err = json.Unmarshal(body, &jdata)
			if err != nil {
				logs.Error(err, string(body))
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			strcode := jdata["code"].(float64)
			if strcode != 200 {
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			ctx.RespOK()
		}
	} else {
		if c.sms_debug == 100 {
			ctx.RespOK(gin.H{"VerifyCode": code})
		} else {
			reqpath := fmt.Sprintf(c.sms_path, c.sms_key, c.sms_account, arrphonenum[0]+arrphonenum[1], msg)
			resp, err := req.Get(c.sms_url + reqpath)
			if err != nil {
				logs.Error(err)
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			defer resp.Response().Body.Close()
			body, err := io.ReadAll(resp.Response().Body)
			if err != nil {
				logs.Error(err)
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			jdata := map[string]interface{}{}
			err = json.Unmarshal(body, &jdata)
			if err != nil {
				logs.Error(err, string(body))
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			strcode := jdata["code"].(string)
			if strcode != "0" {
				ctx.RespErrString(true, &errcode, "发送失败,请稍后再试")
				return
			}
			ctx.RespOK()
		}
	}
}
