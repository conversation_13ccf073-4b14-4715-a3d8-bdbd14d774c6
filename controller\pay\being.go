package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var Being = new(being)

type being struct {
	Base
}

// Init 初始化函数
// 初始化服务器的HTTP接口，注册充值和提现回调接口
func (c *being) Init() {
	// 注册充值回调接口
	// 接口路径：/api/being/recharge/callback
	// 方法：POST
	// 无需鉴权
	server.Http().PostNoAuth("/api/being/recharge/callback", c.rechargeCallback)

	// 注册提现回调接口
	// 接口路径：/api/being/withdraw/callback
	// 方法：POST
	// 无需鉴权
	server.Http().PostNoAuth("/api/being/withdraw/callback", c.withdrawCallback)
}

// Recharge 充值接口
// 处理充值请求，创建订单，调用支付接口
func (c *being) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0 // 初始化错误码

	payMethod, err := c.getPayMethod(req.MethodId) // 获取支付方式
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode) // 支付方式不存在，返回错误
		return
	}

	var cfg map[string]any // 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	token := server.GetToken(ctx) // 获取用户信息
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode) // 用户不存在，返回错误
		return
	}
	var rate, realAmount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                         // 运营商ID为26时，汇率设为0
		realAmount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getWithdrawRate(req.Symbol) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		realAmount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	logs.Info("运营商ID:", user.SellerID, "原始金额:", req.Amount, "汇率:", rate, "汇率金额:", realAmount)
	tx := server.DaoxHashGame().Begin() // 开始事务
	rechargeOrder := &model.XRecharge{  // 创建订单
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      18,
		Amount:       req.Amount,
		RealAmount:   realAmount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()                               // 回滚事务
		ctx.RespErr(errors.New("创建订单失败"), &errcode) // 创建订单失败，返回错误
		return
	}

	params := xgo.H{ // 构造支付请求参数
		"amount":       fmt.Sprintf("%.2f", req.Amount),
		"mchOrderNo":   rechargeOrder.ID,
		"mchUserId":    user.UserID,
		"subject":      "商品标题",
		"wayCode":      payMethod.PayType,
		"reqTime":      fmt.Sprintf("%d", time.Now().UnixMilli()),
		"body":         "Being充值",
		"version":      "1.0",
		"appId":        cfg["app_id"],
		"notifyUrl":    fmt.Sprintf("%s/api/being/recharge/callback", cfg["cburl"]),
		"signType":     "MD5",
		"currency":     strings.ToUpper(rechargeOrder.Symbol),
		"returnUrl":    fmt.Sprintf("%s/api/being/recharge/callback", cfg["cburl"]),
		"mchNo":        cfg["mch_no"],
		"channelExtra": fmt.Sprintf("{\"name\": \"%s\"}", req.RealName),
	}
	params["sign"] = c.generateMd5Sign(params, cfg["key"].(string)) // 生成签名

	jsonData, _ := json.Marshal(&params) // 序列化参数
	url := fmt.Sprintf("%s/api/pay/unifiedOrder", cfg["url"].(string))
	logs.Info("Being:发起支付请求 url=", url) // 记录请求url

	resp, err := c.post(url, map[string]string{ // 发送支付请求
		"Content-Type": "application/json",
	}, jsonData)
	if err != nil {
		tx.Rollback()                                      // 回滚事务
		ctx.RespErr(errors.New("Being发起支付请求失败"), &errcode) // 发送支付请求失败，返回错误
		return
	}

	//	解析响应数据
	type BeingRechargeResponse struct {
		Code int `json:"code"` // 状态码
		Data struct {
			Amount       float64 `json:"amount"`       // 订单金额
			AmountActual float64 `json:"amountActual"` // 实际支付金额
			ExpiredTime  string  `json:"expiredTime"`  // 订单过期时间
			MchOrderNo   string  `json:"mchOrderNo"`   // 商户订单号
			MchUserId    string  `json:"mchUserId"`    // 商户用户ID
			OrderState   int     `json:"orderState"`   // 订单状态
			PayData      string  `json:"payData"`      // 支付数据
			PayDataType  string  `json:"payDataType"`  // 支付数据类型
			PayOrderId   string  `json:"payOrderId"`   // 支付订单号
		} `json:"data"` // 响应数据
		Msg  string `json:"msg"`  // 响应消息
		Sign string `json:"sign"` // 响应签名
	}

	response := BeingRechargeResponse{} // 解析响应
	err = json.Unmarshal(resp.Body(), &response)
	if err != nil {
		logs.Error("Being:解析响应消息体错误 err=", err.Error()) // 解析响应失败，记录错误
		ctx.RespErrString(true, &errcode, err.Error())  // 返回错误
		tx.Rollback()                                   // 回滚事务
		return
	}

	if response.Code != 0 { // 处理响应
		tx.Rollback()                                       // 回滚事务
		ctx.RespErr(errors.New("Being:充值订单创建失败"), &errcode) // 充值订单创建失败，返回错误
		return
	} else {
		tx.Commit() // 提交事务
	}

	ctx.RespOK(xgo.H{ // 返回响应
		"payurl": response.Data.PayData,
	})
}

// Being查询充值订单接口
func (c *being) rechargeOrderQuery(ctx *abugo.AbuHttpContent, thirdOrderId string, cfg map[string]any) error {
	// 错误码
	errcode := 0

	// 构造请求参数
	params := xgo.H{
		"mchNo":      cfg["mch_no"],                             // 商户号
		"appId":      cfg["app_id"],                             // 应用ID
		"payOrderId": thirdOrderId,                              // 支付订单号
		"reqTime":    fmt.Sprintf("%d", time.Now().UnixMilli()), // 请求时间
		"version":    "1.0",                                     // 版本号
		"signType":   "MD5",                                     // 签名类型
	}

	// 生成签名
	params["sign"] = c.generateMd5Sign(params, cfg["key"].(string))

	// 发送查询订单请求
	jsonData, _ := json.Marshal(&params)
	resp, err := c.post(cfg["url"].(string)+"/api/pay/query", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)

	// 处理请求错误
	if err != nil {
		ctx.RespErr(errors.New("Being查询订单请求失败"), &errcode)
		return err
	}

	// QueryOrderResponse 查询订单响应数据
	type QueryOrderResponse struct {
		Code int    `json:"code"` // 返回状态
		Msg  string `json:"msg"`  // 返回信息
		Sign string `json:"sign"` // 签名信息
		Data struct {
			PayOrderId     string  `json:"payOrderId"`     // 支付订单号
			MchNo          string  `json:"mchNo"`          // 商户号
			AppId          string  `json:"appId"`          // 应用ID
			MchOrderNo     string  `json:"mchOrderNo"`     // 商户订单号
			IfCode         string  `json:"ifCode"`         // 支付接口
			WayCode        string  `json:"wayCode"`        // 支付方式
			Amount         float64 `json:"amount"`         // 支付金额
			Currency       string  `json:"currency"`       // 货币代码
			State          int     `json:"state"`          // 订单状态
			ClientIp       string  `json:"clientIp"`       // 客户端IP
			Subject        string  `json:"subject"`        // 商品标题
			Body           string  `json:"body"`           // 商品描述
			ChannelOrderNo string  `json:"channelOrderNo"` // 渠道订单号
			ErrCode        string  `json:"errCode"`        // 渠道错误码
			ErrMsg         string  `json:"errMsg"`         // 渠道错误描述
			ExtParam       string  `json:"extParam"`       // 扩展参数
			CreatedAt      int64   `json:"createdAt"`      // 创建时间
			SuccessTime    int64   `json:"successTime"`    // 成功时间
		} `json:"data"`
	}
	var response QueryOrderResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		ctx.RespErr(errors.New("Being查询订单响应数据解析失败"), &errcode)
		return err
	}

	// 处理响应错误
	if response.Code != 0 {
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return errors.New(response.Msg)
	}

	// 处理订单状态
	if response.Data.State != 2 {
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return errors.New(response.Msg)
	}

	return nil
}

// Being查询提现订单接口
func (c *being) withdrawOrderQuery(ctx *abugo.AbuHttpContent, thirdOrderId string, cfg map[string]any) error {
	// 错误码
	errcode := 0

	// 构造请求参数
	params := xgo.H{
		"mchNo":      cfg["mch_no"],                             // 商户号
		"appId":      cfg["app_id"],                             // 应用ID
		"transferId": thirdOrderId,                              // 支付订单号
		"reqTime":    fmt.Sprintf("%d", time.Now().UnixMilli()), // 请求时间
		"version":    "1.0",                                     // 版本号
		"signType":   "MD5",                                     // 签名类型
	}

	// 生成签名
	params["sign"] = c.generateMd5Sign(params, cfg["key"].(string))
	// 发送查询订单请求
	jsonData, _ := json.Marshal(&params)
	resp, err := c.post(cfg["url"].(string)+"/api/transfer/query", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)

	// 处理请求错误
	if err != nil {
		ctx.RespErr(errors.New("Being查询订单请求失败"), &errcode)
		return err
	}

	// QueryOrderResponse 查询订单响应数据
	type QueryOrderResponse struct {
		Code int    `json:"code"` // 返回状态码
		Msg  string `json:"msg"`  // 返回信息
		Sign string `json:"sign"` // 签名信息
		Data struct {
			MchNo          string  `json:"mchNo"`          // 商户号
			AppId          string  `json:"appId"`          // 应用ID
			MchOrderNo     string  `json:"mchOrderNo"`     // 商户订单号
			TransferId     string  `json:"transferId"`     // 转账订单号
			Amount         float64 `json:"amount"`         // 转账金额
			Currency       string  `json:"currency"`       // 货币代码
			IfCode         string  `json:"ifCode"`         // 接口代码
			EntryType      string  `json:"entryType"`      // 入账方式
			State          int     `json:"state"`          // 转账状态
			AccountNo      string  `json:"accountNo"`      // 收款账号
			AccountName    string  `json:"accountName"`    // 收款人姓名
			BankName       string  `json:"bankName"`       // 收款人开户行名称
			TransferDesc   string  `json:"transferDesc"`   // 转账备注信息
			ChannelOrderNo string  `json:"channelOrderNo"` // 渠道转账单号
			ErrCode        string  `json:"errCode"`        // 渠道错误码
			ErrMsg         string  `json:"errMsg"`         // 渠道错误描述
			ExtraParam     string  `json:"extraParam"`     // 扩展参数
			CreatedAt      int64   `json:"createdAt"`      // 订单创建时间
			SuccessTime    int64   `json:"successTime"`    // 转账成功时间
		} `json:"data"` // 返回数据
	}

	var response QueryOrderResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		ctx.RespErr(errors.New("Being查询订单响应数据解析失败"), &errcode)
		return err
	}

	// 处理响应错误
	if response.Code != 0 {
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return errors.New(response.Msg)
	}

	// 处理订单状态
	if response.Data.State != 2 {
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return errors.New(response.Msg)
	}
	return nil
}

// 充值回调
func (c *being) rechargeCallback(ctx *abugo.AbuHttpContent) {
	type BeingRechargeCallBack struct {
		Amount     string `json:"amount"`     // 订单金额
		Body       string `json:"body"`       // 订单描述
		ClientIp   string `json:"clientIp"`   // 客户端IP地址
		CreatedAt  string `json:"createdAt"`  // 订单创建时间
		Currency   string `json:"currency"`   // 订单币种
		ExtParam   string `json:"extParam"`   // 扩展参数
		IfCode     string `json:"ifCode"`     // 接口代码
		MchNo      string `json:"mchNo"`      // 商户号
		AppId      string `json:"appId"`      // 应用ID
		MchOrderNo string `json:"mchOrderNo"` // 商户订单号
		PayOrderId string `json:"payOrderId"` // 支付订单号
		State      string `json:"state"`      // 订单状态
		Subject    string `json:"subject"`    // 订单标题
		WayCode    string `json:"wayCode"`    // 支付方式代码
		Sign       string `json:"sign"`       // 签名
	}
	callBack := BeingRechargeCallBack{
		Amount:     ctx.Gin().PostForm("amount"),
		Body:       ctx.Gin().PostForm("body"),
		ClientIp:   ctx.Gin().PostForm("clientIp"),
		CreatedAt:  ctx.Gin().PostForm("createdAt"),
		Currency:   ctx.Gin().PostForm("currency"),
		ExtParam:   ctx.Gin().PostForm("extParam"),
		IfCode:     ctx.Gin().PostForm("ifCode"),
		MchNo:      ctx.Gin().PostForm("mchNo"),
		AppId:      ctx.Gin().PostForm("appId"),
		MchOrderNo: ctx.Gin().PostForm("mchOrderNo"),
		PayOrderId: ctx.Gin().PostForm("payOrderId"),
		State:      ctx.Gin().PostForm("state"),
		Subject:    ctx.Gin().PostForm("subject"),
		WayCode:    ctx.Gin().PostForm("wayCode"),
	}
	jsonData, _ := json.Marshal(callBack)
	logs.Info("Being充值回调数据:", string(jsonData))
	if callBack.State != "2" {
		logs.Error("Being:充值回调失败")
		ctx.Gin().String(200, "充值回调失败")
		return
	}
	// 查看订单是否存在
	orderId, _ := strconv.Atoi(callBack.MchOrderNo)
	order, err := c.getRechargeOrder(orderId)
	// 订单不存在
	if err != nil {
		logs.Error("Being:充值订单不存在", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	// 验证签名
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Info("Being:支付方式不存在", err)
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 查询三方订单是否存在
	if err = c.rechargeOrderQuery(ctx, callBack.PayOrderId, cfg); err != nil {
		logs.Error("Being:", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	// 是否成功订单
	if order.State == 5 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	amount, _ := strconv.ParseFloat(callBack.Amount, 64)
	if math.Abs(order.Amount-amount) > 0.01 {
		logs.Info("Being:回调金额不一致 order.Amount:", order.Amount, "amount:", amount)
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 更新三方订单号
	if err = c.updateThirdOrder(order.ID, callBack.PayOrderId); err != nil {
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}

	// 处理充值回调
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	ctx.Gin().String(200, "success")
}

// 提现回调
func (c *being) withdrawCallback(ctx *abugo.AbuHttpContent) {
	type BeingWithdrawCallBack struct {
		MchNo          string `json:"mchNo"`          // 商户号
		AppId          string `json:"appId"`          // 应用ID
		MchOrderNo     string `json:"mchOrderNo"`     // 商户订单号
		TransferId     string `json:"transferId"`     // 转账订单号
		Amount         string `json:"amount"`         // 转账金额
		Currency       string `json:"currency"`       // 货币代码
		IfCode         string `json:"ifCode"`         // 接口代码
		EntryType      string `json:"entryType"`      // 入账方式
		State          string `json:"state"`          // 转账状态
		AccountNo      string `json:"accountNo"`      // 收款账号
		AccountName    string `json:"accountName"`    // 收款人姓名
		BankName       string `json:"bankName"`       // 收款人开户行名称
		TransferDesc   string `json:"transferDesc"`   // 转账备注信息
		ChannelOrderNo string `json:"channelOrderNo"` // 渠道转账单号
		ErrCode        string `json:"errCode"`        // 渠道错误码
		ErrMsg         string `json:"errMsg"`         // 渠道错误描述
		ExtraParam     string `json:"extraParam"`     // 扩展参数
		CreatedAt      string `json:"createdAt"`      // 创建时间
		SuccessTime    string `json:"successTime"`    // 成功时间
	}
	callBack := BeingWithdrawCallBack{
		MchNo:          ctx.Gin().PostForm("mchNo"),
		AppId:          ctx.Gin().PostForm("appId"),
		MchOrderNo:     ctx.Gin().PostForm("mchOrderNo"),
		TransferId:     ctx.Gin().PostForm("transferId"),
		Amount:         ctx.Gin().PostForm("amount"),
		Currency:       ctx.Gin().PostForm("currency"),
		IfCode:         ctx.Gin().PostForm("ifCode"),
		EntryType:      ctx.Gin().PostForm("entryType"),
		State:          ctx.Gin().PostForm("state"),
		AccountNo:      ctx.Gin().PostForm("accountNo"),
		AccountName:    ctx.Gin().PostForm("accountName"),
		BankName:       ctx.Gin().PostForm("bankName"),
		TransferDesc:   ctx.Gin().PostForm("transferDesc"),
		ChannelOrderNo: ctx.Gin().PostForm("channelOrderNo"),
		ErrCode:        ctx.Gin().PostForm("errCode"),
		ErrMsg:         ctx.Gin().PostForm("errMsg"),
		ExtraParam:     ctx.Gin().PostForm("extraParam"),
		CreatedAt:      ctx.Gin().PostForm("createdAt"),
		SuccessTime:    ctx.Gin().PostForm("successTime"),
	}
	jsonData, _ := json.Marshal(callBack)
	logs.Info("Being:提现回调数据:", string(jsonData))
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(callBack.MchOrderNo)
	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		ctx.Gin().String(200, "订单号不存在")
		return
	}
	amount, err := strconv.ParseFloat(callBack.Amount, 64)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("Being:提现回调金额转换错误")
		ctx.Gin().String(200, "下发金额转换错误")
		return
	}
	if math.Abs((order.RealAmount-amount)/100) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("Being:提现回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证支付方式
	var cfg map[string]any
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("Being:提现回调支付方式不存在")
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 查询三方订单是否存在
	if err = c.withdrawOrderQuery(ctx, order.ThirdID, cfg); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("Being:", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	// 是否成功订单
	if order.State == 6 {
		ctx.Gin().String(200, "success")
		return
	}

	if callBack.State == "2" {
		c.withdrawCallbackHandel(int(order.ID), 6)
		logs.Info("Being:代付成功:", order.ID)
		ctx.Gin().String(200, "success")
		return
	} else {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("Being:代付失败:", order.ID)
		ctx.Gin().String(200, "update failure")
	}
}
