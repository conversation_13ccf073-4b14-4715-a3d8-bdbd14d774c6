// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashStat/model"
)

func newXTgRobotLogAction(db *gorm.DB, opts ...gen.DOOption) xTgRobotLogAction {
	_xTgRobotLogAction := xTgRobotLogAction{}

	_xTgRobotLogAction.xTgRobotLogActionDo.UseDB(db, opts...)
	_xTgRobotLogAction.xTgRobotLogActionDo.UseModel(&model.XTgRobotLogAction{})

	tableName := _xTgRobotLogAction.xTgRobotLogActionDo.TableName()
	_xTgRobotLogAction.ALL = field.NewAsterisk(tableName)
	_xTgRobotLogAction.ID = field.NewInt64(tableName, "Id")
	_xTgRobotLogAction.RobotID = field.NewInt64(tableName, "RobotId")
	_xTgRobotLogAction.TgChatID = field.NewInt64(tableName, "TgChatId")
	_xTgRobotLogAction.SellerID = field.NewInt32(tableName, "SellerId")
	_xTgRobotLogAction.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xTgRobotLogAction.ActionType = field.NewInt32(tableName, "ActionType")
	_xTgRobotLogAction.IsInResourceDb = field.NewInt32(tableName, "IsInResourceDb")
	_xTgRobotLogAction.Memo = field.NewString(tableName, "Memo")
	_xTgRobotLogAction.RecordTime = field.NewTime(tableName, "RecordTime")

	_xTgRobotLogAction.fillFieldMap()

	return _xTgRobotLogAction
}

// xTgRobotLogAction 机器人行为日志
type xTgRobotLogAction struct {
	xTgRobotLogActionDo xTgRobotLogActionDo

	ALL            field.Asterisk
	ID             field.Int64  // 记录Id
	RobotID        field.Int64  // 机器人Id
	TgChatID       field.Int64  //  tg用户Id
	SellerID       field.Int32  // 运营商id
	ChannelID      field.Int32  // 渠道id
	ActionType     field.Int32  // 行为分类(1:start 2:注册 3: 领U 4:领T 5:激活)
	IsInResourceDb field.Int32  // 是否在库用户 0不在库 1在库
	Memo           field.String // 备注
	RecordTime     field.Time   // 记录时间

	fieldMap map[string]field.Expr
}

func (x xTgRobotLogAction) Table(newTableName string) *xTgRobotLogAction {
	x.xTgRobotLogActionDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTgRobotLogAction) As(alias string) *xTgRobotLogAction {
	x.xTgRobotLogActionDo.DO = *(x.xTgRobotLogActionDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTgRobotLogAction) updateTableName(table string) *xTgRobotLogAction {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.RobotID = field.NewInt64(table, "RobotId")
	x.TgChatID = field.NewInt64(table, "TgChatId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.ActionType = field.NewInt32(table, "ActionType")
	x.IsInResourceDb = field.NewInt32(table, "IsInResourceDb")
	x.Memo = field.NewString(table, "Memo")
	x.RecordTime = field.NewTime(table, "RecordTime")

	x.fillFieldMap()

	return x
}

func (x *xTgRobotLogAction) WithContext(ctx context.Context) *xTgRobotLogActionDo {
	return x.xTgRobotLogActionDo.WithContext(ctx)
}

func (x xTgRobotLogAction) TableName() string { return x.xTgRobotLogActionDo.TableName() }

func (x xTgRobotLogAction) Alias() string { return x.xTgRobotLogActionDo.Alias() }

func (x xTgRobotLogAction) Columns(cols ...field.Expr) gen.Columns {
	return x.xTgRobotLogActionDo.Columns(cols...)
}

func (x *xTgRobotLogAction) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTgRobotLogAction) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["RobotId"] = x.RobotID
	x.fieldMap["TgChatId"] = x.TgChatID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["ActionType"] = x.ActionType
	x.fieldMap["IsInResourceDb"] = x.IsInResourceDb
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["RecordTime"] = x.RecordTime
}

func (x xTgRobotLogAction) clone(db *gorm.DB) xTgRobotLogAction {
	x.xTgRobotLogActionDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTgRobotLogAction) replaceDB(db *gorm.DB) xTgRobotLogAction {
	x.xTgRobotLogActionDo.ReplaceDB(db)
	return x
}

type xTgRobotLogActionDo struct{ gen.DO }

func (x xTgRobotLogActionDo) Debug() *xTgRobotLogActionDo {
	return x.withDO(x.DO.Debug())
}

func (x xTgRobotLogActionDo) WithContext(ctx context.Context) *xTgRobotLogActionDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTgRobotLogActionDo) ReadDB() *xTgRobotLogActionDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTgRobotLogActionDo) WriteDB() *xTgRobotLogActionDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTgRobotLogActionDo) Session(config *gorm.Session) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTgRobotLogActionDo) Clauses(conds ...clause.Expression) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTgRobotLogActionDo) Returning(value interface{}, columns ...string) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTgRobotLogActionDo) Not(conds ...gen.Condition) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTgRobotLogActionDo) Or(conds ...gen.Condition) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTgRobotLogActionDo) Select(conds ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTgRobotLogActionDo) Where(conds ...gen.Condition) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTgRobotLogActionDo) Order(conds ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTgRobotLogActionDo) Distinct(cols ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTgRobotLogActionDo) Omit(cols ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTgRobotLogActionDo) Join(table schema.Tabler, on ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTgRobotLogActionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTgRobotLogActionDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTgRobotLogActionDo) Group(cols ...field.Expr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTgRobotLogActionDo) Having(conds ...gen.Condition) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTgRobotLogActionDo) Limit(limit int) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTgRobotLogActionDo) Offset(offset int) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTgRobotLogActionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTgRobotLogActionDo) Unscoped() *xTgRobotLogActionDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTgRobotLogActionDo) Create(values ...*model.XTgRobotLogAction) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTgRobotLogActionDo) CreateInBatches(values []*model.XTgRobotLogAction, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTgRobotLogActionDo) Save(values ...*model.XTgRobotLogAction) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTgRobotLogActionDo) First() (*model.XTgRobotLogAction, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotLogAction), nil
	}
}

func (x xTgRobotLogActionDo) Take() (*model.XTgRobotLogAction, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotLogAction), nil
	}
}

func (x xTgRobotLogActionDo) Last() (*model.XTgRobotLogAction, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotLogAction), nil
	}
}

func (x xTgRobotLogActionDo) Find() ([]*model.XTgRobotLogAction, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTgRobotLogAction), err
}

func (x xTgRobotLogActionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTgRobotLogAction, err error) {
	buf := make([]*model.XTgRobotLogAction, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTgRobotLogActionDo) FindInBatches(result *[]*model.XTgRobotLogAction, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTgRobotLogActionDo) Attrs(attrs ...field.AssignExpr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTgRobotLogActionDo) Assign(attrs ...field.AssignExpr) *xTgRobotLogActionDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTgRobotLogActionDo) Joins(fields ...field.RelationField) *xTgRobotLogActionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTgRobotLogActionDo) Preload(fields ...field.RelationField) *xTgRobotLogActionDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTgRobotLogActionDo) FirstOrInit() (*model.XTgRobotLogAction, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotLogAction), nil
	}
}

func (x xTgRobotLogActionDo) FirstOrCreate() (*model.XTgRobotLogAction, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTgRobotLogAction), nil
	}
}

func (x xTgRobotLogActionDo) FindByPage(offset int, limit int) (result []*model.XTgRobotLogAction, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTgRobotLogActionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTgRobotLogActionDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTgRobotLogActionDo) Delete(models ...*model.XTgRobotLogAction) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTgRobotLogActionDo) withDO(do gen.Dao) *xTgRobotLogActionDo {
	x.DO = *do.(*gen.DO)
	return x
}
