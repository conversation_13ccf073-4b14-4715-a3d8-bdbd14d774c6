// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXGamePeriod(db *gorm.DB, opts ...gen.DOOption) xGamePeriod {
	_xGamePeriod := xGamePeriod{}

	_xGamePeriod.xGamePeriodDo.UseDB(db, opts...)
	_xGamePeriod.xGamePeriodDo.UseModel(&model.XGamePeriod{})

	tableName := _xGamePeriod.xGamePeriodDo.TableName()
	_xGamePeriod.ALL = field.NewAsterisk(tableName)
	_xGamePeriod.ID = field.NewInt32(tableName, "Id")
	_xGamePeriod.ChainType = field.NewInt32(tableName, "ChainType")
	_xGamePeriod.GameID = field.NewInt32(tableName, "GameId")
	_xGamePeriod.Period = field.NewString(tableName, "Period")
	_xGamePeriod.State = field.NewInt32(tableName, "State")
	_xGamePeriod.OpenHash = field.NewString(tableName, "OpenHash")
	_xGamePeriod.OpenResult = field.NewString(tableName, "OpenResult")
	_xGamePeriod.BlockMaker = field.NewString(tableName, "BlockMaker")
	_xGamePeriod.MakeTotal = field.NewInt32(tableName, "MakeTotal")
	_xGamePeriod.CreateTime = field.NewTime(tableName, "CreateTime")
	_xGamePeriod.BlockTime = field.NewTime(tableName, "BlockTime")

	_xGamePeriod.fillFieldMap()

	return _xGamePeriod
}

type xGamePeriod struct {
	xGamePeriodDo xGamePeriodDo

	ALL        field.Asterisk
	ID         field.Int32 // id
	ChainType  field.Int32 // 网链分类 1trc 2erc 3bsc
	GameID     field.Int32
	Period     field.String
	State      field.Int32  // 状态  1等待开奖  2已开奖未结算 3已开奖结算完成
	OpenHash   field.String // 开奖哈希
	OpenResult field.String // 开奖结果
	BlockMaker field.String
	MakeTotal  field.Int32
	CreateTime field.Time
	BlockTime  field.Time

	fieldMap map[string]field.Expr
}

func (x xGamePeriod) Table(newTableName string) *xGamePeriod {
	x.xGamePeriodDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xGamePeriod) As(alias string) *xGamePeriod {
	x.xGamePeriodDo.DO = *(x.xGamePeriodDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xGamePeriod) updateTableName(table string) *xGamePeriod {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.ChainType = field.NewInt32(table, "ChainType")
	x.GameID = field.NewInt32(table, "GameId")
	x.Period = field.NewString(table, "Period")
	x.State = field.NewInt32(table, "State")
	x.OpenHash = field.NewString(table, "OpenHash")
	x.OpenResult = field.NewString(table, "OpenResult")
	x.BlockMaker = field.NewString(table, "BlockMaker")
	x.MakeTotal = field.NewInt32(table, "MakeTotal")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.BlockTime = field.NewTime(table, "BlockTime")

	x.fillFieldMap()

	return x
}

func (x *xGamePeriod) WithContext(ctx context.Context) *xGamePeriodDo {
	return x.xGamePeriodDo.WithContext(ctx)
}

func (x xGamePeriod) TableName() string { return x.xGamePeriodDo.TableName() }

func (x xGamePeriod) Alias() string { return x.xGamePeriodDo.Alias() }

func (x xGamePeriod) Columns(cols ...field.Expr) gen.Columns { return x.xGamePeriodDo.Columns(cols...) }

func (x *xGamePeriod) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xGamePeriod) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 11)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["ChainType"] = x.ChainType
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Period"] = x.Period
	x.fieldMap["State"] = x.State
	x.fieldMap["OpenHash"] = x.OpenHash
	x.fieldMap["OpenResult"] = x.OpenResult
	x.fieldMap["BlockMaker"] = x.BlockMaker
	x.fieldMap["MakeTotal"] = x.MakeTotal
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["BlockTime"] = x.BlockTime
}

func (x xGamePeriod) clone(db *gorm.DB) xGamePeriod {
	x.xGamePeriodDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xGamePeriod) replaceDB(db *gorm.DB) xGamePeriod {
	x.xGamePeriodDo.ReplaceDB(db)
	return x
}

type xGamePeriodDo struct{ gen.DO }

func (x xGamePeriodDo) Debug() *xGamePeriodDo {
	return x.withDO(x.DO.Debug())
}

func (x xGamePeriodDo) WithContext(ctx context.Context) *xGamePeriodDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xGamePeriodDo) ReadDB() *xGamePeriodDo {
	return x.Clauses(dbresolver.Read)
}

func (x xGamePeriodDo) WriteDB() *xGamePeriodDo {
	return x.Clauses(dbresolver.Write)
}

func (x xGamePeriodDo) Session(config *gorm.Session) *xGamePeriodDo {
	return x.withDO(x.DO.Session(config))
}

func (x xGamePeriodDo) Clauses(conds ...clause.Expression) *xGamePeriodDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xGamePeriodDo) Returning(value interface{}, columns ...string) *xGamePeriodDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xGamePeriodDo) Not(conds ...gen.Condition) *xGamePeriodDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xGamePeriodDo) Or(conds ...gen.Condition) *xGamePeriodDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xGamePeriodDo) Select(conds ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xGamePeriodDo) Where(conds ...gen.Condition) *xGamePeriodDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xGamePeriodDo) Order(conds ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xGamePeriodDo) Distinct(cols ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xGamePeriodDo) Omit(cols ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xGamePeriodDo) Join(table schema.Tabler, on ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xGamePeriodDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xGamePeriodDo) RightJoin(table schema.Tabler, on ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xGamePeriodDo) Group(cols ...field.Expr) *xGamePeriodDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xGamePeriodDo) Having(conds ...gen.Condition) *xGamePeriodDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xGamePeriodDo) Limit(limit int) *xGamePeriodDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xGamePeriodDo) Offset(offset int) *xGamePeriodDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xGamePeriodDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xGamePeriodDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xGamePeriodDo) Unscoped() *xGamePeriodDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xGamePeriodDo) Create(values ...*model.XGamePeriod) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xGamePeriodDo) CreateInBatches(values []*model.XGamePeriod, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xGamePeriodDo) Save(values ...*model.XGamePeriod) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xGamePeriodDo) First() (*model.XGamePeriod, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGamePeriod), nil
	}
}

func (x xGamePeriodDo) Take() (*model.XGamePeriod, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGamePeriod), nil
	}
}

func (x xGamePeriodDo) Last() (*model.XGamePeriod, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGamePeriod), nil
	}
}

func (x xGamePeriodDo) Find() ([]*model.XGamePeriod, error) {
	result, err := x.DO.Find()
	return result.([]*model.XGamePeriod), err
}

func (x xGamePeriodDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XGamePeriod, err error) {
	buf := make([]*model.XGamePeriod, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xGamePeriodDo) FindInBatches(result *[]*model.XGamePeriod, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xGamePeriodDo) Attrs(attrs ...field.AssignExpr) *xGamePeriodDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xGamePeriodDo) Assign(attrs ...field.AssignExpr) *xGamePeriodDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xGamePeriodDo) Joins(fields ...field.RelationField) *xGamePeriodDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xGamePeriodDo) Preload(fields ...field.RelationField) *xGamePeriodDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xGamePeriodDo) FirstOrInit() (*model.XGamePeriod, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGamePeriod), nil
	}
}

func (x xGamePeriodDo) FirstOrCreate() (*model.XGamePeriod, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGamePeriod), nil
	}
}

func (x xGamePeriodDo) FindByPage(offset int, limit int) (result []*model.XGamePeriod, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xGamePeriodDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xGamePeriodDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xGamePeriodDo) Delete(models ...*model.XGamePeriod) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xGamePeriodDo) withDO(do gen.Dao) *xGamePeriodDo {
	x.DO = *do.(*gen.DO)
	return x
}
