package single

import (
	"bufio"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"math"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

const Hub88CacheTaExpireTime = 1800 // 三十分钟缓存

var hub88AccessProductList = []string{
	"3Oaks",            // 第一批上
	"Retro Gaming",     // 第一批上
	"SlotMill Games",   // 第一批上
	"Booming Games",    // 第一批上
	"BetsyGames",       // 第一批上
	"Mancala Gaming",   // 第一批上
	"Habanero",         // 第一批上
	"Play'n Go",        // 第一批上
	"OneTouch Generic", // 测试服专用
	"OneTouch",         // 第二批上
	"Live88",           // 第二批上
	// "7Mojos",
	// "7Mojos Live",
	// "AirDice",
	// "Alchemy Gaming",
	// "All41",
	// "Apparat Gaming",
	// "Asia Gaming",
	// "Atomic Slot Lab",
	// "AvatarUX",
	// "BGaming",
	// "Barbarabang",
	// "BetGames.TV",
	// "Betsoft",
	// "Blue Guru",
	// "Blueprint Gaming",
	// "Buck Stakes Entertainment",
	// "Caleta Gaming",
	// "CandleBets",
	// "Elbet",
	// "Evolution Gaming",
	// "Evoplay Entertainment",
	// "Ezugi",
	// "FBastards",
	// "Fantasma",
	// "Foxium",
	// "Fugaso",
	// "Gameburger",
	// "Gametech",
	// "Gamomat",
	// "Gamzix",
	// "Genii",
	// "Golden Hero",
	// "Golden Race",
	// "Golden Rock Studios",
	// "Hacksaw Gaming",
	// "JFTW",
	// "JVL",
	// "Kalamba",
	// "Kalamba Games",
	// "Live Solutions",
	// "Lotto Instant Win",
	// "MG Live",
	// "MG Slots",
	// "Mascot Gaming",
	// "Microgaming",
	// "Neon Valley",
	// "NetEnt",
	// "NetGaming",
	// "Nolimit City",
	// "Northern Lights Gaming",
	// "Old Skool",
	// "OneGame",
	// "OneTouch Table game",
	// "Onlyplay",
	// "Oryx Gaming",
	// "PGSoft",
	// "Peter & Sons",
	// "Playson",
	// "Pragmatic Play",
	// "Pulse 8",
	// "Push Gaming",
	// "Rabcat",
	// "Red Rake Gaming",
	// "Red Tiger Gaming",
	// "Relax Gaming",
	// "Revolver Gaming",
	// "RubyPlay",
	// "SkyWind",
	// "Slingshot",
	// "Snowborn Studios",
	// "Spadegaming",
	// "SpinPlay Games",
	// "Spinomenal",
	// "Spribe",
	// "Stormcraft",
	// "Tangente",
	// "Thunderkick",
	// "TopSpin Games",
	// "Triple Edge",
	// "TrueLab",
	// "Turbogames",
	// "Wazdan",
	// "WinFast",
	// "Yolted",
	// "Zillion",
}

type Hub88Config struct {
	urlAsia               string
	operatorIdAsia        string
	urlEu                 string
	operatorIdEu          string
	public                string
	private               string
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
}

type ThirdOrderHub88 struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

type AmountChangeLogHub88 struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

type GameListHub88 struct {
	Id        int    `json:"Id" gorm:"column:Id"`
	Brand     string `json:"Brand" gorm:"column:Brand"`
	GameId    string `json:"GameId" gorm:"column:GameId"`
	Name      string `json:"Name" gorm:"column:Name"`
	EName     string `json:"EName" gorm:"column:EName"`
	State     int    `json:"State" gorm:"column:State"`
	OpenState int    `json:"OpenState" gorm:"column:OpenState"`
	GameType  int    `json:"GameType" gorm:"column:GameType"`
	HubType   int    `json:"HubType" gorm:"column:HubType"`
}

func NewHub88Logic(params map[string]string, fc func(int) error) *Hub88Config {
	return &Hub88Config{
		urlAsia:               params["url_asia"],
		operatorIdAsia:        params["operator_id_asia"],
		urlEu:                 params["url_eu"],
		operatorIdEu:          params["operator_id_eu"],
		public:                params["public"],
		private:               params["private"],
		currency:              params["currency"],
		brandName:             "hub88",
		RefreshUserAmountFunc: fc,
	}
}

func (l *Hub88Config) GetGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Line string `json:"line"` // EU 或者 ASIA
		Add  string `json:"add"`  // 1 添加数据库 非1不添加数据库
	}
	theReq := RequestData{}
	err := ctx.RequestData(&theReq)
	if err != nil {
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	operatorId := 0
	baseUrl := ""
	//if theReq.Line == "EU" {
	//	operatorId, _ = strconv.Atoi(l.operatorIdEu)
	//  baseUrl = l.urlEu
	//} else {
	operatorId, _ = strconv.Atoi(l.operatorIdAsia) // 全部走亚洲
	baseUrl = l.urlAsia
	//}

	type params struct {
		OperatorId int `json:"operator_id"`
	}

	errcode := 0

	req := params{OperatorId: operatorId}
	jbytes, _ := json.Marshal(req)
	signstr, err := Sign(l.private, jbytes)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	heardmap := map[string]string{
		"X-Hub88-Signature": signstr,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    baseUrl + "/operator/generic/v2/game/list",
		Param:      jbytes,
		Header:     heardmap,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	type resp struct {
		UrlThumb              string   `json:"url_thumb"`
		UrlBackground         string   `json:"url_background"`
		Product               string   `json:"product"`
		Platforms             []string `json:"platforms"`
		Name                  string   `json:"name"`
		GameCode              string   `json:"game_code"`
		FreebetSupport        bool     `json:"freebet_support"`
		DemoGameSupport       bool     `json:"demo_game_support"`
		PhoenixJackpotSupport bool     `json:"phoenix_jackpot_support"`
		Enabled               bool     `json:"enabled"`
		Category              string   `json:"category"`
		BlockedCountries      []string `json:"blocked_countries"`
		ReleaseDate           string   `json:"release_date"`
		Volatility            int      `json:"volatility"`
		Rtp                   string   `json:"rtp"`
		Paylines              int      `json:"paylines"`
		HitRatio              string   `json:"hit_ratio"`
		Certifications        []string `json:"certifications"`
		Languages             []string `json:"languages"`
		Theme                 []string `json:"theme"`
		Technology            []string `json:"technology"`
		Features              []string `json:"features"`
	}
	rest := make([]*resp, 0)
	_ = json.Unmarshal(b, &rest)
	rest2 := make([]*resp, 0)
	for _, v := range rest {
		rest2 = append(rest2, v)
		if !in_array_string(hub88AccessProductList, v.Product) {
			continue
		}
		Brand := "hub88_" + strings.ReplaceAll(v.Product, " ", "_")
		Brand = strings.ReplaceAll(Brand, "'", "_")
		GameType := 0
		dianzi := []string{"Video Slots", "Scratch", "Unknown", "Arcade Games", "Jackpot Slots"}
		qipai := []string{"Table Games", "Blackjack", "Roulette", "Poker", "Sic Bo", "Baccarat", "Video Poker", "Other Table Games", "Dice", "Virtual Sports", "Scratch Cards"}
		quwei := []string{"Hi Lo", "Crash", "Crash Games"}
		lottery := []string{"Bingo", "Keno", "Lottery"}
		zhenren := []string{"Live Baccarat", "Live Blackjack", "Live Roulette", "Bet On Poker", "Live Games", "Live Dealer", "Live Dice", "Dragon Tiger", "Other Live Games", "Live Poker", "Live Dragon Tiger", "Live Game Shows"}
		sport := []string{"Sportsbook"}

		if in_array_string(dianzi, v.Category) {
			GameType = 1
		} else if in_array_string(qipai, v.Category) {
			GameType = 2
		} else if in_array_string(quwei, v.Category) {
			GameType = 3
		} else if in_array_string(lottery, v.Category) {
			GameType = 4
		} else if in_array_string(zhenren, v.Category) {
			GameType = 5
		} else if in_array_string(sport, v.Category) {
			GameType = 6
		}

		// "3Oaks", "Retro Gaming", "SlotMill Games", "Booming Games", "BetsyGames", "Mancala Gaming", "Habanero", "Play'n Go" // 第一批上
		// "OneTouch", "Live88" // 第二批上
		if v.Product == "3Oaks" || v.Product == "Retro Gaming" || v.Product == "SlotMill Games" || v.Product == "Booming Games" || v.Product == "Mancala Gaming" || v.Product == "Habanero" || v.Product == "Play'n Go" {
			if GameType != 1 && GameType != 2 { // 第一批上 爆辣鸡块说 只上电子 棋牌 体育
				continue
			}
		} else if v.Product == "BetsyGames" { // 第一批上 // BetsyGames要上分类 体育
			if GameType != 6 {
				continue
			}
		} else if v.Product == "OneTouch" { // 第二批上 // OneTouch要上分类 棋牌 电子 真人 趣味
			if GameType != 1 && GameType != 2 && GameType != 3 && GameType != 5 {
				continue
			}
		} else if v.Product == "Live88" { // 第二批上 // Live88要上分类 真人
			if GameType != 5 {
				continue
			}
		} else {
			continue
		}

		if theReq.Add == "1" {
			gameList := GameListHub88{}
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", Brand, v.GameCode).First(&gameList).Error
			if err != daogorm.ErrRecordNotFound {
				continue
			}

			//写入数据库中
			gameData := xgo.H{
				"Brand":     Brand,
				"GameId":    v.GameCode,
				"Name":      v.Name,
				"EName":     v.Name,
				"GameType":  GameType,
				"HubType":   1, // 0非聚合类型 1hub88类型
				"State":     2,
				"OpenState": 1,
			}
			server.Db().Table("x_game_list").Insert(gameData)
		}
	}
	ctx.RespJson(rest2)

}

func in_array_string(arr []string, s string) bool {
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}

const cacheKeyHub = "cacheKeyHub88:"
const cacheKeyHubInfo = "cacheKeyHub88:Info:%s"
const UNIT float64 = 100000

func (l *Hub88Config) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameCode   string `validate:"required"` //游戏code
		Lang       string `validate:"required"` //语言代码
		LobbyUrl   string //我们的大厅地址
		DepositUrl string //我们的存款页面
		DeviceType string `json:"DeviceType"` // 设备类型 IOS Android Windows
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	token := server.GetToken(ctx)

	if err, errcode = base.IsLoginByUserId(cacheKeyHub, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	gameList := GameListHub88{}
	err = server.Db().GormDao().Table("x_game_list").Where("GameId = ? and HubType=1", reqdata.GameCode).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("hub88 Login 查询游戏错误 userId=", token.UserId, " gamecode=", reqdata.GameCode, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("hub88 Login 游戏不可用 userId=", token.UserId, " gamecode=", reqdata.GameCode, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	//ip
	strip := ctx.GetIp()
	if net.ParseIP(ctx.GetIp()) == nil {
		strip = "************"
	}

	// 国家
	country := ctx.Gin().Request.Header.Get("country")
	// 欧洲国家 AT奥地利 BE比利时 CY塞浦路斯 EE爱沙尼亚 FI芬兰 FR法国 DE德国 GR希腊 IE爱尔兰 IT意大利 LV拉脱维亚 LT立陶宛 卢森堡 马耳他 NL荷兰 PT葡萄牙 SK斯洛伐克 SI斯洛文尼亚 ES西班牙
	// eur := "AT,BE,CY,EE,FI,FR,DE,GR,IE,IT,LV,LT,LU,MT,NL,PT,SK,SI,ES"
	// if country != "" && strings.Contains(eur, country) { // 欧洲国家走欧洲路线
	// 	country = "EU"
	// }
	if country == "" {
		ctx.RespErrString(true, &errcode, "您的区域暂不支持")
		return
	}

	//姓名
	username := fmt.Sprintf("%d", token.UserId)

	server.Redis().SetString(fmt.Sprintf(cacheKeyHubInfo, username), country)

	//sessionId
	sessionId := base.UserId2token(cacheKeyHub, token.UserId)

	operatorId := 0
	baseUrl := ""
	// if country == "EU" {
	// 	operatorId, _ = strconv.Atoi(l.operatorIdEu)
	// 	baseUrl = l.urlEu
	// } else {
	operatorId, _ = strconv.Atoi(l.operatorIdAsia)
	baseUrl = l.urlAsia
	// }

	type meta struct {
		Rating   int    `json:"rating"`
		OddsType string `json:"oddsType"`
	}

	type thirdLogin struct {
		User         string  `json:"user"`
		Token        string  `json:"token"`
		SubPartnerId *string `json:"sub_partner_id"`
		Platform     string  `json:"platform"`
		OperatorId   int     `json:"operator_id"`
		Meta         *meta   `json:"meta"`
		LobbyUrl     string  `json:"lobby_url"`
		Lang         string  `json:"lang"`
		Ip           string  `json:"ip"`
		GameCode     string  `json:"game_code"`
		DepositUrl   string  `json:"deposit_url"`
		Currency     string  `json:"currency"`
		GameCurrency string  `json:"game_currency"`
		Country      string  `json:"country"`
	}

	platformType := "GPL_MOBILE"
	if strings.EqualFold(reqdata.DeviceType, "Windows") {
		platformType = "GPL_DESKTOP"
	}

	thirdLoginReq := thirdLogin{
		User:         username,
		Token:        sessionId,
		Platform:     platformType,
		OperatorId:   operatorId,
		LobbyUrl:     reqdata.LobbyUrl,
		Lang:         reqdata.Lang,
		Ip:           strip,
		GameCode:     reqdata.GameCode,
		DepositUrl:   reqdata.DepositUrl,
		Currency:     l.currency,
		GameCurrency: l.currency,
		Country:      country,
	}

	reqbytes, _ := json.Marshal(thirdLoginReq)
	logs.Info("hub88  请求三方登录参数:", string(reqbytes))

	signstr, err := Sign(l.private, reqbytes)

	if err != nil {
		logs.Error("hub88  请求三方签名错误:", err.Error())
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	heardmap := map[string]string{
		"X-Hub88-Signature": signstr,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    baseUrl + "/operator/generic/v2/game/url",
		Param:      reqbytes,
		Header:     heardmap,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	type resp struct {
		Url   string `json:"url"`
		Error string `json:"error"`
	}
	rest := resp{}
	_ = json.Unmarshal(b, &rest)
	if rest.Error != "" {
		logs.Error("hub88  请求三方返回的错误rest.Error:", rest.Error)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	ctx.Put("url", rest.Url)
	ctx.RespOK()
}

func (l *Hub88Config) getRoundDetail(reqUser, reqUuid, reqRound string) (detailUrl string) {
	type RequestData struct {
		User            string `json:"user"`             // 用户
		TransactionUuid string `json:"transaction_uuid"` // 请求唯一ID
		Round           string `json:"round"`            // 轮次
		OperatorId      int    `json:"operator_id"`
	}

	opId, _ := strconv.Atoi(l.operatorIdAsia)
	baseUrl := l.urlAsia
	req := RequestData{
		User:            reqUser,
		TransactionUuid: reqUuid,
		Round:           reqRound,
		OperatorId:      opId,
	}

	reqbytes, _ := json.Marshal(req)
	logs.Info("hub88  请求三方开奖结果 参数:", string(reqbytes))

	signstr, err := Sign(l.private, reqbytes)

	if err != nil {
		logs.Error("hub88  请求三方开奖结果签名错误:", err.Error())
		return
	}
	heardmap := map[string]string{
		"X-Hub88-Signature": signstr,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    baseUrl + "/operator/generic/v2/game/round",
		Param:      reqbytes,
		Header:     heardmap,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		logs.Error("hub88  请求三方开奖结果 请求错误:", err.Error())
		return
	}
	logs.Info("hub88  请求三方开奖结果 响应:", string(b))

	type resp struct {
		Url string `json:"url"`
	}
	rest := resp{}
	_ = json.Unmarshal(b, &rest)
	detailUrl = rest.Url
	return
}

func (l *Hub88Config) Info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		User        string `json:"user"`
		RequestUuid string `json:"request_uuid"`
	}

	type RespData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Country     string `json:"country"`
		// Jurisdiction     string   `json:"jurisdiction"`
		// SubPartnerId     string   `json:"sub_partner_id"`
		// BirthDate        string   `json:"birth_date"`
		// RegistrationDate string   `json:"registration_date"`
		// Tags             []string `json:"tags"`
		// Sex              string   `json:"sex"` //MALE FEMALE
		// AffiliateId      string   `json:"affiliate_id"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88  Info api  Body := ", string(bodyBytes))

	res := RespData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Info api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Info api  Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名

	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88  Info api  signature := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88  Info api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	//获取国家
	countryAny := server.Redis().Get(fmt.Sprintf(cacheKeyHubInfo, reqdata.User))
	country := abugo.GetStringFromInterface(countryAny)
	if country == "" {
		logs.Error("hub88  Info api  get country key=", fmt.Sprintf(cacheKeyHubInfo, reqdata.User), " countryAny=", countryAny, " country=", country)
	}

	userId, _ := strconv.Atoi(reqdata.User)

	if err, _ = base.IsLoginByUserId(cacheKeyHub, userId); err != nil {
		res.Status = "RS_ERROR_INVALID_TOKEN"
		res.RequestUuid = reqdata.RequestUuid
		res.User = reqdata.User
		res.Country = country
		logs.Error("hub88  Info api  reply := ", res)
		ctx.RespJson(res)
		return
	}

	res.Status = "RS_OK"
	res.RequestUuid = reqdata.RequestUuid
	res.User = reqdata.User
	res.Country = country
	logs.Error("hub88  Info api  reply := ", res)
	ctx.RespJson(res)
	return

}

func (l *Hub88Config) Authorize(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Token       string `json:"token"`
		GameCode    string `json:"game_code"`
		RequestUuid string `json:"request_uuid"`
	}

	type respData struct {
		Token       string `json:"token"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Currency    string `json:"currency"`
		Balance     int64  `json:"balance"`
	}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88  Authorize api  Body := ", string(bodyBytes))

	res := respData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Authorize api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Authorize api  Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名
	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88  Authorize api  Header := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88  Authorize api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKeyHub, reqdata.Token)
	if userId == -1 {
		res.Status = "RS_ERROR_TOKEN_EXPIRED"
		res.RequestUuid = reqdata.RequestUuid
		res.Token = reqdata.Token
		logs.Error("hub88  Authorize api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	//获取余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyHub)
	if err != nil {
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		res.Token = reqdata.Token
		logs.Error("hub88  Authorize api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	balance2 := int64(balance * UNIT)
	//sessionId
	sessionId := base.UserId2token(cacheKeyHub, userId)

	res.Token = sessionId
	res.Status = "RS_OK"
	res.RequestUuid = reqdata.RequestUuid
	res.Currency = l.currency
	res.Balance = balance2
	logs.Info("hub88  Authorize api  reply := ", res, err)
	ctx.RespJson(res)
	return

}

func (l *Hub88Config) Balance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		User        string `json:"user"`
		Token       string `json:"token"`
		RequestUuid string `json:"request_uuid"`
		GameCode    string `json:"game_code"`
	}

	type respData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Currency    string `json:"currency"`
		Balance     int64  `json:"balance"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88  Balance api  Body := ", string(bodyBytes))
	res := respData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Balance api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Balance api  Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名

	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88  Balance api  signature := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88  Balance api  reply := ", res)
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKeyHub, reqdata.Token)
	if userId == -1 {
		res.Status = "RS_ERROR_TOKEN_EXPIRED"
		res.RequestUuid = reqdata.RequestUuid
		logs.Info("hub88  Balance api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	//获取余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyHub)
	if err != nil {
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		logs.Info("hub88  Balance api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}
	balance2 := int64(balance * UNIT)

	res.User = fmt.Sprint(userId)
	res.Status = "RS_OK"
	res.RequestUuid = reqdata.RequestUuid
	res.Currency = l.currency
	res.Balance = balance2
	logs.Info("hub88  Balance api  reply := ", res, err)
	ctx.RespJson(res)
	return
}

func (l *Hub88Config) Win(ctx *abugo.AbuHttpContent) {
	type MetaData struct {
		Selection string  `json:"selection"`
		Odds      float64 `json:"odds"`
	}
	type RequestData struct {
		User                     string   `json:"user"`
		TransactionUuid          string   `json:"transaction_uuid"`
		SupplierTransactionId    string   `json:"supplier_transaction_id"`
		Token                    string   `json:"token"`
		SupplierUser             string   `json:"supplier_user"`
		RoundClosed              bool     `json:"round_closed"`
		Round                    string   `json:"round"`
		RewardUuid               string   `json:"reward_uuid"`
		RequestUuid              string   `json:"request_uuid"`
		ReferenceTransactionUuid string   `json:"reference_transaction_uuid"`
		IsFree                   bool     `json:"is_free"`
		IsAggregated             bool     `json:"is_aggregated"`
		GameCode                 string   `json:"game_code"`
		Currency                 string   `json:"currency"`
		Bet                      string   `json:"bet"`
		Amount                   int64    `json:"amount"`
		Meta                     MetaData `json:"meta"`
	}

	type RespData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Currency    string `json:"currency"`
		Balance     int64  `json:"balance"`
	}

	type CacheRequest struct {
		Req RequestData `json:"req"`
		Res RespData    `json:"res"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88  Win api  Body := ", string(bodyBytes))

	res := RespData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Win api get body reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Win api body Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名

	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88  Win api  signature := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88  Win api VerifySignature reply := ", res)
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKeyHub, reqdata.Token)
	if userId == -1 {
		userId, _ = strconv.Atoi(reqdata.User)
		if userId <= 0 {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_USER_DISABLED"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			logs.Error("hub88  Win api =1 reply := ", res)
			ctx.RespJson(res)
			return
		}
	}

	// win transaction缓存
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:hub88:win:ta:%v:%v", server.Project(), server.Module(), userId, reqdata.TransactionUuid)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.TransactionUuid == reqdata.TransactionUuid && cacheRes.Req.Round == reqdata.Round && cacheRes.Req.Amount == reqdata.Amount && cacheRes.Req.ReferenceTransactionUuid == reqdata.ReferenceTransactionUuid {
				res = cacheRes.Res
				res.RequestUuid = reqdata.RequestUuid
				logs.Debug("hub88 重复请求结算:  win ta = ", reqdata.TransactionUuid, res)
				ctx.RespJson(res)
				return
			}
		} else {
			logs.Error("hub88 重复结算事务:  win ta = ", reqdata.TransactionUuid, "反序列化返回结果错误", e)
		}

		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_DUPLICATE_TRANSACTION"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		ctx.RespJson(res)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: reqdata, Res: res}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, Hub88CacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("hub88 结算请求 设置响应缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()

	udata, balance, err := base.GetUserBalance(userId, cacheKeyHub)
	balance2 := int64(balance * UNIT)
	if err != nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88  Win api =2  reply := ", res)
		ctx.RespJson(res)
		return
	}

	//三方来源的数据整理
	var (
		amount    = float64(reqdata.Amount) / UNIT
		thirdId   = fmt.Sprintf("%s_%s_%s_%s", reqdata.GameCode, reqdata.User, reqdata.Round, reqdata.ReferenceTransactionUuid)
		gamecode  = reqdata.GameCode
		thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	)
	//根据游戏获取分类
	gameList, _ := server.XDb().Table("x_game_list").Where("GameId = ? AND HubType=1", gamecode).First()
	if gameList == nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88 Win api 游戏ID获取失败 thirdId=", thirdId, " GameId=", gamecode)
		ctx.RespJson(res)
		return
	}
	gameType := abugo.GetInt64FromInterface(gameList.RawData["GameType"])
	brandName := abugo.GetStringFromInterface(gameList.RawData["Brand"])
	gameName := abugo.GetStringFromInterface(gameList.RawData["Name"])
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人
	var tablepre string
	var goldType int
	var table string
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
		// } else if gameType == 3 {
		// 	tablepre = "x_third_quwei_pre_order"
		// 	table = "x_third_quwei"
		// } else if gameType == 4 {
		// 	tablepre = "x_third_lottery_pre_order"
		// 	table = "x_third_lottery"
	} else if gameType == 5 {
		tablepre = "x_third_live_pre_order"
		table = "x_third_live"
	} else if gameType == 6 {
		tablepre = "x_third_sport_pre_order"
		table = "x_third_sport"
	} else {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		ctx.RespJson(res)
		logs.Error("hub88 Win api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType)
		return
	}

	goldType = l.getHub88GoldChangeType(K_HUB88CHANGETYPESETTLE, brandName)
	if goldType == 0 {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_UNKNOWN"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88  Win api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablepre=", tablepre, " reply= ", res)
		ctx.RespJson(res)
		return
	}

	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderHub88{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 订单不存在  reply := ", res, e)
				ctx.RespJson(res)
				return e
			} else {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 从预设表查询注单错误 id=", thirdId, " e=", e, " tablepre=", tablepre, " reply= ", res)
				ctx.RespJson(res)
				return e
			}
		}

		dataState := betTran.DataState
		if dataState == -2 { // 订单已被撤销
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Win api 订单已被撤销 id=", thirdId, " reply= ", res, " betTran=", betTran)
			ctx.RespJson(res)
			return errors.New("订单已被撤销")
		}

		logs.Info("hub88 结算订单数据: thirdId=", thirdId, " betTran=", betTran)

		if dataState == -1 { // 还没有被移至正式表的处理方式
			//将下注订单移动至结算订单表
			//修改成已经结算了
			betId := betTran.Id
			betAmount := betTran.BetAmount
			validBet := betAmount

			if betId == 0 {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 订单Id是0 id=", thirdId, " reply= ", res, " betTran=", betTran)
				ctx.RespJson(res)
				return errors.New("订单未找到")
			}

			winAmount := amount
			// 除了体育所有三方有效流水取不大于下注金额的输赢绝对值，体育有效流水取下注金额
			if gameType != 6 {
				validBet = math.Abs(winAmount - betAmount)
				if validBet > math.Abs(betAmount) {
					validBet = math.Abs(betAmount)
				}
			}

			betTran.WinAmount = winAmount
			betTran.ValidBet = validBet
			betTran.RawData = string(bodyBytes)
			betTran.GameId = gamecode
			betTran.GameName = gameName
			betTran.ThirdTime = thirdTime
			betTran.DataState = 1

			resultTmp := tx.Table(tablepre).Where("Id=?", betId).Updates(map[string]interface{}{
				"WinAmount": winAmount,
				"ValidBet":  validBet,
				"RawData":   string(bodyBytes),
				"GameId":    gamecode,
				"GameName":  gameName,
				"ThirdTime": thirdTime,
				"DataState": 1,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 修改订单错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " e=", e)
				ctx.RespJson(res)
				return e
			}
			//移动至统计表
			betTran.Id = 0
			betTran.CreateTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			e = tx.Table(table).Create(&betTran).Error
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 插入正式表记录错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
				ctx.RespJson(res)
				return e
			}
		} else if dataState == 1 {
			// 已经被移动至正式表的处理方式(一单多赢情况)
			betId := betTran.Id
			winAmount := amount

			if betId == 0 {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 订单Id是0 id=", thirdId, " reply= ", res, " betTran=", betTran)
				ctx.RespJson(res)
				return errors.New("订单未找到")
			}

			betTran.WinAmount += winAmount
			betTran.RawData += string(bodyBytes)
			betTran.ThirdTime = thirdTime
			resultTmp := tx.Table(tablepre).Where("Id=?", betId).Updates(map[string]interface{}{
				"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
				"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 多赢修改结算金额错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
				ctx.RespJson(res)
				return e
			}

			resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Updates(map[string]interface{}{
				"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
				"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 多赢修改结算金额错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
				ctx.RespJson(res)
				return e
			}
		} else {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Win api 订单状态不是-1或1 id=", thirdId, " reply= ", res, " betTran=", betTran)
			ctx.RespJson(res)
			return errors.New("订单状态不正确")
		}

		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Win api 修改用户余额错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
				ctx.RespJson(res)
				return errors.New("修改x_user失败了")
			}
		}

		amountLog := AmountChangeLogHub88{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       amount,
			AfterAmount:  balance + amount,
			Reason:       goldType,
			Memo:         brandName + " settle,thirdId:" + thirdId,
			SellerId:     int(abugo.GetInt64FromInterface((*udata)["SellerId"])),
			ChannelId:    int(abugo.GetInt64FromInterface((*udata)["ChannelId"])),
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Win api 插入账变记录错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " amountLog=", amountLog, " e=", e)
			ctx.RespJson(res)
			return e
		}

		res.User = fmt.Sprint(userId)
		res.Status = "RS_OK"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = int64((balance + amount) * UNIT)
		logs.Debug("hub88 Win 結算成功", res, " thirdId:", thirdId, " betTran=", betTran)
		ctx.RespJson(res)
		return nil
	})
	if err == nil {
		go func() {
			// time.Sleep(time.Second * 2)
			url := l.getRoundDetail(res.User, reqdata.RequestUuid, reqdata.Round)
			if url != "" && thirdId != "" && brandName != "" {
				if e := server.Db().GormDao().Table(table).Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("hub88 异步更新开奖结果 错误 userId=", userId, "table=", table, "brand=", brandName, "gameCode=", reqdata.GameCode, " thirdId=", thirdId)
				}
				if e := server.Db().GormDao().Table(tablepre).Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("hub88 异步更新开奖结果 错误 userId=", userId, "tablepre=", tablepre, "brand=", brandName, "gameCode=", reqdata.GameCode, " thirdId=", thirdId)
				}
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][Hub88Config] Win 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][Hub88Config] Win 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (l *Hub88Config) Rollback(ctx *abugo.AbuHttpContent) {
	type meta struct {
		Selection string  `json:"selection"`
		Odds      float64 `json:"odds"`
	}
	type RequestData struct {
		User                     string `json:"user"`
		TransactionUuid          string `json:"transaction_uuid"`
		SupplierTransactionId    string `json:"supplier_transaction_id"`
		Token                    string `json:"token"`
		RoundClosed              bool   `json:"round_closed"`
		Round                    string `json:"round"`
		RewardUuid               string `json:"reward_uuid"`
		RequestUuid              string `json:"request_uuid"`
		ReferenceTransactionUuid string `json:"reference_transaction_uuid"`
		GameCode                 string `json:"game_code"`
		Meta                     *meta  `json:"meta"`
	}

	type RespData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Currency    string `json:"currency"`
		Balance     int64  `json:"balance"`
	}

	type CacheRequest struct {
		Req RequestData `json:"req"`
		Res RespData    `json:"res"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88  Rollback api Body := ", string(bodyBytes))

	res := RespData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Rollback api get body reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88  Rollback api body Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名

	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88  Rollback api signature := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88  Rollback api VerifySignature reply := ", res)
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKeyHub, reqdata.Token)
	if userId == -1 {
		userId, _ = strconv.Atoi(reqdata.User)
		if userId <= 0 {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_USER_DISABLED"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			logs.Error("hub88  Rollback api 查询用户ID错误 reply := ", res)
			ctx.RespJson(res)
			return
		}
	}

	// rollback transaction缓存
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:hub88:rollback:ta:%v:%v", server.Project(), server.Module(), userId, reqdata.TransactionUuid)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.TransactionUuid == reqdata.TransactionUuid && cacheRes.Req.Round == reqdata.Round && cacheRes.Req.ReferenceTransactionUuid == reqdata.ReferenceTransactionUuid {
				res = cacheRes.Res
				res.RequestUuid = reqdata.RequestUuid
				logs.Debug("hub88 重复请求回滚:  rollback ta = ", reqdata.TransactionUuid, res)
				ctx.RespJson(res)
				return
			}
		} else {
			logs.Error("hub88 重复回滚事务:  rollback ta = ", reqdata.TransactionUuid, "反序列化返回结果错误", e)
		}

		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_DUPLICATE_TRANSACTION"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		ctx.RespJson(res)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: reqdata, Res: res}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, Hub88CacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("hub88 回滚请求 设置响应缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()

	udata, balance, err := base.GetUserBalance(userId, cacheKeyHub)
	balance2 := int64(balance * UNIT)
	if err != nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88  Rollback api 获取用户余额错误 reply := ", res)
		ctx.RespJson(res)
		return
	}

	//三方来源的数据整理
	var (
		thirdId  = fmt.Sprintf("%s_%s_%s_%s", reqdata.GameCode, reqdata.User, reqdata.Round, reqdata.ReferenceTransactionUuid)
		gamecode = reqdata.GameCode
	)

	// 读取reference_transaction_uuid对应的ID
	{
		type RequestDataBet struct {
			User            string `json:"user"`
			TransactionUuid string `json:"transaction_uuid"`
		}
		type CacheRequestBet struct {
			Req RequestDataBet `json:"req"`
			Res RespData       `json:"res"`
		}
		type RequestDataWin struct {
			User                     string `json:"user"`
			TransactionUuid          string `json:"transaction_uuid"`
			ReferenceTransactionUuid string `json:"reference_transaction_uuid"`
		}
		type CacheRequestWin struct {
			Req RequestDataWin `json:"req"`
			Res RespData       `json:"res"`
		}
		cacheKeyTaBet := fmt.Sprintf("%v:%v:cache:hub88:bet:ta:%v:%v", server.Project(), server.Module(), userId, reqdata.ReferenceTransactionUuid)
		if cacheValueBet := server.Redis().Get(cacheKeyTaBet); cacheValueBet != nil {
			cacheResBet := CacheRequestBet{}
			if e := json.Unmarshal([]byte(cacheValueBet.([]byte)), &cacheResBet); e == nil {
				// 回滚下注
				if cacheResBet.Res.Status == "RS_OK" && cacheResBet.Req.TransactionUuid != "" {
					thirdId = fmt.Sprintf("%s_%s_%s_%s", reqdata.GameCode, reqdata.User, reqdata.Round, cacheResBet.Req.TransactionUuid)
				}
			}
		} else {
			cacheKeyTaWin := fmt.Sprintf("%v:%v:cache:hub88:win:ta:%v:%v", server.Project(), server.Module(), userId, reqdata.ReferenceTransactionUuid)
			if cacheValueWin := server.Redis().Get(cacheKeyTaWin); cacheValueWin != nil {
				cacheResWin := CacheRequestWin{}
				if e := json.Unmarshal([]byte(cacheValueWin.([]byte)), &cacheResWin); e == nil {
					// 回滚结算
					if cacheResWin.Res.Status == "RS_OK" && cacheResWin.Req.ReferenceTransactionUuid != "" {
						thirdId = fmt.Sprintf("%s_%s_%s_%s", reqdata.GameCode, reqdata.User, reqdata.Round, cacheResWin.Req.ReferenceTransactionUuid)
					}
				}
			}
		}
	}

	//根据游戏获取分类
	gameList, _ := server.XDb().Table("x_game_list").Where("GameId = ? AND HubType=1", gamecode).First()
	if gameList == nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88 WRollbackin api 游戏ID获取失败 thirdId=", thirdId, " GameId=", gamecode)
		ctx.RespJson(res)
		return
	}
	gameType := abugo.GetInt64FromInterface(gameList.RawData["GameType"])
	brandName := abugo.GetStringFromInterface(gameList.RawData["Brand"])
	gameName := abugo.GetStringFromInterface(gameList.RawData["Name"])
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人
	var tablepre string
	var goldType int
	var table string
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
		// } else if gameType == 3 {
		// 	tablepre = "x_third_quwei_pre_order"
		// 	table = "x_third_quwei"
		// } else if gameType == 4 {
		// 	tablepre = "x_third_lottery_pre_order"
		// 	table = "x_third_lottery"
	} else if gameType == 5 {
		tablepre = "x_third_live_pre_order"
		table = "x_third_live"
	} else if gameType == 6 {
		tablepre = "x_third_sport_pre_order"
		table = "x_third_sport"
	} else {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88 Rollback api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType)
		ctx.RespJson(res)
		return
	}

	goldType = l.getHub88GoldChangeType(K_HUB88CHANGETYPEROLLBACK, brandName)
	if goldType == 0 {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_UNKNOWN"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88  Rollback api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablepre=", tablepre, " reply= ", res)
		ctx.RespJson(res)
		return
	}

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderHub88{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 订单不存在 thirdId=", thirdId, " reply= ", res)
				ctx.RespJson(res)
				return errors.New("订单不存在")
			} else {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 从预设表查询注单错误 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " e=", e)
				ctx.RespJson(res)
				return e
			}
		}
		if betTran.DataState == 1 {
			//回滚已结算订单
			logs.Error("hub88  Rollback 异常回滚已结算订单 thirdId=", thirdId, " reply= ", res, " table=", table)
		}

		dataState := betTran.DataState
		rollbackDataState := dataState
		rollbackName := "rollbackbet"
		if dataState == -1 {
			rollbackDataState = -2
			rollbackName = "rollbackbet"
		} else if dataState == 1 {
			rollbackDataState = -1
			rollbackName = "rollbackwin"
		} else if dataState == -2 {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_OK"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Rollback 订单已撤销 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " betTran=", betTran)
			ctx.RespJson(res)
			return errors.New("订单已撤销")
		} else {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Rollback 订单状态不对 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " betTran=", betTran)
			ctx.RespJson(res)
			return errors.New("订单状态不对")
		}

		betId := betTran.Id
		amount := float64(0)
		if dataState == -1 { // 回滚未开奖
			resultTmp := tx.Table(tablepre).Where("Id=?", betId).Updates(map[string]interface{}{
				"GameName":  gameName,
				"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				"DataState": rollbackDataState,
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 修改订单错误 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " betTran=", betTran, " e=", e)
				ctx.RespJson(res)
				return errors.New("修改订单失败")
			}
			amount = betTran.BetAmount
		} else if dataState == 1 {
			amount = -betTran.WinAmount
			resultTmp := tx.Table(tablepre).Where("Id=?", betId).Updates(map[string]interface{}{
				"GameName":  gameName,
				"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				"DataState": rollbackDataState,
				"WinAmount": 0,
				"ValidBet":  0,
				"GameRst":   "",
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 修改订单错误 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " betTran=", betTran, " e=", e)
				ctx.RespJson(res)
				return errors.New("修改订单状态失败")
			}
			// 修改正式表记录
			resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Updates(map[string]interface{}{
				"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
				"DataState": -2,
				"ThirdId":   daogorm.Expr("CONCAT(ThirdId, ?)", betId),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 从正式表删除注单错误 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran, " e=", e)
				ctx.RespJson(res)
				return e
			}
		} else {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Rollback 订单状态不对 thirdId=", thirdId, " reply= ", res, " tablepre=", tablepre, " betTran=", betTran, " e=", e)
			ctx.RespJson(res)
			return e
		}

		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88  Rollback 修改用户余额错误 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran, " e=", e)
				ctx.RespJson(res)
				return errors.New("修改x_user失败了")
			}
		}

		amountLog := AmountChangeLogHub88{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       amount,
			AfterAmount:  balance + amount,
			Reason:       goldType,
			Memo:         brandName + " " + rollbackName + ",thirdId:" + thirdId,
			SellerId:     int(abugo.GetInt64FromInterface((*udata)["SellerId"])),
			ChannelId:    int(abugo.GetInt64FromInterface((*udata)["ChannelId"])),
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88  Rollback 插入账变记录错误 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			ctx.RespJson(res)
			return e
		}
		res.User = fmt.Sprint(userId)
		res.Status = "RS_OK"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = int64((balance + amount) * UNIT)

		logs.Info("hub88  Rollback 订单取消成功 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran)
		ctx.RespJson(res)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][Hub88Config] Rollback 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][Hub88Config] Rollback 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (l *Hub88Config) Bet(ctx *abugo.AbuHttpContent) {

	type MetaData struct {
		Selection string  `json:"selection"`
		Odds      float64 `json:"odds"`
	}
	type RequestData struct {
		User                  string   `json:"user"`
		TransactionUuid       string   `json:"transaction_uuid"`
		SupplierTransactionId string   `json:"supplier_transaction_id"`
		Token                 string   `json:"token"`
		SupplierUser          string   `json:"supplier_user"`
		RoundClosed           bool     `json:"round_closed"`
		Round                 string   `json:"round"`
		RewardUuid            string   `json:"reward_uuid"`
		RequestUuid           string   `json:"request_uuid"`
		IsFree                bool     `json:"is_free"`
		IsAggregated          bool     `json:"is_aggregated"`
		GameCode              string   `json:"game_code"`
		Currency              string   `json:"currency"`
		Bet                   string   `json:"bet"`
		Amount                int64    `json:"amount"`
		Meta                  MetaData `json:"meta"`
	}

	type RespData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Currency    string `json:"currency"`
		Balance     int64  `json:"balance"`
	}

	type CacheRequest struct {
		Req RequestData `json:"req"`
		Res RespData    `json:"res"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("hub88 Bet api Body=", string(bodyBytes))

	res := RespData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88 Bet api get body reply=", res, err)
		ctx.RespJson(res)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("hub88 Bet api body Unmarshal=", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名

	signature := ctx.Gin().Request.Header.Get("X-Hub88-Signature")
	logs.Info("hub88 Bet api  signature := ", signature)

	err = VerifySignature(l.public, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("hub88 Bet api VerifySignature reply := ", res)
		ctx.RespJson(res)
		return
	}

	userId := base.Token2UserId(cacheKeyHub, reqdata.Token)
	if userId == -1 {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88 Bet api 查询用户ID错误 reply := ", res)
		ctx.RespJson(res)
		return
	}

	// bet transaction缓存
	cacheKeyTa := fmt.Sprintf("%v:%v:cache:hub88:bet:ta:%v:%v", server.Project(), server.Module(), userId, reqdata.TransactionUuid)
	if cacheValue := server.Redis().Get(cacheKeyTa); cacheValue != nil {
		cacheRes := CacheRequest{}
		if e := json.Unmarshal([]byte(cacheValue.([]byte)), &cacheRes); e == nil {
			if cacheRes.Req.TransactionUuid == reqdata.TransactionUuid && cacheRes.Req.Round == reqdata.Round && cacheRes.Req.Amount == reqdata.Amount {
				res = cacheRes.Res
				res.RequestUuid = reqdata.RequestUuid
				logs.Debug("hub88 Bet 重复请求下注:  bet ta = ", reqdata.TransactionUuid, res)
				ctx.RespJson(res)
				return
			}
			logs.Error("hub88 Bet 下注事务相同 内容不容:  bet ta = ", reqdata.TransactionUuid, reqdata, cacheRes)
		} else {
			logs.Error("hub88 Bet 重复下注事务:  bet ta = ", reqdata.TransactionUuid, "反序列化返回结果错误", e)
		}

		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_DUPLICATE_TRANSACTION"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		ctx.RespJson(res)
		return
	}

	defer func() {
		// 设置请求缓存
		cacheData := CacheRequest{Req: reqdata, Res: res}
		cacheValueByte, _ := json.Marshal(cacheData)
		if e := server.Redis().SetStringEx(cacheKeyTa, Hub88CacheTaExpireTime, string(cacheValueByte)); e != nil {
			logs.Error("hub88 Bet 下注请求 设置重复请求缓存错误:  cacheKeyTa = ", cacheKeyTa, e)
		}
	}()

	udata, balance, err := base.GetUserBalance(userId, cacheKeyHub)
	balance2 := int64(balance * UNIT)
	if err != nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_USER_DISABLED"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88 Bet api 获取用户余额错误 reply := ", res)
		ctx.RespJson(res)
		return
	}

	if reqdata.Amount < 0 {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_CURRENCY"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88 Bet api 下注金额错误 reply := ", res)
		ctx.RespJson(res)
		return
	}

	//三方来源的数据整理
	var (
		betAmount = float64(reqdata.Amount) / UNIT
		thirdId   = fmt.Sprintf("%s_%s_%s_%s", reqdata.GameCode, reqdata.User, reqdata.Round, reqdata.TransactionUuid)
		gamecode  = reqdata.GameCode
		thirdTime = time.Now()
	)
	//根据游戏获取分类
	gameList, _ := server.XDb().Table("x_game_list").Where("GameId = ? AND HubType=1", gamecode).First()
	if gameList == nil {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88 Bet api 游戏ID获取失败 thirdId=", thirdId, " GameId=", gamecode)
		ctx.RespJson(res)
		return
	}
	gameType := abugo.GetInt64FromInterface(gameList.RawData["GameType"])
	brandName := abugo.GetStringFromInterface(gameList.RawData["Brand"])
	gameName := abugo.GetStringFromInterface(gameList.RawData["Name"])
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人
	var tablepre string
	var goldType int
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		// } else if gameType == 3 {
		// 	tablepre = "x_third_quwei_pre_order"
		// } else if gameType == 4 {
		// tablepre = "x_third_lottery_pre_order"
	} else if gameType == 5 {
		tablepre = "x_third_live_pre_order"
	} else if gameType == 6 {
		tablepre = "x_third_sport_pre_order"
	} else {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_WRONG_TYPES"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88 Bet api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", res)
		ctx.RespJson(res)
		return
	}
	goldType = l.getHub88GoldChangeType(K_HUB88CHANGETYPEBET, brandName)
	if goldType == 0 {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_UNKNOWN"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = balance2
		logs.Error("hub88  Bet api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablepre=", tablepre, " reply= ", res)
		ctx.RespJson(res)
		return
	}

	if betAmount > balance {
		res.User = fmt.Sprint(userId)
		res.Status = "RS_ERROR_NOT_ENOUGH_MONEY"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		logs.Error("hub88 Bet api 用户余额不足 thirdId=", thirdId, " reply := ", res, betAmount, balance)
		ctx.RespJson(res)
		return
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		//查询订单是否存在
		betTran := ThirdOrderHub88{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			if e != daogorm.ErrRecordNotFound {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88 Bet api 从预设表查询注单错误 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " e=", e)
				ctx.RespJson(res)
				return e
			}
		}

		if betTran.Id > 0 { //订单已存在
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_DUPLICATE_TRANSACTION"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88 Bet api 订单已存在 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " betTran=", betTran)
			ctx.RespJson(res)
			return errors.New("订单已存在")
		}

		if betAmount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ? and amount>= ?", userId, betAmount).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount-?", betAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_NOT_ENOUGH_MONEY"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88 Bet api 修改用户余额错误 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " e=", e)
				ctx.RespJson(res)
				return errors.New("修改x_user失败了")
			}
		}

		afterBalance := balance - betAmount
		order := ThirdOrderHub88{
			SellerId:     int(abugo.GetInt64FromInterface((*udata)["SellerId"])),
			ChannelId:    int(abugo.GetInt64FromInterface((*udata)["ChannelId"])),
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        brandName,
			ThirdId:      thirdId,
			GameId:       gamecode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			DataState:    -1,
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
			BetCtx:       reqdata.Meta.Selection,
			BetCtxType:   1,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			res.User = fmt.Sprint(userId)
			res.Status = "RS_ERROR_UNKNOWN"
			res.RequestUuid = reqdata.RequestUuid
			res.Currency = l.currency
			res.Balance = balance2
			logs.Error("hub88 Bet api 新增注单错误 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " order=", order, " e=", e)
			ctx.RespJson(res)
			return e
		}

		if betAmount > 0 {
			amountLog := AmountChangeLogHub88{
				UserId:       userId,
				BeforeAmount: balance,
				Amount:       0 - betAmount,
				AfterAmount:  afterBalance,
				Reason:       goldType,
				Memo:         brandName + " bet,thirdId:" + thirdId,
				SellerId:     int(abugo.GetInt64FromInterface((*udata)["SellerId"])),
				ChannelId:    int(abugo.GetInt64FromInterface((*udata)["ChannelId"])),
				CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				res.User = fmt.Sprint(userId)
				res.Status = "RS_ERROR_UNKNOWN"
				res.RequestUuid = reqdata.RequestUuid
				res.Currency = l.currency
				res.Balance = balance2
				logs.Error("hub88 Bet api 添加账变记录错误 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " order=", order, " e=", e)
				ctx.RespJson(res)
				return e
			}
		}

		res.User = fmt.Sprint(userId)
		res.Status = "RS_OK"
		res.RequestUuid = reqdata.RequestUuid
		res.Currency = l.currency
		res.Balance = int64(afterBalance * UNIT)
		logs.Info("hub88 Bet api 下注成功 thirdId=", thirdId, " tablepre=", tablepre, " reply=", res, " order=", order)
		ctx.RespJson(res)

		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][Hub88Config] Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][Hub88Config] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func wordWrap(body string, chunklen uint) string {
	end := "\r\n"
	runes, erunes := []rune(body), []rune(end)
	l := uint(len(runes))
	if l <= 1 || l < chunklen {
		return body + end
	}
	ns := make([]rune, 0, len(runes)+len(erunes))
	var i uint
	for i = 0; i < l; i += chunklen {
		if i+chunklen > l {
			ns = append(ns, runes[i:]...)
		} else {
			ns = append(ns, runes[i:i+chunklen]...)
		}
		ns = append(ns, erunes...)
	}
	return strings.TrimRight(string(ns), end)
}

// isPublic 是否是公钥
func getRSA2ByWordWrap(body string, isPublic bool) []byte {
	var s string
	if !isPublic {
		s = fmt.Sprintf("-----BEGIN RSA PRIVATE KEY-----\r\n%s\r\n-----END RSA PRIVATE KEY-----", wordWrap(body, 64))
	} else {
		s = fmt.Sprintf("-----BEGIN  PUBLIC KEY-----\r\n%s\r\n-----END  PUBLIC KEY-----", wordWrap(body, 64))
	}
	f := "aaa.txt"
	file, err := os.Create(f)
	if err != nil {
		fmt.Println(err)
	}
	defer func() {
		file.Close()
		err = os.Remove(f)
		if err != nil {
			fmt.Println(err)
		}
	}()

	writer := bufio.NewWriter(file)
	_, err = writer.WriteString(s)
	if err != nil {
		fmt.Println(err)
	}
	writer.Flush()

	content, err := os.ReadFile(f)
	if err != nil {
		fmt.Println(err)
	}

	return content
}

// 签名函数
func Sign(privateKeyStr string, data []byte) (string, error) {
	privateKeyBytes := getRSA2ByWordWrap(privateKeyStr, false)
	block, _ := pem.Decode(privateKeyBytes)
	if block == nil {
		return "", errors.New("private key decode error")
	}
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}

	// 计算hash
	h := sha256.Sum256(data)

	// 使用私钥进行签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, h[:])
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// 验证签名函数
func VerifySignature(publicStr string, data []byte, signature []byte) error {
	publicKeyBytes := getRSA2ByWordWrap(publicStr, true)
	block, _ := pem.Decode(publicKeyBytes)
	if block == nil {
		return errors.New("public key decode error")
	}
	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return err
	}

	// 计算hash
	h := sha256.Sum256(data)
	signature1, err := base64.StdEncoding.DecodeString(string(signature))
	if err != nil {
		return err
	}
	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey.(*rsa.PublicKey), crypto.SHA256, h[:], signature1)
	if err != nil {
		return err
	}

	return nil
}

const K_HUB88CHANGETYPEBET int = 1
const K_HUB88CHANGETYPESETTLE int = 2
const K_HUB88CHANGETYPEROLLBACK int = 3

/*
"3Oaks",            // 第一批上
"Retro Gaming",     // 第一批上
"SlotMill Games",   // 第一批上
"Booming Games",    // 第一批上
"BetsyGames",       // 第一批上
"Mancala Gaming",   // 第一批上
"Habanero",         // 第一批上
"Play'n Go",        // 第一批上
"OneTouch Generic", // 测试服专用
"OneTouch",    // 第二批上
"Live88", // 第二批上
*/
func (l *Hub88Config) getHub88GoldChangeType(t int, brand string) (goldType int) {
	goldType = 0
	switch brand {
	case "hub88_3Oaks":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub883OaksBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub883OaksSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub883OaksRollback
		}
	case "hub88_Retro_Gaming":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88RetroGamingBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88RetroGamingSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88RetroGamingRollback
		}
	case "hub88_SlotMill_Games":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88SlotMillGamesBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88SlotMillGamesSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88SlotMillGamesRollback
		}
	case "hub88_Booming_Games":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88BoomingGamesBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88BoomingGamesSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88BoomingGamesRollback
		}
	case "hub88_BetsyGames":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88BetsyGamesBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88BetsyGamesSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88BetsyGamesRollback
		}
	case "hub88_Mancala_Gaming":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88MancalaGamingBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88MancalaGamingSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88MancalaGamingRollback
		}
	case "hub88_Habanero":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88HabaneroBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88HabaneroSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88HabaneroRollback
		}
	case "hub88_Play_n_Go":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88PlaynGoBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88PlaynGoSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88PlaynGoRollback
		}
	case "hub88_OneTouch_Generic":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88OneTouchGenericBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88OneTouchGenericSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88OneTouchGenericRollback
		}
	case "hub88_OneTouch":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88OneTouchBet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88OneTouchSettle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88OneTouchRollback
		}
	case "hub88_Live88":
		if t == K_HUB88CHANGETYPEBET {
			goldType = utils.BalanceCReasonHub88Live88Bet
		} else if t == K_HUB88CHANGETYPESETTLE {
			goldType = utils.BalanceCReasonHub88Live88Settle
		} else if t == K_HUB88CHANGETYPEROLLBACK {
			goldType = utils.BalanceCReasonHub88Live88Rollback
		}
		/*
			case "hub88_7Mojos":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub887MojosBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub887MojosSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub887MojosRollback
				}
			case "hub88_7Mojos_Live":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub887MojosLiveBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub887MojosLiveSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub887MojosLiveRollback
				}
			case "hub88_AirDice":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88AirDiceBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88AirDiceSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88AirDiceRollback
				}
			case "hub88_Alchemy_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88AlchemyGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88AlchemyGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88AlchemyGamingRollback
				}
			case "hub88_All41":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88All41Bet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88All41Settle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88All41Rollback
				}
			case "hub88_Apparat_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88ApparatGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88ApparatGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88ApparatGamingRollback
				}
			case "hub88_Asia_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88AsiaGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88AsiaGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88AsiaGamingRollback
				}
			case "hub88_Atomic_Slot_Lab":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88AtomicSlotLabBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88AtomicSlotLabSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88AtomicSlotLabRollback
				}
			case "hub88_AvatarUX":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88AvatarUXBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88AvatarUXSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88AvatarUXRollback
				}
			case "hub88_BGaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BGamingRollback
				}
			case "hub88_Barbarabang":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BarbarabangBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BarbarabangSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BarbarabangRollback
				}
			case "hub88_BetGames.TV":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BetGamesTVBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BetGamesTVSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BetGamesTVRollback
				}
			case "hub88_Betsoft":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BetsoftBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BetsoftSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BetsoftRollback
				} else {
					goldType = 0
				}
			case "hub88_Blue_Guru":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BlueGuruBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BlueGuruSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BlueGuruRollback
				}
			case "hub88_Blueprint_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BlueprintGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BlueprintGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BlueprintGamingRollback
				}
			case "hub88_Buck_Stakes_Entertainment":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88BuckStakesEntertainmentBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88BuckStakesEntertainmentSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88BuckStakesEntertainmentRollback
				}
			case "hub88_Caleta_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88CaletaGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88CaletaGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88CaletaGamingRollback
				}
			case "hub88_CandleBets":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88CandleBetsBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88CandleBetsSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88CandleBetsRollback
				}
			case "hub88_Elbet":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88ElbetBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88ElbetSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88ElbetRollback
				}
			case "hub88_Evolution_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88EvolutionGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88EvolutionGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88EvolutionGamingRollback
				}
			case "hub88_Evoplay_Entertainment":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88EvoplayEntertainmentBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88EvoplayEntertainmentSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88EvoplayEntertainmentRollback
				}
			case "hub88_Ezugi":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88EzugiBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88EzugiSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88EzugiRollback
				}
			case "hub88_FBastards":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88FBastardsBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88FBastardsSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88FBastardsRollback
				}
			case "hub88_Fantasma":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88FantasmaBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88FantasmaSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88FantasmaRollback
				}
			case "hub88_Foxium":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88FoxiumBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88FoxiumSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88FoxiumRollback
				}
			case "hub88_Fugaso":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88FugasoBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88FugasoSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88FugasoRollback
				}
			case "hub88_Gameburger":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GameburgerBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GameburgerSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GameburgerRollback
				}
			case "hub88_Gametech":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GametechBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GametechSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GametechRollback
				}
			case "hub88_Gamomat":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GamomatBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GamomatSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GamomatRollback
				}
			case "hub88_Gamzix":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GamzixBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GamzixSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GamzixRollback
				}
			case "hub88_Genii":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GeniiBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GeniiSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GeniiRollback
				}
			case "hub88_Golden_Hero":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GoldenHeroBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GoldenHeroSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GoldenHeroRollback
				}
			case "hub88_Golden_Race":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GoldenRaceBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GoldenRaceSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GoldenRaceRollback
				}
			case "hub88_Golden_Rock_Studios":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88GoldenRockStudiosBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88GoldenRockStudiosSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88GoldenRockStudiosRollback
				}
			case "hub88_Hacksaw_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88HacksawGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88HacksawGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88HacksawGamingRollback
				}
			case "hub88_JFTW":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88JFTWBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88JFTWSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88JFTWRollback
				}
			case "hub88_JVL":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88JVLBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88JVLSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88JVLRollback
				}
			case "hub88_Kalamba":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88KalambaBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88KalambaSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88KalambaRollback
				}
			case "hub88_Kalamba_Games":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88KalambaGamesBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88KalambaGamesSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88KalambaGamesRollback
				}
			case "hub88_Live_Solutions":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88LiveSolutionsBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88LiveSolutionsSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88LiveSolutionsRollback
				}
			case "hub88_Lotto_Instant_Win":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88LottoInstantWinBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88LottoInstantWinSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88LottoInstantWinRollback
				}
			case "hub88_MG_Live":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88MGLiveBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88MGLiveSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88MGLiveRollback
				}
			case "hub88_MG_Slots":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88MGSlotsBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88MGSlotsSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88MGSlotsRollback
				}
			case "hub88_Mascot_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88MascotGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88MascotGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88MascotGamingRollback
				}
			case "hub88_Microgaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88MicrogamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88MicrogamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88MicrogamingRollback
				}
			case "h8_nvl": // "hub88_Neon_Valley":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88NeonValleyBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88NeonValleySettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88NeonValleyRollback
				}
			case "hub88_NetEnt":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88NetEntBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88NetEntSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88NetEntRollback
				}
			case "hub88_NetGaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88NetGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88NetGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88NetGamingRollback
				}
			case "hub88_Nolimit_City":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88NolimitCityBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88NolimitCitySettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88NolimitCityRollback
				}
			case "hub88_Northern_Lights_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88NorthernLightsGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88NorthernLightsGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88NorthernLightsGamingRollback
				}
			case "hub88_Old_Skool":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88OldSkoolBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88OldSkoolSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88OldSkoolRollback
				}
			case "hub88_OneGame":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88OneGameBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88OneGameSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88OneGameRollback
				}
			case "hub88_OneTouch_Table_game":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88OneTouchTablegameBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88OneTouchTablegameSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88OneTouchTablegameRollback
				}
			case "hub88_Onlyplay":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88OnlyplayBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88OnlyplaySettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88OnlyplayRollback
				}
			case "hub88_Oryx_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88OryxGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88OryxGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88OryxGamingRollback
				}
			case "hub88_PGSoft":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88PGSoftBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88PGSoftSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88PGSoftRollback
				}
			case "hub88_Peter_&_Sons":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88PeterSonsBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88PeterSonsSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88PeterSonsRollback
				}
			case "hub88_Playson":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88PlaysonBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88PlaysonSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88PlaysonRollback
				}
			case "hub88_Pragmatic_Play":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88PragmaticPlayBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88PragmaticPlaySettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88PragmaticPlayRollback
				}
			case "hub88_Pulse_8":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88Pulse8Bet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88Pulse8Settle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88Pulse8Rollback
				}
			case "hub88_Push_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88PushGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88PushGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88PushGamingRollback
				}
			case "hub88_Rabcat":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RabcatBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RabcatSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RabcatRollback
				}
			case "hub88_Red_Rake_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RedRakeGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RedRakeGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RedRakeGamingRollback
				}
			case "hub88_Red_Tiger_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RedTigerGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RedTigerGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RedTigerGamingRollback
				}
			case "hub88_Relax_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RelaxGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RelaxGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RelaxGamingRollback
				}
			case "hub88_Revolver_Gaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RevolverGamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RevolverGamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RevolverGamingRollback
				}
			case "hub88_RubyPlay":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88RubyPlayBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88RubyPlaySettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88RubyPlayRollback
				}
			case "hub88_SkyWind":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SkyWindBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SkyWindSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SkyWindRollback
				}
			case "hub88_Slingshot":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SlingshotBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SlingshotSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SlingshotRollback
				}
			case "hub88_Snowborn_Studios":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SnowbornStudiosBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SnowbornStudiosSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SnowbornStudiosRollback
				}
			case "hub88_Spadegaming":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SpadegamingBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SpadegamingSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SpadegamingRollback
				}
			case "hub88_SpinPlay_Games":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SpinPlayGamesBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SpinPlayGamesSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SpinPlayGamesRollback
				}
			case "hub88_Spinomenal":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SpinomenalBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SpinomenalSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SpinomenalRollback
				}
			case "hub88_Spribe":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88SpribeBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88SpribeSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88SpribeRollback
				}
			case "hub88_Stormcraft":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88StormcraftBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88StormcraftSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88StormcraftRollback
				}
			case "hub88_Tangente":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88TangenteBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88TangenteSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88TangenteRollback
				}
			case "hub88_Thunderkick":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88ThunderkickBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88ThunderkickSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88ThunderkickRollback
				}
			case "hub88_TopSpin_Games":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88TopSpinGamesBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88TopSpinGamesSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88TopSpinGamesRollback
				}
			case "hub88_Triple_Edge":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88TripleEdgeBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88TripleEdgeSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88TripleEdgeRollback
				}
			case "hub88_TrueLab":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88TrueLabBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88TrueLabSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88TrueLabRollback
				}
			case "hub88_Turbogames":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88TurbogamesBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88TurbogamesSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88TurbogamesRollback
				}
			case "hub88_Wazdan":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88WazdanBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88WazdanSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88WazdanRollback
				}
			case "hub88_WinFast":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88WinFastBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88WinFastSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88WinFastRollback
				}
			case "hub88_Yolted":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88YoltedBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88YoltedSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88YoltedRollback
				}
			case "hub88_Zillion":
				if t == K_HUB88CHANGETYPEBET {
					goldType = utils.BalanceCReasonHub88ZillionBet
				} else if t == K_HUB88CHANGETYPESETTLE {
					goldType = utils.BalanceCReasonHub88ZillionSettle
				} else if t == K_HUB88CHANGETYPEROLLBACK {
					goldType = utils.BalanceCReasonHub88ZillionRollback
				}
		*/
	}
	return
}
