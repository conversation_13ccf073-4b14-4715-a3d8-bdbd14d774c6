// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgent(db *gorm.DB, opts ...gen.DOOption) xAgent {
	_xAgent := xAgent{}

	_xAgent.xAgentDo.UseDB(db, opts...)
	_xAgent.xAgentDo.UseModel(&model.XAgent{})

	tableName := _xAgent.xAgentDo.TableName()
	_xAgent.ALL = field.NewAsterisk(tableName)
	_xAgent.ID = field.NewInt32(tableName, "Id")
	_xAgent.UserID = field.NewInt32(tableName, "UserId")
	_xAgent.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgent.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgent.Address = field.NewString(tableName, "Address")
	_xAgent.IsTopAgent = field.NewInt32(tableName, "IsTopAgent")
	_xAgent.IsNeedActive = field.NewInt32(tableName, "IsNeedActive")
	_xAgent.ChildCount = field.NewInt32(tableName, "ChildCount")
	_xAgent.DictChildCount = field.NewInt32(tableName, "DictChildCount")
	_xAgent.TotalLiuSuiTrx = field.NewFloat64(tableName, "TotalLiuSuiTrx")
	_xAgent.TotalLiuSuiUsdt = field.NewFloat64(tableName, "TotalLiuSuiUsdt")
	_xAgent.TotalBetTrx = field.NewFloat64(tableName, "TotalBetTrx")
	_xAgent.TotalBetUsdt = field.NewFloat64(tableName, "TotalBetUsdt")
	_xAgent.TotalRewardTrx = field.NewFloat64(tableName, "TotalRewardTrx")
	_xAgent.TotalRewardUsdt = field.NewFloat64(tableName, "TotalRewardUsdt")
	_xAgent.SelfLiuSuiTrx = field.NewFloat64(tableName, "SelfLiuSuiTrx")
	_xAgent.SelfLiuSuiUsdt = field.NewFloat64(tableName, "SelfLiuSuiUsdt")
	_xAgent.SelfBetTrx = field.NewFloat64(tableName, "SelfBetTrx")
	_xAgent.SelfBetUsdt = field.NewFloat64(tableName, "SelfBetUsdt")
	_xAgent.SelfRewardTrx = field.NewFloat64(tableName, "SelfRewardTrx")
	_xAgent.SelfRewardUsdt = field.NewFloat64(tableName, "SelfRewardUsdt")
	_xAgent.DictLiuSuiTrx = field.NewFloat64(tableName, "DictLiuSuiTrx")
	_xAgent.DictLiuSuiUsdt = field.NewFloat64(tableName, "DictLiuSuiUsdt")
	_xAgent.DictBetTrx = field.NewFloat64(tableName, "DictBetTrx")
	_xAgent.DictBetUsdt = field.NewFloat64(tableName, "DictBetUsdt")
	_xAgent.DictRewardTrx = field.NewFloat64(tableName, "DictRewardTrx")
	_xAgent.DictRewardUsdt = field.NewFloat64(tableName, "DictRewardUsdt")
	_xAgent.TotalCommissionTrx = field.NewFloat64(tableName, "TotalCommissionTrx")
	_xAgent.TotalCommissionUsdt = field.NewFloat64(tableName, "TotalCommissionUsdt")
	_xAgent.GetedCommissionTrx = field.NewFloat64(tableName, "GetedCommissionTrx")
	_xAgent.GetedCommissionUsdt = field.NewFloat64(tableName, "GetedCommissionUsdt")
	_xAgent.AuditCommissionTrx = field.NewFloat64(tableName, "AuditCommissionTrx")
	_xAgent.AuditCommissionUsdt = field.NewFloat64(tableName, "AuditCommissionUsdt")
	_xAgent.AgentLevel = field.NewInt32(tableName, "AgentLevel")
	_xAgent.FineCommissionTrx = field.NewFloat64(tableName, "FineCommissionTrx")
	_xAgent.FineCommissionUsdt = field.NewFloat64(tableName, "FineCommissionUsdt")
	_xAgent.CanGetCommissionTrx = field.NewFloat64(tableName, "CanGetCommissionTrx")
	_xAgent.CanGetCommissionUsdt = field.NewFloat64(tableName, "CanGetCommissionUsdt")
	_xAgent.NewLiuSuiTrx = field.NewFloat64(tableName, "NewLiuSuiTrx")
	_xAgent.NewDictLiuSuiTrx = field.NewFloat64(tableName, "NewDictLiuSuiTrx")
	_xAgent.NewLiuSuiHaXiRouletteTrx = field.NewFloat64(tableName, "NewLiuSuiHaXiRouletteTrx")
	_xAgent.NewDictLiuSuiHaXiRouletteTrx = field.NewFloat64(tableName, "NewDictLiuSuiHaXiRouletteTrx")
	_xAgent.NewLiuSui = field.NewFloat64(tableName, "NewLiuSui")
	_xAgent.NewLiuSuiDict = field.NewFloat64(tableName, "NewLiuSuiDict")
	_xAgent.NewLiuSuiHaXi = field.NewFloat64(tableName, "NewLiuSuiHaXi")
	_xAgent.NewLiuSuiHaXiDict = field.NewFloat64(tableName, "NewLiuSuiHaXiDict")
	_xAgent.NewLiuSuiHaXiRoulette = field.NewFloat64(tableName, "NewLiuSuiHaXiRoulette")
	_xAgent.NewLiuSuiHaXiRouletteDict = field.NewFloat64(tableName, "NewLiuSuiHaXiRouletteDict")
	_xAgent.NewLiuSuiLottery = field.NewFloat64(tableName, "NewLiuSuiLottery")
	_xAgent.NewLiuSuiLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLotteryDict")
	_xAgent.NewLiuSuiLowLottery = field.NewFloat64(tableName, "NewLiuSuiLowLottery")
	_xAgent.NewLiuSuiLowLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLowLotteryDict")
	_xAgent.NewLiuSuiLiuHeLottery = field.NewFloat64(tableName, "NewLiuSuiLiuHeLottery")
	_xAgent.NewLiuSuiLiuHeLotteryDict = field.NewFloat64(tableName, "NewLiuSuiLiuHeLotteryDict")
	_xAgent.NewLiuSuiQiPai = field.NewFloat64(tableName, "NewLiuSuiQiPai")
	_xAgent.NewLiuSuiQiPaiDict = field.NewFloat64(tableName, "NewLiuSuiQiPaiDict")
	_xAgent.NewLiuSuiDianZhi = field.NewFloat64(tableName, "NewLiuSuiDianZhi")
	_xAgent.NewLiuSuiDianZhiDict = field.NewFloat64(tableName, "NewLiuSuiDianZhiDict")
	_xAgent.NewLiuSuiXiaoYouXi = field.NewFloat64(tableName, "NewLiuSuiXiaoYouXi")
	_xAgent.NewLiuSuiXiaoYouXiDict = field.NewFloat64(tableName, "NewLiuSuiXiaoYouXiDict")
	_xAgent.NewLiuSuiCryptoMarket = field.NewFloat64(tableName, "NewLiuSuiCryptoMarket")
	_xAgent.NewLiuSuiCryptoMarketDict = field.NewFloat64(tableName, "NewLiuSuiCryptoMarketDict")
	_xAgent.NewLiuSuiLive = field.NewFloat64(tableName, "NewLiuSuiLive")
	_xAgent.NewLiuSuiLiveDict = field.NewFloat64(tableName, "NewLiuSuiLiveDict")
	_xAgent.NewLiuSuiSport = field.NewFloat64(tableName, "NewLiuSuiSport")
	_xAgent.NewLiuSuiSportDict = field.NewFloat64(tableName, "NewLiuSuiSportDict")
	_xAgent.NewLiuSuiTexas = field.NewFloat64(tableName, "NewLiuSuiTexas")
	_xAgent.NewLiuSuiTexasDict = field.NewFloat64(tableName, "NewLiuSuiTexasDict")
	_xAgent.NewCommissionTrx = field.NewFloat64(tableName, "NewCommissionTrx")
	_xAgent.NewCommission = field.NewFloat64(tableName, "NewCommission")
	_xAgent.AgentType = field.NewInt32(tableName, "AgentType")
	_xAgent.TotalCommissionTrxT1 = field.NewFloat64(tableName, "TotalCommissionTrx_t1")
	_xAgent.FineCommissionTrxT1 = field.NewFloat64(tableName, "FineCommissionTrx_t1")
	_xAgent.AvailableCommissionTrxT1 = field.NewFloat64(tableName, "AvailableCommissionTrx_t1")
	_xAgent.BackCommissionTrxT1 = field.NewFloat64(tableName, "BackCommissionTrx_t1")
	_xAgent.GetedCommissionTrxT1 = field.NewFloat64(tableName, "GetedCommissionTrx_t1")
	_xAgent.TotalCommissionUsdtT1 = field.NewFloat64(tableName, "TotalCommissionUsdt_t1")
	_xAgent.FineCommissionUsdtT1 = field.NewFloat64(tableName, "FineCommissionUsdt_t1")
	_xAgent.AvailableCommissionUsdtT1 = field.NewFloat64(tableName, "AvailableCommissionUsdt_t1")
	_xAgent.BackCommissionUsdtT1 = field.NewFloat64(tableName, "BackCommissionUsdt_t1")
	_xAgent.GetedCommissionUsdtT1 = field.NewFloat64(tableName, "GetedCommissionUsdt_t1")

	_xAgent.fillFieldMap()

	return _xAgent
}

type xAgent struct {
	xAgentDo xAgentDo

	ALL                          field.Asterisk
	ID                           field.Int32
	UserID                       field.Int32 // 代理id
	SellerID                     field.Int32
	ChannelID                    field.Int32
	Address                      field.String
	IsTopAgent                   field.Int32   // 是否是顶级代理 1是 2不是
	IsNeedActive                 field.Int32   // 提款是否需要激活钱包 1激活 2不需要激活
	ChildCount                   field.Int32   // 团队总人数
	DictChildCount               field.Int32   // 直属总人数
	TotalLiuSuiTrx               field.Float64 // 总流水trx
	TotalLiuSuiUsdt              field.Float64 // 总流水usdt
	TotalBetTrx                  field.Float64 // 总下注trx
	TotalBetUsdt                 field.Float64 // 总下注usdt
	TotalRewardTrx               field.Float64 // 总返奖trx
	TotalRewardUsdt              field.Float64 // 总返奖usdt
	SelfLiuSuiTrx                field.Float64 // 自身流水trx
	SelfLiuSuiUsdt               field.Float64 // 自身流水usdt
	SelfBetTrx                   field.Float64 // 自身下注trx
	SelfBetUsdt                  field.Float64 // 自身下注usdt
	SelfRewardTrx                field.Float64 // 自身返奖trx
	SelfRewardUsdt               field.Float64 // 自身返奖usdt
	DictLiuSuiTrx                field.Float64 // 直属下级总流水trx
	DictLiuSuiUsdt               field.Float64 // 直属下级总流水usdt
	DictBetTrx                   field.Float64 // 直属下级总下注trx
	DictBetUsdt                  field.Float64 // 直属下级总下注usdt
	DictRewardTrx                field.Float64 // 直属下级总返奖trx
	DictRewardUsdt               field.Float64 // 直属下级总返奖usdt
	TotalCommissionTrx           field.Float64 // 历史总佣金
	TotalCommissionUsdt          field.Float64 // 历史总佣金
	GetedCommissionTrx           field.Float64 // 已领取佣金
	GetedCommissionUsdt          field.Float64 // 已领取佣金
	AuditCommissionTrx           field.Float64 // 待审核佣金
	AuditCommissionUsdt          field.Float64 // 待审核佣金
	AgentLevel                   field.Int32   // 代理层级
	FineCommissionTrx            field.Float64 // 审核拒绝,罚没佣金
	FineCommissionUsdt           field.Float64 // 审核拒绝,罚没佣金
	CanGetCommissionTrx          field.Float64 // 可领取佣金trx
	CanGetCommissionUsdt         field.Float64 // 可领取佣金Usdt
	NewLiuSuiTrx                 field.Float64 // 新版代理trx业绩,包含直属的业绩
	NewDictLiuSuiTrx             field.Float64 // 新版代理trx直属业绩
	NewLiuSuiHaXiRouletteTrx     field.Float64
	NewDictLiuSuiHaXiRouletteTrx field.Float64
	NewLiuSui                    field.Float64 // 新版代理业绩,包含直属业绩
	NewLiuSuiDict                field.Float64 // 新版代理直属业绩
	NewLiuSuiHaXi                field.Float64 // 新版代理哈希业绩,包含直属
	NewLiuSuiHaXiDict            field.Float64 // 新版代理哈希直属业绩
	NewLiuSuiHaXiRoulette        field.Float64
	NewLiuSuiHaXiRouletteDict    field.Float64
	NewLiuSuiLottery             field.Float64
	NewLiuSuiLotteryDict         field.Float64
	NewLiuSuiLowLottery          field.Float64
	NewLiuSuiLowLotteryDict      field.Float64
	NewLiuSuiLiuHeLottery        field.Float64
	NewLiuSuiLiuHeLotteryDict    field.Float64
	NewLiuSuiQiPai               field.Float64
	NewLiuSuiQiPaiDict           field.Float64
	NewLiuSuiDianZhi             field.Float64
	NewLiuSuiDianZhiDict         field.Float64
	NewLiuSuiXiaoYouXi           field.Float64
	NewLiuSuiXiaoYouXiDict       field.Float64
	NewLiuSuiCryptoMarket        field.Float64
	NewLiuSuiCryptoMarketDict    field.Float64
	NewLiuSuiLive                field.Float64
	NewLiuSuiLiveDict            field.Float64
	NewLiuSuiSport               field.Float64
	NewLiuSuiSportDict           field.Float64
	NewLiuSuiTexas               field.Float64
	NewLiuSuiTexasDict           field.Float64
	NewCommissionTrx             field.Float64 // 历史总佣金trx
	NewCommission                field.Float64 // 历史总佣金
	AgentType                    field.Int32   // 代理类型 1无,2无限代,3三级代
	TotalCommissionTrxT1         field.Float64 // 独立代理—历史总佣金
	FineCommissionTrxT1          field.Float64 // 独立代理-审核拒绝,罚没佣金
	AvailableCommissionTrxT1     field.Float64 // 独立代理-可领取佣金trx
	BackCommissionTrxT1          field.Float64 // 独立代理-退回佣金
	GetedCommissionTrxT1         field.Float64 // 独立代理-已领取佣金
	TotalCommissionUsdtT1        field.Float64
	FineCommissionUsdtT1         field.Float64
	AvailableCommissionUsdtT1    field.Float64
	BackCommissionUsdtT1         field.Float64
	GetedCommissionUsdtT1        field.Float64

	fieldMap map[string]field.Expr
}

func (x xAgent) Table(newTableName string) *xAgent {
	x.xAgentDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgent) As(alias string) *xAgent {
	x.xAgentDo.DO = *(x.xAgentDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgent) updateTableName(table string) *xAgent {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Address = field.NewString(table, "Address")
	x.IsTopAgent = field.NewInt32(table, "IsTopAgent")
	x.IsNeedActive = field.NewInt32(table, "IsNeedActive")
	x.ChildCount = field.NewInt32(table, "ChildCount")
	x.DictChildCount = field.NewInt32(table, "DictChildCount")
	x.TotalLiuSuiTrx = field.NewFloat64(table, "TotalLiuSuiTrx")
	x.TotalLiuSuiUsdt = field.NewFloat64(table, "TotalLiuSuiUsdt")
	x.TotalBetTrx = field.NewFloat64(table, "TotalBetTrx")
	x.TotalBetUsdt = field.NewFloat64(table, "TotalBetUsdt")
	x.TotalRewardTrx = field.NewFloat64(table, "TotalRewardTrx")
	x.TotalRewardUsdt = field.NewFloat64(table, "TotalRewardUsdt")
	x.SelfLiuSuiTrx = field.NewFloat64(table, "SelfLiuSuiTrx")
	x.SelfLiuSuiUsdt = field.NewFloat64(table, "SelfLiuSuiUsdt")
	x.SelfBetTrx = field.NewFloat64(table, "SelfBetTrx")
	x.SelfBetUsdt = field.NewFloat64(table, "SelfBetUsdt")
	x.SelfRewardTrx = field.NewFloat64(table, "SelfRewardTrx")
	x.SelfRewardUsdt = field.NewFloat64(table, "SelfRewardUsdt")
	x.DictLiuSuiTrx = field.NewFloat64(table, "DictLiuSuiTrx")
	x.DictLiuSuiUsdt = field.NewFloat64(table, "DictLiuSuiUsdt")
	x.DictBetTrx = field.NewFloat64(table, "DictBetTrx")
	x.DictBetUsdt = field.NewFloat64(table, "DictBetUsdt")
	x.DictRewardTrx = field.NewFloat64(table, "DictRewardTrx")
	x.DictRewardUsdt = field.NewFloat64(table, "DictRewardUsdt")
	x.TotalCommissionTrx = field.NewFloat64(table, "TotalCommissionTrx")
	x.TotalCommissionUsdt = field.NewFloat64(table, "TotalCommissionUsdt")
	x.GetedCommissionTrx = field.NewFloat64(table, "GetedCommissionTrx")
	x.GetedCommissionUsdt = field.NewFloat64(table, "GetedCommissionUsdt")
	x.AuditCommissionTrx = field.NewFloat64(table, "AuditCommissionTrx")
	x.AuditCommissionUsdt = field.NewFloat64(table, "AuditCommissionUsdt")
	x.AgentLevel = field.NewInt32(table, "AgentLevel")
	x.FineCommissionTrx = field.NewFloat64(table, "FineCommissionTrx")
	x.FineCommissionUsdt = field.NewFloat64(table, "FineCommissionUsdt")
	x.CanGetCommissionTrx = field.NewFloat64(table, "CanGetCommissionTrx")
	x.CanGetCommissionUsdt = field.NewFloat64(table, "CanGetCommissionUsdt")
	x.NewLiuSuiTrx = field.NewFloat64(table, "NewLiuSuiTrx")
	x.NewDictLiuSuiTrx = field.NewFloat64(table, "NewDictLiuSuiTrx")
	x.NewLiuSuiHaXiRouletteTrx = field.NewFloat64(table, "NewLiuSuiHaXiRouletteTrx")
	x.NewDictLiuSuiHaXiRouletteTrx = field.NewFloat64(table, "NewDictLiuSuiHaXiRouletteTrx")
	x.NewLiuSui = field.NewFloat64(table, "NewLiuSui")
	x.NewLiuSuiDict = field.NewFloat64(table, "NewLiuSuiDict")
	x.NewLiuSuiHaXi = field.NewFloat64(table, "NewLiuSuiHaXi")
	x.NewLiuSuiHaXiDict = field.NewFloat64(table, "NewLiuSuiHaXiDict")
	x.NewLiuSuiHaXiRoulette = field.NewFloat64(table, "NewLiuSuiHaXiRoulette")
	x.NewLiuSuiHaXiRouletteDict = field.NewFloat64(table, "NewLiuSuiHaXiRouletteDict")
	x.NewLiuSuiLottery = field.NewFloat64(table, "NewLiuSuiLottery")
	x.NewLiuSuiLotteryDict = field.NewFloat64(table, "NewLiuSuiLotteryDict")
	x.NewLiuSuiLowLottery = field.NewFloat64(table, "NewLiuSuiLowLottery")
	x.NewLiuSuiLowLotteryDict = field.NewFloat64(table, "NewLiuSuiLowLotteryDict")
	x.NewLiuSuiLiuHeLottery = field.NewFloat64(table, "NewLiuSuiLiuHeLottery")
	x.NewLiuSuiLiuHeLotteryDict = field.NewFloat64(table, "NewLiuSuiLiuHeLotteryDict")
	x.NewLiuSuiQiPai = field.NewFloat64(table, "NewLiuSuiQiPai")
	x.NewLiuSuiQiPaiDict = field.NewFloat64(table, "NewLiuSuiQiPaiDict")
	x.NewLiuSuiDianZhi = field.NewFloat64(table, "NewLiuSuiDianZhi")
	x.NewLiuSuiDianZhiDict = field.NewFloat64(table, "NewLiuSuiDianZhiDict")
	x.NewLiuSuiXiaoYouXi = field.NewFloat64(table, "NewLiuSuiXiaoYouXi")
	x.NewLiuSuiXiaoYouXiDict = field.NewFloat64(table, "NewLiuSuiXiaoYouXiDict")
	x.NewLiuSuiCryptoMarket = field.NewFloat64(table, "NewLiuSuiCryptoMarket")
	x.NewLiuSuiCryptoMarketDict = field.NewFloat64(table, "NewLiuSuiCryptoMarketDict")
	x.NewLiuSuiLive = field.NewFloat64(table, "NewLiuSuiLive")
	x.NewLiuSuiLiveDict = field.NewFloat64(table, "NewLiuSuiLiveDict")
	x.NewLiuSuiSport = field.NewFloat64(table, "NewLiuSuiSport")
	x.NewLiuSuiSportDict = field.NewFloat64(table, "NewLiuSuiSportDict")
	x.NewLiuSuiTexas = field.NewFloat64(table, "NewLiuSuiTexas")
	x.NewLiuSuiTexasDict = field.NewFloat64(table, "NewLiuSuiTexasDict")
	x.NewCommissionTrx = field.NewFloat64(table, "NewCommissionTrx")
	x.NewCommission = field.NewFloat64(table, "NewCommission")
	x.AgentType = field.NewInt32(table, "AgentType")
	x.TotalCommissionTrxT1 = field.NewFloat64(table, "TotalCommissionTrx_t1")
	x.FineCommissionTrxT1 = field.NewFloat64(table, "FineCommissionTrx_t1")
	x.AvailableCommissionTrxT1 = field.NewFloat64(table, "AvailableCommissionTrx_t1")
	x.BackCommissionTrxT1 = field.NewFloat64(table, "BackCommissionTrx_t1")
	x.GetedCommissionTrxT1 = field.NewFloat64(table, "GetedCommissionTrx_t1")
	x.TotalCommissionUsdtT1 = field.NewFloat64(table, "TotalCommissionUsdt_t1")
	x.FineCommissionUsdtT1 = field.NewFloat64(table, "FineCommissionUsdt_t1")
	x.AvailableCommissionUsdtT1 = field.NewFloat64(table, "AvailableCommissionUsdt_t1")
	x.BackCommissionUsdtT1 = field.NewFloat64(table, "BackCommissionUsdt_t1")
	x.GetedCommissionUsdtT1 = field.NewFloat64(table, "GetedCommissionUsdt_t1")

	x.fillFieldMap()

	return x
}

func (x *xAgent) WithContext(ctx context.Context) *xAgentDo { return x.xAgentDo.WithContext(ctx) }

func (x xAgent) TableName() string { return x.xAgentDo.TableName() }

func (x xAgent) Alias() string { return x.xAgentDo.Alias() }

func (x xAgent) Columns(cols ...field.Expr) gen.Columns { return x.xAgentDo.Columns(cols...) }

func (x *xAgent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgent) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 81)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Address"] = x.Address
	x.fieldMap["IsTopAgent"] = x.IsTopAgent
	x.fieldMap["IsNeedActive"] = x.IsNeedActive
	x.fieldMap["ChildCount"] = x.ChildCount
	x.fieldMap["DictChildCount"] = x.DictChildCount
	x.fieldMap["TotalLiuSuiTrx"] = x.TotalLiuSuiTrx
	x.fieldMap["TotalLiuSuiUsdt"] = x.TotalLiuSuiUsdt
	x.fieldMap["TotalBetTrx"] = x.TotalBetTrx
	x.fieldMap["TotalBetUsdt"] = x.TotalBetUsdt
	x.fieldMap["TotalRewardTrx"] = x.TotalRewardTrx
	x.fieldMap["TotalRewardUsdt"] = x.TotalRewardUsdt
	x.fieldMap["SelfLiuSuiTrx"] = x.SelfLiuSuiTrx
	x.fieldMap["SelfLiuSuiUsdt"] = x.SelfLiuSuiUsdt
	x.fieldMap["SelfBetTrx"] = x.SelfBetTrx
	x.fieldMap["SelfBetUsdt"] = x.SelfBetUsdt
	x.fieldMap["SelfRewardTrx"] = x.SelfRewardTrx
	x.fieldMap["SelfRewardUsdt"] = x.SelfRewardUsdt
	x.fieldMap["DictLiuSuiTrx"] = x.DictLiuSuiTrx
	x.fieldMap["DictLiuSuiUsdt"] = x.DictLiuSuiUsdt
	x.fieldMap["DictBetTrx"] = x.DictBetTrx
	x.fieldMap["DictBetUsdt"] = x.DictBetUsdt
	x.fieldMap["DictRewardTrx"] = x.DictRewardTrx
	x.fieldMap["DictRewardUsdt"] = x.DictRewardUsdt
	x.fieldMap["TotalCommissionTrx"] = x.TotalCommissionTrx
	x.fieldMap["TotalCommissionUsdt"] = x.TotalCommissionUsdt
	x.fieldMap["GetedCommissionTrx"] = x.GetedCommissionTrx
	x.fieldMap["GetedCommissionUsdt"] = x.GetedCommissionUsdt
	x.fieldMap["AuditCommissionTrx"] = x.AuditCommissionTrx
	x.fieldMap["AuditCommissionUsdt"] = x.AuditCommissionUsdt
	x.fieldMap["AgentLevel"] = x.AgentLevel
	x.fieldMap["FineCommissionTrx"] = x.FineCommissionTrx
	x.fieldMap["FineCommissionUsdt"] = x.FineCommissionUsdt
	x.fieldMap["CanGetCommissionTrx"] = x.CanGetCommissionTrx
	x.fieldMap["CanGetCommissionUsdt"] = x.CanGetCommissionUsdt
	x.fieldMap["NewLiuSuiTrx"] = x.NewLiuSuiTrx
	x.fieldMap["NewDictLiuSuiTrx"] = x.NewDictLiuSuiTrx
	x.fieldMap["NewLiuSuiHaXiRouletteTrx"] = x.NewLiuSuiHaXiRouletteTrx
	x.fieldMap["NewDictLiuSuiHaXiRouletteTrx"] = x.NewDictLiuSuiHaXiRouletteTrx
	x.fieldMap["NewLiuSui"] = x.NewLiuSui
	x.fieldMap["NewLiuSuiDict"] = x.NewLiuSuiDict
	x.fieldMap["NewLiuSuiHaXi"] = x.NewLiuSuiHaXi
	x.fieldMap["NewLiuSuiHaXiDict"] = x.NewLiuSuiHaXiDict
	x.fieldMap["NewLiuSuiHaXiRoulette"] = x.NewLiuSuiHaXiRoulette
	x.fieldMap["NewLiuSuiHaXiRouletteDict"] = x.NewLiuSuiHaXiRouletteDict
	x.fieldMap["NewLiuSuiLottery"] = x.NewLiuSuiLottery
	x.fieldMap["NewLiuSuiLotteryDict"] = x.NewLiuSuiLotteryDict
	x.fieldMap["NewLiuSuiLowLottery"] = x.NewLiuSuiLowLottery
	x.fieldMap["NewLiuSuiLowLotteryDict"] = x.NewLiuSuiLowLotteryDict
	x.fieldMap["NewLiuSuiLiuHeLottery"] = x.NewLiuSuiLiuHeLottery
	x.fieldMap["NewLiuSuiLiuHeLotteryDict"] = x.NewLiuSuiLiuHeLotteryDict
	x.fieldMap["NewLiuSuiQiPai"] = x.NewLiuSuiQiPai
	x.fieldMap["NewLiuSuiQiPaiDict"] = x.NewLiuSuiQiPaiDict
	x.fieldMap["NewLiuSuiDianZhi"] = x.NewLiuSuiDianZhi
	x.fieldMap["NewLiuSuiDianZhiDict"] = x.NewLiuSuiDianZhiDict
	x.fieldMap["NewLiuSuiXiaoYouXi"] = x.NewLiuSuiXiaoYouXi
	x.fieldMap["NewLiuSuiXiaoYouXiDict"] = x.NewLiuSuiXiaoYouXiDict
	x.fieldMap["NewLiuSuiCryptoMarket"] = x.NewLiuSuiCryptoMarket
	x.fieldMap["NewLiuSuiCryptoMarketDict"] = x.NewLiuSuiCryptoMarketDict
	x.fieldMap["NewLiuSuiLive"] = x.NewLiuSuiLive
	x.fieldMap["NewLiuSuiLiveDict"] = x.NewLiuSuiLiveDict
	x.fieldMap["NewLiuSuiSport"] = x.NewLiuSuiSport
	x.fieldMap["NewLiuSuiSportDict"] = x.NewLiuSuiSportDict
	x.fieldMap["NewLiuSuiTexas"] = x.NewLiuSuiTexas
	x.fieldMap["NewLiuSuiTexasDict"] = x.NewLiuSuiTexasDict
	x.fieldMap["NewCommissionTrx"] = x.NewCommissionTrx
	x.fieldMap["NewCommission"] = x.NewCommission
	x.fieldMap["AgentType"] = x.AgentType
	x.fieldMap["TotalCommissionTrx_t1"] = x.TotalCommissionTrxT1
	x.fieldMap["FineCommissionTrx_t1"] = x.FineCommissionTrxT1
	x.fieldMap["AvailableCommissionTrx_t1"] = x.AvailableCommissionTrxT1
	x.fieldMap["BackCommissionTrx_t1"] = x.BackCommissionTrxT1
	x.fieldMap["GetedCommissionTrx_t1"] = x.GetedCommissionTrxT1
	x.fieldMap["TotalCommissionUsdt_t1"] = x.TotalCommissionUsdtT1
	x.fieldMap["FineCommissionUsdt_t1"] = x.FineCommissionUsdtT1
	x.fieldMap["AvailableCommissionUsdt_t1"] = x.AvailableCommissionUsdtT1
	x.fieldMap["BackCommissionUsdt_t1"] = x.BackCommissionUsdtT1
	x.fieldMap["GetedCommissionUsdt_t1"] = x.GetedCommissionUsdtT1
}

func (x xAgent) clone(db *gorm.DB) xAgent {
	x.xAgentDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgent) replaceDB(db *gorm.DB) xAgent {
	x.xAgentDo.ReplaceDB(db)
	return x
}

type xAgentDo struct{ gen.DO }

func (x xAgentDo) Debug() *xAgentDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentDo) WithContext(ctx context.Context) *xAgentDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentDo) ReadDB() *xAgentDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentDo) WriteDB() *xAgentDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentDo) Session(config *gorm.Session) *xAgentDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentDo) Clauses(conds ...clause.Expression) *xAgentDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentDo) Returning(value interface{}, columns ...string) *xAgentDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentDo) Not(conds ...gen.Condition) *xAgentDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentDo) Or(conds ...gen.Condition) *xAgentDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentDo) Select(conds ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentDo) Where(conds ...gen.Condition) *xAgentDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentDo) Order(conds ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentDo) Distinct(cols ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentDo) Omit(cols ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentDo) Join(table schema.Tabler, on ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentDo) Group(cols ...field.Expr) *xAgentDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentDo) Having(conds ...gen.Condition) *xAgentDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentDo) Limit(limit int) *xAgentDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentDo) Offset(offset int) *xAgentDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentDo) Unscoped() *xAgentDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentDo) Create(values ...*model.XAgent) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentDo) CreateInBatches(values []*model.XAgent, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentDo) Save(values ...*model.XAgent) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentDo) First() (*model.XAgent, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgent), nil
	}
}

func (x xAgentDo) Take() (*model.XAgent, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgent), nil
	}
}

func (x xAgentDo) Last() (*model.XAgent, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgent), nil
	}
}

func (x xAgentDo) Find() ([]*model.XAgent, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgent), err
}

func (x xAgentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgent, err error) {
	buf := make([]*model.XAgent, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentDo) FindInBatches(result *[]*model.XAgent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentDo) Attrs(attrs ...field.AssignExpr) *xAgentDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentDo) Assign(attrs ...field.AssignExpr) *xAgentDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentDo) Joins(fields ...field.RelationField) *xAgentDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentDo) Preload(fields ...field.RelationField) *xAgentDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentDo) FirstOrInit() (*model.XAgent, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgent), nil
	}
}

func (x xAgentDo) FirstOrCreate() (*model.XAgent, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgent), nil
	}
}

func (x xAgentDo) FindByPage(offset int, limit int) (result []*model.XAgent, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentDo) Delete(models ...*model.XAgent) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentDo) withDO(do gen.Dao) *xAgentDo {
	x.DO = *do.(*gen.DO)
	return x
}
