// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserOnlineProcess(db *gorm.DB, opts ...gen.DOOption) xUserOnlineProcess {
	_xUserOnlineProcess := xUserOnlineProcess{}

	_xUserOnlineProcess.xUserOnlineProcessDo.UseDB(db, opts...)
	_xUserOnlineProcess.xUserOnlineProcessDo.UseModel(&model.XUserOnlineProcess{})

	tableName := _xUserOnlineProcess.xUserOnlineProcessDo.TableName()
	_xUserOnlineProcess.ALL = field.NewAsterisk(tableName)
	_xUserOnlineProcess.ID = field.NewInt64(tableName, "Id")
	_xUserOnlineProcess.UserID = field.NewInt32(tableName, "UserId")
	_xUserOnlineProcess.Time = field.NewTime(tableName, "Time")
	_xUserOnlineProcess.State = field.NewInt32(tableName, "State")
	_xUserOnlineProcess.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserOnlineProcess.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserOnlineProcess.fillFieldMap()

	return _xUserOnlineProcess
}

type xUserOnlineProcess struct {
	xUserOnlineProcessDo xUserOnlineProcessDo

	ALL        field.Asterisk
	ID         field.Int64
	UserID     field.Int32
	Time       field.Time
	State      field.Int32
	CreateTime field.Time
	UpdateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xUserOnlineProcess) Table(newTableName string) *xUserOnlineProcess {
	x.xUserOnlineProcessDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserOnlineProcess) As(alias string) *xUserOnlineProcess {
	x.xUserOnlineProcessDo.DO = *(x.xUserOnlineProcessDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserOnlineProcess) updateTableName(table string) *xUserOnlineProcess {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.Time = field.NewTime(table, "Time")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserOnlineProcess) WithContext(ctx context.Context) *xUserOnlineProcessDo {
	return x.xUserOnlineProcessDo.WithContext(ctx)
}

func (x xUserOnlineProcess) TableName() string { return x.xUserOnlineProcessDo.TableName() }

func (x xUserOnlineProcess) Alias() string { return x.xUserOnlineProcessDo.Alias() }

func (x xUserOnlineProcess) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserOnlineProcessDo.Columns(cols...)
}

func (x *xUserOnlineProcess) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserOnlineProcess) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 6)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Time"] = x.Time
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserOnlineProcess) clone(db *gorm.DB) xUserOnlineProcess {
	x.xUserOnlineProcessDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserOnlineProcess) replaceDB(db *gorm.DB) xUserOnlineProcess {
	x.xUserOnlineProcessDo.ReplaceDB(db)
	return x
}

type xUserOnlineProcessDo struct{ gen.DO }

func (x xUserOnlineProcessDo) Debug() *xUserOnlineProcessDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserOnlineProcessDo) WithContext(ctx context.Context) *xUserOnlineProcessDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserOnlineProcessDo) ReadDB() *xUserOnlineProcessDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserOnlineProcessDo) WriteDB() *xUserOnlineProcessDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserOnlineProcessDo) Session(config *gorm.Session) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserOnlineProcessDo) Clauses(conds ...clause.Expression) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserOnlineProcessDo) Returning(value interface{}, columns ...string) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserOnlineProcessDo) Not(conds ...gen.Condition) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserOnlineProcessDo) Or(conds ...gen.Condition) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserOnlineProcessDo) Select(conds ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserOnlineProcessDo) Where(conds ...gen.Condition) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserOnlineProcessDo) Order(conds ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserOnlineProcessDo) Distinct(cols ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserOnlineProcessDo) Omit(cols ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserOnlineProcessDo) Join(table schema.Tabler, on ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserOnlineProcessDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserOnlineProcessDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserOnlineProcessDo) Group(cols ...field.Expr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserOnlineProcessDo) Having(conds ...gen.Condition) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserOnlineProcessDo) Limit(limit int) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserOnlineProcessDo) Offset(offset int) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserOnlineProcessDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserOnlineProcessDo) Unscoped() *xUserOnlineProcessDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserOnlineProcessDo) Create(values ...*model.XUserOnlineProcess) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserOnlineProcessDo) CreateInBatches(values []*model.XUserOnlineProcess, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserOnlineProcessDo) Save(values ...*model.XUserOnlineProcess) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserOnlineProcessDo) First() (*model.XUserOnlineProcess, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnlineProcess), nil
	}
}

func (x xUserOnlineProcessDo) Take() (*model.XUserOnlineProcess, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnlineProcess), nil
	}
}

func (x xUserOnlineProcessDo) Last() (*model.XUserOnlineProcess, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnlineProcess), nil
	}
}

func (x xUserOnlineProcessDo) Find() ([]*model.XUserOnlineProcess, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserOnlineProcess), err
}

func (x xUserOnlineProcessDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserOnlineProcess, err error) {
	buf := make([]*model.XUserOnlineProcess, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserOnlineProcessDo) FindInBatches(result *[]*model.XUserOnlineProcess, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserOnlineProcessDo) Attrs(attrs ...field.AssignExpr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserOnlineProcessDo) Assign(attrs ...field.AssignExpr) *xUserOnlineProcessDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserOnlineProcessDo) Joins(fields ...field.RelationField) *xUserOnlineProcessDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserOnlineProcessDo) Preload(fields ...field.RelationField) *xUserOnlineProcessDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserOnlineProcessDo) FirstOrInit() (*model.XUserOnlineProcess, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnlineProcess), nil
	}
}

func (x xUserOnlineProcessDo) FirstOrCreate() (*model.XUserOnlineProcess, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserOnlineProcess), nil
	}
}

func (x xUserOnlineProcessDo) FindByPage(offset int, limit int) (result []*model.XUserOnlineProcess, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserOnlineProcessDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserOnlineProcessDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserOnlineProcessDo) Delete(models ...*model.XUserOnlineProcess) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserOnlineProcessDo) withDO(do gen.Dao) *xUserOnlineProcessDo {
	x.DO = *do.(*gen.DO)
	return x
}
