package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/zhms/xgo/xgo"
)

const (
	ApiXwGateway      = "http://openapi.moth1709.vip/api"
	ApiXwBalanceQuery = "/balance"
	ApiXwTrans        = "pay/trans"
	ApiXwCreate       = "pay/v3/create"
	ApiXwTransStatus  = "pay/trans/status"
	ApiXwOrderStatus  = "pay/order/status"
)

func XwVerify(basic *BasicPay, sign, signStr string) int64 {

	code := int64(0)

	md5Str := fmt.Sprintf("%s&key=%s", signStr, basic.Md5Key)
	md5Sign := HexMd5(md5Str)
	if md5Sign == strings.ToLower(sign) {
		code = 1
	}
	return code
}

func XwSign(basic *BasicPay, rawStr string) string {
	signStr := rawStr + basic.Merchant + basic.Md5Key
	signStr = HexMd5(signStr)
	signStr = strings.ToLower(signStr)

	return signStr
}

func XwGetBaseHeader(merchant, sign string) map[string]string {
	jsHeader := make(map[string]string)
	jsHeader["Content-Type"] = "application/json; charset=UTF-8"
	jsHeader["Authorization"] = merchant
	jsHeader["Sign"] = sign

	return jsHeader
}

//{
//	"Timestamp":    long     13位长度的时间戳
//	"Secret":       string   Timestamp 进行 MD5 加密后的值
//}

// 账户余额
func XwQueryBalance(basic *BasicPay) (string, error) {

	balance := ""
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))

	rawStr, err := json.Marshal(params)
	if err != nil {
		return balance, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwBalanceQuery
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return balance, errors.New(RequestError)
	}

	//注意: Timestamp 过期时间为5秒,5秒前的 Timestamp 无法进行余额查询
	//{
	//	"Data ": Object     业务数据,返回的是余额
	//	"Code":  Integer    业务编码:当为0是成功,其他为失败
	//	"CodeStr": String   业务编码描述
	//	"Msg":   String     业务消息
	//}
	//{"Data":"1826.0000","Code":0,"CodeStr":"OK","Msg":"操作成功!!"}

	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return balance, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return balance, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return balance, errors.New(errMsg)
	}

	if val, ok := res["Data"]; ok {
		balance = fmt.Sprintf("%v", val)
	}

	return balance, nil
}

//MerchantOrderNo String 是 订单号
//
//MerchantOrderAmount Decimal 是 代付金额
//
//MerchantNotifyUrl String 否
//回调通知地址,
//
//如需通过订单状态查询接口来确认订单状
//态该参数可不传
//
//BankCardHolder String 是 银行卡持卡人
//
//BankCardNo String 是 银行卡号
//
//BankName String 是 银行名称,参考代付银行列表

// 代付
func XwTransOrder(basic *BasicPay, amount float64, orderNo,
	notifyUrl, holder, cardNo, bankName string) (int, error) {
	iRet := 1
	params := make(map[string]interface{})

	params["MerchantOrderNo"] = orderNo
	params["MerchantOrderAmount"] = amount
	params["MerchantNotifyUrl"] = notifyUrl
	params["BankCardHolder"] = holder
	params["BankCardNo"] = cardNo
	params["BankName"] = bankName

	rawStr, err := json.Marshal(params)
	if err != nil {
		return iRet, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)
	jsHeader["TransType"] = "MYBCT"

	reqUrl := basic.GatewayUrl + ApiXwTrans
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return iRet, errors.New(RequestError)
	}

	//{"Data":null,"Code":0,"CodeStr":"OK","Msg":"操作成功!!"}
	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return iRet, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return iRet, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return iRet, errors.New(errMsg)
	}

	return 0, nil
}

//MerchantNotifyUrl String 是 回调通知地址
//
//MerchantOrderNo String 是 订单号
//
//MerchantPayAmount Decimal 是 订单金额
//
//MerchantPayType String 是
//代收方式,具体查看文档
//下说明
//
//BankCode String
//
//只有当代收方式为
//
//MYBCT 时
//该字段为必传项
//
//用户付款银行的银行编
//码,代收付款银行编码
//
//Name String
//
//只有当代收方式为
//
//MYDuitNow 时
//该字段为必传项
//
//存款用户的真实姓名

// 代收
func XwCreateOrder(basic *BasicPay, amount float64,
	notifyUrl, orderNo, payType, bankCode, name string) (map[string]interface{}, error) {

	var res map[string]interface{}
	params := make(map[string]interface{})

	params["MerchantOrderNo"] = orderNo
	params["MerchantPayAmount"] = amount
	params["MerchantNotifyUrl"] = notifyUrl
	params["MerchantPayType"] = payType
	params["BankCode"] = bankCode
	params["Name"] = name

	rawStr, err := json.Marshal(params)
	if err != nil {
		return res, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)
	jsHeader["TransType"] = "MYBCT"

	reqUrl := basic.GatewayUrl + ApiXwCreate
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return res, errors.New(RequestError)
	}

	//{"Data":
	//{"PcPayUrl":"http://www.xxx.vip/pay/D9118774D6F67F4","MobilePayUrl":"http://w
	//ww.xxx.vip/pay/D9118774D6F67F4","RequiredAmount":100.0,"PlatformOrderNo":"D91
	//18774D6F67F4"},"Code":0,"CodeStr":"OK","Msg":"操作成功!!"}

	err = json.Unmarshal(body, &res)
	if err != nil {
		return res, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return res, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return res, errors.New(errMsg)
	}

	return res, nil

}

//Timestamp Long 是 13位长度的时间戳
//
//Secret String 是 Timestamp 进行 MD5 加密后的值
//
//MerchantOrderNo String 是 订单号
//注意: Timestamp 过期时间为10秒,10秒前的 Timestamp 无法进行订单状态查询

// 代付订单状态查询
func XwQueryTransOrder(basic *BasicPay, orderNo string) (interface{}, error) {
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))
	params["MerchantOrderNo"] = orderNo

	rawStr, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwTransStatus
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return nil, errors.New(RequestError)
	}

	//Data:MerchantOrderNo String 商户订单号
	//
	//Data:Status Integer 订单状态(-2:下单失败0:待完成,1:已完成,2:取消)
	//
	//Data:CreateTime String 订单创建时间
	//
	//Data:TransTime String
	//订单代付完成时间,注意:当订单实际代付完成时返
	//回的数据存在该字段
	//
	//Code Integer 业务编码:当为0是成功,其他为失败
	//
	//CodeStr String 业务编码描述

	//{"Data":
	//{"MerchantOrderNo":"202306262201595813761","Status":2,"CreateTime":"2023-01-
	//26 12:02:00","TransTime":"2023-01-26
	//12:03:00"},"Code":0,"CodeStr":"OK","Msg":"操作成功!!"}

	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return nil, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return nil, errors.New(errMsg)
	}

	var data interface{}

	if val, ok := res["Data"]; ok {
		data = val
	} else {
		return nil, errors.New(ResultParamError)
	}

	return data, nil
}

//Timestamp Long 是 13位长度的时间戳
//
//Secret String 是 Timestamp 进行 MD5 加密后的值
//
//MerchantOrderNo String 是 订单号
//请求参数
//
//注意: Timestamp 过期时间为10秒,10秒前的 Timestamp 无法进行订单状态查询
//该接口为每10秒钟进行请求一次,否则将会返回 http 503

// 代收订单状态查询
func XwQueryCreateOrder(basic *BasicPay, orderNo string) (interface{}, error) {
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))
	params["MerchantOrderNo"] = orderNo

	rawStr, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwOrderStatus
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return nil, errors.New(RequestError)
	}

	//Data:Status Integer
	//
	//订单状态说明:
	//( -2:下单失败,-1:创建中,0:待支付,1:支付完成,2:订
	//单超时,3:订单异常)
	//
	//Data:MerchantOrderNo String 商户订单号
	//
	//Data:CreateTime String 订单创建时间
	//
	//Data:PayDate String
	//支付到账时间,注意:当订单实际到账完成时返回的
	//数据存在该字段
	//
	//Data:PayAmount Decimal
	//实际支付到账金额,注意:当订单实际到账完成时返
	//回的数据存在该字段
	//
	//Data:OrderAmount Decimal 订单金额
	//
	//Data:MemberName String 会员付款真实姓名
	//
	//Code Integer 业务编码:当为0是成功,其他为失败
	//
	//CodeStr String 业务编码描述
	//
	//Msg String 业务消息

	//{"Data":{"Status":1,"MerchantOrderNo":"1686297823540595","CreateTime":"2023-
	//06-09 16:03:43","PayDate":"2023-06-09
	//16:11:53","OrderAmount":100.0000,"PayAmount":100.0000,"MemberName":"马
	//超"},"Code":0,"CodeStr":"OK","Msg":"操作成功!!"}}

	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return nil, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return nil, errors.New(errMsg)
	}

	var data interface{}

	if val, ok := res["Data"]; ok {
		data = val
	} else {
		return nil, errors.New(ResultParamError)
	}

	return data, nil
}

func (c *PayController) xinwang_create_order(ctx *abugo.AbuHttpContent, jcfg map[string]interface{}, rate float64,
	userdata *xgo.XMap, SpecialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap) {

	symbol := strings.ToLower(reqdata.Symbol)
	paytype := 2
	if symbol != "brl" {
		paytype = 7
	}
	// 新增订单
	errcode := 0
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var realAmount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                             // 运营商ID为26时，汇率设为0
		realAmount = float64(reqdata.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getWithdrawRate(reqdata.Symbol) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		realAmount = float64(reqdata.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	OrderId, _ := server.Db().Table("x_recharge").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      paytype,
		"Amount":       reqdata.Amount,
		"RealAmount":   realAmount,
		"TransferRate": rate,
		"State":        3,
		"CSGroup":      userdata.Int("CSGroup"),
		"CSId":         userdata.Int("CSId"),
		"SpecialAgent": SpecialAgent,
		"TopAgentId":   userdata.Int("TopAgentId"),
	})

	basic := new(BasicPay)

	//TODO: key的来源??

	amount := float64(reqdata.Amount)
	orderNo := fmt.Sprintf("%d", OrderId)
	notifyUrl := jcfg["cburl"].(string) + "/xinwangcreate"
	payType := reqdata.PayType
	bankCode := reqdata.BankCode
	name := reqdata.RealName

	res, err := XwCreateOrder(basic, amount, notifyUrl, orderNo, payType, bankCode, name)
	if err != nil || nil == res {
		return
	}

	data := make(map[string]interface{}, 0)
	data["PayId"] = reqdata.MethodId
	data["Brand"] = paymethod.String("Brand")
	data["Name"] = paymethod.String("Name")
	bytes, _ := json.Marshal(data)

	server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
		"PayData": string(bytes),
		"ThirdId": data["PlatformOrderNo"],
	})
	ctx.RespOK(xgo.H{
		"payurl": data["MobilePayUrl"],
	})

	return
}
