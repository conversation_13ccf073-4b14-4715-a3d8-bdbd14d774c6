// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentIndependenceFenchengHistory(db *gorm.DB, opts ...gen.DOOption) xAgentIndependenceFenchengHistory {
	_xAgentIndependenceFenchengHistory := xAgentIndependenceFenchengHistory{}

	_xAgentIndependenceFenchengHistory.xAgentIndependenceFenchengHistoryDo.UseDB(db, opts...)
	_xAgentIndependenceFenchengHistory.xAgentIndependenceFenchengHistoryDo.UseModel(&model.XAgentIndependenceFenchengHistory{})

	tableName := _xAgentIndependenceFenchengHistory.xAgentIndependenceFenchengHistoryDo.TableName()
	_xAgentIndependenceFenchengHistory.ALL = field.NewAsterisk(tableName)
	_xAgentIndependenceFenchengHistory.ID = field.NewInt64(tableName, "Id")
	_xAgentIndependenceFenchengHistory.UserID = field.NewInt32(tableName, "UserId")
	_xAgentIndependenceFenchengHistory.AgentID = field.NewInt32(tableName, "AgentId")
	_xAgentIndependenceFenchengHistory.Reason = field.NewInt32(tableName, "Reason")
	_xAgentIndependenceFenchengHistory.ChangeBefore = field.NewString(tableName, "ChangeBefore")
	_xAgentIndependenceFenchengHistory.ChangeAfter = field.NewString(tableName, "ChangeAfter")
	_xAgentIndependenceFenchengHistory.Systime = field.NewTime(tableName, "Systime")

	_xAgentIndependenceFenchengHistory.fillFieldMap()

	return _xAgentIndependenceFenchengHistory
}

type xAgentIndependenceFenchengHistory struct {
	xAgentIndependenceFenchengHistoryDo xAgentIndependenceFenchengHistoryDo

	ALL          field.Asterisk
	ID           field.Int64
	UserID       field.Int32
	AgentID      field.Int32
	Reason       field.Int32 // 1注册2上级设置3顶级方案调整
	ChangeBefore field.String
	ChangeAfter  field.String
	Systime      field.Time

	fieldMap map[string]field.Expr
}

func (x xAgentIndependenceFenchengHistory) Table(newTableName string) *xAgentIndependenceFenchengHistory {
	x.xAgentIndependenceFenchengHistoryDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentIndependenceFenchengHistory) As(alias string) *xAgentIndependenceFenchengHistory {
	x.xAgentIndependenceFenchengHistoryDo.DO = *(x.xAgentIndependenceFenchengHistoryDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentIndependenceFenchengHistory) updateTableName(table string) *xAgentIndependenceFenchengHistory {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.Reason = field.NewInt32(table, "Reason")
	x.ChangeBefore = field.NewString(table, "ChangeBefore")
	x.ChangeAfter = field.NewString(table, "ChangeAfter")
	x.Systime = field.NewTime(table, "Systime")

	x.fillFieldMap()

	return x
}

func (x *xAgentIndependenceFenchengHistory) WithContext(ctx context.Context) *xAgentIndependenceFenchengHistoryDo {
	return x.xAgentIndependenceFenchengHistoryDo.WithContext(ctx)
}

func (x xAgentIndependenceFenchengHistory) TableName() string {
	return x.xAgentIndependenceFenchengHistoryDo.TableName()
}

func (x xAgentIndependenceFenchengHistory) Alias() string {
	return x.xAgentIndependenceFenchengHistoryDo.Alias()
}

func (x xAgentIndependenceFenchengHistory) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentIndependenceFenchengHistoryDo.Columns(cols...)
}

func (x *xAgentIndependenceFenchengHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentIndependenceFenchengHistory) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 7)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["Reason"] = x.Reason
	x.fieldMap["ChangeBefore"] = x.ChangeBefore
	x.fieldMap["ChangeAfter"] = x.ChangeAfter
	x.fieldMap["Systime"] = x.Systime
}

func (x xAgentIndependenceFenchengHistory) clone(db *gorm.DB) xAgentIndependenceFenchengHistory {
	x.xAgentIndependenceFenchengHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentIndependenceFenchengHistory) replaceDB(db *gorm.DB) xAgentIndependenceFenchengHistory {
	x.xAgentIndependenceFenchengHistoryDo.ReplaceDB(db)
	return x
}

type xAgentIndependenceFenchengHistoryDo struct{ gen.DO }

func (x xAgentIndependenceFenchengHistoryDo) Debug() *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentIndependenceFenchengHistoryDo) WithContext(ctx context.Context) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentIndependenceFenchengHistoryDo) ReadDB() *xAgentIndependenceFenchengHistoryDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentIndependenceFenchengHistoryDo) WriteDB() *xAgentIndependenceFenchengHistoryDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentIndependenceFenchengHistoryDo) Session(config *gorm.Session) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentIndependenceFenchengHistoryDo) Clauses(conds ...clause.Expression) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Returning(value interface{}, columns ...string) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentIndependenceFenchengHistoryDo) Not(conds ...gen.Condition) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Or(conds ...gen.Condition) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Select(conds ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Where(conds ...gen.Condition) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Order(conds ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Distinct(cols ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentIndependenceFenchengHistoryDo) Omit(cols ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentIndependenceFenchengHistoryDo) Join(table schema.Tabler, on ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentIndependenceFenchengHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentIndependenceFenchengHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentIndependenceFenchengHistoryDo) Group(cols ...field.Expr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentIndependenceFenchengHistoryDo) Having(conds ...gen.Condition) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentIndependenceFenchengHistoryDo) Limit(limit int) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentIndependenceFenchengHistoryDo) Offset(offset int) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentIndependenceFenchengHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentIndependenceFenchengHistoryDo) Unscoped() *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentIndependenceFenchengHistoryDo) Create(values ...*model.XAgentIndependenceFenchengHistory) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentIndependenceFenchengHistoryDo) CreateInBatches(values []*model.XAgentIndependenceFenchengHistory, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentIndependenceFenchengHistoryDo) Save(values ...*model.XAgentIndependenceFenchengHistory) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentIndependenceFenchengHistoryDo) First() (*model.XAgentIndependenceFenchengHistory, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependenceFenchengHistory), nil
	}
}

func (x xAgentIndependenceFenchengHistoryDo) Take() (*model.XAgentIndependenceFenchengHistory, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependenceFenchengHistory), nil
	}
}

func (x xAgentIndependenceFenchengHistoryDo) Last() (*model.XAgentIndependenceFenchengHistory, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependenceFenchengHistory), nil
	}
}

func (x xAgentIndependenceFenchengHistoryDo) Find() ([]*model.XAgentIndependenceFenchengHistory, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentIndependenceFenchengHistory), err
}

func (x xAgentIndependenceFenchengHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentIndependenceFenchengHistory, err error) {
	buf := make([]*model.XAgentIndependenceFenchengHistory, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentIndependenceFenchengHistoryDo) FindInBatches(result *[]*model.XAgentIndependenceFenchengHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentIndependenceFenchengHistoryDo) Attrs(attrs ...field.AssignExpr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentIndependenceFenchengHistoryDo) Assign(attrs ...field.AssignExpr) *xAgentIndependenceFenchengHistoryDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentIndependenceFenchengHistoryDo) Joins(fields ...field.RelationField) *xAgentIndependenceFenchengHistoryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentIndependenceFenchengHistoryDo) Preload(fields ...field.RelationField) *xAgentIndependenceFenchengHistoryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentIndependenceFenchengHistoryDo) FirstOrInit() (*model.XAgentIndependenceFenchengHistory, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependenceFenchengHistory), nil
	}
}

func (x xAgentIndependenceFenchengHistoryDo) FirstOrCreate() (*model.XAgentIndependenceFenchengHistory, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentIndependenceFenchengHistory), nil
	}
}

func (x xAgentIndependenceFenchengHistoryDo) FindByPage(offset int, limit int) (result []*model.XAgentIndependenceFenchengHistory, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentIndependenceFenchengHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentIndependenceFenchengHistoryDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentIndependenceFenchengHistoryDo) Delete(models ...*model.XAgentIndependenceFenchengHistory) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentIndependenceFenchengHistoryDo) withDO(do gen.Dao) *xAgentIndependenceFenchengHistoryDo {
	x.DO = *do.(*gen.DO)
	return x
}
