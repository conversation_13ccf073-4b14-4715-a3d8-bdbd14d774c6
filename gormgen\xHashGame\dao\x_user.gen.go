// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUser(db *gorm.DB, opts ...gen.DOOption) xUser {
	_xUser := xUser{}

	_xUser.xUserDo.UseDB(db, opts...)
	_xUser.xUserDo.UseModel(&model.XUser{})

	tableName := _xUser.xUserDo.TableName()
	_xUser.ALL = field.NewAsterisk(tableName)
	_xUser.ID = field.NewInt32(tableName, "Id")
	_xUser.UserID = field.NewInt32(tableName, "UserId")
	_xUser.SellerID = field.NewInt32(tableName, "SellerId")
	_xUser.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUser.Account = field.NewString(tableName, "Account")
	_xUser.Password = field.NewString(tableName, "Password")
	_xUser.PasswordV2 = field.NewString(tableName, "PasswordV2")
	_xUser.NickName = field.NewString(tableName, "NickName")
	_xUser.Token = field.NewString(tableName, "Token")
	_xUser.Agents = field.NewString(tableName, "Agents")
	_xUser.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xUser.AgentID = field.NewInt32(tableName, "AgentId")
	_xUser.RegisterIP = field.NewString(tableName, "RegisterIp")
	_xUser.RegisterRegion = field.NewString(tableName, "RegisterRegion")
	_xUser.RegisterTime = field.NewTime(tableName, "RegisterTime")
	_xUser.Address = field.NewString(tableName, "Address")
	_xUser.Email = field.NewString(tableName, "Email")
	_xUser.PhoneNum = field.NewString(tableName, "PhoneNum")
	_xUser.IsTopAgent = field.NewInt32(tableName, "IsTopAgent")
	_xUser.BetTrx = field.NewFloat64(tableName, "BetTrx")
	_xUser.RewardTrx = field.NewFloat64(tableName, "RewardTrx")
	_xUser.LiuSuiTrx = field.NewFloat64(tableName, "LiuSuiTrx")
	_xUser.BetUsdt = field.NewFloat64(tableName, "BetUsdt")
	_xUser.RewardUsdt = field.NewFloat64(tableName, "RewardUsdt")
	_xUser.LiuSuiUsdt = field.NewFloat64(tableName, "LiuSuiUsdt")
	_xUser.IsAgent = field.NewInt32(tableName, "IsAgent")
	_xUser.LoginTime = field.NewTime(tableName, "LoginTime")
	_xUser.LoginIP = field.NewString(tableName, "LoginIp")
	_xUser.State = field.NewInt32(tableName, "State")
	_xUser.FineLiuSuiTrx = field.NewFloat64(tableName, "FineLiuSuiTrx")
	_xUser.FineLiuSuiUsdt = field.NewFloat64(tableName, "FineLiuSuiUsdt")
	_xUser.LastGameInfo = field.NewString(tableName, "LastGameInfo")
	_xUser.FenCheng = field.NewString(tableName, "FenCheng")
	_xUser.AgentCode = field.NewString(tableName, "AgentCode")
	_xUser.TgChatID = field.NewInt64(tableName, "TgChatId")
	_xUser.TgUserName = field.NewString(tableName, "TgUserName")
	_xUser.VerifyState = field.NewInt32(tableName, "VerifyState")
	_xUser.VerifyAmount = field.NewInt32(tableName, "VerifyAmount")
	_xUser.VerifyTime = field.NewTime(tableName, "VerifyTime")
	_xUser.Amount = field.NewFloat64(tableName, "Amount")
	_xUser.BonusAmount = field.NewFloat64(tableName, "BonusAmount")
	_xUser.BankerAmount = field.NewFloat64(tableName, "BankerAmount")
	_xUser.LockedAmount = field.NewFloat64(tableName, "LockedAmount")
	_xUser.LastGameInfo6 = field.NewString(tableName, "LastGameInfo6")
	_xUser.LastGameInfo7 = field.NewString(tableName, "LastGameInfo7")
	_xUser.GameFee = field.NewString(tableName, "GameFee")
	_xUser.VipAmount = field.NewFloat64(tableName, "VipAmount")
	_xUser.WinAudit = field.NewString(tableName, "WinAudit")
	_xUser.MaxBet = field.NewString(tableName, "MaxBet")
	_xUser.RechargeAddressTron = field.NewString(tableName, "RechargeAddressTron")
	_xUser.WalletPassword = field.NewString(tableName, "WalletPassword")
	_xUser.MaxBetTime = field.NewTime(tableName, "MaxBetTime")
	_xUser.JpType = field.NewInt32(tableName, "JpType")
	_xUser.LastGameInfoEx = field.NewString(tableName, "LastGameInfoEx")
	_xUser.WithdrawLiuSui = field.NewFloat64(tableName, "WithdrawLiuSui")
	_xUser.TotalLiuSui = field.NewFloat64(tableName, "TotalLiuSui")
	_xUser.RechargeAddressEth = field.NewString(tableName, "RechargeAddressEth")
	_xUser.HeadID = field.NewString(tableName, "HeadId")
	_xUser.Gender = field.NewString(tableName, "Gender")
	_xUser.DeliveryAddress = field.NewString(tableName, "DeliveryAddress")
	_xUser.Birthday = field.NewTime(tableName, "Birthday")
	_xUser.RealName = field.NewString(tableName, "RealName")
	_xUser.IsTest = field.NewInt32(tableName, "IsTest")
	_xUser.UpdatePasswordTime = field.NewTime(tableName, "UpdatePasswordTime")
	_xUser.UpdateWalletPasswordTime = field.NewTime(tableName, "UpdateWalletPasswordTime")
	_xUser.AuditAmount = field.NewFloat64(tableName, "AuditAmount")
	_xUser.WinJiangPeiMax = field.NewString(tableName, "WinJiangPeiMax")
	_xUser.IsPanda = field.NewInt32(tableName, "IsPanda")
	_xUser.BlackMaker = field.NewString(tableName, "BlackMaker")
	_xUser.TgName = field.NewString(tableName, "TgName")
	_xUser.CSGroup = field.NewString(tableName, "CSGroup")
	_xUser.CSID = field.NewString(tableName, "CSId")
	_xUser.RegGift = field.NewInt32(tableName, "RegGift")
	_xUser.IgnoreWinJiangPei = field.NewInt32(tableName, "IgnoreWinJiangPei")
	_xUser.BetCount = field.NewInt32(tableName, "BetCount")
	_xUser.BetDays = field.NewInt32(tableName, "BetDays")
	_xUser.FirstBetTime = field.NewTime(tableName, "FirstBetTime")
	_xUser.LastBetTime = field.NewTime(tableName, "LastBetTime")
	_xUser.FirstRechargeTime = field.NewTime(tableName, "FirstRechargeTime")
	_xUser.LastRechargeTime = field.NewTime(tableName, "LastRechargeTime")
	_xUser.FirstWithdrawTime = field.NewTime(tableName, "FirstWithdrawTime")
	_xUser.LastWithdrawTime = field.NewTime(tableName, "LastWithdrawTime")
	_xUser.FirstWithdrawFinishTime = field.NewTime(tableName, "FirstWithdrawFinishTime")
	_xUser.LastWithdrawFinishTime = field.NewTime(tableName, "LastWithdrawFinishTime")
	_xUser.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xUser.WithdrawSuccessCount = field.NewInt32(tableName, "WithdrawSuccessCount")
	_xUser.RegURL = field.NewString(tableName, "RegUrl")
	_xUser.Memo = field.NewString(tableName, "Memo")
	_xUser.KeFuTgName = field.NewString(tableName, "KeFuTgName")
	_xUser.Tag = field.NewString(tableName, "Tag")
	_xUser.CaiJingTrx = field.NewFloat64(tableName, "CaiJingTrx")
	_xUser.CaiJingUsdt = field.NewFloat64(tableName, "CaiJingUsdt")
	_xUser.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xUser.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xUser.CSBindTime = field.NewTime(tableName, "CSBindTime")
	_xUser.ThirdID = field.NewString(tableName, "ThirdId")
	_xUser.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xUser.LastAddAmount = field.NewFloat64(tableName, "LastAddAmount")
	_xUser.AuditAmountUsdt = field.NewFloat64(tableName, "AuditAmountUsdt")
	_xUser.AuditAmountTrx = field.NewFloat64(tableName, "AuditAmountTrx")
	_xUser.RegDeviceID = field.NewString(tableName, "RegDeviceId")
	_xUser.LoginDeviceID = field.NewString(tableName, "LoginDeviceId")
	_xUser.RegDeviceType = field.NewString(tableName, "RegDeviceType")
	_xUser.LoginDeviceType = field.NewString(tableName, "LoginDeviceType")
	_xUser.GameLimit = field.NewString(tableName, "GameLimit")
	_xUser.WithwardNeedLiuSui = field.NewInt32(tableName, "WithwardNeedLiuSui")
	_xUser.RegLang = field.NewString(tableName, "RegLang")
	_xUser.LoginLang = field.NewString(tableName, "LoginLang")
	_xUser.AgentType = field.NewInt32(tableName, "AgentType")
	_xUser.AgentConfigID = field.NewInt32(tableName, "AgentConfigId")
	_xUser.AgentNickName = field.NewString(tableName, "AgentNickName")
	_xUser.AgentUseID = field.NewInt32(tableName, "AgentUseId")
	_xUser.CxdID = field.NewString(tableName, "CxdId")
	_xUser.IsoCountry = field.NewString(tableName, "IsoCountry")
	_xUser.AccountType = field.NewInt32(tableName, "AccountType")
	_xUser.TgRobotToken = field.NewString(tableName, "TgRobotToken")
	_xUser.TgRobotGiftStatus = field.NewInt32(tableName, "TgRobotGiftStatus")
	_xUser.TgBindLastSendTime = field.NewTime(tableName, "TgBindLastSendTime")
	_xUser.UsdtGiftStatus = field.NewInt32(tableName, "UsdtGiftStatus")
	_xUser.TrxGiftStatus = field.NewInt32(tableName, "TrxGiftStatus")
	_xUser.FirstSignTime = field.NewTime(tableName, "FirstSignTime")
	_xUser.LastSignTime = field.NewTime(tableName, "LastSignTime")
	_xUser.InviteRewardUsers = field.NewInt32(tableName, "InviteRewardUsers")
	_xUser.IsInviteReward = field.NewInt32(tableName, "IsInviteReward")
	_xUser.IsInResourceDb = field.NewInt32(tableName, "IsInResourceDb")
	_xUser.Kwai = field.NewString(tableName, "Kwai")
	_xUser.Bigo = field.NewString(tableName, "Bigo")
	_xUser.Brand = field.NewInt32(tableName, "Brand")
	_xUser.Fbc = field.NewString(tableName, "Fbc")
	_xUser.AgentShortURL = field.NewString(tableName, "AgentShortUrl")

	_xUser.fillFieldMap()

	return _xUser
}

// xUser 用户表
type xUser struct {
	xUserDo xUserDo

	ALL                      field.Asterisk
	ID                       field.Int32   // id
	UserID                   field.Int32   // 玩家
	SellerID                 field.Int32   // 运营商
	ChannelID                field.Int32   // 渠道商
	Account                  field.String  // 账号
	Password                 field.String  // 登录密码
	PasswordV2               field.String  // 登录密码
	NickName                 field.String  // 昵称
	Token                    field.String  // 登录token
	Agents                   field.String  // json数组,所有上级id,下标0是直属代理,越往后,代理等级越高
	TopAgentID               field.Int32   // 顶级代理id
	AgentID                  field.Int32   // 直属代理
	RegisterIP               field.String  // 注册ip
	RegisterRegion           field.String  // 注册区域
	RegisterTime             field.Time    // 注册时间
	Address                  field.String  // 地址
	Email                    field.String  // email地址
	PhoneNum                 field.String  // 手机号码
	IsTopAgent               field.Int32   // 是否是顶级代理 1是 2不是
	BetTrx                   field.Float64 // Trx下注
	RewardTrx                field.Float64 // trx返奖
	LiuSuiTrx                field.Float64 // trx流水
	BetUsdt                  field.Float64 // usdt下注
	RewardUsdt               field.Float64 // usdt返奖
	LiuSuiUsdt               field.Float64 // usdt流水
	IsAgent                  field.Int32   // 是否是代理 1是,2不是
	LoginTime                field.Time    // 登录时间
	LoginIP                  field.String  // 登录ip
	State                    field.Int32   // 状态 1启用 2禁用
	FineLiuSuiTrx            field.Float64 // 扣除流水trx
	FineLiuSuiUsdt           field.Float64 // 扣除流水Usdt
	LastGameInfo             field.String  // 最后玩的游戏
	FenCheng                 field.String  // 分成比例
	AgentCode                field.String  // 注册邀请码
	TgChatID                 field.Int64   // 电报机器人chatid
	TgUserName               field.String  // 电报机器人用户名
	VerifyState              field.Int32   // 地址是否已验证
	VerifyAmount             field.Int32   // 地址验证转账金额
	VerifyTime               field.Time    // 地址验证时间
	Amount                   field.Float64 // 账户余额usdt
	BonusAmount              field.Float64 // Bonus钱包usdt
	BankerAmount             field.Float64 // 上庄余额
	LockedAmount             field.Float64 // 锁定账户余额usdt
	LastGameInfo6            field.String  // 最后一局快三
	LastGameInfo7            field.String  // 最后一局pk10
	GameFee                  field.String  // 个人游戏费率
	VipAmount                field.Float64 // vip累计金额
	WinAudit                 field.String  // 连赢审核记录
	MaxBet                   field.String  // 单笔最大下注记录
	RechargeAddressTron      field.String  // 充值地址
	WalletPassword           field.String  // 提现密码
	MaxBetTime               field.Time    // 降赔重置时间
	JpType                   field.Int32   // 降赔类型
	LastGameInfoEx           field.String  // 最后玩的游戏
	WithdrawLiuSui           field.Float64 // 提现流水
	TotalLiuSui              field.Float64 // 当前流水
	RechargeAddressEth       field.String  // 充值地址
	HeadID                   field.String  // 头像id
	Gender                   field.String  // 性别
	DeliveryAddress          field.String  // 收货地址
	Birthday                 field.Time    // 生日
	RealName                 field.String  // 真实姓名
	IsTest                   field.Int32   // 是否是测试账号,1是,2不是
	UpdatePasswordTime       field.Time    // 最后一次改密码时间
	UpdateWalletPasswordTime field.Time    // 最后一次改资金密码时间
	AuditAmount              field.Float64 // 审核金额,返奖超过此金额需要审核
	WinJiangPeiMax           field.String  // 降赔最大金额
	IsPanda                  field.Int32   // 是否是量化用户 1是,2不是
	BlackMaker               field.String  // 区块黑名单
	TgName                   field.String  // 注册tg
	CSGroup                  field.String  // 客服团队
	CSID                     field.String  // 客服工号
	RegGift                  field.Int32   // 【废弃】体验金状态 1无体验金,2可以领取体验金,3可以领取trx体验金,4已经领取trx体验金,5可以领取u体验金,6已经领取u体验金
	IgnoreWinJiangPei        field.Int32   // 忽略盈利降赔,1是,2否
	BetCount                 field.Int32   // 投注次数
	BetDays                  field.Int32   // 下注天数
	FirstBetTime             field.Time    // 首次投注时间
	LastBetTime              field.Time    // 最后投注时间
	FirstRechargeTime        field.Time    // 首次充值时间
	LastRechargeTime         field.Time    // 最后充值时间
	FirstWithdrawTime        field.Time    // 首次提款申请时间
	LastWithdrawTime         field.Time    // 最后提款申请时间
	FirstWithdrawFinishTime  field.Time    // 首次提款完成时间
	LastWithdrawFinishTime   field.Time    // 最后提款完成时间
	WithdrawCount            field.Int32   // 提款申请数
	WithdrawSuccessCount     field.Int32   // 提款成功数
	RegURL                   field.String  // 注册域名
	Memo                     field.String  // 备注
	KeFuTgName               field.String  // 客服tg
	Tag                      field.String  // 标签
	CaiJingTrx               field.Float64 // 累计彩金trx
	CaiJingUsdt              field.Float64 // 累计彩金usdt
	RechargeAmount           field.Float64 // 累计充值
	WithdrawAmount           field.Float64 // 累计提款
	CSBindTime               field.Time    // 客服绑定时间
	ThirdID                  field.String  // 三方id
	SpecialAgent             field.Int32   // 是否是独立代理 1 是,2不是
	LastAddAmount            field.Float64 // 最后一次后台增值金额
	AuditAmountUsdt          field.Float64 // 审核金额,返奖超过此金额需要审核
	AuditAmountTrx           field.Float64 // 审核金额,返奖超过此金额需要审核
	RegDeviceID              field.String  // 注册设备Id
	LoginDeviceID            field.String  // 登录设备Id
	RegDeviceType            field.String  // 注册设备类型  ios android windows
	LoginDeviceType          field.String  // 登录设备类型 ios android windows
	GameLimit                field.String  // 游戏限额
	WithwardNeedLiuSui       field.Int32   // 提现是否需要流水,1是,2否
	RegLang                  field.String
	LoginLang                field.String // 登录语言
	AgentType                field.Int32  // 代理类型 1无代理类型,2无限代,3三代理
	AgentConfigID            field.Int32  // 代理方案id
	AgentNickName            field.String
	AgentUseID               field.Int32 // 佣金方案id
	CxdID                    field.String
	IsoCountry               field.String
	AccountType              field.Int32  // 1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram
	TgRobotToken             field.String // tg机器人token
	TgRobotGiftStatus        field.Int32  // 【废弃】tg机器人账户体验金状态(0无体验金,1可以领取u体验金,2已经领取u体验金,3可以领取trx体验金,4已经领取trx体验金)
	TgBindLastSendTime       field.Time   // tg机器人最后发送绑定指引消息的时间
	UsdtGiftStatus           field.Int32  // USDT体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取 3:可领取 4:已领取)
	TrxGiftStatus            field.Int32  // TRX体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取)
	FirstSignTime            field.Time   // 首次签到时间
	LastSignTime             field.Time   // 最后签到时间
	InviteRewardUsers        field.Int32  // 邀请好友返奖人数
	IsInviteReward           field.Int32  // 是否邀请好友返奖 1已返奖励 2未返奖励
	IsInResourceDb           field.Int32  // 是否在库(0:否 1:是)
	Kwai                     field.String // 快手clickId
	Bigo                     field.String // bigo clickId
	Brand                    field.Int32  // 2 ut
	Fbc                      field.String // fb归因上报
	AgentShortURL            field.String // 代理短连接

	fieldMap map[string]field.Expr
}

func (x xUser) Table(newTableName string) *xUser {
	x.xUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUser) As(alias string) *xUser {
	x.xUserDo.DO = *(x.xUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUser) updateTableName(table string) *xUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.Account = field.NewString(table, "Account")
	x.Password = field.NewString(table, "Password")
	x.PasswordV2 = field.NewString(table, "PasswordV2")
	x.NickName = field.NewString(table, "NickName")
	x.Token = field.NewString(table, "Token")
	x.Agents = field.NewString(table, "Agents")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.RegisterIP = field.NewString(table, "RegisterIp")
	x.RegisterRegion = field.NewString(table, "RegisterRegion")
	x.RegisterTime = field.NewTime(table, "RegisterTime")
	x.Address = field.NewString(table, "Address")
	x.Email = field.NewString(table, "Email")
	x.PhoneNum = field.NewString(table, "PhoneNum")
	x.IsTopAgent = field.NewInt32(table, "IsTopAgent")
	x.BetTrx = field.NewFloat64(table, "BetTrx")
	x.RewardTrx = field.NewFloat64(table, "RewardTrx")
	x.LiuSuiTrx = field.NewFloat64(table, "LiuSuiTrx")
	x.BetUsdt = field.NewFloat64(table, "BetUsdt")
	x.RewardUsdt = field.NewFloat64(table, "RewardUsdt")
	x.LiuSuiUsdt = field.NewFloat64(table, "LiuSuiUsdt")
	x.IsAgent = field.NewInt32(table, "IsAgent")
	x.LoginTime = field.NewTime(table, "LoginTime")
	x.LoginIP = field.NewString(table, "LoginIp")
	x.State = field.NewInt32(table, "State")
	x.FineLiuSuiTrx = field.NewFloat64(table, "FineLiuSuiTrx")
	x.FineLiuSuiUsdt = field.NewFloat64(table, "FineLiuSuiUsdt")
	x.LastGameInfo = field.NewString(table, "LastGameInfo")
	x.FenCheng = field.NewString(table, "FenCheng")
	x.AgentCode = field.NewString(table, "AgentCode")
	x.TgChatID = field.NewInt64(table, "TgChatId")
	x.TgUserName = field.NewString(table, "TgUserName")
	x.VerifyState = field.NewInt32(table, "VerifyState")
	x.VerifyAmount = field.NewInt32(table, "VerifyAmount")
	x.VerifyTime = field.NewTime(table, "VerifyTime")
	x.Amount = field.NewFloat64(table, "Amount")
	x.BonusAmount = field.NewFloat64(table, "BonusAmount")
	x.BankerAmount = field.NewFloat64(table, "BankerAmount")
	x.LockedAmount = field.NewFloat64(table, "LockedAmount")
	x.LastGameInfo6 = field.NewString(table, "LastGameInfo6")
	x.LastGameInfo7 = field.NewString(table, "LastGameInfo7")
	x.GameFee = field.NewString(table, "GameFee")
	x.VipAmount = field.NewFloat64(table, "VipAmount")
	x.WinAudit = field.NewString(table, "WinAudit")
	x.MaxBet = field.NewString(table, "MaxBet")
	x.RechargeAddressTron = field.NewString(table, "RechargeAddressTron")
	x.WalletPassword = field.NewString(table, "WalletPassword")
	x.MaxBetTime = field.NewTime(table, "MaxBetTime")
	x.JpType = field.NewInt32(table, "JpType")
	x.LastGameInfoEx = field.NewString(table, "LastGameInfoEx")
	x.WithdrawLiuSui = field.NewFloat64(table, "WithdrawLiuSui")
	x.TotalLiuSui = field.NewFloat64(table, "TotalLiuSui")
	x.RechargeAddressEth = field.NewString(table, "RechargeAddressEth")
	x.HeadID = field.NewString(table, "HeadId")
	x.Gender = field.NewString(table, "Gender")
	x.DeliveryAddress = field.NewString(table, "DeliveryAddress")
	x.Birthday = field.NewTime(table, "Birthday")
	x.RealName = field.NewString(table, "RealName")
	x.IsTest = field.NewInt32(table, "IsTest")
	x.UpdatePasswordTime = field.NewTime(table, "UpdatePasswordTime")
	x.UpdateWalletPasswordTime = field.NewTime(table, "UpdateWalletPasswordTime")
	x.AuditAmount = field.NewFloat64(table, "AuditAmount")
	x.WinJiangPeiMax = field.NewString(table, "WinJiangPeiMax")
	x.IsPanda = field.NewInt32(table, "IsPanda")
	x.BlackMaker = field.NewString(table, "BlackMaker")
	x.TgName = field.NewString(table, "TgName")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.RegGift = field.NewInt32(table, "RegGift")
	x.IgnoreWinJiangPei = field.NewInt32(table, "IgnoreWinJiangPei")
	x.BetCount = field.NewInt32(table, "BetCount")
	x.BetDays = field.NewInt32(table, "BetDays")
	x.FirstBetTime = field.NewTime(table, "FirstBetTime")
	x.LastBetTime = field.NewTime(table, "LastBetTime")
	x.FirstRechargeTime = field.NewTime(table, "FirstRechargeTime")
	x.LastRechargeTime = field.NewTime(table, "LastRechargeTime")
	x.FirstWithdrawTime = field.NewTime(table, "FirstWithdrawTime")
	x.LastWithdrawTime = field.NewTime(table, "LastWithdrawTime")
	x.FirstWithdrawFinishTime = field.NewTime(table, "FirstWithdrawFinishTime")
	x.LastWithdrawFinishTime = field.NewTime(table, "LastWithdrawFinishTime")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawSuccessCount = field.NewInt32(table, "WithdrawSuccessCount")
	x.RegURL = field.NewString(table, "RegUrl")
	x.Memo = field.NewString(table, "Memo")
	x.KeFuTgName = field.NewString(table, "KeFuTgName")
	x.Tag = field.NewString(table, "Tag")
	x.CaiJingTrx = field.NewFloat64(table, "CaiJingTrx")
	x.CaiJingUsdt = field.NewFloat64(table, "CaiJingUsdt")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.CSBindTime = field.NewTime(table, "CSBindTime")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.LastAddAmount = field.NewFloat64(table, "LastAddAmount")
	x.AuditAmountUsdt = field.NewFloat64(table, "AuditAmountUsdt")
	x.AuditAmountTrx = field.NewFloat64(table, "AuditAmountTrx")
	x.RegDeviceID = field.NewString(table, "RegDeviceId")
	x.LoginDeviceID = field.NewString(table, "LoginDeviceId")
	x.RegDeviceType = field.NewString(table, "RegDeviceType")
	x.LoginDeviceType = field.NewString(table, "LoginDeviceType")
	x.GameLimit = field.NewString(table, "GameLimit")
	x.WithwardNeedLiuSui = field.NewInt32(table, "WithwardNeedLiuSui")
	x.RegLang = field.NewString(table, "RegLang")
	x.LoginLang = field.NewString(table, "LoginLang")
	x.AgentType = field.NewInt32(table, "AgentType")
	x.AgentConfigID = field.NewInt32(table, "AgentConfigId")
	x.AgentNickName = field.NewString(table, "AgentNickName")
	x.AgentUseID = field.NewInt32(table, "AgentUseId")
	x.CxdID = field.NewString(table, "CxdId")
	x.IsoCountry = field.NewString(table, "IsoCountry")
	x.AccountType = field.NewInt32(table, "AccountType")
	x.TgRobotToken = field.NewString(table, "TgRobotToken")
	x.TgRobotGiftStatus = field.NewInt32(table, "TgRobotGiftStatus")
	x.TgBindLastSendTime = field.NewTime(table, "TgBindLastSendTime")
	x.UsdtGiftStatus = field.NewInt32(table, "UsdtGiftStatus")
	x.TrxGiftStatus = field.NewInt32(table, "TrxGiftStatus")
	x.FirstSignTime = field.NewTime(table, "FirstSignTime")
	x.LastSignTime = field.NewTime(table, "LastSignTime")
	x.InviteRewardUsers = field.NewInt32(table, "InviteRewardUsers")
	x.IsInviteReward = field.NewInt32(table, "IsInviteReward")
	x.IsInResourceDb = field.NewInt32(table, "IsInResourceDb")
	x.Kwai = field.NewString(table, "Kwai")
	x.Bigo = field.NewString(table, "Bigo")
	x.Brand = field.NewInt32(table, "Brand")
	x.Fbc = field.NewString(table, "Fbc")
	x.AgentShortURL = field.NewString(table, "AgentShortUrl")

	x.fillFieldMap()

	return x
}

func (x *xUser) WithContext(ctx context.Context) *xUserDo { return x.xUserDo.WithContext(ctx) }

func (x xUser) TableName() string { return x.xUserDo.TableName() }

func (x xUser) Alias() string { return x.xUserDo.Alias() }

func (x xUser) Columns(cols ...field.Expr) gen.Columns { return x.xUserDo.Columns(cols...) }

func (x *xUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 130)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["Account"] = x.Account
	x.fieldMap["Password"] = x.Password
	x.fieldMap["PasswordV2"] = x.PasswordV2
	x.fieldMap["NickName"] = x.NickName
	x.fieldMap["Token"] = x.Token
	x.fieldMap["Agents"] = x.Agents
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["RegisterIp"] = x.RegisterIP
	x.fieldMap["RegisterRegion"] = x.RegisterRegion
	x.fieldMap["RegisterTime"] = x.RegisterTime
	x.fieldMap["Address"] = x.Address
	x.fieldMap["Email"] = x.Email
	x.fieldMap["PhoneNum"] = x.PhoneNum
	x.fieldMap["IsTopAgent"] = x.IsTopAgent
	x.fieldMap["BetTrx"] = x.BetTrx
	x.fieldMap["RewardTrx"] = x.RewardTrx
	x.fieldMap["LiuSuiTrx"] = x.LiuSuiTrx
	x.fieldMap["BetUsdt"] = x.BetUsdt
	x.fieldMap["RewardUsdt"] = x.RewardUsdt
	x.fieldMap["LiuSuiUsdt"] = x.LiuSuiUsdt
	x.fieldMap["IsAgent"] = x.IsAgent
	x.fieldMap["LoginTime"] = x.LoginTime
	x.fieldMap["LoginIp"] = x.LoginIP
	x.fieldMap["State"] = x.State
	x.fieldMap["FineLiuSuiTrx"] = x.FineLiuSuiTrx
	x.fieldMap["FineLiuSuiUsdt"] = x.FineLiuSuiUsdt
	x.fieldMap["LastGameInfo"] = x.LastGameInfo
	x.fieldMap["FenCheng"] = x.FenCheng
	x.fieldMap["AgentCode"] = x.AgentCode
	x.fieldMap["TgChatId"] = x.TgChatID
	x.fieldMap["TgUserName"] = x.TgUserName
	x.fieldMap["VerifyState"] = x.VerifyState
	x.fieldMap["VerifyAmount"] = x.VerifyAmount
	x.fieldMap["VerifyTime"] = x.VerifyTime
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["BonusAmount"] = x.BonusAmount
	x.fieldMap["BankerAmount"] = x.BankerAmount
	x.fieldMap["LockedAmount"] = x.LockedAmount
	x.fieldMap["LastGameInfo6"] = x.LastGameInfo6
	x.fieldMap["LastGameInfo7"] = x.LastGameInfo7
	x.fieldMap["GameFee"] = x.GameFee
	x.fieldMap["VipAmount"] = x.VipAmount
	x.fieldMap["WinAudit"] = x.WinAudit
	x.fieldMap["MaxBet"] = x.MaxBet
	x.fieldMap["RechargeAddressTron"] = x.RechargeAddressTron
	x.fieldMap["WalletPassword"] = x.WalletPassword
	x.fieldMap["MaxBetTime"] = x.MaxBetTime
	x.fieldMap["JpType"] = x.JpType
	x.fieldMap["LastGameInfoEx"] = x.LastGameInfoEx
	x.fieldMap["WithdrawLiuSui"] = x.WithdrawLiuSui
	x.fieldMap["TotalLiuSui"] = x.TotalLiuSui
	x.fieldMap["RechargeAddressEth"] = x.RechargeAddressEth
	x.fieldMap["HeadId"] = x.HeadID
	x.fieldMap["Gender"] = x.Gender
	x.fieldMap["DeliveryAddress"] = x.DeliveryAddress
	x.fieldMap["Birthday"] = x.Birthday
	x.fieldMap["RealName"] = x.RealName
	x.fieldMap["IsTest"] = x.IsTest
	x.fieldMap["UpdatePasswordTime"] = x.UpdatePasswordTime
	x.fieldMap["UpdateWalletPasswordTime"] = x.UpdateWalletPasswordTime
	x.fieldMap["AuditAmount"] = x.AuditAmount
	x.fieldMap["WinJiangPeiMax"] = x.WinJiangPeiMax
	x.fieldMap["IsPanda"] = x.IsPanda
	x.fieldMap["BlackMaker"] = x.BlackMaker
	x.fieldMap["TgName"] = x.TgName
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["RegGift"] = x.RegGift
	x.fieldMap["IgnoreWinJiangPei"] = x.IgnoreWinJiangPei
	x.fieldMap["BetCount"] = x.BetCount
	x.fieldMap["BetDays"] = x.BetDays
	x.fieldMap["FirstBetTime"] = x.FirstBetTime
	x.fieldMap["LastBetTime"] = x.LastBetTime
	x.fieldMap["FirstRechargeTime"] = x.FirstRechargeTime
	x.fieldMap["LastRechargeTime"] = x.LastRechargeTime
	x.fieldMap["FirstWithdrawTime"] = x.FirstWithdrawTime
	x.fieldMap["LastWithdrawTime"] = x.LastWithdrawTime
	x.fieldMap["FirstWithdrawFinishTime"] = x.FirstWithdrawFinishTime
	x.fieldMap["LastWithdrawFinishTime"] = x.LastWithdrawFinishTime
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawSuccessCount"] = x.WithdrawSuccessCount
	x.fieldMap["RegUrl"] = x.RegURL
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["KeFuTgName"] = x.KeFuTgName
	x.fieldMap["Tag"] = x.Tag
	x.fieldMap["CaiJingTrx"] = x.CaiJingTrx
	x.fieldMap["CaiJingUsdt"] = x.CaiJingUsdt
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["CSBindTime"] = x.CSBindTime
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["LastAddAmount"] = x.LastAddAmount
	x.fieldMap["AuditAmountUsdt"] = x.AuditAmountUsdt
	x.fieldMap["AuditAmountTrx"] = x.AuditAmountTrx
	x.fieldMap["RegDeviceId"] = x.RegDeviceID
	x.fieldMap["LoginDeviceId"] = x.LoginDeviceID
	x.fieldMap["RegDeviceType"] = x.RegDeviceType
	x.fieldMap["LoginDeviceType"] = x.LoginDeviceType
	x.fieldMap["GameLimit"] = x.GameLimit
	x.fieldMap["WithwardNeedLiuSui"] = x.WithwardNeedLiuSui
	x.fieldMap["RegLang"] = x.RegLang
	x.fieldMap["LoginLang"] = x.LoginLang
	x.fieldMap["AgentType"] = x.AgentType
	x.fieldMap["AgentConfigId"] = x.AgentConfigID
	x.fieldMap["AgentNickName"] = x.AgentNickName
	x.fieldMap["AgentUseId"] = x.AgentUseID
	x.fieldMap["CxdId"] = x.CxdID
	x.fieldMap["IsoCountry"] = x.IsoCountry
	x.fieldMap["AccountType"] = x.AccountType
	x.fieldMap["TgRobotToken"] = x.TgRobotToken
	x.fieldMap["TgRobotGiftStatus"] = x.TgRobotGiftStatus
	x.fieldMap["TgBindLastSendTime"] = x.TgBindLastSendTime
	x.fieldMap["UsdtGiftStatus"] = x.UsdtGiftStatus
	x.fieldMap["TrxGiftStatus"] = x.TrxGiftStatus
	x.fieldMap["FirstSignTime"] = x.FirstSignTime
	x.fieldMap["LastSignTime"] = x.LastSignTime
	x.fieldMap["InviteRewardUsers"] = x.InviteRewardUsers
	x.fieldMap["IsInviteReward"] = x.IsInviteReward
	x.fieldMap["IsInResourceDb"] = x.IsInResourceDb
	x.fieldMap["Kwai"] = x.Kwai
	x.fieldMap["Bigo"] = x.Bigo
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["Fbc"] = x.Fbc
	x.fieldMap["AgentShortUrl"] = x.AgentShortURL
}

func (x xUser) clone(db *gorm.DB) xUser {
	x.xUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUser) replaceDB(db *gorm.DB) xUser {
	x.xUserDo.ReplaceDB(db)
	return x
}

type xUserDo struct{ gen.DO }

func (x xUserDo) Debug() *xUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserDo) WithContext(ctx context.Context) *xUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserDo) ReadDB() *xUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserDo) WriteDB() *xUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserDo) Session(config *gorm.Session) *xUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserDo) Clauses(conds ...clause.Expression) *xUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserDo) Returning(value interface{}, columns ...string) *xUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserDo) Not(conds ...gen.Condition) *xUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserDo) Or(conds ...gen.Condition) *xUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserDo) Select(conds ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserDo) Where(conds ...gen.Condition) *xUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserDo) Order(conds ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserDo) Distinct(cols ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserDo) Omit(cols ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserDo) Join(table schema.Tabler, on ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserDo) Group(cols ...field.Expr) *xUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserDo) Having(conds ...gen.Condition) *xUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserDo) Limit(limit int) *xUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserDo) Offset(offset int) *xUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserDo) Unscoped() *xUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserDo) Create(values ...*model.XUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserDo) CreateInBatches(values []*model.XUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserDo) Save(values ...*model.XUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserDo) First() (*model.XUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUser), nil
	}
}

func (x xUserDo) Take() (*model.XUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUser), nil
	}
}

func (x xUserDo) Last() (*model.XUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUser), nil
	}
}

func (x xUserDo) Find() ([]*model.XUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUser), err
}

func (x xUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUser, err error) {
	buf := make([]*model.XUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserDo) FindInBatches(result *[]*model.XUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserDo) Attrs(attrs ...field.AssignExpr) *xUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserDo) Assign(attrs ...field.AssignExpr) *xUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserDo) Joins(fields ...field.RelationField) *xUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserDo) Preload(fields ...field.RelationField) *xUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserDo) FirstOrInit() (*model.XUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUser), nil
	}
}

func (x xUserDo) FirstOrCreate() (*model.XUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUser), nil
	}
}

func (x xUserDo) FindByPage(offset int, limit int) (result []*model.XUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserDo) Delete(models ...*model.XUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserDo) withDO(do gen.Dao) *xUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
