// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXUserRechargeWithard(db *gorm.DB, opts ...gen.DOOption) xUserRechargeWithard {
	_xUserRechargeWithard := xUserRechargeWithard{}

	_xUserRechargeWithard.xUserRechargeWithardDo.UseDB(db, opts...)
	_xUserRechargeWithard.xUserRechargeWithardDo.UseModel(&model.XUserRechargeWithard{})

	tableName := _xUserRechargeWithard.xUserRechargeWithardDo.TableName()
	_xUserRechargeWithard.ALL = field.NewAsterisk(tableName)
	_xUserRechargeWithard.UserID = field.NewInt32(tableName, "UserId")
	_xUserRechargeWithard.SellerID = field.NewInt32(tableName, "SellerId")
	_xUserRechargeWithard.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xUserRechargeWithard.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xUserRechargeWithard.RechargeCount = field.NewInt32(tableName, "RechargeCount")
	_xUserRechargeWithard.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xUserRechargeWithard.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xUserRechargeWithard.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xUserRechargeWithard.WithdrawFee = field.NewFloat64(tableName, "WithdrawFee")
	_xUserRechargeWithard.FirstRechargeAmount = field.NewFloat64(tableName, "FirstRechargeAmount")
	_xUserRechargeWithard.FirstRechargeTime = field.NewTime(tableName, "FirstRechargeTime")
	_xUserRechargeWithard.CreateTime = field.NewTime(tableName, "CreateTime")
	_xUserRechargeWithard.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xUserRechargeWithard.fillFieldMap()

	return _xUserRechargeWithard
}

type xUserRechargeWithard struct {
	xUserRechargeWithardDo xUserRechargeWithardDo

	ALL                 field.Asterisk
	UserID              field.Int32
	SellerID            field.Int32
	ChannelID           field.Int32
	TopAgentID          field.Int32
	RechargeCount       field.Int32   // 充值笔数
	RechargeAmount      field.Float64 // 充值金额
	WithdrawCount       field.Int32   // 体现笔数
	WithdrawAmount      field.Float64 // 提现金额
	WithdrawFee         field.Float64 // 提现手续费
	FirstRechargeAmount field.Float64
	FirstRechargeTime   field.Time
	CreateTime          field.Time // 创建时间
	UpdateTime          field.Time // 更新时间

	fieldMap map[string]field.Expr
}

func (x xUserRechargeWithard) Table(newTableName string) *xUserRechargeWithard {
	x.xUserRechargeWithardDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xUserRechargeWithard) As(alias string) *xUserRechargeWithard {
	x.xUserRechargeWithardDo.DO = *(x.xUserRechargeWithardDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xUserRechargeWithard) updateTableName(table string) *xUserRechargeWithard {
	x.ALL = field.NewAsterisk(table)
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.RechargeCount = field.NewInt32(table, "RechargeCount")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.WithdrawFee = field.NewFloat64(table, "WithdrawFee")
	x.FirstRechargeAmount = field.NewFloat64(table, "FirstRechargeAmount")
	x.FirstRechargeTime = field.NewTime(table, "FirstRechargeTime")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xUserRechargeWithard) WithContext(ctx context.Context) *xUserRechargeWithardDo {
	return x.xUserRechargeWithardDo.WithContext(ctx)
}

func (x xUserRechargeWithard) TableName() string { return x.xUserRechargeWithardDo.TableName() }

func (x xUserRechargeWithard) Alias() string { return x.xUserRechargeWithardDo.Alias() }

func (x xUserRechargeWithard) Columns(cols ...field.Expr) gen.Columns {
	return x.xUserRechargeWithardDo.Columns(cols...)
}

func (x *xUserRechargeWithard) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xUserRechargeWithard) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 13)
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["RechargeCount"] = x.RechargeCount
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["WithdrawFee"] = x.WithdrawFee
	x.fieldMap["FirstRechargeAmount"] = x.FirstRechargeAmount
	x.fieldMap["FirstRechargeTime"] = x.FirstRechargeTime
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xUserRechargeWithard) clone(db *gorm.DB) xUserRechargeWithard {
	x.xUserRechargeWithardDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xUserRechargeWithard) replaceDB(db *gorm.DB) xUserRechargeWithard {
	x.xUserRechargeWithardDo.ReplaceDB(db)
	return x
}

type xUserRechargeWithardDo struct{ gen.DO }

func (x xUserRechargeWithardDo) Debug() *xUserRechargeWithardDo {
	return x.withDO(x.DO.Debug())
}

func (x xUserRechargeWithardDo) WithContext(ctx context.Context) *xUserRechargeWithardDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xUserRechargeWithardDo) ReadDB() *xUserRechargeWithardDo {
	return x.Clauses(dbresolver.Read)
}

func (x xUserRechargeWithardDo) WriteDB() *xUserRechargeWithardDo {
	return x.Clauses(dbresolver.Write)
}

func (x xUserRechargeWithardDo) Session(config *gorm.Session) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Session(config))
}

func (x xUserRechargeWithardDo) Clauses(conds ...clause.Expression) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xUserRechargeWithardDo) Returning(value interface{}, columns ...string) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xUserRechargeWithardDo) Not(conds ...gen.Condition) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xUserRechargeWithardDo) Or(conds ...gen.Condition) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xUserRechargeWithardDo) Select(conds ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xUserRechargeWithardDo) Where(conds ...gen.Condition) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xUserRechargeWithardDo) Order(conds ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xUserRechargeWithardDo) Distinct(cols ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xUserRechargeWithardDo) Omit(cols ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xUserRechargeWithardDo) Join(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xUserRechargeWithardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xUserRechargeWithardDo) RightJoin(table schema.Tabler, on ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xUserRechargeWithardDo) Group(cols ...field.Expr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xUserRechargeWithardDo) Having(conds ...gen.Condition) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xUserRechargeWithardDo) Limit(limit int) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xUserRechargeWithardDo) Offset(offset int) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xUserRechargeWithardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xUserRechargeWithardDo) Unscoped() *xUserRechargeWithardDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xUserRechargeWithardDo) Create(values ...*model.XUserRechargeWithard) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xUserRechargeWithardDo) CreateInBatches(values []*model.XUserRechargeWithard, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xUserRechargeWithardDo) Save(values ...*model.XUserRechargeWithard) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xUserRechargeWithardDo) First() (*model.XUserRechargeWithard, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithard), nil
	}
}

func (x xUserRechargeWithardDo) Take() (*model.XUserRechargeWithard, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithard), nil
	}
}

func (x xUserRechargeWithardDo) Last() (*model.XUserRechargeWithard, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithard), nil
	}
}

func (x xUserRechargeWithardDo) Find() ([]*model.XUserRechargeWithard, error) {
	result, err := x.DO.Find()
	return result.([]*model.XUserRechargeWithard), err
}

func (x xUserRechargeWithardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XUserRechargeWithard, err error) {
	buf := make([]*model.XUserRechargeWithard, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xUserRechargeWithardDo) FindInBatches(result *[]*model.XUserRechargeWithard, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xUserRechargeWithardDo) Attrs(attrs ...field.AssignExpr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xUserRechargeWithardDo) Assign(attrs ...field.AssignExpr) *xUserRechargeWithardDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xUserRechargeWithardDo) Joins(fields ...field.RelationField) *xUserRechargeWithardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xUserRechargeWithardDo) Preload(fields ...field.RelationField) *xUserRechargeWithardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xUserRechargeWithardDo) FirstOrInit() (*model.XUserRechargeWithard, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithard), nil
	}
}

func (x xUserRechargeWithardDo) FirstOrCreate() (*model.XUserRechargeWithard, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XUserRechargeWithard), nil
	}
}

func (x xUserRechargeWithardDo) FindByPage(offset int, limit int) (result []*model.XUserRechargeWithard, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xUserRechargeWithardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xUserRechargeWithardDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xUserRechargeWithardDo) Delete(models ...*model.XUserRechargeWithard) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xUserRechargeWithardDo) withDO(do gen.Dao) *xUserRechargeWithardDo {
	x.DO = *do.(*gen.DO)
	return x
}
