// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                                db,
		XActiveDefine:                     newXActiveDefine(db, opts...),
		XActiveLogUserSign:                newXActiveLogUserSign(db, opts...),
		XActiveRedeemcodeRecord:           newXActiveRedeemcodeRecord(db, opts...),
		XActiveReward:                     newXActiveReward(db, opts...),
		XActiveRewardAudit:                newXActiveRewardAudit(db, opts...),
		XAgent:                            newXAgent(db, opts...),
		XAgentChild:                       newXAgentChild(db, opts...),
		XAgentCodeT1:                      newXAgentCodeT1(db, opts...),
		XAgentCommissionConfig:            newXAgentCommissionConfig(db, opts...),
		XAgentDailly:                      newXAgentDailly(db, opts...),
		XAgentIndependence:                newXAgentIndependence(db, opts...),
		XAgentIndependenceFenchengHistory: newXAgentIndependenceFenchengHistory(db, opts...),
		XAgentIndependentCommissionDetail: newXAgentIndependentCommissionDetail(db, opts...),
		XAmountChangeLog:                  newXAmountChangeLog(db, opts...),
		XBonusTaskUser:                    newXBonusTaskUser(db, opts...),
		XCaijingDetail:                    newXCaijingDetail(db, opts...),
		XChannel:                          newXChannel(db, opts...),
		XChannelGameList:                  newXChannelGameList(db, opts...),
		XChannelHost:                      newXChannelHost(db, opts...),
		XConfig:                           newXConfig(db, opts...),
		XCustomDailly:                     newXCustomDailly(db, opts...),
		XCustomThird:                      newXCustomThird(db, opts...),
		XDictGametype:                     newXDictGametype(db, opts...),
		XFinanceMethod:                    newXFinanceMethod(db, opts...),
		XFinanceSymbol:                    newXFinanceSymbol(db, opts...),
		XGame:                             newXGame(db, opts...),
		XGameBrand:                        newXGameBrand(db, opts...),
		XGameChain:                        newXGameChain(db, opts...),
		XGameList:                         newXGameList(db, opts...),
		XGamePeriod:                       newXGamePeriod(db, opts...),
		XHomeCarouselV2:                   newXHomeCarouselV2(db, opts...),
		XLangGameList:                     newXLangGameList(db, opts...),
		XLangList:                         newXLangList(db, opts...),
		XLiuhecaiMap:                      newXLiuhecaiMap(db, opts...),
		XNoticeV2:                         newXNoticeV2(db, opts...),
		XOnlineBrandUser:                  newXOnlineBrandUser(db, opts...),
		XOnlineGameUser:                   newXOnlineGameUser(db, opts...),
		XOnlinePlayUser:                   newXOnlinePlayUser(db, opts...),
		XOnlineWebUser:                    newXOnlineWebUser(db, opts...),
		XOrder:                            newXOrder(db, opts...),
		XRankDatum:                        newXRankDatum(db, opts...),
		XRankNew:                          newXRankNew(db, opts...),
		XRecharge:                         newXRecharge(db, opts...),
		XRobotConfig:                      newXRobotConfig(db, opts...),
		XRobotMissionReward:               newXRobotMissionReward(db, opts...),
		XSeller:                           newXSeller(db, opts...),
		XTbBankerConfig:                   newXTbBankerConfig(db, opts...),
		XTbBankerUser:                     newXTbBankerUser(db, opts...),
		XTbWinlostConfig:                  newXTbWinlostConfig(db, opts...),
		XTgRobotGuide:                     newXTgRobotGuide(db, opts...),
		XThirdDianzhi:                     newXThirdDianzhi(db, opts...),
		XThirdLive:                        newXThirdLive(db, opts...),
		XThirdLottery:                     newXThirdLottery(db, opts...),
		XThirdQipai:                       newXThirdQipai(db, opts...),
		XThirdQuwei:                       newXThirdQuwei(db, opts...),
		XThirdSport:                       newXThirdSport(db, opts...),
		XThirdTexa:                        newXThirdTexa(db, opts...),
		XTiyanjinex:                       newXTiyanjinex(db, opts...),
		XTiyanjing:                        newXTiyanjing(db, opts...),
		XTransationUt:                     newXTransationUt(db, opts...),
		XUser:                             newXUser(db, opts...),
		XUserActive:                       newXUserActive(db, opts...),
		XUserAddress:                      newXUserAddress(db, opts...),
		XUserDailly:                       newXUserDailly(db, opts...),
		XUserMore:                         newXUserMore(db, opts...),
		XUserOnline:                       newXUserOnline(db, opts...),
		XUserOnlineProcess:                newXUserOnlineProcess(db, opts...),
		XUserRechargeWithard:              newXUserRechargeWithard(db, opts...),
		XUserRechargeWithardDate:          newXUserRechargeWithardDate(db, opts...),
		XUserReduce:                       newXUserReduce(db, opts...),
		XUserSetting:                      newXUserSetting(db, opts...),
		XUserStatAddress:                  newXUserStatAddress(db, opts...),
		XUserWallet:                       newXUserWallet(db, opts...),
		XVerify:                           newXVerify(db, opts...),
		XVipDailly:                        newXVipDailly(db, opts...),
		XVipDefine:                        newXVipDefine(db, opts...),
		XVipInfo:                          newXVipInfo(db, opts...),
		XVipMonthly:                       newXVipMonthly(db, opts...),
		XVipWeekly:                        newXVipWeekly(db, opts...),
		XWithdraw:                         newXWithdraw(db, opts...),
		XWithdrawAddress:                  newXWithdrawAddress(db, opts...),
		XWithdrawLimitConfig:              newXWithdrawLimitConfig(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	XActiveDefine                     xActiveDefine
	XActiveLogUserSign                xActiveLogUserSign
	XActiveRedeemcodeRecord           xActiveRedeemcodeRecord
	XActiveReward                     xActiveReward
	XActiveRewardAudit                xActiveRewardAudit
	XAgent                            xAgent
	XAgentChild                       xAgentChild
	XAgentCodeT1                      xAgentCodeT1
	XAgentCommissionConfig            xAgentCommissionConfig
	XAgentDailly                      xAgentDailly
	XAgentIndependence                xAgentIndependence
	XAgentIndependenceFenchengHistory xAgentIndependenceFenchengHistory
	XAgentIndependentCommissionDetail xAgentIndependentCommissionDetail
	XAmountChangeLog                  xAmountChangeLog
	XBonusTaskUser                    xBonusTaskUser
	XCaijingDetail                    xCaijingDetail
	XChannel                          xChannel
	XChannelGameList                  xChannelGameList
	XChannelHost                      xChannelHost
	XConfig                           xConfig
	XCustomDailly                     xCustomDailly
	XCustomThird                      xCustomThird
	XDictGametype                     xDictGametype
	XFinanceMethod                    xFinanceMethod
	XFinanceSymbol                    xFinanceSymbol
	XGame                             xGame
	XGameBrand                        xGameBrand
	XGameChain                        xGameChain
	XGameList                         xGameList
	XGamePeriod                       xGamePeriod
	XHomeCarouselV2                   xHomeCarouselV2
	XLangGameList                     xLangGameList
	XLangList                         xLangList
	XLiuhecaiMap                      xLiuhecaiMap
	XNoticeV2                         xNoticeV2
	XOnlineBrandUser                  xOnlineBrandUser
	XOnlineGameUser                   xOnlineGameUser
	XOnlinePlayUser                   xOnlinePlayUser
	XOnlineWebUser                    xOnlineWebUser
	XOrder                            xOrder
	XRankDatum                        xRankDatum
	XRankNew                          xRankNew
	XRecharge                         xRecharge
	XRobotConfig                      xRobotConfig
	XRobotMissionReward               xRobotMissionReward
	XSeller                           xSeller
	XTbBankerConfig                   xTbBankerConfig
	XTbBankerUser                     xTbBankerUser
	XTbWinlostConfig                  xTbWinlostConfig
	XTgRobotGuide                     xTgRobotGuide
	XThirdDianzhi                     xThirdDianzhi
	XThirdLive                        xThirdLive
	XThirdLottery                     xThirdLottery
	XThirdQipai                       xThirdQipai
	XThirdQuwei                       xThirdQuwei
	XThirdSport                       xThirdSport
	XThirdTexa                        xThirdTexa
	XTiyanjinex                       xTiyanjinex
	XTiyanjing                        xTiyanjing
	XTransationUt                     xTransationUt
	XUser                             xUser
	XUserActive                       xUserActive
	XUserAddress                      xUserAddress
	XUserDailly                       xUserDailly
	XUserMore                         xUserMore
	XUserOnline                       xUserOnline
	XUserOnlineProcess                xUserOnlineProcess
	XUserRechargeWithard              xUserRechargeWithard
	XUserRechargeWithardDate          xUserRechargeWithardDate
	XUserReduce                       xUserReduce
	XUserSetting                      xUserSetting
	XUserStatAddress                  xUserStatAddress
	XUserWallet                       xUserWallet
	XVerify                           xVerify
	XVipDailly                        xVipDailly
	XVipDefine                        xVipDefine
	XVipInfo                          xVipInfo
	XVipMonthly                       xVipMonthly
	XVipWeekly                        xVipWeekly
	XWithdraw                         xWithdraw
	XWithdrawAddress                  xWithdrawAddress
	XWithdrawLimitConfig              xWithdrawLimitConfig
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                                db,
		XActiveDefine:                     q.XActiveDefine.clone(db),
		XActiveLogUserSign:                q.XActiveLogUserSign.clone(db),
		XActiveRedeemcodeRecord:           q.XActiveRedeemcodeRecord.clone(db),
		XActiveReward:                     q.XActiveReward.clone(db),
		XActiveRewardAudit:                q.XActiveRewardAudit.clone(db),
		XAgent:                            q.XAgent.clone(db),
		XAgentChild:                       q.XAgentChild.clone(db),
		XAgentCodeT1:                      q.XAgentCodeT1.clone(db),
		XAgentCommissionConfig:            q.XAgentCommissionConfig.clone(db),
		XAgentDailly:                      q.XAgentDailly.clone(db),
		XAgentIndependence:                q.XAgentIndependence.clone(db),
		XAgentIndependenceFenchengHistory: q.XAgentIndependenceFenchengHistory.clone(db),
		XAgentIndependentCommissionDetail: q.XAgentIndependentCommissionDetail.clone(db),
		XAmountChangeLog:                  q.XAmountChangeLog.clone(db),
		XBonusTaskUser:                    q.XBonusTaskUser.clone(db),
		XCaijingDetail:                    q.XCaijingDetail.clone(db),
		XChannel:                          q.XChannel.clone(db),
		XChannelGameList:                  q.XChannelGameList.clone(db),
		XChannelHost:                      q.XChannelHost.clone(db),
		XConfig:                           q.XConfig.clone(db),
		XCustomDailly:                     q.XCustomDailly.clone(db),
		XCustomThird:                      q.XCustomThird.clone(db),
		XDictGametype:                     q.XDictGametype.clone(db),
		XFinanceMethod:                    q.XFinanceMethod.clone(db),
		XFinanceSymbol:                    q.XFinanceSymbol.clone(db),
		XGame:                             q.XGame.clone(db),
		XGameBrand:                        q.XGameBrand.clone(db),
		XGameChain:                        q.XGameChain.clone(db),
		XGameList:                         q.XGameList.clone(db),
		XGamePeriod:                       q.XGamePeriod.clone(db),
		XHomeCarouselV2:                   q.XHomeCarouselV2.clone(db),
		XLangGameList:                     q.XLangGameList.clone(db),
		XLangList:                         q.XLangList.clone(db),
		XLiuhecaiMap:                      q.XLiuhecaiMap.clone(db),
		XNoticeV2:                         q.XNoticeV2.clone(db),
		XOnlineBrandUser:                  q.XOnlineBrandUser.clone(db),
		XOnlineGameUser:                   q.XOnlineGameUser.clone(db),
		XOnlinePlayUser:                   q.XOnlinePlayUser.clone(db),
		XOnlineWebUser:                    q.XOnlineWebUser.clone(db),
		XOrder:                            q.XOrder.clone(db),
		XRankDatum:                        q.XRankDatum.clone(db),
		XRankNew:                          q.XRankNew.clone(db),
		XRecharge:                         q.XRecharge.clone(db),
		XRobotConfig:                      q.XRobotConfig.clone(db),
		XRobotMissionReward:               q.XRobotMissionReward.clone(db),
		XSeller:                           q.XSeller.clone(db),
		XTbBankerConfig:                   q.XTbBankerConfig.clone(db),
		XTbBankerUser:                     q.XTbBankerUser.clone(db),
		XTbWinlostConfig:                  q.XTbWinlostConfig.clone(db),
		XTgRobotGuide:                     q.XTgRobotGuide.clone(db),
		XThirdDianzhi:                     q.XThirdDianzhi.clone(db),
		XThirdLive:                        q.XThirdLive.clone(db),
		XThirdLottery:                     q.XThirdLottery.clone(db),
		XThirdQipai:                       q.XThirdQipai.clone(db),
		XThirdQuwei:                       q.XThirdQuwei.clone(db),
		XThirdSport:                       q.XThirdSport.clone(db),
		XThirdTexa:                        q.XThirdTexa.clone(db),
		XTiyanjinex:                       q.XTiyanjinex.clone(db),
		XTiyanjing:                        q.XTiyanjing.clone(db),
		XTransationUt:                     q.XTransationUt.clone(db),
		XUser:                             q.XUser.clone(db),
		XUserActive:                       q.XUserActive.clone(db),
		XUserAddress:                      q.XUserAddress.clone(db),
		XUserDailly:                       q.XUserDailly.clone(db),
		XUserMore:                         q.XUserMore.clone(db),
		XUserOnline:                       q.XUserOnline.clone(db),
		XUserOnlineProcess:                q.XUserOnlineProcess.clone(db),
		XUserRechargeWithard:              q.XUserRechargeWithard.clone(db),
		XUserRechargeWithardDate:          q.XUserRechargeWithardDate.clone(db),
		XUserReduce:                       q.XUserReduce.clone(db),
		XUserSetting:                      q.XUserSetting.clone(db),
		XUserStatAddress:                  q.XUserStatAddress.clone(db),
		XUserWallet:                       q.XUserWallet.clone(db),
		XVerify:                           q.XVerify.clone(db),
		XVipDailly:                        q.XVipDailly.clone(db),
		XVipDefine:                        q.XVipDefine.clone(db),
		XVipInfo:                          q.XVipInfo.clone(db),
		XVipMonthly:                       q.XVipMonthly.clone(db),
		XVipWeekly:                        q.XVipWeekly.clone(db),
		XWithdraw:                         q.XWithdraw.clone(db),
		XWithdrawAddress:                  q.XWithdrawAddress.clone(db),
		XWithdrawLimitConfig:              q.XWithdrawLimitConfig.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                                db,
		XActiveDefine:                     q.XActiveDefine.replaceDB(db),
		XActiveLogUserSign:                q.XActiveLogUserSign.replaceDB(db),
		XActiveRedeemcodeRecord:           q.XActiveRedeemcodeRecord.replaceDB(db),
		XActiveReward:                     q.XActiveReward.replaceDB(db),
		XActiveRewardAudit:                q.XActiveRewardAudit.replaceDB(db),
		XAgent:                            q.XAgent.replaceDB(db),
		XAgentChild:                       q.XAgentChild.replaceDB(db),
		XAgentCodeT1:                      q.XAgentCodeT1.replaceDB(db),
		XAgentCommissionConfig:            q.XAgentCommissionConfig.replaceDB(db),
		XAgentDailly:                      q.XAgentDailly.replaceDB(db),
		XAgentIndependence:                q.XAgentIndependence.replaceDB(db),
		XAgentIndependenceFenchengHistory: q.XAgentIndependenceFenchengHistory.replaceDB(db),
		XAgentIndependentCommissionDetail: q.XAgentIndependentCommissionDetail.replaceDB(db),
		XAmountChangeLog:                  q.XAmountChangeLog.replaceDB(db),
		XBonusTaskUser:                    q.XBonusTaskUser.replaceDB(db),
		XCaijingDetail:                    q.XCaijingDetail.replaceDB(db),
		XChannel:                          q.XChannel.replaceDB(db),
		XChannelGameList:                  q.XChannelGameList.replaceDB(db),
		XChannelHost:                      q.XChannelHost.replaceDB(db),
		XConfig:                           q.XConfig.replaceDB(db),
		XCustomDailly:                     q.XCustomDailly.replaceDB(db),
		XCustomThird:                      q.XCustomThird.replaceDB(db),
		XDictGametype:                     q.XDictGametype.replaceDB(db),
		XFinanceMethod:                    q.XFinanceMethod.replaceDB(db),
		XFinanceSymbol:                    q.XFinanceSymbol.replaceDB(db),
		XGame:                             q.XGame.replaceDB(db),
		XGameBrand:                        q.XGameBrand.replaceDB(db),
		XGameChain:                        q.XGameChain.replaceDB(db),
		XGameList:                         q.XGameList.replaceDB(db),
		XGamePeriod:                       q.XGamePeriod.replaceDB(db),
		XHomeCarouselV2:                   q.XHomeCarouselV2.replaceDB(db),
		XLangGameList:                     q.XLangGameList.replaceDB(db),
		XLangList:                         q.XLangList.replaceDB(db),
		XLiuhecaiMap:                      q.XLiuhecaiMap.replaceDB(db),
		XNoticeV2:                         q.XNoticeV2.replaceDB(db),
		XOnlineBrandUser:                  q.XOnlineBrandUser.replaceDB(db),
		XOnlineGameUser:                   q.XOnlineGameUser.replaceDB(db),
		XOnlinePlayUser:                   q.XOnlinePlayUser.replaceDB(db),
		XOnlineWebUser:                    q.XOnlineWebUser.replaceDB(db),
		XOrder:                            q.XOrder.replaceDB(db),
		XRankDatum:                        q.XRankDatum.replaceDB(db),
		XRankNew:                          q.XRankNew.replaceDB(db),
		XRecharge:                         q.XRecharge.replaceDB(db),
		XRobotConfig:                      q.XRobotConfig.replaceDB(db),
		XRobotMissionReward:               q.XRobotMissionReward.replaceDB(db),
		XSeller:                           q.XSeller.replaceDB(db),
		XTbBankerConfig:                   q.XTbBankerConfig.replaceDB(db),
		XTbBankerUser:                     q.XTbBankerUser.replaceDB(db),
		XTbWinlostConfig:                  q.XTbWinlostConfig.replaceDB(db),
		XTgRobotGuide:                     q.XTgRobotGuide.replaceDB(db),
		XThirdDianzhi:                     q.XThirdDianzhi.replaceDB(db),
		XThirdLive:                        q.XThirdLive.replaceDB(db),
		XThirdLottery:                     q.XThirdLottery.replaceDB(db),
		XThirdQipai:                       q.XThirdQipai.replaceDB(db),
		XThirdQuwei:                       q.XThirdQuwei.replaceDB(db),
		XThirdSport:                       q.XThirdSport.replaceDB(db),
		XThirdTexa:                        q.XThirdTexa.replaceDB(db),
		XTiyanjinex:                       q.XTiyanjinex.replaceDB(db),
		XTiyanjing:                        q.XTiyanjing.replaceDB(db),
		XTransationUt:                     q.XTransationUt.replaceDB(db),
		XUser:                             q.XUser.replaceDB(db),
		XUserActive:                       q.XUserActive.replaceDB(db),
		XUserAddress:                      q.XUserAddress.replaceDB(db),
		XUserDailly:                       q.XUserDailly.replaceDB(db),
		XUserMore:                         q.XUserMore.replaceDB(db),
		XUserOnline:                       q.XUserOnline.replaceDB(db),
		XUserOnlineProcess:                q.XUserOnlineProcess.replaceDB(db),
		XUserRechargeWithard:              q.XUserRechargeWithard.replaceDB(db),
		XUserRechargeWithardDate:          q.XUserRechargeWithardDate.replaceDB(db),
		XUserReduce:                       q.XUserReduce.replaceDB(db),
		XUserSetting:                      q.XUserSetting.replaceDB(db),
		XUserStatAddress:                  q.XUserStatAddress.replaceDB(db),
		XUserWallet:                       q.XUserWallet.replaceDB(db),
		XVerify:                           q.XVerify.replaceDB(db),
		XVipDailly:                        q.XVipDailly.replaceDB(db),
		XVipDefine:                        q.XVipDefine.replaceDB(db),
		XVipInfo:                          q.XVipInfo.replaceDB(db),
		XVipMonthly:                       q.XVipMonthly.replaceDB(db),
		XVipWeekly:                        q.XVipWeekly.replaceDB(db),
		XWithdraw:                         q.XWithdraw.replaceDB(db),
		XWithdrawAddress:                  q.XWithdrawAddress.replaceDB(db),
		XWithdrawLimitConfig:              q.XWithdrawLimitConfig.replaceDB(db),
	}
}

type queryCtx struct {
	XActiveDefine                     *xActiveDefineDo
	XActiveLogUserSign                *xActiveLogUserSignDo
	XActiveRedeemcodeRecord           *xActiveRedeemcodeRecordDo
	XActiveReward                     *xActiveRewardDo
	XActiveRewardAudit                *xActiveRewardAuditDo
	XAgent                            *xAgentDo
	XAgentChild                       *xAgentChildDo
	XAgentCodeT1                      *xAgentCodeT1Do
	XAgentCommissionConfig            *xAgentCommissionConfigDo
	XAgentDailly                      *xAgentDaillyDo
	XAgentIndependence                *xAgentIndependenceDo
	XAgentIndependenceFenchengHistory *xAgentIndependenceFenchengHistoryDo
	XAgentIndependentCommissionDetail *xAgentIndependentCommissionDetailDo
	XAmountChangeLog                  *xAmountChangeLogDo
	XBonusTaskUser                    *xBonusTaskUserDo
	XCaijingDetail                    *xCaijingDetailDo
	XChannel                          *xChannelDo
	XChannelGameList                  *xChannelGameListDo
	XChannelHost                      *xChannelHostDo
	XConfig                           *xConfigDo
	XCustomDailly                     *xCustomDaillyDo
	XCustomThird                      *xCustomThirdDo
	XDictGametype                     *xDictGametypeDo
	XFinanceMethod                    *xFinanceMethodDo
	XFinanceSymbol                    *xFinanceSymbolDo
	XGame                             *xGameDo
	XGameBrand                        *xGameBrandDo
	XGameChain                        *xGameChainDo
	XGameList                         *xGameListDo
	XGamePeriod                       *xGamePeriodDo
	XHomeCarouselV2                   *xHomeCarouselV2Do
	XLangGameList                     *xLangGameListDo
	XLangList                         *xLangListDo
	XLiuhecaiMap                      *xLiuhecaiMapDo
	XNoticeV2                         *xNoticeV2Do
	XOnlineBrandUser                  *xOnlineBrandUserDo
	XOnlineGameUser                   *xOnlineGameUserDo
	XOnlinePlayUser                   *xOnlinePlayUserDo
	XOnlineWebUser                    *xOnlineWebUserDo
	XOrder                            *xOrderDo
	XRankDatum                        *xRankDatumDo
	XRankNew                          *xRankNewDo
	XRecharge                         *xRechargeDo
	XRobotConfig                      *xRobotConfigDo
	XRobotMissionReward               *xRobotMissionRewardDo
	XSeller                           *xSellerDo
	XTbBankerConfig                   *xTbBankerConfigDo
	XTbBankerUser                     *xTbBankerUserDo
	XTbWinlostConfig                  *xTbWinlostConfigDo
	XTgRobotGuide                     *xTgRobotGuideDo
	XThirdDianzhi                     *xThirdDianzhiDo
	XThirdLive                        *xThirdLiveDo
	XThirdLottery                     *xThirdLotteryDo
	XThirdQipai                       *xThirdQipaiDo
	XThirdQuwei                       *xThirdQuweiDo
	XThirdSport                       *xThirdSportDo
	XThirdTexa                        *xThirdTexaDo
	XTiyanjinex                       *xTiyanjinexDo
	XTiyanjing                        *xTiyanjingDo
	XTransationUt                     *xTransationUtDo
	XUser                             *xUserDo
	XUserActive                       *xUserActiveDo
	XUserAddress                      *xUserAddressDo
	XUserDailly                       *xUserDaillyDo
	XUserMore                         *xUserMoreDo
	XUserOnline                       *xUserOnlineDo
	XUserOnlineProcess                *xUserOnlineProcessDo
	XUserRechargeWithard              *xUserRechargeWithardDo
	XUserRechargeWithardDate          *xUserRechargeWithardDateDo
	XUserReduce                       *xUserReduceDo
	XUserSetting                      *xUserSettingDo
	XUserStatAddress                  *xUserStatAddressDo
	XUserWallet                       *xUserWalletDo
	XVerify                           *xVerifyDo
	XVipDailly                        *xVipDaillyDo
	XVipDefine                        *xVipDefineDo
	XVipInfo                          *xVipInfoDo
	XVipMonthly                       *xVipMonthlyDo
	XVipWeekly                        *xVipWeeklyDo
	XWithdraw                         *xWithdrawDo
	XWithdrawAddress                  *xWithdrawAddressDo
	XWithdrawLimitConfig              *xWithdrawLimitConfigDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		XActiveDefine:                     q.XActiveDefine.WithContext(ctx),
		XActiveLogUserSign:                q.XActiveLogUserSign.WithContext(ctx),
		XActiveRedeemcodeRecord:           q.XActiveRedeemcodeRecord.WithContext(ctx),
		XActiveReward:                     q.XActiveReward.WithContext(ctx),
		XActiveRewardAudit:                q.XActiveRewardAudit.WithContext(ctx),
		XAgent:                            q.XAgent.WithContext(ctx),
		XAgentChild:                       q.XAgentChild.WithContext(ctx),
		XAgentCodeT1:                      q.XAgentCodeT1.WithContext(ctx),
		XAgentCommissionConfig:            q.XAgentCommissionConfig.WithContext(ctx),
		XAgentDailly:                      q.XAgentDailly.WithContext(ctx),
		XAgentIndependence:                q.XAgentIndependence.WithContext(ctx),
		XAgentIndependenceFenchengHistory: q.XAgentIndependenceFenchengHistory.WithContext(ctx),
		XAgentIndependentCommissionDetail: q.XAgentIndependentCommissionDetail.WithContext(ctx),
		XAmountChangeLog:                  q.XAmountChangeLog.WithContext(ctx),
		XBonusTaskUser:                    q.XBonusTaskUser.WithContext(ctx),
		XCaijingDetail:                    q.XCaijingDetail.WithContext(ctx),
		XChannel:                          q.XChannel.WithContext(ctx),
		XChannelGameList:                  q.XChannelGameList.WithContext(ctx),
		XChannelHost:                      q.XChannelHost.WithContext(ctx),
		XConfig:                           q.XConfig.WithContext(ctx),
		XCustomDailly:                     q.XCustomDailly.WithContext(ctx),
		XCustomThird:                      q.XCustomThird.WithContext(ctx),
		XDictGametype:                     q.XDictGametype.WithContext(ctx),
		XFinanceMethod:                    q.XFinanceMethod.WithContext(ctx),
		XFinanceSymbol:                    q.XFinanceSymbol.WithContext(ctx),
		XGame:                             q.XGame.WithContext(ctx),
		XGameBrand:                        q.XGameBrand.WithContext(ctx),
		XGameChain:                        q.XGameChain.WithContext(ctx),
		XGameList:                         q.XGameList.WithContext(ctx),
		XGamePeriod:                       q.XGamePeriod.WithContext(ctx),
		XHomeCarouselV2:                   q.XHomeCarouselV2.WithContext(ctx),
		XLangGameList:                     q.XLangGameList.WithContext(ctx),
		XLangList:                         q.XLangList.WithContext(ctx),
		XLiuhecaiMap:                      q.XLiuhecaiMap.WithContext(ctx),
		XNoticeV2:                         q.XNoticeV2.WithContext(ctx),
		XOnlineBrandUser:                  q.XOnlineBrandUser.WithContext(ctx),
		XOnlineGameUser:                   q.XOnlineGameUser.WithContext(ctx),
		XOnlinePlayUser:                   q.XOnlinePlayUser.WithContext(ctx),
		XOnlineWebUser:                    q.XOnlineWebUser.WithContext(ctx),
		XOrder:                            q.XOrder.WithContext(ctx),
		XRankDatum:                        q.XRankDatum.WithContext(ctx),
		XRankNew:                          q.XRankNew.WithContext(ctx),
		XRecharge:                         q.XRecharge.WithContext(ctx),
		XRobotConfig:                      q.XRobotConfig.WithContext(ctx),
		XRobotMissionReward:               q.XRobotMissionReward.WithContext(ctx),
		XSeller:                           q.XSeller.WithContext(ctx),
		XTbBankerConfig:                   q.XTbBankerConfig.WithContext(ctx),
		XTbBankerUser:                     q.XTbBankerUser.WithContext(ctx),
		XTbWinlostConfig:                  q.XTbWinlostConfig.WithContext(ctx),
		XTgRobotGuide:                     q.XTgRobotGuide.WithContext(ctx),
		XThirdDianzhi:                     q.XThirdDianzhi.WithContext(ctx),
		XThirdLive:                        q.XThirdLive.WithContext(ctx),
		XThirdLottery:                     q.XThirdLottery.WithContext(ctx),
		XThirdQipai:                       q.XThirdQipai.WithContext(ctx),
		XThirdQuwei:                       q.XThirdQuwei.WithContext(ctx),
		XThirdSport:                       q.XThirdSport.WithContext(ctx),
		XThirdTexa:                        q.XThirdTexa.WithContext(ctx),
		XTiyanjinex:                       q.XTiyanjinex.WithContext(ctx),
		XTiyanjing:                        q.XTiyanjing.WithContext(ctx),
		XTransationUt:                     q.XTransationUt.WithContext(ctx),
		XUser:                             q.XUser.WithContext(ctx),
		XUserActive:                       q.XUserActive.WithContext(ctx),
		XUserAddress:                      q.XUserAddress.WithContext(ctx),
		XUserDailly:                       q.XUserDailly.WithContext(ctx),
		XUserMore:                         q.XUserMore.WithContext(ctx),
		XUserOnline:                       q.XUserOnline.WithContext(ctx),
		XUserOnlineProcess:                q.XUserOnlineProcess.WithContext(ctx),
		XUserRechargeWithard:              q.XUserRechargeWithard.WithContext(ctx),
		XUserRechargeWithardDate:          q.XUserRechargeWithardDate.WithContext(ctx),
		XUserReduce:                       q.XUserReduce.WithContext(ctx),
		XUserSetting:                      q.XUserSetting.WithContext(ctx),
		XUserStatAddress:                  q.XUserStatAddress.WithContext(ctx),
		XUserWallet:                       q.XUserWallet.WithContext(ctx),
		XVerify:                           q.XVerify.WithContext(ctx),
		XVipDailly:                        q.XVipDailly.WithContext(ctx),
		XVipDefine:                        q.XVipDefine.WithContext(ctx),
		XVipInfo:                          q.XVipInfo.WithContext(ctx),
		XVipMonthly:                       q.XVipMonthly.WithContext(ctx),
		XVipWeekly:                        q.XVipWeekly.WithContext(ctx),
		XWithdraw:                         q.XWithdraw.WithContext(ctx),
		XWithdrawAddress:                  q.XWithdrawAddress.WithContext(ctx),
		XWithdrawLimitConfig:              q.XWithdrawLimitConfig.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
