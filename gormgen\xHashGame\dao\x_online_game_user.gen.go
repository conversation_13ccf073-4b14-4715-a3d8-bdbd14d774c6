// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOnlineGameUser(db *gorm.DB, opts ...gen.DOOption) xOnlineGameUser {
	_xOnlineGameUser := xOnlineGameUser{}

	_xOnlineGameUser.xOnlineGameUserDo.UseDB(db, opts...)
	_xOnlineGameUser.xOnlineGameUserDo.UseModel(&model.XOnlineGameUser{})

	tableName := _xOnlineGameUser.xOnlineGameUserDo.TableName()
	_xOnlineGameUser.ALL = field.NewAsterisk(tableName)
	_xOnlineGameUser.ID = field.NewInt32(tableName, "Id")
	_xOnlineGameUser.Online = field.NewInt32(tableName, "Online")
	_xOnlineGameUser.GameID = field.NewString(tableName, "GameId")
	_xOnlineGameUser.Brand = field.NewString(tableName, "Brand")
	_xOnlineGameUser.CreateTime = field.NewTime(tableName, "CreateTime")

	_xOnlineGameUser.fillFieldMap()

	return _xOnlineGameUser
}

type xOnlineGameUser struct {
	xOnlineGameUserDo xOnlineGameUserDo

	ALL        field.Asterisk
	ID         field.Int32
	Online     field.Int32  // 在线人数
	GameID     field.String // 游戏ID
	Brand      field.String // 游戏厂商
	CreateTime field.Time   // 记录时间

	fieldMap map[string]field.Expr
}

func (x xOnlineGameUser) Table(newTableName string) *xOnlineGameUser {
	x.xOnlineGameUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOnlineGameUser) As(alias string) *xOnlineGameUser {
	x.xOnlineGameUserDo.DO = *(x.xOnlineGameUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOnlineGameUser) updateTableName(table string) *xOnlineGameUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Online = field.NewInt32(table, "Online")
	x.GameID = field.NewString(table, "GameId")
	x.Brand = field.NewString(table, "Brand")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xOnlineGameUser) WithContext(ctx context.Context) *xOnlineGameUserDo {
	return x.xOnlineGameUserDo.WithContext(ctx)
}

func (x xOnlineGameUser) TableName() string { return x.xOnlineGameUserDo.TableName() }

func (x xOnlineGameUser) Alias() string { return x.xOnlineGameUserDo.Alias() }

func (x xOnlineGameUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xOnlineGameUserDo.Columns(cols...)
}

func (x *xOnlineGameUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOnlineGameUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 5)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Online"] = x.Online
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xOnlineGameUser) clone(db *gorm.DB) xOnlineGameUser {
	x.xOnlineGameUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOnlineGameUser) replaceDB(db *gorm.DB) xOnlineGameUser {
	x.xOnlineGameUserDo.ReplaceDB(db)
	return x
}

type xOnlineGameUserDo struct{ gen.DO }

func (x xOnlineGameUserDo) Debug() *xOnlineGameUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xOnlineGameUserDo) WithContext(ctx context.Context) *xOnlineGameUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOnlineGameUserDo) ReadDB() *xOnlineGameUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOnlineGameUserDo) WriteDB() *xOnlineGameUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOnlineGameUserDo) Session(config *gorm.Session) *xOnlineGameUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOnlineGameUserDo) Clauses(conds ...clause.Expression) *xOnlineGameUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOnlineGameUserDo) Returning(value interface{}, columns ...string) *xOnlineGameUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOnlineGameUserDo) Not(conds ...gen.Condition) *xOnlineGameUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOnlineGameUserDo) Or(conds ...gen.Condition) *xOnlineGameUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOnlineGameUserDo) Select(conds ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOnlineGameUserDo) Where(conds ...gen.Condition) *xOnlineGameUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOnlineGameUserDo) Order(conds ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOnlineGameUserDo) Distinct(cols ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOnlineGameUserDo) Omit(cols ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOnlineGameUserDo) Join(table schema.Tabler, on ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOnlineGameUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOnlineGameUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOnlineGameUserDo) Group(cols ...field.Expr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOnlineGameUserDo) Having(conds ...gen.Condition) *xOnlineGameUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOnlineGameUserDo) Limit(limit int) *xOnlineGameUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOnlineGameUserDo) Offset(offset int) *xOnlineGameUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOnlineGameUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOnlineGameUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOnlineGameUserDo) Unscoped() *xOnlineGameUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOnlineGameUserDo) Create(values ...*model.XOnlineGameUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOnlineGameUserDo) CreateInBatches(values []*model.XOnlineGameUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOnlineGameUserDo) Save(values ...*model.XOnlineGameUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOnlineGameUserDo) First() (*model.XOnlineGameUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineGameUser), nil
	}
}

func (x xOnlineGameUserDo) Take() (*model.XOnlineGameUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineGameUser), nil
	}
}

func (x xOnlineGameUserDo) Last() (*model.XOnlineGameUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineGameUser), nil
	}
}

func (x xOnlineGameUserDo) Find() ([]*model.XOnlineGameUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOnlineGameUser), err
}

func (x xOnlineGameUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOnlineGameUser, err error) {
	buf := make([]*model.XOnlineGameUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOnlineGameUserDo) FindInBatches(result *[]*model.XOnlineGameUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOnlineGameUserDo) Attrs(attrs ...field.AssignExpr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOnlineGameUserDo) Assign(attrs ...field.AssignExpr) *xOnlineGameUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOnlineGameUserDo) Joins(fields ...field.RelationField) *xOnlineGameUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOnlineGameUserDo) Preload(fields ...field.RelationField) *xOnlineGameUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOnlineGameUserDo) FirstOrInit() (*model.XOnlineGameUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineGameUser), nil
	}
}

func (x xOnlineGameUserDo) FirstOrCreate() (*model.XOnlineGameUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineGameUser), nil
	}
}

func (x xOnlineGameUserDo) FindByPage(offset int, limit int) (result []*model.XOnlineGameUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOnlineGameUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOnlineGameUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOnlineGameUserDo) Delete(models ...*model.XOnlineGameUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOnlineGameUserDo) withDO(do gen.Dao) *xOnlineGameUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
