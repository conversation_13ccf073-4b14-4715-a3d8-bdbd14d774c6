package twitter

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"github.com/golang-module/carbon/v2"
)

func (c *Client) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	logs.Info("请求地址 %v，Twitter 请求参数 %v", url, string(data))
	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, err
	}

	logs.Info("Twitter 返回：", resp.String())

	return resp, nil
}

func (c *Client) PushEvent(eventId string, eventName string, amount float64) (err error) {
	data := &PostData{
		Conversions: []Conversion{
			{
				ConversionTime: carbon.Now().ToStdTime(),
				EventID:        eventName,
				Identifiers:    []Identifier{},
				Value:          amount,
				NumberItems:    1,
				ConversionID:   eventId,
				Description:    "",
				Contents: []Content{
					{
						ContentID: eventId,
					},
				},
			},
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = c.post(c.api, map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + c.accessToken,
	}, jsonData)

	if err != nil {
		return err
	}

	return nil
}

func (c *Client) AddToCart(eventId string) (err error) {
	return c.PushEvent(eventId, c.addToCart, 0)
}

func (c *Client) Purchase(eventId string, amount float64) (err error) {
	return c.PushEvent(eventId, c.purchase, amount)
}

func (c *Client) FirstRecharge(eventId string, amount float64) (err error) {
	return c.PushEvent(eventId, c.firstRecharge, amount)
}

func (c *Client) CompleteRegistration(eventId string, amount float64) (err error) {
	return c.PushEvent(eventId, c.completeRegistration, amount)
}
