// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXWithdrawLimitConfig = "x_withdraw_limit_config"

// XWithdrawLimitConfig 提款限制表
type XWithdrawLimitConfig struct {
	ID                 int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增id" json:"Id"`             // 自增id
	SellerID           int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                       // 运营商
	ChannelID          int32     `gorm:"column:ChannelId;not null;comment:渠道商" json:"ChannelId"`                     // 渠道商
	Type               int32     `gorm:"column:Type;comment:配置类型(1:全局 2:VIP 3:代理 4:个人)" json:"Type"`                 // 配置类型(1:全局 2:VIP 3:代理 4:个人)
	Status             int32     `gorm:"column:Status;comment:状态（1开启 2关闭）" json:"Status"`                            // 状态（1开启 2关闭）
	MaxAmountPerDay    float64   `gorm:"column:MaxAmountPerDay;comment:单日提款金额上限" json:"MaxAmountPerDay"`             // 单日提款金额上限
	MaxCountPerDay     int32     `gorm:"column:MaxCountPerDay;comment:单日提款次数上限" json:"MaxCountPerDay"`               // 单日提款次数上限
	FeeFreeQuotaPerDay float64   `gorm:"column:FeeFreeQuotaPerDay;comment:单日免手续费额度" json:"FeeFreeQuotaPerDay"`       // 单日免手续费额度
	FeeFreeCountPerDay int32     `gorm:"column:FeeFreeCountPerDay;comment:单日免手续费提款次数" json:"FeeFreeCountPerDay"`     // 单日免手续费提款次数
	FeePercent         float64   `gorm:"column:FeePercent;comment:超出后手续费%" json:"FeePercent"`                        // 超出后手续费%
	MaxAmountEveryTime float64   `gorm:"column:MaxAmountEveryTime;comment:单次提款上限" json:"MaxAmountEveryTime"`         // 单次提款上限
	VipLevel           int32     `gorm:"column:VipLevel;comment:vip等级" json:"VipLevel"`                              // vip等级
	UserID             int32     `gorm:"column:UserId;comment:玩家id" json:"UserId"`                                   // 玩家id
	CreateTime         time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime         time.Time `gorm:"column:UpdateTime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XWithdrawLimitConfig's table name
func (*XWithdrawLimitConfig) TableName() string {
	return TableNameXWithdrawLimitConfig
}
