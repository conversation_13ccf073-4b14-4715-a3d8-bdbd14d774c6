package controller

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"net/http"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
)

// Domain 域名信息
type Domain struct {
	Domain   string `json:"domain"`
	Priority int    `json:"priority"`
}

// DomainResponse 域名查询响应
type DomainResponse struct {
	Success bool     `json:"success"`
	Message string   `json:"message"`
	Data    []string `json:"data"`
}

// BalanceResponse 余额查询响应
type BalanceResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Balance  float64 `json:"balance"`
		Exposure float64 `json:"exposure"`
	} `json:"data"`
}

// WithdrawResponse 提现响应
type WithdrawResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		WithdrawBalance  float64 `json:"withdrawBalance"`
		RemainingBalance float64 `json:"remainingBalance"`
	} `json:"data"`
}

// TransferRecord 转账记录
type TransferRecord struct {
	Balance    float64 `json:"balance"`
	CreateDate string  `json:"createDate"`
	TsCode     string  `json:"tsCode"`
	UserId     string  `json:"userId"`
	Status     string  `json:"status"`
}

// CheckTransferResponse 转账记录查询响应
type CheckTransferResponse struct {
	Success bool                      `json:"success"`
	Message string                    `json:"message"`
	Data    map[string]TransferRecord `json:"data"`
}

// GameRecord 游戏记录
type GameRecord struct {
	GameId     string  `json:"gameId"`
	GameName   string  `json:"gameName"`
	BetAmount  float64 `json:"betAmount"`
	WinAmount  float64 `json:"winAmount"`
	CreateTime string  `json:"createTime"`
}

// GameRecordResponse 游戏记录响应
type GameRecordResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Data    []GameRecord `json:"data"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Url string `json:"url"`
	} `json:"data"`
}

// NewBalanceResponse 新余额响应
type NewBalanceResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Balance float64 `json:"balance"`
	} `json:"data"`
}

// QueryDomainResponse 域名查询响应
type QueryDomainResponse struct {
	Status         string   `json:"status"`
	PrivateDomains []Domain `json:"privateDomains"`
	Domains        []Domain `json:"domains"`
}

// GetKeyResponse getKey接口响应
type GetKeyResponse struct {
	Status string `json:"status"`
	Key    string `json:"key"`
}

// TransferCheckRequest 转账记录查询请求
type TransferCheckRequest struct {
	UserId string `json:"userId"`
	TsCode string `json:"tsCode"`
}

// TransferCheckResponse 转账记录查询响应
type TransferCheckResponse struct {
	Status string                     `json:"status"`
	Result map[string]TransferDetails `json:"result"`
}

// TransferDetails 转账详情
type TransferDetails struct {
	Balance    float64 `json:"balance"`
	CreateDate string  `json:"createdate"`
	TsCode     string  `json:"tsCode"`
	UserId     string  `json:"userId"`
	Status     string  `json:"status"`
}

// 9Wickets游戏登录
func (c *ThirdController) wickets_login(ctx *abugo.AbuHttpContent) {
	logs.Info("wickets_login")

	// 初始化错误码
	errcode := 0

	// 获取用户token
	token := server.GetToken(ctx)
	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 6)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	// 验证用户
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	user, _ := server.Db().Table("x_user").Select("State").Where(where).GetOne()
	if user == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	state := abugo.GetInt64FromInterface((*user)["State"])
	if state != 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	// 同步余额
	c.sync_amountex(token.UserId, TRANSFER_PLATFORM_WICKETS)

	// 获取可用域名列表
	domains, err := c.wickets_query_domains()
	if err != nil {
		logs.Error("wickets_login query domains failed:", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("获取域名失败: %v", err))
		return
	}

	// 获取登录key
	var key string
	var lastError error
	for _, domain := range domains {
		key, err = c.wickets_get_key(domain, token.UserId)
		if err == nil {
			break
		}
		lastError = err
		logs.Warning("wickets_login get key failed for domain", domain, ":", err)
	}

	if key == "" {
		logs.Error("wickets_login get key failed for all domains:", lastError)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("获取登录key失败: %v", lastError))
		return
	}

	// 构造登录参数
	params := url.Values{}
	params.Add("userId", fmt.Sprintf("%d", token.UserId))
	params.Add("key", key)
	params.Add("eventType", "1") // 默认显示足球页面

	// 构造登录URL
	loginPath := fmt.Sprintf("/apiWallet/player/%s/login", c.wickets_website)
	loginURL := fmt.Sprintf("https://%s.%s%s?%s", c.wickets_exchange_host, domains[0], loginPath, params.Encode())

	// 发送GET请求验证登录
	client := &http.Client{}

	if c.wickets_proxy != "" {
		proxyURLWithAuth := fmt.Sprintf("http://%s:%s@%s", c.wickets_proxy_user, c.wickets_proxy_pass, c.wickets_proxy)
		//logs.Info("Setting proxy URL with authentication:", proxyURLWithAuth)
		proxyURL, err := url.Parse(proxyURLWithAuth)
		if err != nil {
			logs.Error("wickets_login parse proxy url failed:", err)
			ctx.RespErrString(true, &errcode, fmt.Sprintf("代理设置失败: %v", err))
			return
		}
		client.Transport = &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}
	}

	resp, err := client.Get(loginURL)
	if err != nil {
		logs.Error("wickets_login request failed:", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("登录请求失败: %v", err))
		return
	}
	defer resp.Body.Close()

	// 打印响应状态码和数据
	logs.Info("wickets_login  loginURL:", resp.StatusCode, " ", loginURL)
	//body, _ := io.ReadAll(resp.Body)
	//logs.Info("wickets_login response body:", string(body))

	// 获取重定向URL
	//location := resp.Header.Get("Location")
	//logs.Info("wickets_login redirect location:", location)

	if resp.StatusCode != 200 {
		logs.Error("wickets_login request failed:", err)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("登录请求失败: %v", err))
		return
	}
	// 登录成功后执行转账
	err = c.third_transfer_out(TRANSFER_PLATFORM_WICKETS, token.UserId, "")
	if err != nil {
		ctx.RespErrString(true, &errcode, "进入失败 "+err.Error())
		return
	}

	// 返回登录URL
	ctx.Put("url", loginURL)
	ctx.RespOK()
}

// wickets_http_post 通过域名轮询发送HTTP POST请求
func (c *ThirdController) wickets_http_post(path string, data interface{}) (*req.Resp, error) {
	// 获取活跃域名列表
	domains, err := c.wickets_query_domains()
	if err != nil {
		return nil, fmt.Errorf("failed to get active domains: %v", err)
	}

	// 遍历域名列表尝试请求
	var lastError error
	for _, domain := range domains {
		resp, err := c.wickets_http_post_direct(domain, path, data)
		if err == nil {
			return resp, nil
		}
		lastError = err
		logs.Warning("wickets_http_post failed for domain", domain, ":", err)
	}

	return nil, fmt.Errorf("all domains failed: %v", lastError)
}

// wickets_http_post_direct 直接发送HTTP POST请求到指定域名
func (c *ThirdController) wickets_http_post_direct(domain string, path string, data interface{}) (*req.Resp, error) {
	//logs.Info("wickets_http_post_direct 域名:", domain, " 路径:", path, " 数据:", data)
	client := req.New()

	// 如果设置了代理，使用代理
	if len(c.wickets_proxy) > 0 {
		proxyURLWithAuth := fmt.Sprintf("http://%s:%s@%s", c.wickets_proxy_user, c.wickets_proxy_pass, c.wickets_proxy)
		//logs.Info("Setting proxy URL with authentication:", proxyURLWithAuth)
		client.SetProxyUrl(proxyURLWithAuth)
	}

	// 设置请求头
	headers := req.Header{
		"Accept":       "application/json",
		"Content-Type": "application/x-www-form-urlencoded",
	}

	// 构建完整的URL
	var fullURL string
	if strings.Contains(domain, "://") {
		fullURL = domain + path
	} else {
		fullURL = fmt.Sprintf("https://%s.%s%s", c.wickets_api_server_host, domain, path)
	}

	// 将请求参数转换为 JSON 并输出日志
	//reqJson, _ := json.Marshal(data)
	logs.Info("wickets_post 请求地址:", fullURL)
	logs.Info("wickets_post 请求参数:", data)
	//logs.Info("wickets_http_post_direct 请求头: %v", headers)

	// 将数据转换为表单格式
	formData := req.Param{}
	if mapData, ok := data.(map[string]interface{}); ok {
		for key, value := range mapData {
			formData[key] = fmt.Sprintf("%v", value)
		}
	}

	resp, err := client.Post(fullURL, headers, formData)
	if err != nil {
		return nil, fmt.Errorf("post请求失败: %v", err)
	}

	return resp, nil
}

// 查询可用域名列表
func (c *ThirdController) wickets_query_domains() ([]string, error) {
	// 构造请求数据
	reqData := map[string]interface{}{
		"cert": c.wickets_api_key,
	}

	// 使用私有域名查询可用域名
	resp, err := c.wickets_http_post_direct(
		c.wickets_private_domain,
		fmt.Sprintf("/api/apiWallet/%s/queryDomain", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_query_domains request failed:", err)
		return nil, fmt.Errorf("查询域名失败: %v", err)
	}

	// 解析响应
	var response QueryDomainResponse
	if err := resp.ToJSON(&response); err != nil {
		logs.Error("wickets_query_domains unmarshal error:", err)
		return nil, fmt.Errorf("解析域名响应失败: %v", err)
	}

	// 检查响应状态
	if response.Status != "1" {
		return nil, fmt.Errorf("查询域名失败: status=%s", response.Status)
	}

	// 按优先级排序并返回域名列表
	var domains []string
	if len(response.Domains) > 0 {
		// 按优先级排序
		sort.Slice(response.Domains, func(i, j int) bool {
			return response.Domains[i].Priority < response.Domains[j].Priority
		})

		// 提取域名
		for _, d := range response.Domains {
			domains = append(domains, d.Domain)
		}
	}

	if len(domains) == 0 {
		return nil, errors.New("没有可用的域名")
	}

	return domains, nil
}

// 获取9Wickets余额
func (c *ThirdController) wickets_get_balance(userId int) (float64, error) {
	//logs.Info("wickets_get_balance userId:", userId)

	// 构造请求参数
	reqData := map[string]interface{}{
		"cert":    c.wickets_api_key,
		"alluser": 0, // 只查询指定用户
		"userIds": fmt.Sprintf("%d", userId),
	}

	// 发送请求
	resp, err := c.wickets_http_post(
		fmt.Sprintf("/api/apiWallet/%s/getBalance", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_get_balance request failed:", err)
		return 0, fmt.Errorf("余额查询请求失败: %v", err)
	}

	// 解析响应
	var response struct {
		Status    string `json:"status"`
		Count     int    `json:"count"`
		QueryTime string `json:"querytime"`
		Results   []struct {
			UserId   string  `json:"userId"`
			Balance  float64 `json:"balance"`
			Exposure float64 `json:"exposure"`
		} `json:"results"`
	}

	if err := json.NewDecoder(resp.Response().Body).Decode(&response); err != nil {
		logs.Error("wickets_get_balance parse response failed:", err)
		return 0, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查状态
	if response.Status != "1" {
		logs.Error("wickets_get_balance failed with status:", response.Status)
		return 0, fmt.Errorf("余额查询失败，状态码: %s", response.Status)
	}

	// 检查结果是否为空
	if len(response.Results) == 0 {
		logs.Error("wickets_get_balance no results found")
		return 0, fmt.Errorf("未找到用户余额信息")
	}

	// 返回第一个结果的余额
	return response.Results[0].Balance, nil
}

// 转账到9Wickets(存款)，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *ThirdController) wickets_transfer_deposit(userId int, amount float64, tsCode string) (int, error) {
	logs.Info("wickets_deposit(转出) 参数 userId:", userId, " amount:", amount)

	// 参数验证
	if amount <= 0 {
		return TRANSFER_STATUS_FAILED, errors.New("金额必须大于0") // 失败
	}

	// 构造请求参数
	reqData := map[string]interface{}{
		"cert":    c.wickets_api_key,
		"userId":  fmt.Sprintf("%d", userId),
		"balance": amount,
		"tsCode":  tsCode,
	}

	// 发送存款请求
	resp, err := c.wickets_http_post(
		fmt.Sprintf("/api/apiWallet/%s/deposit", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_transfer_deposit request failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("存款请求失败: %v", err) // 网络错误或其它错误
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Response().Body).Decode(&result); err != nil {
		logs.Error("wickets_transfer_deposit parse response failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("解析响应失败: %v", err) // 网络错误或其它错误
	}

	status := result["status"].(string)
	logs.Info("transfer_deposit(转出) 响应内容: %v", result)
	if status != "1" {
		// 如果状态为0，需要使用checkTransferRecord检查操作是否成功
		if status == "0" {
			// 等待一段时间后检查转账记录
			time.Sleep(2 * time.Second)
			checkStatus, err := c.checkTransferRecord(userId, tsCode)
			if err != nil {
				logs.Error("wickets_transfer_deposit check transfer failed:", err)
				return checkStatus, fmt.Errorf("存款失败: %v", err)
			}
			return TRANSFER_STATUS_SUCCESS, nil // 成功
		}
		logs.Error("wickets_transfer_deposit failed with status:", status)
		return TRANSFER_STATUS_FAILED, fmt.Errorf("存款失败，状态码: %s", status) // 失败
	}

	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

// 从9Wickets转出(取款)，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *ThirdController) wickets_transfer_withdraw(userId int, amount float64, tsCode string) (int, error) {
	logs.Info("wickets_withdraw(转入) 参数 userId:", userId, " amount:", amount)

	// 参数验证
	if amount <= 0 {
		return TRANSFER_STATUS_FAILED, errors.New("金额必须大于0") // 失败
	}

	// 构造请求参数
	reqData := map[string]interface{}{
		"cert":         c.wickets_api_key,
		"userId":       fmt.Sprintf("%d", userId),
		"withdrawtype": 1, // 指定金额取款 1 全部额
		"balance":      amount,
		"tsCode":       tsCode,
	}

	// 发送取款请求
	resp, err := c.wickets_http_post(
		fmt.Sprintf("/api/apiWallet/%s/withdraw", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_transfer_withdraw request failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("取款请求失败: %v", err) // 网络错误或其它错误
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Response().Body).Decode(&result); err != nil {
		logs.Error("wickets_transfer_withdraw parse response failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("解析响应失败: %v", err) // 网络错误或其它错误
	}
	logs.Info("wickets_withdraw(转入) 响应内容: %v", result)
	status := result["status"].(string)
	if status != "1" {
		// 如果状态为0，需要使用checkTransferRecord检查操作是否成功
		if status == "0" {
			// 等待一段时间后检查转账记录
			time.Sleep(2 * time.Second)
			checkStatus, err := c.checkTransferRecord(userId, tsCode)
			if err != nil {
				logs.Error("wickets_transfer_withdraw check transfer failed:", err)
				return checkStatus, fmt.Errorf("取款失败: %v", err)
			}
			return TRANSFER_STATUS_SUCCESS, nil // 成功
		}
		logs.Error("wickets_transfer_withdraw failed with status:", status, result)
		return TRANSFER_STATUS_FAILED, fmt.Errorf("取款失败，状态码: %s", status) // 失败
	}

	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

// wickets_get_key 获取登录key
func (c *ThirdController) wickets_get_key(domain string, userId int) (string, error) {
	// 构造请求数据
	reqData := map[string]interface{}{
		"cert":     c.wickets_api_key,
		"userId":   fmt.Sprintf("%d", userId),
		"userName": fmt.Sprintf("%d", userId),
		"agent":    c.wickets_agent,
		"currency": c.wickets_currency_code,
		"timeZone": 21, // ASIA/BANGKOK(GMT+7:00)
	}

	// 发送请求
	resp, err := c.wickets_http_post_direct(
		domain,
		fmt.Sprintf("/api/apiWallet/%s/getKey", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_get_key request failed:", err)
		return "", fmt.Errorf("获取key失败: %v", err)
	}

	// 解析响应
	var response GetKeyResponse
	if err := resp.ToJSON(&response); err != nil {
		logs.Error("wickets_get_key unmarshal error:", err)
		return "", fmt.Errorf("解析key响应失败: %v", err)
	}

	// 检查响应状态
	if response.Status != "1" {
		return "", fmt.Errorf("获取key失败: status=%s", response.Status)
	}

	return response.Key, nil
}

// checkTransferRecord 检查转账订单状态，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *ThirdController) checkTransferRecord(userId int, orderId string) (int, error) {
	logs.Info("wickets_transfer_check userId:", userId, " orderId:", orderId)

	// 构造请求数据
	request := []TransferCheckRequest{
		{
			UserId: fmt.Sprintf("%d", userId),
			TsCode: orderId,
		},
	}

	requestJSON, err := json.Marshal(request)
	if err != nil {
		logs.Error("wickets_transfer_check marshal error:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("failed to marshal request: %v", err) // 网络错误或其它错误
	}

	reqData := map[string]interface{}{
		"cert":    c.wickets_api_key,
		"tsCodes": string(requestJSON),
	}

	// 发送请求
	resp, err := c.wickets_http_post(
		fmt.Sprintf("/api/apiWallet/%s/checkTransferRecord", c.wickets_website),
		reqData,
	)
	if err != nil {
		logs.Error("wickets_transfer_check request failed:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("transfer check request failed: %v", err) // 网络错误或其它错误
	}

	// 检查响应状态码
	if resp.Response().StatusCode != 200 {
		logs.Error("wickets_transfer_check invalid status:", resp.Response().StatusCode)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("invalid response status: %d", resp.Response().StatusCode) // 网络错误或其它错误
	}

	// 解析响应
	var response struct {
		Success bool   `json:"success"`
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    map[string]struct {
			Status     string  `json:"status"`
			Balance    float64 `json:"balance"`
			CreateDate string  `json:"createDate"`
			TsCode     string  `json:"tsCode"`
			UserId     string  `json:"userId"`
		} `json:"data"`
	}
	if err := resp.ToJSON(&response); err != nil {
		logs.Error("wickets_transfer_check unmarshal error:", err)
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("failed to parse response: %v", err) // 网络错误或其它错误
	}

	// 检查响应状态
	if !response.Success || response.Code != 1 {
		return TRANSFER_STATUS_FAILED, fmt.Errorf("transfer check failed: %s", response.Message) // 失败
	}

	// 获取订单状态
	details, exists := response.Data[orderId]
	if !exists {
		logs.Error("wickets_transfer_check order not found")
		return TRANSFER_STATUS_FAILED, fmt.Errorf("order not found") // 失败
	}

	// 检查订单状态
	// 根据API文档，status=1表示成功
	if details.Status != "1" {
		// 如果状态为0，可能表示处理中
		if details.Status == "0" {
			logs.Warning("wickets_transfer_check order status processing:", details.Status)
			return TRANSFER_STATUS_PROCESSING, fmt.Errorf("order status processing: %s", details.Status) // 处理中
		}
		logs.Error("wickets_transfer_check order status not success:", details.Status)
		return TRANSFER_STATUS_FAILED, fmt.Errorf("order status not success: %s", details.Status) // 失败
	}

	return TRANSFER_STATUS_SUCCESS, nil // 成功
}
