// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOnlinePlayUser = "x_online_play_user"

// XOnlinePlayUser mapped from table <x_online_play_user>
type XOnlinePlayUser struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Online     int32     `gorm:"column:Online;not null;comment:在线人数" json:"Online"`                                   // 在线人数
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"CreateTime"` // 记录时间
}

// TableName XOnlinePlayUser's table name
func (*XOnlinePlayUser) TableName() string {
	return TableNameXOnlinePlayUser
}
