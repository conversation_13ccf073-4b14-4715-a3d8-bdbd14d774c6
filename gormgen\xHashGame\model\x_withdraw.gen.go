// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXWithdraw = "x_withdraw"

// XWithdraw 提现列表
type XWithdraw struct {
	ID                     int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`                          // id
	UserID                 int32     `gorm:"column:UserId;comment:用户" json:"UserId"`                                                // 用户
	SellerID               int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                           // 运营商
	ChannelID              int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                                          // 渠道
	BetChannelID           int32     `gorm:"column:BetChannelId;comment:投注渠道" json:"BetChannelId"`                                  // 投注渠道
	OrderType              int32     `gorm:"column:OrderType;not null;default:31;comment:提款订单分类 31提款 32上庄提款" json:"OrderType"`      // 提款订单分类 31提款 32上庄提款
	State                  int32     `gorm:"column:State;comment:状态 0 待审核 1审核拒绝 2审核通过  8拒绝发放 4已发放 5正在出款 6出款完成  7失败退回" json:"State"` // 状态 0 待审核 1审核拒绝 2审核通过  8拒绝发放 4已发放 5正在出款 6出款完成  7失败退回
	Symbol                 string    `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                                // 币种
	Net                    string    `gorm:"column:Net;comment:区块链网络" json:"Net"`                                                   // 区块链网络
	RepeatCheck            int32     `gorm:"column:RepeatCheck;default:2;comment:重复性检测(ip、设备、密码) 1通过 2未通过" json:"RepeatCheck"`      // 重复性检测(ip、设备、密码) 1通过 2未通过
	TotalWinLost           float64   `gorm:"column:TotalWinLost;default:0.000000;comment:输赢总额" json:"TotalWinLost"`                 // 输赢总额
	WithdrawLiuSui         float64   `gorm:"column:WithdrawLiuSui;default:0.000000;comment:提现流水" json:"WithdrawLiuSui"`             // 提现流水
	TotalLiuSui            float64   `gorm:"column:TotalLiuSui;default:0.000000;comment:当前流水" json:"TotalLiuSui"`                   // 当前流水
	WithwardNeedLiuSui     int32     `gorm:"column:WithwardNeedLiuSui;default:1;comment:提现是否需要流水,1是,2否" json:"WithwardNeedLiuSui"`  // 提现是否需要流水,1是,2否
	TotalWithdrawLiuSui    float64   `gorm:"column:TotalWithdrawLiuSui" json:"TotalWithdrawLiuSui"`
	OrderCheck             int32     `gorm:"column:OrderCheck;default:2;comment:注单检测 1通过 2未通过" json:"OrderCheck"`          // 注单检测 1通过 2未通过
	Address                string    `gorm:"column:Address;comment:提现地址" json:"Address"`                                   // 提现地址
	AddressState           int32     `gorm:"column:AddressState;default:2;comment:状态 1已验证 2未验证" json:"AddressState"`       // 状态 1已验证 2未验证
	Amount                 float64   `gorm:"column:Amount;comment:提现金额" json:"Amount"`                                     // 提现金额
	OrderFee               float64   `gorm:"column:OrderFee;not null;default:0.000000;comment:单日免手续费提款次数" json:"OrderFee"` // 单日免手续费提款次数
	AfterAmount            float64   `gorm:"column:AfterAmount;comment:提款后用户余额" json:"AfterAmount"`                        // 提款后用户余额
	Txid                   string    `gorm:"column:Txid" json:"Txid"`
	AuditAccount           string    `gorm:"column:AuditAccount;comment:审核人" json:"AuditAccount"`                          // 审核人
	AduitTime              time.Time `gorm:"column:AduitTime;comment:审核时间" json:"AduitTime"`                               // 审核时间
	AduitMemo              string    `gorm:"column:AduitMemo;comment:审核备注" json:"AduitMemo"`                               // 审核备注
	CreateTime             time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:提现时间" json:"CreateTime"`   // 提现时间
	Account                string    `gorm:"column:Account;comment:玩家账号" json:"Account"`                                   // 玩家账号
	Fee                    float64   `gorm:"column:Fee;comment:手续费" json:"Fee"`                                            // 手续费
	HbcOrder               string    `gorm:"column:HbcOrder;comment:hbc订单号" json:"HbcOrder"`                               // hbc订单号
	HbcState               int32     `gorm:"column:HbcState;comment:hbc状态" json:"HbcState"`                                // hbc状态
	HbcFinishTime          time.Time `gorm:"column:HbcFinishTime;comment:hbc提现完成时间" json:"HbcFinishTime"`                  // hbc提现完成时间
	Memo                   string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                           // 备注
	TopAgentID             int32     `gorm:"column:TopAgentId;comment:顶级id" json:"TopAgentId"`                             // 顶级id
	SendAccount            string    `gorm:"column:SendAccount;comment:发放账号" json:"SendAccount"`                           // 发放账号
	SendTime               time.Time `gorm:"column:SendTime;comment:发放时间" json:"SendTime"`                                 // 发放时间
	LockState              int32     `gorm:"column:LockState;default:2;comment:锁定状态 1锁定 2解锁" json:"LockState"`             // 锁定状态 1锁定 2解锁
	LockUserAccount        string    `gorm:"column:LockUserAccount;comment:锁定账号" json:"LockUserAccount"`                   // 锁定账号
	LockUserID             int32     `gorm:"column:LockUserId;comment:锁定账号Id" json:"LockUserId"`                           // 锁定账号Id
	LockTime               time.Time `gorm:"column:LockTime;comment:锁定时间" json:"LockTime"`                                 // 锁定时间
	HbcMemo                string    `gorm:"column:HbcMemo;comment:hbc备注" json:"HbcMemo"`                                  // hbc备注
	NetFee                 float64   `gorm:"column:NetFee;comment:链上费用" json:"NetFee"`                                     // 链上费用
	CSGroup                string    `gorm:"column:CSGroup;comment:客服组" json:"CSGroup"`                                    // 客服组
	CSID                   string    `gorm:"column:CSId;comment:客服id" json:"CSId"`                                         // 客服id
	SpecialAgent           int32     `gorm:"column:SpecialAgent;comment:是否独立代理 1是,2否" json:"SpecialAgent"`                 // 是否独立代理 1是,2否
	PayType                int32     `gorm:"column:PayType;default:1;comment:支付类型 1链上提款 2.pix" json:"PayType"`             // 支付类型 1链上提款 2.pix
	PayID                  int32     `gorm:"column:PayId;comment:支付id" json:"PayId"`                                       // 支付id
	PayData                string    `gorm:"column:PayData;comment:提现额外数据" json:"PayData"`                                 // 提现额外数据
	TransferRate           float64   `gorm:"column:TransferRate;comment:汇率" json:"TransferRate"`                           // 汇率
	RealAmount             float64   `gorm:"column:RealAmount;comment:到账金额" json:"RealAmount"`                             // 到账金额
	PayDataEx              string    `gorm:"column:PayDataEx;comment:提现额外数据" json:"PayDataEx"`                             // 提现额外数据
	ThirdID                string    `gorm:"column:ThirdId;comment:三方订单id" json:"ThirdId"`                                 // 三方订单id
	IsFirst                int32     `gorm:"column:IsFirst;default:2;comment:是否首次提款 1是 2否" json:"IsFirst"`                 // 是否首次提款 1是 2否
	WithdrawCount          int32     `gorm:"column:WithdrawCount;comment:提款申请数" json:"WithdrawCount"`                      // 提款申请数
	WithdrawSuccessCount   int32     `gorm:"column:WithdrawSuccessCount;comment:提款成功数" json:"WithdrawSuccessCount"`        // 提款成功数
	LastWithdrawTime       time.Time `gorm:"column:LastWithdrawTime;comment:上次提款申请时间" json:"LastWithdrawTime"`             // 上次提款申请时间
	LastWithdrawFinishTime time.Time `gorm:"column:LastWithdrawFinishTime;comment:上次提款申请时间" json:"LastWithdrawFinishTime"` // 上次提款申请时间
	IsTgCounted            int32     `gorm:"column:IsTgCounted;comment:是否tg统计(0未统计 1已统计)" json:"IsTgCounted"`              // 是否tg统计(0未统计 1已统计)
	FeeFreeCountPerDay     int32     `gorm:"column:FeeFreeCountPerDay;comment:单日免手续费提款次数" json:"FeeFreeCountPerDay"`       // 单日免手续费提款次数
	FeePercent             float64   `gorm:"column:FeePercent;comment:超出后手续费%" json:"FeePercent"`                          // 超出后手续费%
	TodayWithdrawCount     int32     `gorm:"column:TodayWithdrawCount;comment:当日提现次数" json:"TodayWithdrawCount"`           // 当日提现次数
}

// TableName XWithdraw's table name
func (*XWithdraw) TableName() string {
	return TableNameXWithdraw
}
