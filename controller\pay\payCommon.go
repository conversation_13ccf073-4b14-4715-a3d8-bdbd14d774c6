package paycontroller

import (
	"crypto/md5"
	"encoding/hex"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
	"time"
)

// zark 鑫旺支付

const (
	OrderExist        = "订单已存在"
	OrderNotExist     = "订单不存在"
	OrderUpdateError  = "订单更新失败"
	DBError           = "数据库错误"
	PayIdError        = "支付ID错误"
	PayClosedError    = "该支付已关闭"
	PayChannelError   = "渠道编码错误"
	AmountError       = "额度不符合"
	PayConfigError    = "支付配置错误"
	RequestError      = "请求失败"
	Success           = "success"
	Fail              = "失败"
	Processing        = "处理中"
	JsonError         = "json解析错误"
	SignError         = "验签失败"
	AmountConfigError = "额度配置错误"
	MerIDError        = "商户ID错误"
	MerNotExist       = "商户不存在"
	MerDel            = "该商户已删除"
	IDError           = "ID错误"
	IDNotExist        = "ID不存在"
	ResultParamError  = "请求结果参数错误"
	ResultError       = "请求结果错误"
)

//1. 签名加密: MD5
//
//2. 签名过程: :原始业务参数序列化为 json 字符串,将 json 字符串+商户号+ Key 使用 MD5 加密,再将
//加密结果转成小写,具体公式如下: lower(md5(json+商户号+KEY))
//
//3. 请求方式: POST , 在请求时，将 Authorization =商户号 和 Sign= lower(md5(json+商户号
//
//+KEY)) 放 入 http 请求 headers 内，再将 json 放入输入流 post 提交到平台
//
//4. 请求媒体类型（ JSON 数据格式 ） Content-Type: application/json

func HttpBody(url, method, param string, header map[string]string) (int, []byte) {

	state := 100
	par := strings.NewReader(param)
	req, err := http.NewRequest(method, url, par)

	if err != nil {

		return state, nil
	}

	if req.Body != nil {
		defer req.Body.Close()
	}

	if len(header) < 1 {
		header["Content-Type"] = "text/plain; charset=UTF8"
		header["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.80 Safari/537.36"
		header["Accept"] = "application/json, text/plain, */*"
	}
	for k, v := range header {
		req.Header.Set(k, v)
	}

	tr := &http.Transport{DisableKeepAlives: true,
		Dial: func(netw, addr string) (net.Conn, error) {
			c, err := net.DialTimeout(netw, addr, time.Second*30) //设置建立连接超时
			if err != nil {
				return nil, err
			}
			c.SetDeadline(time.Now().Add(5 * time.Second)) //设置发送接收数据超时
			return c, nil
		}}
	client := &http.Client{Transport: tr}

	//处理返回结果
	resp, err := client.Do(req)
	if err != nil {
		return state, nil
	}
	if resp != nil && resp.Body != nil {
		defer resp.Body.Close()
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return state, nil
	}

	state = resp.StatusCode

	return state, body
}

func HexMd5(str string) string {
	m := md5.New()
	m.Write([]byte(str))
	c := m.Sum(nil)
	return hex.EncodeToString(c)
}

type BasicPay struct {
	Md5Key     string
	PublicKey  string
	PrivateKey string

	Company  string
	Merchant string
	Channel  string

	GatewayUrl string
	Base
}
