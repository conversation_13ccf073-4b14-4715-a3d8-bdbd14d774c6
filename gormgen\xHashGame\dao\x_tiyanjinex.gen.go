// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXTiyanjinex(db *gorm.DB, opts ...gen.DOOption) xTiyanjinex {
	_xTiyanjinex := xTiyanjinex{}

	_xTiyanjinex.xTiyanjinexDo.UseDB(db, opts...)
	_xTiyanjinex.xTiyanjinexDo.UseModel(&model.XTiyanjinex{})

	tableName := _xTiyanjinex.xTiyanjinexDo.TableName()
	_xTiyanjinex.ALL = field.NewAsterisk(tableName)
	_xTiyanjinex.ID = field.NewInt32(tableName, "Id")
	_xTiyanjinex.State = field.NewInt32(tableName, "State")
	_xTiyanjinex.CreateTime = field.NewTime(tableName, "CreateTime")

	_xTiyanjinex.fillFieldMap()

	return _xTiyanjinex
}

type xTiyanjinex struct {
	xTiyanjinexDo xTiyanjinexDo

	ALL        field.Asterisk
	ID         field.Int32
	State      field.Int32 // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
	CreateTime field.Time

	fieldMap map[string]field.Expr
}

func (x xTiyanjinex) Table(newTableName string) *xTiyanjinex {
	x.xTiyanjinexDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xTiyanjinex) As(alias string) *xTiyanjinex {
	x.xTiyanjinexDo.DO = *(x.xTiyanjinexDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xTiyanjinex) updateTableName(table string) *xTiyanjinex {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.State = field.NewInt32(table, "State")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xTiyanjinex) WithContext(ctx context.Context) *xTiyanjinexDo {
	return x.xTiyanjinexDo.WithContext(ctx)
}

func (x xTiyanjinex) TableName() string { return x.xTiyanjinexDo.TableName() }

func (x xTiyanjinex) Alias() string { return x.xTiyanjinexDo.Alias() }

func (x xTiyanjinex) Columns(cols ...field.Expr) gen.Columns { return x.xTiyanjinexDo.Columns(cols...) }

func (x *xTiyanjinex) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xTiyanjinex) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 3)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["State"] = x.State
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xTiyanjinex) clone(db *gorm.DB) xTiyanjinex {
	x.xTiyanjinexDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xTiyanjinex) replaceDB(db *gorm.DB) xTiyanjinex {
	x.xTiyanjinexDo.ReplaceDB(db)
	return x
}

type xTiyanjinexDo struct{ gen.DO }

func (x xTiyanjinexDo) Debug() *xTiyanjinexDo {
	return x.withDO(x.DO.Debug())
}

func (x xTiyanjinexDo) WithContext(ctx context.Context) *xTiyanjinexDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xTiyanjinexDo) ReadDB() *xTiyanjinexDo {
	return x.Clauses(dbresolver.Read)
}

func (x xTiyanjinexDo) WriteDB() *xTiyanjinexDo {
	return x.Clauses(dbresolver.Write)
}

func (x xTiyanjinexDo) Session(config *gorm.Session) *xTiyanjinexDo {
	return x.withDO(x.DO.Session(config))
}

func (x xTiyanjinexDo) Clauses(conds ...clause.Expression) *xTiyanjinexDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xTiyanjinexDo) Returning(value interface{}, columns ...string) *xTiyanjinexDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xTiyanjinexDo) Not(conds ...gen.Condition) *xTiyanjinexDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xTiyanjinexDo) Or(conds ...gen.Condition) *xTiyanjinexDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xTiyanjinexDo) Select(conds ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xTiyanjinexDo) Where(conds ...gen.Condition) *xTiyanjinexDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xTiyanjinexDo) Order(conds ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xTiyanjinexDo) Distinct(cols ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xTiyanjinexDo) Omit(cols ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xTiyanjinexDo) Join(table schema.Tabler, on ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xTiyanjinexDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xTiyanjinexDo) RightJoin(table schema.Tabler, on ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xTiyanjinexDo) Group(cols ...field.Expr) *xTiyanjinexDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xTiyanjinexDo) Having(conds ...gen.Condition) *xTiyanjinexDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xTiyanjinexDo) Limit(limit int) *xTiyanjinexDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xTiyanjinexDo) Offset(offset int) *xTiyanjinexDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xTiyanjinexDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xTiyanjinexDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xTiyanjinexDo) Unscoped() *xTiyanjinexDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xTiyanjinexDo) Create(values ...*model.XTiyanjinex) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xTiyanjinexDo) CreateInBatches(values []*model.XTiyanjinex, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xTiyanjinexDo) Save(values ...*model.XTiyanjinex) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xTiyanjinexDo) First() (*model.XTiyanjinex, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinex), nil
	}
}

func (x xTiyanjinexDo) Take() (*model.XTiyanjinex, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinex), nil
	}
}

func (x xTiyanjinexDo) Last() (*model.XTiyanjinex, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinex), nil
	}
}

func (x xTiyanjinexDo) Find() ([]*model.XTiyanjinex, error) {
	result, err := x.DO.Find()
	return result.([]*model.XTiyanjinex), err
}

func (x xTiyanjinexDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XTiyanjinex, err error) {
	buf := make([]*model.XTiyanjinex, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xTiyanjinexDo) FindInBatches(result *[]*model.XTiyanjinex, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xTiyanjinexDo) Attrs(attrs ...field.AssignExpr) *xTiyanjinexDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xTiyanjinexDo) Assign(attrs ...field.AssignExpr) *xTiyanjinexDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xTiyanjinexDo) Joins(fields ...field.RelationField) *xTiyanjinexDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xTiyanjinexDo) Preload(fields ...field.RelationField) *xTiyanjinexDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xTiyanjinexDo) FirstOrInit() (*model.XTiyanjinex, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinex), nil
	}
}

func (x xTiyanjinexDo) FirstOrCreate() (*model.XTiyanjinex, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XTiyanjinex), nil
	}
}

func (x xTiyanjinexDo) FindByPage(offset int, limit int) (result []*model.XTiyanjinex, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xTiyanjinexDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xTiyanjinexDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xTiyanjinexDo) Delete(models ...*model.XTiyanjinex) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xTiyanjinexDo) withDO(do gen.Dao) *xTiyanjinexDo {
	x.DO = *do.(*gen.DO)
	return x
}
