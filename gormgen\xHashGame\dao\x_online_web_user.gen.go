// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOnlineWebUser(db *gorm.DB, opts ...gen.DOOption) xOnlineWebUser {
	_xOnlineWebUser := xOnlineWebUser{}

	_xOnlineWebUser.xOnlineWebUserDo.UseDB(db, opts...)
	_xOnlineWebUser.xOnlineWebUserDo.UseModel(&model.XOnlineWebUser{})

	tableName := _xOnlineWebUser.xOnlineWebUserDo.TableName()
	_xOnlineWebUser.ALL = field.NewAsterisk(tableName)
	_xOnlineWebUser.ID = field.NewInt32(tableName, "Id")
	_xOnlineWebUser.Online = field.NewInt32(tableName, "Online")
	_xOnlineWebUser.CreateTime = field.NewTime(tableName, "CreateTime")

	_xOnlineWebUser.fillFieldMap()

	return _xOnlineWebUser
}

type xOnlineWebUser struct {
	xOnlineWebUserDo xOnlineWebUserDo

	ALL        field.Asterisk
	ID         field.Int32
	Online     field.Int32 // 在线人数
	CreateTime field.Time  // 记录时间

	fieldMap map[string]field.Expr
}

func (x xOnlineWebUser) Table(newTableName string) *xOnlineWebUser {
	x.xOnlineWebUserDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOnlineWebUser) As(alias string) *xOnlineWebUser {
	x.xOnlineWebUserDo.DO = *(x.xOnlineWebUserDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOnlineWebUser) updateTableName(table string) *xOnlineWebUser {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Online = field.NewInt32(table, "Online")
	x.CreateTime = field.NewTime(table, "CreateTime")

	x.fillFieldMap()

	return x
}

func (x *xOnlineWebUser) WithContext(ctx context.Context) *xOnlineWebUserDo {
	return x.xOnlineWebUserDo.WithContext(ctx)
}

func (x xOnlineWebUser) TableName() string { return x.xOnlineWebUserDo.TableName() }

func (x xOnlineWebUser) Alias() string { return x.xOnlineWebUserDo.Alias() }

func (x xOnlineWebUser) Columns(cols ...field.Expr) gen.Columns {
	return x.xOnlineWebUserDo.Columns(cols...)
}

func (x *xOnlineWebUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOnlineWebUser) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 3)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Online"] = x.Online
	x.fieldMap["CreateTime"] = x.CreateTime
}

func (x xOnlineWebUser) clone(db *gorm.DB) xOnlineWebUser {
	x.xOnlineWebUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOnlineWebUser) replaceDB(db *gorm.DB) xOnlineWebUser {
	x.xOnlineWebUserDo.ReplaceDB(db)
	return x
}

type xOnlineWebUserDo struct{ gen.DO }

func (x xOnlineWebUserDo) Debug() *xOnlineWebUserDo {
	return x.withDO(x.DO.Debug())
}

func (x xOnlineWebUserDo) WithContext(ctx context.Context) *xOnlineWebUserDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOnlineWebUserDo) ReadDB() *xOnlineWebUserDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOnlineWebUserDo) WriteDB() *xOnlineWebUserDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOnlineWebUserDo) Session(config *gorm.Session) *xOnlineWebUserDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOnlineWebUserDo) Clauses(conds ...clause.Expression) *xOnlineWebUserDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOnlineWebUserDo) Returning(value interface{}, columns ...string) *xOnlineWebUserDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOnlineWebUserDo) Not(conds ...gen.Condition) *xOnlineWebUserDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOnlineWebUserDo) Or(conds ...gen.Condition) *xOnlineWebUserDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOnlineWebUserDo) Select(conds ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOnlineWebUserDo) Where(conds ...gen.Condition) *xOnlineWebUserDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOnlineWebUserDo) Order(conds ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOnlineWebUserDo) Distinct(cols ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOnlineWebUserDo) Omit(cols ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOnlineWebUserDo) Join(table schema.Tabler, on ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOnlineWebUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOnlineWebUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOnlineWebUserDo) Group(cols ...field.Expr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOnlineWebUserDo) Having(conds ...gen.Condition) *xOnlineWebUserDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOnlineWebUserDo) Limit(limit int) *xOnlineWebUserDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOnlineWebUserDo) Offset(offset int) *xOnlineWebUserDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOnlineWebUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOnlineWebUserDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOnlineWebUserDo) Unscoped() *xOnlineWebUserDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOnlineWebUserDo) Create(values ...*model.XOnlineWebUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOnlineWebUserDo) CreateInBatches(values []*model.XOnlineWebUser, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOnlineWebUserDo) Save(values ...*model.XOnlineWebUser) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOnlineWebUserDo) First() (*model.XOnlineWebUser, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineWebUser), nil
	}
}

func (x xOnlineWebUserDo) Take() (*model.XOnlineWebUser, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineWebUser), nil
	}
}

func (x xOnlineWebUserDo) Last() (*model.XOnlineWebUser, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineWebUser), nil
	}
}

func (x xOnlineWebUserDo) Find() ([]*model.XOnlineWebUser, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOnlineWebUser), err
}

func (x xOnlineWebUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOnlineWebUser, err error) {
	buf := make([]*model.XOnlineWebUser, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOnlineWebUserDo) FindInBatches(result *[]*model.XOnlineWebUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOnlineWebUserDo) Attrs(attrs ...field.AssignExpr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOnlineWebUserDo) Assign(attrs ...field.AssignExpr) *xOnlineWebUserDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOnlineWebUserDo) Joins(fields ...field.RelationField) *xOnlineWebUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOnlineWebUserDo) Preload(fields ...field.RelationField) *xOnlineWebUserDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOnlineWebUserDo) FirstOrInit() (*model.XOnlineWebUser, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineWebUser), nil
	}
}

func (x xOnlineWebUserDo) FirstOrCreate() (*model.XOnlineWebUser, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOnlineWebUser), nil
	}
}

func (x xOnlineWebUserDo) FindByPage(offset int, limit int) (result []*model.XOnlineWebUser, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOnlineWebUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOnlineWebUserDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOnlineWebUserDo) Delete(models ...*model.XOnlineWebUser) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOnlineWebUserDo) withDO(do gen.Dao) *xOnlineWebUserDo {
	x.DO = *do.(*gen.DO)
	return x
}
