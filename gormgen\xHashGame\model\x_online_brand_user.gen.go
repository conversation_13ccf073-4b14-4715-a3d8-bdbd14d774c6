// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOnlineBrandUser = "x_online_brand_user"

// XOnlineBrandUser mapped from table <x_online_brand_user>
type XOnlineBrandUser struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	Online     int32     `gorm:"column:Online;not null;comment:在线人数" json:"Online"`                                   // 在线人数
	Brand      string    `gorm:"column:Brand;not null;comment:厂商名称" json:"Brand"`                                     // 厂商名称
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:记录时间" json:"CreateTime"` // 记录时间
}

// TableName XOnlineBrandUser's table name
func (*XOnlineBrandUser) TableName() string {
	return TableNameXOnlineBrandUser
}
