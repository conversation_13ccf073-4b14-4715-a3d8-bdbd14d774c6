// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotMissionReward(db *gorm.DB, opts ...gen.DOOption) xRobotMissionReward {
	_xRobotMissionReward := xRobotMissionReward{}

	_xRobotMissionReward.xRobotMissionRewardDo.UseDB(db, opts...)
	_xRobotMissionReward.xRobotMissionRewardDo.UseModel(&model.XRobotMissionReward{})

	tableName := _xRobotMissionReward.xRobotMissionRewardDo.TableName()
	_xRobotMissionReward.ALL = field.NewAsterisk(tableName)
	_xRobotMissionReward.ID = field.NewInt64(tableName, "id")
	_xRobotMissionReward.UserID = field.NewInt64(tableName, "user_id")
	_xRobotMissionReward.Currency = field.NewString(tableName, "currency")
	_xRobotMissionReward.Amount = field.NewInt32(tableName, "amount")
	_xRobotMissionReward.Stat = field.NewInt32(tableName, "stat")
	_xRobotMissionReward.Remark = field.NewString(tableName, "remark")
	_xRobotMissionReward.IsDel = field.NewInt32(tableName, "is_del")
	_xRobotMissionReward.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotMissionReward.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotMissionReward.fillFieldMap()

	return _xRobotMissionReward
}

// xRobotMissionReward 活动货币领取表记录
type xRobotMissionReward struct {
	xRobotMissionRewardDo xRobotMissionRewardDo

	ALL        field.Asterisk
	ID         field.Int64  // pk
	UserID     field.Int64  // 用户ID
	Currency   field.String // 货币
	Amount     field.Int32  // 数量
	Stat       field.Int32  // 活动奖励领取状态0：未知，1已发放
	Remark     field.String // 备注
	IsDel      field.Int32  // 软删除
	CreateTime field.Time   // 创建日期
	UpdateTime field.Time   // 更新日期

	fieldMap map[string]field.Expr
}

func (x xRobotMissionReward) Table(newTableName string) *xRobotMissionReward {
	x.xRobotMissionRewardDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotMissionReward) As(alias string) *xRobotMissionReward {
	x.xRobotMissionRewardDo.DO = *(x.xRobotMissionRewardDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotMissionReward) updateTableName(table string) *xRobotMissionReward {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.UserID = field.NewInt64(table, "user_id")
	x.Currency = field.NewString(table, "currency")
	x.Amount = field.NewInt32(table, "amount")
	x.Stat = field.NewInt32(table, "stat")
	x.Remark = field.NewString(table, "remark")
	x.IsDel = field.NewInt32(table, "is_del")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotMissionReward) WithContext(ctx context.Context) *xRobotMissionRewardDo {
	return x.xRobotMissionRewardDo.WithContext(ctx)
}

func (x xRobotMissionReward) TableName() string { return x.xRobotMissionRewardDo.TableName() }

func (x xRobotMissionReward) Alias() string { return x.xRobotMissionRewardDo.Alias() }

func (x xRobotMissionReward) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotMissionRewardDo.Columns(cols...)
}

func (x *xRobotMissionReward) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotMissionReward) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 9)
	x.fieldMap["id"] = x.ID
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["currency"] = x.Currency
	x.fieldMap["amount"] = x.Amount
	x.fieldMap["stat"] = x.Stat
	x.fieldMap["remark"] = x.Remark
	x.fieldMap["is_del"] = x.IsDel
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotMissionReward) clone(db *gorm.DB) xRobotMissionReward {
	x.xRobotMissionRewardDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotMissionReward) replaceDB(db *gorm.DB) xRobotMissionReward {
	x.xRobotMissionRewardDo.ReplaceDB(db)
	return x
}

type xRobotMissionRewardDo struct{ gen.DO }

func (x xRobotMissionRewardDo) Debug() *xRobotMissionRewardDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotMissionRewardDo) WithContext(ctx context.Context) *xRobotMissionRewardDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotMissionRewardDo) ReadDB() *xRobotMissionRewardDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotMissionRewardDo) WriteDB() *xRobotMissionRewardDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotMissionRewardDo) Session(config *gorm.Session) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotMissionRewardDo) Clauses(conds ...clause.Expression) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotMissionRewardDo) Returning(value interface{}, columns ...string) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotMissionRewardDo) Not(conds ...gen.Condition) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotMissionRewardDo) Or(conds ...gen.Condition) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotMissionRewardDo) Select(conds ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotMissionRewardDo) Where(conds ...gen.Condition) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotMissionRewardDo) Order(conds ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotMissionRewardDo) Distinct(cols ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotMissionRewardDo) Omit(cols ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotMissionRewardDo) Join(table schema.Tabler, on ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotMissionRewardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotMissionRewardDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotMissionRewardDo) Group(cols ...field.Expr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotMissionRewardDo) Having(conds ...gen.Condition) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotMissionRewardDo) Limit(limit int) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotMissionRewardDo) Offset(offset int) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotMissionRewardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotMissionRewardDo) Unscoped() *xRobotMissionRewardDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotMissionRewardDo) Create(values ...*model.XRobotMissionReward) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotMissionRewardDo) CreateInBatches(values []*model.XRobotMissionReward, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotMissionRewardDo) Save(values ...*model.XRobotMissionReward) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotMissionRewardDo) First() (*model.XRobotMissionReward, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionReward), nil
	}
}

func (x xRobotMissionRewardDo) Take() (*model.XRobotMissionReward, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionReward), nil
	}
}

func (x xRobotMissionRewardDo) Last() (*model.XRobotMissionReward, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionReward), nil
	}
}

func (x xRobotMissionRewardDo) Find() ([]*model.XRobotMissionReward, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotMissionReward), err
}

func (x xRobotMissionRewardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotMissionReward, err error) {
	buf := make([]*model.XRobotMissionReward, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotMissionRewardDo) FindInBatches(result *[]*model.XRobotMissionReward, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotMissionRewardDo) Attrs(attrs ...field.AssignExpr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotMissionRewardDo) Assign(attrs ...field.AssignExpr) *xRobotMissionRewardDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotMissionRewardDo) Joins(fields ...field.RelationField) *xRobotMissionRewardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotMissionRewardDo) Preload(fields ...field.RelationField) *xRobotMissionRewardDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotMissionRewardDo) FirstOrInit() (*model.XRobotMissionReward, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionReward), nil
	}
}

func (x xRobotMissionRewardDo) FirstOrCreate() (*model.XRobotMissionReward, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotMissionReward), nil
	}
}

func (x xRobotMissionRewardDo) FindByPage(offset int, limit int) (result []*model.XRobotMissionReward, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotMissionRewardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotMissionRewardDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotMissionRewardDo) Delete(models ...*model.XRobotMissionReward) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotMissionRewardDo) withDO(do gen.Dao) *xRobotMissionRewardDo {
	x.DO = *do.(*gen.DO)
	return x
}
