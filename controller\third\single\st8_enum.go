package single

// Evolution Gaming
const (
	EvolutionGamingBet      = 4000 // Evolution Gaming-下注
	EvolutionGamingSettle   = 4001 // Evolution Gaming-结算
	EvolutionGamingRollback = 4002 // Evolution Gaming-回滚
	EvolutionGamingDeduct   = 4003 // Evolution Gaming-扣款
	EvolutionGamingAdd      = 4004 // Evolution Gaming-加款
)

// Red Tiger
const (
	RedTigerBet      = 4005 // Red Tiger-下注
	RedTigerSettle   = 4006 // Red Tiger-结算
	RedTigerRollback = 4007 // Red Tiger-回滚
	RedTigerDeduct   = 4008 // Red Tiger-扣款
	RedTigerAdd      = 4009 // Red Tiger-加款
)

// NetEnt
const (
	NetEntBet      = 4010 // NetEnt-下注
	NetEntSettle   = 4011 // NetEnt-结算
	NetEntRollback = 4012 // NetEnt-回滚
	NetEntDeduct   = 4013 // NetEnt-扣款
	NetEntAdd      = 4014 // NetEnt-加款
)

// Spribe
const (
	SpribeBet      = 4015 // Spribe-下注
	SpribeSettle   = 4016 // Spribe-结算
	SpribeRollback = 4017 // Spribe-回滚
	SpribeDeduct   = 4018 // Spribe-扣款
	SpribeAdd      = 4019 // Spribe-加款
)

// Ezugi
const (
	EzugiBet      = 4020 // Ezugi-下注
	EzugiSettle   = 4021 // Ezugi-结算
	EzugiRollback = 4022 // Ezugi-回滚
	EzugiDeduct   = 4023 // Ezugi-扣款
	EzugiAdd      = 4024 // Ezugi-加款
)

// Betsolutions
const (
	BetsolutionsBet      = 4025 // Betsolutions-下注
	BetsolutionsSettle   = 4026 // Betsolutions-结算
	BetsolutionsRollback = 4027 // Betsolutions-回滚
	BetsolutionsDeduct   = 4028 // Betsolutions-扣款
	BetsolutionsAdd      = 4029 // Betsolutions-加款
)

// Charismatic
const (
	CharismaticBet      = 4030 // Charismatic-下注
	CharismaticSettle   = 4031 // Charismatic-结算
	CharismaticRollback = 4032 // Charismatic-回滚
	CharismaticDeduct   = 4033 // Charismatic-扣款
	CharismaticAdd      = 4034 // Charismatic-加款
)

// Pragmatic Play
const (
	PragmaticPlayBet      = 4035 // PragmaticPlay-下注
	PragmaticPlaySettle   = 4036 // PragmaticPlay-结算
	PragmaticPlayRollback = 4037 // PragmaticPlay-回滚
	PragmaticPlayDeduct   = 4038 // PragmaticPlay-扣款
	PragmaticPlayAdd      = 4039 // PragmaticPlay-加款
)

// Pragmatic Play Live
const (
	PragmaticPlayLiveBet      = 4040 // PragmaticPlayLive-下注
	PragmaticPlayLiveSettle   = 4041 // PragmaticPlayLive-结算
	PragmaticPlayLiveRollback = 4042 // PragmaticPlayLive-回滚
	PragmaticPlayLiveDeduct   = 4043 // PragmaticPlayLive-扣款
	PragmaticPlayLiveAdd      = 4044 // PragmaticPlayLive-加款
)

// Habanero
const (
	HabaneroBet      = 4045 // Habanero-下注
	HabaneroSettle   = 4046 // Habanero-结算
	HabaneroRollback = 4047 // Habanero-回滚
	HabaneroDeduct   = 4048 // Habanero-扣款
	HabaneroAdd      = 4049 // Habanero-加款
)

// Gamzix
const (
	GamzixBet      = 4050 // Gamzix-下注
	GamzixSettle   = 4051 // Gamzix-结算
	GamzixRollback = 4052 // Gamzix-回滚
	GamzixDeduct   = 4053 // Gamzix-扣款
	GamzixAdd      = 4054 // Gamzix-加款
)

// Peter & Sons
const (
	PeterAndSonsBet      = 4055 // PeterAndSons-下注
	PeterAndSonsSettle   = 4056 // PeterAndSons-结算
	PeterAndSonsRollback = 4057 // PeterAndSons-回滚
	PeterAndSonsDeduct   = 4058 // PeterAndSons-扣款
	PeterAndSonsAdd      = 4059 // PeterAndSons-加款
)

// Jade Rabbit Studio
const (
	JadeRabbitStudioBet      = 4060 // JadeRabbitStudio-下注
	JadeRabbitStudioSettle   = 4061 // JadeRabbitStudio-结算
	JadeRabbitStudioRollback = 4062 // JadeRabbitStudio-回滚
	JadeRabbitStudioDeduct   = 4063 // JadeRabbitStudio-扣款
	JadeRabbitStudioAdd      = 4064 // JadeRabbitStudio-加款
)

// Naga Games
const (
	NagaGamesBet      = 4065 // NagaGames-下注
	NagaGamesSettle   = 4066 // NagaGames-结算
	NagaGamesRollback = 4067 // NagaGames-回滚
	NagaGamesDeduct   = 4068 // NagaGames-扣款
	NagaGamesAdd      = 4069 // NagaGames-加款
)

// OneGame
const (
	OneGameBet      = 4070 // OneGame-下注
	OneGameSettle   = 4071 // OneGame-结算
	OneGameRollback = 4072 // OneGame-回滚
	OneGameDeduct   = 4073 // OneGame-扣款
	OneGameAdd      = 4074 // OneGame-加款
)

// Nolimit City
const (
	NolimitCityBet      = 4075 // NolimitCity-下注
	NolimitCitySettle   = 4076 // NolimitCity-结算
	NolimitCityRollback = 4077 // NolimitCity-回滚
	NolimitCityDeduct   = 4078 // NolimitCity-扣款
	NolimitCityAdd      = 4079 // NolimitCity-加款
)

// Big Time Gaming
const (
	BigTimeGamingBet      = 4080 // BigTimeGaming-下注
	BigTimeGamingSettle   = 4081 // BigTimeGaming-结算
	BigTimeGamingRollback = 4082 // BigTimeGaming-回滚
	BigTimeGamingDeduct   = 4083 // BigTimeGaming-扣款
	BigTimeGamingAdd      = 4084 // BigTimeGaming-加款
)

// PGSoft
const (
	PGSoftBet      = 4085 // PGSoft-下注
	PGSoftSettle   = 4086 // PGSoft-结算
	PGSoftRollback = 4087 // PGSoft-回滚
	PGSoftDeduct   = 4088 // PGSoft-扣款
	PGSoftAdd      = 4089 // PGSoft-加款
)

// Mancala Gaming
const (
	MancalaGamingBet      = 4090 // MancalaGaming-下注
	MancalaGamingSettle   = 4091 // MancalaGaming-结算
	MancalaGamingRollback = 4092 // MancalaGaming-回滚
	MancalaGamingDeduct   = 4093 // MancalaGaming-扣款
	MancalaGamingAdd      = 4094 // MancalaGaming-加款
)

// 3 Oaks Gaming
const (
	ThreeOaksGamingBet      = 4095 // 3OaksGaming-下注
	ThreeOaksGamingSettle   = 4096 // 3OaksGaming-结算
	ThreeOaksGamingRollback = 4097 // 3OaksGaming-回滚
	ThreeOaksGamingDeduct   = 4098 // 3OaksGaming-扣款
	ThreeOaksGamingAdd      = 4099 // 3OaksGaming-加款
)

// Kingmidas
const (
	KingmidasBet      = 4100 // Kingmidas-下注
	KingmidasSettle   = 4101 // Kingmidas-结算
	KingmidasRollback = 4102 // Kingmidas-回滚
	KingmidasDeduct   = 4103 // Kingmidas-扣款
	KingmidasAdd      = 4104 // Kingmidas-加款
)

// AE Sexy
const (
	AESexyBet      = 4105 // AESexy-下注
	AESexySettle   = 4106 // AESexy-结算
	AESexyRollback = 4107 // AESexy-回滚
	AESexyDeduct   = 4108 // AESexy-扣款
	AESexyAdd      = 4109 // AESexy-加款
)

// Spinomenal
const (
	SpinomenalBet      = 4110 // Spinomenal-下注
	SpinomenalSettle   = 4111 // Spinomenal-结算
	SpinomenalRollback = 4112 // Spinomenal-回滚
	SpinomenalDeduct   = 4113 // Spinomenal-扣款
	SpinomenalAdd      = 4114 // Spinomenal-加款
)

// Smartsoft Gaming
const (
	SmartsoftGamingBet      = 4115 // SmartsoftGaming-下注
	SmartsoftGamingSettle   = 4116 // SmartsoftGaming-结算
	SmartsoftGamingRollback = 4117 // SmartsoftGaming-回滚
	SmartsoftGamingDeduct   = 4118 // SmartsoftGaming-扣款
	SmartsoftGamingAdd      = 4119 // SmartsoftGaming-加款
)

// TVBet
const (
	TVBetBet      = 4120 // TVBet-下注
	TVBetSettle   = 4121 // TVBet-结算
	TVBetRollback = 4122 // TVBet-回滚
	TVBetDeduct   = 4123 // TVBet-扣款
	TVBetAdd      = 4124 // TVBet-加款
)

// Fantasma Games
const (
	FantasmaGamesBet      = 4125 // FantasmaGames-下注
	FantasmaGamesSettle   = 4126 // FantasmaGames-结算
	FantasmaGamesRollback = 4127 // FantasmaGames-回滚
	FantasmaGamesDeduct   = 4128 // FantasmaGames-扣款
	FantasmaGamesAdd      = 4129 // FantasmaGames-加款
)

// VoltEnt
const (
	VoltEntBet      = 4130 // VoltEnt-下注
	VoltEntSettle   = 4131 // VoltEnt-结算
	VoltEntRollback = 4132 // VoltEnt-回滚
	VoltEntDeduct   = 4133 // VoltEnt-扣款
	VoltEntAdd      = 4134 // VoltEnt-加款
)

// ICONIC21
const (
	ICONIC21Bet      = 4135 // ICONIC21-下注
	ICONIC21Settle   = 4136 // ICONIC21-结算
	ICONIC21Rollback = 4137 // ICONIC21-回滚
	ICONIC21Deduct   = 4138 // ICONIC21-扣款
	ICONIC21Add      = 4139 // ICONIC21-加款
)

// Fugaso
const (
	FugasoBet      = 4140 // Fugaso-下注
	FugasoSettle   = 4141 // Fugaso-结算
	FugasoRollback = 4142 // Fugaso-回滚
	FugasoDeduct   = 4143 // Fugaso-扣款
	FugasoAdd      = 4144 // Fugaso-加款
)

// Slotmill
const (
	SlotmillBet      = 4145 // Slotmill-下注
	SlotmillSettle   = 4146 // Slotmill-结算
	SlotmillRollback = 4147 // Slotmill-回滚
	SlotmillDeduct   = 4148 // Slotmill-扣款
	SlotmillAdd      = 4149 // Slotmill-加款
)

// JDB balance change constants
const (
	JDBBet      = 4150 // 下注
	JDBSettle   = 4151 // 结算
	JDBRollback = 4152 // 回滚
	JDBDeduct   = 4153 // 扣款
	JDBAdd      = 4154 // 加款
)

// Tom Horn Gaming balance change constants
const (
	TomHornGamingBet      = 4155 // 下注
	TomHornGamingSettle   = 4156 // 结算
	TomHornGamingRollback = 4157 // 回滚
	TomHornGamingDeduct   = 4158 // 扣款
	TomHornGamingAdd      = 4159 // 加款
)

// Endorphina balance change constants
const (
	EndorphinaBet      = 4160 // 下注
	EndorphinaSettle   = 4161 // 结算
	EndorphinaRollback = 4162 // 回滚
	EndorphinaDeduct   = 4163 // 扣款
	EndorphinaAdd      = 4164 // 加款
)

// NetGaming balance change constants
const (
	NetGamingBet      = 4165 // 下注
	NetGamingSettle   = 4166 // 结算
	NetGamingRollback = 4167 // 回滚
	NetGamingDeduct   = 4168 // 扣款
	NetGamingAdd      = 4169 // 加款
)

// Hacksaw Gaming balance change constants
const (
	HacksawGamingBet      = 4170 // 下注
	HacksawGamingSettle   = 4171 // 结算
	HacksawGamingRollback = 4172 // 回滚
	HacksawGamingDeduct   = 4173 // 扣款
	HacksawGamingAdd      = 4174 // 加款
)

// BGaming balance change constants
const (
	BGamingBet      = 4175 // 下注
	BGamingSettle   = 4176 // 结算
	BGamingRollback = 4177 // 回滚
	BGamingDeduct   = 4178 // 扣款
	BGamingAdd      = 4179 // 加款
)

// Gamebeat balance change constants
const (
	GamebeatBet      = 4180 // 下注
	GamebeatSettle   = 4181 // 结算
	GamebeatRollback = 4182 // 回滚
	GamebeatDeduct   = 4183 // 扣款
	GamebeatAdd      = 4184 // 加款
)

// Gaming Corps balance change constants
const (
	GamingCorpsBet      = 4185 // 下注
	GamingCorpsSettle   = 4186 // 结算
	GamingCorpsRollback = 4187 // 回滚
	GamingCorpsDeduct   = 4188 // 扣款
	GamingCorpsAdd      = 4189 // 加款
)

// AvatarUX balance change constants
const (
	AvatarUXBet      = 4190 // 下注
	AvatarUXSettle   = 4191 // 结算
	AvatarUXRollback = 4192 // 回滚
	AvatarUXDeduct   = 4193 // 扣款
	AvatarUXAdd      = 4194 // 加款
)

// Blueprint Gaming balance change constants
const (
	BlueprintGamingBet      = 4195 // 下注
	BlueprintGamingSettle   = 4196 // 结算
	BlueprintGamingRollback = 4197 // 回滚
	BlueprintGamingDeduct   = 4198 // 扣款
	BlueprintGamingAdd      = 4199 // 加款
)

// NetGame Entertainment balance change constants
const (
	NetGameEntertainmentBet      = 4200 // 下注
	NetGameEntertainmentSettle   = 4201 // 结算
	NetGameEntertainmentRollback = 4202 // 回滚
	NetGameEntertainmentDeduct   = 4203 // 扣款
	NetGameEntertainmentAdd      = 4204 // 加款
)

// Thunderkick balance change constants
const (
	ThunderkickBet      = 4205 // 下注
	ThunderkickSettle   = 4206 // 结算
	ThunderkickRollback = 4207 // 回滚
	ThunderkickDeduct   = 4208 // 扣款
	ThunderkickAdd      = 4209 // 加款
)

// BF Games balance change constants
const (
	BFGamesBet      = 4210 // 下注
	BFGamesSettle   = 4211 // 结算
	BFGamesRollback = 4212 // 回滚
	BFGamesDeduct   = 4213 // 扣款
	BFGamesAdd      = 4214 // 加款
)

// TaDa Gaming balance change constants
const (
	TaDaGamingBet      = 4215 // 下注
	TaDaGamingSettle   = 4216 // 结算
	TaDaGamingRollback = 4217 // 回滚
	TaDaGamingDeduct   = 4218 // 扣款
	TaDaGamingAdd      = 4219 // 加款
)

// Booming Games balance change constants
const (
	BoomingGamesBet      = 4220 // 下注
	BoomingGamesSettle   = 4221 // 结算
	BoomingGamesRollback = 4222 // 回滚
	BoomingGamesDeduct   = 4223 // 扣款
	BoomingGamesAdd      = 4224 // 加款
)

// Amigo Gaming balance change constants
const (
	AmigoGamingBet      = 4225 // 下注
	AmigoGamingSettle   = 4226 // 结算
	AmigoGamingRollback = 4227 // 回滚
	AmigoGamingDeduct   = 4228 // 扣款
	AmigoGamingAdd      = 4229 // 加款
)

// Raw iGaming balance change constants
const (
	RawIGamingBet      = 4230 // 下注
	RawIGamingSettle   = 4231 // 结算
	RawIGamingRollback = 4232 // 回滚
	RawIGamingDeduct   = 4233 // 扣款
	RawIGamingAdd      = 4234 // 加款
)

// Leander Games balance change constants
const (
	LeanderGamesBet      = 4235 // 下注
	LeanderGamesSettle   = 4236 // 结算
	LeanderGamesRollback = 4237 // 回滚
	LeanderGamesDeduct   = 4238 // 扣款
	LeanderGamesAdd      = 4239 // 加款
)

// CQ9 balance change constants
const (
	CQ9Bet      = 4240 // 下注
	CQ9Settle   = 4241 // 结算
	CQ9Rollback = 4242 // 回滚
	CQ9Deduct   = 4243 // 扣款
	CQ9Add      = 4244 // 加款
)

// Atmosfera balance change constants
const (
	AtmosferaBet      = 4245 // 下注
	AtmosferaSettle   = 4246 // 结算
	AtmosferaRollback = 4247 // 回滚
	AtmosferaDeduct   = 4248 // 扣款
	AtmosferaAdd      = 4249 // 加款
)

// Novomatic balance change constants
const (
	NovomaticBet      = 4250 // 下注
	NovomaticSettle   = 4251 // 结算
	NovomaticRollback = 4252 // 回滚
	NovomaticDeduct   = 4253 // 扣款
	NovomaticAdd      = 4254 // 加款
)

// Winfinity balance change constants
const (
	WinfinityBet      = 4255 // 下注
	WinfinitySettle   = 4256 // 结算
	WinfinityRollback = 4257 // 回滚
	WinfinityDeduct   = 4258 // 扣款
	WinfinityAdd      = 4259 // 加款
)

// Onlyplay balance change constants
const (
	OnlyplayBet      = 4260 // 下注
	OnlyplaySettle   = 4261 // 结算
	OnlyplayRollback = 4262 // 回滚
	OnlyplayDeduct   = 4263 // 扣款
	OnlyplayAdd      = 4264 // 加款
)

// Darwin Gaming balance change constants
const (
	DarwinGamingBet      = 4265 // 下注
	DarwinGamingSettle   = 4266 // 结算
	DarwinGamingRollback = 4267 // 回滚
	DarwinGamingDeduct   = 4268 // 扣款
	DarwinGamingAdd      = 4269 // 加款
)

// Playson balance change constants
const (
	PlaysonBet      = 4270 // 下注
	PlaysonSettle   = 4271 // 结算
	PlaysonRollback = 4272 // 回滚
	PlaysonDeduct   = 4273 // 扣款
	PlaysonAdd      = 4274 // 加款
)

// Yolted balance change constants
const (
	YoltedBet      = 4275 // 下注
	YoltedSettle   = 4276 // 结算
	YoltedRollback = 4277 // 回滚
	YoltedDeduct   = 4278 // 扣款
	YoltedAdd      = 4279 // 加款
)

// iMoon Games balance change constants
const (
	IMoonGamesBet      = 4280 // 下注
	IMoonGamesSettle   = 4281 // 结算
	IMoonGamesRollback = 4282 // 回滚
	IMoonGamesDeduct   = 4283 // 扣款
	IMoonGamesAdd      = 4284 // 加款
)

// InOut Games balance change constants
const (
	InOutGamesBet      = 4285 // 下注
	InOutGamesSettle   = 4286 // 结算
	InOutGamesRollback = 4287 // 回滚
	InOutGamesDeduct   = 4288 // 扣款
	InOutGamesAdd      = 4289 // 加款
)

// Backseat Gaming balance change constants
const (
	BackseatGamingBet      = 4290 // 下注
	BackseatGamingSettle   = 4291 // 结算
	BackseatGamingRollback = 4292 // 回滚
	BackseatGamingDeduct   = 4293 // 扣款
	BackseatGamingAdd      = 4294 // 加款
)

// Octoplay balance change constants
const (
	OctoplayBet      = 4295 // 下注
	OctoplaySettle   = 4296 // 结算
	OctoplayRollback = 4297 // 回滚
	OctoplayDeduct   = 4298 // 扣款
	OctoplayAdd      = 4299 // 加款
)

// SpinOro balance change constants
const (
	SpinOroBet      = 4300 // 下注
	SpinOroSettle   = 4301 // 结算
	SpinOroRollback = 4302 // 回滚
	SpinOroDeduct   = 4303 // 扣款
	SpinOroAdd      = 4304 // 加款
)

// PHOENIX7 balance change constants
const (
	PHOENIX7Bet      = 4305 // 下注
	PHOENIX7Settle   = 4306 // 结算
	PHOENIX7Rollback = 4307 // 回滚
	PHOENIX7Deduct   = 4308 // 扣款
	PHOENIX7Add      = 4309 // 加款
)

// BullsharkGames balance change constants
const (
	BullsharkGamesBet      = 4310 // 下注
	BullsharkGamesSettle   = 4311 // 结算
	BullsharkGamesRollback = 4312 // 回滚
	BullsharkGamesDeduct   = 4313 // 扣款
	BullsharkGamesAdd      = 4314 // 加款
)

// SpinlogicGaming balance change constants
const (
	SpinlogicGamingBet      = 4315 // 下注
	SpinlogicGamingSettle   = 4316 // 结算
	SpinlogicGamingRollback = 4317 // 回滚
	SpinlogicGamingDeduct   = 4318 // 扣款
	SpinlogicGamingAdd      = 4319 // 加款
)

// NowNowGaming balance change constants
const (
	NowNowGamingBet      = 4320 // 下注
	NowNowGamingSettle   = 4321 // 结算
	NowNowGamingRollback = 4322 // 回滚
	NowNowGamingDeduct   = 4323 // 扣款
	NowNowGamingAdd      = 4324 // 加款
)

// ElysiumStudios balance change constants
const (
	ElysiumStudiosBet      = 4325 // 下注
	ElysiumStudiosSettle   = 4326 // 结算
	ElysiumStudiosRollback = 4327 // 回滚
	ElysiumStudiosDeduct   = 4328 // 扣款
	ElysiumStudiosAdd      = 4329 // 加款
)

// SwinttGames balance change constants
const (
	SwinttGamesBet      = 4330 // 下注
	SwinttGamesSettle   = 4331 // 结算
	SwinttGamesRollback = 4332 // 回滚
	SwinttGamesDeduct   = 4333 // 扣款
	SwinttGamesAdd      = 4334 // 加款
)

// SwinttPremium balance change constants
const (
	SwinttPremiumBet      = 4335 // 下注
	SwinttPremiumSettle   = 4336 // 结算
	SwinttPremiumRollback = 4337 // 回滚
	SwinttPremiumDeduct   = 4338 // 扣款
	SwinttPremiumAdd      = 4339 // 加款
)

// EvoplayEntertainment balance change constants
const (
	EvoplayEntertainmentBet      = 4340 // 下注
	EvoplayEntertainmentSettle   = 4341 // 结算
	EvoplayEntertainmentRollback = 4342 // 回滚
	EvoplayEntertainmentDeduct   = 4343 // 扣款
	EvoplayEntertainmentAdd      = 4344 // 加款
)

// FaChai balance change constants
const (
	FaChaiBet      = 4345 // 下注
	FaChaiSettle   = 4346 // 结算
	FaChaiRollback = 4347 // 回滚
	FaChaiDeduct   = 4348 // 扣款
	FaChaiAdd      = 4349 // 加款
)

// VivoGaming balance change constants
const (
	VivoGamingBet      = 4350 // 下注
	VivoGamingSettle   = 4351 // 结算
	VivoGamingRollback = 4352 // 回滚
	VivoGamingDeduct   = 4353 // 扣款
	VivoGamingAdd      = 4354 // 加款
)

// MascotGaming balance change constants
const (
	MascotGamingBet      = 4355 // 下注
	MascotGamingSettle   = 4356 // 结算
	MascotGamingRollback = 4357 // 回滚
	MascotGamingDeduct   = 4358 // 扣款
	MascotGamingAdd      = 4359 // 加款
)

// Playzia balance change constants
const (
	PlayziaBet      = 4360 // 下注
	PlayziaSettle   = 4361 // 结算
	PlayziaRollback = 4362 // 回滚
	PlayziaDeduct   = 4363 // 扣款
	PlayziaAdd      = 4364 // 加款
)

// TrustyStudios balance change constants
const (
	TrustyStudiosBet      = 4365 // 下注
	TrustyStudiosSettle   = 4366 // 结算
	TrustyStudiosRollback = 4367 // 回滚
	TrustyStudiosDeduct   = 4368 // 扣款
	TrustyStudiosAdd      = 4369 // 加款
)

// Slotopia balance change constants
const (
	SlotopiaBet      = 4370 // 下注
	SlotopiaSettle   = 4371 // 结算
	SlotopiaRollback = 4372 // 回滚
	SlotopiaDeduct   = 4373 // 扣款
	SlotopiaAdd      = 4374 // 加款
)

// KitsuneStudios balance change constants
const (
	KitsuneStudiosBet      = 4375 // 下注
	KitsuneStudiosSettle   = 4376 // 结算
	KitsuneStudiosRollback = 4377 // 回滚
	KitsuneStudiosDeduct   = 4378 // 扣款
	KitsuneStudiosAdd      = 4379 // 加款
)
const (
	// ReelPlay (rpl)
	BalanceCReasonHS8ReelPlayBet      = 4380
	BalanceCReasonHS8ReelPlaySettle   = 4381
	BalanceCReasonHS8ReelPlayRollback = 4382
	BalanceCReasonHS8ReelPlayBuyin    = 4383
	BalanceCReasonHS8ReelPlayPayout   = 4384

	// Reel Life Games (rlg)
	BalanceCReasonHS8ReelLifeGamesBet      = 4385
	BalanceCReasonHS8ReelLifeGamesSettle   = 4386
	BalanceCReasonHS8ReelLifeGamesRollback = 4387
	BalanceCReasonHS8ReelLifeGamesBuyin    = 4388
	BalanceCReasonHS8ReelLifeGamesPayout   = 4389

	// Playtech (plt)
	BalanceCReasonHS8PlaytechBet      = 4390
	BalanceCReasonHS8PlaytechSettle   = 4391
	BalanceCReasonHS8PlaytechRollback = 4392
	BalanceCReasonHS8PlaytechBuyin    = 4393
	BalanceCReasonHS8PlaytechPayout   = 4394

	// Relax Gaming (reg)
	BalanceCReasonHS8RelaxGamingBet      = 4395
	BalanceCReasonHS8RelaxGamingSettle   = 4396
	BalanceCReasonHS8RelaxGamingRollback = 4397
	BalanceCReasonHS8RelaxGamingBuyin    = 4398
	BalanceCReasonHS8RelaxGamingPayout   = 4399

	// RTG Slots (rts)
	BalanceCReasonHS8RTGSlotsBet      = 4400
	BalanceCReasonHS8RTGSlotsSettle   = 4401
	BalanceCReasonHS8RTGSlotsRollback = 4402
	BalanceCReasonHS8RTGSlotsBuyin    = 4403
	BalanceCReasonHS8RTGSlotsPayout   = 4404

	// Qubit Games (qbt)
	BalanceCReasonHS8QubitGamesBet      = 4405
	BalanceCReasonHS8QubitGamesSettle   = 4406
	BalanceCReasonHS8QubitGamesRollback = 4407
	BalanceCReasonHS8QubitGamesBuyin    = 4408
	BalanceCReasonHS8QubitGamesPayout   = 4409

	// 1Spin4Win (osp)
	BalanceCReasonHS81Spin4WinBet      = 4410
	BalanceCReasonHS81Spin4WinSettle   = 4411
	BalanceCReasonHS81Spin4WinRollback = 4412
	BalanceCReasonHS81Spin4WinBuyin    = 4413
	BalanceCReasonHS81Spin4WinPayout   = 4414

	// Northern Lights Gaming (nrl)
	BalanceCReasonHS8NorthernLightsGamingBet      = 4415
	BalanceCReasonHS8NorthernLightsGamingSettle   = 4416
	BalanceCReasonHS8NorthernLightsGamingRollback = 4417
	BalanceCReasonHS8NorthernLightsGamingBuyin    = 4418
	BalanceCReasonHS8NorthernLightsGamingPayout   = 4419

	// Reflex Gaming (rfg)
	BalanceCReasonHS8ReflexGamingBet      = 4420
	BalanceCReasonHS8ReflexGamingSettle   = 4421
	BalanceCReasonHS8ReflexGamingRollback = 4422
	BalanceCReasonHS8ReflexGamingBuyin    = 4423
	BalanceCReasonHS8ReflexGamingPayout   = 4424

	// Hot Rise Games (hrg)
	BalanceCReasonHS8HotRiseGamesBet      = 4425
	BalanceCReasonHS8HotRiseGamesSettle   = 4426
	BalanceCReasonHS8HotRiseGamesRollback = 4427
	BalanceCReasonHS8HotRiseGamesBuyin    = 4428
	BalanceCReasonHS8HotRiseGamesPayout   = 4429

	// Bulletproof Games (bpg)
	BalanceCReasonHS8BulletproofGamesBet      = 4430
	BalanceCReasonHS8BulletproofGamesSettle   = 4431
	BalanceCReasonHS8BulletproofGamesRollback = 4432
	BalanceCReasonHS8BulletproofGamesBuyin    = 4433
	BalanceCReasonHS8BulletproofGamesPayout   = 4434

	// GameVy (gmv)
	BalanceCReasonHS8GameVyBet      = 4435
	BalanceCReasonHS8GameVySettle   = 4436
	BalanceCReasonHS8GameVyRollback = 4437
	BalanceCReasonHS8GameVyBuyin    = 4438
	BalanceCReasonHS8GameVyPayout   = 4439

	// Jelly (jly)
	BalanceCReasonHS8JellyBet      = 4440
	BalanceCReasonHS8JellySettle   = 4441
	BalanceCReasonHS8JellyRollback = 4442
	BalanceCReasonHS8JellyBuyin    = 4443
	BalanceCReasonHS8JellyPayout   = 4444

	// Hungry Bear Gaming (hbg)
	BalanceCReasonHS8HungryBearGamingBet      = 4445
	BalanceCReasonHS8HungryBearGamingSettle   = 4446
	BalanceCReasonHS8HungryBearGamingRollback = 4447
	BalanceCReasonHS8HungryBearGamingBuyin    = 4448
	BalanceCReasonHS8HungryBearGamingPayout   = 4449

	// Play'n Go (png)
	BalanceCReasonHS8PlaynGoBet      = 4450
	BalanceCReasonHS8PlaynGoSettle   = 4451
	BalanceCReasonHS8PlaynGoRollback = 4452
	BalanceCReasonHS8PlaynGoBuyin    = 4453
	BalanceCReasonHS8PlaynGoPayout   = 4454

	// Yggdrasil (ygg)
	BalanceCReasonHS8YggdrasilBet      = 4455
	BalanceCReasonHS8YggdrasilSettle   = 4456
	BalanceCReasonHS8YggdrasilRollback = 4457
	BalanceCReasonHS8YggdrasilBuyin    = 4458
	BalanceCReasonHS8YggdrasilPayout   = 4459

	// Bang Bang Games (bbg)
	BalanceCReasonHS8BangBangGamesBet      = 4460
	BalanceCReasonHS8BangBangGamesSettle   = 4461
	BalanceCReasonHS8BangBangGamesRollback = 4462
	BalanceCReasonHS8BangBangGamesBuyin    = 4463
	BalanceCReasonHS8BangBangGamesPayout   = 4464

	// Boomerang Studios (brg)
	BalanceCReasonHS8BoomerangStudiosBet      = 4465
	BalanceCReasonHS8BoomerangStudiosSettle   = 4466
	BalanceCReasonHS8BoomerangStudiosRollback = 4467
	BalanceCReasonHS8BoomerangStudiosBuyin    = 4468
	BalanceCReasonHS8BoomerangStudiosPayout   = 4469

	// Jili Games (jil)
	BalanceCReasonHS8JiliGamesBet      = 4470
	BalanceCReasonHS8JiliGamesSettle   = 4471
	BalanceCReasonHS8JiliGamesRollback = 4472
	BalanceCReasonHS8JiliGamesBuyin    = 4473
	BalanceCReasonHS8JiliGamesPayout   = 4474

	// Push Gaming (psh)
	BalanceCReasonHS8PushGamingBet      = 4475
	BalanceCReasonHS8PushGamingSettle   = 4476
	BalanceCReasonHS8PushGamingRollback = 4477
	BalanceCReasonHS8PushGamingBuyin    = 4478
	BalanceCReasonHS8PushGamingPayout   = 4479

	// Print Studios (pnt)
	BalanceCReasonHS8PrintStudiosBet      = 4480
	BalanceCReasonHS8PrintStudiosSettle   = 4481
	BalanceCReasonHS8PrintStudiosRollback = 4482
	BalanceCReasonHS8PrintStudiosBuyin    = 4483
	BalanceCReasonHS8PrintStudiosPayout   = 4484

	// Quickspin (qps)
	BalanceCReasonHS8QuickspinBet      = 4485
	BalanceCReasonHS8QuickspinSettle   = 4486
	BalanceCReasonHS8QuickspinRollback = 4487
	BalanceCReasonHS8QuickspinBuyin    = 4488
	BalanceCReasonHS8QuickspinPayout   = 4489

	// 4ThePlayer (ftp)
	BalanceCReasonHS84ThePlayerBet      = 4490
	BalanceCReasonHS84ThePlayerSettle   = 4491
	BalanceCReasonHS84ThePlayerRollback = 4492
	BalanceCReasonHS84ThePlayerBuyin    = 4493
	BalanceCReasonHS84ThePlayerPayout   = 4494

	// Booongo (bng)
	BalanceCReasonHS8BoongoBet      = 4495
	BalanceCReasonHS8BoongoSettle   = 4496
	BalanceCReasonHS8BoongoRollback = 4497
	BalanceCReasonHS8BoongoBuyin    = 4498
	BalanceCReasonHS8BoongoPayout   = 4499

	// Win Fast Games (wfg)
	BalanceCReasonHS8WinFastGamesBet      = 4500
	BalanceCReasonHS8WinFastGamesSettle   = 4501
	BalanceCReasonHS8WinFastGamesRollback = 4502
	BalanceCReasonHS8WinFastGamesBuyin    = 4503
	BalanceCReasonHS8WinFastGamesPayout   = 4504

	// Rabcat Gambling (rbc)
	BalanceCReasonHS8RabcatGamblingBet      = 4505
	BalanceCReasonHS8RabcatGamblingSettle   = 4506
	BalanceCReasonHS8RabcatGamblingRollback = 4507
	BalanceCReasonHS8RabcatGamblingBuyin    = 4508
	BalanceCReasonHS8RabcatGamblingPayout   = 4509
)
