package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/spf13/viper"
	"github.com/zhms/xgo/xgo"
)

func (c *AgentController) get_code_t1(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	data, _ := server.XDb().Table("x_agent_code_t1").Select("AgentCode,FenCheng,Memo").Where("UserId=?", token.UserId).Find()
	if data == nil {
		return
	}
	ctx.RespOK(data.RawData)
}

func (c *AgentController) create_code_t1(ctx *abugo.AbuHttpContent) {

	// 增加香港六合彩返佣传参数
	type GameDefine struct {
		CatId      int     `json:"CatId"`
		RewardRate float64 `json:"RewardRate"`
	}
	type XGameArr []GameDefine
	type RequestData struct {
		HaxiTrx          float64 `json:"haxitrx"`
		HaxiUsdt         float64 `json:"haxiusdt"`
		Dianzi           float64 `json:"dianzi"`
		Live             float64 `json:"live"`
		Qipai            float64 `json:"qipai"`
		Sport            float64 `json:"sport"`
		Lottery          float64 `json:"lottery"`
		Texas            float64 `json:"texas"`
		LowLottery       float64 `json:"lowLottery"`
		CryptoMarket     float64 `json:"cryptoMarket"`
		HaXiRouletteTrx  float64 `json:"haxiroulettetrx"`
		HaXiRouletteUsdt float64 `json:"haxirouletteusdt"`
		Chain            float64 `json:"chain"`
		XGameArr         XGameArr
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.HaxiTrx < 0 || reqdata.HaxiUsdt < 0 || reqdata.Dianzi < 0 || reqdata.Live < 0 || reqdata.Qipai < 0 || reqdata.Sport < 0 || reqdata.Lottery < 0 || reqdata.Chain < 0 || reqdata.Texas < 0 || reqdata.LowLottery < 0 || reqdata.CryptoMarket < 0 || reqdata.HaXiRouletteTrx < 0 || reqdata.HaXiRouletteUsdt < 0 {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}
	token := server.GetToken(ctx)
	count, _ := server.XDb().Table("x_agent_code_t1").Where("UserId=?", token.UserId).Count()
	if count > 20 {
		ctx.RespErrString(true, &errcode, "最多只能生成20个邀请码")
		return
	}

	//TODO : 检查参数合法性

	bytes, _ := json.Marshal(reqdata)
	id := 0
	for {
		id = 10000000 + rand.Intn(99999999-10000000)
		_, err := server.XDb().Table("x_agent_code_t1").Insert(map[string]interface{}{
			"UserId":    token.UserId,
			"AgentCode": fmt.Sprint(id),
			"FenCheng":  string(bytes),
		})
		if err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}
	ctx.RespOK(id)
}

func (c *AgentController) update_code_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AgentCode string  `json:"AgentCode" validate:"required"`
		Name      string  `json:"Name" validate:"required"`
		FenCheng  float64 `json:"FenCheng"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	data, _ := server.XDb().Table("x_agent_code_t1").Where("AgentCode = ?", reqdata.AgentCode).First()
	if data == nil {
		ctx.RespErrString(true, &errcode, "AgentCode不存在")
		return
	}
	if data.Int("UserId") != token.UserId {
		ctx.RespErrString(true, &errcode, "不能修改别人的分享链接")
		return
	}
	// Определяем допустимые значения Name
	validNames := map[string]bool{
		"haxitrx": true, "haxiusdt": true, "dianzi": true, "live": true,
		"qipai": true, "sport": true, "lottery": true, "chain": true,
		"texas": true, "lowLottery": true, "cryptoMarket": true,
		"haxiroulettetrx": true, "haxirouletteusdt": true,
	}

	// Проверяем Name
	if !validNames[reqdata.Name] {
		ctx.RespErrString(true, &errcode, "无效的名称参数") // "Недопустимое значение параметра Name"
		return
	}

	// Проверяем FenCheng
	const (
		minFenCheng = 0.0
		maxFenCheng = 0.022
	)

	if reqdata.FenCheng < minFenCheng || reqdata.FenCheng > maxFenCheng {
		ctx.RespErrString(true, &errcode, "分成比例超出允许范围") // "Значение FenCheng вне допустимого диапазона"
		return
	}
	parent, _ := server.XDb().Table("x_user").Select("FenCheng,AgentUseId").Where("UserId = ?", token.UserId).First()
	if parent == nil {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	fencheng := parent.String("FenCheng")
	if parent.Int("AgentUseId") > 0 {
		fangan, _ := server.XDb().Table("x_agent_commission_config").Select("Data").Where("Id = ?", parent.Int("AgentUseId")).First()
		if fangan == nil {
			ctx.RespOK()
			return
		}
		d := map[string]interface{}{}
		json.Unmarshal([]byte(fangan.String("Data")), &d)
		dx := d["rate"]
		bytes, _ := json.Marshal(dx)
		fencheng = string(bytes)
	}
	parentfc := map[string]interface{}{}
	json.Unmarshal([]byte(fencheng), &parentfc)
	if xgo.ToFloat(parentfc[reqdata.Name]) < reqdata.FenCheng {
		ctx.RespErrString(true, &errcode, "分成不可大于自己的分成")
		return
	}

	userfc := map[string]interface{}{}
	json.Unmarshal([]byte(data.String("FenCheng")), &userfc)
	userfc[reqdata.Name] = reqdata.FenCheng
	bytes, _ := json.Marshal(userfc)
	server.XDb().Table("x_agent_code_t1").Where("AgentCode = ?", reqdata.AgentCode).Update(map[string]interface{}{
		"FenCheng": string(bytes),
	})
	logs.Debug("update_code_t1", token.UserId, reqdata.AgentCode, reqdata.Name, reqdata.FenCheng)
	ctx.RespOK()
}

func (c *AgentController) delete_code_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AgentCode string `json:"AgentCode"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	server.XDb().Table("x_agent_code_t1").Where("UserId = ? and AgentCode = ?", token.UserId, reqdata.AgentCode).Delete()
	ctx.RespOK()
}

func (c *AgentController) modify_code_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AgentCode string `json:"AgentCode"`
		Memo      string `json:"Memo"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	server.XDb().Table("x_agent_code_t1").Where("UserId = ? and AgentCode = ?", token.UserId, reqdata.AgentCode).Update(map[string]interface{}{
		"Memo": reqdata.Memo,
	})
	ctx.RespOK()
}

func (c *AgentController) info_t1(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	data, err := server.Db().CallProcedure("x_api_agent_get_info_t1", token.UserId)
	if err != nil {
		logs.Error("info_t1", err)
		return
	}
	ctx.RespOK(data)
}

func (c *AgentController) get_commission_mine_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		StartTime int64 `json:"StartTime"`
		EndTime   int64 `json:"EndTime"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.StartTime < 1 {
		reqdata.StartTime = carbon.Now().StartOfDay().StdTime().Unix()
		reqdata.EndTime = carbon.Now().EndOfDay().StdTime().Unix()
	}
	token := server.GetToken(ctx)
	xAgentDaillyDo := server.DaoxHashGame().XAgentDailly
	xAgentDaillyDb := xAgentDaillyDo.WithContext(context.Background())
	startTime := carbon.CreateFromTimestamp(reqdata.StartTime)
	endTime := carbon.CreateFromTimestamp(reqdata.EndTime)
	xAgentDaillyDb = xAgentDaillyDb.Where(xAgentDaillyDo.RecordDate.Gte(startTime.StdTime()))
	xAgentDaillyDb = xAgentDaillyDb.Where(xAgentDaillyDo.RecordDate.Lt(endTime.StdTime()))
	xAgentDaillyDb = xAgentDaillyDb.Where(xAgentDaillyDo.UserID.Eq(int32(token.UserId)))
	type Response struct {
		LiuSuiHashTrx_t1          float64
		LiuSuiHashUsdt_t1         float64
		LiuSuiDianZhiUsdt_t1      float64
		LiuSuiQiPaiUsdt_t1        float64
		LiuSuiLiveUsdt_t1         float64
		LiuSuiChainUsdt_t1        float64
		LiuSuiSportUsdt_t1        float64
		LiuSuiLotteryUsdt_t1      float64
		LiuSuiTexasUsdt_t1        float64
		LiuSuiHaXiRouletteTrx_t1  float64
		LiuSuiHaXiRouletteUsdt_t1 float64
		LiuSuiLowLotteryUsdt_t1   float64
		LiuSuiCryptoMarketUsdt_t1 float64
		LiuSuiLiuHeLotteryUsdt_t1 float64

		SelfLiuSuiHashTrx_t1          float64
		SelfLiuSuiHashUsdt_t1         float64
		SelfLiuSuiDianZhiUsdt_t1      float64
		SelfLiuSuiQiPaiUsdt_t1        float64
		SelfLiuSuiLiveUsdt_t1         float64
		SelfLiuSuiChainUsdt_t1        float64
		SelfLiuSuiSportUsdt_t1        float64
		SelfLiuSuiTexasUsdt_t1        float64
		SelfLiuSuiLotteryUsdt_t1      float64
		SelfLiuSuiLowLotteryUsdt_t1   float64
		SelfLiuSuiHaXiRouletteUsdt_t1 float64
		SelfLiuSuiHaXiRouletteTrx_t1  float64
		SelfLiuSuiCryptoMarketUsdt_t1 float64
		SelfLiuSuiLiuHeLotteryUsdt_t1 float64

		DirectLiuSuiHashTrx_t1          float64
		DirectLiuSuiHashUsdt_t1         float64
		DirectLiuSuiDianZhiUsdt_t1      float64
		DirectLiuSuiQiPaiUsdt_t1        float64
		DirectLiuSuiLiveUsdt_t1         float64
		DirectLiuSuiChainUsdt_t1        float64
		DirectLiuSuiLotteryUsdt_t1      float64
		DirectLiuSuiLowLotteryUsdt_t1   float64
		DirectLiuSuiSportUsdt_t1        float64
		DirectLiuSuiTexasUsdt_t1        float64
		DirectLiuSuiHaXiRouletteUsdt_t1 float64
		DirectLiuSuiHaXiRouletteTrx_t1  float64
		DirectLiuSuiCryptoMarketUsdt_t1 float64
		DirectLiuSuiLiuHeLotteryUsdt_t1 float64

		SelfCommissionUsdt_t1   float64
		DirectCommissionUsdt_t1 float64
		TeamCommissionUsdt_t1   float64
		SelfCommissionTrx_t1    float64
		DirectCommissionTrx_t1  float64
		TeamCommissionTrx_t1    float64
	}
	response := Response{}
	xAgentDaillyDb.Select(
		xAgentDaillyDo.LiuSuiHashTrxT1.Sum().As(xAgentDaillyDo.LiuSuiHashTrxT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiHashUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiHashUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiDianZhiUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiDianZhiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiQiPaiUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiQiPaiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiLiveUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiLiveUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiChainUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiChainUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiLotteryUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiSportUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiSportUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiTexasUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiTexasUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiLowLotteryUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiLowLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiCryptoMarketUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiCryptoMarketUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiHaXiRouletteUsdtT1.Sum().As(xAgentDaillyDo.LiuSuiHaXiRouletteUsdtT1.ColumnName().String()),
		xAgentDaillyDo.LiuSuiHaXiRouletteTrxT1.Sum().As(xAgentDaillyDo.LiuSuiHaXiRouletteTrxT1.ColumnName().String()),

		xAgentDaillyDo.SelfLiuSuiHashTrxT1.Sum().As(xAgentDaillyDo.SelfLiuSuiHashTrxT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiHashUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiHashUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiDianZhiUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiDianZhiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiQiPaiUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiQiPaiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiLiveUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiLiveUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiChainUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiChainUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiLotteryUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiSportUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiSportUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiTexasUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiTexasUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiLowLotteryUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiLowLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiCryptoMarketUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiCryptoMarketUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiHaXiRouletteUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiHaXiRouletteUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiHaXiRouletteTrxT1.Sum().As(xAgentDaillyDo.SelfLiuSuiHaXiRouletteTrxT1.ColumnName().String()),

		xAgentDaillyDo.DirectLiuSuiHashTrxT1.Sum().As(xAgentDaillyDo.DirectLiuSuiHashTrxT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiHashUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiHashUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiDianZhiUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiDianZhiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiQiPaiUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiQiPaiUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiLiveUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiLiveUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiChainUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiChainUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiLotteryUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiSportUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiSportUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiTexasUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiTexasUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiLowLotteryUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiLowLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiCryptoMarketUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiCryptoMarketUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiHaXiRouletteUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiHaXiRouletteUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiHaXiRouletteTrxT1.Sum().As(xAgentDaillyDo.DirectLiuSuiHaXiRouletteTrxT1.ColumnName().String()),

		xAgentDaillyDo.SelfCommissionUsdtT1.Sum().As(xAgentDaillyDo.SelfCommissionUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectCommissionUsdtT1.Sum().As(xAgentDaillyDo.DirectCommissionUsdtT1.ColumnName().String()),
		xAgentDaillyDo.TeamCommissionUsdtT1.Sum().As(xAgentDaillyDo.TeamCommissionUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfCommissionTrxT1.Sum().As(xAgentDaillyDo.SelfCommissionTrxT1.ColumnName().String()),
		xAgentDaillyDo.DirectCommissionTrxT1.Sum().As(xAgentDaillyDo.DirectCommissionTrxT1.ColumnName().String()),
		xAgentDaillyDo.TeamCommissionTrxT1.Sum().As(xAgentDaillyDo.TeamCommissionTrxT1.ColumnName().String()),

		// 香港六合彩流水
		xAgentDaillyDo.DirectLiuSuiLiuHeLotteryUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiLiuHeLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.SelfLiuSuiLiuHeLotteryUsdtT1.Sum().As(xAgentDaillyDo.SelfLiuSuiLiuHeLotteryUsdtT1.ColumnName().String()),
		xAgentDaillyDo.DirectLiuSuiLiuHeLotteryUsdtT1.Sum().As(xAgentDaillyDo.DirectLiuSuiLiuHeLotteryUsdtT1.ColumnName().String()),
	).Scan(&response)

	ctx.RespOK(response)
}

func (c *AgentController) set_nickname_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId   string `json:"AgentCode"`
		NickName string `json:"NickName"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	server.XDb().Table("x_user").Where("UserId = ?", token.UserId).Update(map[string]interface{}{
		"AgentNickName": reqdata.NickName,
	})
	ctx.RespOK()
}

func (c *AgentController) dict_child_search_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId    int   `json:"UserId"`
		StartTime int64 `json:"StartTime" validate:"required"`
		EndTime   int64 `json:"EndTime" validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.StartTime < 1 {
		reqdata.StartTime = xgo.LocalTimeToTimeStamp(xgo.GetLocalDate())
	}
	if reqdata.EndTime < 1 {
		reqdata.EndTime = reqdata.StartTime + 24*60*60*1000
	} else {
		reqdata.EndTime = reqdata.EndTime + 1000
	}
	token := server.GetToken(ctx)
	userdata, _ := server.XDb().Table("x_user").Select("UserId,AgentNickName,FenCheng").Where("AgentId=?", token.UserId).Where("UserId = ?", reqdata.UserId, 0).Find()
	data := []map[string]interface{}{}

	userdata.ForEach(func(item *xgo.XMap) bool {
		db := server.XDb().Table("x_agent_dailly").Select(`
		sum(LiuSuiHashTrx_t1) as LiuSuiHashTrx,
		sum(LiuSuiHashUsdt_t1) as LiuSuiHashUsdt,
		sum(LiuSuiDianZhiUsdt_t1) as LiuSuiDianZhiUsdt,
		sum(LiuSuiLiveUsdt_t1) as LiuSuiLiveUsdt,
		sum(LiuSuiQiPaiUsdt_t1) as LiuSuiQiPaiUsdt,
		sum(LiuSuiSportUsdt_t1) as LiuSuiSportUsdt,
		sum(LiuSuiLotteryUsdt_t1) as LiuSuiLotteryUsdt,
		sum(LiuSuiChainUsdt_t1) as LiuSuiChainUsdt,
		sum(LiuSuiTexasUsdt_t1) as LiuSuiTexasUsdt,
		sum(LiuSuiLowLotteryUsdt_t1) as LiuSuiLowLotteryUsdt,
		sum(LiuSuiLowLotteryUsdt_t1) as LiuSuiLowLotteryUsdt,
		sum(LiuSuiCryptoMarketUsdt_t1) as LiuSuiCryptoMarketUsdt,
		sum(LiuSuiCryptoMarketUsdt_t1) as LiuSuiCryptoMarketUsdt,
		sum(LiuSuiLiuHeLotteryUsdt_t1) as LiuSuiLiuHeLotteryUsdt,
		sum(LiuSuiHaXiRouletteUsdt_t1) as LiuSuiHaXiRouletteUsdt,
		sum(LiuSuiHaXiRouletteTrx_t1) as LiuSuiHaXiRouletteTrx,

		sum(SelfLiuSuiHashTrx_t1) as SelfLiuSuiHashTrx,
		sum(SelfLiuSuiHashUsdt_t1) as SelfLiuSuiHashUsdt,
		sum(SelfLiuSuiDianZhiUsdt_t1) as SelfLiuSuiDianZhiUsdt,
		sum(SelfLiuSuiLiveUsdt_t1) as SelfLiuSuiLiveUsdt,
		sum(SelfLiuSuiQiPaiUsdt_t1) as SelfLiuSuiQiPaiUsdt,
		sum(SelfLiuSuiSportUsdt_t1) as SelfLiuSuiSportUsdt,
		sum(SelfLiuSuiLotteryUsdt_t1) as SelfLiuSuiLotteryUsdt,
		sum(SelfLiuSuiChainUsdt_t1) as SelfLiuSuiChainUsdt,
		sum(SelfLiuSuiTexasUsdt_t1) as SelfLiuSuiTexasUsdt,
		sum(SelfLiuSuiLowLotteryUsdt_t1) as SelfLiuSuiLowLotteryUsdt,
		sum(SelfLiuSuiCryptoMarketUsdt_t1) as SelfLiuSuiCryptoMarketUsdt,
		sum(SelfLiuSuiLiuHeLotteryUsdt_t1) as SelfLiuSuiLiuHeLotteryUsdt,
		sum(SelfLiuSuiHaXiRouletteUsdt_t1) as SelfLiuSuiHaXiRouletteUsdt,
		sum(SelfLiuSuiHaXiRouletteTrx_t1) as SelfLiuSuiHaXiRouletteTrx,
		
		sum(DirectLiuSuiHashTrx_t1) as DirectLiuSuiHashTrx,
		sum(DirectLiuSuiHashUsdt_t1) as DirectLiuSuiHashUsdt,
		sum(DirectLiuSuiDianZhiUsdt_t1) as DirectLiuSuiDianZhiUsdt,
		sum(DirectLiuSuiLiveUsdt_t1) as DirectLiuSuiLiveUsdt,
		sum(DirectLiuSuiQiPaiUsdt_t1) as DirectLiuSuiQiPaiUsdt,
		sum(DirectLiuSuiSportUsdt_t1) as DirectLiuSuiSportUsdt,
		sum(DirectLiuSuiLotteryUsdt_t1) as DirectLiuSuiLotteryUsdt,
		sum(DirectLiuSuiChainUsdt_t1) as DirectLiuSuiChainUsdt,
		sum(DirectLiuSuiTexasUsdt_t1) as DirectLiuSuiTexasUsdt,
		sum(DirectLiuSuiLowLotteryUsdt_t1) as DirectLiuSuiLowLotteryUsdt,
		sum(DirectLiuSuiCryptoMarketUsdt_t1) as DirectLiuSuiCryptoMarketUsdt,
		sum(DirectLiuSuiLiuHeLotteryUsdt_t1) as DirectLiuSuiLiuHeLotteryUsdt,
		sum(DirectLiuSuiHaXiRouletteUsdt_t1) as DirectLiuSuiHaXiRouletteUsdt,
		sum(DirectLiuSuiHaXiRouletteTrx_t1) as DirectLiuSuiHaXiRouletteTrx
		`)

		db = db.Where("UserId = ?", item.Int("UserId"))
		db = db.Where("RecordDate >= ?", xgo.TimeStampToLocalDate(reqdata.StartTime/1000))
		db = db.Where("RecordDate < ?", xgo.TimeStampToLocalDate(reqdata.EndTime/1000))
		d, _ := db.First()
		item.Set("LiuSuiHashTrx", d.Float64("LiuSuiHashTrx"))
		item.Set("LiuSuiHashUsdt", d.Float64("LiuSuiHashUsdt"))
		item.Set("LiuSuiDianZhiUsdt", d.Float64("LiuSuiDianZhiUsdt"))
		item.Set("LiuSuiLiveUsdt", d.Float64("LiuSuiLiveUsdt"))
		item.Set("LiuSuiQiPaiUsdt", d.Float64("LiuSuiQiPaiUsdt"))
		item.Set("LiuSuiSportUsdt", d.Float64("LiuSuiSportUsdt"))
		item.Set("LiuSuiLotteryUsdt", d.Float64("LiuSuiLotteryUsdt"))
		item.Set("LiuSuiChainUsdt", d.Float64("LiuSuiChainUsdt"))
		item.Set("LiuSuiTexasUsdt", d.Float64("LiuSuiTexasUsdt"))
		item.Set("LiuSuiLowLotteryUsdt", d.Float64("LiuSuiLowLotteryUsdt"))
		item.Set("LiuSuiCryptoMarketUsdt", d.Float64("LiuSuiCryptoMarketUsdt"))
		item.Set("LiuSuiLiuHeLotteryUsdt", d.Float64("LiuSuiLiuHeLotteryUsdt"))
		item.Set("LiuSuiHaXiRouletteUsdt", d.Float64("LiuSuiHaXiRouletteUsdt"))
		item.Set("LiuSuiHaXiRouletteTrx", d.Float64("LiuSuiHaXiRouletteTrx"))

		item.Set("SelfLiuSuiHashTrx", d.Float64("SelfLiuSuiHashTrx"))
		item.Set("SelfLiuSuiHashUsdt", d.Float64("SelfLiuSuiHashUsdt"))
		item.Set("SelfLiuSuiDianZhiUsdt", d.Float64("SelfLiuSuiDianZhiUsdt"))
		item.Set("SelfLiuSuiLiveUsdt", d.Float64("SelfLiuSuiLiveUsdt"))
		item.Set("SelfLiuSuiQiPaiUsdt", d.Float64("SelfLiuSuiQiPaiUsdt"))
		item.Set("SelfLiuSuiSportUsdt", d.Float64("SelfLiuSuiSportUsdt"))
		item.Set("SelfLiuSuiLotteryUsdt", d.Float64("SelfLiuSuiLotteryUsdt"))
		item.Set("SelfLiuSuiChainUsdt", d.Float64("SelfLiuSuiChainUsdt"))
		item.Set("SelfLiuSuiTexasUsdt", d.Float64("SelfLiuSuiTexasUsdt"))
		item.Set("SelfLiuSuiLowLotteryUsdt", d.Float64("SelfLiuSuiLowLotteryUsdt"))
		item.Set("SelfLiuSuiCryptoMarketUsdt", d.Float64("SelfLiuSuiCryptoMarketUsdt"))
		item.Set("SelfLiuSuiLiuHeLotteryUsdt", d.Float64("SelfLiuSuiLiuHeLotteryUsdt"))
		item.Set("SelfLiuSuiHaXiRouletteUsdt", d.Float64("SelfLiuSuiHaXiRouletteUsdt"))
		item.Set("SelfLiuSuiHaXiRouletteTrx", d.Float64("SelfLiuSuiHaXiRouletteTrx"))

		item.Set("DirectLiuSuiHashTrx", d.Float64("DirectLiuSuiHashTrx"))
		item.Set("DirectLiuSuiHashUsdt", d.Float64("DirectLiuSuiHashUsdt"))
		item.Set("DirectLiuSuiDianZhiUsdt", d.Float64("DirectLiuSuiDianZhiUsdt"))
		item.Set("DirectLiuSuiLiveUsdt", d.Float64("DirectLiuSuiLiveUsdt"))
		item.Set("DirectLiuSuiQiPaiUsdt", d.Float64("DirectLiuSuiQiPaiUsdt"))
		item.Set("DirectLiuSuiSportUsdt", d.Float64("DirectLiuSuiSportUsdt"))
		item.Set("DirectLiuSuiLotteryUsdt", d.Float64("DirectLiuSuiLotteryUsdt"))
		item.Set("DirectLiuSuiChainUsdt", d.Float64("DirectLiuSuiChainUsdt"))
		item.Set("DirectLiuSuiTexasUsdt", d.Float64("DirectLiuSuiTexasUsdt"))
		item.Set("DirectLiuSuiLowLotteryUsdt", d.Float64("DirectLiuSuiLowLotteryUsdt"))
		item.Set("DirectLiuSuiCryptoMarketUsdt", d.Float64("DirectLiuSuiCryptoMarketUsdt"))
		item.Set("DirectLiuSuiLiuHeLotteryUsdt", d.Float64("DirectLiuSuiLiuHeLotteryUsdt"))
		item.Set("DirectLiuSuiHaXiRouletteUsdt", d.Float64("DirectLiuSuiHaXiRouletteUsdt"))
		item.Set("DirectLiuSuiHaXiRouletteTrx", d.Float64("DirectLiuSuiHaXiRouletteTrx"))

		data = append(data, item.RawData)
		return true
	})
	ctx.RespOK(data)
}

func (c *AgentController) child_search_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId    int   `json:"UserId" validate:"required"`
		StartTime int64 `json:"StartTime" validate:"required"`
		EndTime   int64 `json:"EndTime" validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.StartTime < 1 {
		reqdata.StartTime = xgo.LocalTimeToTimeStamp(xgo.GetLocalDate())
	}
	if reqdata.EndTime < 1 {
		reqdata.EndTime = reqdata.StartTime + 24*60*60*1000
	} else {
		reqdata.EndTime = reqdata.EndTime + 1000
	}
	token := server.GetToken(ctx)
	userdata, _ := server.XDb().Table("x_user").Select("UserId,TopAgentId,AgentId,AgentNickName,FenCheng,Agents").Where("UserId = ?", reqdata.UserId).First()
	if userdata == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	agents := []int{}
	json.Unmarshal([]byte(userdata.String("Agents")), &agents)
	finded := false
	for i := 0; i < len(agents); i++ {
		if token.UserId == agents[i] {
			finded = true
			break
		}
	}
	if !finded {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	db := server.XDb().Table("x_agent_dailly").Select(`
		sum(LiuSuiHashTrx_t1) as LiuSuiHashTrx,
		sum(LiuSuiHashUsdt_t1) as LiuSuiHashUsdt,
		sum(LiuSuiDianZhiUsdt_t1) as LiuSuiDianZhiUsdt,
		sum(LiuSuiLiveUsdt_t1) as LiuSuiLiveUsdt,
		sum(LiuSuiQiPaiUsdt_t1) as LiuSuiQiPaiUsdt,
		sum(LiuSuiSportUsdt_t1) as LiuSuiSportUsdt,
		sum(LiuSuiLotteryUsdt_t1) as LiuSuiLotteryUsdt,
		sum(LiuSuiChainUsdt_t1) as LiuSuiChainUsdt,
		sum(LiuSuiTexasUsdt_t1) as LiuSuiTexasUsdt,
		sum(LiuSuiLowLotteryUsdt_t1) as LiuSuiLowLotteryUsdt,
		sum(LiuSuiCryptoMarketUsdt_t1) as LiuSuiCryptoMarketUsdt,
		sum(LiuSuiLiuHeLotteryUsdt_t1) as LiuSuiLiuHeLotteryUsdt,
		sum(LiuSuiHaXiRouletteUsdt_t1) as LiuSuiHaXiRouletteUsdt,
		sum(LiuSuiHaXiRouletteTrx_t1) as LiuSuiHaXiRouletteTrx,
		sum(DirectLiuSuiHashTrx_t1) as DirectLiuSuiHashTrx,
		sum(DirectLiuSuiHashUsdt_t1) as DirectLiuSuiHashUsdt,
		sum(DirectLiuSuiDianZhiUsdt_t1) as DirectLiuSuiDianZhiUsdt,
		sum(DirectLiuSuiLiveUsdt_t1) as DirectLiuSuiLiveUsdt,
		sum(DirectLiuSuiQiPaiUsdt_t1) as DirectLiuSuiQiPaiUsdt,
		sum(DirectLiuSuiSportUsdt_t1) as DirectLiuSuiSportUsdt,
		sum(DirectLiuSuiLotteryUsdt_t1) as DirectLiuSuiLotteryUsdt,
		sum(DirectLiuSuiChainUsdt_t1) as DirectLiuSuiChainUsdt,
		sum(DirectLiuSuiTexasUsdt_t1) as DirectLiuSuiTexasUsdt,
		sum(DirectLiuSuiLowLotteryUsdt_t1) as DirectLiuSuiLowLotteryUsdt,
		sum(DirectLiuSuiCryptoMarketUsdt_t1) as DirectLiuSuiCryptoMarketUsdt,
		sum(DirectLiuSuiLiuHeLotteryUsdt_t1) as DirectLiuSuiLiuHeLotteryUsdt,
		sum(DirectLiuSuiHaXiRouletteUsdt_t1) as DirectLiuSuiHaXiRouletteUsdt,
		sum(DirectLiuSuiHaXiRouletteTrx_t1) as DirectLiuSuiHaXiRouletteTrx,

		sum(SelfLiuSuiHashTrx_t1) as SelfLiuSuiHashTrx,
		sum(SelfLiuSuiHashUsdt_t1) as SelfLiuSuiHashUsdt,
		sum(SelfLiuSuiDianZhiUsdt_t1) as SelfLiuSuiDianZhiUsdt,
		sum(SelfLiuSuiLiveUsdt_t1) as SelfLiuSuiLiveUsdt,
		sum(SelfLiuSuiQiPaiUsdt_t1) as SelfLiuSuiQiPaiUsdt,
		sum(SelfLiuSuiSportUsdt_t1) as SelfLiuSuiSportUsdt,
		sum(SelfLiuSuiLotteryUsdt_t1) as SelfLiuSuiLotteryUsdt,	
		sum(SelfLiuSuiChainUsdt_t1) as SelfLiuSuiChainUsdt,
		sum(SelfLiuSuiTexasUsdt_t1) as SelfLiuSuiTexasUsdt,
		sum(SelfLiuSuiLowLotteryUsdt_t1) as SelfLiuSuiLowLotteryUsdt,
		sum(SelfLiuSuiCryptoMarketUsdt_t1) as SelfLiuSuiCryptoMarketUsdt,
		sum(SelfLiuSuiLiuHeLotteryUsdt_t1) as SelfLiuSuiLiuHeLotteryUsdt,
		sum(SelfLiuSuiHaXiRouletteUsdt_t1) as SelfLiuSuiHaXiRouletteUsdt,
		sum(SelfLiuSuiHaXiRouletteTrx_t1) as SelfLiuSuiHaXiRouletteTrx`)
	db = db.Where("UserId = ?", userdata.Int("UserId"))
	db = db.Where("RecordDate >= ?", xgo.TimeStampToLocalDate(reqdata.StartTime/1000))
	db = db.Where("RecordDate < ?", xgo.TimeStampToLocalDate(reqdata.EndTime/1000))
	d, _ := db.First()
	userdata.Set("LiuSuiHashTrx", d.Float64("LiuSuiHashTrx"))
	userdata.Set("LiuSuiHashUsdt", d.Float64("LiuSuiHashUsdt"))
	userdata.Set("LiuSuiDianZhiUsdt", d.Float64("LiuSuiDianZhiUsdt"))
	userdata.Set("LiuSuiLiveUsdt", d.Float64("LiuSuiLiveUsdt"))
	userdata.Set("LiuSuiQiPaiUsdt", d.Float64("LiuSuiQiPaiUsdt"))
	userdata.Set("LiuSuiSportUsdt", d.Float64("LiuSuiSportUsdt"))
	userdata.Set("LiuSuiLotteryUsdt", d.Float64("LiuSuiLotteryUsdt"))
	userdata.Set("LiuSuiChainUsdt", d.Float64("LiuSuiChainUsdt"))
	userdata.Set("LiuSuiTexasUsdt", d.Float64("LiuSuiTexasUsdt"))
	userdata.Set("LiuSuiLowLotteryUsdt", d.Float64("LiuSuiLowLotteryUsdt"))
	userdata.Set("LiuSuiCryptoMarketUsdt", d.Float64("LiuSuiCryptoMarketUsdt"))
	userdata.Set("LiuSuiLiuHeLotteryUsdt", d.Float64("LiuSuiLiuHeLotteryUsdt"))
	userdata.Set("LiuSuiHaXiRouletteUsdt", d.Float64("LiuSuiHaXiRouletteUsdt"))
	userdata.Set("LiuSuiHaXiRouletteTrx", d.Float64("LiuSuiHaXiRouletteTrx"))

	userdata.Set("DirectLiuSuiHashTrx", d.Float64("DirectLiuSuiHashTrx"))
	userdata.Set("DirectLiuSuiHashUsdt", d.Float64("DirectLiuSuiHashUsdt"))
	userdata.Set("DirectLiuSuiDianZhiUsdt", d.Float64("DirectLiuSuiDianZhiUsdt"))
	userdata.Set("DirectLiuSuiLiveUsdt", d.Float64("DirectLiuSuiLiveUsdt"))
	userdata.Set("DirectLiuSuiQiPaiUsdt", d.Float64("DirectLiuSuiQiPaiUsdt"))
	userdata.Set("DirectLiuSuiSportUsdt", d.Float64("DirectLiuSuiSportUsdt"))
	userdata.Set("DirectLiuSuiLotteryUsdt", d.Float64("DirectLiuSuiLotteryUsdt"))
	userdata.Set("DirectLiuSuiChainUsdt", d.Float64("DirectLiuSuiChainUsdt"))
	userdata.Set("DirectLiuSuiTexasUsdt", d.Float64("DirectLiuSuiTexasUsdt"))
	userdata.Set("DirectLiuSuiLowLotteryUsdt", d.Float64("DirectLiuSuiLowLotteryUsdt"))
	userdata.Set("DirectLiuSuiCryptoMarketUsdt", d.Float64("DirectLiuSuiCryptoMarketUsdt"))
	userdata.Set("DirectLiuSuiLiuHeLotteryUsdt", d.Float64("DirectLiuSuiLiuHeLotteryUsdt"))
	userdata.Set("DirectLiuSuiHaXiRouletteUsdt", d.Float64("DirectLiuSuiHaXiRouletteUsdt"))
	userdata.Set("DirectLiuSuiHaXiRouletteTrx", d.Float64("DirectLiuSuiHaXiRouletteTrx"))

	userdata.Set("SelfLiuSuiHashTrx", d.Float64("SelfLiuSuiHashTrx"))
	userdata.Set("SelfLiuSuiHashUsdt", d.Float64("SelfLiuSuiHashUsdt"))
	userdata.Set("SelfLiuSuiDianZhiUsdt", d.Float64("SelfLiuSuiDianZhiUsdt"))
	userdata.Set("SelfLiuSuiLiveUsdt", d.Float64("SelfLiuSuiLiveUsdt"))
	userdata.Set("SelfLiuSuiQiPaiUsdt", d.Float64("SelfLiuSuiQiPaiUsdt"))
	userdata.Set("SelfLiuSuiSportUsdt", d.Float64("SelfLiuSuiSportUsdt"))
	userdata.Set("SelfLiuSuiLotteryUsdt", d.Float64("SelfLiuSuiLotteryUsdt"))
	userdata.Set("SelfLiuSuiChainUsdt", d.Float64("SelfLiuSuiChainUsdt"))
	userdata.Set("SelfLiuSuiTexasUsdt", d.Float64("SelfLiuSuiTexasUsdt"))
	userdata.Set("SelfLiuSuiLowLotteryUsdt", d.Float64("SelfLiuSuiLowLotteryUsdt"))
	userdata.Set("SelfLiuSuiCryptoMarketUsdt", d.Float64("SelfLiuSuiCryptoMarketUsdt"))
	userdata.Set("SelfLiuSuiLiuHeLotteryUsdt", d.Float64("SelfLiuSuiLiuHeLotteryUsdt"))
	userdata.Set("SelfLiuSuiHaXiRouletteUsdt", d.Float64("SelfLiuSuiHaXiRouletteUsdt"))

	userdata.Set("SelfLiuSuiHaXiRouletteTrx", d.Float64("SelfLiuSuiHaXiRouletteTrx"))
	userdata.Delete("Agents")
	ctx.RespOK(userdata.RawData)
}

func (c *AgentController) set_fencheng_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId   int     `json:"UserId" validate:"required"`
		Name     string  `json:"Name" validate:"required"`
		FenCheng float64 `json:"FenCheng" validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	userdata, _ := server.XDb().Table("x_user").Select("UserId,TopAgentId,AgentId,AgentNickName,FenCheng,Agents").
		Where("UserId = ? and AgentId = ?", reqdata.UserId, token.UserId).First()
	if userdata == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	if userdata.Int("AgentId") != token.UserId {
		ctx.RespErrString(true, &errcode, "不是自己的直属下级不可设置")
		return
	}
	// Определяем допустимые значения Name
	validNames := map[string]bool{
		"haxitrx": true, "haxiusdt": true, "dianzi": true, "live": true,
		"qipai": true, "sport": true, "lottery": true, "chain": true,
		"texas": true, "lowLottery": true, "cryptoMarket": true,
		"haxiroulettetrx": true, "haxirouletteusdt": true,
	}

	// Проверяем Name
	if !validNames[reqdata.Name] {
		ctx.RespErrString(true, &errcode, "无效的名称参数") // "Недопустимое значение параметра Name"
		return
	}

	// Проверяем FenCheng
	const (
		minFenCheng = 0.0
		maxFenCheng = 0.022
	)

	if reqdata.FenCheng < minFenCheng || reqdata.FenCheng > maxFenCheng {
		ctx.RespErrString(true, &errcode, "分成比例超出允许范围") // "Значение FenCheng вне допустимого диапазона"
		return
	}

	parent, _ := server.XDb().Table("x_user").Select("FenCheng,AgentUseId").Where("UserId = ?", token.UserId).First()
	if parent == nil {
		ctx.RespOK()
		return
	}

	fencheng := parent.String("FenCheng")
	if parent.Int("AgentUseId") > 0 {
		fangan, _ := server.XDb().Table("x_agent_commission_config").Select("Data").Where("Id = ?", parent.Int("AgentUseId")).First()
		if fangan == nil {
			ctx.RespOK()
			return
		}
		d := map[string]interface{}{}
		json.Unmarshal([]byte(fangan.String("Data")), &d)
		dx := d["rate"]
		bytes, _ := json.Marshal(dx)
		fencheng = string(bytes)
	}

	parentfc := map[string]interface{}{}
	json.Unmarshal([]byte(fencheng), &parentfc)
	if !(xgo.ToFloat(parentfc[reqdata.Name]) > reqdata.FenCheng || math.Abs(xgo.ToFloat(parentfc[reqdata.Name])-reqdata.FenCheng) < 0.0000000001) {
		ctx.RespErrString(true, &errcode, "分成不可大于自己的分成")
		return
	}
	userfc := map[string]interface{}{}
	json.Unmarshal([]byte(userdata.String("FenCheng")), &userfc)
	userfc[reqdata.Name] = reqdata.FenCheng
	bytes, _ := json.Marshal(userfc)
	server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
		"FenCheng": string(bytes),
	})
	db := server.DaoxHashGame().XAgentIndependenceFenchengHistory.WithContext(context.Background())
	db.Create(&model.XAgentIndependenceFenchengHistory{
		UserID:       int32(reqdata.UserId),
		AgentID:      int32(token.UserId),
		Reason:       2, // 上级设置
		ChangeBefore: userdata.String("FenCheng"),
		ChangeAfter:  string(bytes),
	})
	logs.Debug("set_fencheng_t1", token.UserId, reqdata.UserId, reqdata.Name, reqdata.FenCheng)
	ctx.RespOK()
}

func (c *AgentController) get_commission_new_t1(ctx *abugo.AbuHttpContent) {
	errcode := 0
	token := server.GetToken(ctx)
	presult, err := server.Db().CallProcedure("x_admin_get_commission_new_t1", token.UserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	if (*presult)["Id"] != nil {
		env := "测试服,"
		if !server.Debug() {
			env = "正式服,"
		}
		msg := fmt.Sprintf(`%v新的独立佣金领取订单,请立即审核
编号: %v
金额: %v
时间: %v`, env, (*presult)["Id"], (*presult)["FinalAmount"], (*presult)["NowTime"])
		req.Post(viper.GetString("tgbotapi")+"/sendmsg", msg)
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}

func (c *AgentController) commission_record_new_t1(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)

	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")

	fields := "Id,UserId,State,FinalAmount,CreateTime"
	count, presult := server.Db().Table("x_commission_audit_t1").Select(fields).Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	if presult == nil {
		res := map[string]interface{}{
			"list":  []map[string]interface{}{},
			"count": count,
		}
		ctx.RespOK(res)
	}
	result := *presult
	for i := 0; i < len(result); i++ {
		result[i]["CreateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(result[i]["CreateTime"]))
	}
	res := map[string]interface{}{
		"list":  result,
		"count": count,
	}
	ctx.RespOK(res)
}

func (c *AgentController) set_agentnickname_t1(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		UserId int    `validate:"required"`
		Name   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	userdata, _ := server.XDb().Table("x_user").Select("Agents").Where("UserId = ?", reqdata.UserId).First()
	if userdata == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	agents := []int{}
	json.Unmarshal([]byte(userdata.String("Agents")), &agents)
	finded := false
	for i := 0; i < len(agents); i++ {
		if token.UserId == agents[i] {
			finded = true
			break
		}
	}
	if !finded {
		ctx.RespOK()
		return
	}
	server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
		"AgentNickName": reqdata.Name,
	})
	ctx.RespOK()
}

func (c *AgentController) set_xg_fencheng_t1(ctx *abugo.AbuHttpContent) {
	type FenCheng struct {
		CatId      int
		RewardRate float64
	}
	type FenChengArr []FenCheng
	type RequestData struct {
		UserId   int    `json:"UserId" validate:"required"`
		Name     string `json:"Name" validate:"required"`
		FenCheng FenChengArr
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	userdata, _ := server.XDb().Table("x_user").Select("UserId,TopAgentId,AgentId,AgentNickName,FenCheng,Agents").
		Where("UserId = ? and AgentId = ?", reqdata.UserId, token.UserId).First()
	if userdata == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	if userdata.Int("AgentId") != token.UserId {
		ctx.RespErrString(true, &errcode, "不是自己的直属下级不可设置")
		return
	}

	parent, _ := server.XDb().Table("x_user").Select("FenCheng,AgentUseId").Where("UserId = ?", token.UserId).First()
	if parent == nil {
		ctx.RespOK()
		return
	}

	fencheng := parent.String("FenCheng")
	if parent.Int("AgentUseId") > 0 {
		fangan, _ := server.XDb().Table("x_agent_commission_config").Select("Data").Where("Id = ?", parent.Int("AgentUseId")).First()
		if fangan == nil {
			ctx.RespOK()
			return
		}
		d := map[string]interface{}{}
		json.Unmarshal([]byte(fangan.String("Data")), &d)
		dx := d["rate"]
		bytes, _ := json.Marshal(dx)
		fencheng = string(bytes)
	}

	parentfc := map[string]interface{}{}
	json.Unmarshal([]byte(fencheng), &parentfc)
	//if !(xgo.ToFloat(parentfc[reqdata.Name]) > reqdata.FenCheng || math.Abs(xgo.ToFloat(parentfc[reqdata.Name])-reqdata.FenCheng) < 0.0000000001) {
	//	ctx.RespErrString(true, &errcode, "分成不可大于自己的分成")
	//	return
	//}
	userfc := map[string]interface{}{}
	json.Unmarshal([]byte(userdata.String("FenCheng")), &userfc)
	userfc[reqdata.Name] = reqdata.FenCheng
	bytes, _ := json.Marshal(userfc)
	server.XDb().Table("x_user").Where("UserId = ?", reqdata.UserId).Update(map[string]interface{}{
		"FenCheng": string(bytes),
	})
	db := server.DaoxHashGame().XAgentIndependenceFenchengHistory.WithContext(context.Background())
	db.Create(&model.XAgentIndependenceFenchengHistory{
		UserID:       int32(reqdata.UserId),
		AgentID:      int32(token.UserId),
		Reason:       2, // 上级设置
		ChangeBefore: userdata.String("FenCheng"),
		ChangeAfter:  string(bytes),
	})
	logs.Debug("set_fencheng_t1", token.UserId, reqdata.UserId, reqdata.Name, reqdata.FenCheng)
	ctx.RespOK()
}

func (c *AgentController) update_xg_code_t1(ctx *abugo.AbuHttpContent) {
	type FenCheng struct {
		CatId      int
		RewardRate float64
	}
	type FenChengArr []FenCheng
	type RequestData struct {
		AgentCode string `json:"AgentCode" validate:"required"`
		Name      string `json:"Name" validate:"required"`
		FenCheng  FenChengArr
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	data, _ := server.XDb().Table("x_agent_code_t1").Where("AgentCode = ?", reqdata.AgentCode).First()
	if data == nil {
		ctx.RespErrString(true, &errcode, "AgentCode不存在")
		return
	}
	if data.Int("UserId") != token.UserId {
		ctx.RespErrString(true, &errcode, "不能修改别人的分享链接")
		return
	}
	parent, _ := server.XDb().Table("x_user").Select("FenCheng,AgentUseId").Where("UserId = ?", token.UserId).First()
	if parent == nil {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	fencheng := parent.String("FenCheng")
	if parent.Int("AgentUseId") > 0 {
		fangan, _ := server.XDb().Table("x_agent_commission_config").Select("Data").Where("Id = ?", parent.Int("AgentUseId")).First()
		if fangan == nil {
			ctx.RespOK()
			return
		}
		d := map[string]interface{}{}
		json.Unmarshal([]byte(fangan.String("Data")), &d)
		dx := d["rate"]
		bytes, _ := json.Marshal(dx)
		fencheng = string(bytes)
	}
	parentfc := map[string]interface{}{}
	json.Unmarshal([]byte(fencheng), &parentfc)
	//if xgo.ToFloat(parentfc[reqdata.Name]) < reqdata.FenCheng {
	//	ctx.RespErrString(true, &errcode, "分成不可大于自己的分成")
	//	return
	//}

	userfc := map[string]interface{}{}
	json.Unmarshal([]byte(data.String("FenCheng")), &userfc)
	userfc[reqdata.Name] = reqdata.FenCheng
	bytes, _ := json.Marshal(userfc)
	server.XDb().Table("x_agent_code_t1").Where("AgentCode = ?", reqdata.AgentCode).Update(map[string]interface{}{
		"FenCheng": string(bytes),
	})
	logs.Debug("update_code_t1", token.UserId, reqdata.AgentCode, reqdata.Name, reqdata.FenCheng)
	ctx.RespOK()
}
