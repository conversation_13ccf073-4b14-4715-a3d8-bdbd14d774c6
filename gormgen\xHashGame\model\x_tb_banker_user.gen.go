// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXTbBankerUser = "x_tb_banker_user"

// XTbBankerUser 上庄玩家记录
type XTbBankerUser struct {
	ID              int64     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID          int32     `gorm:"column:UserId;comment:用户id" json:"UserId"`                                            // 用户id
	ChannelID       int32     `gorm:"column:ChannelId;not null;comment:渠道id" json:"ChannelId"`                             // 渠道id
	SellerID        int32     `gorm:"column:SellerId;comment:运营商id" json:"SellerId"`                                       // 运营商id
	TopAgentID      int32     `gorm:"column:TopAgentId;comment:顶级代理" json:"TopAgentId"`                                    // 顶级代理
	AgentID         int32     `gorm:"column:AgentId;comment:代理id" json:"AgentId"`                                          // 代理id
	BankerMinTime   int32     `gorm:"column:BankerMinTime;comment:上庄最小时间(单位：秒)" json:"BankerMinTime"`                      // 上庄最小时间(单位：秒)
	BankerMaxTime   int32     `gorm:"column:BankerMaxTime;comment:上庄最大时间(单位：秒)" json:"BankerMaxTime"`                      // 上庄最大时间(单位：秒)
	BankerMinAmount float64   `gorm:"column:BankerMinAmount;default:0.000000;comment:上庄最小金额" json:"BankerMinAmount"`       // 上庄最小金额
	BankerTgGroup   string    `gorm:"column:BankerTgGroup;comment:上庄tg群" json:"BankerTgGroup"`                             // 上庄tg群
	BankerTime      int32     `gorm:"column:BankerTime;comment:上庄时间(单位：秒)" json:"BankerTime"`                              // 上庄时间(单位：秒)
	BankerStartTime time.Time `gorm:"column:BankerStartTime;comment:上庄开始时间" json:"BankerStartTime"`                        // 上庄开始时间
	BankerEndTime   time.Time `gorm:"column:BankerEndTime;comment:上庄结束时间" json:"BankerEndTime"`                            // 上庄结束时间
	BankerAmount    float64   `gorm:"column:BankerAmount;default:0.000000;comment:上庄余额" json:"BankerAmount"`               // 上庄余额
	BankerStatus    int32     `gorm:"column:BankerStatus;comment:上庄状态 0已下庄 1上庄排队中 2正在上庄 3取消上庄" json:"BankerStatus"`        // 上庄状态 0已下庄 1上庄排队中 2正在上庄 3取消上庄
	Memo            string    `gorm:"column:Memo;comment:描述" json:"Memo"`                                                  // 描述
	CreateTime      time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime      time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XTbBankerUser's table name
func (*XTbBankerUser) TableName() string {
	return TableNameXTbBankerUser
}
